# DigitalOcean App Platform Configuration for NeuroColony
# Deploy with: doctl apps create --spec .do/app.yaml

name: neurocolony
region: nyc

# Database - Managed MongoDB
databases:
- name: neurocolony-db
  engine: MONGODB
  version: "5"
  size: basic
  num_nodes: 1
  production: true

# Backend API Service
services:
- name: neurocolony-backend
  source_dir: /backend
  github:
    repo: YOUR_GITHUB_USERNAME/neurocolony
    branch: main
  build_command: npm install
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 5000
  
  # Environment variables for production
  envs:
  - key: NODE_ENV
    value: production
  - key: PORT
    value: "5000"
  - key: DEMO_MODE
    value: "false"
  - key: JWT_SECRET
    value: ${NEUROCOLONY_JWT_SECRET}
    type: SECRET
  - key: STRIPE_SECRET_KEY
    value: ${NEUROCOLONY_STRIPE_SECRET}
    type: SECRET
  - key: STRIPE_WEBHOOK_SECRET
    value: ${NEUROCOLONY_STRIPE_WEBHOOK_SECRET}
    type: SECRET
  - key: OPENAI_API_KEY
    value: ${NEUROCOLONY_OPENAI_KEY}
    type: SECRET
  - key: MONGODB_URI
    value: ${neurocolony-db.DATABASE_URL}
  - key: FRONTEND_URL
    value: ${APP_URL}
  
  # Health check
  health_check:
    http_path: /api/health
    initial_delay_seconds: 60
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 3
    success_threshold: 1

# Frontend Static Site
static_sites:
- name: neurocolony-frontend
  source_dir: /frontend
  github:
    repo: YOUR_GITHUB_USERNAME/neurocolony
    branch: main
  build_command: npm install && npm run build
  output_dir: /dist
  
  # Environment variables for build
  envs:
  - key: VITE_API_URL
    value: ${APP_URL}/api
  - key: VITE_STRIPE_PUBLISHABLE_KEY
    value: ${NEUROCOLONY_STRIPE_PUBLISHABLE_KEY}
    type: SECRET
  
  # Routing configuration
  routes:
  - path: /api
    preserve_path_prefix: true
  catchall_document: index.html
  error_document: index.html

# Custom domain configuration
domains:
- domain: neurocolony.dev
  type: PRIMARY
- domain: www.neurocolony.dev
  type: ALIAS

# Alerts for monitoring
alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
- rule: CPU_UTILIZATION
  value: 80
- rule: MEM_UTILIZATION
  value: 80