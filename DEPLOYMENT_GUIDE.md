# NeuroColony Production Deployment Guide

## Overview

NeuroColony is a full-stack SaaS application for AI-powered email sequence generation with advanced features including A/B testing, scheduling, and analytics.

## Architecture

- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: Node.js + Express + MongoDB
- **Cache**: Redis
- **Proxy**: Nginx
- **Containerization**: Docker + Docker Compose
- **SSL**: TLS 1.2/1.3 with security headers

## Prerequisites

### Server Requirements
- **CPU**: 2+ cores
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 20GB minimum, SSD recommended
- **OS**: Ubuntu 20.04+ or similar Linux distribution

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL (for SSL certificates)

### External Services
- OpenAI API account
- Stripe account (for payments)
- Domain name with DNS access
- SSL certificate (Let's Encrypt recommended)

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url> sequenceai
   cd sequenceai
   ```

2. **Configure Environment**
   ```bash
   cp .env.production .env.local
   # Edit .env.local with your actual values
   nano .env.local
   ```

3. **Deploy**
   ```bash
   ./deploy.sh production
   ```

## Environment Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `MONGO_USERNAME` | MongoDB admin username | `sequenceai_admin` |
| `MONGO_PASSWORD` | MongoDB admin password | `secure_password_123` |
| `REDIS_PASSWORD` | Redis password | `redis_password_456` |
| `JWT_SECRET` | JWT signing secret | `64-character-random-string` |
| `OPENAI_API_KEY` | OpenAI API key | `sk-proj-...` |
| `STRIPE_SECRET_KEY` | Stripe secret key | `sk_live_...` |
| `FRONTEND_URL` | Production frontend URL | `https://sequenceai.com` |

### SSL Certificate Setup

#### Option 1: Let's Encrypt (Recommended)
```bash
# Install certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d sequenceai.com -d www.sequenceai.com

# Copy certificates
sudo cp /etc/letsencrypt/live/sequenceai.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/sequenceai.com/privkey.pem nginx/ssl/key.pem
```

#### Option 2: Self-Signed (Development)
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem \
  -subj "/C=US/ST=State/L=City/O=NeuroColony/CN=sequenceai.com"
```

## Deployment Process

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Application Deployment
```bash
# Clone repository
git clone <repository-url> /opt/sequenceai
cd /opt/sequenceai

# Configure environment
cp .env.production .env.local
nano .env.local

# Deploy
./deploy.sh production
```

### 3. DNS Configuration
Point your domain to the server:
```
A     sequenceai.com       -> YOUR_SERVER_IP
CNAME www.sequenceai.com   -> sequenceai.com
```

## Monitoring and Maintenance

### Health Checks
- Backend: `http://localhost:5000/api/health`
- Frontend: `http://localhost:3000`
- Nginx: `http://localhost/health`

### Log Management
```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f nginx

# Log rotation is handled automatically
```

### Database Backup
```bash
# Manual backup
docker exec sequenceai-mongo-prod mongodump \
  --authenticationDatabase admin \
  -u ${MONGO_USERNAME} -p ${MONGO_PASSWORD} \
  --out /tmp/backup

# Automated backup (add to crontab)
0 2 * * * /opt/sequenceai/scripts/backup.sh
```

### Updates and Rollbacks
```bash
# Update to latest version
git pull origin main
./deploy.sh production

# Rollback to previous version
git checkout <previous-commit>
./deploy.sh production

# Restore from backup
./scripts/restore.sh /path/to/backup
```

## Security Considerations

### Firewall Configuration
```bash
# UFW configuration
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### Security Headers
Nginx automatically applies security headers:
- HSTS
- CSP
- X-Frame-Options
- X-Content-Type-Options

### SSL/TLS Configuration
- TLS 1.2 and 1.3 only
- Strong cipher suites
- HSTS with includeSubDomains

## Performance Optimization

### Nginx Caching
Static assets are cached for 1 year with proper headers.

### Database Indexes
MongoDB indexes are automatically created for:
- User lookups
- Sequence queries
- Template searches

### Redis Caching
Session data and frequently accessed data is cached in Redis.

## Troubleshooting

### Common Issues

1. **Container Won't Start**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs <service-name>
   
   # Check system resources
   docker system df
   free -h
   ```

2. **Database Connection Issues**
   ```bash
   # Check MongoDB status
   docker exec sequenceai-mongo-prod mongo admin -u $MONGO_USERNAME -p $MONGO_PASSWORD --eval "db.runCommand('ping')"
   ```

3. **SSL Certificate Issues**
   ```bash
   # Verify certificate
   openssl x509 -in nginx/ssl/cert.pem -text -noout
   
   # Test SSL
   curl -I https://sequenceai.com
   ```

### Performance Issues
1. Check system resources: `htop`, `iotop`
2. Monitor Docker stats: `docker stats`
3. Check application logs for errors
4. Verify database performance

## Scaling Considerations

### Horizontal Scaling
- Use Docker Swarm or Kubernetes
- Implement load balancer
- Separate database servers
- CDN for static assets

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Implement caching layers
- Use Redis clustering

## Backup and Recovery

### Automated Backup Strategy
1. Daily database backups
2. Weekly full system backups
3. Real-time log shipping
4. Geographic backup distribution

### Recovery Procedures
1. Service restoration from backups
2. Database point-in-time recovery
3. Disaster recovery testing
4. RTO/RPO definitions

## Support and Maintenance

### Regular Maintenance
- Security updates: Weekly
- Dependency updates: Monthly
- Performance review: Monthly
- Backup verification: Weekly

### Monitoring Setup
- Application metrics
- System resource monitoring
- Error tracking
- Uptime monitoring
- Security monitoring

For additional support, refer to the application documentation or contact the development team.