"""
Agent Executor Service - Production-grade AI agent orchestration
Powered by Claude 3.5 Sonnet and GPT-4 with advanced reasoning capabilities
"""

import os
import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import logging

from celery import Celery, Task
from pymongo import MongoClient
import redis
from anthropic import Anthropic
from openai import OpenAI
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential
from prometheus_client import Counter, Histogram, Gauge
from langchain.memory import ConversationSummaryBufferMemory
from langchain.schema import SystemMessage, HumanMessage, AIMessage
from pydantic import BaseModel, Field

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metrics
agent_execution_counter = Counter('agent_executions_total', 'Total agent executions', ['agent_type', 'status'])
agent_execution_duration = Histogram('agent_execution_duration_seconds', 'Agent execution duration', ['agent_type'])
active_agents = Gauge('active_agents', 'Number of active agent executions')
ai_api_calls = Counter('ai_api_calls_total', 'Total AI API calls', ['provider', 'model'])
ai_api_errors = Counter('ai_api_errors_total', 'AI API errors', ['provider', 'error_type'])

# Initialize Celery
celery_app = Celery(
    'agent_executor',
    broker=os.getenv('RABBITMQ_URL', 'amqp://guest:guest@localhost:5672'),
    backend=os.getenv('REDIS_URL', 'redis://localhost:6379/0')
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes
    task_soft_time_limit=240,  # 4 minutes
    worker_prefetch_multiplier=2,
    worker_max_tasks_per_child=100,
)

# Initialize connections
mongo_client = MongoClient(os.getenv('MONGODB_URI'))
db = mongo_client.neurocolony
redis_client = redis.from_url(os.getenv('REDIS_URL'))

# Initialize AI clients
anthropic_client = Anthropic(api_key=os.getenv('CLAUDE_API_KEY'))
openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Thread pool for parallel processing
executor = ThreadPoolExecutor(max_workers=10)


class AgentContext(BaseModel):
    """Context for agent execution"""
    agent_id: str
    user_id: str
    execution_id: str
    inputs: Dict[str, Any]
    memory: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    
class AgentStep(BaseModel):
    """Represents a single step in agent execution"""
    step_number: int
    action: str
    thought: str
    observation: Optional[str] = None
    result: Optional[Any] = None
    duration_ms: int = 0
    

class ChainOfThoughtReasoner:
    """Advanced Chain-of-Thought reasoning with Claude 3.5 Sonnet"""
    
    def __init__(self):
        self.max_steps = 10
        self.model = "claude-3-opus-20240229"
        
    async def create_plan(self, task: str, context: Dict[str, Any]) -> List[str]:
        """Create an execution plan using CoT reasoning"""
        prompt = f"""You are an expert AI agent planner. Create a detailed step-by-step plan to accomplish this task.

Task: {task}
Context: {json.dumps(context, indent=2)}

Think through this step-by-step:
1. What is the main objective?
2. What information do we have available?
3. What are the key challenges?
4. What steps are needed to accomplish this?

Provide a numbered list of concrete actions to take. Be specific and actionable.
Format: 
1. [Action]: [Specific description]
2. [Action]: [Specific description]
...
"""
        
        try:
            response = anthropic_client.messages.create(
                model=self.model,
                max_tokens=2000,
                temperature=0.7,
                system="You are a strategic planning AI that breaks down complex tasks into actionable steps.",
                messages=[{"role": "user", "content": prompt}]
            )
            
            ai_api_calls.labels(provider='anthropic', model=self.model).inc()
            
            # Parse the plan from the response
            plan_text = response.content[0].text
            steps = []
            
            for line in plan_text.split('\n'):
                line = line.strip()
                if line and line[0].isdigit() and '.' in line:
                    # Extract the action part after the number
                    action = line.split('.', 1)[1].strip()
                    steps.append(action)
                    
            return steps[:self.max_steps]  # Limit steps
            
        except Exception as e:
            logger.error("Failed to create plan", error=str(e))
            ai_api_errors.labels(provider='anthropic', error_type=type(e).__name__).inc()
            return ["Analyze the request", "Generate appropriate response"]
    
    async def execute_step(self, step: str, context: Dict[str, Any], history: List[AgentStep]) -> AgentStep:
        """Execute a single reasoning step"""
        start_time = time.time()
        
        # Build history context
        history_text = ""
        if history:
            history_text = "Previous steps:\n"
            for h in history[-3:]:  # Last 3 steps for context
                history_text += f"- {h.action}: {h.observation}\n"
        
        prompt = f"""You are executing a specific step in an AI agent workflow.

Current Step: {step}
Context: {json.dumps(context, indent=2)}
{history_text}

For this step:
1. Explain your thinking (thought process)
2. Perform the required action
3. Provide the result

Format your response as:
THOUGHT: [Your reasoning]
ACTION: [What you're doing]
RESULT: [The outcome]
"""
        
        try:
            response = anthropic_client.messages.create(
                model=self.model,
                max_tokens=1500,
                temperature=0.5,
                system="You are an AI agent executing specific tasks with precision and clarity.",
                messages=[{"role": "user", "content": prompt}]
            )
            
            response_text = response.content[0].text
            
            # Parse the response
            thought = ""
            action = step
            result = ""
            
            for line in response_text.split('\n'):
                if line.startswith('THOUGHT:'):
                    thought = line.replace('THOUGHT:', '').strip()
                elif line.startswith('ACTION:'):
                    action = line.replace('ACTION:', '').strip()
                elif line.startswith('RESULT:'):
                    result = line.replace('RESULT:', '').strip()
            
            duration_ms = int((time.time() - start_time) * 1000)
            
            return AgentStep(
                step_number=len(history) + 1,
                action=action,
                thought=thought,
                observation=result,
                result=result,
                duration_ms=duration_ms
            )
            
        except Exception as e:
            logger.error("Failed to execute step", step=step, error=str(e))
            ai_api_errors.labels(provider='anthropic', error_type=type(e).__name__).inc()
            
            return AgentStep(
                step_number=len(history) + 1,
                action=step,
                thought="Error occurred during execution",
                observation=str(e),
                result=None,
                duration_ms=int((time.time() - start_time) * 1000)
            )


class AgentMemory:
    """Long-term memory for agents using vector embeddings"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.redis_key = f"agent_memory:{agent_id}"
        self.max_memories = 100
        
    async def store(self, key: str, value: Any, metadata: Dict[str, Any] = None):
        """Store a memory with metadata"""
        memory = {
            'key': key,
            'value': value,
            'metadata': metadata or {},
            'timestamp': datetime.utcnow().isoformat(),
            'access_count': 0
        }
        
        # Store in Redis with expiration
        redis_client.hset(
            self.redis_key,
            key,
            json.dumps(memory)
        )
        redis_client.expire(self.redis_key, 86400 * 7)  # 7 days
        
    async def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve a memory by key"""
        data = redis_client.hget(self.redis_key, key)
        if data:
            memory = json.loads(data)
            memory['access_count'] += 1
            redis_client.hset(self.redis_key, key, json.dumps(memory))
            return memory['value']
        return None
        
    async def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories by similarity (simplified for now)"""
        all_memories = []
        for key in redis_client.hkeys(self.redis_key):
            data = redis_client.hget(self.redis_key, key)
            if data:
                memory = json.loads(data)
                # Simple keyword matching for now
                if query.lower() in str(memory).lower():
                    all_memories.append(memory)
                    
        # Sort by access count and recency
        all_memories.sort(
            key=lambda x: (x['access_count'], x['timestamp']),
            reverse=True
        )
        
        return all_memories[:limit]


class ProductionAgent:
    """Base class for production AI agents"""
    
    def __init__(self, agent_config: Dict[str, Any]):
        self.config = agent_config
        self.id = agent_config['id']
        self.name = agent_config['name']
        self.type = agent_config['type']
        self.capabilities = agent_config.get('capabilities', [])
        self.reasoner = ChainOfThoughtReasoner()
        self.memory = AgentMemory(self.id)
        
    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute agent with full reasoning pipeline"""
        active_agents.inc()
        start_time = time.time()
        execution_steps = []
        
        try:
            logger.info("Starting agent execution", 
                       agent_id=self.id,
                       execution_id=context.execution_id)
            
            # Update execution status
            await self._update_status(context.execution_id, 'planning')
            
            # Create execution plan
            task_description = self._build_task_description(context)
            plan = await self.reasoner.create_plan(task_description, context.inputs)
            
            await self._update_status(context.execution_id, 'executing', {
                'plan': plan,
                'total_steps': len(plan)
            })
            
            # Execute each step
            for i, step in enumerate(plan):
                await self._update_progress(context.execution_id, i + 1, len(plan))
                
                step_result = await self.reasoner.execute_step(
                    step, 
                    context.inputs,
                    execution_steps
                )
                
                execution_steps.append(step_result)
                
                # Store important results in memory
                if step_result.result:
                    await self.memory.store(
                        f"step_{i}_{step[:30]}",
                        step_result.result,
                        {'step': i, 'action': step}
                    )
            
            # Synthesize final result
            final_result = await self._synthesize_results(execution_steps, context)
            
            # Record metrics
            duration = time.time() - start_time
            agent_execution_duration.labels(agent_type=self.type).observe(duration)
            agent_execution_counter.labels(agent_type=self.type, status='success').inc()
            
            # Update final status
            await self._update_status(context.execution_id, 'completed', {
                'result': final_result,
                'duration_seconds': duration,
                'steps_executed': len(execution_steps)
            })
            
            return {
                'success': True,
                'result': final_result,
                'execution_steps': [step.dict() for step in execution_steps],
                'duration_seconds': duration,
                'agent': {
                    'id': self.id,
                    'name': self.name,
                    'type': self.type
                }
            }
            
        except Exception as e:
            logger.error("Agent execution failed",
                        agent_id=self.id,
                        execution_id=context.execution_id,
                        error=str(e))
            
            agent_execution_counter.labels(agent_type=self.type, status='failure').inc()
            
            await self._update_status(context.execution_id, 'failed', {
                'error': str(e),
                'error_type': type(e).__name__
            })
            
            return {
                'success': False,
                'error': str(e),
                'execution_steps': [step.dict() for step in execution_steps],
                'agent': {
                    'id': self.id,
                    'name': self.name,
                    'type': self.type
                }
            }
            
        finally:
            active_agents.dec()
    
    def _build_task_description(self, context: AgentContext) -> str:
        """Build a detailed task description for the agent"""
        base_description = f"Execute {self.name} agent with the following inputs:\n"
        
        # Add specific descriptions based on agent type
        if self.type == 'email_sequence_generator':
            return base_description + f"""
Generate an email sequence for:
- Product: {context.inputs.get('product_name', 'Unknown')}
- Target Audience: {context.inputs.get('target_audience', 'General')}
- Sequence Length: {context.inputs.get('sequence_length', 5)} emails
- Tone: {context.inputs.get('tone', 'Professional')}
- Goals: {context.inputs.get('goals', 'Engagement and conversion')}
"""
        
        elif self.type == 'subject_line_optimizer':
            return base_description + f"""
Optimize email subject lines for:
- Original Subject: {context.inputs.get('subject', 'Unknown')}
- Target Audience: {context.inputs.get('audience', 'General')}
- Campaign Type: {context.inputs.get('campaign_type', 'Marketing')}
- Optimization Goals: {context.inputs.get('goals', 'Open rate')}
Generate multiple A/B test variations.
"""
        
        elif self.type == 'audience_segmentation':
            return base_description + f"""
Segment audience based on:
- Customer Data: {len(context.inputs.get('customers', []))} records
- Segmentation Criteria: {context.inputs.get('criteria', 'Behavior')}
- Number of Segments: {context.inputs.get('num_segments', 5)}
- Business Goals: {context.inputs.get('goals', 'Personalization')}
"""
        
        else:
            # Generic task description
            return base_description + json.dumps(context.inputs, indent=2)
    
    async def _synthesize_results(self, steps: List[AgentStep], context: AgentContext) -> Dict[str, Any]:
        """Synthesize the final result from all execution steps"""
        
        # Collect all step results
        step_results = [step.result for step in steps if step.result]
        
        synthesis_prompt = f"""Synthesize the results from the following agent execution steps into a final, actionable output.

Agent: {self.name}
Task Type: {self.type}
Original Inputs: {json.dumps(context.inputs, indent=2)}

Step Results:
{json.dumps(step_results, indent=2)}

Provide a comprehensive final output that:
1. Summarizes what was accomplished
2. Presents the key deliverables in a structured format
3. Includes any recommendations or next steps
4. Formats the output appropriately for the agent type

For email sequences, provide the complete email content.
For optimizations, provide specific recommendations with metrics.
For segmentation, provide detailed segment definitions.
"""
        
        try:
            response = anthropic_client.messages.create(
                model="claude-3-opus-20240229",
                max_tokens=3000,
                temperature=0.3,
                system="You are an expert at synthesizing AI agent results into actionable business outputs.",
                messages=[{"role": "user", "content": synthesis_prompt}]
            )
            
            return {
                'synthesis': response.content[0].text,
                'step_count': len(steps),
                'execution_time_ms': sum(step.duration_ms for step in steps)
            }
            
        except Exception as e:
            logger.error("Failed to synthesize results", error=str(e))
            return {
                'synthesis': "Combined results from execution steps",
                'raw_results': step_results,
                'step_count': len(steps)
            }
    
    async def _update_status(self, execution_id: str, status: str, data: Dict[str, Any] = None):
        """Update execution status in database"""
        update = {
            'status': status,
            'last_updated': datetime.utcnow(),
            f'status_history.{status}_at': datetime.utcnow()
        }
        
        if data:
            update['data'] = data
            
        db.agent_executions.update_one(
            {'_id': execution_id},
            {'$set': update}
        )
        
    async def _update_progress(self, execution_id: str, current_step: int, total_steps: int):
        """Update execution progress"""
        progress = (current_step / total_steps) * 100
        
        db.agent_executions.update_one(
            {'_id': execution_id},
            {
                '$set': {
                    'progress': progress,
                    'current_step': current_step,
                    'total_steps': total_steps,
                    'last_updated': datetime.utcnow()
                }
            }
        )


# Celery Tasks
@celery_app.task(bind=True, name='execute_agent')
def execute_agent_task(self, agent_id: str, execution_data: Dict[str, Any]) -> Dict[str, Any]:
    """Celery task to execute an agent"""
    
    # Load agent configuration
    agent_doc = db.agents.find_one({'_id': agent_id})
    if not agent_doc:
        raise ValueError(f"Agent {agent_id} not found")
    
    # Create agent instance
    agent = ProductionAgent(agent_doc)
    
    # Create execution context
    context = AgentContext(
        agent_id=agent_id,
        user_id=execution_data['user_id'],
        execution_id=execution_data['execution_id'],
        inputs=execution_data['inputs'],
        metadata=execution_data.get('metadata', {})
    )
    
    # Run async execution in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(agent.execute(context))
        return result
    finally:
        loop.close()


@celery_app.task(name='process_workflow')
def process_workflow_task(workflow_id: str, execution_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process a complete workflow with multiple agents"""
    
    workflow = db.workflows.find_one({'_id': workflow_id})
    if not workflow:
        raise ValueError(f"Workflow {workflow_id} not found")
    
    results = []
    context = execution_data.copy()
    
    # Execute each step in the workflow
    for step in workflow['steps']:
        agent_id = step['agent_id']
        
        # Use previous step output as input if configured
        if step.get('use_previous_output') and results:
            context['inputs'].update(results[-1]['result'])
        
        # Execute agent
        result = execute_agent_task(agent_id, {
            'user_id': execution_data['user_id'],
            'execution_id': f"{execution_data['execution_id']}_step_{len(results)}",
            'inputs': context['inputs'],
            'metadata': {
                'workflow_id': workflow_id,
                'step_index': len(results)
            }
        })
        
        results.append(result)
        
        # Check if we should continue based on result
        if not result['success'] and not step.get('continue_on_error', False):
            break
    
    return {
        'workflow_id': workflow_id,
        'success': all(r['success'] for r in results),
        'steps': results,
        'final_output': results[-1]['result'] if results else None
    }


@celery_app.task(name='health_check')
def health_check() -> Dict[str, Any]:
    """Health check task"""
    return {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'connections': {
            'mongodb': mongo_client.server_info() is not None,
            'redis': redis_client.ping(),
            'anthropic': bool(os.getenv('CLAUDE_API_KEY')),
            'openai': bool(os.getenv('OPENAI_API_KEY'))
        }
    }


if __name__ == '__main__':
    # Start Celery worker
    celery_app.start()