import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import { createClient } from 'redis';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import winston from 'winston';
import promClient from 'prom-client';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { v4 as uuidv4 } from 'uuid';

// Initialize Express app
const app = express();

// Logger configuration
const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'auth-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

// Prometheus metrics
const register = new promClient.Registry();
promClient.collectDefaultMetrics({ register });

const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
});

const authAttempts = new promClient.Counter({
  name: 'auth_attempts_total',
  help: 'Total number of authentication attempts',
  labelNames: ['type', 'status'],
});

const activeTokens = new promClient.Gauge({
  name: 'active_tokens',
  help: 'Number of active authentication tokens',
});

register.registerMetric(httpRequestDuration);
register.registerMetric(authAttempts);
register.registerMetric(activeTokens);

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request tracking middleware
app.use((req, res, next) => {
  req.id = uuidv4();
  req.startTime = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - req.startTime) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path, res.statusCode)
      .observe(duration);
    
    logger.info('Request completed', {
      requestId: req.id,
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration: `${duration}s`,
      userAgent: req.get('user-agent'),
      ip: req.ip,
    });
  });
  
  next();
});

// Database connections
const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/neurocolony-auth';
mongoose.connect(mongoUri, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
});

mongoose.connection.on('connected', () => {
  logger.info('Connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  logger.error('MongoDB connection error:', err);
});

// Redis connection
const redis = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    reconnectStrategy: (retries) => {
      if (retries > 10) {
        logger.error('Redis connection failed after 10 retries');
        return new Error('Redis connection failed');
      }
      return Math.min(retries * 100, 3000);
    },
  },
});

redis.on('error', (err) => {
  logger.error('Redis error:', err);
});

redis.on('connect', () => {
  logger.info('Connected to Redis');
});

await redis.connect();

// Rate limiter
const rateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'auth_rl',
  points: 10, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 300, // Block for 5 minutes
});

const loginRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'login_rl',
  points: 5,
  duration: 900, // Per 15 minutes
  blockDuration: 900, // Block for 15 minutes
});

// Schemas
const UserSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String, required: true },
  name: { type: String },
  role: { type: String, enum: ['user', 'admin', 'service'], default: 'user' },
  status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
  emailVerified: { type: Boolean, default: false },
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: { type: String },
  subscription: {
    plan: { type: String, enum: ['free', 'starter', 'professional', 'business', 'enterprise'], default: 'free' },
    status: { type: String, enum: ['active', 'trialing', 'past_due', 'canceled'], default: 'active' },
    expiresAt: { type: Date },
  },
  metadata: {
    lastLogin: { type: Date },
    loginCount: { type: Number, default: 0 },
    ipAddresses: [String],
    userAgent: String,
  },
  security: {
    passwordChangedAt: { type: Date },
    passwordResetToken: String,
    passwordResetExpires: Date,
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
  },
}, {
  timestamps: true,
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ 'subscription.plan': 1, 'subscription.status': 1 });
UserSchema.index({ createdAt: -1 });

const User = mongoose.model('User', UserSchema);

// Session schema
const SessionSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  userId: { type: String, required: true, index: true },
  token: { type: String, required: true, unique: true },
  refreshToken: { type: String, unique: true },
  ipAddress: String,
  userAgent: String,
  expiresAt: { type: Date, required: true, index: { expireAfterSeconds: 0 } },
  refreshExpiresAt: Date,
  revokedAt: Date,
  revokedReason: String,
}, {
  timestamps: true,
});

const Session = mongoose.model('Session', SessionSchema);

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  rememberMe: z.boolean().optional(),
});

const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  name: z.string().min(2).max(100),
});

const refreshSchema = z.object({
  refreshToken: z.string(),
});

// Helper functions
async function generateTokens(user, sessionId) {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    sessionId,
  };
  
  const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: '15m',
    issuer: 'https://neurocolony.ai',
    audience: 'neurocolony-api',
  });
  
  const refreshToken = jwt.sign(
    { ...payload, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    {
      expiresIn: '7d',
      issuer: 'https://neurocolony.ai',
    }
  );
  
  return { accessToken, refreshToken };
}

async function validatePassword(inputPassword, hashedPassword) {
  return bcrypt.compare(inputPassword, hashedPassword);
}

async function hashPassword(password) {
  return bcrypt.hash(password, 12);
}

// Auth middleware
async function authenticate(req, res, next) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      issuer: 'https://neurocolony.ai',
      audience: 'neurocolony-api',
    });
    
    // Check if session is still valid
    const session = await Session.findOne({
      id: decoded.sessionId,
      revokedAt: null,
    });
    
    if (!session || session.expiresAt < new Date()) {
      return res.status(401).json({ error: 'Session expired' });
    }
    
    // Check if user still exists and is active
    const user = await User.findOne({ id: decoded.userId, status: 'active' });
    
    if (!user) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }
    
    req.user = user;
    req.session = session;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    
    logger.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
}

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

app.get('/ready', async (req, res) => {
  try {
    // Check MongoDB
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({ status: 'not ready', reason: 'MongoDB disconnected' });
    }
    
    // Check Redis
    await redis.ping();
    
    res.json({ status: 'ready' });
  } catch (error) {
    res.status(503).json({ status: 'not ready', reason: error.message });
  }
});

app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// Registration endpoint
app.post('/auth/register', async (req, res) => {
  try {
    // Rate limiting
    await rateLimiter.consume(req.ip);
    
    // Validate input
    const validatedData = registerSchema.parse(req.body);
    
    // Check if user exists
    const existingUser = await User.findOne({ email: validatedData.email });
    if (existingUser) {
      authAttempts.labels('register', 'failed').inc();
      return res.status(409).json({ error: 'Email already registered' });
    }
    
    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);
    
    // Create user
    const user = await User.create({
      id: uuidv4(),
      email: validatedData.email,
      password: hashedPassword,
      name: validatedData.name,
      security: {
        passwordChangedAt: new Date(),
      },
    });
    
    // Create session
    const sessionId = uuidv4();
    const { accessToken, refreshToken } = await generateTokens(user, sessionId);
    
    await Session.create({
      id: sessionId,
      userId: user.id,
      token: accessToken,
      refreshToken,
      ipAddress: req.ip,
      userAgent: req.get('user-agent'),
      expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      refreshExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });
    
    activeTokens.inc();
    authAttempts.labels('register', 'success').inc();
    
    logger.info('User registered', { userId: user.id, email: user.email });
    
    res.status(201).json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        subscription: user.subscription,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: 900, // 15 minutes in seconds
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    
    logger.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// Login endpoint
app.post('/auth/login', async (req, res) => {
  try {
    // Rate limiting
    await loginRateLimiter.consume(req.ip);
    
    // Validate input
    const validatedData = loginSchema.parse(req.body);
    
    // Find user
    const user = await User.findOne({ email: validatedData.email });
    
    if (!user) {
      authAttempts.labels('login', 'failed').inc();
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Check if account is locked
    if (user.security.lockUntil && user.security.lockUntil > new Date()) {
      authAttempts.labels('login', 'locked').inc();
      return res.status(423).json({ 
        error: 'Account locked',
        lockedUntil: user.security.lockUntil,
      });
    }
    
    // Validate password
    const isValidPassword = await validatePassword(validatedData.password, user.password);
    
    if (!isValidPassword) {
      // Increment login attempts
      user.security.loginAttempts = (user.security.loginAttempts || 0) + 1;
      
      // Lock account after 5 failed attempts
      if (user.security.loginAttempts >= 5) {
        user.security.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        logger.warn('Account locked due to failed attempts', { userId: user.id });
      }
      
      await user.save();
      
      authAttempts.labels('login', 'failed').inc();
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Check if user is active
    if (user.status !== 'active') {
      authAttempts.labels('login', 'inactive').inc();
      return res.status(403).json({ error: 'Account is not active' });
    }
    
    // Reset login attempts on successful login
    user.security.loginAttempts = 0;
    user.security.lockUntil = null;
    user.metadata.lastLogin = new Date();
    user.metadata.loginCount = (user.metadata.loginCount || 0) + 1;
    
    // Store IP address
    if (!user.metadata.ipAddresses.includes(req.ip)) {
      user.metadata.ipAddresses.push(req.ip);
      if (user.metadata.ipAddresses.length > 10) {
        user.metadata.ipAddresses.shift(); // Keep only last 10 IPs
      }
    }
    
    user.metadata.userAgent = req.get('user-agent');
    await user.save();
    
    // Create session
    const sessionId = uuidv4();
    const { accessToken, refreshToken } = await generateTokens(user, sessionId);
    
    const expiresAt = validatedData.rememberMe
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      : new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    
    await Session.create({
      id: sessionId,
      userId: user.id,
      token: accessToken,
      refreshToken,
      ipAddress: req.ip,
      userAgent: req.get('user-agent'),
      expiresAt,
      refreshExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    });
    
    activeTokens.inc();
    authAttempts.labels('login', 'success').inc();
    
    logger.info('User logged in', { userId: user.id, email: user.email });
    
    res.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        subscription: user.subscription,
        emailVerified: user.emailVerified,
        twoFactorEnabled: user.twoFactorEnabled,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: validatedData.rememberMe ? 2592000 : 900,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    
    logger.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Refresh token endpoint
app.post('/auth/refresh', async (req, res) => {
  try {
    // Validate input
    const validatedData = refreshSchema.parse(req.body);
    
    // Verify refresh token
    const decoded = jwt.verify(validatedData.refreshToken, process.env.JWT_REFRESH_SECRET, {
      issuer: 'https://neurocolony.ai',
    });
    
    // Find session
    const session = await Session.findOne({
      refreshToken: validatedData.refreshToken,
      revokedAt: null,
    });
    
    if (!session || session.refreshExpiresAt < new Date()) {
      return res.status(401).json({ error: 'Invalid or expired refresh token' });
    }
    
    // Find user
    const user = await User.findOne({ id: decoded.userId, status: 'active' });
    
    if (!user) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }
    
    // Generate new tokens
    const sessionId = uuidv4();
    const { accessToken, refreshToken: newRefreshToken } = await generateTokens(user, sessionId);
    
    // Revoke old session
    session.revokedAt = new Date();
    session.revokedReason = 'token_refresh';
    await session.save();
    
    // Create new session
    await Session.create({
      id: sessionId,
      userId: user.id,
      token: accessToken,
      refreshToken: newRefreshToken,
      ipAddress: req.ip,
      userAgent: req.get('user-agent'),
      expiresAt: new Date(Date.now() + 15 * 60 * 1000),
      refreshExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    });
    
    authAttempts.labels('refresh', 'success').inc();
    
    res.json({
      tokens: {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: 900,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    
    if (error.name === 'JsonWebTokenError') {
      authAttempts.labels('refresh', 'failed').inc();
      return res.status(401).json({ error: 'Invalid refresh token' });
    }
    
    logger.error('Token refresh error:', error);
    res.status(500).json({ error: 'Token refresh failed' });
  }
});

// Logout endpoint
app.post('/auth/logout', authenticate, async (req, res) => {
  try {
    // Revoke current session
    req.session.revokedAt = new Date();
    req.session.revokedReason = 'user_logout';
    await req.session.save();
    
    activeTokens.dec();
    
    logger.info('User logged out', { userId: req.user.id });
    
    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

// Logout all sessions endpoint
app.post('/auth/logout-all', authenticate, async (req, res) => {
  try {
    // Revoke all user sessions
    await Session.updateMany(
      { userId: req.user.id, revokedAt: null },
      { 
        revokedAt: new Date(),
        revokedReason: 'user_logout_all',
      }
    );
    
    const revokedCount = await Session.countDocuments({
      userId: req.user.id,
      revokedAt: { $ne: null },
    });
    
    activeTokens.dec(revokedCount);
    
    logger.info('User logged out from all sessions', { userId: req.user.id });
    
    res.json({ message: 'Logged out from all sessions successfully' });
  } catch (error) {
    logger.error('Logout all error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

// Verify token endpoint
app.get('/auth/verify', authenticate, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      name: req.user.name,
      role: req.user.role,
      subscription: req.user.subscription,
    },
  });
});

// Get user sessions
app.get('/auth/sessions', authenticate, async (req, res) => {
  try {
    const sessions = await Session.find({
      userId: req.user.id,
      revokedAt: null,
    }).select('-token -refreshToken').sort('-createdAt');
    
    res.json({
      sessions: sessions.map(session => ({
        id: session.id,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt,
        current: session.id === req.session.id,
      })),
    });
  } catch (error) {
    logger.error('Get sessions error:', error);
    res.status(500).json({ error: 'Failed to retrieve sessions' });
  }
});

// Revoke specific session
app.delete('/auth/sessions/:sessionId', authenticate, async (req, res) => {
  try {
    const session = await Session.findOne({
      id: req.params.sessionId,
      userId: req.user.id,
      revokedAt: null,
    });
    
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }
    
    session.revokedAt = new Date();
    session.revokedReason = 'user_revoked';
    await session.save();
    
    activeTokens.dec();
    
    res.json({ message: 'Session revoked successfully' });
  } catch (error) {
    logger.error('Revoke session error:', error);
    res.status(500).json({ error: 'Failed to revoke session' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({ error: 'Validation error', details: err.errors });
  }
  
  if (err.name === 'MongoError' && err.code === 11000) {
    return res.status(409).json({ error: 'Duplicate entry' });
  }
  
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  // Stop accepting new requests
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  // Close database connections
  await mongoose.connection.close();
  await redis.quit();
  
  process.exit(0);
});

// Start server
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
  logger.info(`Auth service listening on port ${PORT}`);
});