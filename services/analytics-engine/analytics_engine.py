"""
Analytics Engine Service - Production ML/AI analytics for NeuroColony
Provides predictive modeling, customer segmentation, and business intelligence
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

import numpy as np
import pandas as pd
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import structlog
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, mean_squared_error, silhouette_score
import xgboost as xgb
import lightgbm as lgb
from prophet import Prophet
import tensorflow as tf
from tensorflow import keras
import joblib
import redis
from clickhouse_driver import Client as ClickHouseClient
import psycopg2
from psycopg2.extras import RealDictCursor
from pymongo import MongoClient
import mlflow
import optuna
from scipy import stats
import hdbscan
import umap

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metrics
prediction_counter = Counter('ml_predictions_total', 'Total ML predictions', ['model_type', 'status'])
prediction_latency = Histogram('ml_prediction_latency_seconds', 'ML prediction latency', ['model_type'])
model_accuracy = Gauge('ml_model_accuracy', 'Model accuracy scores', ['model_type'])
active_models = Gauge('ml_active_models', 'Number of active ML models')
training_duration = Histogram('ml_training_duration_seconds', 'Model training duration', ['model_type'])

# Initialize FastAPI app
app = FastAPI(title="NeuroColony Analytics Engine", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database connections
redis_client = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379'))
mongo_client = MongoClient(os.getenv('MONGODB_URI'))
db = mongo_client.neurocolony

# ClickHouse connection
clickhouse_client = ClickHouseClient(
    host=os.getenv('CLICKHOUSE_HOST', 'localhost'),
    port=int(os.getenv('CLICKHOUSE_PORT', 9000)),
    user=os.getenv('CLICKHOUSE_USER', 'default'),
    password=os.getenv('CLICKHOUSE_PASSWORD', ''),
    database='analytics'
)

# PostgreSQL connection pool
postgres_pool = psycopg2.pool.SimpleConnectionPool(
    1, 20,
    host=os.getenv('POSTGRES_HOST', 'localhost'),
    port=int(os.getenv('POSTGRES_PORT', 5432)),
    database='analytics',
    user=os.getenv('POSTGRES_USER', 'postgres'),
    password=os.getenv('POSTGRES_PASSWORD', '')
)

# Thread pool for CPU-intensive tasks
executor = ThreadPoolExecutor(max_workers=8)

# Model registry
model_registry = {}


class PredictionRequest(BaseModel):
    """Request model for predictions"""
    model_type: str
    features: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


class SegmentationRequest(BaseModel):
    """Request model for customer segmentation"""
    customer_ids: List[str]
    features: List[str]
    num_segments: int = 5
    method: str = "kmeans"  # kmeans, dbscan, hdbscan, hierarchical


class ForecastRequest(BaseModel):
    """Request model for time series forecasting"""
    metric: str  # revenue, conversions, open_rate, etc.
    historical_data: List[Dict[str, Any]]
    periods: int = 30
    confidence_interval: float = 0.95


class OptimizationRequest(BaseModel):
    """Request model for campaign optimization"""
    campaign_type: str
    current_metrics: Dict[str, float]
    constraints: Dict[str, Any]
    objective: str = "maximize_roi"


class EmailOpenRatePredictor:
    """Predicts email open rates using ensemble methods"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_importance = {}
        
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Extract and engineer features for open rate prediction"""
        features = pd.DataFrame()
        
        # Time-based features
        if 'send_hour' in data.columns:
            features['send_hour'] = data['send_hour']
            features['is_weekend'] = data['send_hour'].apply(lambda x: 1 if x in [0, 6] else 0)
            features['is_business_hours'] = data['send_hour'].apply(lambda x: 1 if 9 <= x <= 17 else 0)
        
        # Subject line features
        if 'subject' in data.columns:
            features['subject_length'] = data['subject'].str.len()
            features['subject_word_count'] = data['subject'].str.split().str.len()
            features['has_emoji'] = data['subject'].str.contains('[😀-🙏]', regex=True).astype(int)
            features['has_personalization'] = data['subject'].str.contains('{{.*}}', regex=True).astype(int)
            features['has_urgency'] = data['subject'].str.contains('limited|urgent|now|today', case=False).astype(int)
        
        # Audience features
        if 'audience_segment' in data.columns:
            features['audience_size'] = data['audience_size']
            features['avg_engagement_score'] = data['avg_engagement_score']
            features['days_since_last_email'] = data['days_since_last_email']
        
        # Historical performance
        if 'previous_open_rate' in data.columns:
            features['previous_open_rate'] = data['previous_open_rate']
            features['previous_click_rate'] = data['previous_click_rate']
            
        return features
    
    async def train(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train the open rate prediction model"""
        start_time = time.time()
        
        try:
            # Prepare features
            X = self.prepare_features(training_data)
            y = training_data['open_rate']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train ensemble model
            models = {
                'rf': RandomForestClassifier(n_estimators=100, random_state=42),
                'xgb': xgb.XGBRegressor(n_estimators=100, learning_rate=0.1, random_state=42),
                'lgb': lgb.LGBMRegressor(n_estimators=100, learning_rate=0.1, random_state=42)
            }
            
            predictions = []
            for name, model in models.items():
                model.fit(X_train_scaled, y_train)
                pred = model.predict(X_test_scaled)
                predictions.append(pred)
                
            # Ensemble prediction (average)
            ensemble_pred = np.mean(predictions, axis=0)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, ensemble_pred)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y_test - ensemble_pred))
            
            # Store the best model
            self.model = models['xgb']  # Use XGBoost as primary
            
            # Calculate feature importance
            if hasattr(self.model, 'feature_importances_'):
                self.feature_importance = dict(zip(X.columns, self.model.feature_importances_))
            
            # Update metrics
            model_accuracy.labels(model_type='open_rate_predictor').set(1 - mae)
            training_duration.labels(model_type='open_rate_predictor').observe(time.time() - start_time)
            
            # Log with MLflow
            mlflow.log_metrics({
                'rmse': rmse,
                'mae': mae,
                'training_samples': len(X_train)
            })
            
            return {
                'success': True,
                'metrics': {
                    'rmse': rmse,
                    'mae': mae,
                    'feature_importance': self.feature_importance
                },
                'training_duration': time.time() - start_time
            }
            
        except Exception as e:
            logger.error("Failed to train open rate model", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Predict open rate for given features"""
        if not self.model:
            raise ValueError("Model not trained")
        
        start_time = time.time()
        
        try:
            # Convert to DataFrame
            df = pd.DataFrame([features])
            X = self.prepare_features(df)
            X_scaled = self.scaler.transform(X)
            
            # Make prediction
            prediction = self.model.predict(X_scaled)[0]
            
            # Get confidence interval (simplified)
            confidence_lower = max(0, prediction - 0.1)
            confidence_upper = min(1, prediction + 0.1)
            
            # Update metrics
            prediction_counter.labels(model_type='open_rate', status='success').inc()
            prediction_latency.labels(model_type='open_rate').observe(time.time() - start_time)
            
            return {
                'prediction': float(prediction),
                'confidence_interval': {
                    'lower': float(confidence_lower),
                    'upper': float(confidence_upper)
                },
                'features_used': list(X.columns),
                'prediction_time_ms': int((time.time() - start_time) * 1000)
            }
            
        except Exception as e:
            prediction_counter.labels(model_type='open_rate', status='error').inc()
            raise


class CustomerSegmentation:
    """Advanced customer segmentation using multiple algorithms"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        
    async def segment_customers(self, data: pd.DataFrame, request: SegmentationRequest) -> Dict[str, Any]:
        """Perform customer segmentation"""
        start_time = time.time()
        
        try:
            # Prepare features
            feature_cols = request.features
            X = data[feature_cols].fillna(0)
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Choose segmentation method
            if request.method == 'kmeans':
                model = KMeans(n_clusters=request.num_segments, random_state=42)
                labels = model.fit_predict(X_scaled)
                
                # Calculate silhouette score
                if len(np.unique(labels)) > 1:
                    score = silhouette_score(X_scaled, labels)
                else:
                    score = 0
                    
            elif request.method == 'dbscan':
                model = DBSCAN(eps=0.5, min_samples=5)
                labels = model.fit_predict(X_scaled)
                score = silhouette_score(X_scaled, labels) if len(np.unique(labels)) > 1 else 0
                
            elif request.method == 'hdbscan':
                model = hdbscan.HDBSCAN(min_cluster_size=10)
                labels = model.fit_predict(X_scaled)
                score = model.relative_validity_
                
            else:
                raise ValueError(f"Unknown segmentation method: {request.method}")
            
            # Analyze segments
            segments = []
            for segment_id in np.unique(labels):
                if segment_id == -1:  # Noise in DBSCAN/HDBSCAN
                    continue
                    
                mask = labels == segment_id
                segment_data = data[mask]
                
                segment_analysis = {
                    'segment_id': int(segment_id),
                    'size': int(mask.sum()),
                    'percentage': float(mask.sum() / len(data) * 100),
                    'characteristics': {}
                }
                
                # Calculate segment characteristics
                for col in feature_cols:
                    if segment_data[col].dtype in ['float64', 'int64']:
                        segment_analysis['characteristics'][col] = {
                            'mean': float(segment_data[col].mean()),
                            'std': float(segment_data[col].std()),
                            'min': float(segment_data[col].min()),
                            'max': float(segment_data[col].max())
                        }
                
                segments.append(segment_analysis)
            
            # Store model
            model_key = f"segmentation_{request.method}_{datetime.utcnow().isoformat()}"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            
            return {
                'success': True,
                'method': request.method,
                'num_segments': len(segments),
                'quality_score': float(score),
                'segments': segments,
                'model_key': model_key,
                'processing_time': time.time() - start_time
            }
            
        except Exception as e:
            logger.error("Segmentation failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }


class RevenueAttributionModel:
    """Multi-touch attribution modeling for revenue"""
    
    def __init__(self):
        self.attribution_models = {
            'first_touch': self._first_touch_attribution,
            'last_touch': self._last_touch_attribution,
            'linear': self._linear_attribution,
            'time_decay': self._time_decay_attribution,
            'u_shaped': self._u_shaped_attribution,
            'data_driven': self._data_driven_attribution
        }
        
    async def calculate_attribution(self, 
                                  customer_journeys: List[Dict[str, Any]], 
                                  model_type: str = 'time_decay') -> Dict[str, Any]:
        """Calculate revenue attribution across touchpoints"""
        
        if model_type not in self.attribution_models:
            raise ValueError(f"Unknown attribution model: {model_type}")
        
        attribution_func = self.attribution_models[model_type]
        touchpoint_values = {}
        total_revenue = 0
        
        for journey in customer_journeys:
            if not journey['touchpoints'] or not journey.get('revenue', 0):
                continue
                
            revenue = journey['revenue']
            total_revenue += revenue
            
            # Calculate attribution for this journey
            attributions = attribution_func(journey['touchpoints'], revenue)
            
            # Aggregate by touchpoint type
            for touchpoint, value in attributions.items():
                if touchpoint not in touchpoint_values:
                    touchpoint_values[touchpoint] = 0
                touchpoint_values[touchpoint] += value
        
        # Calculate percentages
        attribution_percentages = {
            tp: (value / total_revenue * 100) if total_revenue > 0 else 0
            for tp, value in touchpoint_values.items()
        }
        
        return {
            'model_type': model_type,
            'total_revenue': total_revenue,
            'touchpoint_values': touchpoint_values,
            'attribution_percentages': attribution_percentages,
            'num_journeys_analyzed': len(customer_journeys)
        }
    
    def _first_touch_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """First touch gets all credit"""
        if not touchpoints:
            return {}
        
        first_touch = touchpoints[0]['channel']
        return {first_touch: revenue}
    
    def _last_touch_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """Last touch gets all credit"""
        if not touchpoints:
            return {}
        
        last_touch = touchpoints[-1]['channel']
        return {last_touch: revenue}
    
    def _linear_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """Equal credit to all touchpoints"""
        if not touchpoints:
            return {}
        
        credit_per_touch = revenue / len(touchpoints)
        attribution = {}
        
        for tp in touchpoints:
            channel = tp['channel']
            if channel not in attribution:
                attribution[channel] = 0
            attribution[channel] += credit_per_touch
            
        return attribution
    
    def _time_decay_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """More recent touchpoints get more credit"""
        if not touchpoints:
            return {}
        
        # Calculate days from conversion for each touchpoint
        weights = []
        for i, tp in enumerate(touchpoints):
            days_from_conversion = len(touchpoints) - i - 1
            weight = 2 ** (-days_from_conversion / 7)  # Half-life of 7 days
            weights.append(weight)
        
        total_weight = sum(weights)
        attribution = {}
        
        for tp, weight in zip(touchpoints, weights):
            channel = tp['channel']
            if channel not in attribution:
                attribution[channel] = 0
            attribution[channel] += revenue * (weight / total_weight)
            
        return attribution
    
    def _u_shaped_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """40% to first, 40% to last, 20% distributed among middle"""
        if not touchpoints:
            return {}
        
        attribution = {}
        
        if len(touchpoints) == 1:
            attribution[touchpoints[0]['channel']] = revenue
        elif len(touchpoints) == 2:
            attribution[touchpoints[0]['channel']] = revenue * 0.5
            attribution[touchpoints[1]['channel']] = revenue * 0.5
        else:
            # First touch: 40%
            first_channel = touchpoints[0]['channel']
            attribution[first_channel] = revenue * 0.4
            
            # Last touch: 40%
            last_channel = touchpoints[-1]['channel']
            if last_channel not in attribution:
                attribution[last_channel] = 0
            attribution[last_channel] += revenue * 0.4
            
            # Middle touches: 20% distributed
            middle_touches = touchpoints[1:-1]
            if middle_touches:
                middle_credit = revenue * 0.2 / len(middle_touches)
                for tp in middle_touches:
                    channel = tp['channel']
                    if channel not in attribution:
                        attribution[channel] = 0
                    attribution[channel] += middle_credit
                    
        return attribution
    
    def _data_driven_attribution(self, touchpoints: List[Dict], revenue: float) -> Dict[str, float]:
        """Machine learning based attribution (simplified Shapley values)"""
        # This is a simplified version. In production, use proper Shapley value calculation
        # or train a model to predict conversion probability
        
        attribution = {}
        
        # Calculate engagement score for each touchpoint
        total_engagement = 0
        engagement_scores = []
        
        for tp in touchpoints:
            # Simplified engagement scoring
            score = 1.0
            if tp.get('duration', 0) > 60:  # More than 1 minute
                score *= 1.5
            if tp.get('pages_viewed', 0) > 3:
                score *= 1.3
            if tp.get('actions_taken', 0) > 0:
                score *= 2.0
                
            engagement_scores.append(score)
            total_engagement += score
        
        # Distribute revenue based on engagement
        for tp, score in zip(touchpoints, engagement_scores):
            channel = tp['channel']
            if channel not in attribution:
                attribution[channel] = 0
            attribution[channel] += revenue * (score / total_engagement)
            
        return attribution


class ConversionOptimizer:
    """Optimize campaigns for maximum conversion using reinforcement learning concepts"""
    
    def __init__(self):
        self.optimization_history = {}
        
    async def optimize_campaign(self, request: OptimizationRequest) -> Dict[str, Any]:
        """Optimize campaign parameters for maximum performance"""
        
        # Define optimization objective
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'send_time': trial.suggest_int('send_time', 0, 23),
                'subject_length': trial.suggest_int('subject_length', 20, 80),
                'personalization_level': trial.suggest_float('personalization_level', 0, 1),
                'content_length': trial.suggest_int('content_length', 100, 1000),
                'cta_prominence': trial.suggest_float('cta_prominence', 0, 1),
                'urgency_score': trial.suggest_float('urgency_score', 0, 1)
            }
            
            # Simulate performance (in production, use historical data or A/B test results)
            estimated_conversion = self._estimate_conversion(params, request.current_metrics)
            
            # Apply constraints
            if request.constraints:
                if 'max_sends_per_day' in request.constraints:
                    if params['send_time'] not in range(9, 18):  # Business hours
                        estimated_conversion *= 0.8
                        
            return estimated_conversion
        
        # Run optimization
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=100)
        
        # Get best parameters
        best_params = study.best_params
        best_value = study.best_value
        
        # Generate recommendations
        recommendations = self._generate_recommendations(best_params, request.current_metrics)
        
        return {
            'optimal_parameters': best_params,
            'expected_improvement': float((best_value - request.current_metrics.get('conversion_rate', 0.02)) * 100),
            'recommendations': recommendations,
            'confidence_score': float(min(study.best_trial.number / 50, 1.0)),  # More trials = more confidence
            'optimization_iterations': len(study.trials)
        }
    
    def _estimate_conversion(self, params: Dict[str, Any], current_metrics: Dict[str, float]) -> float:
        """Estimate conversion rate based on parameters"""
        base_rate = current_metrics.get('conversion_rate', 0.02)
        
        # Simple model for demonstration - in production, use ML model
        score = base_rate
        
        # Optimal send time bonus
        if 10 <= params['send_time'] <= 14:  # Lunch hours
            score *= 1.2
            
        # Subject length optimization
        if 40 <= params['subject_length'] <= 60:
            score *= 1.1
            
        # Personalization bonus
        score *= (1 + params['personalization_level'] * 0.3)
        
        # Content length penalty for too long
        if params['content_length'] > 500:
            score *= 0.95
            
        # CTA prominence bonus
        score *= (1 + params['cta_prominence'] * 0.2)
        
        # Urgency can help but not too much
        score *= (1 + params['urgency_score'] * 0.15 * (1 - params['urgency_score']))
        
        return min(score, 0.5)  # Cap at 50% conversion
    
    def _generate_recommendations(self, optimal_params: Dict[str, Any], current_metrics: Dict[str, float]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Send time recommendation
        optimal_hour = optimal_params['send_time']
        recommendations.append(f"Schedule emails at {optimal_hour}:00 for optimal engagement")
        
        # Subject line recommendation
        if optimal_params['subject_length'] < 40:
            recommendations.append("Use concise subject lines (30-40 characters) for better open rates")
        else:
            recommendations.append("Use descriptive subject lines (50-60 characters) to set clear expectations")
            
        # Personalization recommendation
        if optimal_params['personalization_level'] > 0.7:
            recommendations.append("Implement deep personalization using customer behavior and preferences")
        elif optimal_params['personalization_level'] > 0.3:
            recommendations.append("Add basic personalization like first name and company")
            
        # Content recommendation
        if optimal_params['content_length'] < 300:
            recommendations.append("Keep email content concise and scannable (under 300 words)")
        else:
            recommendations.append("Use structured content with clear sections and bullet points")
            
        # CTA recommendation
        if optimal_params['cta_prominence'] > 0.7:
            recommendations.append("Make CTAs highly visible with contrasting colors and clear copy")
            
        # Urgency recommendation
        if optimal_params['urgency_score'] > 0.5:
            recommendations.append("Add time-sensitive elements but avoid being too pushy")
            
        return recommendations


# API Endpoints

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "redis": redis_client.ping(),
            "mongodb": db.command("ping")["ok"] == 1,
            "clickhouse": True,  # Add proper check
            "postgres": True  # Add proper check
        }
    }


@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()


@app.post("/predict/open-rate")
async def predict_open_rate(request: PredictionRequest):
    """Predict email open rate"""
    predictor = model_registry.get('open_rate_predictor')
    if not predictor:
        predictor = EmailOpenRatePredictor()
        model_registry['open_rate_predictor'] = predictor
        
    try:
        result = await predictor.predict(request.features)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/segment/customers")
async def segment_customers(request: SegmentationRequest):
    """Perform customer segmentation"""
    
    # Fetch customer data
    customers = list(db.users.find(
        {"_id": {"$in": request.customer_ids}},
        {"_id": 1, "email": 1, "created_at": 1, "subscription_tier": 1, 
         "usage_stats": 1, "last_login": 1}
    ))
    
    if not customers:
        raise HTTPException(status_code=404, detail="No customers found")
    
    # Prepare DataFrame
    df = pd.DataFrame(customers)
    
    # Add computed features
    df['account_age_days'] = (datetime.utcnow() - pd.to_datetime(df['created_at'])).dt.days
    df['days_since_login'] = (datetime.utcnow() - pd.to_datetime(df['last_login'])).dt.days
    
    segmenter = CustomerSegmentation()
    result = await segmenter.segment_customers(df, request)
    
    return result


@app.post("/forecast/metrics")
async def forecast_metrics(request: ForecastRequest):
    """Forecast business metrics using Prophet"""
    
    # Prepare data for Prophet
    df = pd.DataFrame(request.historical_data)
    df['ds'] = pd.to_datetime(df['date'])
    df['y'] = df[request.metric]
    
    # Initialize and fit Prophet model
    model = Prophet(
        daily_seasonality=True,
        weekly_seasonality=True,
        yearly_seasonality=True,
        interval_width=request.confidence_interval
    )
    
    model.fit(df[['ds', 'y']])
    
    # Make predictions
    future = model.make_future_dataframe(periods=request.periods)
    forecast = model.predict(future)
    
    # Extract relevant predictions
    predictions = []
    for _, row in forecast.tail(request.periods).iterrows():
        predictions.append({
            'date': row['ds'].isoformat(),
            'predicted': float(row['yhat']),
            'lower_bound': float(row['yhat_lower']),
            'upper_bound': float(row['yhat_upper'])
        })
    
    return {
        'metric': request.metric,
        'predictions': predictions,
        'model_components': {
            'trend': float(forecast['trend'].iloc[-1]),
            'weekly_seasonality': float(forecast['weekly'].iloc[-1]),
            'yearly_seasonality': float(forecast['yearly'].iloc[-1])
        }
    }


@app.post("/attribution/calculate")
async def calculate_attribution(customer_journeys: List[Dict[str, Any]], model_type: str = "time_decay"):
    """Calculate multi-touch attribution"""
    
    model = RevenueAttributionModel()
    result = await model.calculate_attribution(customer_journeys, model_type)
    
    return result


@app.post("/optimize/campaign")
async def optimize_campaign(request: OptimizationRequest):
    """Optimize campaign parameters"""
    
    optimizer = ConversionOptimizer()
    result = await optimizer.optimize_campaign(request)
    
    return result


@app.post("/train/model")
async def train_model(model_type: str, background_tasks: BackgroundTasks):
    """Train a specific ML model"""
    
    if model_type == "open_rate":
        # Fetch training data from ClickHouse
        query = """
        SELECT 
            subject,
            send_hour,
            audience_size,
            open_rate,
            click_rate
        FROM email_campaigns
        WHERE sent_date >= today() - 90
        """
        
        # Execute query and prepare data
        # This is simplified - add proper data fetching
        
        background_tasks.add_task(train_open_rate_model)
        
        return {"message": "Model training started", "model_type": model_type}
    
    else:
        raise HTTPException(status_code=400, detail=f"Unknown model type: {model_type}")


async def train_open_rate_model():
    """Background task to train open rate model"""
    predictor = EmailOpenRatePredictor()
    
    # Fetch data (simplified)
    # In production, fetch from ClickHouse/PostgreSQL
    
    # Train model
    # await predictor.train(training_data)
    
    # Save to model registry
    model_registry['open_rate_predictor'] = predictor
    
    logger.info("Open rate model trained successfully")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)