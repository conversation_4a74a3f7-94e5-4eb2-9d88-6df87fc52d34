FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for ML libraries
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 analyticsuser && chown -R analyticsuser:analyticsuser /app
USER analyticsuser

# Expose API port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "analytics_engine:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "2"]