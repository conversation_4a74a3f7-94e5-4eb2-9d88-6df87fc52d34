/**
 * Integration Gateway Service
 * Handles 50+ production API integrations for NeuroColony
 */

const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const Redis = require('ioredis');
const Bull = require('bull');
const winston = require('winston');
const { register, Counter, Histogram, Gauge } = require('prom-client');
const CryptoJS = require('crypto-js');

// Load environment variables
require('dotenv').config();

// Import integration handlers
const integrationHandlers = require('./integrations');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5001;

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Metrics
const integrationCounter = new Counter({
  name: 'integration_requests_total',
  help: 'Total integration API requests',
  labelNames: ['platform', 'method', 'status']
});

const integrationDuration = new Histogram({
  name: 'integration_request_duration_seconds',
  help: 'Integration request duration',
  labelNames: ['platform', 'method']
});

const activeIntegrations = new Gauge({
  name: 'active_integrations',
  help: 'Number of active integrations',
  labelNames: ['platform']
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// MongoDB connection
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  logger.info('Connected to MongoDB');
}).catch(err => {
  logger.error('MongoDB connection error:', err);
});

// Redis connection
const redis = new Redis(process.env.REDIS_URL);
redis.on('connect', () => {
  logger.info('Connected to Redis');
});

// Bull queue for async processing
const integrationQueue = new Bull('integration-queue', process.env.REDIS_URL);

// Encryption helper
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(JSON.stringify(data), process.env.ENCRYPTION_KEY).toString();
};

const decryptData = (encryptedData) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, process.env.ENCRYPTION_KEY);
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
};

// Integration credential management
class CredentialManager {
  async getCredentials(userId, platform) {
    const cacheKey = `creds:${userId}:${platform}`;
    
    // Check cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      return decryptData(cached);
    }
    
    // Fetch from database
    const Integration = mongoose.model('Integration');
    const integration = await Integration.findOne({ userId, platform });
    
    if (!integration || !integration.credentials) {
      throw new Error(`No credentials found for ${platform}`);
    }
    
    const credentials = decryptData(integration.credentials);
    
    // Cache for 5 minutes
    await redis.set(cacheKey, integration.credentials, 'EX', 300);
    
    return credentials;
  }
  
  async saveCredentials(userId, platform, credentials) {
    const encrypted = encryptData(credentials);
    
    const Integration = mongoose.model('Integration');
    await Integration.findOneAndUpdate(
      { userId, platform },
      {
        userId,
        platform,
        credentials: encrypted,
        isActive: true,
        lastSync: new Date()
      },
      { upsert: true }
    );
    
    // Clear cache
    await redis.del(`creds:${userId}:${platform}`);
  }
}

const credentialManager = new CredentialManager();

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    integrations: Object.keys(integrationHandlers).length
  });
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// List available integrations
app.get('/api/integrations', (req, res) => {
  const integrations = Object.keys(integrationHandlers).map(key => {
    const handler = integrationHandlers[key];
    return {
      id: key,
      name: handler.name,
      category: handler.category,
      features: handler.features,
      authType: handler.authType,
      webhooksSupported: handler.webhooksSupported || false
    };
  });
  
  res.json({ integrations });
});

// Connect integration (OAuth flow)
app.post('/api/integrations/:platform/connect', async (req, res) => {
  const { platform } = req.params;
  const { userId, redirectUri } = req.body;
  
  try {
    const handler = integrationHandlers[platform];
    if (!handler) {
      return res.status(404).json({ error: 'Integration not found' });
    }
    
    const authUrl = await handler.getAuthUrl(userId, redirectUri);
    res.json({ authUrl });
    
  } catch (error) {
    logger.error('Integration connect error:', error);
    res.status(500).json({ error: 'Failed to initiate connection' });
  }
});

// OAuth callback
app.get('/api/integrations/:platform/callback', async (req, res) => {
  const { platform } = req.params;
  const { code, state } = req.query;
  
  try {
    const handler = integrationHandlers[platform];
    if (!handler) {
      return res.status(404).json({ error: 'Integration not found' });
    }
    
    // Extract userId from state
    const { userId } = JSON.parse(Buffer.from(state, 'base64').toString());
    
    // Exchange code for tokens
    const tokens = await handler.exchangeCodeForTokens(code);
    
    // Save credentials
    await credentialManager.saveCredentials(userId, platform, tokens);
    
    // Test connection
    const testResult = await handler.testConnection(tokens);
    
    res.json({
      success: true,
      platform,
      connected: true,
      accountInfo: testResult
    });
    
  } catch (error) {
    logger.error('OAuth callback error:', error);
    res.status(500).json({ error: 'Failed to complete authentication' });
  }
});

// Execute integration method
app.post('/api/integrations/:platform/:method', async (req, res) => {
  const { platform, method } = req.params;
  const { userId, params } = req.body;
  
  const startTime = Date.now();
  
  try {
    const handler = integrationHandlers[platform];
    if (!handler) {
      integrationCounter.labels(platform, method, 'not_found').inc();
      return res.status(404).json({ error: 'Integration not found' });
    }
    
    if (!handler[method]) {
      integrationCounter.labels(platform, method, 'method_not_found').inc();
      return res.status(404).json({ error: 'Method not found' });
    }
    
    // Get credentials
    const credentials = await credentialManager.getCredentials(userId, platform);
    
    // Execute method
    const result = await handler[method](credentials, params);
    
    // Record metrics
    const duration = (Date.now() - startTime) / 1000;
    integrationDuration.labels(platform, method).observe(duration);
    integrationCounter.labels(platform, method, 'success').inc();
    
    res.json({
      success: true,
      platform,
      method,
      result,
      executionTime: duration
    });
    
  } catch (error) {
    logger.error('Integration execution error:', error);
    integrationCounter.labels(platform, method, 'error').inc();
    
    res.status(500).json({
      error: error.message,
      platform,
      method
    });
  }
});

// Webhook endpoints
app.post('/api/webhooks/:platform', async (req, res) => {
  const { platform } = req.params;
  
  try {
    const handler = integrationHandlers[platform];
    if (!handler || !handler.handleWebhook) {
      return res.status(404).json({ error: 'Webhook handler not found' });
    }
    
    // Verify webhook signature
    const isValid = await handler.verifyWebhook(req);
    if (!isValid) {
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }
    
    // Queue webhook for processing
    await integrationQueue.add('process-webhook', {
      platform,
      headers: req.headers,
      body: req.body
    });
    
    res.status(200).json({ received: true });
    
  } catch (error) {
    logger.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Bulk sync endpoint
app.post('/api/integrations/:platform/sync', async (req, res) => {
  const { platform } = req.params;
  const { userId, syncType, options } = req.body;
  
  try {
    // Queue sync job
    const job = await integrationQueue.add('sync-data', {
      platform,
      userId,
      syncType,
      options
    });
    
    res.json({
      success: true,
      jobId: job.id,
      status: 'queued'
    });
    
  } catch (error) {
    logger.error('Sync error:', error);
    res.status(500).json({ error: 'Failed to queue sync' });
  }
});

// Get sync status
app.get('/api/integrations/sync/:jobId', async (req, res) => {
  const { jobId } = req.params;
  
  try {
    const job = await integrationQueue.getJob(jobId);
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }
    
    const state = await job.getState();
    const progress = job.progress();
    
    res.json({
      jobId,
      state,
      progress,
      result: job.returnvalue,
      failedReason: job.failedReason
    });
    
  } catch (error) {
    logger.error('Get sync status error:', error);
    res.status(500).json({ error: 'Failed to get sync status' });
  }
});

// Process webhook queue
integrationQueue.process('process-webhook', async (job) => {
  const { platform, headers, body } = job.data;
  
  try {
    const handler = integrationHandlers[platform];
    const result = await handler.handleWebhook(headers, body);
    
    // Store webhook data
    const Webhook = mongoose.model('Webhook');
    await Webhook.create({
      platform,
      event: result.event,
      data: result.data,
      processedAt: new Date()
    });
    
    return result;
    
  } catch (error) {
    logger.error('Webhook processing error:', error);
    throw error;
  }
});

// Process sync queue
integrationQueue.process('sync-data', async (job) => {
  const { platform, userId, syncType, options } = job.data;
  
  try {
    const handler = integrationHandlers[platform];
    const credentials = await credentialManager.getCredentials(userId, platform);
    
    // Update progress
    job.progress(10);
    
    // Perform sync
    const result = await handler.syncData(credentials, syncType, options, (progress) => {
      job.progress(progress);
    });
    
    // Update last sync time
    const Integration = mongoose.model('Integration');
    await Integration.findOneAndUpdate(
      { userId, platform },
      { lastSync: new Date() }
    );
    
    job.progress(100);
    
    return result;
    
  } catch (error) {
    logger.error('Sync processing error:', error);
    throw error;
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Integration Gateway Service running on port ${PORT}`);
  logger.info(`${Object.keys(integrationHandlers).length} integrations available`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  await integrationQueue.close();
  await redis.quit();
  await mongoose.connection.close();
  
  process.exit(0);
});

module.exports = app;