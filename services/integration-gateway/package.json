{"name": "neurocolony-integration-gateway", "version": "1.0.0", "description": "Integration Gateway Service for NeuroColony - 50+ API integrations", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.5", "node-fetch": "^3.3.2", "bull": "^4.12.0", "ioredis": "^5.3.2", "mongoose": "^8.0.4", "joi": "^17.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "prom-client": "^15.1.0", "crypto-js": "^4.2.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@sendgrid/mail": "^8.1.0", "@hubspot/api-client": "^11.0.0", "klaviyo-api": "^7.0.0", "@getbrevo/brevo": "^1.0.1", "activecampaign": "^1.2.4", "convertkit-node": "^1.0.0", "stripe": "^14.12.0", "shopify-api-node": "^3.13.1", "@google-analytics/data": "^4.1.0", "googleapis": "^130.0.0", "salesforce-node": "^0.1.8", "pipedrive": "^13.0.0", "slack-node": "^0.1.8", "@microsoft/microsoft-graph-client": "^3.0.7", "twilio": "^4.20.1", "aws-sdk": "^2.1527.0", "oauth": "^0.10.0", "simple-oauth2": "^5.0.0", "passport": "^0.7.0", "passport-oauth2": "^1.7.0", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=20.0.0"}}