/**
 * Integration Handlers Registry
 * 50+ production-ready API integrations
 */

// Email Marketing Platforms
const mailchimp = require('./email/mailchimp');
const sendgrid = require('./email/sendgrid');
const convertkit = require('./email/convertkit');
const activecampaign = require('./email/activecampaign');
const klaviyo = require('./email/klaviyo');
const brevo = require('./email/brevo');
const constantContact = require('./email/constant-contact');
const getresponse = require('./email/getresponse');
const campaignMonitor = require('./email/campaign-monitor');
const aweber = require('./email/aweber');

// CRM Platforms
const salesforce = require('./crm/salesforce');
const hubspot = require('./crm/hubspot');
const pipedrive = require('./crm/pipedrive');
const zoho = require('./crm/zoho');
const dynamics365 = require('./crm/dynamics365');
const freshsales = require('./crm/freshsales');
const copper = require('./crm/copper');
const insightly = require('./crm/insightly');

// Analytics & Tracking
const googleAnalytics = require('./analytics/google-analytics');
const googleAds = require('./analytics/google-ads');
const facebookAds = require('./analytics/facebook-ads');
const linkedinAds = require('./analytics/linkedin-ads');
const mixpanel = require('./analytics/mixpanel');
const amplitude = require('./analytics/amplitude');
const segment = require('./analytics/segment');
const heap = require('./analytics/heap');

// E-commerce
const shopify = require('./ecommerce/shopify');
const woocommerce = require('./ecommerce/woocommerce');
const bigcommerce = require('./ecommerce/bigcommerce');
const magento = require('./ecommerce/magento');
const squarespace = require('./ecommerce/squarespace');
const ecwid = require('./ecommerce/ecwid');

// Payment & Billing
const stripe = require('./payment/stripe');
const paypal = require('./payment/paypal');
const square = require('./payment/square');
const razorpay = require('./payment/razorpay');
const paddle = require('./payment/paddle');

// Communication
const slack = require('./communication/slack');
const microsoftTeams = require('./communication/microsoft-teams');
const discord = require('./communication/discord');
const twilio = require('./communication/twilio');
const whatsapp = require('./communication/whatsapp');
const telegram = require('./communication/telegram');

// Productivity & Project Management
const asana = require('./productivity/asana');
const trello = require('./productivity/trello');
const monday = require('./productivity/monday');
const clickup = require('./productivity/clickup');
const notion = require('./productivity/notion');
const airtable = require('./productivity/airtable');

// Social Media
const twitter = require('./social/twitter');
const facebook = require('./social/facebook');
const instagram = require('./social/instagram');
const linkedin = require('./social/linkedin');
const youtube = require('./social/youtube');
const tiktok = require('./social/tiktok');

// Export all integrations
module.exports = {
  // Email Marketing (10)
  mailchimp,
  sendgrid,
  convertkit,
  activecampaign,
  klaviyo,
  brevo,
  constantContact,
  getresponse,
  campaignMonitor,
  aweber,
  
  // CRM (8)
  salesforce,
  hubspot,
  pipedrive,
  zoho,
  dynamics365,
  freshsales,
  copper,
  insightly,
  
  // Analytics (8)
  googleAnalytics,
  googleAds,
  facebookAds,
  linkedinAds,
  mixpanel,
  amplitude,
  segment,
  heap,
  
  // E-commerce (6)
  shopify,
  woocommerce,
  bigcommerce,
  magento,
  squarespace,
  ecwid,
  
  // Payment (5)
  stripe,
  paypal,
  square,
  razorpay,
  paddle,
  
  // Communication (6)
  slack,
  microsoftTeams,
  discord,
  twilio,
  whatsapp,
  telegram,
  
  // Productivity (6)
  asana,
  trello,
  monday,
  clickup,
  notion,
  airtable,
  
  // Social Media (6)
  twitter,
  facebook,
  instagram,
  linkedin,
  youtube,
  tiktok
};