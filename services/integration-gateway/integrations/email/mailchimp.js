/**
 * Mailchimp Integration Handler
 * Full production implementation with all features
 */

const axios = require('axios');
const crypto = require('crypto');
const { OAuth2 } = require('oauth');

class MailchimpIntegration {
  constructor() {
    this.name = 'Mailchimp';
    this.category = 'email_marketing';
    this.authType = 'oauth2';
    this.webhooksSupported = true;
    this.features = [
      'lists_management',
      'campaigns',
      'automations',
      'segments',
      'tags',
      'analytics',
      'templates',
      'webhooks'
    ];
    
    this.oauth2 = new OAuth2(
      process.env.MAILCHIMP_CLIENT_ID,
      process.env.MAILCHIMP_CLIENT_SECRET,
      'https://login.mailchimp.com',
      '/oauth2/authorize',
      '/oauth2/token',
      null
    );
  }
  
  // OAuth Methods
  async getAuthUrl(userId, redirectUri) {
    const state = Buffer.from(JSON.stringify({ userId })).toString('base64');
    
    return this.oauth2.getAuthorizeUrl({
      redirect_uri: redirectUri,
      state: state
    });
  }
  
  async exchangeCodeForTokens(code) {
    return new Promise((resolve, reject) => {
      this.oauth2.getOAuthAccessToken(
        code,
        { grant_type: 'authorization_code' },
        (error, accessToken, refreshToken, results) => {
          if (error) {
            reject(error);
          } else {
            resolve({
              accessToken,
              refreshToken,
              apiEndpoint: results.api_endpoint,
              expiresIn: results.expires_in
            });
          }
        }
      );
    });
  }
  
  async refreshAccessToken(refreshToken) {
    return new Promise((resolve, reject) => {
      this.oauth2.getOAuthAccessToken(
        refreshToken,
        { grant_type: 'refresh_token' },
        (error, accessToken, newRefreshToken, results) => {
          if (error) {
            reject(error);
          } else {
            resolve({
              accessToken,
              refreshToken: newRefreshToken || refreshToken,
              expiresIn: results.expires_in
            });
          }
        }
      );
    });
  }
  
  // Connection Test
  async testConnection(credentials) {
    try {
      const response = await axios.get(`${credentials.apiEndpoint}/3.0/`, {
        headers: {
          'Authorization': `Bearer ${credentials.accessToken}`
        }
      });
      
      return {
        accountId: response.data.account_id,
        accountName: response.data.account_name,
        email: response.data.email,
        role: response.data.role,
        memberSince: response.data.member_since
      };
    } catch (error) {
      throw new Error(`Connection test failed: ${error.message}`);
    }
  }
  
  // List Management
  async getLists(credentials, params = {}) {
    const { count = 100, offset = 0 } = params;
    
    try {
      const response = await axios.get(
        `${credentials.apiEndpoint}/3.0/lists`,
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          },
          params: { count, offset }
        }
      );
      
      return response.data.lists.map(list => ({
        id: list.id,
        name: list.name,
        memberCount: list.stats.member_count,
        unsubscribeCount: list.stats.unsubscribe_count,
        openRate: list.stats.open_rate,
        clickRate: list.stats.click_rate,
        dateCreated: list.date_created,
        listRating: list.list_rating
      }));
    } catch (error) {
      throw new Error(`Failed to get lists: ${error.message}`);
    }
  }
  
  async createList(credentials, params) {
    const { name, permissionReminder, emailTypeOption, contact, campaignDefaults } = params;
    
    try {
      const response = await axios.post(
        `${credentials.apiEndpoint}/3.0/lists`,
        {
          name,
          permission_reminder: permissionReminder,
          email_type_option: emailTypeOption,
          contact,
          campaign_defaults: campaignDefaults
        },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        id: response.data.id,
        name: response.data.name,
        webId: response.data.web_id
      };
    } catch (error) {
      throw new Error(`Failed to create list: ${error.message}`);
    }
  }
  
  // Member Management
  async addMember(credentials, params) {
    const { listId, email, status = 'subscribed', mergeFields = {}, tags = [] } = params;
    
    const subscriberHash = crypto.createHash('md5').update(email.toLowerCase()).digest('hex');
    
    try {
      const response = await axios.put(
        `${credentials.apiEndpoint}/3.0/lists/${listId}/members/${subscriberHash}`,
        {
          email_address: email,
          status: status,
          merge_fields: mergeFields,
          tags: tags
        },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        id: response.data.id,
        email: response.data.email_address,
        status: response.data.status,
        uniqueEmailId: response.data.unique_email_id
      };
    } catch (error) {
      throw new Error(`Failed to add member: ${error.message}`);
    }
  }
  
  async batchAddMembers(credentials, params) {
    const { listId, members, updateExisting = true } = params;
    
    try {
      const operations = members.map(member => ({
        method: updateExisting ? 'PUT' : 'POST',
        path: `/lists/${listId}/members/${crypto.createHash('md5').update(member.email.toLowerCase()).digest('hex')}`,
        body: JSON.stringify({
          email_address: member.email,
          status: member.status || 'subscribed',
          merge_fields: member.mergeFields || {},
          tags: member.tags || []
        })
      }));
      
      const response = await axios.post(
        `${credentials.apiEndpoint}/3.0/batches`,
        { operations },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        batchId: response.data.id,
        status: response.data.status,
        totalOperations: response.data.total_operations
      };
    } catch (error) {
      throw new Error(`Failed to batch add members: ${error.message}`);
    }
  }
  
  // Campaign Management
  async createCampaign(credentials, params) {
    const { type = 'regular', recipients, settings, tracking, content } = params;
    
    try {
      const response = await axios.post(
        `${credentials.apiEndpoint}/3.0/campaigns`,
        {
          type,
          recipients,
          settings,
          tracking,
          content_type: 'template'
        },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      const campaignId = response.data.id;
      
      // Set campaign content if provided
      if (content) {
        await axios.put(
          `${credentials.apiEndpoint}/3.0/campaigns/${campaignId}/content`,
          content,
          {
            headers: {
              'Authorization': `Bearer ${credentials.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }
      
      return {
        campaignId,
        webId: response.data.web_id,
        type: response.data.type,
        status: response.data.status
      };
    } catch (error) {
      throw new Error(`Failed to create campaign: ${error.message}`);
    }
  }
  
  async sendCampaign(credentials, params) {
    const { campaignId } = params;
    
    try {
      await axios.post(
        `${credentials.apiEndpoint}/3.0/campaigns/${campaignId}/actions/send`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          }
        }
      );
      
      return { success: true, campaignId };
    } catch (error) {
      throw new Error(`Failed to send campaign: ${error.message}`);
    }
  }
  
  async getCampaignStats(credentials, params) {
    const { campaignId } = params;
    
    try {
      const response = await axios.get(
        `${credentials.apiEndpoint}/3.0/reports/${campaignId}`,
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          }
        }
      );
      
      return {
        campaignId: response.data.id,
        emailsSent: response.data.emails_sent,
        opens: {
          total: response.data.opens.opens_total,
          unique: response.data.opens.unique_opens,
          rate: response.data.opens.open_rate
        },
        clicks: {
          total: response.data.clicks.clicks_total,
          unique: response.data.clicks.unique_clicks,
          rate: response.data.clicks.click_rate
        },
        unsubscribed: response.data.unsubscribed,
        bounces: {
          hard: response.data.bounces.hard_bounces,
          soft: response.data.bounces.soft_bounces,
          syntaxErrors: response.data.bounces.syntax_errors
        },
        industryAverage: {
          openRate: response.data.industry_stats.open_rate,
          clickRate: response.data.industry_stats.click_rate
        }
      };
    } catch (error) {
      throw new Error(`Failed to get campaign stats: ${error.message}`);
    }
  }
  
  // Automation Management
  async getAutomations(credentials, params = {}) {
    const { count = 100, offset = 0 } = params;
    
    try {
      const response = await axios.get(
        `${credentials.apiEndpoint}/3.0/automations`,
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          },
          params: { count, offset }
        }
      );
      
      return response.data.automations.map(automation => ({
        id: automation.id,
        workflowType: automation.settings.workflow_type,
        title: automation.settings.title,
        status: automation.status,
        recipientsListId: automation.recipients.list_id,
        triggerSettings: automation.trigger_settings,
        reportSummary: automation.report_summary
      }));
    } catch (error) {
      throw new Error(`Failed to get automations: ${error.message}`);
    }
  }
  
  // Segment Management
  async createSegment(credentials, params) {
    const { listId, name, conditions } = params;
    
    try {
      const response = await axios.post(
        `${credentials.apiEndpoint}/3.0/lists/${listId}/segments`,
        {
          name,
          static_segment: conditions ? undefined : [],
          options: conditions || undefined
        },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        id: response.data.id,
        name: response.data.name,
        memberCount: response.data.member_count,
        type: response.data.type
      };
    } catch (error) {
      throw new Error(`Failed to create segment: ${error.message}`);
    }
  }
  
  // Analytics
  async getListGrowthHistory(credentials, params) {
    const { listId, count = 10 } = params;
    
    try {
      const response = await axios.get(
        `${credentials.apiEndpoint}/3.0/lists/${listId}/growth-history`,
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          },
          params: { count }
        }
      );
      
      return response.data.history.map(item => ({
        month: item.month,
        subscribed: item.subscribed,
        unsubscribed: item.unsubscribed,
        reconfirm: item.reconfirm,
        cleaned: item.cleaned,
        pending: item.pending,
        existing: item.existing
      }));
    } catch (error) {
      throw new Error(`Failed to get list growth history: ${error.message}`);
    }
  }
  
  // Webhook Management
  async createWebhook(credentials, params) {
    const { listId, url, events, sources } = params;
    
    try {
      const response = await axios.post(
        `${credentials.apiEndpoint}/3.0/lists/${listId}/webhooks`,
        {
          url,
          events,
          sources
        },
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        id: response.data.id,
        url: response.data.url,
        events: response.data.events,
        sources: response.data.sources
      };
    } catch (error) {
      throw new Error(`Failed to create webhook: ${error.message}`);
    }
  }
  
  async verifyWebhook(req) {
    const signature = req.headers['x-mailchimp-signature'];
    if (!signature) return false;
    
    const webhookKey = process.env.MAILCHIMP_WEBHOOK_KEY;
    const payload = JSON.stringify(req.body);
    
    const expectedSignature = crypto
      .createHmac('sha256', webhookKey)
      .update(payload)
      .digest('hex');
    
    return signature === expectedSignature;
  }
  
  async handleWebhook(headers, body) {
    const eventType = body.type;
    
    const eventData = {
      event: eventType,
      data: {
        listId: body.data.list_id,
        email: body.data.email,
        mergeFields: body.data.merges,
        timestamp: body.fired_at
      }
    };
    
    // Handle specific event types
    switch (eventType) {
      case 'subscribe':
        eventData.data.action = 'user_subscribed';
        break;
      case 'unsubscribe':
        eventData.data.action = 'user_unsubscribed';
        break;
      case 'profile':
        eventData.data.action = 'profile_updated';
        break;
      case 'cleaned':
        eventData.data.action = 'email_cleaned';
        break;
      case 'campaign':
        eventData.data.action = 'campaign_event';
        eventData.data.campaignId = body.data.id;
        break;
    }
    
    return eventData;
  }
  
  // Bulk Data Sync
  async syncData(credentials, syncType, options, progressCallback) {
    switch (syncType) {
      case 'import_contacts':
        return await this._importContacts(credentials, options, progressCallback);
      case 'export_contacts':
        return await this._exportContacts(credentials, options, progressCallback);
      case 'sync_campaigns':
        return await this._syncCampaigns(credentials, options, progressCallback);
      default:
        throw new Error(`Unknown sync type: ${syncType}`);
    }
  }
  
  async _importContacts(credentials, options, progressCallback) {
    const { contacts, listId } = options;
    const batchSize = 500;
    const totalBatches = Math.ceil(contacts.length / batchSize);
    
    let imported = 0;
    const results = [];
    
    for (let i = 0; i < totalBatches; i++) {
      const batch = contacts.slice(i * batchSize, (i + 1) * batchSize);
      
      const result = await this.batchAddMembers(credentials, {
        listId,
        members: batch.map(contact => ({
          email: contact.email,
          mergeFields: {
            FNAME: contact.firstName,
            LNAME: contact.lastName,
            COMPANY: contact.company
          },
          tags: contact.tags || []
        }))
      });
      
      results.push(result);
      imported += batch.length;
      
      progressCallback(Math.round((imported / contacts.length) * 100));
    }
    
    return {
      totalImported: imported,
      batchResults: results
    };
  }
  
  async _exportContacts(credentials, options, progressCallback) {
    const { listId, includeUnsubscribed = false } = options;
    const limit = 1000;
    let offset = 0;
    let allContacts = [];
    
    progressCallback(10);
    
    // Get total count
    const listInfo = await axios.get(
      `${credentials.apiEndpoint}/3.0/lists/${listId}`,
      {
        headers: {
          'Authorization': `Bearer ${credentials.accessToken}`
        }
      }
    );
    
    const totalMembers = listInfo.data.stats.member_count;
    
    while (true) {
      const response = await axios.get(
        `${credentials.apiEndpoint}/3.0/lists/${listId}/members`,
        {
          headers: {
            'Authorization': `Bearer ${credentials.accessToken}`
          },
          params: {
            count: limit,
            offset: offset,
            status: includeUnsubscribed ? 'all' : 'subscribed'
          }
        }
      );
      
      const members = response.data.members;
      if (members.length === 0) break;
      
      allContacts = allContacts.concat(members.map(member => ({
        email: member.email_address,
        firstName: member.merge_fields.FNAME,
        lastName: member.merge_fields.LNAME,
        company: member.merge_fields.COMPANY,
        status: member.status,
        tags: member.tags.map(tag => tag.name),
        subscribedAt: member.timestamp_opt,
        lastChanged: member.last_changed
      })));
      
      offset += limit;
      progressCallback(Math.round((offset / totalMembers) * 90) + 10);
      
      if (members.length < limit) break;
    }
    
    progressCallback(100);
    
    return {
      totalExported: allContacts.length,
      contacts: allContacts
    };
  }
  
  async _syncCampaigns(credentials, options, progressCallback) {
    const { startDate, endDate } = options;
    
    progressCallback(10);
    
    const campaigns = await axios.get(
      `${credentials.apiEndpoint}/3.0/campaigns`,
      {
        headers: {
          'Authorization': `Bearer ${credentials.accessToken}`
        },
        params: {
          count: 1000,
          since_create_time: startDate,
          before_create_time: endDate
        }
      }
    );
    
    progressCallback(40);
    
    const campaignStats = [];
    const totalCampaigns = campaigns.data.campaigns.length;
    
    for (let i = 0; i < totalCampaigns; i++) {
      const campaign = campaigns.data.campaigns[i];
      
      if (campaign.status === 'sent') {
        const stats = await this.getCampaignStats(credentials, {
          campaignId: campaign.id
        });
        
        campaignStats.push({
          ...campaign,
          stats
        });
      }
      
      progressCallback(40 + Math.round((i / totalCampaigns) * 60));
    }
    
    progressCallback(100);
    
    return {
      totalSynced: campaignStats.length,
      campaigns: campaignStats
    };
  }
}

module.exports = new MailchimpIntegration();