/**
 * Workflow Engine Worker
 * Temporal-based workflow orchestration for NeuroColony
 */

const { Worker } = require('@temporalio/worker');
const { Connection } = require('@temporalio/client');
const mongoose = require('mongoose');
const Redis = require('redis');
const winston = require('winston');
const { register, Counter, Histogram, Gauge } = require('prom-client');
require('dotenv').config();

// Import activities
const activities = require('./activities');
const workflows = require('./workflows');

// Logger configuration
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Metrics
const workflowCounter = new Counter({
  name: 'workflow_executions_total',
  help: 'Total workflow executions',
  labelNames: ['workflow_type', 'status']
});

const workflowDuration = new Histogram({
  name: 'workflow_duration_seconds',
  help: 'Workflow execution duration',
  labelNames: ['workflow_type']
});

const activeWorkflows = new Gauge({
  name: 'active_workflows',
  help: 'Number of active workflows'
});

// Redis client
let redisClient;

// MongoDB connection
async function connectMongoDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    throw error;
  }
}

// Redis connection
async function connectRedis() {
  redisClient = Redis.createClient({
    url: process.env.REDIS_URL
  });
  
  redisClient.on('error', (err) => {
    logger.error('Redis Client Error:', err);
  });
  
  await redisClient.connect();
  logger.info('Connected to Redis');
}

// Main worker function
async function run() {
  try {
    // Connect to databases
    await connectMongoDB();
    await connectRedis();
    
    // Create connection to Temporal
    const connection = await Connection.connect({
      address: process.env.TEMPORAL_ADDRESS || 'temporal:7233',
    });
    
    // Create worker
    const worker = await Worker.create({
      connection,
      namespace: 'default',
      taskQueue: 'neurocolony-workflows',
      workflowsPath: require.resolve('./workflows'),
      activities,
      maxConcurrentActivityTaskExecutions: 100,
      maxConcurrentWorkflowTaskExecutions: 100,
    });
    
    logger.info('Worker started, listening on task queue: neurocolony-workflows');
    
    // Run worker
    await worker.run();
    
  } catch (error) {
    logger.error('Worker failed to start:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  
  if (redisClient) {
    await redisClient.quit();
  }
  
  await mongoose.connection.close();
  
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Export for testing
module.exports = { run };

// Start worker if running directly
if (require.main === module) {
  run().catch((err) => {
    logger.error('Worker error:', err);
    process.exit(1);
  });
}