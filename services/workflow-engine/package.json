{"name": "neurocolony-workflow-engine", "version": "1.0.0", "description": "Workflow Engine for NeuroColony - Temporal-based workflow orchestration", "main": "worker.js", "scripts": {"start": "node worker.js", "dev": "nodemon worker.js", "test": "jest"}, "dependencies": {"@temporalio/client": "^1.8.6", "@temporalio/worker": "^1.8.6", "@temporalio/activity": "^1.8.6", "@temporalio/workflow": "^1.8.6", "mongoose": "^8.0.4", "redis": "^4.6.12", "axios": "^1.6.5", "winston": "^3.11.0", "prom-client": "^15.1.0", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=20.0.0"}}