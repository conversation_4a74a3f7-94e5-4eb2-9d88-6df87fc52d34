/**
 * Workflow Definitions
 * Complex multi-step workflows for marketing automation
 */

const { proxyActivities, sleep, defineQuery, setHand<PERSON> } = require('@temporalio/workflow');

// Import activity types
const activities = proxyActivities({
  startOptions: {
    startToCloseTimeout: '5 minutes',
    retry: {
      initialInterval: '1s',
      backoffCoefficient: 2,
      maximumAttempts: 5,
    },
  },
});

// Define queries for workflow state
const getProgressQuery = defineQuery('getProgress');
const getStatusQuery = defineQuery('getStatus');

/**
 * Email Campaign Workflow
 * Orchestrates a complete email marketing campaign
 */
async function emailCampaignWorkflow(input) {
  const state = {
    progress: 0,
    status: 'initializing',
    steps: [],
    results: {},
  };

  // Query handlers
  setHandler(getProgressQuery, () => state.progress);
  setHandler(getStatusQuery, () => state.status);

  try {
    // Step 1: Validate campaign settings
    state.status = 'validating';
    state.progress = 10;
    const validation = await activities.validateCampaignSettings(input);
    state.steps.push({ step: 'validation', status: 'completed', result: validation });

    // Step 2: Segment audience
    state.status = 'segmenting_audience';
    state.progress = 25;
    const segments = await activities.segmentAudience({
      campaignId: input.campaignId,
      criteria: input.targetingCriteria,
    });
    state.results.segments = segments;
    state.steps.push({ step: 'segmentation', status: 'completed', result: segments });

    // Step 3: Generate email content
    state.status = 'generating_content';
    state.progress = 40;
    const content = await activities.generateEmailContent({
      campaignId: input.campaignId,
      template: input.template,
      personalization: input.personalizationSettings,
      segments: segments.segmentIds,
    });
    state.results.content = content;
    state.steps.push({ step: 'content_generation', status: 'completed', result: content });

    // Step 4: Optimize send times
    state.status = 'optimizing_send_times';
    state.progress = 55;
    const sendSchedule = await activities.optimizeSendTimes({
      segments: segments.segmentIds,
      timezone: input.timezone,
      historicalData: input.useHistoricalData,
    });
    state.results.sendSchedule = sendSchedule;
    state.steps.push({ step: 'send_time_optimization', status: 'completed', result: sendSchedule });

    // Step 5: Create A/B test variants
    if (input.enableABTesting) {
      state.status = 'creating_ab_tests';
      state.progress = 70;
      const abTests = await activities.createABTestVariants({
        content: content,
        testingParameters: input.abTestSettings,
      });
      state.results.abTests = abTests;
      state.steps.push({ step: 'ab_test_creation', status: 'completed', result: abTests });
    }

    // Step 6: Schedule campaign
    state.status = 'scheduling_campaign';
    state.progress = 85;
    const scheduled = await activities.scheduleCampaign({
      campaignId: input.campaignId,
      content: content,
      segments: segments.segmentIds,
      schedule: sendSchedule,
      abTests: state.results.abTests,
    });
    state.results.scheduled = scheduled;
    state.steps.push({ step: 'scheduling', status: 'completed', result: scheduled });

    // Step 7: Set up tracking
    state.status = 'configuring_tracking';
    state.progress = 95;
    const tracking = await activities.setupCampaignTracking({
      campaignId: input.campaignId,
      trackingSettings: input.trackingSettings,
    });
    state.results.tracking = tracking;
    state.steps.push({ step: 'tracking_setup', status: 'completed', result: tracking });

    // Complete
    state.status = 'completed';
    state.progress = 100;

    return {
      success: true,
      campaignId: input.campaignId,
      results: state.results,
      steps: state.steps,
      completedAt: new Date().toISOString(),
    };

  } catch (error) {
    state.status = 'failed';
    state.steps.push({ step: 'error', status: 'failed', error: error.message });
    
    throw error;
  }
}

/**
 * Lead Nurturing Workflow
 * Automated multi-touch lead nurturing sequence
 */
async function leadNurturingWorkflow(input) {
  const state = {
    progress: 0,
    status: 'initializing',
    touchpoints: [],
    leadScores: {},
  };

  setHandler(getProgressQuery, () => state.progress);
  setHandler(getStatusQuery, () => state.status);

  try {
    // Step 1: Analyze lead behavior
    state.status = 'analyzing_leads';
    state.progress = 15;
    const leadAnalysis = await activities.analyzeLeadBehavior({
      leadIds: input.leadIds,
      lookbackDays: input.behaviorLookback || 30,
    });
    state.leadScores = leadAnalysis.scores;

    // Step 2: Create nurturing path
    state.status = 'creating_nurture_path';
    state.progress = 30;
    const nurturePath = await activities.createNurturePath({
      leadScores: leadAnalysis.scores,
      businessType: input.businessType,
      nurturingGoals: input.goals,
    });

    // Step 3: Execute nurturing touches
    for (let i = 0; i < nurturePath.touches.length; i++) {
      const touch = nurturePath.touches[i];
      state.status = `executing_touch_${i + 1}`;
      state.progress = 30 + ((i + 1) / nurturePath.touches.length) * 60;

      // Wait for the specified delay
      if (touch.delayDays > 0) {
        await sleep(`${touch.delayDays} days`);
      }

      // Execute the touch
      const touchResult = await activities.executeNurturingTouch({
        touch: touch,
        leadIds: input.leadIds,
        personalization: input.personalizationLevel,
      });

      state.touchpoints.push({
        touchNumber: i + 1,
        type: touch.type,
        executedAt: new Date().toISOString(),
        result: touchResult,
      });

      // Check for conversions
      const conversions = await activities.checkConversions({
        leadIds: input.leadIds,
        touchpointId: touchResult.touchpointId,
      });

      if (conversions.convertedLeads.length > 0) {
        // Move converted leads to next stage
        await activities.moveLeadsToStage({
          leadIds: conversions.convertedLeads,
          stage: 'converted',
          reason: `Converted after touch ${i + 1}`,
        });
      }
    }

    // Step 4: Final scoring and recommendations
    state.status = 'finalizing';
    state.progress = 95;
    const finalAnalysis = await activities.generateNurturingReport({
      leadIds: input.leadIds,
      touchpoints: state.touchpoints,
      initialScores: state.leadScores,
    });

    state.status = 'completed';
    state.progress = 100;

    return {
      success: true,
      nurturingId: input.nurturingId,
      touchpoints: state.touchpoints,
      finalAnalysis: finalAnalysis,
      completedAt: new Date().toISOString(),
    };

  } catch (error) {
    state.status = 'failed';
    throw error;
  }
}

/**
 * Customer Journey Automation Workflow
 * Complex multi-channel customer journey orchestration
 */
async function customerJourneyWorkflow(input) {
  const state = {
    progress: 0,
    status: 'initializing',
    journeyStages: [],
    customerActions: {},
  };

  setHandler(getProgressQuery, () => state.progress);
  setHandler(getStatusQuery, () => state.status);

  try {
    // Initialize journey
    state.status = 'initializing_journey';
    const journey = await activities.initializeJourney({
      customerId: input.customerId,
      journeyType: input.journeyType,
      startingPoint: input.entryPoint,
    });

    // Execute journey stages
    for (const stage of journey.stages) {
      state.status = `executing_${stage.name}`;
      state.progress = (stage.order / journey.stages.length) * 100;

      // Check stage conditions
      const conditions = await activities.evaluateStageConditions({
        customerId: input.customerId,
        stage: stage,
        previousActions: state.customerActions,
      });

      if (conditions.shouldExecute) {
        // Execute stage actions
        const stageResult = await activities.executeJourneyStage({
          customerId: input.customerId,
          stage: stage,
          contextData: conditions.contextData,
        });

        state.journeyStages.push({
          stageName: stage.name,
          executedAt: new Date().toISOString(),
          result: stageResult,
        });

        // Wait for customer response or timeout
        const response = await Promise.race([
          activities.waitForCustomerAction({
            customerId: input.customerId,
            expectedActions: stage.expectedActions,
          }),
          sleep(stage.timeoutDuration || '7 days'),
        ]);

        if (response) {
          state.customerActions[stage.name] = response;
          
          // Adjust journey based on response
          if (response.requiresPathChange) {
            journey.stages = await activities.adjustJourneyPath({
              currentStage: stage,
              customerResponse: response,
              remainingStages: journey.stages.filter(s => s.order > stage.order),
            });
          }
        }
      }
    }

    // Complete journey
    state.status = 'completed';
    state.progress = 100;

    const summary = await activities.generateJourneySummary({
      customerId: input.customerId,
      journeyStages: state.journeyStages,
      customerActions: state.customerActions,
    });

    return {
      success: true,
      journeyId: journey.id,
      stages: state.journeyStages,
      customerActions: state.customerActions,
      summary: summary,
      completedAt: new Date().toISOString(),
    };

  } catch (error) {
    state.status = 'failed';
    throw error;
  }
}

/**
 * Data Sync Workflow
 * Synchronize data across multiple platforms
 */
async function dataSyncWorkflow(input) {
  const state = {
    progress: 0,
    status: 'initializing',
    syncedPlatforms: [],
    errors: [],
  };

  setHandler(getProgressQuery, () => state.progress);
  setHandler(getStatusQuery, () => state.status);

  try {
    // Get platforms to sync
    const platforms = input.platforms || ['mailchimp', 'hubspot', 'salesforce'];
    const totalPlatforms = platforms.length;

    for (let i = 0; i < platforms.length; i++) {
      const platform = platforms[i];
      state.status = `syncing_${platform}`;
      state.progress = ((i + 1) / totalPlatforms) * 100;

      try {
        // Sync data for platform
        const syncResult = await activities.syncPlatformData({
          platform: platform,
          userId: input.userId,
          syncType: input.syncType,
          lastSyncTime: input.lastSyncTime,
        });

        state.syncedPlatforms.push({
          platform: platform,
          status: 'success',
          recordsSynced: syncResult.recordCount,
          syncedAt: new Date().toISOString(),
        });

      } catch (error) {
        state.errors.push({
          platform: platform,
          error: error.message,
          timestamp: new Date().toISOString(),
        });

        // Continue with other platforms
        if (!input.stopOnError) {
          continue;
        } else {
          throw error;
        }
      }
    }

    // Generate sync report
    const report = await activities.generateSyncReport({
      syncedPlatforms: state.syncedPlatforms,
      errors: state.errors,
      totalRecords: state.syncedPlatforms.reduce((sum, p) => sum + p.recordsSynced, 0),
    });

    state.status = 'completed';
    state.progress = 100;

    return {
      success: true,
      syncId: input.syncId,
      platforms: state.syncedPlatforms,
      errors: state.errors,
      report: report,
      completedAt: new Date().toISOString(),
    };

  } catch (error) {
    state.status = 'failed';
    throw error;
  }
}

// Export workflows
module.exports = {
  emailCampaignWorkflow,
  leadNurturingWorkflow,
  customerJourneyWorkflow,
  dataSyncWorkflow,
};