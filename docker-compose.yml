version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: neurocolony-mongodb
    restart: unless-stopped
    ports:
      - "27019:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: neurocolony
      MONGO_INITDB_ROOT_PASSWORD: neurocolony123
      MONGO_INITDB_DATABASE: neurocolony
    volumes:
      - mongodb_data:/data/db
      - ./backend/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - neurocolony-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: neurocolony-redis
    restart: unless-stopped
    ports:
      - "6381:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - neurocolony-network

  # Backend API Development
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: neurocolony-backend-dev
    restart: unless-stopped
    ports:
      - "5003:5002"
    environment:
      - NODE_ENV=development
      - PORT=5002
      - MONGODB_URI=*******************************************************************************
      - REDIS_URL=redis://redis:6379
      - FRONTEND_URL=http://localhost:3003
      - JWT_SECRET=dev-jwt-secret-key-for-development-only
      - LOCAL_AI_ENABLED=true
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - neurocolony-network
    command: nodemon server.js

  # Frontend Development
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: neurocolony-frontend-dev
    restart: unless-stopped
    ports:
      - "3003:3002"
    environment:
      - VITE_API_URL=http://localhost:5003/api
      - VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - neurocolony-network
    command: npm run dev -- --host 0.0.0.0 --port 3002

  # Nginx Reverse Proxy (Production-like)
  nginx:
    image: nginx:alpine
    container_name: neurocolony-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - neurocolony-network

  # Local AI (Ollama) for development  
  ollama:
    image: ollama/ollama:latest
    container_name: neurocolony-ollama
    restart: unless-stopped
    ports:
      - "11436:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - neurocolony-network
    environment:
      - OLLAMA_HOST=0.0.0.0

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  ollama_data:
    driver: local

networks:
  neurocolony-network:
    driver: bridge