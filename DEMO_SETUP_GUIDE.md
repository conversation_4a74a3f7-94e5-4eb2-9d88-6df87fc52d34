# 🐜 NeuroColony AI Agent Platform - Demo Setup Guide

## 🚀 **Phase 2 Complete: Enhanced AI Agent Platform**

NeuroColony has been successfully transformed into a comprehensive AI agent platform with colony intelligence that surpasses n8n capabilities. This guide will help you set up and explore the enhanced features.

---

## 📋 **Prerequisites**

- **Node.js** (v18 or higher)
- **MongoDB** (v5.0 or higher)
- **Git**
- **Modern web browser** (Chrome, Firefox, Safari, Edge)

---

## 🛠️ **Installation & Setup**

### 1. **<PERSON>lone and Install Dependencies**

```bash
# Clone the repository
git clone https://github.com/dorianbladel/NeuroColony.git
cd NeuroColony

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 2. **Environment Configuration**

Create a `.env` file in the `backend` directory:

```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/neurocolony
DB_NAME=neurocolony

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# AI Service Configuration
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Integration Encryption
INTEGRATION_ENCRYPTION_KEY=your-integration-encryption-key-32-chars

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Server Configuration
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### 3. **Database Setup**

```bash
# Start MongoDB (if not running)
mongod

# The application will automatically create the database and collections
```

### 4. **Start the Development Servers**

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm start
```

---

## 🌐 **Access the Platform**

### **Primary URL:** `http://localhost:3000`

The NeuroColony platform will be available at the above URL with the following key pages:

---

## 🎯 **Key Features to Explore**

### 1. **🐜 Colony Dashboard** - `http://localhost:3000/colony-dashboard`

**What to Test:**
- **Colony Overview**: View Queen/Worker/Scout agent hierarchy
- **Performance Metrics**: Real-time colony intelligence statistics
- **Agent Status**: Monitor active agents and their success rates
- **Quick Actions**: Create new colonies and agents

**Demo Actions:**
1. Click "Create Marketing Colony" to set up a complete agent ecosystem
2. View the colony hierarchy visualization
3. Monitor real-time performance metrics
4. Explore agent type distribution

### 2. **🔗 Integration Hub** - `http://localhost:3000/integration-hub`

**What to Test:**
- **400+ Platform Connections**: Browse available marketing platforms
- **Multi-Channel Setup**: Connect Mailchimp, HubSpot, Salesforce, etc.
- **Health Monitoring**: Real-time integration status and performance
- **Unified Interface**: Marketing-first approach to platform management

**Demo Actions:**
1. Browse platform categories (Email Marketing, CRM, Analytics, Social Media)
2. Add a new integration (use demo API keys)
3. Test connection health
4. View integration statistics

### 3. **🤖 Agent Marketplace** - `http://localhost:3000/agent-marketplace`

**What to Test:**
- **Enhanced Agent Library**: 20+ specialized marketing agents
- **Colony Intelligence**: Queen/Worker/Scout agent types
- **One-Click Installation**: Install pre-built marketing automation agents
- **Agent Categories**: Email marketing, social media, CRM, analytics

**Demo Actions:**
1. Browse agent categories
2. Install "Email Sequence Generator Queen"
3. Install "Subject Line Optimizer Worker"
4. Install "Performance Analyzer Scout"
5. View agent capabilities and descriptions

### 4. **🎨 Visual Workflow Builder** - `http://localhost:3000/agent-builder`

**What to Test:**
- **N8N-Style Canvas**: Drag-and-drop workflow creation
- **Colony-Aware Nodes**: Queen/Worker/Scout agent nodes
- **Real-Time Execution**: Live workflow monitoring
- **Advanced Node Types**: Triggers, conditions, integrations, actions

**Demo Actions:**
1. Drag agents from the palette to the canvas
2. Connect agents with visual connections
3. Configure agent settings
4. Execute workflows and monitor real-time status
5. View execution logs and performance

### 5. **📊 Agent Dashboard** - `http://localhost:3000/agent-dashboard`

**What to Test:**
- **Agent Management**: View and manage all your agents
- **Execution History**: Track agent performance over time
- **Colony Coordination**: See how agents work together
- **Performance Analytics**: Success rates and optimization insights

---

## 🧪 **Demo Scenarios**

### **Scenario 1: Complete Marketing Colony Setup**

1. **Go to Colony Dashboard** (`/colony-dashboard`)
2. **Click "Create Marketing Colony"**
3. **Watch as the system creates:**
   - 1 Queen Agent (Email Orchestrator)
   - 3 Worker Agents (Email Generator, Subject Optimizer, Content Personalizer)
   - 2 Scout Agents (Performance Monitor, Lead Scout)
4. **View the colony hierarchy and relationships**

### **Scenario 2: Multi-Channel Integration**

1. **Go to Integration Hub** (`/integration-hub`)
2. **Browse available platforms by category**
3. **Add Mailchimp integration:**
   - Select Mailchimp from Email Marketing category
   - Enter demo API key: `demo-key-12345-us1`
   - Test connection
4. **Add HubSpot integration:**
   - Select HubSpot from CRM category
   - Enter demo token: `demo-hubspot-token-12345`
   - View integration health

### **Scenario 3: Visual Workflow Creation**

1. **Go to Agent Builder** (`/agent-builder`)
2. **Create an email marketing workflow:**
   - Drag "Schedule Trigger" to canvas
   - Add "Email Orchestrator Queen"
   - Add "Subject Optimizer Worker"
   - Add "Performance Monitor Scout"
   - Connect them with visual connections
3. **Configure each node**
4. **Execute the workflow and watch real-time status**

### **Scenario 4: Agent Marketplace Exploration**

1. **Go to Agent Marketplace** (`/agent-marketplace`)
2. **Browse by categories:**
   - Email Marketing: Email generators, subject optimizers
   - Social Media: Social schedulers, engagement monitors
   - Analytics: Performance analyzers, attribution trackers
   - CRM: Lead scorers, pipeline managers
3. **Install multiple agents and see them in your dashboard**

---

## 🔧 **Advanced Configuration**

### **AI Provider Setup**

To enable full AI capabilities, add your API keys to the `.env` file:

```env
# OpenAI (for GPT-4)
OPENAI_API_KEY=sk-your-openai-key-here

# Anthropic (for Claude 4)
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
```

### **Integration Platform Setup**

For real integrations, obtain API keys from:

- **Mailchimp**: https://mailchimp.com/developer/
- **HubSpot**: https://developers.hubspot.com/
- **Salesforce**: https://developer.salesforce.com/
- **Google Analytics**: https://developers.google.com/analytics

---

## 🎉 **What Makes This Special**

### **🆚 Advantages Over N8N:**

✅ **Marketing-First Design**: Purpose-built for marketing teams
✅ **Colony Intelligence**: Autonomous agent collaboration
✅ **Email Expertise**: Advanced email sequence capabilities
✅ **AI-Native**: Multi-AI provider support
✅ **User-Friendly**: Marketing team focused interface
✅ **Performance Focus**: Revenue and conversion optimization

### **🐜 Colony Intelligence Features:**

- **Queen Agents**: Orchestrate complex marketing campaigns
- **Worker Agents**: Execute specific marketing tasks
- **Scout Agents**: Gather intelligence and monitor performance
- **Autonomous Collaboration**: Agents work together intelligently
- **Real-Time Coordination**: Live workflow execution monitoring

---

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **MongoDB Connection Error**
   - Ensure MongoDB is running: `mongod`
   - Check connection string in `.env`

2. **Port Already in Use**
   - Backend: Change `PORT` in `.env`
   - Frontend: Use `PORT=3001 npm start`

3. **API Key Errors**
   - Add valid API keys to `.env`
   - Restart backend server after changes

4. **Build Errors**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Clear browser cache

---

## 📞 **Support**

For issues or questions:
- Check the console logs in browser developer tools
- Review backend logs in terminal
- Ensure all environment variables are set correctly

---

## 🎯 **Next Steps**

After exploring the demo:
1. **Customize agents** for your specific marketing needs
2. **Connect real integrations** with your marketing stack
3. **Build complex workflows** using the visual builder
4. **Scale your colony** with additional specialized agents

**The future of marketing automation is here with NeuroColony's colony intelligence! 🐜✨**
