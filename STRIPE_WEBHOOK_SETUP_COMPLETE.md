# 🎯 NeuroColony Stripe Webhook Setup - Complete Configuration

## 📋 **EXACT EVENTS NEEDED FOR NEUROCOLONY**

Based on the backend code analysis, NeuroColony requires these **5 specific webhook events**:

### **Required Events:**
1. `customer.subscription.created` - New subscription activation
2. `customer.subscription.updated` - Plan changes, status updates  
3. `customer.subscription.deleted` - Subscription cancellation
4. `invoice.payment_succeeded` - Successful payment processing
5. `invoice.payment_failed` - Payment failure handling

## 🚀 **STEP-BY-STEP SETUP PROCESS**

### **Step 1: Install Stripe CLI (if not already done)**
```bash
# Ubuntu/Debian
curl -s https://packages.stripe.com/api/security/keypairs/stripe-cli-gpg/public | gpg --dearmor | sudo tee /usr/share/keyrings/stripe.gpg
echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.com/stripe-cli-debian-local stable main" | sudo tee -a /etc/apt/sources.list.d/stripe.list
sudo apt update && sudo apt install stripe

# Or download directly
wget https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.4_linux_x86_64.tar.gz
tar -xzf stripe_1.19.4_linux_x86_64.tar.gz
sudo mv stripe /usr/local/bin/
```

### **Step 2: Authenticate with Stripe**
```bash
stripe login
```
This will open your browser to connect your Stripe account.

### **Step 3: Get Your API Keys**
1. Go to: https://dashboard.stripe.com/test/apikeys
2. Copy your **Secret key** (starts with `sk_test_`)
3. Copy your **Publishable key** (starts with `pk_test_`)

### **Step 4: Start Webhook Forwarding**
```bash
# This command forwards webhooks to your local NeuroColony backend
stripe listen --forward-to localhost:5000/api/webhooks/stripe --events customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed
```

**IMPORTANT**: This command will output your webhook signing secret:
```
> Ready! Your webhook signing secret is whsec_1234567890abcdef...
```
**Copy this secret immediately!**

### **Step 5: Update NeuroColony Environment Variables**
Edit `/home/<USER>/SequenceAI/backend/.env`:

```env
# Replace these placeholders with your actual keys:
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_SECRET_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_ACTUAL_WEBHOOK_SECRET_HERE
```

For the frontend, create `/home/<USER>/SequenceAI/frontend/.env.local`:
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_PUBLISHABLE_KEY_HERE
```

## 🧪 **TESTING THE SETUP**

### **Test 1: Webhook Endpoint Health**
```bash
curl -X POST http://localhost:5000/api/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"type": "test"}'
```

### **Test 2: Trigger Test Events**
```bash
# Test subscription creation
stripe trigger customer.subscription.created

# Test payment success
stripe trigger invoice.payment_succeeded

# Test payment failure  
stripe trigger invoice.payment_failed
```

### **Test 3: Start NeuroColony Backend**
```bash
cd /home/<USER>/SequenceAI/backend
npm run dev
```

You should see webhook events logged in the backend console.

## 🔧 **CREATING PRODUCTS IN STRIPE DASHBOARD**

### **Step 1: Create Products**
1. Go to: https://dashboard.stripe.com/test/products
2. Click "Add product"
3. Create these products:

**Starter Colony Plan:**
- Name: "Starter Colony"
- Description: "50 sequences/month, 5 agents, basic colony intelligence"
- Pricing: $29/month recurring
- Copy the Price ID (starts with `price_`)

**Professional Colony Plan:**
- Name: "Professional Colony"  
- Description: "200 sequences/month, 15 agents, advanced colony intelligence"
- Pricing: $99/month recurring
- Copy the Price ID

**Business Colony Plan:**
- Name: "Business Colony"
- Description: "Unlimited sequences, unlimited agents, enterprise features"  
- Pricing: $299/month recurring
- Copy the Price ID

### **Step 2: Update Environment with Price IDs**
Add to `/home/<USER>/SequenceAI/backend/.env`:
```env
STRIPE_STARTER_PRICE_ID=price_YOUR_STARTER_PRICE_ID
STRIPE_PROFESSIONAL_PRICE_ID=price_YOUR_PROFESSIONAL_PRICE_ID  
STRIPE_BUSINESS_PRICE_ID=price_YOUR_BUSINESS_PRICE_ID
```

## 📊 **WEBHOOK EVENT DETAILS**

### **Events NeuroColony Handles:**

1. **`customer.subscription.created`**
   - Activates new user subscription
   - Sets up usage tracking
   - Sends welcome email

2. **`customer.subscription.updated`**
   - Handles plan upgrades/downgrades
   - Updates usage limits
   - Processes status changes

3. **`customer.subscription.deleted`**
   - Deactivates subscription
   - Preserves user data
   - Sends cancellation confirmation

4. **`invoice.payment_succeeded`**
   - Resets monthly usage counters
   - Clears usage notifications
   - Processes overage charges

5. **`invoice.payment_failed`**
   - Sets subscription to past_due
   - Sends payment failure notification
   - Initiates retry logic

## ⚡ **QUICK START COMMANDS**

### **Terminal 1: Start Backend**
```bash
cd /home/<USER>/SequenceAI/backend
npm run dev
```

### **Terminal 2: Start Webhook Forwarding**
```bash
stripe listen --forward-to localhost:5000/api/webhooks/stripe --events customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed
```

### **Terminal 3: Start Frontend**
```bash
cd /home/<USER>/SequenceAI/frontend  
npm run dev
```

## 🎯 **VALIDATION CHECKLIST**

- [ ] Stripe CLI installed and authenticated
- [ ] Webhook forwarding active with correct events
- [ ] Backend .env updated with real Stripe keys  
- [ ] Frontend .env.local created with publishable key
- [ ] Products created in Stripe dashboard with price IDs
- [ ] Test webhooks triggering successfully
- [ ] Backend logging webhook events
- [ ] Full payment flow tested end-to-end

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

**Webhook Secret Wrong:**
```
Error: No signatures found matching the expected signature
```
**Fix**: Copy the exact webhook secret from `stripe listen` output

**Port Already in Use:**
```
Error: listen EADDRINUSE: address already in use :::5000
```
**Fix**: Kill existing processes: `pkill -f "node.*5000"`

**Invalid API Key:**
```
Error: Invalid API Key provided  
```
**Fix**: Ensure using test keys (not live) and no extra spaces

## 🎉 **SUCCESS INDICATORS**

When everything is working correctly, you'll see:

1. **Backend Console**: Webhook events being received and processed
2. **Stripe Dashboard**: Events showing as successfully delivered
3. **Database**: User subscriptions being created/updated
4. **Frontend**: Payment forms working without errors

---

**🔥 Pro Tip**: Keep the `stripe listen` command running in a dedicated terminal during development. This ensures real-time webhook delivery to your local NeuroColony instance.

**Next Step**: Once webhooks are working, test the full subscription flow: Register → Subscribe → Pay → Verify webhook → Use platform!