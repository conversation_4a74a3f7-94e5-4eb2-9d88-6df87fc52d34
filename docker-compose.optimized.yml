# NeuroColony Optimized Docker Configuration
# Enhanced with Claude 4 AI, Email Intelligence, and Production Features

services:
  # MongoDB Database (Optimized for AI workloads)
  mongodb:
    image: mongo:6.0
    container_name: neurocolony-mongodb-opt
    restart: unless-stopped
    ports:
      - "27021:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: neurocolony
      MONGO_INITDB_ROOT_PASSWORD: neurocolony123
      MONGO_INITDB_DATABASE: neurocolony_optimized
    volumes:
      - mongodb_opt_data:/data/db
      - ./backend/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - neurocolony-opt-network
    command: >
      mongod --bind_ip_all
      --wiredTigerCacheSizeGB 2
      --wiredTigerCollectionBlockCompressor zstd
      --wiredTigerIndexPrefixCompression true
    healthcheck:
      test: ["<PERSON><PERSON>", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache (Optimized for AI response caching)
  redis:
    image: redis:7-alpine
    container_name: neurocolony-redis-opt
    restart: unless-stopped
    ports:
      - "6383:6379"
    volumes:
      - redis_opt_data:/data
    networks:
      - neurocolony-opt-network
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
      --save 900 1
      --save 300 10
      --save 60 10000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Optimized with Claude 4 & Email Intelligence)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: neurocolony-backend-opt
    restart: unless-stopped
    ports:
      - "5004:5000"
    environment:
      # Core Configuration
      - NODE_ENV=production
      - PORT=5000
      - LOG_LEVEL=info
      
      # Database Configuration
      - MONGODB_URI=*****************************************************************************************
      - REDIS_URL=redis://redis:6379
      
      # AI Service Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-dummy-key}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-anthropic-dummy-key}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-google-dummy-key}
      - LOCAL_AI_ENABLED=true
      - OLLAMA_BASE_URL=http://ollama:11434
      
      # Security Configuration
      - JWT_SECRET=${JWT_SECRET:-production-jwt-secret-change-me}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY:-sk_test_dummy}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET:-whsec_dummy}
      
      # Email Configuration
      - SMTP_HOST=${SMTP_HOST:-smtp.gmail.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASS=${SMTP_PASS:-your-app-password}
      
      # Application Configuration
      - FRONTEND_URL=http://localhost:3011
      - DOMAIN=${DOMAIN:-localhost}
      - MAX_SEQUENCE_LENGTH=14
      - RATE_LIMIT_REQUESTS=1000
      - RATE_LIMIT_WINDOW=900000
      
      # Performance Configuration
      - NODE_OPTIONS=--max-old-space-size=2048
      - UV_THREADPOOL_SIZE=16
      
    volumes:
      - backend_opt_logs:/app/logs
      - ./backend/.env:/app/.env:ro
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - neurocolony-opt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Frontend (Optimized React build)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
      args:
        - VITE_API_URL=http://localhost:5004/api
        - VITE_APP_NAME=NeuroColony
        - VITE_APP_VERSION=2.1.0
        - VITE_STRIPE_PUBLIC_KEY=${VITE_STRIPE_PUBLIC_KEY:-pk_test_dummy}
        - VITE_ENABLE_ANALYTICS=true
        - VITE_CLAUDE_ARTIFACTS_ENABLED=true
    container_name: neurocolony-frontend-opt
    restart: unless-stopped
    ports:
      - "3011:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - neurocolony-opt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health.html || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Local AI Service (Ollama with GPU support)
  ollama:
    image: ollama/ollama:latest
    container_name: neurocolony-ollama-opt
    restart: unless-stopped
    ports:
      - "11437:11434"
    volumes:
      - ollama_opt_data:/root/.ollama
    networks:
      - neurocolony-opt-network
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
      - OLLAMA_KEEP_ALIVE=24h
      - OLLAMA_MAX_LOADED_MODELS=3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    # Uncomment for GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Nginx Reverse Proxy (Production-grade)
  nginx:
    image: nginx:alpine
    container_name: neurocolony-nginx-opt
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.optimized.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_opt_cache:/var/cache/nginx
      - nginx_opt_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - neurocolony-opt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'

  # Monitoring & Analytics (Optional)
  # Uncomment for advanced monitoring
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: neurocolony-prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  #     - prometheus_data:/prometheus
  #   networks:
  #     - neurocolony-opt-network
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'

  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: neurocolony-grafana
  #   ports:
  #     - "3001:3000"
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
  #     - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin123
  #   networks:
  #     - neurocolony-opt-network

volumes:
  mongodb_opt_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/mongodb
  
  redis_opt_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis
  
  ollama_opt_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/ollama
  
  backend_opt_logs:
    driver: local
  
  nginx_opt_cache:
    driver: local
  
  nginx_opt_logs:
    driver: local
  
  # prometheus_data:
  #   driver: local
  
  # grafana_data:
  #   driver: local

networks:
  neurocolony-opt-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: neurocolony-opt
      com.docker.network.driver.mtu: 1500