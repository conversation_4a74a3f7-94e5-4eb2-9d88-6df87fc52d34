# Contributing to NeuroColony

We love your input! We want to make contributing to NeuroColony as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use GitHub to host code, to track issues and feature requests, as well as accept pull requests.

## Pull Requests

Pull requests are the best way to propose changes to the codebase. We actively welcome your pull requests:

1. Fork the repo and create your branch from `main`.
2. If you've added code that should be tested, add tests.
3. If you've changed APIs, update the documentation.
4. Ensure the test suite passes.
5. Make sure your code lints.
6. Issue that pull request!

## Development Setup

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Git

### Local Setup

```bash
# Fork and clone the repository
git clone https://github.com/yourusername/neurocolony.git
cd neurocolony

# Install dependencies
npm install
cd backend && npm install
cd ../frontend && npm install
cd ..

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Start development environment
npm run dev
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e

# Run tests with coverage
npm run test:coverage
```

## Code Style

We use ESLint and Prettier for code formatting:

```bash
# Check code style
npm run lint

# Auto-fix formatting issues
npm run lint:fix

# Format code
npm run format
```

## Coding Standards

### JavaScript/React
- Use modern ES6+ syntax
- Follow React hooks patterns
- Write descriptive variable and function names
- Add JSDoc comments for complex functions
- Use TypeScript when possible

### Backend
- Follow RESTful API conventions
- Implement proper error handling
- Use async/await for promises
- Validate all inputs
- Follow the established architecture patterns

### Testing
- Write unit tests for business logic
- Add integration tests for API endpoints
- Include E2E tests for critical user journeys
- Aim for >90% code coverage

## Commit Messages

Use conventional commits format:

```
feat: add new email template system
fix: resolve authentication bug
docs: update API documentation
test: add unit tests for sequence generation
chore: update dependencies
```

## Issue Reporting

We use GitHub issues to track public bugs. Report a bug by [opening a new issue](https://github.com/username/neurocolony/issues).

**Great Bug Reports** tend to have:

- A quick summary and/or background
- Steps to reproduce
  - Be specific!
  - Give sample code if you can
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening, or stuff you tried that didn't work)

## Feature Requests

We welcome feature requests! Please provide:

- **Clear description** of the feature
- **Use case** - why would this be useful?
- **Implementation ideas** - if you have any thoughts on how it could be implemented
- **Examples** - mockups, screenshots, or similar features in other tools

## Security Issues

If you discover security vulnerabilities, please send an <NAME_EMAIL> instead of using the issue tracker.

## Documentation

Help us improve our documentation:

- Fix typos and grammatical errors
- Add examples and use cases
- Improve API documentation
- Create tutorials and guides

## Community

- Join our [Discord server](https://discord.gg/neurocolony) for discussions
- Follow us on [Twitter](https://twitter.com/neurocolony) for updates
- Read our [blog](https://blog.neurocolony.app) for insights

## Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Social media shoutouts
- Special contributor badges

## License

By contributing, you agree that your contributions will be licensed under the MIT License.

## Questions?

Don't hesitate to reach out:
- Create an [issue](https://github.com/username/neurocolony/issues)
- Join our [Discord](https://discord.gg/neurocolony)
- Email <NAME_EMAIL>

Thank you for contributing to NeuroColony! 🧠✨
