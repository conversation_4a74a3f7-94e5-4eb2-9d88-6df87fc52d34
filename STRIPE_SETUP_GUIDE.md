# 🔧 Stripe Webhook Setup Guide for NeuroColony

## 📋 Prerequisites
- Stripe account (free to create at stripe.com)
- Stripe CLI installed
- NeuroColony backend running on localhost:5000

## 🚀 Step-by-Step Setup

### 1. Install Stripe CLI
```bash
# Download and install Stripe CLI
curl -s https://packages.stripe.com/api/security/keypairs/stripe-cli-gpg/public | gpg --dearmor | sudo tee /usr/share/keyrings/stripe.gpg
echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.com/stripe-cli-debian-local stable main" | sudo tee -a /etc/apt/sources.list.d/stripe.list
sudo apt update && sudo apt install stripe
```

### 2. Login to Stripe
```bash
stripe login
```

### 3. Configure Webhook Events

#### Required Events for NeuroColony:
- `customer.subscription.created`
- `customer.subscription.updated` 
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `checkout.session.completed`
- `customer.created`
- `customer.updated`

### 4. Start Webhook Forwarding
```bash
# Forward webhooks to localhost
stripe listen --forward-to localhost:5000/api/webhooks/stripe
```

This command will output your webhook signing secret like:
```
whsec_1234567890abcdef...
```

### 5. Update Environment Variables

Copy this webhook secret to your `.env` file:
```bash
# In /home/<USER>/SequenceAI/backend/.env
STRIPE_WEBHOOK_SECRET=whsec_YOUR_ACTUAL_WEBHOOK_SECRET_HERE
```

### 6. Test Webhook Integration
```bash
# Trigger a test webhook
stripe trigger checkout.session.completed
```

## 🔑 Get Your API Keys

### Stripe Dashboard Setup:
1. Go to https://dashboard.stripe.com/test/apikeys
2. Copy your **Publishable key** (starts with `pk_test_`)
3. Copy your **Secret key** (starts with `sk_test_`)

### Update .env with Real Keys:
```bash
# Replace in /home/<USER>/SequenceAI/backend/.env
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_SECRET_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_PUBLISHABLE_KEY_HERE
```

## 🧪 Testing Payment Flow

### 1. Test Credit Cards (Stripe provides these):
- **Success**: `****************`
- **Declined**: `****************`
- **Requires 3D Secure**: `****************`

### 2. Test Subscription Flow:
1. Start NeuroColony frontend: `npm run dev`
2. Navigate to pricing page
3. Click "Subscribe to Pro"
4. Use test card: `****************`
5. Check backend logs for webhook events

## 🛠️ Backend Webhook Handler

The webhook handler is already implemented in:
`/home/<USER>/SequenceAI/backend/routes/payments-simple.js`

It handles:
- ✅ Subscription creation/updates
- ✅ Payment success/failure  
- ✅ Usage-based billing
- ✅ Customer management

## 🔍 Troubleshooting

### Common Issues:

1. **Webhook Secret Wrong**:
   ```
   Error: No signatures found matching the expected signature
   ```
   **Fix**: Copy the exact webhook secret from `stripe listen` output

2. **Port Conflicts**:
   ```
   Error: Port 5000 already in use
   ```
   **Fix**: Update backend port in .env and restart both backend and webhook forwarding

3. **API Key Invalid**:
   ```
   Error: Invalid API Key provided
   ```
   **Fix**: Ensure you're using test keys (not live keys) during development

## 📱 Frontend Configuration

Update frontend environment variables:
```bash
# In /home/<USER>/SequenceAI/frontend/.env.local
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_PUBLISHABLE_KEY_HERE
```

## ✅ Verification Checklist

- [ ] Stripe CLI installed and logged in
- [ ] Webhook forwarding active (`stripe listen --forward-to localhost:5000/api/webhooks/stripe`)
- [ ] Backend .env updated with real Stripe keys
- [ ] Frontend .env.local updated with publishable key
- [ ] Test payment flow works end-to-end
- [ ] Webhook events logging in backend console

## 🎯 Next Steps

Once webhooks are working:
1. Test subscription upgrades/downgrades
2. Test usage-based overage billing
3. Verify email notifications
4. Test payment failures and retries

---

**Important**: Keep `stripe listen` running in a separate terminal while developing to receive webhook events in real-time.