# NeuroColony Production Environment Variables
# Copy this to .env.production and fill in your values

# Domain Configuration
DOMAIN_NAME=neurocolony.dev
FRONTEND_URL=https://neurocolony.dev
BACKEND_URL=https://neurocolony.dev/api

# Database Configuration
MONGO_ROOT_USER=admin
MONGO_ROOT_PASSWORD=q+0rTFEKCPZPc1L9D2rar7aryseMqaJE

# Security
JWT_SECRET=ftlhL+jY1mkOb8FlMXSeqNYXMj6MUyBfjyOeJjKh/hI=
INTEGRATION_ENCRYPTION_KEY=your-32-character-encryption-key

# Stripe Configuration (LIVE KEYS - NOT TEST!)
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_YOUR_PRODUCTION_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_live_51RBkqMGIKRQgxzOUfzdA8ZbO0qntFqTLMcwiCEaQpGGlDWrAWEpl0bkP0WpVgwqDTp5sPLp6wnYzATgdQN4eiFsf00hZpx0oNb

# Stripe Product Price IDs
STRIPE_STARTER_PRICE_ID=price_YOUR_STARTER_PRICE_ID
STRIPE_PROFESSIONAL_PRICE_ID=price_YOUR_PROFESSIONAL_PRICE_ID
STRIPE_BUSINESS_PRICE_ID=price_YOUR_BUSINESS_PRICE_ID

# OpenAI API
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Email Configuration (SendGrid recommended)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Production Settings
NODE_ENV=production
DEMO_MODE=false
PORT=5000

# Optional: Analytics & Monitoring
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
SENTRY_DSN=your-sentry-dsn-for-error-tracking
