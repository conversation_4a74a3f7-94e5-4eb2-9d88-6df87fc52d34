#!/bin/bash

# NeuroColony Debug & Start Script
# Handles port conflicts and ensures clean startup

echo "🧠 NeuroColony Debug & Startup Script"
echo "======================================"

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        echo "⚠️  Port $port is in use by PID $pid"
        echo "🔄 Killing process..."
        kill -9 $pid 2>/dev/null
        sleep 1
        echo "✅ Port $port cleared"
    else
        echo "✅ Port $port is available"
    fi
}

# Function to check if a service is running
check_service() {
    local name=$1
    local port=$2
    if lsof -ti:$port > /dev/null; then
        echo "✅ $name is running on port $port"
        return 0
    else
        echo "❌ $name is not running on port $port"
        return 1
    fi
}

echo ""
echo "1️⃣  Checking port availability..."
echo "---------------------------------"

# Check and clear ports if needed
kill_port 3004  # Frontend
kill_port 5002  # Backend

echo ""
echo "2️⃣  Starting Backend Services..."
echo "---------------------------------"

# Start backend
cd backend
if ! check_service "Backend" 5002; then
    echo "🚀 Starting backend on port 5002..."
    npm start &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to initialize..."
    sleep 5
    
    if check_service "Backend" 5002; then
        echo "✅ Backend started successfully (PID: $BACKEND_PID)"
    else
        echo "❌ Backend failed to start"
        exit 1
    fi
fi

echo ""
echo "3️⃣  Starting Frontend..."
echo "---------------------------------"

# Start frontend
cd ../frontend
echo "🚀 Starting frontend on port 3004..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to initialize..."
sleep 5

if check_service "Frontend" 3004; then
    echo "✅ Frontend started successfully (PID: $FRONTEND_PID)"
else
    echo "❌ Frontend failed to start"
    # Kill backend if frontend fails
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "4️⃣  System Status"
echo "---------------------------------"
echo "✅ Backend:  http://localhost:5002"
echo "✅ Frontend: http://localhost:3004"
echo "✅ API Proxy: http://localhost:3004/api → http://localhost:5002/api"
echo ""
echo "🎉 NeuroColony is running successfully!"
echo ""
echo "Press Ctrl+C to stop all services"

# Keep script running and handle cleanup
trap "echo '🛑 Stopping services...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0" INT TERM

# Wait for processes
wait