# Ghostty Themes Quick Reference Card

## Theme Quick Switch Commands

```bash
# Switch themes by editing config
sed -i 's/^theme = .*/theme = THEME_NAME/' ~/.config/ghostty/config

# Available themes:
# cyberpunk-matrix    - Green-on-black hacker aesthetic
# military-tactical   - Amber command center style
# synthwave-neon     - Pink/cyan 80s aesthetic
# cyberpunk-anime    - Blue/pink futuristic style
# minimal-dev        - Clean professional theme
# retro-amber        - Vintage CRT amber glow
# retro-ibm          - Classic IBM terminal
# high-contrast      - Maximum accessibility
# custom-brand       - Template for branding
# tokyo-night        - Popular developer theme
```

## Essential Configuration Options

```conf
# Core Settings
theme = THEME_NAME
font-family = "JetBrains Mono"
font-size = 14
background-opacity = 0.95

# Performance
scrollback-limit = 100000
cursor-style-blink = false

# Accessibility
minimum-contrast = 3.0
font-thicken = true
```

## Quick Installation

```bash
# 1. Create directories
mkdir -p ~/.config/ghostty/{themes,backups}

# 2. Backup existing config
cp ~/.config/ghostty/config ~/.config/ghostty/backups/config.backup.$(date +%Y%m%d)

# 3. Install fonts (Ubuntu/Debian)
sudo apt install fonts-jetbrains-mono fonts-firacode

# 4. Create theme files (copy from main guide)
# 5. Update main config with desired theme
# 6. Reload: Ctrl+Shift+R
```

## Color Palette Template

```conf
# Standard 16-color palette template
palette = 0=#000000   # Black
palette = 1=#FF0000   # Red
palette = 2=#00FF00   # Green
palette = 3=#FFFF00   # Yellow
palette = 4=#0000FF   # Blue
palette = 5=#FF00FF   # Magenta
palette = 6=#00FFFF   # Cyan
palette = 7=#FFFFFF   # White
palette = 8=#808080   # Bright Black
palette = 9=#FF8080   # Bright Red
palette = 10=#80FF80  # Bright Green
palette = 11=#FFFF80  # Bright Yellow
palette = 12=#8080FF  # Bright Blue
palette = 13=#FF80FF  # Bright Magenta
palette = 14=#80FFFF  # Bright Cyan
palette = 15=#FFFFFF  # Bright White
```

## Troubleshooting Checklist

- [ ] Theme file exists in `~/.config/ghostty/themes/`
- [ ] Theme name matches in main config
- [ ] Configuration syntax is valid (`ghostty --validate`)
- [ ] Fonts are installed and available
- [ ] Configuration reloaded (Ctrl+Shift+R)
- [ ] No conflicting color settings in main config

## Font Recommendations by Theme

| Theme | Primary Font | Alternative |
|-------|-------------|-------------|
| Cyberpunk Matrix | JetBrains Mono | Fira Code |
| Military Tactical | Cascadia Code | Consolas |
| Synthwave Neon | Iosevka | Victor Mono |
| Cyberpunk Anime | Victor Mono | JetBrains Mono |
| Minimal Dev | SF Mono | Source Code Pro |
| Retro Amber | IBM Plex Mono | Courier Prime |
| Retro IBM | Courier Prime | IBM Plex Mono |
| High Contrast | Atkinson Hyperlegible | JetBrains Mono |
| Tokyo Night | JetBrains Mono | Fira Code |

## Common Keybindings

```conf
# Essential keybindings for theme management
keybind = ctrl+shift+r=reload_config
keybind = ctrl+shift+plus=increase_font_size:1
keybind = ctrl+shift+minus=decrease_font_size:1
keybind = ctrl+shift+zero=reset_font_size
keybind = ctrl+shift+c=copy_to_clipboard
keybind = ctrl+shift+v=paste_from_clipboard
```

## Performance Optimization

```conf
# For better performance
background-opacity = 1.0
background-blur = false
cursor-style-blink = false
font-feature = -liga
scrollback-limit = 50000
```

## Accessibility Settings

```conf
# For better accessibility
minimum-contrast = 7.0
font-thicken = true
font-size = 16
cursor-style = block
cursor-style-blink = true
adjust-cursor-height = 2
```
