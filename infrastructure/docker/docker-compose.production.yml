version: '3.8'

services:
  # Load Balancer / Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: neurocolony-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
      - frontend
    networks:
      - neurocolony-network
    restart: unless-stopped

  # API Gateway
  api-gateway:
    image: kong:3.4-alpine
    container_name: neurocolony-api-gateway
    environment:
      KONG_DATABASE: "postgres"
      KONG_PG_HOST: postgres
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: ${KONG_DB_PASSWORD}
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: "0.0.0.0:8001"
    ports:
      - "8000:8000"
      - "8001:8001"
    depends_on:
      - postgres
      - kong-migration
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Kong Database Migration
  kong-migration:
    image: kong:3.4-alpine
    container_name: neurocolony-kong-migration
    environment:
      KONG_DATABASE: "postgres"
      KONG_PG_HOST: postgres
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: ${KONG_DB_PASSWORD}
    command: kong migrations bootstrap
    depends_on:
      - postgres
    networks:
      - neurocolony-network
    restart: on-failure

  # Frontend Application
  frontend:
    build:
      context: ../../frontend
      dockerfile: Dockerfile.production
    container_name: neurocolony-frontend
    volumes:
      - ../../frontend/dist:/usr/share/nginx/html
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Core Backend Service
  backend:
    build:
      context: ../../backend
      dockerfile: Dockerfile.production
    container_name: neurocolony-backend
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://neurocolony:${MONGO_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      JWT_SECRET: ${JWT_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
      - redis
      - rabbitmq
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Agent Executor Service
  agent-executor:
    build:
      context: ../../services/agent-executor
      dockerfile: Dockerfile
    container_name: neurocolony-agent-executor
    environment:
      PYTHON_ENV: production
      MONGODB_URI: mongodb://neurocolony:${MONGO_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      CLAUDE_API_KEY: ${ANTHROPIC_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - mongodb
      - redis
      - rabbitmq
    networks:
      - neurocolony-network
    restart: unless-stopped
    deploy:
      replicas: 3

  # Workflow Engine Service (Temporal)
  temporal:
    image: temporalio/auto-setup:1.22.3
    container_name: neurocolony-temporal
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=${TEMPORAL_DB_PASSWORD}
      - POSTGRES_SEEDS=postgres
    ports:
      - "7233:7233"
    depends_on:
      - postgres
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Workflow Engine Worker
  workflow-worker:
    build:
      context: ../../services/workflow-engine
      dockerfile: Dockerfile
    container_name: neurocolony-workflow-worker
    environment:
      TEMPORAL_ADDRESS: temporal:7233
      MONGODB_URI: mongodb://neurocolony:${MONGO_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
    depends_on:
      - temporal
      - mongodb
      - redis
    networks:
      - neurocolony-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # Analytics Engine Service
  analytics-engine:
    build:
      context: ../../services/analytics-engine
      dockerfile: Dockerfile
    container_name: neurocolony-analytics
    environment:
      PYTHON_ENV: production
      CLICKHOUSE_URL: clickhouse://analytics:${CLICKHOUSE_PASSWORD}@clickhouse:8123/analytics
      POSTGRES_URL: postgresql://analytics:${POSTGRES_PASSWORD}@postgres:5432/analytics
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
    depends_on:
      - clickhouse
      - postgres
      - redis
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Integration Gateway Service
  integration-gateway:
    build:
      context: ../../services/integration-gateway
      dockerfile: Dockerfile
    container_name: neurocolony-integrations
    environment:
      NODE_ENV: production
      MONGODB_URI: mongodb://neurocolony:${MONGO_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      ENCRYPTION_KEY: ${INTEGRATION_ENCRYPTION_KEY}
    ports:
      - "5001:5001"
    depends_on:
      - mongodb
      - redis
    networks:
      - neurocolony-network
    restart: unless-stopped

  # MongoDB Primary
  mongodb:
    image: mongo:7.0
    container_name: neurocolony-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: neurocolony
    volumes:
      - mongodb-data:/data/db
      - ./mongodb/init.js:/docker-entrypoint-initdb.d/init.js
    ports:
      - "27017:27017"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # PostgreSQL (Analytics & Kong)
  postgres:
    image: postgres:16-alpine
    container_name: neurocolony-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_MULTIPLE_DATABASES: analytics,kong,temporal
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/init-multi-db.sh:/docker-entrypoint-initdb.d/init-multi-db.sh
    ports:
      - "5432:5432"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Redis Cluster
  redis:
    image: redis:7-alpine
    container_name: neurocolony-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: neurocolony-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # ClickHouse (Analytics)
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: neurocolony-clickhouse
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: ${CLICKHOUSE_USER}
      CLICKHOUSE_PASSWORD: ${CLICKHOUSE_PASSWORD}
    volumes:
      - clickhouse-data:/var/lib/clickhouse
      - ./clickhouse/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "8123:8123"
      - "9000:9000"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.3
    container_name: neurocolony-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: neurocolony-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - minio-data:/data
    ports:
      - "9001:9001"
      - "9002:9002"
    command: server /data --console-address ":9002"
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: neurocolony-prometheus
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: neurocolony-grafana
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: neurocolony-jaeger
    environment:
      COLLECTOR_ZIPKIN_HOST_PORT: 9411
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - neurocolony-network
    restart: unless-stopped

networks:
  neurocolony-network:
    driver: bridge

volumes:
  mongodb-data:
  postgres-data:
  redis-data:
  rabbitmq-data:
  clickhouse-data:
  elasticsearch-data:
  minio-data:
  prometheus-data:
  grafana-data: