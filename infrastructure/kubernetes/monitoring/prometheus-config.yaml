apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-server-conf
  namespace: monitoring
  labels:
    name: prometheus-server-conf
data:
  prometheus.yml: |-
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'neurocolony-production'
        region: 'us-east-1'
    
    # Alertmanager configuration
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - 'alertmanager:9093'
    
    # Load rules once and periodically evaluate them
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    # Scrape configurations
    scrape_configs:
      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https
      
      # Kubernetes nodes
      - job_name: 'kubernetes-nodes'
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        kubernetes_sd_configs:
        - role: node
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # Kubernetes pods
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name
      
      # NeuroColony services
      - job_name: 'neurocolony-services'
        kubernetes_sd_configs:
        - role: service
          namespaces:
            names:
            - neurocolony
            - neurocolony-staging
            - neurocolony-production
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
          action: replace
          target_label: __scheme__
          regex: (https?)
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
          action: replace
          target_label: __address__
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
        - action: labelmap
          regex: __meta_kubernetes_service_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          action: replace
          target_label: kubernetes_name
      
      # Istio mesh metrics
      - job_name: 'istio-mesh'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - istio-system
            - neurocolony
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: istio-telemetry;prometheus
      
      # Kafka metrics
      - job_name: 'kafka'
        static_configs:
        - targets:
          - 'kafka-0.kafka-headless:9308'
          - 'kafka-1.kafka-headless:9308'
          - 'kafka-2.kafka-headless:9308'
        metrics_path: '/metrics'
      
      # Redis metrics
      - job_name: 'redis'
        static_configs:
        - targets:
          - 'redis-master:9121'
          - 'redis-slave:9121'
      
      # MongoDB metrics
      - job_name: 'mongodb'
        static_configs:
        - targets:
          - 'mongodb-metrics:9216'
      
      # PostgreSQL metrics
      - job_name: 'postgresql'
        static_configs:
        - targets:
          - 'postgresql-metrics:9187'
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
  labels:
    name: prometheus-rules
data:
  neurocolony-alerts.yml: |-
    groups:
    - name: neurocolony.rules
      interval: 30s
      rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up{job=~"neurocolony-services"} == 0
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Service {{ $labels.kubernetes_name }} is down"
          description: "{{ $labels.kubernetes_name }} in namespace {{ $labels.kubernetes_namespace }} has been down for more than 2 minutes."
      
      # High error rate
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (kubernetes_name)
            /
            sum(rate(http_requests_total[5m])) by (kubernetes_name)
          ) > 0.05
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High error rate on {{ $labels.kubernetes_name }}"
          description: "{{ $labels.kubernetes_name }} has error rate above 5% (current: {{ $value | humanizePercentage }})"
      
      # High latency
      - alert: HighLatency
        expr: |
          histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[5m])) by (kubernetes_name, le)) > 1
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High latency on {{ $labels.kubernetes_name }}"
          description: "99th percentile latency for {{ $labels.kubernetes_name }} is above 1s (current: {{ $value }}s)"
      
      # Pod resource alerts
      - alert: PodMemoryUsage
        expr: |
          (
            container_memory_working_set_bytes{namespace="neurocolony", container!=""}
            / 
            container_spec_memory_limit_bytes{namespace="neurocolony", container!=""} 
          ) > 0.9
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Pod {{ $labels.pod }} high memory usage"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is using more than 90% of its memory limit"
      
      - alert: PodCPUUsage
        expr: |
          (
            rate(container_cpu_usage_seconds_total{namespace="neurocolony", container!=""}[5m])
            /
            container_spec_cpu_quota{namespace="neurocolony", container!=""} 
          ) > 0.9
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Pod {{ $labels.pod }} high CPU usage"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is using more than 90% of its CPU limit"
      
      # Agent execution alerts
      - alert: AgentExecutionBacklog
        expr: agent_queue_depth > 100
        for: 5m
        labels:
          severity: warning
          team: agents
        annotations:
          summary: "High agent execution backlog"
          description: "Agent execution queue has more than 100 pending jobs (current: {{ $value }})"
      
      - alert: AgentExecutionFailureRate
        expr: |
          (
            sum(rate(agent_execution_failures_total[5m]))
            /
            sum(rate(agent_execution_total[5m]))
          ) > 0.1
        for: 5m
        labels:
          severity: critical
          team: agents
        annotations:
          summary: "High agent execution failure rate"
          description: "More than 10% of agent executions are failing (current: {{ $value | humanizePercentage }})"
      
      # Database alerts
      - alert: MongoDBConnectionPoolExhausted
        expr: mongodb_connections_current / mongodb_connections_available > 0.8
        for: 5m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "MongoDB connection pool near exhaustion"
          description: "MongoDB is using more than 80% of available connections"
      
      - alert: RedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: critical
          team: database
        annotations:
          summary: "Redis memory usage critical"
          description: "Redis is using more than 90% of max memory"
      
      # Kafka alerts
      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag > 1000
        for: 5m
        labels:
          severity: warning
          team: streaming
        annotations:
          summary: "Kafka consumer lag high"
          description: "Consumer {{ $labels.consumer_group }} has lag > 1000 messages (current: {{ $value }})"