apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: neurocolony-gateway
  namespace: neurocolony
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "neurocolony.ai"
    - "*.neurocolony.ai"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "neurocolony.ai"
    - "*.neurocolony.ai"
    tls:
      mode: SIMPLE
      credentialName: neurocolony-tls
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: neurocolony-routes
  namespace: neurocolony
spec:
  hosts:
  - "neurocolony.ai"
  - "api.neurocolony.ai"
  gateways:
  - neurocolony-gateway
  http:
  # Auth Service Routes
  - match:
    - uri:
        prefix: "/api/auth"
    route:
    - destination:
        host: auth-service
        port:
          number: 80
      weight: 100
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: gateway-error,connect-failure,refused-stream
  
  # Agent Service Routes
  - match:
    - uri:
        prefix: "/api/agents"
    route:
    - destination:
        host: agent-service
        port:
          number: 80
      weight: 100
    timeout: 60s
    retries:
      attempts: 3
      perTryTimeout: 20s
      retryOn: gateway-error,connect-failure,refused-stream
  
  # Workflow Service Routes
  - match:
    - uri:
        prefix: "/api/workflows"
    route:
    - destination:
        host: workflow-service
        port:
          number: 80
      weight: 100
    timeout: 45s
    retries:
      attempts: 3
      perTryTimeout: 15s
      retryOn: gateway-error,connect-failure,refused-stream
  
  # Analytics Service Routes
  - match:
    - uri:
        prefix: "/api/analytics"
    route:
    - destination:
        host: analytics-service
        port:
          number: 80
      weight: 100
    timeout: 30s
    retries:
      attempts: 2
      perTryTimeout: 15s
      retryOn: gateway-error,connect-failure
  
  # Frontend Routes (default)
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: frontend-service
        port:
          number: 80
      weight: 100
    timeout: 10s
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: circuit-breaker-auth
  namespace: neurocolony
spec:
  host: auth-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 2
    outlierDetection:
      consecutiveErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 50
      splitExternalLocalOriginErrors: true
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: circuit-breaker-agent
  namespace: neurocolony
spec:
  host: agent-service
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 200
      http:
        http1MaxPendingRequests: 100
        http2MaxRequests: 200
        maxRequestsPerConnection: 2
    outlierDetection:
      consecutiveErrors: 10
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 30
      splitExternalLocalOriginErrors: true
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: require-jwt
  namespace: neurocolony
spec:
  selector:
    matchLabels:
      app: agent-service
  action: ALLOW
  rules:
  - from:
    - source:
        requestPrincipals: ["*"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
    when:
    - key: request.auth.claims[iss]
      values: ["https://neurocolony.ai"]
---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: jwt-auth
  namespace: neurocolony
spec:
  selector:
    matchLabels:
      app: agent-service
  jwtRules:
  - issuer: "https://neurocolony.ai"
    jwksUri: "https://neurocolony.ai/.well-known/jwks.json"
    audiences:
    - "neurocolony-api"
    forwardOriginalToken: true