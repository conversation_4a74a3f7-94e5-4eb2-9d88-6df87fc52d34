apiVersion: apps/v1
kind: Deployment
metadata:
  name: neurocolony-agent-executor
  namespace: neurocolony-prod
  labels:
    app: neurocolony
    component: agent-executor
    tier: worker
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: neurocolony
      component: agent-executor
  template:
    metadata:
      labels:
        app: neurocolony
        component: agent-executor
        tier: worker
    spec:
      serviceAccountName: neurocolony-agent-executor
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: agent-executor
        image: neurocolony/agent-executor:latest
        imagePullPolicy: Always
        env:
        - name: PYTHON_ENV
          value: "production"
        - name: CELERY_WORKER_CONCURRENCY
          value: "4"
        - name: CELERY_WORKER_PREFETCH_MULTIPLIER
          value: "2"
        envFrom:
        - secretRef:
            name: neurocolony-ai-secrets
        - secretRef:
            name: neurocolony-db-secrets
        - configMapRef:
            name: neurocolony-agent-config
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import celery; print('healthy')"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
        volumeMounts:
        - name: agent-cache
          mountPath: /app/cache
        - name: model-cache
          mountPath: /app/models
      volumes:
      - name: agent-cache
        emptyDir:
          sizeLimit: 10Gi
      - name: model-cache
        persistentVolumeClaim:
          claimName: agent-model-cache-pvc
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: neurocolony-agent-executor
  namespace: neurocolony-prod
  labels:
    app: neurocolony
    component: agent-executor
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: neurocolony-agent-executor-hpa
  namespace: neurocolony-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: neurocolony-agent-executor
  minReplicas: 5
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  - type: External
    external:
      metric:
        name: celery_queue_length
        selector:
          matchLabels:
            queue: agent-executor
      target:
        type: Value
        value: "10"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: agent-model-cache-pvc
  namespace: neurocolony-prod
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi