#!/bin/bash

# SequenceA<PERSON> Optimized Deployment Script
# Enhanced with Claude 4 AI, Email Intelligence, and Production Features

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.optimized.yml"
PROJECT_NAME="sequenceai-opt"
DATA_DIR="./data"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check requirements
check_requirements() {
    log_info "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if ports are available
    local ports=(5004 3011 27021 6383 11437 8080 8443)
    for port in "${ports[@]}"; do
        if lsof -i:$port &> /dev/null; then
            log_warning "Port $port is already in use. This might cause conflicts."
        fi
    done
    
    log_success "Requirements check completed"
}

# Setup data directories
setup_directories() {
    log_info "Setting up data directories..."
    
    mkdir -p "${DATA_DIR}"/{mongodb,redis,ollama}
    mkdir -p ./nginx/ssl
    mkdir -p ./logs
    
    # Set proper permissions
    chmod -R 755 "${DATA_DIR}"
    
    log_success "Data directories created"
}

# Create environment file
create_env_file() {
    log_info "Creating environment file..."
    
    if [[ ! -f .env.optimized ]]; then
        cat > .env.optimized << EOF
# SequenceAI Optimized Environment Configuration
# Generated on $(date)

# Core Configuration
NODE_ENV=production
LOG_LEVEL=info
DOMAIN=localhost

# Security (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=optimized-jwt-secret-$(openssl rand -hex 16)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here

# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Database Configuration
MONGODB_URI=**************************************************************************************
REDIS_URL=redis://redis:6379

# Application URLs
FRONTEND_URL=http://localhost:3011
BACKEND_URL=http://localhost:5004

# Performance Configuration
MAX_SEQUENCE_LENGTH=14
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=900000
NODE_OPTIONS=--max-old-space-size=2048
UV_THREADPOOL_SIZE=16
EOF
        log_success "Environment file created: .env.optimized"
        log_warning "Please update the API keys and secrets in .env.optimized before deploying to production!"
    else
        log_info "Environment file already exists"
    fi
}

# Pull and prepare AI models
prepare_ai_models() {
    log_info "Preparing AI models..."
    
    # Start only Ollama to download models
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d ollama
    
    # Wait for Ollama to be ready
    log_info "Waiting for Ollama to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:11437/api/tags > /dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    # Download essential AI models
    log_info "Downloading AI models (this may take a while)..."
    docker exec sequenceai-ollama-opt ollama pull codellama:7b || log_warning "Failed to pull codellama:7b"
    docker exec sequenceai-ollama-opt ollama pull llama3:8b || log_warning "Failed to pull llama3:8b"
    
    log_success "AI models preparation completed"
}

# Deploy the full stack
deploy() {
    log_info "Deploying SequenceAI Optimized Stack..."
    
    # Build and start all services
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d --build
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_health
    
    log_success "Deployment completed!"
    show_info
}

# Check service health
check_health() {
    log_info "Checking service health..."
    
    local services=("backend:5004/health" "frontend:3011")
    
    for service in "${services[@]}"; do
        local name=$(echo $service | cut -d: -f1)
        local endpoint=$(echo $service | cut -d: -f2)
        
        if curl -f -s "http://localhost:$endpoint" > /dev/null; then
            log_success "$name is healthy"
        else
            log_error "$name is not responding"
        fi
    done
}

# Show deployment information
show_info() {
    echo ""
    echo "🚀 SequenceAI Optimized Stack is running!"
    echo ""
    echo "📱 Frontend (React):     http://localhost:3011"
    echo "🔧 Backend API:          http://localhost:5004"
    echo "📊 API Health:           http://localhost:5004/health"
    echo "🧠 AI Service (Ollama):  http://localhost:11437"
    echo "🔄 Nginx Proxy:          http://localhost:8080"
    echo "📈 Nginx Status:         http://localhost:8080/nginx-status"
    echo ""
    echo "🗄️  Database Ports:"
    echo "   MongoDB:              localhost:27021"
    echo "   Redis:                localhost:6383"
    echo ""
    echo "📋 Management Commands:"
    echo "   View logs:            docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f"
    echo "   Stop services:        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down"
    echo "   Update services:      docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME pull && docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d"
    echo "   View status:          docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps"
    echo ""
    echo "⚙️  Configuration file:   .env.optimized"
    echo "📁 Data directory:       ./data/"
    echo "📝 Logs directory:       ./logs/"
}

# Stop and cleanup
cleanup() {
    log_info "Stopping and cleaning up..."
    
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    
    if [[ "$1" == "--volumes" ]]; then
        log_warning "Removing volumes (this will delete all data)..."
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down -v
        docker volume prune -f
    fi
    
    log_success "Cleanup completed"
}

# Update services
update() {
    log_info "Updating services..."
    
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME pull
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d --build
    
    log_success "Services updated"
}

# Show logs
logs() {
    if [[ -n "$1" ]]; then
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f "$1"
    else
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f
    fi
}

# Show status
status() {
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps
    echo ""
    check_health
}

# Main command handling
case "$1" in
    "deploy")
        check_requirements
        setup_directories
        create_env_file
        prepare_ai_models
        deploy
        ;;
    "start")
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
        check_health
        show_info
        ;;
    "stop")
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
        ;;
    "restart")
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME restart
        ;;
    "update")
        update
        ;;
    "logs")
        logs "$2"
        ;;
    "status")
        status
        ;;
    "health")
        check_health
        ;;
    "cleanup")
        cleanup "$2"
        ;;
    "shell")
        if [[ -n "$2" ]]; then
            docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec "$2" /bin/sh
        else
            log_error "Please specify a service name (backend, frontend, mongodb, redis, ollama)"
        fi
        ;;
    "info")
        show_info
        ;;
    *)
        echo "SequenceAI Optimized Deployment Script"
        echo ""
        echo "Usage: $0 {deploy|start|stop|restart|update|logs|status|health|cleanup|shell|info}"
        echo ""
        echo "Commands:"
        echo "  deploy    - Full deployment with setup"
        echo "  start     - Start all services"
        echo "  stop      - Stop all services"
        echo "  restart   - Restart all services"
        echo "  update    - Update and restart services"
        echo "  logs      - Show logs (optionally for specific service)"
        echo "  status    - Show service status"
        echo "  health    - Check service health"
        echo "  cleanup   - Stop and cleanup (add --volumes to remove data)"
        echo "  shell     - Open shell in service container"
        echo "  info      - Show deployment information"
        echo ""
        echo "Examples:"
        echo "  $0 deploy                 # Full deployment"
        echo "  $0 logs backend           # Show backend logs"
        echo "  $0 shell backend          # Open shell in backend container"
        echo "  $0 cleanup --volumes      # Remove everything including data"
        ;;
esac