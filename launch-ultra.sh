#!/bin/bash

# SequenceAI v2.0 Ultra Launch Script
# Ultra-Premium Email Intelligence Platform

echo "🚀 SequenceAI v2.0 Ultra - Launch Sequence Initiated"
echo "=================================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# ASCII Art Banner
echo -e "${PURPLE}"
cat << "EOF"
  ____                                        _    _____ 
 / ___|  ___  __ _ _   _  ___ _ __   ___ ___  / \  |_   _|
 \___ \ / _ \/ _` | | | |/ _ \ '_ \ / __/ _ \/  _  \  | |  
  ___) |  __/ (_| | |_| |  __/ | | | (_|  __/  /_\  \ | |  
 |____/ \___|\__, |\__,_|\___|_| |_|\___\___\_/   \_\|_|  
                |_|                                       
EOF
echo -e "${NC}"

echo -e "${CYAN}Ultra-Premium Email Intelligence Platform v2.0${NC}"
echo -e "${WHITE}Powered by Advanced AI • Glassmorphism UI • Dark Mode${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Pre-flight checks
echo -e "${CYAN}🔍 Pre-flight System Checks${NC}"
echo "--------------------------------"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
else
    print_status "Docker is running"
fi

# Check if docker-compose exists
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose not found. Please install docker-compose."
    exit 1
else
    print_status "docker-compose is available"
fi

# Check if required files exist
required_files=("docker-compose.yml" "backend/Dockerfile" "frontend/Dockerfile")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file missing: $file"
        exit 1
    else
        print_status "Found: $file"
    fi
done

# Check environment file
if [ ! -f "backend/.env" ]; then
    print_warning "No .env file found. Using development defaults."
    echo "# SequenceAI Development Environment" > backend/.env
    echo "MONGODB_URI=mongodb://mongodb:27017/sequenceai" >> backend/.env
    echo "JWT_SECRET=ultra-secret-development-key-2025" >> backend/.env
    echo "OPENAI_API_KEY=demo-mode" >> backend/.env
    echo "STRIPE_SECRET_KEY=demo-mode" >> backend/.env
    echo "NODE_ENV=development" >> backend/.env
    print_status "Created development .env file"
else
    print_status "Environment file exists"
fi

echo ""

# Run debug check
echo -e "${CYAN}🔍 Running System Validation${NC}"
echo "--------------------------------"
if [ -f "debug-sequenceai.js" ]; then
    node debug-sequenceai.js
else
    print_warning "Debug script not found, skipping validation"
fi

echo ""

# Start services
echo -e "${CYAN}🚀 Launching SequenceAI Ultra Services${NC}"
echo "-------------------------------------"

print_info "Stopping any existing containers..."
docker-compose down > /dev/null 2>&1

print_info "Building ultra-premium containers..."
if docker-compose build --no-cache; then
    print_status "Container build successful"
else
    print_error "Container build failed"
    exit 1
fi

print_info "Starting SequenceAI Ultra Platform..."
if docker-compose up -d; then
    print_status "All services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi

echo ""

# Wait for services to be ready
echo -e "${CYAN}⏳ Waiting for Services to Initialize${NC}"
echo "------------------------------------"

print_info "Waiting for MongoDB to be ready..."
sleep 5

print_info "Waiting for backend API to be ready..."
sleep 10

print_info "Waiting for frontend to compile..."
sleep 15

echo ""

# Service status check
echo -e "${CYAN}📊 Service Status Check${NC}"
echo "-----------------------"

# Check if containers are running
containers=("sequenceai-frontend-1" "sequenceai-backend-1" "sequenceai-mongodb-1" "sequenceai-redis-1")
all_running=true

for container in "${containers[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$container"; then
        print_status "$container is running"
    else
        print_error "$container is not running"
        all_running=false
    fi
done

echo ""

# Display access information
echo -e "${PURPLE}🌟 SequenceAI Ultra v2.0 - Ready for Launch!${NC}"
echo "=============================================="
echo ""
echo -e "${WHITE}🎯 Access Points:${NC}"
echo -e "   ${GREEN}Frontend (Ultra UI):${NC}     http://localhost:3001"
echo -e "   ${GREEN}Backend API:${NC}            http://localhost:5000"
echo -e "   ${GREEN}MongoDB:${NC}               mongodb://localhost:27018"
echo -e "   ${GREEN}Redis:${NC}                 redis://localhost:6380"
echo ""
echo -e "${WHITE}🚀 Ultra Routes (New!):${NC}"
echo -e "   ${CYAN}Ultra Dashboard:${NC}        http://localhost:3001/dashboard-ultra"
echo -e "   ${CYAN}Ultra Generator:${NC}        http://localhost:3001/generator-ultra"
echo -e "   ${CYAN}Quantum Dashboard:${NC}      http://localhost:3001/dashboard-v2"
echo -e "   ${CYAN}Advanced Generator:${NC}     http://localhost:3001/generator-v2"
echo ""
echo -e "${WHITE}🔌 API Endpoints (New!):${NC}"
echo -e "   ${YELLOW}AI Advanced:${NC}           /api/ai-advanced/*"
echo -e "   ${YELLOW}Social Integration:${NC}    /api/social/*"
echo -e "   ${YELLOW}A/B Testing:${NC}           /api/ab-testing/*"
echo ""
echo -e "${WHITE}✨ Ultra Features:${NC}"
echo -e "   ${GREEN}• Dark/Light Mode Toggle with System Detection${NC}"
echo -e "   ${GREEN}• Glassmorphism UI with Advanced Blur Effects${NC}"
echo -e "   ${GREEN}• Holographic Text with Aurora Animations${NC}"
echo -e "   ${GREEN}• 5-Mode AI Email Generation Suite${NC}"
echo -e "   ${GREEN}• Social Media Content Generation${NC}"
echo -e "   ${GREEN}• Advanced A/B Testing with Statistical Analysis${NC}"
echo -e "   ${GREEN}• Voice-to-Email Generation (Beta)${NC}"
echo -e "   ${GREEN}• Multi-Language Email Translation${NC}"
echo -e "   ${GREEN}• Competitor Analysis Intelligence${NC}"
echo -e "   ${GREEN}• Performance Prediction with Neural Networks${NC}"
echo ""

if [ "$all_running" = true ]; then
    echo -e "${GREEN}🎉 All systems operational! SequenceAI Ultra is ready!${NC}"
    echo ""
    echo -e "${WHITE}Quick Start:${NC}"
    echo "1. Open http://localhost:3001 in your browser"
    echo "2. Register a new account or login"
    echo "3. Experience the ultra-premium interface"
    echo "4. Try the new /dashboard-ultra and /generator-ultra routes"
    echo "5. Toggle between dark/light modes with the theme switcher"
    echo ""
    echo -e "${CYAN}💡 Pro Tip: The ultra routes showcase the most advanced features!${NC}"
else
    echo -e "${RED}⚠️  Some services failed to start. Check docker logs for details.${NC}"
    echo ""
    echo -e "${YELLOW}Troubleshooting:${NC}"
    echo "• Run: docker-compose logs"
    echo "• Check: docker ps -a"
    echo "• Restart: docker-compose restart"
fi

echo ""
echo -e "${PURPLE}🚀 SequenceAI Ultra v2.0 Launch Complete!${NC}"
echo "========================================"

# Keep showing logs
echo ""
echo -e "${CYAN}📋 Live Service Logs (Ctrl+C to exit):${NC}"
echo "------------------------------------"
docker-compose logs -f --tail=50