#!/bin/bash

echo "🎯 NeuroColony Advanced Stripe Webhook Setup"
echo "============================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo ""
echo "Choose your webhook configuration level:"
echo ""
echo -e "${GREEN}1) MINIMAL (5 events)${NC} - Basic subscription handling"
echo "   customer.subscription.*, invoice.payment_*"
echo ""
echo -e "${YELLOW}2) STANDARD (15 events)${NC} - Production ready with payment methods"
echo "   + customer.*, checkout.*, payment_method.*, setup_intent.*"
echo ""
echo -e "${BLUE}3) ENTERPRISE (30+ events)${NC} - Full business intelligence & compliance"
echo "   + usage_record.*, dispute.*, tax.*, credit_note.*, coupon.*"
echo ""
echo -e "${PURPLE}4) CUSTOM${NC} - Build your own event list"
echo ""

read -p "Select option (1-4): " choice

case $choice in
    1)
        echo -e "${GREEN}Setting up MINIMAL configuration...${NC}"
        EVENTS="customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed"
        ;;
    2)
        echo -e "${YELLOW}Setting up STANDARD configuration...${NC}"
        EVENTS="customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,customer.subscription.paused,customer.subscription.resumed,invoice.created,invoice.payment_succeeded,invoice.payment_failed,invoice.payment_action_required,customer.created,customer.updated,checkout.session.completed,payment_method.attached,setup_intent.succeeded,charge.failed"
        ;;
    3)
        echo -e "${BLUE}Setting up ENTERPRISE configuration...${NC}"
        EVENTS="customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,customer.subscription.paused,customer.subscription.resumed,invoice.created,invoice.finalized,invoice.payment_succeeded,invoice.payment_failed,invoice.payment_action_required,invoice.upcoming,invoice.paid,invoice.voided,customer.created,customer.updated,customer.deleted,customer.source.created,customer.source.updated,payment_method.attached,payment_method.detached,payment_method.updated,setup_intent.succeeded,setup_intent.requires_action,checkout.session.completed,checkout.session.expired,checkout.session.async_payment_succeeded,checkout.session.async_payment_failed,charge.dispute.created,charge.succeeded,charge.failed,usage_record.created,credit_note.created,tax_rate.created"
        ;;
    4)
        echo -e "${PURPLE}CUSTOM configuration selected${NC}"
        echo ""
        echo "Available event categories:"
        echo "1. Subscriptions: customer.subscription.*"
        echo "2. Payments: invoice.*, charge.*"
        echo "3. Customers: customer.created, customer.updated"
        echo "4. Payment Methods: payment_method.*, setup_intent.*"
        echo "5. Checkout: checkout.session.*"
        echo "6. Usage Billing: usage_record.*, meter.*"
        echo "7. Disputes: charge.dispute.*"
        echo "8. Advanced: credit_note.*, coupon.*, tax_rate.*"
        echo ""
        read -p "Enter comma-separated event list: " EVENTS
        ;;
    *)
        echo -e "${RED}Invalid option. Using MINIMAL configuration.${NC}"
        EVENTS="customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed"
        ;;
esac

echo ""
echo -e "${BLUE}Events to configure:${NC}"
echo "$EVENTS" | tr ',' '\n' | sed 's/^/  ✓ /'
echo ""

# Count events
EVENT_COUNT=$(echo "$EVENTS" | tr ',' '\n' | wc -l)
echo -e "${GREEN}Total events: $EVENT_COUNT${NC}"
echo ""

# Check Stripe CLI
if ! command -v stripe &> /dev/null; then
    echo -e "${RED}❌ Stripe CLI not found. Installing...${NC}"
    cd /tmp
    wget -q https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.4_linux_x86_64.tar.gz
    tar -xzf stripe_1.19.4_linux_x86_64.tar.gz
    sudo mv stripe /usr/local/bin/
    echo -e "${GREEN}✅ Stripe CLI installed${NC}"
else
    echo -e "${GREEN}✅ Stripe CLI found${NC}"
fi

echo ""
echo -e "${YELLOW}⚠️ Make sure you've run 'stripe login' first!${NC}"
echo ""
echo -e "${BLUE}Starting webhook forwarding...${NC}"
echo ""
echo -e "${RED}COPY THE WEBHOOK SIGNING SECRET (whsec_...) THAT APPEARS!${NC}"
echo ""

read -p "Press Enter to start webhook forwarding (Ctrl+C to stop)..."

# Start webhook forwarding
stripe listen --forward-to localhost:5000/api/webhooks/stripe --events "$EVENTS"