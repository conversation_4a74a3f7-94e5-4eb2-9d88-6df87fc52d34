{"timestamp": "2025-06-29T15:21:44.930Z", "duration": 3, "summary": {"info": 46, "warnings": 20, "errors": 0}, "details": {"info": [{"timestamp": "2025-06-29T15:21:44.929Z", "level": "info", "message": "🔍 Checking directory structure...", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: backend", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: backend/routes", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: backend/services", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: backend/models", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend/src", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend/src/components", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend/src/components/premium", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend/src/contexts", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Directory exists: frontend/src/styles", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "info", "message": "🔍 Checking backend files...", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/server.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/package.json", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/routes/ai-advanced.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/routes/social-integration.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/routes/ab-testing.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: backend/services/aiService.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "info", "message": "🔍 Checking frontend files...", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: frontend/src/App.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: frontend/src/components/premium/UltraDashboard.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.929Z", "level": "success", "message": "✓ Found: frontend/src/components/premium/UltraEmailGenerator.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: frontend/src/components/premium/ThemeToggle.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: frontend/src/contexts/ThemeContext.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: frontend/src/styles/premium-theme.css", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "🔍 Checking package.json dependencies...", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Backend dependency: brain.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Backend dependency: natural", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Backend dependency: openai", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Backend dependency: express", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Backend dependency: mongoose", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "🔍 Checking Docker configuration...", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: docker-compose.yml", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: backend/Dockerfile", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Found: frontend/Dockerfile", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "🔍 Validating JavaScript syntax...", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Syntax OK: backend/server.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Syntax OK: backend/routes/ai-advanced.js", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Syntax OK: frontend/src/App.jsx", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "🔍 Checking environment configuration...", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Environment variable: MONGODB_URI", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Environment variable: JWT_SECRET", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Environment variable: OPENAI_API_KEY", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "success", "message": "✓ Environment variable: STRIPE_SECRET_KEY", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "🔍 Checking system resources...", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "info", "message": "Memory usage:", "details": {"rss": "42.28 MB", "heapTotal": "4.73 MB", "heapUsed": "4.46 MB", "external": "1.61 MB"}}], "warnings": [{"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:98", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:99", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:129", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:134", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:138", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:153", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:156", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:158", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:160", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:162", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:164", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:166", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:168", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:170", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:172", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:174", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:193", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:223", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "Debug console.log found in backend/server.js:224", "details": null}, {"timestamp": "2025-06-29T15:21:44.930Z", "level": "warning", "message": "High memory usage detected", "details": null}], "errors": []}}