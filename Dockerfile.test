# Multi-stage Dockerfile for Testing
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/
COPY architecture/testing/package*.json ./architecture/testing/

# Install dependencies
RUN npm ci --only=production --ignore-scripts

# Development stage for testing
FROM base AS test

# Install all dependencies including dev dependencies
RUN npm ci --ignore-scripts

# Copy test-specific dependencies
COPY architecture/testing/package*.json ./architecture/testing/
RUN cd architecture/testing && npm ci

# Copy source code
COPY . .

# Create directories for test outputs
RUN mkdir -p coverage test-reports logs

# Set appropriate permissions
RUN chmod -R 755 coverage test-reports logs

# Install global test tools
RUN npm install -g \
    nyc \
    mocha \
    nodemon

# Set environment variables for testing
ENV NODE_ENV=test
ENV LOG_LEVEL=error
ENV TEST_TIMEOUT=30000

# Health check for test environment
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-3001}/health || exit 1

# Default command runs all tests
CMD ["npm", "run", "test:ci"]

# Expose port for test server
EXPOSE 3001

# Labels for container identification
LABEL maintainer="SequenceAI Team"
LABEL description="Testing environment for SequenceAI application"
LABEL version="1.0.0"
LABEL environment="test"