import { lazy, Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import Login from './pages/Login'
import Register from './pages/Register'
import PricingPage from './pages/PricingPage'
import TermsOfService from './pages/TermsOfService'
import PrivacyPolicy from './pages/PrivacyPolicy'
import AboutUs from './pages/AboutUs'
import ContactPage from './pages/ContactPage'
import FAQ from './pages/FAQ'
import NotFound from './pages/NotFound'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import ProtectedRoute from './components/ProtectedRoute'
import UsageNotification from './components/UsageNotification'
import ErrorBoundary from './components/ErrorBoundary'
import NeuralNetworkBackground from './components/NeuralNetworkBackground'

// Code splitting for heavy dashboard components
const Dashboard = lazy(() => import('./pages/Dashboard'))
const BusinessDashboard = lazy(() => import('./pages/BusinessDashboard'))
const GeneratorPage = lazy(() => import('./pages/GeneratorPage'))
const EnhancedGeneratorPage = lazy(() => import('./pages/EnhancedGeneratorPage'))
const SimpleGeneratorPage = lazy(() => import('./pages/SimpleGeneratorPage'))
const SequencesPage = lazy(() => import('./pages/SequencesPage'))
const WorkingDashboard = lazy(() => import('./pages/WorkingDashboard'))
const TemplatesPage = lazy(() => import('./pages/TemplatesPage'))
const AdvancedFeaturesPage = lazy(() => import('./pages/AdvancedFeaturesPage'))
const QuantumDashboard = lazy(() => import('./components/v2/QuantumDashboard'))
const AdvancedEmailGenerator = lazy(() => import('./components/v2/AdvancedEmailGenerator'))
const UltraDashboard = lazy(() => import('./components/premium/UltraDashboard'))
const UltraEmailGenerator = lazy(() => import('./components/premium/UltraEmailGenerator'))
const WhiteLabelManager = lazy(() => import('./components/WhiteLabelManager'))
const RevenueOptimizer = lazy(() => import('./components/RevenueOptimizer'))
const BillingPage = lazy(() => import('./pages/BillingPage'))
const SettingsPage = lazy(() => import('./pages/SettingsPage'))
const AgentDashboard = lazy(() => import('./pages/AgentDashboard'))
const AgentMarketplace = lazy(() => import('./pages/AgentMarketplace'))
const AgentBuilder = lazy(() => import('./pages/AgentBuilder'))
const ColonyDashboard = lazy(() => import('./pages/ColonyDashboard'))
const ColonyCommand = lazy(() => import('./pages/ColonyCommand'))
const IntegrationHub = lazy(() => import('./pages/IntegrationHub'))
const BusinessCommandCenter = lazy(() => import('./pages/BusinessCommandCenter'))
const ThemeShowcase = lazy(() => import('./pages/ThemeShowcase'))
import { ThemeProvider } from './contexts/ThemeContext'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { Spinner } from './components/ui/DesignSystem'
import './styles/globals.css'
import './styles/neurocolony-theme.css'

// Main App Content Component
function AppContent() {
  console.log('📱 AppContent rendering...')
  const { user, loading } = useAuth()
  console.log('👤 Auth state:', { user: !!user, loading })

  if (loading) {
    console.log('⏳ Showing loading spinner...')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="xl" />
      </div>
    )
  }

  console.log('🎨 Rendering main app content...')

  return (
    <ThemeProvider>
      <ErrorBoundary>
        <div className="min-h-screen relative" style={{ background: 'var(--color-background)' }}>
          <NeuralNetworkBackground />
          <Navbar />
          <main className="flex-1 relative z-10">
            <Suspense fallback={
              <div className="min-h-screen flex items-center justify-center">
                <Spinner size="xl" />
              </div>
            }>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/pricing" element={<PricingPage />} />
                <Route path="/templates" element={<TemplatesPage user={user} />} />
                <Route path="/about" element={<AboutUs />} />
                <Route path="/contact" element={<ContactPage />} />
                <Route path="/faq" element={<FAQ />} />
                <Route path="/terms" element={<TermsOfService />} />
                <Route path="/privacy" element={<PrivacyPolicy />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/login-old" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/theme-showcase" element={<ThemeShowcase />} />
                <Route path="/dashboard" element={<Navigate to="/colony-command" replace />} />
                <Route path="/dashboard-basic" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                <Route path="/dashboard-v2" element={
                  <ProtectedRoute>
                    <QuantumDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/dashboard-ultra" element={
                  <ProtectedRoute>
                    <UltraDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/generator" element={<Navigate to="/generator-simple" replace />} />
                <Route path="/generator-basic" element={
                  <ProtectedRoute>
                    <GeneratorPage />
                  </ProtectedRoute>
                } />
                <Route path="/generator-v2" element={
                  <ProtectedRoute>
                    <AdvancedEmailGenerator />
                  </ProtectedRoute>
                } />
                <Route path="/generator-ultra" element={
                  <ProtectedRoute>
                    <UltraEmailGenerator />
                  </ProtectedRoute>
                } />
                <Route path="/generator-enhanced" element={
                  <ProtectedRoute>
                    <EnhancedGeneratorPage />
                  </ProtectedRoute>
                } />
                <Route path="/generator-simple" element={
                  <ProtectedRoute>
                    <SimpleGeneratorPage />
                  </ProtectedRoute>
                } />
                <Route path="/sequences" element={
                  <ProtectedRoute>
                    <SequencesPage />
                  </ProtectedRoute>
                } />
                <Route path="/dashboard-working" element={
                  <ProtectedRoute>
                    <WorkingDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/agent-dashboard" element={
                  <ProtectedRoute>
                    <AgentDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/agent-marketplace" element={
                  <ProtectedRoute>
                    <AgentMarketplace />
                  </ProtectedRoute>
                } />
                <Route path="/agent-builder" element={
                  <ProtectedRoute>
                    <AgentBuilder />
                  </ProtectedRoute>
                } />
                <Route path="/colony-dashboard" element={
                  <ProtectedRoute>
                    <ColonyDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/colony-command" element={
                  <ProtectedRoute>
                    <ColonyCommand />
                  </ProtectedRoute>
                } />
                <Route path="/integration-hub" element={
                  <ProtectedRoute>
                    <IntegrationHub />
                  </ProtectedRoute>
                } />
                <Route path="/command-center" element={
                  <ProtectedRoute>
                    <BusinessCommandCenter />
                  </ProtectedRoute>
                } />
                <Route path="/advanced/:id" element={
                  <ProtectedRoute>
                    <AdvancedFeaturesPage />
                  </ProtectedRoute>
                } />
                <Route path="/white-label" element={
                  <ProtectedRoute>
                    <WhiteLabelManager />
                  </ProtectedRoute>
                } />
                <Route path="/revenue-optimizer" element={
                  <ProtectedRoute>
                    <RevenueOptimizer />
                  </ProtectedRoute>
                } />
                <Route path="/billing" element={
                  <ProtectedRoute>
                    <BillingPage />
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <SettingsPage />
                  </ProtectedRoute>
                } />
              <Route path="/enterprise-analytics" element={
                <ProtectedRoute>
                  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                    <div className="text-center">
                      <h1 className="text-3xl font-bold text-gray-900 mb-4">Enterprise Analytics</h1>
                      <p className="text-gray-600">Advanced business intelligence dashboard coming soon</p>
                    </div>
                  </div>
                </ProtectedRoute>
              } />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
          </main>
          <Footer />
          
          {/* Usage Notifications - only show for authenticated users */}
          {user && <UsageNotification />}
          
          
          {/* Toast Notifications */}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#f3f4f6',
                border: '1px solid #374151'
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#f3f4f6',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f3f4f6',
                },
              },
            }}
          />
        </div>
      </ErrorBoundary>
    </ThemeProvider>
  )
}

function App() {
  console.log(' App component rendering...')
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App