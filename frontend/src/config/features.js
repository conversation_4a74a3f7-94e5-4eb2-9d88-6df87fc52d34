/**
 * Frontend Feature Flags Configuration
 * Controls which features are available in the UI
 */

// Get feature flags from environment variables or defaults
export const featureFlags = {
  // Core features (always enabled)
  emailSequences: true,
  templates: true,
  authentication: true,
  
  // AI features
  aiGeneration: true,
  agents: import.meta.env.VITE_ENABLE_AGENTS === 'true' || true,
  workflows: import.meta.env.VITE_ENABLE_WORKFLOWS === 'true' || true,
  
  // Integration features
  integrations: import.meta.env.VITE_ENABLE_INTEGRATIONS === 'true' || true,
  webhooks: import.meta.env.VITE_ENABLE_WEBHOOKS === 'true' || false,
  
  // Billing features
  billing: true,
  usageTracking: true,
  
  // Enterprise features
  whiteLabel: import.meta.env.VITE_ENABLE_WHITE_LABEL === 'true' || false,
  revenueOptimizer: import.meta.env.VITE_ENABLE_REVENUE_OPTIMIZER === 'true' || false,
  advancedAnalytics: import.meta.env.VITE_ENABLE_ADVANCED_ANALYTICS === 'true' || false,
  
  // UI features
  darkMode: true,
  betaFeatures: import.meta.env.VITE_ENABLE_BETA === 'true' || false,
  
  // Performance features
  lazyLoading: true,
  caching: true,
  
  // Development features
  devTools: import.meta.env.DEV || false,
  mockData: import.meta.env.VITE_USE_MOCK_DATA === 'true' || false
};

// Feature availability by plan
export const planFeatures = {
  free: [
    'emailSequences',
    'templates',
    'authentication',
    'billing'
  ],
  pro: [
    ...planFeatures.free,
    'aiGeneration',
    'integrations',
    'usageTracking'
  ],
  business: [
    ...planFeatures.pro,
    'agents',
    'workflows',
    'webhooks',
    'revenueOptimizer'
  ],
  enterprise: [
    ...planFeatures.business,
    'whiteLabel',
    'advancedAnalytics',
    'betaFeatures'
  ]
};

// Check if a feature is available for a user's plan
export const isFeatureAvailable = (feature, userPlan = 'free') => {
  if (!featureFlags[feature]) return false;
  
  const availableFeatures = planFeatures[userPlan] || planFeatures.free;
  return availableFeatures.includes(feature);
};

// Get all available features for a plan
export const getAvailableFeatures = (userPlan = 'free') => {
  const availableFeatures = planFeatures[userPlan] || planFeatures.free;
  return availableFeatures.filter(feature => featureFlags[feature]);
};

// Feature descriptions for UI
export const featureDescriptions = {
  emailSequences: 'Create and manage email sequences',
  templates: 'Access pre-built email templates',
  aiGeneration: 'AI-powered content generation',
  agents: 'Autonomous AI agents for marketing',
  workflows: 'Automated workflow builder',
  integrations: 'Connect with external services',
  webhooks: 'Real-time event notifications',
  whiteLabel: 'Custom branding and domains',
  revenueOptimizer: 'Revenue optimization tools',
  advancedAnalytics: 'Advanced analytics and reporting'
};

// Export default object for easier imports
export default {
  flags: featureFlags,
  planFeatures,
  isFeatureAvailable,
  getAvailableFeatures,
  descriptions: featureDescriptions
};