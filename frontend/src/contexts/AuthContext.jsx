import React, { createContext, useContext, useReducer, useEffect } from 'react'
import axios from 'axios'

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api'

// Auth reducer for state management
const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null }
    case 'LOGIN_SUCCESS':
      return { 
        ...state, 
        loading: false, 
        isAuthenticated: true, 
        user: action.payload.user,
        token: action.payload.token,
        error: null 
      }
    case 'LOGIN_FAILURE':
      return { 
        ...state, 
        loading: false, 
        isAuthenticated: false, 
        user: null,
        token: null,
        error: action.payload 
      }
    case 'LOGOUT':
      return { 
        ...state, 
        loading: false,
        isAuthenticated: false, 
        user: null, 
        token: null,
        error: null 
      }
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    case 'UPDATE_USER':
      return { ...state, user: { ...state.user, ...action.payload } }
    default:
      return state
  }
}

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true, // Start as loading
  error: null
}

const AuthContext = createContext()

// Enhanced HTTP client with interceptors
const createApiClient = (token) => {
  const client = axios.create({
    baseURL: API_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    }
  })

  // Request interceptor for token refresh
  client.interceptors.request.use(
    (config) => {
      const currentToken = localStorage.getItem('authToken')
      if (currentToken) {
        config.headers.Authorization = `Bearer ${currentToken}`
      }
      return config
    },
    (error) => Promise.reject(error)
  )

  // Response interceptor for automatic token refresh
  client.interceptors.response.use(
    (response) => response,
    async (error) => {
      const original = error.config
      
      if (error.response?.status === 401 && !original._retry) {
        original._retry = true
        
        try {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            const response = await axios.post(`${API_URL}/auth/refresh`, {
              refreshToken
            })
            
            const { token: newToken } = response.data
            localStorage.setItem('authToken', newToken)
            
            return client(original)
          }
        } catch (refreshError) {
          // Refresh failed, logout user
          localStorage.removeItem('authToken')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
        }
      }
      
      return Promise.reject(error)
    }
  )

  return client
}

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🔐 Initializing auth...')
      dispatch({ type: 'LOGIN_START' })
      
      const token = localStorage.getItem('authToken')
      const storedUser = localStorage.getItem('user')
      
      if (token && storedUser) {
        try {
          console.log('📞 Verifying token with API...')
          // Verify token is still valid
          const api = createApiClient(token)
          const response = await api.get('/auth/me')
          
          console.log(' Token valid, user authenticated')
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              user: response.data.user,
              token
            }
          })
        } catch (error) {
          console.log('❌ Token invalid, logging out')
          // Token is invalid, clear storage
          localStorage.removeItem('authToken')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('user')
          dispatch({ type: 'LOGOUT' })
        }
      } else {
        console.log('🔒 No stored auth, continuing as guest')
        dispatch({ type: 'LOGOUT' })
      }
    }

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.log('⏰ Auth initialization timeout, proceeding...')
      dispatch({ type: 'LOGOUT' })
    }, 5000)

    initializeAuth().finally(() => {
      clearTimeout(timeoutId)
    })
  }, [])

  const login = async (email, password) => {
    dispatch({ type: 'LOGIN_START' })
    
    try {
      const api = createApiClient()
      const response = await api.post('/auth/login', { email, password })
      
      const { token, user } = response.data
      
      // Store in localStorage (in production, use httpOnly cookies)
      localStorage.setItem('authToken', token)
      localStorage.setItem('user', JSON.stringify(user))
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      })
      
      return { success: true, user, token }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Login failed'
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      })
      
      return { success: false, error: errorMessage }
    }
  }

  const register = async (name, email, password) => {
    dispatch({ type: 'LOGIN_START' })
    
    try {
      const api = createApiClient()
      const response = await api.post('/auth/register', { name, email, password })
      
      const { token, user } = response.data
      
      localStorage.setItem('authToken', token)
      localStorage.setItem('user', JSON.stringify(user))
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      })
      
      return { success: true, user, token }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Registration failed'
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      })
      
      return { success: false, error: errorMessage }
    }
  }

  const logout = async () => {
    try {
      const api = createApiClient(state.token)
      await api.post('/auth/logout')
    } catch (error) {
      console.warn('Logout API call failed:', error)
    } finally {
      localStorage.removeItem('authToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      dispatch({ type: 'LOGOUT' })
    }
  }

  const updateUser = (userData) => {
    const updatedUser = { ...state.user, ...userData }
    localStorage.setItem('user', JSON.stringify(updatedUser))
    dispatch({ type: 'UPDATE_USER', payload: userData })
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  const getApiClient = () => createApiClient(state.token)

  const value = {
    ...state,
    login,
    register,
    logout,
    updateUser,
    clearError,
    getApiClient
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext