/**
 * Unified Email Generator Component
 * Consolidates all email generator variations into a single, configurable component
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Spinner } from '../../components/ui/Spinner';
import { featureFlags } from '../../config/features';
import { sequenceAPI } from '../../services/api/sequences';
import { toast } from 'react-hot-toast';

const UnifiedEmailGenerator = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [mode, setMode] = useState('standard'); // standard, advanced, ai-powered
  const [loading, setLoading] = useState(false);
  const [generatedSequence, setGeneratedSequence] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    topic: '',
    tone: 'professional',
    length: 5,
    targetAudience: '',
    industry: '',
    goals: [],
    keywords: [],
    competitorUrl: '',
    brandVoice: '',
    includePersonalization: true,
    includeABTesting: false
  });

  // Available modes based on feature flags and user plan
  const availableModes = [
    { id: 'standard', name: 'Standard', description: 'Basic email sequence generation' },
    { 
      id: 'advanced', 
      name: 'Advanced', 
      description: 'Enhanced with AI optimization',
      requiresPlan: 'pro'
    },
    { 
      id: 'ai-powered', 
      name: 'AI Powered', 
      description: 'Full AI automation with multiple providers',
      requiresPlan: 'business',
      requiresFeature: 'aiEngine'
    }
  ].filter(mode => {
    if (mode.requiresFeature && !featureFlags[mode.requiresFeature]) return false;
    if (mode.requiresPlan && user?.plan !== mode.requiresPlan && user?.plan !== 'enterprise') return false;
    return true;
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleArrayInput = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value.split(',').map(item => item.trim()).filter(Boolean)
    }));
  };

  const handleGenerate = async () => {
    // Validate required fields
    if (!formData.topic) {
      toast.error('Please enter a topic for your email sequence');
      return;
    }

    try {
      setLoading(true);
      
      const params = {
        ...formData,
        generationMode: mode
      };

      const response = await sequenceAPI.generateSequence(params);
      
      setGeneratedSequence(response.sequence);
      toast.success('Email sequence generated successfully!');
      
      // Track analytics
      if (window.gtag) {
        window.gtag('event', 'generate_sequence', {
          mode,
          length: formData.length,
          industry: formData.industry
        });
      }
    } catch (error) {
      console.error('Generation error:', error);
      toast.error(error.message || 'Failed to generate sequence');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSequence = async () => {
    if (!generatedSequence) return;

    try {
      setLoading(true);
      await sequenceAPI.saveSequence(generatedSequence);
      toast.success('Sequence saved successfully!');
      navigate('/dashboard/sequences');
    } catch (error) {
      toast.error('Failed to save sequence');
    } finally {
      setLoading(false);
    }
  };

  const renderModeSelector = () => (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4">Generation Mode</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {availableModes.map((modeOption) => (
          <Card
            key={modeOption.id}
            className={`p-4 cursor-pointer border-2 transition-all ${
              mode === modeOption.id
                ? 'border-indigo-600 bg-indigo-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setMode(modeOption.id)}
          >
            <h4 className="font-semibold">{modeOption.name}</h4>
            <p className="text-sm text-gray-600 mt-1">{modeOption.description}</p>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderBasicInputs = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Topic <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="topic"
          value={formData.topic}
          onChange={handleInputChange}
          placeholder="e.g., Product launch, Customer onboarding"
          className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tone
          </label>
          <select
            name="tone"
            value={formData.tone}
            onChange={handleInputChange}
            className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
          >
            <option value="professional">Professional</option>
            <option value="friendly">Friendly</option>
            <option value="casual">Casual</option>
            <option value="formal">Formal</option>
            <option value="persuasive">Persuasive</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Emails
          </label>
          <input
            type="number"
            name="length"
            value={formData.length}
            onChange={handleInputChange}
            min="3"
            max="10"
            className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Target Audience
        </label>
        <input
          type="text"
          name="targetAudience"
          value={formData.targetAudience}
          onChange={handleInputChange}
          placeholder="e.g., Small business owners, Marketing managers"
          className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Industry
        </label>
        <input
          type="text"
          name="industry"
          value={formData.industry}
          onChange={handleInputChange}
          placeholder="e.g., SaaS, E-commerce, Healthcare"
          className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
        />
      </div>
    </div>
  );

  const renderAdvancedInputs = () => {
    if (mode === 'standard') return null;

    return (
      <div className="mt-6 space-y-6 border-t pt-6">
        <h3 className="text-lg font-semibold">Advanced Options</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Goals (comma-separated)
          </label>
          <input
            type="text"
            value={formData.goals.join(', ')}
            onChange={(e) => handleArrayInput('goals', e.target.value)}
            placeholder="e.g., Increase engagement, Drive sales, Build trust"
            className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Keywords (comma-separated)
          </label>
          <input
            type="text"
            value={formData.keywords.join(', ')}
            onChange={(e) => handleArrayInput('keywords', e.target.value)}
            placeholder="e.g., innovation, efficiency, ROI"
            className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        {mode === 'ai-powered' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Competitor URL (for analysis)
              </label>
              <input
                type="url"
                name="competitorUrl"
                value={formData.competitorUrl}
                onChange={handleInputChange}
                placeholder="https://competitor.com"
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Brand Voice Description
              </label>
              <textarea
                name="brandVoice"
                value={formData.brandVoice}
                onChange={handleInputChange}
                rows="3"
                placeholder="Describe your brand's unique voice and personality..."
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="includePersonalization"
                  checked={formData.includePersonalization}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <span className="text-sm">Include personalization tokens</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="includeABTesting"
                  checked={formData.includeABTesting}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <span className="text-sm">Generate A/B test variations</span>
              </label>
            </div>
          </>
        )}
      </div>
    );
  };

  const renderGeneratedSequence = () => {
    if (!generatedSequence) return null;

    return (
      <div className="mt-8 space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Generated Sequence</h3>
          <div className="space-x-4">
            <Button
              variant="outline"
              onClick={() => setGeneratedSequence(null)}
            >
              Regenerate
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveSequence}
            >
              Save Sequence
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {generatedSequence.emails.map((email, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold">Email {index + 1}</h4>
                <span className="text-sm text-gray-500">
                  Day {email.dayDelay || index * 2}
                </span>
              </div>
              
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-700">Subject:</span>
                  <p className="mt-1">{email.subject}</p>
                </div>
                
                {email.preview && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Preview:</span>
                    <p className="mt-1 text-gray-600">{email.preview}</p>
                  </div>
                )}
                
                <div>
                  <span className="text-sm font-medium text-gray-700">Content:</span>
                  <div className="mt-1 p-4 bg-gray-50 rounded-lg">
                    <p className="whitespace-pre-wrap">{email.content}</p>
                  </div>
                </div>

                {email.callToAction && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">CTA:</span>
                    <p className="mt-1 text-indigo-600 font-medium">{email.callToAction}</p>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Email Sequence Generator</h1>
          <p className="mt-2 text-gray-600">
            Create engaging email sequences powered by AI
          </p>
        </div>

        <Card className="p-8">
          {renderModeSelector()}
          
          <form onSubmit={(e) => { e.preventDefault(); handleGenerate(); }}>
            {renderBasicInputs()}
            {renderAdvancedInputs()}
            
            <div className="mt-8">
              <Button
                type="submit"
                variant="primary"
                className="w-full"
                disabled={loading || !formData.topic}
              >
                {loading ? (
                  <>
                    <Spinner size="small" className="mr-2" />
                    Generating...
                  </>
                ) : (
                  'Generate Email Sequence'
                )}
              </Button>
            </div>
          </form>
        </Card>

        {renderGeneratedSequence()}
      </div>
    </div>
  );
};

export default UnifiedEmailGenerator;