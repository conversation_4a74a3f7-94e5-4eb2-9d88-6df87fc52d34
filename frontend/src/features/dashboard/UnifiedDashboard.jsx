/**
 * Unified Dashboard Component
 * Consolidates all dashboard variations into a single, feature-flag controlled component
 */

import React, { useState, useEffect, lazy, Suspense } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/ui/Card';
import { Spinner } from '../../components/ui/Spinner';
import { featureFlags } from '../../config/features';
import { dashboardAPI } from '../../services/api/dashboard';

// Lazy load dashboard modules based on features
const AnalyticsModule = lazy(() => import('./modules/AnalyticsModule'));
const SequencesModule = lazy(() => import('./modules/SequencesModule'));
const AgentsModule = lazy(() => import('./modules/AgentsModule'));
const BillingModule = lazy(() => import('./modules/BillingModule'));
const WorkflowsModule = lazy(() => import('./modules/WorkflowsModule'));
const ColonyModule = lazy(() => import('./modules/ColonyModule'));

const UnifiedDashboard = () => {
  const { user } = useAuth();
  const [activeModule, setActiveModule] = useState('overview');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await dashboardAPI.getDashboardOverview();
      setDashboardData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Define available modules based on feature flags and user permissions
  const availableModules = [
    { id: 'overview', name: 'Overview', icon: '📊', enabled: true },
    { id: 'sequences', name: 'Email Sequences', icon: '📧', enabled: featureFlags.emailSequences },
    { id: 'agents', name: 'AI Agents', icon: '🤖', enabled: featureFlags.agents },
    { id: 'workflows', name: 'Workflows', icon: '🔄', enabled: featureFlags.workflows },
    { id: 'analytics', name: 'Analytics', icon: '📈', enabled: featureFlags.analytics },
    { id: 'billing', name: 'Billing', icon: '💳', enabled: featureFlags.billing },
    { id: 'colony', name: 'Colony', icon: '🏭', enabled: featureFlags.colony && user?.plan === 'enterprise' }
  ].filter(module => module.enabled);

  const renderModuleContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <Spinner size="large" />
        </div>
      );
    }

    if (error) {
      return (
        <Card className="p-6 bg-red-50 border-red-200">
          <p className="text-red-600">Error loading dashboard: {error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </Card>
      );
    }

    switch (activeModule) {
      case 'overview':
        return <OverviewModule data={dashboardData} />;
      case 'sequences':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <SequencesModule />
          </Suspense>
        );
      case 'agents':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <AgentsModule />
          </Suspense>
        );
      case 'workflows':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <WorkflowsModule />
          </Suspense>
        );
      case 'analytics':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <AnalyticsModule />
          </Suspense>
        );
      case 'billing':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <BillingModule />
          </Suspense>
        );
      case 'colony':
        return (
          <Suspense fallback={<ModuleLoader />}>
            <ColonyModule />
          </Suspense>
        );
      default:
        return <OverviewModule data={dashboardData} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {user?.name || 'User'}
            </h1>
            <p className="mt-2 text-gray-600">
              Manage your AI-powered email marketing campaigns
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar Navigation */}
          <aside className="w-64 flex-shrink-0">
            <nav className="space-y-1">
              {availableModules.map((module) => (
                <button
                  key={module.id}
                  onClick={() => setActiveModule(module.id)}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    activeModule === module.id
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="mr-3 text-lg">{module.icon}</span>
                  {module.name}
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {renderModuleContent()}
          </main>
        </div>
      </div>
    </div>
  );
};

// Overview Module Component
const OverviewModule = ({ data }) => {
  if (!data) return null;

  const stats = [
    {
      label: 'Total Sequences',
      value: data.totalSequences || 0,
      change: '+12%',
      icon: '📧'
    },
    {
      label: 'Active Campaigns',
      value: data.activeCampaigns || 0,
      change: '+5%',
      icon: '🚀'
    },
    {
      label: 'Total Subscribers',
      value: data.totalSubscribers || 0,
      change: '+18%',
      icon: '👥'
    },
    {
      label: 'Avg Open Rate',
      value: `${data.avgOpenRate || 0}%`,
      change: '+2.3%',
      icon: '📈'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold mt-1">{stat.value}</p>
                <p className="text-sm text-green-600 mt-1">{stat.change}</p>
              </div>
              <div className="text-3xl">{stat.icon}</div>
            </div>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {data.recentActivity?.map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b last:border-0">
              <div>
                <p className="font-medium">{activity.title}</p>
                <p className="text-sm text-gray-600">{activity.description}</p>
              </div>
              <span className="text-sm text-gray-500">{activity.time}</span>
            </div>
          )) || (
            <p className="text-gray-500">No recent activity</p>
          )}
        </div>
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 border rounded-lg hover:bg-gray-50 text-center">
            <div className="text-2xl mb-2">✨</div>
            <span className="text-sm">Generate Sequence</span>
          </button>
          <button className="p-4 border rounded-lg hover:bg-gray-50 text-center">
            <div className="text-2xl mb-2">📊</div>
            <span className="text-sm">View Analytics</span>
          </button>
          <button className="p-4 border rounded-lg hover:bg-gray-50 text-center">
            <div className="text-2xl mb-2">🤖</div>
            <span className="text-sm">Browse Agents</span>
          </button>
          <button className="p-4 border rounded-lg hover:bg-gray-50 text-center">
            <div className="text-2xl mb-2">⚙️</div>
            <span className="text-sm">Settings</span>
          </button>
        </div>
      </Card>
    </div>
  );
};

// Module Loader Component
const ModuleLoader = () => (
  <div className="flex items-center justify-center h-64">
    <Spinner size="large" />
    <span className="ml-3 text-gray-600">Loading module...</span>
  </div>
);

export default UnifiedDashboard;