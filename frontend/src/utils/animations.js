// Performance-optimized animation configurations for NeuroColony
// Using GPU-accelerated transforms and respecting prefers-reduced-motion

import React from 'react';

export const shouldReduceMotion = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Animation variants for Framer Motion
export const animations = {
  // Page transitions
  pageTransition: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeInOut' }
  },

  // Card animations
  cardHover: {
    rest: { 
      scale: 1, 
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
    },
    hover: { 
      scale: shouldReduceMotion() ? 1 : 1.02,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 30
      }
    }
  },

  // Neural network particle connections
  neuralPulse: {
    initial: { pathLength: 0, opacity: 0 },
    animate: { 
      pathLength: 1, 
      opacity: [0, 0.8, 0],
      transition: {
        pathLength: { duration: 2, ease: 'easeInOut' },
        opacity: { duration: 2, ease: 'easeInOut', repeat: Infinity }
      }
    }
  },

  // Stagger children animations
  staggerContainer: {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceMotion() ? 0 : 0.1
      }
    }
  },

  staggerItem: {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  },

  // Hexagonal grid transitions
  hexagonHover: {
    rest: { 
      rotate: 0,
      scale: 1,
      filter: 'brightness(1)'
    },
    hover: { 
      rotate: shouldReduceMotion() ? 0 : 360,
      scale: shouldReduceMotion() ? 1 : 1.1,
      filter: 'brightness(1.2)',
      transition: {
        rotate: { duration: 0.6, ease: 'easeInOut' },
        scale: { duration: 0.3, ease: 'easeOut' }
      }
    }
  },

  // Success/Error state animations
  successPulse: {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: [0.8, 1.2, 1],
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  },

  errorShake: {
    animate: {
      x: shouldReduceMotion() ? 0 : [-10, 10, -10, 10, 0],
      transition: {
        duration: 0.5,
        ease: 'easeInOut'
      }
    }
  },

  // Progress indicators
  progressBar: {
    initial: { scaleX: 0 },
    animate: { 
      scaleX: 1,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  },

  // Floating elements
  float: {
    animate: {
      y: shouldReduceMotion() ? 0 : [0, -10, 0],
      transition: {
        duration: 3,
        ease: 'easeInOut',
        repeat: Infinity
      }
    }
  },

  // Glow effect
  glow: {
    animate: {
      boxShadow: shouldReduceMotion() 
        ? '0 0 0 rgba(139, 92, 246, 0)' 
        : [
            '0 0 20px rgba(139, 92, 246, 0.3)',
            '0 0 40px rgba(139, 92, 246, 0.5)',
            '0 0 20px rgba(139, 92, 246, 0.3)'
          ],
      transition: {
        duration: 2,
        ease: 'easeInOut',
        repeat: Infinity
      }
    }
  },

  // Data flow animation
  dataFlow: {
    initial: { pathLength: 0, opacity: 0 },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 1.5, ease: 'easeInOut' },
        opacity: { duration: 0.5 }
      }
    }
  },

  // Skeleton loader
  skeleton: {
    animate: {
      backgroundPosition: shouldReduceMotion() ? '0% 0%' : ['0% 0%', '100% 0%'],
      transition: {
        duration: 1.5,
        ease: 'linear',
        repeat: Infinity
      }
    }
  }
};

// CSS class utilities for GPU-accelerated animations
export const animationClasses = {
  // Transform-based animations (GPU accelerated)
  'hover-lift': 'transition-transform duration-300 ease-out hover:-translate-y-1',
  'hover-scale': 'transition-transform duration-300 ease-out hover:scale-105',
  'hover-rotate': 'transition-transform duration-300 ease-out hover:rotate-3',
  
  // Shadow animations
  'hover-shadow': 'transition-shadow duration-300 ease-out hover:shadow-2xl',
  'hover-glow': 'transition-shadow duration-300 ease-out hover:shadow-purple-500/50',
  
  // Color transitions
  'hover-brightness': 'transition-all duration-300 ease-out hover:brightness-110',
  'hover-saturate': 'transition-all duration-300 ease-out hover:saturate-150',
  
  // Combined effects
  'card-hover': 'transition-all duration-300 ease-out hover:-translate-y-1 hover:shadow-2xl',
  'button-press': 'transition-all duration-150 ease-out active:scale-95',
  
  // Pulse animations
  'pulse-slow': 'animate-pulse-slow',
  'pulse-glow': 'animate-pulse-glow',
  
  // Float animations
  'float': 'animate-float',
  'float-delayed': 'animate-float animation-delay-1000',
  
  // Entrance animations
  'fade-in': 'animate-fade-in',
  'slide-up': 'animate-slide-up',
  'scale-in': 'animate-scale-in'
};

// Optimized animation hooks
export const useOptimizedAnimation = (animationKey, deps = []) => {
  const shouldReduce = shouldReduceMotion();
  
  if (shouldReduce) {
    return {
      initial: {},
      animate: {},
      exit: {},
      transition: { duration: 0 }
    };
  }
  
  return animations[animationKey] || {};
};

// Performance monitoring for animations
export const measureAnimationPerformance = (animationName) => {
  if (typeof window === 'undefined' || !window.performance) return;
  
  const startMark = `animation-${animationName}-start`;
  const endMark = `animation-${animationName}-end`;
  
  return {
    start: () => performance.mark(startMark),
    end: () => {
      performance.mark(endMark);
      performance.measure(animationName, startMark, endMark);
      
      const measure = performance.getEntriesByName(animationName)[0];
      if (measure && measure.duration > 16.67) { // More than 1 frame at 60fps
        console.warn(`Animation "${animationName}" took ${measure.duration.toFixed(2)}ms`);
      }
      
      // Cleanup
      performance.clearMarks(startMark);
      performance.clearMarks(endMark);
      performance.clearMeasures(animationName);
    }
  };
};

// Intersection Observer for triggering animations on scroll
export const useScrollAnimation = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef(null);
  
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );
    
    if (ref.current) {
      observer.observe(ref.current);
    }
    
    return () => observer.disconnect();
  }, [threshold]);
  
  return [ref, isVisible];
};

// Debounced animation trigger for performance
export const useDebouncedAnimation = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => clearTimeout(handler);
  }, [value, delay]);
  
  return debouncedValue;
};