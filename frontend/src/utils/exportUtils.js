// Export utilities for email sequences and templates

export const exportFormats = {
  CSV: 'csv',
  HTML: 'html',
  TXT: 'txt',
  JSON: 'json',
  MAILCHIMP: 'mailchimp',
  KLAVIYO: 'klaviyo',
  CONVERTKIT: 'convertkit'
}

export const exportSequenceToCSV = (sequence) => {
  const headers = ['Email Number', 'Day Delay', 'Subject Line', 'Email Body', 'Conversion Score']
  const rows = sequence.emails?.map((email, index) => [
    index + 1,
    email.dayDelay || index + 1,
    `"${email.subject?.replace(/"/g, '""') || ''}"`,
    `"${email.body?.replace(/"/g, '""') || ''}"`,
    email.conversionScore || 'N/A'
  ]) || []

  const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
  return csvContent
}

export const exportSequenceToHTML = (sequence) => {
  const emailsHTML = sequence.emails?.map((email, index) => `
    <div class="email-item" style="margin-bottom: 40px; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
      <div class="email-header" style="border-bottom: 1px solid #e5e7eb; padding-bottom: 15px; margin-bottom: 15px;">
        <h3 style="margin: 0; color: #1f2937; font-size: 18px;">Email ${index + 1} - Day ${email.dayDelay || index + 1}</h3>
        <span style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
          Score: ${email.conversionScore || 85}
        </span>
      </div>
      <div class="subject-line" style="margin-bottom: 15px;">
        <h4 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 600;">Subject Line:</h4>
        <p style="margin: 0; background: #f9fafb; padding: 12px; border-radius: 4px; font-family: monospace;">
          ${email.subject || ''}
        </p>
      </div>
      <div class="email-body">
        <h4 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 600;">Email Body:</h4>
        <div style="background: #f9fafb; padding: 16px; border-radius: 4px; white-space: pre-wrap; line-height: 1.6;">
          ${email.body || ''}
        </div>
      </div>
    </div>
  `).join('') || ''

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${sequence.title || 'Email Sequence'} - NeuroColony Export</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #3b82f6;
        }
        .sequence-info {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .export-info {
            font-size: 12px;
            color: #6b7280;
            margin-top: 40px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 style="margin: 0; color: #1f2937;">${sequence.title || 'Email Sequence'}</h1>
        <p style="margin: 10px 0 0 0; color: #6b7280;">Generated by NeuroColony</p>
    </div>
    
    <div class="sequence-info">
        <h2 style="margin: 0 0 15px 0; color: #374151; font-size: 16px;">Sequence Information</h2>
        <p><strong>Title:</strong> ${sequence.title || 'Untitled Sequence'}</p>
        <p><strong>Total Emails:</strong> ${sequence.emails?.length || 0}</p>
        <p><strong>Sequence Type:</strong> ${sequence.generationSettings?.primaryGoal || 'General'}</p>
        <p><strong>Target Audience:</strong> ${sequence.businessInfo?.targetAudience || 'Not specified'}</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
    </div>
    
    <div class="emails-container">
        ${emailsHTML}
    </div>
    
    <div class="export-info">
        <p>Exported from NeuroColony on ${new Date().toLocaleDateString()}</p>
        <p>Visit sequenceai.com for more AI-powered email sequences</p>
    </div>
</body>
</html>`
}

export const exportSequenceToTXT = (sequence) => {
  const content = [
    `EMAIL SEQUENCE: ${sequence.title || 'Untitled Sequence'}`,
    `Generated by NeuroColony on ${new Date().toLocaleDateString()}`,
    '',
    'SEQUENCE INFORMATION:',
    `- Total Emails: ${sequence.emails?.length || 0}`,
    `- Sequence Type: ${sequence.generationSettings?.primaryGoal || 'General'}`,
    `- Target Audience: ${sequence.businessInfo?.targetAudience || 'Not specified'}`,
    '',
    '=' .repeat(80),
    ''
  ]

  sequence.emails?.forEach((email, index) => {
    content.push(
      `EMAIL ${index + 1} - DAY ${email.dayDelay || index + 1}`,
      `-`.repeat(40),
      '',
      `SUBJECT: ${email.subject || ''}`,
      '',
      'BODY:',
      email.body || '',
      '',
      `CONVERSION SCORE: ${email.conversionScore || 'N/A'}`,
      '',
      '=' .repeat(80),
      ''
    )
  })

  content.push(
    'Generated by NeuroColony',
    'Visit sequenceai.com for more AI-powered email sequences'
  )

  return content.join('\n')
}

export const exportSequenceToJSON = (sequence) => {
  const exportData = {
    title: sequence.title,
    exportedAt: new Date().toISOString(),
    exportedBy: 'NeuroColony',
    sequenceInfo: {
      totalEmails: sequence.emails?.length || 0,
      sequenceType: sequence.generationSettings?.primaryGoal || 'General',
      targetAudience: sequence.businessInfo?.targetAudience || 'Not specified',
      tone: sequence.generationSettings?.tone || 'Professional',
      industry: sequence.businessInfo?.industry || 'General'
    },
    emails: sequence.emails?.map((email, index) => ({
      emailNumber: index + 1,
      dayDelay: email.dayDelay || index + 1,
      subject: email.subject || '',
      body: email.body || '',
      conversionScore: email.conversionScore || null,
      metadata: {
        generatedAt: new Date().toISOString(),
        framework: email.framework || 'AI-Generated'
      }
    })) || []
  }

  return JSON.stringify(exportData, null, 2)
}

// Email platform specific formats
export const exportToMailchimp = (sequence) => {
  // Mailchimp automation series format
  const campaigns = sequence.emails?.map((email, index) => ({
    type: 'automation',
    recipients: {
      list_id: 'YOUR_LIST_ID' // User would replace this
    },
    settings: {
      subject_line: email.subject || '',
      title: `${sequence.title} - Email ${index + 1}`,
      from_name: 'YOUR_COMPANY', // User would replace this
      reply_to: '<EMAIL>' // User would replace this
    },
    content: {
      html: `<div style="font-family: Arial, sans-serif; line-height: 1.6;">${email.body?.replace(/\n/g, '<br>') || ''}</div>`,
      text: email.body || ''
    },
    delay: {
      amount: email.dayDelay || index + 1,
      type: 'day'
    }
  })) || []

  return JSON.stringify({
    automation_name: sequence.title || 'Untitled Sequence',
    description: `Email sequence generated by NeuroColony`,
    campaigns: campaigns,
    notes: 'Import instructions: Replace YOUR_LIST_ID, YOUR_COMPANY, and <EMAIL> with your actual values'
  }, null, 2)
}

export const exportToKlaviyo = (sequence) => {
  // Klaviyo flow format
  const messages = sequence.emails?.map((email, index) => ({
    name: `${sequence.title} - Email ${index + 1}`,
    subject: email.subject || '',
    content: email.body || '',
    delay_minutes: (email.dayDelay || index + 1) * 24 * 60, // Convert days to minutes
    channel: 'email'
  })) || []

  return JSON.stringify({
    flow_name: sequence.title || 'Untitled Sequence',
    description: `Email sequence generated by NeuroColony`,
    trigger: 'custom', // User would set appropriate trigger
    messages: messages,
    import_note: 'Configure trigger and list settings in Klaviyo after import'
  }, null, 2)
}

export const downloadFile = (content, filename, mimeType = 'text/plain') => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export const exportSequence = (sequence, format) => {
  const timestamp = new Date().toISOString().split('T')[0]
  const baseFilename = `${sequence.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'email_sequence'}_${timestamp}`
  
  let content, filename, mimeType

  switch (format) {
    case exportFormats.CSV:
      content = exportSequenceToCSV(sequence)
      filename = `${baseFilename}.csv`
      mimeType = 'text/csv'
      break
    
    case exportFormats.HTML:
      content = exportSequenceToHTML(sequence)
      filename = `${baseFilename}.html`
      mimeType = 'text/html'
      break
    
    case exportFormats.TXT:
      content = exportSequenceToTXT(sequence)
      filename = `${baseFilename}.txt`
      mimeType = 'text/plain'
      break
    
    case exportFormats.JSON:
      content = exportSequenceToJSON(sequence)
      filename = `${baseFilename}.json`
      mimeType = 'application/json'
      break
    
    case exportFormats.MAILCHIMP:
      content = exportToMailchimp(sequence)
      filename = `${baseFilename}_mailchimp.json`
      mimeType = 'application/json'
      break
    
    case exportFormats.KLAVIYO:
      content = exportToKlaviyo(sequence)
      filename = `${baseFilename}_klaviyo.json`
      mimeType = 'application/json'
      break
    
    default:
      throw new Error(`Unsupported export format: ${format}`)
  }

  downloadFile(content, filename, mimeType)
  return { filename, size: content.length }
}