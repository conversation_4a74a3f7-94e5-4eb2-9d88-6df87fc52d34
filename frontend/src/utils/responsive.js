/**
 * Responsive design utilities for NeuroColony
 * Ensures consistent mobile layouts and touch targets
 */

// Touch target minimum size (WCAG requirement)
export const TOUCH_TARGET_SIZE = 44

// Responsive breakpoints
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

// Check if device has touch capability
export const isTouchDevice = () => {
  return 'ontouchstart' in window || 
         navigator.maxTouchPoints > 0 ||
         navigator.msMaxTouchPoints > 0
}

// Get current breakpoint
export const getCurrentBreakpoint = () => {
  const width = window.innerWidth
  
  if (width < breakpoints.sm) return 'xs'
  if (width < breakpoints.md) return 'sm'
  if (width < breakpoints.lg) return 'md'
  if (width < breakpoints.xl) return 'lg'
  if (width < breakpoints['2xl']) return 'xl'
  return '2xl'
}

// Check if current viewport is mobile
export const isMobile = () => {
  return window.innerWidth < breakpoints.md
}

// Check if current viewport is tablet
export const isTablet = () => {
  const width = window.innerWidth
  return width >= breakpoints.md && width < breakpoints.lg
}

// Check if current viewport is desktop
export const isDesktop = () => {
  return window.innerWidth >= breakpoints.lg
}

// Get responsive padding based on viewport
export const getResponsivePadding = () => {
  const breakpoint = getCurrentBreakpoint()
  
  switch (breakpoint) {
    case 'xs':
    case 'sm':
      return 'px-4 py-3'
    case 'md':
      return 'px-6 py-4'
    case 'lg':
    case 'xl':
    case '2xl':
    default:
      return 'px-8 py-5'
  }
}

// Get responsive text size
export const getResponsiveTextSize = (baseSize = 'base') => {
  const breakpoint = getCurrentBreakpoint()
  
  const sizeMap = {
    xs: { sm: 'text-xs', base: 'text-sm', lg: 'text-base', xl: 'text-lg' },
    sm: { sm: 'text-sm', base: 'text-base', lg: 'text-lg', xl: 'text-xl' },
    md: { sm: 'text-sm', base: 'text-base', lg: 'text-lg', xl: 'text-xl' },
    lg: { sm: 'text-sm', base: 'text-base', lg: 'text-xl', xl: 'text-2xl' },
    xl: { sm: 'text-base', base: 'text-lg', lg: 'text-xl', xl: 'text-2xl' },
    '2xl': { sm: 'text-base', base: 'text-lg', lg: 'text-2xl', xl: 'text-3xl' }
  }
  
  return sizeMap[breakpoint]?.[baseSize] || sizeMap.md[baseSize]
}

// Ensure touch target size
export const ensureTouchTarget = (className = '') => {
  return `${className} min-h-[44px] min-w-[44px]`
}

// Get responsive grid columns
export const getResponsiveGridCols = (desktop = 3, tablet = 2, mobile = 1) => {
  return `grid-cols-${mobile} md:grid-cols-${tablet} lg:grid-cols-${desktop}`
}

// Viewport height fix for mobile browsers
export const setViewportHeight = () => {
  // First we get the viewport height and we multiple it by 1% to get a value for a vh unit
  const vh = window.innerHeight * 0.01
  // Then we set the value in the --vh custom property to the root of the document
  document.documentElement.style.setProperty('--vh', `${vh}px`)
}

// Add viewport resize listener
export const addViewportListener = () => {
  window.addEventListener('resize', setViewportHeight)
  window.addEventListener('orientationchange', setViewportHeight)
  setViewportHeight()
}

// Remove viewport resize listener
export const removeViewportListener = () => {
  window.removeEventListener('resize', setViewportHeight)
  window.removeEventListener('orientationchange', setViewportHeight)
}

// Responsive container classes
export const getResponsiveContainer = () => {
  return 'container mx-auto px-4 sm:px-6 lg:px-8'
}

// Safe area insets for mobile devices (iPhone X and later)
export const getSafeAreaPadding = () => {
  return 'pb-safe pt-safe px-safe'
}

// Export all utilities as default
export default {
  TOUCH_TARGET_SIZE,
  breakpoints,
  isTouchDevice,
  getCurrentBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  getResponsivePadding,
  getResponsiveTextSize,
  ensureTouchTarget,
  getResponsiveGridCols,
  setViewportHeight,
  addViewportListener,
  removeViewportListener,
  getResponsiveContainer,
  getSafeAreaPadding
}