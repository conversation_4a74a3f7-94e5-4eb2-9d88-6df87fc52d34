// NeuroColony Design System - Color Palette
// Inspired by neural networks, ant colonies, and bee hives

export const colors = {
  // Primary - Neural Blues
  neural: {
    50: '#e6f2ff',
    100: '#b3d9ff',
    200: '#80bfff',
    300: '#4da6ff',
    400: '#1a8cff',
    500: '#0073e6', // Primary
    600: '#005bb3',
    700: '#004280',
    800: '#002a4d',
    900: '#00121a',
  },
  
  // Secondary - Organic Greens
  organic: {
    50: '#e8f5e9',
    100: '#c8e6c9',
    200: '#a5d6a7',
    300: '#81c784',
    400: '#66bb6a',
    500: '#4caf50', // Secondary
    600: '#43a047',
    700: '#388e3c',
    800: '#2e7d32',
    900: '#1b5e20',
  },
  
  // Accent - Golden Honey
  honey: {
    50: '#fff8e1',
    100: '#ffecb3',
    200: '#ffe082',
    300: '#ffd54f',
    400: '#ffca28',
    500: '#ffc107', // Accent
    600: '#ffb300',
    700: '#ffa000',
    800: '#ff8f00',
    900: '#ff6f00',
  },
  
  // Colony - Deep Purple (Queen/Royal)
  colony: {
    50: '#f3e5f5',
    100: '#e1bee7',
    200: '#ce93d8',
    300: '#ba68c8',
    400: '#ab47bc',
    500: '#9c27b0', // Colony Primary
    600: '#8e24aa',
    700: '#7b1fa2',
    800: '#6a1b9a',
    900: '#4a148c',
  },
  
  // Network - Electric Cyan
  network: {
    50: '#e0f7fa',
    100: '#b2ebf2',
    200: '#80deea',
    300: '#4dd0e1',
    400: '#26c6da',
    500: '#00bcd4', // Network Primary
    600: '#00acc1',
    700: '#0097a7',
    800: '#00838f',
    900: '#006064',
  },
  
  // System Colors
  system: {
    background: '#0a0f1c', // Deep space background
    surface: '#141b2d', // Elevated surface
    card: '#1a2332', // Card background
    border: '#2a3441', // Subtle borders
    
    text: {
      primary: '#ffffff',
      secondary: '#b8c0cc',
      muted: '#8892a0',
      inverse: '#0a0f1c',
    },
    
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#00bcd4',
    
    // Agent Status Colors
    agent: {
      active: '#4caf50',
      idle: '#ffc107',
      processing: '#00bcd4',
      error: '#f44336',
      dormant: '#9e9e9e',
    },
    
    // Colony Health Colors
    colony: {
      healthy: '#4caf50',
      moderate: '#ff9800',
      critical: '#f44336',
      expanding: '#00bcd4',
      stable: '#9c27b0',
    },
  },
  
  // Gradients
  gradients: {
    neural: 'linear-gradient(135deg, #0073e6 0%, #00bcd4 100%)',
    organic: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
    honey: 'linear-gradient(135deg, #ffc107 0%, #ff9800 100%)',
    colony: 'linear-gradient(135deg, #9c27b0 0%, #ab47bc 100%)',
    network: 'linear-gradient(135deg, #00bcd4 0%, #26c6da 100%)',
    
    // Special Gradients
    queen: 'linear-gradient(135deg, #9c27b0 0%, #ffc107 100%)',
    hive: 'linear-gradient(135deg, #ff6f00 0%, #ffc107 50%, #ffecb3 100%)',
    neuralNetwork: 'linear-gradient(135deg, #0073e6 0%, #00bcd4 50%, #4dd0e1 100%)',
    
    // Background Gradients
    dark: 'linear-gradient(180deg, #0a0f1c 0%, #141b2d 100%)',
    subtle: 'linear-gradient(180deg, #141b2d 0%, #1a2332 100%)',
  },
  
  // Semantic Colors for Colony Elements
  semantic: {
    queen: '#9c27b0',
    worker: '#0073e6',
    scout: '#00bcd4',
    soldier: '#f44336',
    drone: '#ffc107',
    
    // Task Types
    analyze: '#00bcd4',
    create: '#4caf50',
    optimize: '#ffc107',
    monitor: '#0073e6',
    defend: '#f44336',
  },
};

// Opacity variations
export const opacity = {
  hover: 0.08,
  focus: 0.12,
  selected: 0.16,
  disabled: 0.38,
  backdrop: 0.5,
  overlay: 0.7,
};

// Shadow definitions
export const shadows = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  md: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
  
  // Glow effects for active agents
  glow: {
    neural: '0 0 20px rgba(0, 115, 230, 0.5)',
    organic: '0 0 20px rgba(76, 175, 80, 0.5)',
    honey: '0 0 20px rgba(255, 193, 7, 0.5)',
    colony: '0 0 20px rgba(156, 39, 176, 0.5)',
    network: '0 0 20px rgba(0, 188, 212, 0.5)',
  },
};