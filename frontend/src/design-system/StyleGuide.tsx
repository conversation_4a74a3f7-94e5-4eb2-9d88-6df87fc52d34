import React, { useState } from 'react';
import { tokens } from './tokens';
import './theme/theme.scss';

/**
 * NeuroColony Style Guide
 * Interactive demonstration of all design system elements
 */
export const StyleGuide: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'high-contrast'>('dark');
  const [activeSection, setActiveSection] = useState('colors');

  const sections = [
    'colors',
    'typography',
    'spacing',
    'shadows',
    'components',
    'patterns',
    'animations',
    'layouts',
  ];

  return (
    <div data-theme={theme} className="min-h-screen bg-[var(--color-bg-primary)]">
      {/* Theme Switcher */}
      <div className="fixed top-4 right-4 z-50 flex gap-2">
        <button
          onClick={() => setTheme('light')}
          className={`px-4 py-2 rounded-lg transition-all ${
            theme === 'light'
              ? 'bg-[var(--color-primary)] text-white'
              : 'bg-[var(--color-surface)] text-[var(--color-text-primary)]'
          }`}
        >
          Light
        </button>
        <button
          onClick={() => setTheme('dark')}
          className={`px-4 py-2 rounded-lg transition-all ${
            theme === 'dark'
              ? 'bg-[var(--color-primary)] text-white'
              : 'bg-[var(--color-surface)] text-[var(--color-text-primary)]'
          }`}
        >
          Dark
        </button>
        <button
          onClick={() => setTheme('high-contrast')}
          className={`px-4 py-2 rounded-lg transition-all ${
            theme === 'high-contrast'
              ? 'bg-[var(--color-primary)] text-white'
              : 'bg-[var(--color-surface)] text-[var(--color-text-primary)]'
          }`}
        >
          High Contrast
        </button>
      </div>

      {/* Header */}
      <header className="border-b border-[var(--color-border-default)] bg-[var(--color-surface)]">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <h1 className="text-[var(--text-5xl)] font-secondary font-bold text-[var(--color-text-primary)]">
            NeuroColony Design System
          </h1>
          <p className="text-[var(--text-lg)] text-[var(--color-text-secondary)] mt-2">
            Enterprise-grade design system for AI-powered marketing automation
          </p>
        </div>

        {/* Navigation */}
        <nav className="max-w-7xl mx-auto px-6">
          <div className="flex gap-8 overflow-x-auto">
            {sections.map((section) => (
              <button
                key={section}
                onClick={() => setActiveSection(section)}
                className={`py-4 px-1 border-b-2 transition-colors capitalize ${
                  activeSection === section
                    ? 'border-[var(--color-primary)] text-[var(--color-primary)]'
                    : 'border-transparent text-[var(--color-text-secondary)] hover:text-[var(--color-text-primary)]'
                }`}
              >
                {section}
              </button>
            ))}
          </div>
        </nav>
      </header>

      {/* Content */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        {/* Colors Section */}
        {activeSection === 'colors' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Color Palette
              </h2>
              
              {/* Neural Purple */}
              <div className="mb-8">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Neural Purple (Primary)</h3>
                <div className="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-11 gap-2">
                  {Object.entries(tokens.colors.neural).map(([shade, color]) => (
                    <div key={shade} className="text-center">
                      <div
                        className="h-24 rounded-lg shadow-md mb-2"
                        style={{ backgroundColor: color }}
                      />
                      <p className="text-xs font-mono">{shade}</p>
                      <p className="text-xs text-[var(--color-text-tertiary)]">{color}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Colony Gold */}
              <div className="mb-8">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Colony Gold (Accent)</h3>
                <div className="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-2">
                  {Object.entries(tokens.colors.colony).map(([shade, color]) => (
                    <div key={shade} className="text-center">
                      <div
                        className="h-24 rounded-lg shadow-md mb-2"
                        style={{ backgroundColor: color }}
                      />
                      <p className="text-xs font-mono">{shade}</p>
                      <p className="text-xs text-[var(--color-text-tertiary)]">{color}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Intelligence Blue */}
              <div className="mb-8">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Intelligence Blue (Secondary)</h3>
                <div className="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-2">
                  {Object.entries(tokens.colors.intel).map(([shade, color]) => (
                    <div key={shade} className="text-center">
                      <div
                        className="h-24 rounded-lg shadow-md mb-2"
                        style={{ backgroundColor: color }}
                      />
                      <p className="text-xs font-mono">{shade}</p>
                      <p className="text-xs text-[var(--color-text-tertiary)]">{color}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Semantic Colors */}
              <div>
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Semantic Colors</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-[var(--color-text-secondary)]">Success</h4>
                    <div className="space-y-1">
                      <div className="h-12 rounded bg-[#86EFAC] px-3 flex items-center">
                        <span className="text-xs font-mono">Light</span>
                      </div>
                      <div className="h-12 rounded bg-[#22C55E] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Base</span>
                      </div>
                      <div className="h-12 rounded bg-[#15803D] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Dark</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-[var(--color-text-secondary)]">Warning</h4>
                    <div className="space-y-1">
                      <div className="h-12 rounded bg-[#FDE047] px-3 flex items-center">
                        <span className="text-xs font-mono">Light</span>
                      </div>
                      <div className="h-12 rounded bg-[#EAB308] px-3 flex items-center">
                        <span className="text-xs font-mono">Base</span>
                      </div>
                      <div className="h-12 rounded bg-[#A16207] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Dark</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-[var(--color-text-secondary)]">Error</h4>
                    <div className="space-y-1">
                      <div className="h-12 rounded bg-[#FCA5A5] px-3 flex items-center">
                        <span className="text-xs font-mono">Light</span>
                      </div>
                      <div className="h-12 rounded bg-[#EF4444] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Base</span>
                      </div>
                      <div className="h-12 rounded bg-[#B91C1C] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Dark</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-[var(--color-text-secondary)]">Info</h4>
                    <div className="space-y-1">
                      <div className="h-12 rounded bg-[#93C5FD] px-3 flex items-center">
                        <span className="text-xs font-mono">Light</span>
                      </div>
                      <div className="h-12 rounded bg-[#3B82F6] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Base</span>
                      </div>
                      <div className="h-12 rounded bg-[#1E40AF] px-3 flex items-center text-white">
                        <span className="text-xs font-mono">Dark</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Gradients */}
            <div>
              <h3 className="text-[var(--text-xl)] font-medium mb-4">Signature Gradients</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="h-32 rounded-lg gradient-neural flex items-center justify-center text-white font-medium">
                  Neural Gradient
                </div>
                <div className="h-32 rounded-lg gradient-colony flex items-center justify-center text-white font-medium">
                  Colony Gradient
                </div>
                <div className="h-32 rounded-lg gradient-intel flex items-center justify-center text-white font-medium">
                  Intelligence Gradient
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Typography Section */}
        {activeSection === 'typography' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Typography Scale
              </h2>

              {/* Type Scale */}
              <div className="space-y-4 mb-12">
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">9xl</span>
                  <h1 className="text-[var(--text-9xl)] font-secondary font-bold">Headlines</h1>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">8xl</span>
                  <h1 className="text-[var(--text-8xl)] font-secondary font-bold">Headlines</h1>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">7xl</span>
                  <h1 className="text-[var(--text-7xl)] font-secondary font-bold">Headlines</h1>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">6xl</span>
                  <h1 className="text-[var(--text-6xl)] font-secondary font-bold">Headlines</h1>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">5xl</span>
                  <h1 className="text-[var(--text-5xl)] font-secondary font-bold">Headlines</h1>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">4xl</span>
                  <h2 className="text-[var(--text-4xl)] font-secondary font-semibold">Subheadings</h2>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">3xl</span>
                  <h3 className="text-[var(--text-3xl)] font-secondary font-semibold">Subheadings</h3>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">2xl</span>
                  <h4 className="text-[var(--text-2xl)] font-secondary font-medium">Section titles</h4>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">xl</span>
                  <h5 className="text-[var(--text-xl)] font-medium">Large text</h5>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">lg</span>
                  <p className="text-[var(--text-lg)]">Body text large</p>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">base</span>
                  <p className="text-[var(--text-base)]">Body text regular</p>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">sm</span>
                  <p className="text-[var(--text-sm)]">Small text</p>
                </div>
                <div className="flex items-baseline gap-4">
                  <span className="text-xs font-mono text-[var(--color-text-tertiary)] w-20">xs</span>
                  <p className="text-[var(--text-xs)]">Extra small text</p>
                </div>
              </div>

              {/* Font Families */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-primary font-semibold mb-2">Inter (Primary)</h4>
                  <p className="font-primary text-[var(--color-text-secondary)]">
                    Used for body text, UI elements, and general content. Clean and highly legible.
                  </p>
                </div>
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-secondary font-semibold mb-2">Space Grotesk (Secondary)</h4>
                  <p className="font-secondary text-[var(--color-text-secondary)]">
                    Used for headings and display text. Modern geometric sans-serif.
                  </p>
                </div>
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-mono font-semibold mb-2">JetBrains Mono (Code)</h4>
                  <p className="font-mono text-[var(--color-text-secondary)]">
                    Used for code blocks, technical content, and data display.
                  </p>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Spacing Section */}
        {activeSection === 'spacing' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Spacing System
              </h2>
              <p className="text-[var(--text-lg)] text-[var(--color-text-secondary)] mb-8">
                Based on a 4px grid system for consistent alignment and spacing throughout the interface.
              </p>

              <div className="space-y-3">
                {Object.entries(tokens.spacing).slice(0, 20).map(([name, value]) => (
                  <div key={name} className="flex items-center gap-4">
                    <span className="text-sm font-mono text-[var(--color-text-tertiary)] w-16">
                      {name}
                    </span>
                    <span className="text-sm text-[var(--color-text-secondary)] w-20">{value}</span>
                    <div
                      className="h-8 bg-[var(--color-primary)] rounded"
                      style={{ width: value }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Shadows Section */}
        {activeSection === 'shadows' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Shadow System
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="space-y-4">
                  <h3 className="text-[var(--text-xl)] font-medium">Elevation Shadows</h3>
                  {Object.entries(tokens.shadows).filter(([key]) => !key.includes('glow')).map(([name, shadow]) => (
                    typeof shadow === 'string' && (
                      <div key={name} className="p-6 bg-[var(--color-surface)] rounded-lg" style={{ boxShadow: shadow }}>
                        <p className="font-mono text-sm mb-1">shadow-{name}</p>
                        <p className="text-xs text-[var(--color-text-tertiary)]">{shadow}</p>
                      </div>
                    )
                  ))}
                </div>

                <div className="space-y-4">
                  <h3 className="text-[var(--text-xl)] font-medium">Glow Effects</h3>
                  {theme === 'dark' && (
                    <>
                      <div className="p-6 bg-[var(--color-surface)] rounded-lg" style={{ boxShadow: tokens.shadows.glow.neural }}>
                        <p className="font-mono text-sm mb-1">glow-neural</p>
                        <p className="text-xs text-[var(--color-text-tertiary)]">Neural purple glow</p>
                      </div>
                      <div className="p-6 bg-[var(--color-surface)] rounded-lg" style={{ boxShadow: tokens.shadows.glow.colony }}>
                        <p className="font-mono text-sm mb-1">glow-colony</p>
                        <p className="text-xs text-[var(--color-text-tertiary)]">Colony gold glow</p>
                      </div>
                      <div className="p-6 bg-[var(--color-surface)] rounded-lg" style={{ boxShadow: tokens.shadows.glow.intel }}>
                        <p className="font-mono text-sm mb-1">glow-intel</p>
                        <p className="text-xs text-[var(--color-text-tertiary)]">Intelligence blue glow</p>
                      </div>
                      <div className="p-6 bg-[var(--color-surface)] rounded-lg" style={{ boxShadow: tokens.shadows.glow.neuralLarge }}>
                        <p className="font-mono text-sm mb-1">glow-neural-large</p>
                        <p className="text-xs text-[var(--color-text-tertiary)]">Large neural glow</p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Components Section */}
        {activeSection === 'components' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Component Examples
              </h2>

              {/* Buttons */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Buttons</h3>
                <div className="flex flex-wrap gap-4">
                  <button className="px-6 py-3 bg-[var(--color-primary)] text-white rounded-lg hover:bg-[var(--color-primary-hover)] transition-colors">
                    Primary Button
                  </button>
                  <button className="px-6 py-3 bg-[var(--color-secondary)] text-white rounded-lg hover:bg-[var(--color-secondary-hover)] transition-colors">
                    Secondary Button
                  </button>
                  <button className="px-6 py-3 bg-[var(--color-accent)] text-black rounded-lg hover:bg-[var(--color-accent-hover)] transition-colors">
                    Accent Button
                  </button>
                  <button className="px-6 py-3 border border-[var(--color-border-medium)] text-[var(--color-text-primary)] rounded-lg hover:bg-[var(--color-surface-hover)] transition-colors">
                    Outline Button
                  </button>
                  <button className="px-6 py-3 text-[var(--color-primary)] hover:bg-[var(--color-surface-hover)] rounded-lg transition-colors">
                    Ghost Button
                  </button>
                </div>
              </div>

              {/* Cards */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Cards</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] shadow-[var(--shadow-sm)]">
                    <h4 className="font-semibold mb-2">Basic Card</h4>
                    <p className="text-[var(--color-text-secondary)]">
                      Simple card with border and subtle shadow.
                    </p>
                  </div>
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg shadow-[var(--shadow-lg)]">
                    <h4 className="font-semibold mb-2">Elevated Card</h4>
                    <p className="text-[var(--color-text-secondary)]">
                      Card with larger shadow for emphasis.
                    </p>
                  </div>
                  <div className="p-6 gradient-neural text-white rounded-lg shadow-[var(--shadow-lg)]">
                    <h4 className="font-semibold mb-2">Gradient Card</h4>
                    <p className="text-white/90">
                      Premium card with gradient background.
                    </p>
                  </div>
                </div>
              </div>

              {/* Form Elements */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Form Elements</h3>
                <div className="max-w-md space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Text Input</label>
                    <input
                      type="text"
                      placeholder="Enter your email"
                      className="w-full px-4 py-2 bg-[var(--color-surface)] border border-[var(--color-border-default)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Select</label>
                    <select className="w-full px-4 py-2 bg-[var(--color-surface)] border border-[var(--color-border-default)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent">
                      <option>Choose an option</option>
                      <option>Option 1</option>
                      <option>Option 2</option>
                    </select>
                  </div>
                  <div>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="w-4 h-4 rounded border-[var(--color-border-default)]" />
                      <span className="text-sm">I agree to the terms</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Badges */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Badges</h3>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-[var(--color-primary)] text-white text-sm rounded-full">
                    Primary
                  </span>
                  <span className="px-3 py-1 bg-[var(--color-success)] text-white text-sm rounded-full">
                    Success
                  </span>
                  <span className="px-3 py-1 bg-[var(--color-warning)] text-black text-sm rounded-full">
                    Warning
                  </span>
                  <span className="px-3 py-1 bg-[var(--color-error)] text-white text-sm rounded-full">
                    Error
                  </span>
                  <span className="px-3 py-1 bg-[var(--color-surface)] border border-[var(--color-border-default)] text-sm rounded-full">
                    Outline
                  </span>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Patterns Section */}
        {activeSection === 'patterns' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Design Patterns
              </h2>

              {/* Hero Pattern */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Hero Section</h3>
                <div className="gradient-neural rounded-lg p-12 text-center text-white">
                  <h1 className="text-[var(--text-6xl)] font-secondary font-bold mb-4">
                    Transform Your Marketing with AI
                  </h1>
                  <p className="text-[var(--text-xl)] mb-8 opacity-90 max-w-2xl mx-auto">
                    NeuroColony's intelligent agents work together to optimize your entire marketing funnel
                  </p>
                  <div className="flex gap-4 justify-center">
                    <button className="px-8 py-4 bg-white text-[var(--color-primary)] rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                      Start Free Trial
                    </button>
                    <button className="px-8 py-4 bg-white/20 text-white rounded-lg font-semibold hover:bg-white/30 transition-colors backdrop-blur">
                      Watch Demo
                    </button>
                  </div>
                </div>
              </div>

              {/* Feature Grid */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Feature Grid</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] hover:shadow-[var(--shadow-lg)] transition-shadow">
                    <div className="w-12 h-12 bg-[var(--color-primary)] rounded-lg mb-4 flex items-center justify-center text-white">
                      🤖
                    </div>
                    <h4 className="font-semibold mb-2">AI Agents</h4>
                    <p className="text-[var(--color-text-secondary)]">
                      Intelligent agents that work 24/7 to optimize your campaigns
                    </p>
                  </div>
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] hover:shadow-[var(--shadow-lg)] transition-shadow">
                    <div className="w-12 h-12 bg-[var(--color-secondary)] rounded-lg mb-4 flex items-center justify-center text-white">
                      📊
                    </div>
                    <h4 className="font-semibold mb-2">Analytics</h4>
                    <p className="text-[var(--color-text-secondary)]">
                      Real-time insights into campaign performance and ROI
                    </p>
                  </div>
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] hover:shadow-[var(--shadow-lg)] transition-shadow">
                    <div className="w-12 h-12 bg-[var(--color-accent)] rounded-lg mb-4 flex items-center justify-center text-black">
                      🚀
                    </div>
                    <h4 className="font-semibold mb-2">Automation</h4>
                    <p className="text-[var(--color-text-secondary)]">
                      Automate repetitive tasks and focus on strategy
                    </p>
                  </div>
                </div>
              </div>

              {/* Pricing Table */}
              <div>
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Pricing Table</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                    <h4 className="font-semibold text-[var(--text-2xl)] mb-2">Starter</h4>
                    <p className="text-[var(--color-text-secondary)] mb-4">Perfect for small teams</p>
                    <div className="mb-6">
                      <span className="text-[var(--text-4xl)] font-bold">$29</span>
                      <span className="text-[var(--color-text-secondary)]">/month</span>
                    </div>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>10 AI Agents</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>75 Sequences/month</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>Basic Analytics</span>
                      </li>
                    </ul>
                    <button className="w-full px-4 py-2 border border-[var(--color-border-medium)] rounded-lg hover:bg-[var(--color-surface-hover)] transition-colors">
                      Get Started
                    </button>
                  </div>

                  <div className="p-6 gradient-neural text-white rounded-lg shadow-[var(--shadow-xl)] relative">
                    <div className="absolute -top-3 right-6 px-3 py-1 bg-[var(--color-accent)] text-black text-sm font-medium rounded-full">
                      Most Popular
                    </div>
                    <h4 className="font-semibold text-[var(--text-2xl)] mb-2">Professional</h4>
                    <p className="opacity-90 mb-4">For growing businesses</p>
                    <div className="mb-6">
                      <span className="text-[var(--text-4xl)] font-bold">$99</span>
                      <span className="opacity-90">/month</span>
                    </div>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center gap-2">
                        <span>✓</span>
                        <span>50 AI Agents</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span>✓</span>
                        <span>200 Sequences/month</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span>✓</span>
                        <span>Advanced Analytics</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span>✓</span>
                        <span>Priority Support</span>
                      </li>
                    </ul>
                    <button className="w-full px-4 py-2 bg-white text-[var(--color-primary)] rounded-lg hover:bg-gray-100 transition-colors font-medium">
                      Get Started
                    </button>
                  </div>

                  <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                    <h4 className="font-semibold text-[var(--text-2xl)] mb-2">Enterprise</h4>
                    <p className="text-[var(--color-text-secondary)] mb-4">For large organizations</p>
                    <div className="mb-6">
                      <span className="text-[var(--text-4xl)] font-bold">Custom</span>
                    </div>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>Unlimited Agents</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>Unlimited Sequences</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>Custom Integrations</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-[var(--color-success)]">✓</span>
                        <span>Dedicated Support</span>
                      </li>
                    </ul>
                    <button className="w-full px-4 py-2 bg-[var(--color-primary)] text-white rounded-lg hover:bg-[var(--color-primary-hover)] transition-colors">
                      Contact Sales
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Animations Section */}
        {activeSection === 'animations' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Animations & Effects
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Pulse Animation */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Neural Pulse</h4>
                  <div className="w-20 h-20 bg-[var(--color-primary)] rounded-full mx-auto animate-pulse-neural" />
                </div>

                {/* Gradient Flow */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Gradient Flow</h4>
                  <div className="h-20 gradient-neural rounded-lg animate-flow-gradient" />
                </div>

                {/* Colony Movement */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Colony Movement</h4>
                  <div className="w-20 h-20 bg-[var(--color-accent)] rounded-lg mx-auto animate-colony-move" />
                </div>

                {/* Loading States */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Loading Dots</h4>
                  <div className="flex gap-2 justify-center">
                    <div className="w-3 h-3 bg-[var(--color-primary)] rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <div className="w-3 h-3 bg-[var(--color-primary)] rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <div className="w-3 h-3 bg-[var(--color-primary)] rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                </div>

                {/* Skeleton Loading */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Skeleton</h4>
                  <div className="space-y-3">
                    <div className="h-4 bg-[var(--color-bg-tertiary)] rounded animate-pulse" />
                    <div className="h-4 bg-[var(--color-bg-tertiary)] rounded animate-pulse w-3/4" />
                    <div className="h-4 bg-[var(--color-bg-tertiary)] rounded animate-pulse w-1/2" />
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="p-6 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)]">
                  <h4 className="font-semibold mb-4">Progress</h4>
                  <div className="h-2 bg-[var(--color-bg-tertiary)] rounded-full overflow-hidden">
                    <div className="h-full gradient-neural animate-flow-gradient" style={{ width: '60%' }} />
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Layouts Section */}
        {activeSection === 'layouts' && (
          <section className="space-y-12">
            <div>
              <h2 className="text-[var(--text-3xl)] font-secondary font-semibold mb-6">
                Layout Examples
              </h2>

              {/* Dashboard Layout */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Dashboard Layout</h3>
                <div className="bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] overflow-hidden">
                  <div className="flex h-96">
                    {/* Sidebar */}
                    <div className="w-64 bg-[var(--color-bg-secondary)] border-r border-[var(--color-border-default)] p-4">
                      <div className="space-y-2">
                        <div className="h-8 bg-[var(--color-primary)] rounded opacity-20" />
                        <div className="h-8 bg-[var(--color-bg-tertiary)] rounded" />
                        <div className="h-8 bg-[var(--color-bg-tertiary)] rounded" />
                        <div className="h-8 bg-[var(--color-bg-tertiary)] rounded" />
                      </div>
                    </div>
                    {/* Main Content */}
                    <div className="flex-1 p-6">
                      <div className="h-8 bg-[var(--color-bg-tertiary)] rounded w-1/3 mb-6" />
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <div className="h-24 bg-[var(--color-bg-tertiary)] rounded" />
                        <div className="h-24 bg-[var(--color-bg-tertiary)] rounded" />
                        <div className="h-24 bg-[var(--color-bg-tertiary)] rounded" />
                      </div>
                      <div className="h-40 bg-[var(--color-bg-tertiary)] rounded" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Card Grid Layout */}
              <div className="mb-12">
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Card Grid</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                    <div key={i} className="aspect-square bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] flex items-center justify-center">
                      <span className="text-[var(--color-text-tertiary)]">Card {i}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Split Layout */}
              <div>
                <h3 className="text-[var(--text-xl)] font-medium mb-4">Split Layout</h3>
                <div className="bg-[var(--color-surface)] rounded-lg border border-[var(--color-border-default)] overflow-hidden">
                  <div className="grid grid-cols-1 md:grid-cols-2 h-80">
                    <div className="p-8 flex items-center justify-center border-r border-[var(--color-border-default)]">
                      <div className="text-center">
                        <h4 className="text-[var(--text-2xl)] font-semibold mb-2">Content Area</h4>
                        <p className="text-[var(--color-text-secondary)]">Main content goes here</p>
                      </div>
                    </div>
                    <div className="p-8 bg-[var(--color-bg-secondary)] flex items-center justify-center">
                      <div className="text-center">
                        <h4 className="text-[var(--text-2xl)] font-semibold mb-2">Visual Area</h4>
                        <p className="text-[var(--color-text-secondary)]">Images or graphics</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}
      </main>
    </div>
  );
};

export default StyleGuide;