/**
 * NeuroColony Design Tokens
 * Single source of truth for all design values
 */

// Color Tokens
export const colors = {
  // Neural Purple (Primary)
  neural: {
    50: '#F5F3FF',
    100: '#EDE9FE',
    200: '#DDD6FE',
    300: '#C4B5FD',
    400: '#A78BFA',
    500: '#8B5CF6',
    600: '#7C3AED',
    700: '#6D28D9',
    800: '#5B21B6',
    900: '#4C1D95',
    950: '#2D1065',
  },
  
  // Colony Gold (Accent)
  colony: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
  },
  
  // Intelligence Blue (Secondary)
  intel: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
  },
  
  // Semantic Colors
  success: {
    light: '#86EFAC',
    base: '#22C55E',
    dark: '#15803D',
  },
  
  warning: {
    light: '#FDE047',
    base: '#EAB308',
    dark: '#A16207',
  },
  
  error: {
    light: '#FCA5A5',
    base: '#EF4444',
    dark: '#B91C1C',
  },
  
  info: {
    light: '#93C5FD',
    base: '#3B82F6',
    dark: '#1E40AF',
  },
  
  // Neutral Colors
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0A0A0A',
  },
  
  // Special Effects
  effects: {
    neuralGlow: 'rgba(139, 92, 246, 0.5)',
    colonyGlow: 'rgba(251, 191, 36, 0.5)',
    intelGlow: 'rgba(59, 130, 246, 0.5)',
    gradientStart: '#8B5CF6',
    gradientEnd: '#3B82F6',
  },
};

// Typography Tokens
export const typography = {
  fonts: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    secondary: "'Space Grotesk', 'Inter', sans-serif",
    mono: "'JetBrains Mono', 'Consolas', monospace",
  },
  
  sizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem',     // 128px
  },
  
  weights: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
};

// Spacing Tokens
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem',      // 384px
};

// Border Radius Tokens
export const borderRadius = {
  none: '0',
  sm: '0.125rem',    // 2px
  base: '0.25rem',   // 4px
  md: '0.375rem',    // 6px
  lg: '0.5rem',      // 8px
  xl: '0.75rem',     // 12px
  '2xl': '1rem',     // 16px
  '3xl': '1.5rem',   // 24px
  full: '9999px',
};

// Shadow Tokens
export const shadows = {
  xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  base: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: 'none',
  
  // Glow Effects
  glow: {
    neural: '0 0 20px rgba(139, 92, 246, 0.5)',
    colony: '0 0 20px rgba(251, 191, 36, 0.5)',
    intel: '0 0 20px rgba(59, 130, 246, 0.5)',
    neuralLarge: '0 0 30px rgba(139, 92, 246, 0.3), 0 0 60px rgba(139, 92, 246, 0.1)',
  },
};

// Animation Tokens
export const animations = {
  timing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  duration: {
    fast: '150ms',
    base: '200ms',
    slow: '300ms',
    slower: '500ms',
    slowest: '1000ms',
  },
};

// Breakpoint Tokens
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1920px',
  '4xl': '2560px',
};

// Z-Index Tokens
export const zIndex = {
  hide: -1,
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
  critical: 9999,
};

// Theme Configurations
export const themes = {
  light: {
    name: 'light',
    colors: {
      // Backgrounds
      bgPrimary: colors.neutral[50],
      bgSecondary: colors.neutral[100],
      bgTertiary: colors.neutral[200],
      bgInverse: colors.neutral[900],
      
      // Surfaces
      surface: 'white',
      surfaceHover: colors.neutral[50],
      surfaceActive: colors.neutral[100],
      
      // Text
      textPrimary: colors.neutral[900],
      textSecondary: colors.neutral[700],
      textTertiary: colors.neutral[500],
      textInverse: colors.neutral[50],
      
      // Borders
      borderDefault: colors.neutral[200],
      borderMedium: colors.neutral[300],
      borderStrong: colors.neutral[400],
      
      // Brand
      primary: colors.neural[600],
      primaryHover: colors.neural[700],
      primaryActive: colors.neural[800],
      
      secondary: colors.intel[600],
      secondaryHover: colors.intel[700],
      secondaryActive: colors.intel[800],
      
      accent: colors.colony[500],
      accentHover: colors.colony[600],
      accentActive: colors.colony[700],
    },
  },
  
  dark: {
    name: 'dark',
    colors: {
      // Backgrounds
      bgPrimary: colors.neutral[950],
      bgSecondary: colors.neutral[900],
      bgTertiary: colors.neutral[800],
      bgInverse: colors.neutral[50],
      
      // Surfaces
      surface: colors.neutral[900],
      surfaceHover: colors.neutral[800],
      surfaceActive: colors.neutral[700],
      
      // Text
      textPrimary: colors.neutral[50],
      textSecondary: colors.neutral[200],
      textTertiary: colors.neutral[400],
      textInverse: colors.neutral[900],
      
      // Borders
      borderDefault: colors.neutral[800],
      borderMedium: colors.neutral[700],
      borderStrong: colors.neutral[600],
      
      // Brand
      primary: colors.neural[500],
      primaryHover: colors.neural[400],
      primaryActive: colors.neural[600],
      
      secondary: colors.intel[500],
      secondaryHover: colors.intel[400],
      secondaryActive: colors.intel[600],
      
      accent: colors.colony[400],
      accentHover: colors.colony[300],
      accentActive: colors.colony[500],
    },
  },
};

// Utility Functions
export const getTheme = (themeName: 'light' | 'dark') => themes[themeName];

export const getCSSVariables = (theme: typeof themes.light | typeof themes.dark) => {
  const cssVars: Record<string, string> = {};
  
  // Colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    cssVars[`--color-${key}`] = value;
  });
  
  // Typography
  Object.entries(typography.fonts).forEach(([key, value]) => {
    cssVars[`--font-${key}`] = value;
  });
  
  Object.entries(typography.sizes).forEach(([key, value]) => {
    cssVars[`--text-${key}`] = value;
  });
  
  // Spacing
  Object.entries(spacing).forEach(([key, value]) => {
    cssVars[`--space-${key}`] = value;
  });
  
  // Shadows
  Object.entries(shadows).forEach(([key, value]) => {
    if (typeof value === 'string') {
      cssVars[`--shadow-${key}`] = value;
    }
  });
  
  return cssVars;
};

// Export all tokens as a single object for easy access
export const tokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
  breakpoints,
  zIndex,
  themes,
};

export default tokens;