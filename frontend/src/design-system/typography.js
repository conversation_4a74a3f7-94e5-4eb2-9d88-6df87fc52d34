// NeuroColony Design System - Typography
// Modern technical fonts with organic curves

export const typography = {
  // Font families
  fonts: {
    // Primary font - Modern and technical with subtle curves
    primary: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    
    // Secondary font - For headings and emphasis
    heading: '"Space Grotesk", "Inter", sans-serif',
    
    // Monospace - For code and technical displays
    mono: '"JetBrains Mono", "Fira Code", Consolas, monospace',
    
    // Display - For large feature text
    display: '"Orbitron", "Space Grotesk", sans-serif',
  },
  
  // Font sizes following a neural scale
  sizes: {
    // Base sizes
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    
    // Heading sizes
    h6: '1.25rem',    // 20px
    h5: '1.5rem',     // 24px
    h4: '1.875rem',   // 30px
    h3: '2.25rem',    // 36px
    h2: '3rem',       // 48px
    h1: '3.75rem',    // 60px
    
    // Display sizes
    display: {
      sm: '3rem',     // 48px
      md: '4rem',     // 64px
      lg: '5rem',     // 80px
      xl: '6rem',     // 96px
    },
  },
  
  // Font weights
  weights: {
    thin: 100,
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  
  // Line heights for optimal readability
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  
  // Letter spacing for technical precision
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
    
    // Special spacing for technical displays
    mono: '0.02em',
    heading: '-0.02em',
    display: '-0.03em',
  },
  
  // Text styles presets
  styles: {
    // Body text
    body: {
      fontFamily: '"Inter", sans-serif',
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0',
    },
    
    // Agent status text
    agentStatus: {
      fontFamily: '"JetBrains Mono", monospace',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.25,
      letterSpacing: '0.02em',
      textTransform: 'uppercase',
    },
    
    // Colony metrics
    metric: {
      fontFamily: '"Space Grotesk", sans-serif',
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: 1,
      letterSpacing: '-0.02em',
    },
    
    // Code blocks
    code: {
      fontFamily: '"JetBrains Mono", monospace',
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.625,
      letterSpacing: '0.02em',
    },
    
    // Navigation
    nav: {
      fontFamily: '"Inter", sans-serif',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.025em',
    },
    
    // Button text
    button: {
      fontFamily: '"Inter", sans-serif',
      fontSize: '0.875rem',
      fontWeight: 600,
      lineHeight: 1,
      letterSpacing: '0.025em',
      textTransform: 'none',
    },
    
    // Labels
    label: {
      fontFamily: '"Inter", sans-serif',
      fontSize: '0.75rem',
      fontWeight: 600,
      lineHeight: 1.5,
      letterSpacing: '0.05em',
      textTransform: 'uppercase',
    },
  },
  
  // Text decorations for colony elements
  decorations: {
    // Agent role indicators
    queen: {
      textShadow: '0 0 20px rgba(156, 39, 176, 0.5)',
      fontWeight: 700,
    },
    worker: {
      textShadow: '0 0 10px rgba(0, 115, 230, 0.3)',
      fontWeight: 500,
    },
    scout: {
      textShadow: '0 0 10px rgba(0, 188, 212, 0.3)',
      fontWeight: 500,
    },
    
    // Status indicators
    active: {
      color: '#4caf50',
      textShadow: '0 0 5px rgba(76, 175, 80, 0.5)',
    },
    processing: {
      color: '#00bcd4',
      textShadow: '0 0 5px rgba(0, 188, 212, 0.5)',
    },
    error: {
      color: '#f44336',
      textShadow: '0 0 5px rgba(244, 67, 54, 0.5)',
    },
  },
};