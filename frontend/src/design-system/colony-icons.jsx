import React from 'react';
import { 
  Brain, 
  Hexagon, 
  Network, 
  Cpu, 
  Activity,
  Zap,
  GitBranch,
  Layers,
  Globe,
  Shield,
  Sparkles,
  Target,
  Workflow,
  Bot,
  Beaker,
  Rocket,
  Crown,
  Lightbulb,
  PieChart,
  TrendingUp
} from 'lucide-react';

// Neural Colony Icon Components with proper theming
export const ColonyIcon = ({ size = 24, className = '', animate = false }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`${className} ${animate ? 'animate-float' : ''}`}
  >
    <path 
      d="M12 2L2 7V17L12 22L22 17V7L12 2Z" 
      className="stroke-neural-600 dark:stroke-neural-400"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle 
      cx="12" 
      cy="12" 
      r="3" 
      className="fill-honey-500 dark:fill-honey-400 animate-pulse"
    />
    <path 
      d="M12 9V15M9 12H15" 
      className="stroke-neural-600 dark:stroke-neural-400"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

export const NeuralNetworkIcon = ({ size = 24, className = '', animate = false }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`${className} ${animate ? 'animate-pulse' : ''}`}
  >
    <circle cx="6" cy="6" r="2" className="fill-neural-500 dark:fill-neural-400" />
    <circle cx="18" cy="6" r="2" className="fill-neural-500 dark:fill-neural-400" />
    <circle cx="12" cy="12" r="2" className="fill-honey-500 dark:fill-honey-400" />
    <circle cx="6" cy="18" r="2" className="fill-neural-500 dark:fill-neural-400" />
    <circle cx="18" cy="18" r="2" className="fill-neural-500 dark:fill-neural-400" />
    <path 
      d="M6 6L12 12M18 6L12 12M6 18L12 12M18 18L12 12" 
      className="stroke-neural-300 dark:stroke-neural-600"
      strokeWidth="1"
      strokeDasharray="2 2"
    />
  </svg>
);

export const SwarmIcon = ({ size = 24, className = '', animate = false }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    className={`${className} ${animate ? 'animate-spin-slow' : ''}`}
  >
    {[0, 60, 120, 180, 240, 300].map((rotation, i) => (
      <circle
        key={i}
        cx="12"
        cy="6"
        r="2"
        className="fill-swarm-500 dark:fill-swarm-400"
        transform={`rotate(${rotation} 12 12)`}
        opacity={0.8 - i * 0.1}
      />
    ))}
    <circle cx="12" cy="12" r="3" className="fill-swarm-600 dark:fill-swarm-300" />
  </svg>
);

// Status Icons with proper contrast
export const ActiveStatusIcon = ({ size = 16, className = '' }) => (
  <div className={`relative ${className}`}>
    <div className={`w-${size/4} h-${size/4} bg-swarm-500 rounded-full animate-ping absolute`} />
    <div className={`w-${size/4} h-${size/4} bg-swarm-600 rounded-full`} />
  </div>
);

export const ProcessingStatusIcon = ({ size = 16, className = '' }) => (
  <div className={`w-${size/4} h-${size/4} ${className}`}>
    <div className="relative">
      <div className="w-full h-full border-2 border-honey-200 dark:border-honey-700 rounded-full" />
      <div className="w-full h-full border-2 border-honey-500 dark:border-honey-400 rounded-full border-t-transparent animate-spin absolute top-0" />
    </div>
  </div>
);

export const IdleStatusIcon = ({ size = 16, className = '' }) => (
  <div className={`w-${size/4} h-${size/4} bg-gray-400 dark:bg-gray-600 rounded-full ${className}`} />
);

export const ErrorStatusIcon = ({ size = 16, className = '' }) => (
  <div className={`relative ${className}`}>
    <div className={`w-${size/4} h-${size/4} bg-alert-500 rounded-full animate-pulse`} />
  </div>
);

// Enhanced Icon Map with Colony Theme
export const colonyIcons = {
  // Core Colony Icons
  colony: ColonyIcon,
  neural: NeuralNetworkIcon,
  swarm: SwarmIcon,
  brain: (props) => <Brain {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  hexagon: (props) => <Hexagon {...props} className={`text-honey-600 dark:text-honey-400 ${props.className || ''}`} />,
  network: (props) => <Network {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  
  // Agent Types
  worker: (props) => <Bot {...props} className={`text-swarm-600 dark:text-swarm-400 ${props.className || ''}`} />,
  architect: (props) => <Cpu {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  scout: (props) => <Target {...props} className={`text-honey-600 dark:text-honey-400 ${props.className || ''}`} />,
  guard: (props) => <Shield {...props} className={`text-alert-600 dark:text-alert-400 ${props.className || ''}`} />,
  
  // Actions
  activate: (props) => <Zap {...props} className={`text-swarm-500 dark:text-swarm-400 ${props.className || ''}`} />,
  process: (props) => <Activity {...props} className={`text-honey-500 dark:text-honey-400 ${props.className || ''}`} />,
  optimize: (props) => <Sparkles {...props} className={`text-neural-500 dark:text-neural-400 ${props.className || ''}`} />,
  analyze: (props) => <PieChart {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  
  // Features
  intelligence: (props) => <Lightbulb {...props} className={`text-honey-500 dark:text-honey-400 ${props.className || ''}`} />,
  workflow: (props) => <Workflow {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  experiment: (props) => <Beaker {...props} className={`text-swarm-600 dark:text-swarm-400 ${props.className || ''}`} />,
  launch: (props) => <Rocket {...props} className={`text-alert-600 dark:text-alert-400 ${props.className || ''}`} />,
  
  // Hierarchy
  queen: (props) => <Crown {...props} className={`text-honey-600 dark:text-honey-400 ${props.className || ''}`} />,
  hive: (props) => <Layers {...props} className={`text-neural-600 dark:text-neural-400 ${props.className || ''}`} />,
  global: (props) => <Globe {...props} className={`text-neural-500 dark:text-neural-400 ${props.className || ''}`} />,
  growth: (props) => <TrendingUp {...props} className={`text-swarm-600 dark:text-swarm-400 ${props.className || ''}`} />,
  
  // Status Icons
  statusActive: ActiveStatusIcon,
  statusProcessing: ProcessingStatusIcon,
  statusIdle: IdleStatusIcon,
  statusError: ErrorStatusIcon
};

// Icon wrapper component with consistent sizing and theming
export const Icon = ({ name, size = 24, className = '', animate = false, ...props }) => {
  const IconComponent = colonyIcons[name];
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in colony icons`);
    return null;
  }
  
  return <IconComponent size={size} className={className} animate={animate} {...props} />;
};

// Hexagonal card wrapper for colony theme
export const HexCard = ({ children, className = '', glow = false }) => (
  <div className={`relative ${className}`}>
    <div 
      className={`
        relative bg-white dark:bg-gray-900 
        border-2 border-neural-200 dark:border-neural-700
        shadow-lg hover:shadow-xl
        transition-all duration-300
        ${glow ? 'animate-glow' : ''}
        overflow-hidden
      `}
      style={{
        clipPath: 'polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)'
      }}
    >
      <div className="p-6">
        {children}
      </div>
    </div>
  </div>
);

// Neural connection background pattern
export const NeuralPattern = ({ className = '' }) => (
  <svg 
    className={`absolute inset-0 w-full h-full ${className}`}
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <pattern id="neural-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
        <circle cx="50" cy="50" r="1" className="fill-neural-200 dark:fill-neural-800" />
        <line x1="50" y1="50" x2="100" y2="0" className="stroke-neural-100 dark:stroke-neural-900" strokeWidth="0.5" />
        <line x1="50" y1="50" x2="100" y2="100" className="stroke-neural-100 dark:stroke-neural-900" strokeWidth="0.5" />
        <line x1="50" y1="50" x2="0" y2="0" className="stroke-neural-100 dark:stroke-neural-900" strokeWidth="0.5" />
        <line x1="50" y1="50" x2="0" y2="100" className="stroke-neural-100 dark:stroke-neural-900" strokeWidth="0.5" />
      </pattern>
    </defs>
    <rect width="100%" height="100%" fill="url(#neural-pattern)" opacity="0.1" />
  </svg>
);

export default colonyIcons;