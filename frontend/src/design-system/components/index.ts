/**
 * NeuroColony Component Library
 * Export all 100+ components organized by category
 */

// Layout Components
export { default as Container } from './layout/Container';
export { default as Grid } from './layout/Grid';
export { default as Stack } from './layout/Stack';
export { default as Box } from './layout/Box';
export { default as Flex } from './layout/Flex';
export { default as Divider } from './layout/Divider';
export { default as Spacer } from './layout/Spacer';
export { default as Center } from './layout/Center';
export { default as AspectRatio } from './layout/AspectRatio';
export { default as ScrollArea } from './layout/ScrollArea';

// Typography Components
export { default as Heading } from './typography/Heading';
export { default as Text } from './typography/Text';
export { default as Paragraph } from './typography/Paragraph';
export { default as Link } from './typography/Link';
export { default as Code } from './typography/Code';
export { default as BlockQuote } from './typography/BlockQuote';
export { default as List } from './typography/List';
export { default as Label } from './typography/Label';

// Navigation Components
export { default as Navbar } from './navigation/Navbar';
export { default as Sidebar } from './navigation/Sidebar';
export { default as Breadcrumb } from './navigation/Breadcrumb';
export { default as Tabs } from './navigation/Tabs';
export { default as Pagination } from './navigation/Pagination';
export { default as Steps } from './navigation/Steps';
export { default as Menu } from './navigation/Menu';
export { default as NavigationRail } from './navigation/NavigationRail';

// Form Components
export { default as Input } from './forms/Input';
export { default as TextArea } from './forms/TextArea';
export { default as Select } from './forms/Select';
export { default as Checkbox } from './forms/Checkbox';
export { default as Radio } from './forms/Radio';
export { default as Switch } from './forms/Switch';
export { default as Slider } from './forms/Slider';
export { default as DatePicker } from './forms/DatePicker';
export { default as TimePicker } from './forms/TimePicker';
export { default as ColorPicker } from './forms/ColorPicker';
export { default as FileUpload } from './forms/FileUpload';
export { default as SearchInput } from './forms/SearchInput';
export { default as PinInput } from './forms/PinInput';
export { default as NumberInput } from './forms/NumberInput';
export { default as RangeSlider } from './forms/RangeSlider';
export { default as FormField } from './forms/FormField';
export { default as FormGroup } from './forms/FormGroup';

// Data Display Components
export { default as Table } from './data-display/Table';
export { default as DataGrid } from './data-display/DataGrid';
export { default as Card } from './data-display/Card';
export { default as Stat } from './data-display/Stat';
export { default as Badge } from './data-display/Badge';
export { default as Tag } from './data-display/Tag';
export { default as Timeline } from './data-display/Timeline';
export { default as Avatar } from './data-display/Avatar';
export { default as AvatarGroup } from './data-display/AvatarGroup';
export { default as Image } from './data-display/Image';
export { default as Gallery } from './data-display/Gallery';
export { default as EmptyState } from './data-display/EmptyState';
export { default as DescriptionList } from './data-display/DescriptionList';
export { default as KeyValue } from './data-display/KeyValue';

// Feedback Components
export { default as Alert } from './feedback/Alert';
export { default as Toast } from './feedback/Toast';
export { default as Progress } from './feedback/Progress';
export { default as Spinner } from './feedback/Spinner';
export { default as Skeleton } from './feedback/Skeleton';
export { default as LoadingDots } from './feedback/LoadingDots';
export { default as StatusIndicator } from './feedback/StatusIndicator';
export { default as Notification } from './feedback/Notification';
export { default as Banner } from './feedback/Banner';

// Overlay Components
export { default as Modal } from './overlay/Modal';
export { default as Drawer } from './overlay/Drawer';
export { default as Popover } from './overlay/Popover';
export { default as Tooltip } from './overlay/Tooltip';
export { default as ContextMenu } from './overlay/ContextMenu';
export { default as CommandPalette } from './overlay/CommandPalette';
export { default as Spotlight } from './overlay/Spotlight';
export { default as Sheet } from './overlay/Sheet';

// Action Components
export { default as Button } from './actions/Button';
export { default as IconButton } from './actions/IconButton';
export { default as ButtonGroup } from './actions/ButtonGroup';
export { default as FloatingActionButton } from './actions/FloatingActionButton';
export { default as SpeedDial } from './actions/SpeedDial';
export { default as ToggleButton } from './actions/ToggleButton';
export { default as SplitButton } from './actions/SplitButton';

// AI/Colony Components (Unique to NeuroColony)
export { default as AgentCard } from './ai/AgentCard';
export { default as WorkflowNode } from './ai/WorkflowNode';
export { default as NeuralPath } from './ai/NeuralPath';
export { default as ColonyMap } from './ai/ColonyMap';
export { default as AIChat } from './ai/AIChat';
export { default as PromptBuilder } from './ai/PromptBuilder';
export { default as AgentStatus } from './ai/AgentStatus';
export { default as IntelligenceBar } from './ai/IntelligenceBar';
export { default as WorkflowCanvas } from './ai/WorkflowCanvas';
export { default as NodeConnector } from './ai/NodeConnector';

// Marketing Components (Domain-specific)
export { default as EmailPreview } from './marketing/EmailPreview';
export { default as SequenceBuilder } from './marketing/SequenceBuilder';
export { default as CampaignCard } from './marketing/CampaignCard';
export { default as MetricCard } from './marketing/MetricCard';
export { default as ConversionFunnel } from './marketing/ConversionFunnel';
export { default as ABTestResults } from './marketing/ABTestResults';
export { default as EngagementChart } from './marketing/EngagementChart';
export { default as SubjectLineOptimizer } from './marketing/SubjectLineOptimizer';
export { default as PersonalizationTokens } from './marketing/PersonalizationTokens';
export { default as TemplateGallery } from './marketing/TemplateGallery';

// Charts & Visualizations
export { default as LineChart } from './charts/LineChart';
export { default as BarChart } from './charts/BarChart';
export { default as PieChart } from './charts/PieChart';
export { default as AreaChart } from './charts/AreaChart';
export { default as RadarChart } from './charts/RadarChart';
export { default as HeatMap } from './charts/HeatMap';
export { default as TreeMap } from './charts/TreeMap';
export { default as Gauge } from './charts/Gauge';
export { default as Sparkline } from './charts/Sparkline';
export { default as NetworkGraph } from './charts/NetworkGraph';

// Utility Components
export { default as Portal } from './utility/Portal';
export { default as ErrorBoundary } from './utility/ErrorBoundary';
export { default as LazyLoad } from './utility/LazyLoad';
export { default as InfiniteScroll } from './utility/InfiniteScroll';
export { default as VirtualList } from './utility/VirtualList';
export { default as ResizeObserver } from './utility/ResizeObserver';
export { default as ClickOutside } from './utility/ClickOutside';
export { default as CopyToClipboard } from './utility/CopyToClipboard';
export { default as Transition } from './utility/Transition';
export { default as AnimatedNumber } from './utility/AnimatedNumber';

// Premium Components (Enterprise features)
export { default as Dashboard } from './premium/Dashboard';
export { default as KanbanBoard } from './premium/KanbanBoard';
export { default as Calendar } from './premium/Calendar';
export { default as RichTextEditor } from './premium/RichTextEditor';
export { default as CodeEditor } from './premium/CodeEditor';
export { default as VideoPlayer } from './premium/VideoPlayer';
export { default as AudioPlayer } from './premium/AudioPlayer';
export { default as FileManager } from './premium/FileManager';
export { default as ImageEditor } from './premium/ImageEditor';
export { default as PDFViewer } from './premium/PDFViewer';

// Composite Components (Full features)
export { default as UserProfile } from './composite/UserProfile';
export { default as CommentSection } from './composite/CommentSection';
export { default as PricingTable } from './composite/PricingTable';
export { default as FeatureComparison } from './composite/FeatureComparison';
export { default as TestimonialCarousel } from './composite/TestimonialCarousel';
export { default as FAQAccordion } from './composite/FAQAccordion';
export { default as NewsletterSignup } from './composite/NewsletterSignup';
export { default as ProductShowcase } from './composite/ProductShowcase';
export { default as TeamSection } from './composite/TeamSection';
export { default as ContactForm } from './composite/ContactForm';

// Animation Components
export { default as ParallaxSection } from './animation/ParallaxSection';
export { default as ScrollReveal } from './animation/ScrollReveal';
export { default as TypeWriter } from './animation/TypeWriter';
export { default as CountUp } from './animation/CountUp';
export { default as Confetti } from './animation/Confetti';
export { default as ParticleField } from './animation/ParticleField';
export { default as WaveAnimation } from './animation/WaveAnimation';
export { default as PulseAnimation } from './animation/PulseAnimation';
export { default as GlowEffect } from './animation/GlowEffect';
export { default as MorphingText } from './animation/MorphingText';

// Theme Components
export { default as ThemeProvider } from './theme/ThemeProvider';
export { default as ThemeToggle } from './theme/ThemeToggle';
export { default as ColorModeScript } from './theme/ColorModeScript';
export { default as ThemeCustomizer } from './theme/ThemeCustomizer';

// Mobile Components
export { default as BottomNavigation } from './mobile/BottomNavigation';
export { default as SwipeableViews } from './mobile/SwipeableViews';
export { default as PullToRefresh } from './mobile/PullToRefresh';
export { default as MobileDrawer } from './mobile/MobileDrawer';
export { default as TouchRipple } from './mobile/TouchRipple';

// Accessibility Components
export { default as SkipLink } from './accessibility/SkipLink';
export { default as ScreenReaderOnly } from './accessibility/ScreenReaderOnly';
export { default as FocusTrap } from './accessibility/FocusTrap';
export { default as LiveRegion } from './accessibility/LiveRegion';
export { default as KeyboardShortcuts } from './accessibility/KeyboardShortcuts';

// Export component types
export * from './types';