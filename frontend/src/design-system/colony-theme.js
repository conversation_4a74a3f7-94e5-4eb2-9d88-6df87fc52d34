// NeuroColony AI Colony Theme System
// Comprehensive visual design system with proper contrast and neural/colony branding

export const colonyTheme = {
  // Neural Colony Color Palette
  colors: {
    // Primary Neural Colors
    neural: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49'
    },
    
    // Colony Honey/Amber Colors
    honey: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03'
    },
    
    // Swarm Green (Success/Active)
    swarm: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // Alert Red (Critical/Error)
    alert: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    
    // High Contrast Grays (WCAG AA Compliant)
    gray: {
      50: '#fafafa',
      100: '#f4f4f5',
      200: '#e4e4e7',
      300: '#d4d4d8',
      400: '#a1a1aa',
      500: '#71717a',
      600: '#52525b',
      700: '#3f3f46',
      800: '#27272a',
      900: '#18181b',
      950: '#09090b'
    }
  },
  
  // Typography with Proper Contrast
  typography: {
    // Headers - Always high contrast
    h1: 'text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100',
    h2: 'text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100',
    h3: 'text-2xl md:text-3xl font-semibold text-gray-800 dark:text-gray-200',
    h4: 'text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200',
    h5: 'text-lg md:text-xl font-medium text-gray-700 dark:text-gray-300',
    
    // Body text - Readable contrast
    body: 'text-base font-normal text-gray-700 dark:text-gray-300',
    bodyLarge: 'text-lg font-normal text-gray-700 dark:text-gray-300',
    bodySmall: 'text-sm font-normal text-gray-600 dark:text-gray-400',
    
    // Metadata - Never below gray-600
    meta: 'text-sm font-medium text-gray-600 dark:text-gray-400',
    metaSmall: 'text-xs font-medium text-gray-600 dark:text-gray-400',
    
    // Links and Actions
    link: 'font-medium text-neural-600 hover:text-neural-700 dark:text-neural-400 dark:hover:text-neural-300',
    button: 'font-semibold text-white'
  },
  
  // Component Styles
  components: {
    // Cards with proper shadows and borders
    card: {
      base: 'bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700',
      hover: 'hover:shadow-xl hover:border-neural-300 dark:hover:border-neural-600 transform hover:scale-[1.02] transition-all duration-200',
      active: 'ring-2 ring-neural-500 ring-offset-2'
    },
    
    // Buttons with high contrast
    button: {
      primary: 'bg-gradient-to-r from-neural-600 to-neural-700 hover:from-neural-700 hover:to-neural-800 text-white font-semibold shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200',
      secondary: 'bg-white dark:bg-gray-800 border-2 border-neural-600 text-neural-700 dark:text-neural-300 hover:bg-neural-50 dark:hover:bg-gray-700 font-semibold',
      danger: 'bg-gradient-to-r from-alert-600 to-alert-700 hover:from-alert-700 hover:to-alert-800 text-white font-semibold',
      success: 'bg-gradient-to-r from-swarm-600 to-swarm-700 hover:from-swarm-700 hover:to-swarm-800 text-white font-semibold'
    },
    
    // Badges with proper contrast
    badge: {
      neural: 'bg-neural-100 text-neural-800 dark:bg-neural-900 dark:text-neural-200 font-medium',
      honey: 'bg-honey-100 text-honey-800 dark:bg-honey-900 dark:text-honey-200 font-medium',
      swarm: 'bg-swarm-100 text-swarm-800 dark:bg-swarm-900 dark:text-swarm-200 font-medium',
      alert: 'bg-alert-100 text-alert-800 dark:bg-alert-900 dark:text-alert-200 font-medium',
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 font-medium'
    },
    
    // Status indicators
    status: {
      active: 'bg-swarm-500 animate-pulse',
      processing: 'bg-honey-500 animate-spin',
      idle: 'bg-gray-400',
      error: 'bg-alert-500'
    }
  },
  
  // Background Gradients
  gradients: {
    neural: 'bg-gradient-to-br from-neural-50 via-white to-neural-50 dark:from-gray-900 dark:via-gray-800 dark:to-neural-950',
    honey: 'bg-gradient-to-br from-honey-50 via-white to-neural-50 dark:from-gray-900 dark:via-gray-800 dark:to-honey-950',
    dark: 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900'
  },
  
  // Animations
  animations: {
    pulse: 'animate-pulse',
    spin: 'animate-spin',
    bounce: 'animate-bounce',
    glow: 'animate-glow',
    float: 'animate-float'
  },
  
  // Focus and Accessibility
  accessibility: {
    focusRing: 'focus:ring-2 focus:ring-neural-500 focus:ring-offset-2 focus:outline-none',
    srOnly: 'sr-only',
    notSrOnly: 'not-sr-only',
    highContrast: 'contrast-more:font-semibold contrast-more:text-gray-900 dark:contrast-more:text-white'
  }
};

// Colony-themed terminology mapping
export const colonyTerms = {
  // Core concepts
  'email sequence': 'agent workflow',
  'email campaign': 'colony mission',
  'email template': 'colony blueprint',
  'subject line': 'mission directive',
  'email content': 'agent instructions',
  'recipient': 'colony target',
  'subscriber': 'colony member',
  
  // Metrics
  'open rate': 'activation rate',
  'click rate': 'interaction rate',
  'conversion rate': 'success rate',
  'bounce rate': 'rejection rate',
  'unsubscribe rate': 'colony exit rate',
  
  // Actions
  'send email': 'deploy agent',
  'schedule email': 'queue mission',
  'create template': 'design blueprint',
  'test email': 'simulate mission',
  'analyze results': 'review colony intelligence',
  
  // Features
  'email automation': 'colony automation',
  'email marketing': 'colony operations',
  'email analytics': 'colony intelligence',
  'email segmentation': 'colony organization',
  'email personalization': 'agent customization'
};

// Utility function to apply colony theme classes
export const cn = (...classes) => classes.filter(Boolean).join(' ');

// Theme class generators
export const getTextClass = (variant = 'body') => colonyTheme.typography[variant] || colonyTheme.typography.body;
export const getButtonClass = (variant = 'primary') => colonyTheme.components.button[variant] || colonyTheme.components.button.primary;
export const getBadgeClass = (variant = 'neural') => colonyTheme.components.badge[variant] || colonyTheme.components.badge.neural;
export const getCardClass = (hover = true) => cn(colonyTheme.components.card.base, hover && colonyTheme.components.card.hover);

// Export theme provider component
export const ColonyThemeProvider = ({ children }) => {
  // Add global styles for animations
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes glow {
        0%, 100% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.5); }
        50% { box-shadow: 0 0 30px rgba(14, 165, 233, 0.8); }
      }
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      .animate-glow { animation: glow 2s ease-in-out infinite; }
      .animate-float { animation: float 3s ease-in-out infinite; }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  
  return children;
};

export default colonyTheme;