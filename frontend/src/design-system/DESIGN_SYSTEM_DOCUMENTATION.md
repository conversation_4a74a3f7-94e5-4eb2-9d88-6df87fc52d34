# NeuroColony Design System Documentation

## 🎨 Brand Identity & Visual Language

### Brand Essence
**NeuroColony** represents the perfect fusion of biological intelligence (ant colonies) and artificial intelligence. Our design system reflects this through organic patterns, neural networks, and collaborative workflows.

### Brand Values
- **Intelligence**: Sophisticated AI that thinks ahead
- **Collaboration**: Colony intelligence working together
- **Growth**: Evolutionary optimization
- **Trust**: Enterprise-grade reliability
- **Innovation**: Cutting-edge marketing automation

### Visual Metaphors
1. **Colony Networks**: Interconnected nodes representing agents
2. **Neural Pathways**: Data flow and decision trees
3. **Hexagonal Patterns**: Efficiency and perfect organization
4. **Gradient Flows**: Energy and intelligence transfer
5. **Pulsing Elements**: Living, breathing system

## 🎯 Design Principles

### 1. Intelligence-First Design
Every interface element should feel smart and anticipatory. Use micro-interactions that suggest the system is thinking and learning.

### 2. Progressive Disclosure
Complex features should reveal themselves as users advance. Start simple, scale to enterprise complexity.

### 3. Visual Hierarchy Through Motion
Use subtle animations to guide attention and show relationships between elements.

### 4. Data Visualization Excellence
Transform complex metrics into beautiful, understandable visualizations that tell a story.

### 5. Accessibility Without Compromise
WCAG 2.1 AA compliance while maintaining premium aesthetics.

## 🎨 Color System

### Primary Palette
```scss
// Neural Purple (Primary Brand)
$neural-purple-50: #F5F3FF;
$neural-purple-100: #EDE9FE;
$neural-purple-200: #DDD6FE;
$neural-purple-300: #C4B5FD;
$neural-purple-400: #A78BFA;
$neural-purple-500: #8B5CF6; // Primary
$neural-purple-600: #7C3AED;
$neural-purple-700: #6D28D9;
$neural-purple-800: #5B21B6;
$neural-purple-900: #4C1D95;
$neural-purple-950: #2D1065;

// Colony Gold (Accent)
$colony-gold-50: #FFFBEB;
$colony-gold-100: #FEF3C7;
$colony-gold-200: #FDE68A;
$colony-gold-300: #FCD34D;
$colony-gold-400: #FBBF24; // Accent
$colony-gold-500: #F59E0B;
$colony-gold-600: #D97706;
$colony-gold-700: #B45309;
$colony-gold-800: #92400E;
$colony-gold-900: #78350F;

// Intelligence Blue (Secondary)
$intel-blue-50: #EFF6FF;
$intel-blue-100: #DBEAFE;
$intel-blue-200: #BFDBFE;
$intel-blue-300: #93C5FD;
$intel-blue-400: #60A5FA;
$intel-blue-500: #3B82F6; // Secondary
$intel-blue-600: #2563EB;
$intel-blue-700: #1D4ED8;
$intel-blue-800: #1E40AF;
$intel-blue-900: #1E3A8A;
```

### Semantic Colors
```scss
// Success (Growth)
$success-light: #86EFAC;
$success-base: #22C55E;
$success-dark: #15803D;

// Warning (Attention)
$warning-light: #FDE047;
$warning-base: #EAB308;
$warning-dark: #A16207;

// Error (Critical)
$error-light: #FCA5A5;
$error-base: #EF4444;
$error-dark: #B91C1C;

// Info (Insight)
$info-light: #93C5FD;
$info-base: #3B82F6;
$info-dark: #1E40AF;
```

### Neutral Palette (Light & Dark Modes)
```scss
// Light Mode Neutrals
$light-neutral-50: #FAFAFA;
$light-neutral-100: #F5F5F5;
$light-neutral-200: #E5E5E5;
$light-neutral-300: #D4D4D4;
$light-neutral-400: #A3A3A3;
$light-neutral-500: #737373;
$light-neutral-600: #525252;
$light-neutral-700: #404040;
$light-neutral-800: #262626;
$light-neutral-900: #171717;
$light-neutral-950: #0A0A0A;

// Dark Mode Neutrals (Inverted)
$dark-neutral-50: #0A0A0A;
$dark-neutral-100: #171717;
$dark-neutral-200: #262626;
$dark-neutral-300: #404040;
$dark-neutral-400: #525252;
$dark-neutral-500: #737373;
$dark-neutral-600: #A3A3A3;
$dark-neutral-700: #D4D4D4;
$dark-neutral-800: #E5E5E5;
$dark-neutral-900: #F5F5F5;
$dark-neutral-950: #FAFAFA;
```

## 📐 Typography System

### Font Families
```scss
// Primary: Inter (UI)
$font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

// Secondary: Space Grotesk (Headings)
$font-secondary: 'Space Grotesk', 'Inter', sans-serif;

// Monospace: JetBrains Mono (Code)
$font-mono: 'JetBrains Mono', 'Consolas', monospace;
```

### Type Scale (Major Third - 1.25)
```scss
$text-xs: 0.75rem;     // 12px
$text-sm: 0.875rem;    // 14px
$text-base: 1rem;      // 16px
$text-lg: 1.125rem;    // 18px
$text-xl: 1.25rem;     // 20px
$text-2xl: 1.5rem;     // 24px
$text-3xl: 1.875rem;   // 30px
$text-4xl: 2.25rem;    // 36px
$text-5xl: 3rem;       // 48px
$text-6xl: 3.75rem;    // 60px
$text-7xl: 4.5rem;     // 72px
$text-8xl: 6rem;       // 96px
$text-9xl: 8rem;       // 128px
```

### Font Weights
```scss
$font-thin: 100;
$font-extralight: 200;
$font-light: 300;
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
$font-extrabold: 800;
$font-black: 900;
```

### Line Heights
```scss
$leading-none: 1;
$leading-tight: 1.25;
$leading-snug: 1.375;
$leading-normal: 1.5;
$leading-relaxed: 1.625;
$leading-loose: 2;
```

## 📏 Spacing System

### Base Unit: 4px
```scss
$space-0: 0;
$space-px: 1px;
$space-0.5: 0.125rem;  // 2px
$space-1: 0.25rem;     // 4px
$space-1.5: 0.375rem;  // 6px
$space-2: 0.5rem;      // 8px
$space-2.5: 0.625rem;  // 10px
$space-3: 0.75rem;     // 12px
$space-3.5: 0.875rem;  // 14px
$space-4: 1rem;        // 16px
$space-5: 1.25rem;     // 20px
$space-6: 1.5rem;      // 24px
$space-7: 1.75rem;     // 28px
$space-8: 2rem;        // 32px
$space-9: 2.25rem;     // 36px
$space-10: 2.5rem;     // 40px
$space-12: 3rem;       // 48px
$space-14: 3.5rem;     // 56px
$space-16: 4rem;       // 64px
$space-20: 5rem;       // 80px
$space-24: 6rem;       // 96px
$space-28: 7rem;       // 112px
$space-32: 8rem;       // 128px
$space-36: 9rem;       // 144px
$space-40: 10rem;      // 160px
$space-44: 11rem;      // 176px
$space-48: 12rem;      // 192px
$space-52: 13rem;      // 208px
$space-56: 14rem;      // 224px
$space-60: 15rem;      // 240px
$space-64: 16rem;      // 256px
$space-72: 18rem;      // 288px
$space-80: 20rem;      // 320px
$space-96: 24rem;      // 384px
```

## 🎭 Shadow System

### Elevation Levels
```scss
// Subtle shadows for light mode
$shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
$shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
$shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
$shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
$shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
$shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

// Glow effects for dark mode
$glow-purple: 0 0 20px rgba(139, 92, 246, 0.5);
$glow-gold: 0 0 20px rgba(251, 191, 36, 0.5);
$glow-blue: 0 0 20px rgba(59, 130, 246, 0.5);

// Neural glow (signature effect)
$glow-neural: 0 0 30px rgba(139, 92, 246, 0.3), 0 0 60px rgba(139, 92, 246, 0.1);
```

## 🔄 Animation System

### Timing Functions
```scss
$ease-linear: linear;
$ease-in: cubic-bezier(0.4, 0, 1, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

### Duration Scale
```scss
$duration-75: 75ms;
$duration-100: 100ms;
$duration-150: 150ms;
$duration-200: 200ms;
$duration-300: 300ms;
$duration-500: 500ms;
$duration-700: 700ms;
$duration-1000: 1000ms;
```

### Signature Animations
```scss
// Pulse (for thinking/processing states)
@keyframes pulse-neural {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// Flow (for data movement)
@keyframes flow-gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

// Colony movement
@keyframes colony-move {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(5px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(5px); }
  75% { transform: translateX(5px) translateY(5px); }
  100% { transform: translateX(0) translateY(0); }
}
```

## 📱 Breakpoint System

### Screen Sizes
```scss
$screen-xs: 475px;   // Mobile portrait
$screen-sm: 640px;   // Mobile landscape
$screen-md: 768px;   // Tablet portrait
$screen-lg: 1024px;  // Tablet landscape
$screen-xl: 1280px;  // Desktop
$screen-2xl: 1536px; // Large desktop
$screen-3xl: 1920px; // Full HD
$screen-4xl: 2560px; // 2K/4K
```

### Container Widths
```scss
$container-xs: 100%;
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1400px;
```

## 🎯 Grid System

### 12-Column Grid
```scss
$grid-columns: 12;
$grid-gutter-xs: 16px;
$grid-gutter-sm: 20px;
$grid-gutter-md: 24px;
$grid-gutter-lg: 32px;
$grid-gutter-xl: 40px;
```

## ♿ Accessibility Standards

### Focus Styles
```scss
$focus-ring-width: 2px;
$focus-ring-offset: 2px;
$focus-ring-color: $neural-purple-500;
$focus-ring-color-dark: $colony-gold-400;
```

### Contrast Ratios
- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- Interactive elements: 3:1 minimum
- Focus indicators: 3:1 minimum

### Motion Preferences
```scss
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## 🎨 Component Architecture

### Atomic Design Structure
1. **Atoms**: Basic building blocks (buttons, inputs, labels)
2. **Molecules**: Simple component groups (form fields, cards)
3. **Organisms**: Complex components (navigation, feature sections)
4. **Templates**: Page layouts and structures
5. **Pages**: Specific page implementations

### Component Naming Convention
- PascalCase for components: `Button`, `Card`, `Navigation`
- camelCase for props: `isLoading`, `hasError`, `onClick`
- kebab-case for CSS classes: `btn-primary`, `card-header`
- SCREAMING_SNAKE_CASE for constants: `MAX_WIDTH`, `DEFAULT_TIMEOUT`

### Component Categories
1. **Layout Components**: Container, Grid, Stack, Divider
2. **Navigation**: Navbar, Sidebar, Breadcrumb, Tabs
3. **Data Input**: Input, TextArea, Select, DatePicker, FileUpload
4. **Data Display**: Table, List, Timeline, Stat, Chart
5. **Feedback**: Alert, Toast, Progress, Skeleton, Spinner
6. **Overlay**: Modal, Drawer, Popover, Tooltip
7. **AI Components**: AgentCard, WorkflowNode, NeuralPath
8. **Marketing**: SequenceBuilder, EmailPreview, Analytics

## 🚀 Performance Guidelines

### Image Optimization
- Use WebP format with PNG/JPG fallbacks
- Implement responsive images with srcset
- Lazy load below-the-fold images
- Maximum image size: 200KB for heroes, 50KB for thumbnails

### Code Splitting
- Route-based splitting for all pages
- Component-based splitting for heavy components
- Dynamic imports for optional features
- Tree-shake unused code

### Critical CSS
- Inline critical above-the-fold styles
- Async load non-critical stylesheets
- Use CSS containment for complex components
- Minimize CSS-in-JS runtime overhead

### Bundle Optimization
- Target bundle size: <100KB for initial load
- Use dynamic imports for large dependencies
- Implement progressive enhancement
- Cache static assets aggressively

## 🌍 Internationalization

### Text Direction
- Support RTL languages (Arabic, Hebrew)
- Logical properties for spacing (margin-inline-start)
- Directional icons that flip automatically

### Number Formatting
- Locale-aware number formatting
- Currency display with proper symbols
- Date/time in user's locale

### Font Support
- Variable fonts for multiple weights
- Unicode range subsetting
- Fallback fonts for all languages

## 🎨 Theme Structure

### Theme Tokens
```typescript
interface Theme {
  name: 'light' | 'dark' | 'high-contrast';
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  shadows: ShadowTokens;
  animations: AnimationTokens;
  breakpoints: BreakpointTokens;
  zIndex: ZIndexTokens;
}
```

### CSS Custom Properties
All design tokens are available as CSS custom properties:
```css
:root {
  --color-primary: #8B5CF6;
  --font-primary: 'Inter';
  --spacing-4: 1rem;
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  /* ... */
}
```

## 📚 Usage Examples

### Basic Component
```tsx
import { Button } from '@/components/ui/Button';

<Button variant="primary" size="lg" isLoading>
  Generate Sequence
</Button>
```

### Theming
```tsx
import { useTheme } from '@/hooks/useTheme';

const MyComponent = () => {
  const { theme, setTheme } = useTheme();
  
  return (
    <div className={`bg-surface text-content ${theme}`}>
      {/* Component content */}
    </div>
  );
};
```

### Responsive Design
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
  {/* Responsive grid content */}
</div>
```

## 🔧 Developer Tools

### Design Token Generator
```bash
npm run generate-tokens
```

### Component Generator
```bash
npm run generate-component MyComponent
```

### Theme Validator
```bash
npm run validate-theme
```

### Accessibility Checker
```bash
npm run check-a11y
```

## 📖 Resources

### Design Files
- Figma: [NeuroColony Design System](#)
- Storybook: [Component Library](#)
- Icons: [Custom Icon Set](#)

### Documentation
- [Component API Reference](#)
- [Theming Guide](#)
- [Migration Guide](#)
- [Contributing Guidelines](#)

---

This design system is a living document. As NeuroColony evolves, so will our design language. Always prioritize user experience, accessibility, and performance while maintaining our unique AI-first aesthetic.