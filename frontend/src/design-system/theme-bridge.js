/**
 * Theme Bridge - JavaScript interface for TypeScript tokens
 * This file provides JavaScript access to the TypeScript theme tokens
 * and ensures type safety through <PERSON><PERSON><PERSON> comments
 */

import { colonyTheme } from './colony-theme.js'

// Re-export the TypeScript tokens as JavaScript-compatible objects
// Note: Since the project uses .ts files but components are .jsx, we need this bridge

/**
 * @typedef {Object} ColorShade
 * @property {string} 50
 * @property {string} 100
 * @property {string} 200
 * @property {string} 300
 * @property {string} 400
 * @property {string} 500
 * @property {string} 600
 * @property {string} 700
 * @property {string} 800
 * @property {string} 900
 * @property {string} 950
 */

/**
 * @typedef {Object} ThemeColors
 * @property {ColorShade} neural - Neural purple colors
 * @property {ColorShade} colony - Colony gold colors
 * @property {ColorShade} intel - Intelligence blue colors
 * @property {ColorShade} honey - Honey amber colors (from colony-theme)
 * @property {ColorShade} swarm - Swarm green colors (from colony-theme)
 * @property {ColorShade} alert - Alert red colors (from colony-theme)
 */

// Map the TypeScript tokens to the existing colony theme
export const colors = {
  neural: colonyTheme.colors.neural,
  colony: colonyTheme.colors.honey, // Map colony to honey colors
  intel: colonyTheme.colors.neural, // Map intel to neural colors for consistency
  honey: colonyTheme.colors.honey,
  swarm: colonyTheme.colors.swarm,
  alert: colonyTheme.colors.alert,
  neutral: colonyTheme.colors.gray,
  
  // Semantic colors
  success: {
    light: colonyTheme.colors.swarm[300],
    base: colonyTheme.colors.swarm[500],
    dark: colonyTheme.colors.swarm[700],
  },
  
  warning: {
    light: colonyTheme.colors.honey[300],
    base: colonyTheme.colors.honey[500],
    dark: colonyTheme.colors.honey[700],
  },
  
  error: {
    light: colonyTheme.colors.alert[300],
    base: colonyTheme.colors.alert[500],
    dark: colonyTheme.colors.alert[700],
  },
  
  info: {
    light: colonyTheme.colors.neural[300],
    base: colonyTheme.colors.neural[500],
    dark: colonyTheme.colors.neural[700],
  },
  
  // Special effects
  effects: {
    neuralGlow: `rgba(14, 165, 233, 0.5)`, // Using neural-500
    colonyGlow: `rgba(245, 158, 11, 0.5)`, // Using honey-500
    intelGlow: `rgba(14, 165, 233, 0.5)`, // Using neural-500
    gradientStart: colonyTheme.colors.neural[500],
    gradientEnd: colonyTheme.colors.neural[700],
  },
}

// Typography configuration
export const typography = {
  fonts: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    secondary: "'Space Grotesk', 'Inter', sans-serif",
    mono: "'JetBrains Mono', 'Consolas', monospace",
  },
  
  sizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem',     // 128px
  },
  
  weights: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
}

// Spacing scale
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px - Touch target minimum
  12: '3rem',       // 48px
}

// Border radius scale
export const borderRadius = {
  none: '0',
  sm: '0.125rem',    // 2px
  base: '0.25rem',   // 4px
  md: '0.375rem',    // 6px
  lg: '0.5rem',      // 8px
  xl: '0.75rem',     // 12px
  '2xl': '1rem',     // 16px
  '3xl': '1.5rem',   // 24px
  full: '9999px',
}

// Shadow definitions
export const shadows = {
  xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  base: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: 'none',
  
  // Glow effects using colony theme colors
  glow: {
    neural: `0 0 20px ${colors.effects.neuralGlow}`,
    colony: `0 0 20px ${colors.effects.colonyGlow}`,
    intel: `0 0 20px ${colors.effects.intelGlow}`,
    neuralLarge: `0 0 30px rgba(14, 165, 233, 0.3), 0 0 60px rgba(14, 165, 233, 0.1)`,
  },
}

// Animation configuration
export const animations = {
  timing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  duration: {
    fast: '150ms',
    base: '200ms',
    slow: '300ms',
    slower: '500ms',
    slowest: '1000ms',
  },
}

// Responsive breakpoints
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1920px',
  '4xl': '2560px',
}

// Z-index scale
export const zIndex = {
  hide: -1,
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
  critical: 9999,
}

// Theme configurations
export const themes = {
  light: {
    name: 'light',
    colors: {
      // Use colony theme colors
      bgPrimary: colors.neutral[50],
      bgSecondary: colors.neutral[100],
      bgTertiary: colors.neutral[200],
      bgInverse: colors.neutral[900],
      
      surface: 'white',
      surfaceHover: colors.neutral[50],
      surfaceActive: colors.neutral[100],
      
      textPrimary: colors.neutral[900],
      textSecondary: colors.neutral[700],
      textTertiary: colors.neutral[500],
      textInverse: colors.neutral[50],
      
      borderDefault: colors.neutral[200],
      borderMedium: colors.neutral[300],
      borderStrong: colors.neutral[400],
      
      primary: colors.neural[600],
      primaryHover: colors.neural[700],
      primaryActive: colors.neural[800],
      
      secondary: colors.intel[600],
      secondaryHover: colors.intel[700],
      secondaryActive: colors.intel[800],
      
      accent: colors.colony[500],
      accentHover: colors.colony[600],
      accentActive: colors.colony[700],
    },
  },
  
  dark: {
    name: 'dark',
    colors: {
      bgPrimary: colors.neutral[950],
      bgSecondary: colors.neutral[900],
      bgTertiary: colors.neutral[800],
      bgInverse: colors.neutral[50],
      
      surface: colors.neutral[900],
      surfaceHover: colors.neutral[800],
      surfaceActive: colors.neutral[700],
      
      textPrimary: colors.neutral[50],
      textSecondary: colors.neutral[200],
      textTertiary: colors.neutral[400],
      textInverse: colors.neutral[900],
      
      borderDefault: colors.neutral[800],
      borderMedium: colors.neutral[700],
      borderStrong: colors.neutral[600],
      
      primary: colors.neural[500],
      primaryHover: colors.neural[400],
      primaryActive: colors.neural[600],
      
      secondary: colors.intel[500],
      secondaryHover: colors.intel[400],
      secondaryActive: colors.intel[600],
      
      accent: colors.colony[400],
      accentHover: colors.colony[300],
      accentActive: colors.colony[500],
    },
  },
}

// Utility function to get theme
export const getTheme = (themeName) => themes[themeName] || themes.light

// Utility function to generate CSS variables
export const getCSSVariables = (theme) => {
  const cssVars = {}
  
  // Colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    cssVars[`--color-${key}`] = value
  })
  
  // Typography
  Object.entries(typography.fonts).forEach(([key, value]) => {
    cssVars[`--font-${key}`] = value
  })
  
  Object.entries(typography.sizes).forEach(([key, value]) => {
    cssVars[`--text-${key}`] = value
  })
  
  // Spacing
  Object.entries(spacing).forEach(([key, value]) => {
    cssVars[`--space-${key}`] = value
  })
  
  // Shadows
  Object.entries(shadows).forEach(([key, value]) => {
    if (typeof value === 'string') {
      cssVars[`--shadow-${key}`] = value
    }
  })
  
  return cssVars
}

// Export all tokens as a single object
export const tokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
  breakpoints,
  zIndex,
  themes,
}

export default tokens