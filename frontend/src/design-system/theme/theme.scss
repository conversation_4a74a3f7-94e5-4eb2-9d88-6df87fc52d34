/**
 * NeuroColony Theme Stylesheet
 * Comprehensive theming with CSS custom properties
 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

// Base theme variables
:root {
  // Neural Purple
  --color-neural-50: #F5F3FF;
  --color-neural-100: #EDE9FE;
  --color-neural-200: #DDD6FE;
  --color-neural-300: #C4B5FD;
  --color-neural-400: #A78BFA;
  --color-neural-500: #8B5CF6;
  --color-neural-600: #7C3AED;
  --color-neural-700: #6D28D9;
  --color-neural-800: #5B21B6;
  --color-neural-900: #4C1D95;
  --color-neural-950: #2D1065;
  
  // Colony Gold
  --color-colony-50: #FFFBEB;
  --color-colony-100: #FEF3C7;
  --color-colony-200: #FDE68A;
  --color-colony-300: #FCD34D;
  --color-colony-400: #FBBF24;
  --color-colony-500: #F59E0B;
  --color-colony-600: #D97706;
  --color-colony-700: #B45309;
  --color-colony-800: #92400E;
  --color-colony-900: #78350F;
  
  // Intelligence Blue
  --color-intel-50: #EFF6FF;
  --color-intel-100: #DBEAFE;
  --color-intel-200: #BFDBFE;
  --color-intel-300: #93C5FD;
  --color-intel-400: #60A5FA;
  --color-intel-500: #3B82F6;
  --color-intel-600: #2563EB;
  --color-intel-700: #1D4ED8;
  --color-intel-800: #1E40AF;
  --color-intel-900: #1E3A8A;
  
  // Typography
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-secondary: 'Space Grotesk', 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', 'Consolas', monospace;
  
  // Type Scale
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;
  --text-8xl: 6rem;
  --text-9xl: 8rem;
  
  // Spacing
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;
  --space-1: 0.25rem;
  --space-1-5: 0.375rem;
  --space-2: 0.5rem;
  --space-2-5: 0.625rem;
  --space-3: 0.75rem;
  --space-3-5: 0.875rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-7: 1.75rem;
  --space-8: 2rem;
  --space-9: 2.25rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-14: 3.5rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-28: 7rem;
  --space-32: 8rem;
  --space-36: 9rem;
  --space-40: 10rem;
  --space-44: 11rem;
  --space-48: 12rem;
  --space-52: 13rem;
  --space-56: 14rem;
  --space-60: 15rem;
  --space-64: 16rem;
  --space-72: 18rem;
  --space-80: 20rem;
  --space-96: 24rem;
  
  // Border Radius
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  // Transitions
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  // Z-Index
  --z-hide: -1;
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  --z-critical: 9999;
  
  // Breakpoints (for reference in JS)
  --screen-xs: 475px;
  --screen-sm: 640px;
  --screen-md: 768px;
  --screen-lg: 1024px;
  --screen-xl: 1280px;
  --screen-2xl: 1536px;
  --screen-3xl: 1920px;
  --screen-4xl: 2560px;
}

// Light theme
[data-theme="light"] {
  // Backgrounds
  --color-bg-primary: #FAFAFA;
  --color-bg-secondary: #F5F5F5;
  --color-bg-tertiary: #E5E5E5;
  --color-bg-inverse: #171717;
  
  // Surfaces
  --color-surface: white;
  --color-surface-hover: #FAFAFA;
  --color-surface-active: #F5F5F5;
  
  // Text
  --color-text-primary: #171717;
  --color-text-secondary: #404040;
  --color-text-tertiary: #737373;
  --color-text-inverse: #FAFAFA;
  
  // Borders
  --color-border-default: #E5E5E5;
  --color-border-medium: #D4D4D4;
  --color-border-strong: #A3A3A3;
  
  // Brand Colors
  --color-primary: var(--color-neural-600);
  --color-primary-hover: var(--color-neural-700);
  --color-primary-active: var(--color-neural-800);
  
  --color-secondary: var(--color-intel-600);
  --color-secondary-hover: var(--color-intel-700);
  --color-secondary-active: var(--color-intel-800);
  
  --color-accent: var(--color-colony-500);
  --color-accent-hover: var(--color-colony-600);
  --color-accent-active: var(--color-colony-700);
  
  // Semantic Colors
  --color-success: #22C55E;
  --color-success-light: #86EFAC;
  --color-success-dark: #15803D;
  
  --color-warning: #EAB308;
  --color-warning-light: #FDE047;
  --color-warning-dark: #A16207;
  
  --color-error: #EF4444;
  --color-error-light: #FCA5A5;
  --color-error-dark: #B91C1C;
  
  --color-info: #3B82F6;
  --color-info-light: #93C5FD;
  --color-info-dark: #1E40AF;
  
  // Shadows
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  // Focus
  --color-focus-ring: var(--color-neural-500);
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
}

// Dark theme
[data-theme="dark"] {
  // Backgrounds
  --color-bg-primary: #0A0A0A;
  --color-bg-secondary: #171717;
  --color-bg-tertiary: #262626;
  --color-bg-inverse: #FAFAFA;
  
  // Surfaces
  --color-surface: #171717;
  --color-surface-hover: #262626;
  --color-surface-active: #404040;
  
  // Text
  --color-text-primary: #FAFAFA;
  --color-text-secondary: #E5E5E5;
  --color-text-tertiary: #A3A3A3;
  --color-text-inverse: #171717;
  
  // Borders
  --color-border-default: #262626;
  --color-border-medium: #404040;
  --color-border-strong: #525252;
  
  // Brand Colors
  --color-primary: var(--color-neural-500);
  --color-primary-hover: var(--color-neural-400);
  --color-primary-active: var(--color-neural-600);
  
  --color-secondary: var(--color-intel-500);
  --color-secondary-hover: var(--color-intel-400);
  --color-secondary-active: var(--color-intel-600);
  
  --color-accent: var(--color-colony-400);
  --color-accent-hover: var(--color-colony-300);
  --color-accent-active: var(--color-colony-500);
  
  // Semantic Colors
  --color-success: #22C55E;
  --color-success-light: #86EFAC;
  --color-success-dark: #15803D;
  
  --color-warning: #EAB308;
  --color-warning-light: #FDE047;
  --color-warning-dark: #A16207;
  
  --color-error: #EF4444;
  --color-error-light: #FCA5A5;
  --color-error-dark: #B91C1C;
  
  --color-info: #3B82F6;
  --color-info-light: #93C5FD;
  --color-info-dark: #1E40AF;
  
  // Shadows (with glow effects)
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.5);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.5), 0 1px 2px -1px rgb(0 0 0 / 0.5);
  --shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.5);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.75);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.5);
  
  // Glow Effects
  --glow-neural: 0 0 20px rgba(139, 92, 246, 0.5);
  --glow-colony: 0 0 20px rgba(251, 191, 36, 0.5);
  --glow-intel: 0 0 20px rgba(59, 130, 246, 0.5);
  --glow-neural-large: 0 0 30px rgba(139, 92, 246, 0.3), 0 0 60px rgba(139, 92, 246, 0.1);
  
  // Focus
  --color-focus-ring: var(--color-colony-400);
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
}

// High contrast theme
[data-theme="high-contrast"] {
  // Extreme contrast for accessibility
  --color-bg-primary: black;
  --color-bg-secondary: black;
  --color-bg-tertiary: #171717;
  --color-bg-inverse: white;
  
  --color-surface: black;
  --color-surface-hover: #171717;
  --color-surface-active: #262626;
  
  --color-text-primary: white;
  --color-text-secondary: white;
  --color-text-tertiary: #E5E5E5;
  --color-text-inverse: black;
  
  --color-border-default: white;
  --color-border-medium: white;
  --color-border-strong: white;
  
  --color-primary: #A78BFA;
  --color-primary-hover: #C4B5FD;
  --color-primary-active: #8B5CF6;
  
  --color-secondary: #60A5FA;
  --color-secondary-hover: #93C5FD;
  --color-secondary-active: #3B82F6;
  
  --color-accent: #FCD34D;
  --color-accent-hover: #FDE68A;
  --color-accent-active: #FBBF24;
  
  --color-focus-ring: #FCD34D;
  --focus-ring-width: 3px;
  --focus-ring-offset: 3px;
}

// Base styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color var(--transition-base), color var(--transition-base);
}

// Typography base styles
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  line-height: 1.25;
  margin-top: 0;
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
  margin-top: 0;
  margin-bottom: var(--space-4);
}

// Link styles
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-hover);
    text-decoration: underline;
  }
  
  &:focus-visible {
    outline: var(--focus-ring-width) solid var(--color-focus-ring);
    outline-offset: var(--focus-ring-offset);
    border-radius: var(--radius-sm);
  }
}

// Code styles
code, pre {
  font-family: var(--font-mono);
  font-size: 0.875em;
}

code {
  padding: var(--space-0-5) var(--space-1);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-default);
  border-radius: var(--radius-sm);
}

pre {
  padding: var(--space-4);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-default);
  border-radius: var(--radius-md);
  overflow-x: auto;
  
  code {
    padding: 0;
    background-color: transparent;
    border: none;
  }
}

// Selection styles
::selection {
  background-color: var(--color-primary);
  color: white;
}

// Focus styles
:focus-visible {
  outline: var(--focus-ring-width) solid var(--color-focus-ring);
  outline-offset: var(--focus-ring-offset);
}

// Scrollbar styles
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-strong);
  border-radius: var(--radius-full);
  border: 3px solid var(--color-bg-secondary);
  
  &:hover {
    background: var(--color-text-tertiary);
  }
}

// Utility classes
.font-primary { font-family: var(--font-primary); }
.font-secondary { font-family: var(--font-secondary); }
.font-mono { font-family: var(--font-mono); }

.transition-fast { transition-duration: var(--transition-fast); }
.transition-base { transition-duration: var(--transition-base); }
.transition-slow { transition-duration: var(--transition-slow); }
.transition-slower { transition-duration: var(--transition-slower); }

// Animation classes
@keyframes pulse-neural {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes flow-gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes colony-move {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(5px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(5px); }
  75% { transform: translateX(5px) translateY(5px); }
  100% { transform: translateX(0) translateY(0); }
}

.animate-pulse-neural {
  animation: pulse-neural 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-flow-gradient {
  background-size: 200% 200%;
  animation: flow-gradient 3s ease infinite;
}

.animate-colony-move {
  animation: colony-move 4s ease-in-out infinite;
}

// Gradient utilities
.gradient-neural {
  background: linear-gradient(135deg, var(--color-neural-500) 0%, var(--color-intel-500) 100%);
}

.gradient-colony {
  background: linear-gradient(135deg, var(--color-colony-400) 0%, var(--color-neural-500) 100%);
}

.gradient-intel {
  background: linear-gradient(135deg, var(--color-intel-500) 0%, var(--color-colony-400) 100%);
}

// Motion preferences
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Print styles
@media print {
  body {
    color: black;
    background: white;
  }
  
  a {
    color: black;
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
}