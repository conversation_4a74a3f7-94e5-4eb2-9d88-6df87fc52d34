// NeuroColony Design System - Custom Icon Components
// Colony-themed icons for the AI agent platform

import React from 'react';

// Colony Structure Icons
export const ColonyIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M12 2L2 7V12C2 16.5 4.5 20.74 12 22C19.5 20.74 22 16.5 22 12V7L12 2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 8V16M8 12H16" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2"/>
  </svg>
);

export const HiveIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M12 2L17.66 5V11L12 14L6.34 11V5L12 2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 14L17.66 17V23L12 20L6.34 23V17L12 14Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.34 5L12 8L17.66 5M6.34 17L12 20L17.66 17" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const NeuralNetworkIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="5" cy="12" r="2" stroke={color} strokeWidth="2"/>
    <circle cx="12" cy="5" r="2" stroke={color} strokeWidth="2"/>
    <circle cx="19" cy="12" r="2" stroke={color} strokeWidth="2"/>
    <circle cx="12" cy="19" r="2" stroke={color} strokeWidth="2"/>
    <path d="M6.5 10.5L10.5 6.5M13.5 6.5L17.5 10.5M17.5 13.5L13.5 17.5M10.5 17.5L6.5 13.5" stroke={color} strokeWidth="2" strokeLinecap="round"/>
    <circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2" fill={color} fillOpacity="0.2"/>
  </svg>
);

// Agent Type Icons
export const QueenAgentIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M12 2L14.5 7H19L15.5 10L17 15L12 12L7 15L8.5 10L5 7H9.5L12 2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill={color} fillOpacity="0.2"/>
    <circle cx="12" cy="20" r="2" stroke={color} strokeWidth="2"/>
    <path d="M12 15V18" stroke={color} strokeWidth="2"/>
  </svg>
);

export const WorkerAgentIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="8" r="3" stroke={color} strokeWidth="2"/>
    <path d="M12 11V16M9 14L12 16L15 14" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7 20H17" stroke={color} strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

export const ScoutAgentIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2"/>
    <path d="M12 2V6M12 18V22M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M2 12H6M18 12H22M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22" stroke={color} strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

// Task Type Icons
export const BlueprintIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <rect x="3" y="3" width="18" height="18" rx="2" stroke={color} strokeWidth="2"/>
    <path d="M3 9H21M9 21V9" stroke={color} strokeWidth="2"/>
    <path d="M14 13H18M14 17H18" stroke={color} strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

export const WorkflowIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <rect x="3" y="3" width="6" height="6" rx="1" stroke={color} strokeWidth="2"/>
    <rect x="15" y="3" width="6" height="6" rx="1" stroke={color} strokeWidth="2"/>
    <rect x="9" y="15" width="6" height="6" rx="1" stroke={color} strokeWidth="2"/>
    <path d="M6 9V12H12V15M18 9V12H12V15" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const IntegrationIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M8 12H16M12 8V16" stroke={color} strokeWidth="2" strokeLinecap="round"/>
    <circle cx="12" cy="12" r="9" stroke={color} strokeWidth="2"/>
    <circle cx="12" cy="5" r="2" fill={color}/>
    <circle cx="19" cy="12" r="2" fill={color}/>
    <circle cx="12" cy="19" r="2" fill={color}/>
    <circle cx="5" cy="12" r="2" fill={color}/>
  </svg>
);

// Status Icons
export const ActiveStatusIcon = ({ size = 24, color = '#4caf50', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="12" r="8" stroke={color} strokeWidth="2" fill={color} fillOpacity="0.2">
      <animate attributeName="r" values="8;10;8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="12" cy="12" r="4" fill={color}>
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
    </circle>
  </svg>
);

export const ProcessingIcon = ({ size = 24, color = '#00bcd4', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g>
      <circle cx="12" cy="12" r="8" stroke={color} strokeWidth="2" strokeDasharray="4 2" fill="none">
        <animateTransform attributeName="transform" type="rotate" from="0 12 12" to="360 12 12" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="12" cy="4" r="2" fill={color}>
        <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </svg>
);

export const IdleIcon = ({ size = 24, color = '#ffc107', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="12" r="8" stroke={color} strokeWidth="2" strokeDasharray="2 4" opacity="0.5"/>
    <circle cx="12" cy="12" r="3" fill={color} opacity="0.7"/>
  </svg>
);

// Analytics Icons
export const PerformanceIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M3 22V13M8 22V8M13 22V3M18 22V11" stroke={color} strokeWidth="2" strokeLinecap="round"/>
    <path d="M3 13L8 8L13 3L18 11" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const HealthIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M4 12H7L9 18L13 6L15 12H20" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2"/>
  </svg>
);

// Action Icons
export const DeployIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M12 2L19 8.5V15.5L12 22L5 15.5V8.5L12 2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 8L16 11V15L12 18L8 15V11L12 8Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill={color} fillOpacity="0.2"/>
    <path d="M12 8V18" stroke={color} strokeWidth="2"/>
  </svg>
);

export const ConfigureIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2"/>
    <path d="M12 1V6M12 18V23M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M1 12H6M18 12H23M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22" stroke={color} strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

export const SimulateIcon = ({ size = 24, color = 'currentColor', ...props }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2"/>
    <path d="M8 12L11 15L16 9" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 3V5M12 19V21M3 12H5M19 12H21" stroke={color} strokeWidth="2" strokeLinecap="round" opacity="0.5"/>
  </svg>
);

// Create an icon map for easy access
export const Icons = {
  // Structure
  Colony: ColonyIcon,
  Hive: HiveIcon,
  NeuralNetwork: NeuralNetworkIcon,
  
  // Agents
  QueenAgent: QueenAgentIcon,
  WorkerAgent: WorkerAgentIcon,
  ScoutAgent: ScoutAgentIcon,
  
  // Tasks
  Blueprint: BlueprintIcon,
  Workflow: WorkflowIcon,
  Integration: IntegrationIcon,
  
  // Status
  Active: ActiveStatusIcon,
  Processing: ProcessingIcon,
  Idle: IdleIcon,
  
  // Analytics
  Performance: PerformanceIcon,
  Health: HealthIcon,
  
  // Actions
  Deploy: DeployIcon,
  Configure: ConfigureIcon,
  Simulate: SimulateIcon,
};