@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import './styles/theme-variables.css';
@import './styles/neural-animations.css';
@import './styles/colony-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-800;
  }
  
  html {
    @apply bg-black;
  }
  
  body {
    @apply bg-gradient-to-br from-black via-gray-950 to-black text-gray-200 font-sans min-h-screen;
    font-feature-settings: "rlig" 1, "calt" 1;
    background-attachment: fixed;
  }
  
  /* Neural network background pattern */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(236, 72, 153, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
  }
  
  /* Ensure content is above background */
  #root {
    position: relative;
    z-index: 1;
  }
}

@layer components {
  /* Primary button with purple/pink gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-glow-purple transform hover:scale-105 min-h-[44px];
  }
  
  /* Secondary button with glass effect */
  .btn-secondary {
    @apply bg-gray-900/50 backdrop-blur-sm border border-purple-500/30 hover:border-purple-400/50 text-gray-200 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-gray-900/70 min-h-[44px];
  }
  
  /* Hexagonal card with neural glow */
  .card {
    @apply bg-gray-900/80 backdrop-blur-md rounded-xl shadow-2xl border border-purple-500/20 p-6 hover:shadow-glow-purple transition-all duration-300 hover:border-purple-500/40 hover:scale-[1.02];
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
  }
  
  /* Input field with purple focus */
  .input-field {
    @apply w-full px-4 py-3 bg-gray-900/50 backdrop-blur-sm border border-purple-500/30 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-400 outline-none transition-all duration-200 text-gray-200 placeholder-gray-500;
  }
  
  /* Colony gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 50%, #f59e0b 100%);
  }
  
  /* Neural network card design */
  .neural-card {
    @apply relative overflow-hidden bg-gray-900/60 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-2xl;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
  }
  
  .neural-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #8B5CF6, #EC4899, #8B5CF6);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: -1;
  }
  
  .neural-card:hover::before {
    opacity: 0.5;
    animation: glow 2s ease-in-out infinite;
  }
  
  /* Hexagon shape */
  .hexagon {
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
  }
  
  /* Neural connections animation */
  .neural-connection {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
    height: 1px;
    animation: neural-flow 3s linear infinite;
  }
  
  @keyframes neural-flow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
}