import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const PremiumAIGenerator = () => {
  const [providers, setProviders] = useState([]);
  const [selectedProvider, setSelectedProvider] = useState('openai');
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [prompt, setPrompt] = useState('');
  const [enhancedOptions, setEnhancedOptions] = useState({
    industry: '',
    targetAudience: '',
    tone: 'professional',
    goals: [],
    sequenceLength: 5
  });
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    fetchAvailableProviders();
  }, []);

  const fetchAvailableProviders = async () => {
    try {
      const response = await fetch('/api/premium-ai/providers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setProviders(data.data.providers);
        if (data.data.providers.length > 0) {
          setSelectedProvider(data.data.providers[0].name);
          if (data.data.providers[0].models.length > 0) {
            setSelectedModel(data.data.providers[0].models[0]);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching providers:', error);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/premium-ai/generate/sequence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          prompt,
          provider: selectedProvider,
          model: selectedModel,
          enhancedOptions,
          sequenceLength: enhancedOptions.sequenceLength
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setResult(data.data);
      } else {
        setError(data.error || 'Failed to generate sequence');
        
        // Handle upgrade prompts
        if (data.upgrade) {
          setError(
            <div className="space-y-2">
              <p>{data.error}</p>
              <a 
                href="/pricing" 
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
              >
                Upgrade to {data.upgrade.requiredPlan}
              </a>
            </div>
          );
        }
      }
    } catch (error) {
      console.error('Error generating sequence:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProviderChange = (provider) => {
    setSelectedProvider(provider);
    const providerData = providers.find(p => p.name === provider);
    if (providerData && providerData.models.length > 0) {
      setSelectedModel(providerData.models[0]);
    }
  };

  const selectedProviderData = providers.find(p => p.name === selectedProvider);

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Premium AI Generator
        </h1>
        <p className="text-gray-600">
          Generate high-quality email sequences using advanced AI models.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Configuration Panel */}
        <div className="lg:col-span-1 space-y-6">
          {/* AI Provider Selection */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              AI Provider
            </h3>
            
            <div className="space-y-4">
              {providers.map((provider) => (
                <div key={provider.name}>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="provider"
                      value={provider.name}
                      checked={selectedProvider === provider.name}
                      onChange={(e) => handleProviderChange(e.target.value)}
                      className="form-radio h-4 w-4 text-blue-600"
                    />
                    <div>
                      <span className="font-medium capitalize">
                        {provider.name}
                      </span>
                      <p className="text-sm text-gray-600">
                        {provider.description}
                      </p>
                    </div>
                  </label>
                </div>
              ))}
            </div>

            {/* Model Selection */}
            {selectedProviderData && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Model
                </label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {selectedProviderData.models.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>

          {/* Enhanced Options */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Advanced Options
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industry
                </label>
                <input
                  type="text"
                  value={enhancedOptions.industry}
                  onChange={(e) => setEnhancedOptions({
                    ...enhancedOptions,
                    industry: e.target.value
                  })}
                  placeholder="e.g., SaaS, E-commerce, Healthcare"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience
                </label>
                <input
                  type="text"
                  value={enhancedOptions.targetAudience}
                  onChange={(e) => setEnhancedOptions({
                    ...enhancedOptions,
                    targetAudience: e.target.value
                  })}
                  placeholder="e.g., Small business owners, Developers"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tone
                </label>
                <select
                  value={enhancedOptions.tone}
                  onChange={(e) => setEnhancedOptions({
                    ...enhancedOptions,
                    tone: e.target.value
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="friendly">Friendly</option>
                  <option value="authoritative">Authoritative</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sequence Length
                </label>
                <select
                  value={enhancedOptions.sequenceLength}
                  onChange={(e) => setEnhancedOptions({
                    ...enhancedOptions,
                    sequenceLength: parseInt(e.target.value)
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={3}>3 emails</option>
                  <option value={5}>5 emails</option>
                  <option value={7}>7 emails</option>
                  <option value={10}>10 emails</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Prompt Input */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Generation Prompt
            </h3>
            
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe the email sequence you want to generate. Be specific about the purpose, target audience, and desired outcomes..."
              rows={6}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            />
            
            <div className="mt-4 flex justify-between items-center">
              <p className="text-sm text-gray-600">
                {prompt.length}/2000 characters
              </p>
              
              <button
                onClick={handleGenerate}
                disabled={loading || !prompt.trim()}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    <span>Generating...</span>
                  </>
                ) : (
                  <span>Generate Sequence</span>
                )}
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div className="text-red-800">
                  {typeof error === 'string' ? error : error}
                </div>
              </div>
            </div>
          )}

          {/* Results Display */}
          {result && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Generated Sequence
              </h3>
              
              {/* Usage Stats */}
              {result.usage && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                  <div className="flex items-center justify-between text-sm">
                    <span>Tokens Used: {result.usage.totalTokens}</span>
                    <span>Estimated Cost: ${result.usage.estimatedCost?.toFixed(4) || '0.0000'}</span>
                    <span>Provider: {result.provider}</span>
                  </div>
                </div>
              )}
              
              {/* Email Sequence */}
              {result.sequence && (
                <div className="space-y-4">
                  {result.sequence.emails.map((email, index) => (
                    <div key={index} className="border border-gray-200 rounded-md p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">
                          Email {index + 1}
                        </h4>
                        <span className="text-sm text-gray-600">
                          {email.timing}
                        </span>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm font-medium text-gray-700">Subject:</span>
                          <p className="text-gray-900">{email.subject}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-700">Content:</span>
                          <div className="text-gray-900 whitespace-pre-wrap text-sm bg-gray-50 p-3 rounded">
                            {email.content}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* AI Analysis */}
              {result.aiAnalysis && (
                <div className="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
                  <h4 className="font-medium text-green-900 mb-2">AI Analysis</h4>
                  {result.aiAnalysis.overallStrategy && (
                    <p className="text-green-800 text-sm mb-2">
                      <strong>Strategy:</strong> {result.aiAnalysis.overallStrategy}
                    </p>
                  )}
                  {result.aiAnalysis.expectedPerformance && (
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-green-700">Open Rate:</span>
                        <p className="font-medium">{result.aiAnalysis.expectedPerformance.openRate}</p>
                      </div>
                      <div>
                        <span className="text-green-700">Click Rate:</span>
                        <p className="font-medium">{result.aiAnalysis.expectedPerformance.clickRate}</p>
                      </div>
                      <div>
                        <span className="text-green-700">Conversion Rate:</span>
                        <p className="font-medium">{result.aiAnalysis.expectedPerformance.conversionRate}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PremiumAIGenerator;