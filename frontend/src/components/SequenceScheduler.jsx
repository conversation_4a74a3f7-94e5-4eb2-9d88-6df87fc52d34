import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Calendar,
  Clock,
  Play,
  Pause,
  Settings,
  Users,
  Mail,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  RotateCcw,
  Zap
} from 'lucide-react'
import toast from 'react-hot-toast'

const SequenceScheduler = ({ sequenceId, emails, title }) => {
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCreateSchedule, setShowCreateSchedule] = useState(false)
  const [scheduleForm, setScheduleForm] = useState({
    name: '',
    startDate: '',
    startTime: '09:00',
    timezone: 'UTC',
    frequency: 'once',
    repeatInterval: 1,
    repeatUnit: 'weeks',
    endDate: '',
    audience: {
      type: 'all',
      segmentId: '',
      tags: [],
      criteria: {}
    },
    automationTriggers: {
      onSignup: false,
      onPurchase: false,
      onBehavior: false,
      behaviorType: ''
    },
    deliverySettings: {
      respectTimeZone: true,
      pauseOnWeekends: false,
      maxEmailsPerDay: 1,
      delayBetweenEmails: 1440 // minutes (24 hours)
    }
  })

  useEffect(() => {
    loadSchedules()
  }, [sequenceId])

  const loadSchedules = async () => {
    // Mock data - in production this would load from API
    setSchedules([
      {
        id: 1,
        name: 'Weekly Newsletter Automation',
        status: 'active',
        startDate: new Date(),
        nextRun: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        frequency: 'weekly',
        audience: { type: 'segment', count: 1250 },
        emailsSent: 3420,
        openRate: 24.5,
        clickRate: 3.2,
        triggers: ['onSignup']
      },
      {
        id: 2,
        name: 'New Customer Onboarding',
        status: 'paused',
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        nextRun: null,
        frequency: 'trigger',
        audience: { type: 'all', count: 890 },
        emailsSent: 2150,
        openRate: 31.2,
        clickRate: 8.7,
        triggers: ['onSignup', 'onPurchase']
      }
    ])
  }

  const createSchedule = async () => {
    setLoading(true)
    try {
      // Mock API call - in production this would create the schedule
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newSchedule = {
        id: Date.now(),
        name: scheduleForm.name,
        status: 'active',
        startDate: new Date(scheduleForm.startDate + 'T' + scheduleForm.startTime),
        nextRun: new Date(scheduleForm.startDate + 'T' + scheduleForm.startTime),
        frequency: scheduleForm.frequency,
        audience: { type: scheduleForm.audience.type, count: Math.floor(Math.random() * 1000 + 100) },
        emailsSent: 0,
        openRate: 0,
        clickRate: 0,
        triggers: Object.keys(scheduleForm.automationTriggers).filter(key => scheduleForm.automationTriggers[key])
      }
      
      setSchedules(prev => [newSchedule, ...prev])
      setShowCreateSchedule(false)
      toast.success('Sequence scheduled successfully!')
    } catch (error) {
      toast.error('Failed to create schedule')
    } finally {
      setLoading(false)
    }
  }

  const toggleScheduleStatus = async (scheduleId) => {
    setSchedules(prev => prev.map(schedule => 
      schedule.id === scheduleId 
        ? { ...schedule, status: schedule.status === 'active' ? 'paused' : 'active' }
        : schedule
    ))
    toast.success('Schedule status updated')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100'
      case 'paused': return 'text-yellow-600 bg-yellow-100'
      case 'completed': return 'text-blue-600 bg-blue-100'
      case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <Play className="h-3 w-3" />
      case 'paused': return <Pause className="h-3 w-3" />
      case 'completed': return <CheckCircle className="h-3 w-3" />
      case 'failed': return <AlertCircle className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  const formatNextRun = (date) => {
    if (!date) return 'Not scheduled'
    const now = new Date()
    const diff = date - now
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `in ${days} day${days > 1 ? 's' : ''}`
    if (hours > 0) return `in ${hours} hour${hours > 1 ? 's' : ''}`
    return 'Soon'
  }

  if (showCreateSchedule) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-lg p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Calendar className="h-6 w-6 text-primary-600" />
            <h3 className="text-lg font-semibold text-gray-900">Schedule Sequence</h3>
          </div>
          <button
            onClick={() => setShowCreateSchedule(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Schedule Name
              </label>
              <input
                type="text"
                value={scheduleForm.name}
                onChange={(e) => setScheduleForm({ ...scheduleForm, name: e.target.value })}
                className="input-field"
                placeholder="e.g., Weekly Newsletter"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Frequency
              </label>
              <select
                value={scheduleForm.frequency}
                onChange={(e) => setScheduleForm({ ...scheduleForm, frequency: e.target.value })}
                className="input-field"
              >
                <option value="once">One Time</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="trigger">Trigger Based</option>
              </select>
            </div>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date
              </label>
              <input
                type="date"
                value={scheduleForm.startDate}
                onChange={(e) => setScheduleForm({ ...scheduleForm, startDate: e.target.value })}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time
              </label>
              <input
                type="time"
                value={scheduleForm.startTime}
                onChange={(e) => setScheduleForm({ ...scheduleForm, startTime: e.target.value })}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={scheduleForm.timezone}
                onChange={(e) => setScheduleForm({ ...scheduleForm, timezone: e.target.value })}
                className="input-field"
              >
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
              </select>
            </div>
          </div>

          {/* Automation Triggers */}
          {scheduleForm.frequency === 'trigger' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Automation Triggers
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={scheduleForm.automationTriggers.onSignup}
                    onChange={(e) => setScheduleForm({
                      ...scheduleForm,
                      automationTriggers: { ...scheduleForm.automationTriggers, onSignup: e.target.checked }
                    })}
                    className="mr-2"
                  />
                  <span className="text-sm">When someone signs up</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={scheduleForm.automationTriggers.onPurchase}
                    onChange={(e) => setScheduleForm({
                      ...scheduleForm,
                      automationTriggers: { ...scheduleForm.automationTriggers, onPurchase: e.target.checked }
                    })}
                    className="mr-2"
                  />
                  <span className="text-sm">After a purchase</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={scheduleForm.automationTriggers.onBehavior}
                    onChange={(e) => setScheduleForm({
                      ...scheduleForm,
                      automationTriggers: { ...scheduleForm.automationTriggers, onBehavior: e.target.checked }
                    })}
                    className="mr-2"
                  />
                  <span className="text-sm">Based on behavior</span>
                </label>
              </div>
            </div>
          )}

          {/* Audience */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Audience
            </label>
            <select
              value={scheduleForm.audience.type}
              onChange={(e) => setScheduleForm({
                ...scheduleForm,
                audience: { ...scheduleForm.audience, type: e.target.value }
              })}
              className="input-field"
            >
              <option value="all">All Subscribers</option>
              <option value="segment">Specific Segment</option>
              <option value="tags">By Tags</option>
              <option value="criteria">Custom Criteria</option>
            </select>
          </div>

          {/* Delivery Settings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Delivery Settings
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={scheduleForm.deliverySettings.respectTimeZone}
                  onChange={(e) => setScheduleForm({
                    ...scheduleForm,
                    deliverySettings: { ...scheduleForm.deliverySettings, respectTimeZone: e.target.checked }
                  })}
                  className="mr-2"
                />
                <span className="text-sm">Respect subscriber timezone</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={scheduleForm.deliverySettings.pauseOnWeekends}
                  onChange={(e) => setScheduleForm({
                    ...scheduleForm,
                    deliverySettings: { ...scheduleForm.deliverySettings, pauseOnWeekends: e.target.checked }
                  })}
                  className="mr-2"
                />
                <span className="text-sm">Pause on weekends</span>
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowCreateSchedule(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={createSchedule}
              disabled={loading || !scheduleForm.name || !scheduleForm.startDate}
              className="btn-primary flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Calendar className="h-4 w-4 mr-2" />
                  Create Schedule
                </>
              )}
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Calendar className="h-6 w-6 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900">Sequence Scheduling</h3>
        </div>
        <button
          onClick={() => setShowCreateSchedule(true)}
          className="btn-primary flex items-center"
        >
          <Calendar className="h-4 w-4 mr-2" />
          Create Schedule
        </button>
      </div>

      {/* Schedules List */}
      {schedules.length > 0 ? (
        <div className="space-y-4">
          {schedules.map((schedule, index) => (
            <motion.div
              key={schedule.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              {/* Schedule Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(schedule.status)}`}>
                    {getStatusIcon(schedule.status)}
                    <span className="capitalize">{schedule.status}</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{schedule.name}</h4>
                    <p className="text-sm text-gray-500">
                      {schedule.frequency === 'trigger' ? 'Trigger-based' : `${schedule.frequency.charAt(0).toUpperCase() + schedule.frequency.slice(1)} delivery`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => toggleScheduleStatus(schedule.id)}
                    className={`p-2 rounded-lg ${
                      schedule.status === 'active' 
                        ? 'text-yellow-600 hover:bg-yellow-50' 
                        : 'text-green-600 hover:bg-green-50'
                    }`}
                    title={schedule.status === 'active' ? 'Pause' : 'Resume'}
                  >
                    {schedule.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Schedule Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Users className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-sm font-medium text-gray-600">Audience</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{schedule.audience.count.toLocaleString()}</span>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Mail className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm font-medium text-gray-600">Sent</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{schedule.emailsSent.toLocaleString()}</span>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <TrendingUp className="h-4 w-4 text-purple-500 mr-1" />
                    <span className="text-sm font-medium text-gray-600">Open Rate</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{schedule.openRate.toFixed(1)}%</span>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Zap className="h-4 w-4 text-orange-500 mr-1" />
                    <span className="text-sm font-medium text-gray-600">Click Rate</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{schedule.clickRate.toFixed(1)}%</span>
                </div>
              </div>

              {/* Schedule Details */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 text-sm">
                <div className="flex items-center space-x-4 text-gray-500">
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    Started: {schedule.startDate.toLocaleDateString()}
                  </span>
                  {schedule.nextRun && (
                    <span className="flex items-center">
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Next: {formatNextRun(schedule.nextRun)}
                    </span>
                  )}
                </div>
                {schedule.triggers.length > 0 && (
                  <div className="flex items-center space-x-1">
                    {schedule.triggers.map((trigger, i) => (
                      <span key={i} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        {trigger.replace('on', '').toLowerCase()}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-white rounded-lg shadow-lg"
        >
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Schedules Yet</h3>
          <p className="text-gray-500 mb-6">
            Schedule your email sequence to run automatically at the perfect time.
          </p>
          <button
            onClick={() => setShowCreateSchedule(true)}
            className="btn-primary flex items-center mx-auto"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Create Your First Schedule
          </button>
        </motion.div>
      )}
    </div>
  )
}

export default SequenceScheduler