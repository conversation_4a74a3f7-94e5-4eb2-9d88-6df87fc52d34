import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  TestTube,
  TrendingUp,
  Calendar,
  Users,
  Mail,
  Eye,
  MousePointer,
  Trophy,
  AlertCircle,
  Play,
  Square,
  BarChart3
} from 'lucide-react'
import toast from 'react-hot-toast'

const ABTestManager = ({ sequenceId, emails }) => {
  const [abTests, setAbTests] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedEmail, setSelectedEmail] = useState(0)
  const [showCreateTest, setShowCreateTest] = useState(false)
  const [testForm, setTestForm] = useState({
    testType: 'subject_line',
    variants: [
      { name: 'Original', content: '' },
      { name: 'Variant B', content: '' }
    ],
    trafficSplit: 50
  })

  useEffect(() => {
    loadABTestResults()
  }, [sequenceId])

  useEffect(() => {
    // Pre-fill original content when email is selected
    if (emails && emails[selectedEmail] && testForm.testType === 'subject_line') {
      setTestForm(prev => ({
        ...prev,
        variants: [
          { ...prev.variants[0], content: emails[selectedEmail].subject },
          prev.variants[1]
        ]
      }))
    }
  }, [selectedEmail, emails, testForm.testType])

  const loadABTestResults = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/${sequenceId}/ab-test/results`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAbTests(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to load A/B test results:', error)
    }
  }

  const startABTest = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/${sequenceId}/ab-test/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          emailIndex: selectedEmail,
          testType: testForm.testType,
          variants: testForm.variants,
          trafficSplit: testForm.trafficSplit
        })
      })

      const data = await response.json()
      if (data.success) {
        toast.success('A/B test started successfully!')
        setShowCreateTest(false)
        loadABTestResults()
      } else {
        toast.error(data.message || 'Failed to start A/B test')
      }
    } catch (error) {
      toast.error('Failed to start A/B test')
    } finally {
      setLoading(false)
    }
  }

  const stopABTest = async (emailIndex) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/${sequenceId}/ab-test/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ emailIndex })
      })

      const data = await response.json()
      if (data.success) {
        toast.success('A/B test stopped successfully!')
        loadABTestResults()
      } else {
        toast.error(data.message || 'Failed to stop A/B test')
      }
    } catch (error) {
      toast.error('Failed to stop A/B test')
    }
  }

  const simulateMetrics = async (emailIndex, variantName) => {
    try {
      const token = localStorage.getItem('token')
      
      // Simulate realistic metrics
      const sent = Math.floor(Math.random() * 50 + 20)
      const opened = Math.floor(sent * (0.2 + Math.random() * 0.3))
      const clicked = Math.floor(opened * (0.1 + Math.random() * 0.2))
      const converted = Math.floor(clicked * (0.05 + Math.random() * 0.15))

      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/${sequenceId}/ab-test/update-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          emailIndex,
          variantName,
          metrics: { sent, opened, clicked, converted }
        })
      })

      const data = await response.json()
      if (data.success) {
        toast.success(`Simulated metrics for ${variantName}`)
        loadABTestResults()
      }
    } catch (error) {
      toast.error('Failed to simulate metrics')
    }
  }

  const getTestStatusColor = (test) => {
    if (test.winnerDetermined) return 'text-green-600'
    if (new Date() - new Date(test.testStartDate) > 7 * 24 * 60 * 60 * 1000) return 'text-yellow-600'
    return 'text-blue-600'
  }

  const getTestStatusText = (test) => {
    if (test.winnerDetermined) return 'Completed'
    if (new Date() - new Date(test.testStartDate) > 7 * 24 * 60 * 60 * 1000) return 'Long Running'
    return 'Active'
  }

  if (showCreateTest) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-lg p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <TestTube className="h-6 w-6 text-primary-600" />
            <h3 className="text-lg font-semibold text-gray-900">Create A/B Test</h3>
          </div>
          <button
            onClick={() => setShowCreateTest(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Email Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Email to Test
            </label>
            <select
              value={selectedEmail}
              onChange={(e) => setSelectedEmail(parseInt(e.target.value))}
              className="input-field"
            >
              {emails?.map((email, index) => (
                <option key={index} value={index}>
                  Email {index + 1}: {email.subject?.substring(0, 50)}...
                </option>
              ))}
            </select>
          </div>

          {/* Test Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test Type
            </label>
            <select
              value={testForm.testType}
              onChange={(e) => setTestForm({ ...testForm, testType: e.target.value })}
              className="input-field"
            >
              <option value="subject_line">Subject Line</option>
              <option value="email_content">Email Content</option>
              <option value="send_time">Send Time</option>
            </select>
          </div>

          {/* Variants */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test Variants
            </label>
            <div className="space-y-4">
              {testForm.variants.map((variant, index) => (
                <div key={index}>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    {variant.name}
                  </label>
                  <textarea
                    value={variant.content}
                    onChange={(e) => {
                      const newVariants = [...testForm.variants]
                      newVariants[index].content = e.target.value
                      setTestForm({ ...testForm, variants: newVariants })
                    }}
                    className="input-field"
                    rows="3"
                    placeholder={`Enter ${testForm.testType.replace('_', ' ')} for ${variant.name}`}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Traffic Split */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Traffic Split for Variant B ({testForm.trafficSplit}%)
            </label>
            <input
              type="range"
              min="10"
              max="50"
              value={testForm.trafficSplit}
              onChange={(e) => setTestForm({ ...testForm, trafficSplit: parseInt(e.target.value) })}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Original: {100 - testForm.trafficSplit}%</span>
              <span>Variant B: {testForm.trafficSplit}%</span>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowCreateTest(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={startABTest}
              disabled={loading || !testForm.variants[0].content || !testForm.variants[1].content}
              className="btn-primary flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Starting...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start A/B Test
                </>
              )}
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <TestTube className="h-6 w-6 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900">A/B Testing</h3>
        </div>
        <button
          onClick={() => setShowCreateTest(true)}
          className="btn-primary flex items-center"
        >
          <TestTube className="h-4 w-4 mr-2" />
          Create A/B Test
        </button>
      </div>

      {/* Active Tests */}
      {abTests.length > 0 ? (
        <div className="space-y-4">
          {abTests.map((test, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              {/* Test Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <h4 className="font-medium text-gray-900">
                      Email {test.emailIndex + 1}: {test.emailSubject?.substring(0, 40)}...
                    </h4>
                    <p className="text-sm text-gray-500">
                      Testing: {test.testType.replace('_', ' ')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`text-sm font-medium ${getTestStatusColor(test)}`}>
                    {getTestStatusText(test)}
                  </span>
                  {!test.winnerDetermined && (
                    <button
                      onClick={() => stopABTest(test.emailIndex)}
                      className="text-red-600 hover:text-red-700 p-1"
                      title="Stop Test"
                    >
                      <Square className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Test Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {test.variants.map((variant, vIndex) => (
                  <div
                    key={vIndex}
                    className={`p-4 rounded-lg border ${
                      test.winnerDetermined && test.winningVariant === variant.name
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium text-gray-900 flex items-center">
                        {variant.name}
                        {test.winnerDetermined && test.winningVariant === variant.name && (
                          <Trophy className="h-4 w-4 text-green-600 ml-2" />
                        )}
                      </h5>
                      {!test.winnerDetermined && (
                        <button
                          onClick={() => simulateMetrics(test.emailIndex, variant.name)}
                          className="text-xs text-primary-600 hover:text-primary-700"
                        >
                          Simulate Data
                        </button>
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-3 font-mono bg-gray-50 p-2 rounded">
                      {variant.content?.substring(0, 60)}...
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center">
                        <Users className="h-3 w-3 text-gray-400 mr-1" />
                        <span>{variant.sentCount} sent</span>
                      </div>
                      <div className="flex items-center">
                        <Eye className="h-3 w-3 text-blue-500 mr-1" />
                        <span>{variant.openRate}% open</span>
                      </div>
                      <div className="flex items-center">
                        <MousePointer className="h-3 w-3 text-green-500 mr-1" />
                        <span>{variant.clickRate}% click</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-3 w-3 text-purple-500 mr-1" />
                        <span>{variant.conversionRate}% convert</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Test Info */}
              <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center space-x-4">
                  <span className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Started: {new Date(test.testStartDate).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <BarChart3 className="h-3 w-3 mr-1" />
                    Split: {100 - test.trafficSplit}% / {test.trafficSplit}%
                  </span>
                </div>
                {test.winnerDetermined && test.testEndDate && (
                  <span className="flex items-center text-green-600">
                    <Trophy className="h-3 w-3 mr-1" />
                    Completed: {new Date(test.testEndDate).toLocaleDateString()}
                  </span>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-white rounded-lg shadow-lg"
        >
          <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No A/B Tests Yet</h3>
          <p className="text-gray-500 mb-6">
            Start testing different versions of your emails to improve conversion rates.
          </p>
          <button
            onClick={() => setShowCreateTest(true)}
            className="btn-primary flex items-center mx-auto"
          >
            <TestTube className="h-4 w-4 mr-2" />
            Create Your First A/B Test
          </button>
        </motion.div>
      )}
    </div>
  )
}

export default ABTestManager