import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CreditCard, Shield, Check, ArrowRight, Loader2 } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { toast } from 'react-hot-toast'

const EnhancedPaymentOptions = ({ selectedPlan, onSuccess }) => {
  const { user, getApiClient } = useAuth()
  const [paymentMethod, setPaymentMethod] = useState('stripe')
  const [loading, setLoading] = useState(false)
  const [processingPayment, setProcessingPayment] = useState(false)

  const plans = {
    pro: {
      name: 'Pro Plan',
      price: 29,
      features: ['75 sequences/month', 'Advanced AI features', 'Priority support', 'API access']
    },
    business: {
      name: 'Business Plan', 
      price: 99,
      features: ['200 sequences/month', 'Team collaboration', 'Custom integrations', 'Analytics dashboard']
    },
    enterprise: {
      name: 'Enterprise Plan',
      price: 299,
      features: ['Unlimited sequences', 'White-label options', 'Dedicated support', 'Custom development']
    }
  }

  const currentPlan = plans[selectedPlan]

  const handleStripePayment = async () => {
    try {
      setProcessingPayment(true)
      const apiClient = getApiClient()
      
      const response = await apiClient.post('/payments/create-checkout-session', {
        planId: selectedPlan,
        successUrl: `${window.location.origin}/payment/success?provider=stripe`,
        cancelUrl: `${window.location.origin}/pricing?cancelled=true`
      })

      if (response.data.success) {
        // Redirect to Stripe Checkout
        window.location.href = response.data.url
      } else {
        throw new Error(response.data.message || 'Failed to create checkout session')
      }
    } catch (error) {
      console.error('Stripe payment error:', error)
      toast.error('Failed to initialize Stripe payment. Please try again.')
      setProcessingPayment(false)
    }
  }

  const handlePayPalPayment = async () => {
    try {
      setProcessingPayment(true)
      const apiClient = getApiClient()
      
      const response = await apiClient.post('/paypal/create-subscription', {
        planId: selectedPlan
      })

      if (response.data.success) {
        // Redirect to PayPal for approval
        window.location.href = response.data.approvalUrl
      } else {
        throw new Error(response.data.message || 'Failed to create PayPal subscription')
      }
    } catch (error) {
      console.error('PayPal payment error:', error)
      toast.error('Failed to initialize PayPal payment. Please try again.')
      setProcessingPayment(false)
    }
  }

  const handlePayment = () => {
    if (paymentMethod === 'stripe') {
      handleStripePayment()
    } else if (paymentMethod === 'paypal') {
      handlePayPalPayment()
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Plan Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-8 mb-8"
      >
        <div className="text-center mb-6">
          <h3 className="text-2xl font-light text-white mb-2">{currentPlan.name}</h3>
          <div className="text-4xl font-light text-white">
            ${currentPlan.price}
            <span className="text-lg text-slate-400 ml-2">/month</span>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          {currentPlan.features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
              <span className="text-slate-300">{feature}</span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Payment Method Selection */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-8"
      >
        <h4 className="text-xl font-medium text-white mb-6 text-center">
          Choose Payment Method
        </h4>

        <div className="grid md:grid-cols-2 gap-4">
          {/* Stripe Option */}
          <motion.button
            type="button"
            onClick={() => setPaymentMethod('stripe')}
            className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
              paymentMethod === 'stripe'
                ? 'border-purple-500 bg-purple-500/10'
                : 'border-slate-600 bg-slate-800/30 hover:border-slate-500'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-center mb-4">
              <div className={`p-3 rounded-lg ${
                paymentMethod === 'stripe' ? 'bg-purple-500/20' : 'bg-slate-700/50'
              }`}>
                <CreditCard className={`w-6 h-6 ${
                  paymentMethod === 'stripe' ? 'text-purple-400' : 'text-slate-400'
                }`} />
              </div>
            </div>
            
            <h5 className="text-lg font-medium text-white mb-2">Credit Card</h5>
            <p className="text-sm text-slate-400 mb-4">
              Secure payment via Stripe
            </p>
            
            <div className="flex items-center justify-center gap-2">
              <Shield className="w-4 h-4 text-green-400" />
              <span className="text-xs text-green-400">SSL Encrypted</span>
            </div>

            {paymentMethod === 'stripe' && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center"
              >
                <Check className="w-4 h-4 text-white" />
              </motion.div>
            )}
          </motion.button>

          {/* PayPal Option */}
          <motion.button
            type="button"
            onClick={() => setPaymentMethod('paypal')}
            className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
              paymentMethod === 'paypal'
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-slate-600 bg-slate-800/30 hover:border-slate-500'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-center mb-4">
              <div className={`p-3 rounded-lg ${
                paymentMethod === 'paypal' ? 'bg-blue-500/20' : 'bg-slate-700/50'
              }`}>
                <svg 
                  className={`w-6 h-6 ${
                    paymentMethod === 'paypal' ? 'text-blue-400' : 'text-slate-400'
                  }`}
                  viewBox="0 0 24 24" 
                  fill="currentColor"
                >
                  <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.408-.034c-.518 0-1.05.034-1.614.117-.564.083-1.32.258-2.218.525l-1.65.49c-.524.15-.968.382-1.05.9L13.16 15.97c-.082.518-.026.9.165 1.15.191.25.574.382 1.098.232l1.65-.49c.898-.267 1.654-.442 2.218-.525.564-.083 1.096-.117 1.614-.117.137 0 .275.012.408.034 2.57 0 4.578-.543 5.69-1.81 1.01-1.15 1.304-2.42 1.012-4.287-.292-1.867-1.81-3.147-4.833-3.147z"/>
                </svg>
              </div>
            </div>
            
            <h5 className="text-lg font-medium text-white mb-2">PayPal</h5>
            <p className="text-sm text-slate-400 mb-4">
              Pay with PayPal account
            </p>
            
            <div className="flex items-center justify-center gap-2">
              <Shield className="w-4 h-4 text-green-400" />
              <span className="text-xs text-green-400">Buyer Protection</span>
            </div>

            {paymentMethod === 'paypal' && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <Check className="w-4 h-4 text-white" />
              </motion.div>
            )}
          </motion.button>
        </div>
      </motion.div>

      {/* Security Notice */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-xl p-4 mb-8"
      >
        <div className="flex items-center gap-3 text-green-300">
          <Shield className="w-5 h-5" />
          <div className="text-sm">
            <span className="font-medium">Secure Payment:</span> Your payment information is encrypted and secure. 
            We never store your payment details.
          </div>
        </div>
      </motion.div>

      {/* Payment Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-center"
      >
        <button
          onClick={handlePayment}
          disabled={processingPayment}
          className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-medium text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          {processingPayment ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CreditCard className="w-5 h-5" />
              {paymentMethod === 'stripe' ? 'Continue with Stripe' : 'Continue with PayPal'}
              <ArrowRight className="w-5 h-5" />
            </>
          )}
        </button>

        <p className="text-xs text-slate-400 mt-4">
          You can cancel your subscription at any time. No long-term commitments.
        </p>
      </motion.div>
    </div>
  )
}

export default EnhancedPaymentOptions