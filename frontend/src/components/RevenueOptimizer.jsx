import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, DollarSign, Target, Zap, Brain, 
  BarChart3, PieChart, ArrowUpRight, ArrowDownRight,
  Users, Mail, Clock, Star, Lightbulb, Rocket,
  Settings, RefreshCw, Download, Filter
} from 'lucide-react';
import toast from 'react-hot-toast';

const RevenueOptimizer = ({ user }) => {
  const [revenueData, setRevenueData] = useState({
    totalRevenue: 0,
    monthlyGrowth: 0,
    conversionRate: 0,
    averageLTV: 0,
    churnRate: 0,
    upgradeRate: 0
  });
  
  const [optimizations, setOptimizations] = useState([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchRevenueData();
    fetchOptimizations();
  }, [selectedTimeframe]);

  const fetchRevenueData = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/revenue-optimization/analytics?timeframe=${selectedTimeframe}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setRevenueData(data.analytics);
      }
    } catch (error) {
      console.error('Error fetching revenue data:', error);
    }
  };

  const fetchOptimizations = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/revenue-optimization/suggestions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setOptimizations(data.suggestions);
      }
    } catch (error) {
      console.error('Error fetching optimizations:', error);
    }
  };

  const runRevenueAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/revenue-optimization/analyze`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        await fetchRevenueData();
        await fetchOptimizations();
        toast.success('Revenue analysis completed!');
      } else {
        toast.error('Failed to run revenue analysis');
      }
    } catch (error) {
      toast.error('Error running revenue analysis');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const applyOptimization = async (optimizationId) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/revenue-optimization/apply/${optimizationId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        toast.success('Optimization applied successfully!');
        fetchOptimizations();
      } else {
        toast.error('Failed to apply optimization');
      }
    } catch (error) {
      toast.error('Error applying optimization');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getImpactColor = (impact) => {
    if (impact >= 20) return 'text-green-600 bg-green-100';
    if (impact >= 10) return 'text-blue-600 bg-blue-100';
    return 'text-yellow-600 bg-yellow-100';
  };

  const getPriorityColor = (priority) => {
    if (priority === 'high') return 'bg-red-100 text-red-800';
    if (priority === 'medium') return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'optimizations', label: 'AI Recommendations', icon: Brain },
    { id: 'trends', label: 'Revenue Trends', icon: TrendingUp }
  ];

  const timeframes = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' }
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900 rounded-xl p-8 text-white mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Rocket className="h-8 w-8 text-green-400" />
              <h1 className="text-3xl font-bold">Revenue Optimization Engine</h1>
            </div>
            <p className="text-green-200 text-lg">
              AI-powered revenue analysis with conversion optimization and growth strategies
            </p>
          </div>
          <div className="flex items-center gap-4">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="bg-white/10 text-white border border-white/20 rounded-lg px-4 py-2"
            >
              {timeframes.map(tf => (
                <option key={tf.value} value={tf.value} className="text-black">
                  {tf.label}
                </option>
              ))}
            </select>
            <button
              onClick={runRevenueAnalysis}
              disabled={isAnalyzing}
              className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-6 rounded-lg transition-colors flex items-center gap-2"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-5 w-5" />
                  Analyze Now
                </>
              )}
            </button>
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${
              activeTab === tab.id
                ? 'border-b-2 border-green-600 text-green-600'
                : 'text-gray-600 hover:text-green-600'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-8"
        >
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(revenueData.totalRevenue)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600 text-sm font-medium">
                  +{formatPercent(revenueData.monthlyGrowth)} this month
                </span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Conversion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercent(revenueData.conversionRate)}
                  </p>
                </div>
                <Target className="h-8 w-8 text-blue-500" />
              </div>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-blue-600 text-sm font-medium">
                  Above industry average
                </span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Average LTV</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(revenueData.averageLTV)}
                  </p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-purple-500 mr-1" />
                <span className="text-purple-600 text-sm font-medium">
                  +{formatPercent(revenueData.upgradeRate)} upgrade rate
                </span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Churn Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercent(revenueData.churnRate)}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
              <div className="flex items-center mt-2">
                <ArrowDownRight className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600 text-sm font-medium">
                  Decreasing (good!)
                </span>
              </div>
            </div>
          </div>

          {/* Quick Insights */}
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
              <Lightbulb className="h-6 w-6 text-yellow-500" />
              Quick Insights
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="font-bold text-green-800">Revenue Growth</div>
                <div className="text-green-700 text-sm mt-1">
                  Your revenue is growing {formatPercent(revenueData.monthlyGrowth)} month-over-month, 
                  indicating strong product-market fit.
                </div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="font-bold text-blue-800">Conversion Opportunity</div>
                <div className="text-blue-700 text-sm mt-1">
                  Optimizing your email sequences could increase conversions by an estimated 15-25%.
                </div>
              </div>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="font-bold text-purple-800">LTV Optimization</div>
                <div className="text-purple-700 text-sm mt-1">
                  Focus on increasing user engagement to boost lifetime value by {formatPercent(12)}.
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* AI Recommendations Tab */}
      {activeTab === 'optimizations' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-6"
        >
          {optimizations.length === 0 ? (
            <div className="bg-white rounded-xl p-8 shadow-lg text-center">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">No Optimizations Available</h3>
              <p className="text-gray-600 mb-4">Run an analysis to get AI-powered revenue optimization recommendations.</p>
              <button
                onClick={runRevenueAnalysis}
                className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg transition-colors"
              >
                Run Analysis
              </button>
            </div>
          ) : (
            <div className="grid gap-6">
              {optimizations.map((optimization, index) => (
                <motion.div
                  key={optimization.id || index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:border-green-300 transition-colors"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-lg font-bold text-gray-900">
                          {optimization.title || 'Revenue Optimization'}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(optimization.priority || 'medium')}`}>
                          {optimization.priority || 'Medium'} Priority
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(optimization.impact || 15)}`}>
                          +{optimization.impact || 15}% Revenue Impact
                        </span>
                      </div>
                      <p className="text-gray-600 mb-3">
                        {optimization.description || 'AI-powered optimization recommendation to increase revenue and improve conversion rates.'}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {optimization.timeToImplement || '2-3'} days to implement
                        </span>
                        <span className="flex items-center gap-1">
                          <Star className="h-4 w-4" />
                          {optimization.confidenceScore || 85}% confidence
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => applyOptimization(optimization.id || index)}
                      className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
                    >
                      <Zap className="h-4 w-4" />
                      Apply
                    </button>
                  </div>
                  
                  {optimization.metrics && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h5 className="font-bold mb-2">Expected Improvements:</h5>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Conversion Rate</div>
                          <div className="font-bold text-green-600">
                            +{optimization.metrics.conversionIncrease || 12}%
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Revenue</div>
                          <div className="font-bold text-green-600">
                            +{formatCurrency(optimization.metrics.revenueIncrease || 2500)}/month
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">User Engagement</div>
                          <div className="font-bold text-green-600">
                            +{optimization.metrics.engagementIncrease || 18}%
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      )}

      {/* Revenue Trends Tab */}
      {activeTab === 'trends' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-white rounded-xl p-6 shadow-lg"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold flex items-center gap-2">
              <TrendingUp className="h-6 w-6 text-green-600" />
              Revenue Trends Analysis
            </h3>
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </button>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-8">
            <div>
              <h4 className="font-bold mb-4">Revenue Growth Trajectory</h4>
              <div className="bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  +{formatPercent(revenueData.monthlyGrowth)}
                </div>
                <div className="text-gray-600">Monthly Growth Rate</div>
                <div className="mt-4 text-sm text-gray-500">
                  Projected: {formatCurrency(revenueData.totalRevenue * 1.15)} next month
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Key Performance Indicators</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">Customer Acquisition Cost</span>
                  <span className="font-bold">{formatCurrency(45)}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">Monthly Recurring Revenue</span>
                  <span className="font-bold">{formatCurrency(revenueData.totalRevenue * 0.8)}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">Revenue per User</span>
                  <span className="font-bold">{formatCurrency(29)}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-700">Payback Period</span>
                  <span className="font-bold">1.6 months</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default RevenueOptimizer;