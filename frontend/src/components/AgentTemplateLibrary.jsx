import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  Eye, 
  Download, 
  ChevronRight, 
  Tag, 
  Zap,
  Brain,
  Users,
  Layers,
  GitBranch,
  Activity,
  BarChart,
  Target,
  Palette,
  Bot,
  Sparkles,
  Shield,
  Network,
  Cpu,
  Database,
  Cloud,
  Lock,
  Star,
  TrendingUp,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { Card } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Icons } from '../design-system/icons.jsx'
import { colors } from '../design-system/colors'
import AnimatedBackground from './AnimatedBackground'
import toast from 'react-hot-toast'

const AgentTemplateLibrary = ({ onSelectTemplate }) => {
  const [templates, setTemplates] = useState([])
  const [filteredTemplates, setFilteredTemplates] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState('grid') // grid or honeycomb

  // Agent template categories
  const categories = [
    { id: 'all', name: 'All Blueprints', icon: Icons.Colony, count: 24, color: 'neural' },
    { id: 'agent-blueprints', name: 'Agent Blueprints', icon: Bot, count: 8, color: 'neural' },
    { id: 'neural-workflows', name: 'Neural Workflows', icon: GitBranch, count: 6, color: 'network' },
    { id: 'colony-architectures', name: 'Colony Architectures', icon: Icons.Hive, count: 4, color: 'honey' },
    { id: 'analytics-dashboards', name: 'Analytics Dashboards', icon: BarChart, count: 3, color: 'organic' },
    { id: 'task-automation', name: 'Task Automation', icon: Target, count: 5, color: 'colony' },
    { id: 'integration-connectors', name: 'Integration Connectors', icon: Network, count: 4, color: 'network' },
    { id: 'creative-generators', name: 'Creative Generators', icon: Palette, count: 3, color: 'honey' },
    { id: 'business-intelligence', name: 'Business Intelligence', icon: TrendingUp, count: 3, color: 'neural' }
  ]

  useEffect(() => {
    loadTemplates()
  }, [])

  useEffect(() => {
    filterTemplates()
  }, [searchQuery, selectedCategory, templates])

  const loadTemplates = async () => {
    try {
      // Mock AI agent templates
      const mockTemplates = [
        // Agent Blueprints
        {
          id: 'agent-data-analyst',
          name: 'Data Analyst Queen',
          category: 'agent-blueprints',
          description: 'Orchestrates complex data analysis workflows with worker agents',
          tags: ['analytics', 'queen', 'data', 'orchestrator'],
          agentCount: 5,
          complexity: 'Advanced',
          successRate: 94,
          deployments: 3420,
          capabilities: ['Data Mining', 'Pattern Recognition', 'Report Generation', 'Predictive Analysis'],
          requirements: { cpu: 'Medium', memory: 'High', apis: ['Database', 'Analytics'] },
          topology: {
            queen: 1,
            workers: 3,
            scouts: 1
          }
        },
        {
          id: 'agent-content-creator',
          name: 'Content Creation Hive',
          category: 'agent-blueprints',
          description: 'Multi-agent system for generating diverse content types',
          tags: ['content', 'creative', 'generator', 'hive'],
          agentCount: 8,
          complexity: 'Intermediate',
          successRate: 89,
          deployments: 2150,
          capabilities: ['Blog Writing', 'Social Media', 'Image Generation', 'SEO Optimization'],
          requirements: { cpu: 'Low', memory: 'Medium', apis: ['GPT-4', 'DALL-E'] },
          topology: {
            queen: 1,
            workers: 5,
            scouts: 2
          }
        },
        // Neural Workflows
        {
          id: 'workflow-customer-journey',
          name: 'Customer Journey Orchestrator',
          category: 'neural-workflows',
          description: 'Maps and optimizes complete customer interactions across touchpoints',
          tags: ['customer', 'journey', 'automation', 'neural'],
          agentCount: 12,
          complexity: 'Expert',
          successRate: 91,
          deployments: 1890,
          capabilities: ['Journey Mapping', 'Touchpoint Analysis', 'Personalization', 'Conversion Optimization'],
          requirements: { cpu: 'High', memory: 'High', apis: ['CRM', 'Analytics', 'Email'] },
          topology: {
            queen: 2,
            workers: 8,
            scouts: 2
          }
        },
        // Colony Architectures
        {
          id: 'colony-research-lab',
          name: 'Research Laboratory Colony',
          category: 'colony-architectures',
          description: 'Complete AI research facility with specialized agent roles',
          tags: ['research', 'colony', 'laboratory', 'science'],
          agentCount: 20,
          complexity: 'Expert',
          successRate: 96,
          deployments: 567,
          capabilities: ['Hypothesis Testing', 'Data Collection', 'Experiment Design', 'Result Analysis'],
          requirements: { cpu: 'Very High', memory: 'Very High', apis: ['Research DB', 'Computing Cluster'] },
          topology: {
            queen: 3,
            workers: 12,
            scouts: 5
          }
        },
        // Task Automation
        {
          id: 'automation-devops',
          name: 'DevOps Automation Swarm',
          category: 'task-automation',
          description: 'Automates CI/CD pipelines and infrastructure management',
          tags: ['devops', 'automation', 'deployment', 'infrastructure'],
          agentCount: 7,
          complexity: 'Advanced',
          successRate: 93,
          deployments: 4230,
          capabilities: ['CI/CD', 'Monitoring', 'Auto-scaling', 'Incident Response'],
          requirements: { cpu: 'Medium', memory: 'Medium', apis: ['GitHub', 'AWS', 'Docker'] },
          topology: {
            queen: 1,
            workers: 4,
            scouts: 2
          }
        },
        // Creative Generators
        {
          id: 'creative-design-studio',
          name: 'Design Studio Colony',
          category: 'creative-generators',
          description: 'AI-powered design generation with multiple creative agents',
          tags: ['design', 'creative', 'visual', 'studio'],
          agentCount: 10,
          complexity: 'Intermediate',
          successRate: 87,
          deployments: 1420,
          capabilities: ['Logo Design', 'Brand Guidelines', 'UI/UX', 'Animation'],
          requirements: { cpu: 'High', memory: 'Very High', apis: ['DALL-E 3', 'Midjourney'] },
          topology: {
            queen: 1,
            workers: 7,
            scouts: 2
          }
        }
      ]
      
      setTemplates(mockTemplates)
      setFilteredTemplates(mockTemplates)
      setLoading(false)
    } catch (error) {
      toast.error('Failed to load agent templates')
      setLoading(false)
    }
  }

  const filterTemplates = () => {
    let filtered = templates

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => t.category === selectedCategory)
    }

    if (searchQuery) {
      filtered = filtered.filter(t =>
        t.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredTemplates(filtered)
  }

  const handleDeployTemplate = (template) => {
    if (onSelectTemplate) {
      onSelectTemplate(template)
    } else {
      toast.success(`Deploying colony: ${template.name}`)
    }
  }

  const getComplexityColor = (complexity) => {
    switch(complexity) {
      case 'Beginner': return 'success'
      case 'Intermediate': return 'warning'
      case 'Advanced': return 'primary'
      case 'Expert': return 'error'
      default: return 'default'
    }
  }

  const getComplexityIcon = (complexity) => {
    switch(complexity) {
      case 'Beginner': return <Sparkles size={16} />
      case 'Intermediate': return <Cpu size={16} />
      case 'Advanced': return <Brain size={16} />
      case 'Expert': return <Shield size={16} />
      default: return <Activity size={16} />
    }
  }

  return (
    <div className="min-h-screen bg-system-background">
      <AnimatedBackground variant="network" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <Icons.Hive size={48} className="text-honey-500" />
            <h1 className="text-4xl font-bold text-white">
              Agent Colony Templates
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Deploy pre-built AI agent colonies for any task. Each template includes specialized agents, 
            neural workflows, and optimized collaboration patterns.
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Card className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search agent templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-neural-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'grid' ? 'honeycomb' : 'grid')}
                  className="hidden lg:flex"
                >
                  <Icons.Hive size={20} className={viewMode === 'honeycomb' ? 'text-honey-500' : ''} />
                </Button>
              </div>
            </div>

            {/* Category Filters */}
            <div className="mt-4 flex flex-wrap gap-2">
              {categories.map(category => {
                const Icon = category.icon
                const isActive = selectedCategory === category.id
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`
                      px-4 py-2 rounded-lg font-medium transition-all flex items-center gap-2
                      ${isActive 
                        ? `bg-${category.color}-500/20 text-${category.color}-400 border border-${category.color}-500/50` 
                        : 'bg-gray-800/50 text-gray-400 hover:text-white hover:bg-gray-800/70 border border-gray-700'
                      }
                    `}
                  >
                    <Icon size={18} />
                    {category.name}
                    <Badge variant="ghost" className="ml-1">
                      {category.count}
                    </Badge>
                  </button>
                )
              })}
            </div>
          </Card>
        </motion.div>

        {/* Templates Grid */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Icons.Processing size={48} color={colors.neural[400]} />
          </div>
        ) : (
          <div className={`
            grid gap-6 
            ${viewMode === 'honeycomb' 
              ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3 honeycomb-grid' 
              : 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3'
            }
          `}>
            <AnimatePresence mode="popLayout">
              {filteredTemplates.map((template, index) => (
                <motion.div
                  key={template.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="h-full overflow-hidden hover:shadow-xl transition-all duration-300 group">
                    {/* Template Header */}
                    <div className="p-6 border-b border-gray-800">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-neural-500/20 rounded-lg">
                            <Icons.Colony size={24} className="text-neural-400" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white group-hover:text-neural-400 transition-colors">
                              {template.name}
                            </h3>
                            <p className="text-sm text-gray-400 mt-1">
                              {template.agentCount} agents • {template.complexity}
                            </p>
                          </div>
                        </div>
                        <Badge variant={getComplexityColor(template.complexity)}>
                          {getComplexityIcon(template.complexity)}
                          <span className="ml-1">{template.complexity}</span>
                        </Badge>
                      </div>
                      
                      <p className="text-gray-300 text-sm mb-4">
                        {template.description}
                      </p>
                      
                      {/* Agent Topology */}
                      <div className="flex items-center gap-4 mb-4">
                        <div className="flex items-center gap-1">
                          <Icons.QueenAgent size={16} className="text-colony-400" />
                          <span className="text-xs text-gray-400">{template.topology.queen}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Icons.WorkerAgent size={16} className="text-neural-400" />
                          <span className="text-xs text-gray-400">{template.topology.workers}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Icons.ScoutAgent size={16} className="text-network-400" />
                          <span className="text-xs text-gray-400">{template.topology.scouts}</span>
                        </div>
                      </div>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {template.tags.slice(0, 3).map(tag => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800 text-gray-300"
                          >
                            <Tag size={10} className="mr-1" />
                            {tag}
                          </span>
                        ))}
                        {template.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{template.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Template Stats */}
                    <div className="p-6 bg-gray-900/50">
                      {/* Capabilities */}
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold text-gray-400 uppercase mb-2">Capabilities</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {template.capabilities.slice(0, 4).map((capability, i) => (
                            <div key={i} className="flex items-center gap-1 text-xs text-gray-300">
                              <CheckCircle size={12} className="text-green-400" />
                              {capability}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Performance Metrics */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs text-gray-400">Success Rate</span>
                            <span className="text-xs font-medium text-white">{template.successRate}%</span>
                          </div>
                          <div className="h-1.5 bg-gray-700 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-gradient-to-r from-green-500 to-green-400 transition-all duration-500"
                              style={{ width: `${template.successRate}%` }}
                            />
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs text-gray-400">Deployments</span>
                            <span className="text-xs font-medium text-white">{template.deployments.toLocaleString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star size={12} className="text-yellow-400" />
                            <Star size={12} className="text-yellow-400" />
                            <Star size={12} className="text-yellow-400" />
                            <Star size={12} className="text-yellow-400" />
                            <Star size={12} className="text-gray-600" />
                          </div>
                        </div>
                      </div>

                      {/* Requirements */}
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold text-gray-400 uppercase mb-2">Requirements</h4>
                        <div className="flex items-center gap-3 text-xs">
                          <div className="flex items-center gap-1">
                            <Cpu size={12} className="text-gray-400" />
                            <span className="text-gray-300">{template.requirements.cpu}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Database size={12} className="text-gray-400" />
                            <span className="text-gray-300">{template.requirements.memory}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Cloud size={12} className="text-gray-400" />
                            <span className="text-gray-300">{template.requirements.apis.length} APIs</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex-1"
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <Eye size={16} className="mr-1" />
                          Preview
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleDeployTemplate(template)}
                        >
                          <Icons.Deploy size={16} className="mr-1" />
                          Deploy
                        </Button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredTemplates.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Icons.Colony size={64} className="text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400 text-lg">
              No agent templates found matching your criteria.
            </p>
            <Button
              variant="ghost"
              className="mt-4"
              onClick={() => {
                setSearchQuery('')
                setSelectedCategory('all')
              }}
            >
              Clear filters
            </Button>
          </motion.div>
        )}
      </div>

      {/* Template Preview Modal */}
      <AnimatePresence>
        {selectedTemplate && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedTemplate(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-system-surface rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-800">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="p-3 bg-neural-500/20 rounded-lg">
                      <Icons.Colony size={32} className="text-neural-400" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">
                        {selectedTemplate.name}
                      </h2>
                      <p className="text-gray-400 mt-1">
                        {selectedTemplate.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} • 
                        {selectedTemplate.agentCount} agents • 
                        {selectedTemplate.complexity} complexity
                      </p>
                    </div>
                  </div>
                  <Badge variant={getComplexityColor(selectedTemplate.complexity)}>
                    {getComplexityIcon(selectedTemplate.complexity)}
                    <span className="ml-1">{selectedTemplate.complexity}</span>
                  </Badge>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
                {/* Colony Visualization */}
                <div className="bg-gray-900/50 rounded-lg p-6 mb-6">
                  <h3 className="font-semibold text-white mb-4 flex items-center gap-2">
                    <Icons.NeuralNetwork size={20} className="text-neural-400" />
                    Colony Topology
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-colony-500/10 rounded-lg border border-colony-500/30">
                      <Icons.QueenAgent size={48} className="text-colony-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-white">Queen Agents</p>
                      <p className="text-2xl font-bold text-colony-400">{selectedTemplate.topology.queen}</p>
                    </div>
                    <div className="text-center p-4 bg-neural-500/10 rounded-lg border border-neural-500/30">
                      <Icons.WorkerAgent size={48} className="text-neural-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-white">Worker Agents</p>
                      <p className="text-2xl font-bold text-neural-400">{selectedTemplate.topology.workers}</p>
                    </div>
                    <div className="text-center p-4 bg-network-500/10 rounded-lg border border-network-500/30">
                      <Icons.ScoutAgent size={48} className="text-network-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-white">Scout Agents</p>
                      <p className="text-2xl font-bold text-network-400">{selectedTemplate.topology.scouts}</p>
                    </div>
                  </div>
                </div>

                {/* Capabilities & Requirements */}
                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="font-semibold text-white mb-3">Capabilities</h3>
                    <div className="space-y-2">
                      {selectedTemplate.capabilities.map((capability, i) => (
                        <div key={i} className="flex items-center gap-2 text-sm">
                          <CheckCircle size={16} className="text-green-400" />
                          <span className="text-gray-300">{capability}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-3">System Requirements</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400 flex items-center gap-2">
                          <Cpu size={16} />
                          CPU Usage
                        </span>
                        <Badge variant="warning">{selectedTemplate.requirements.cpu}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400 flex items-center gap-2">
                          <Database size={16} />
                          Memory Usage
                        </span>
                        <Badge variant="error">{selectedTemplate.requirements.memory}</Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400 flex items-center gap-2 mb-2">
                          <Cloud size={16} />
                          Required APIs
                        </span>
                        <div className="flex flex-wrap gap-2">
                          {selectedTemplate.requirements.apis.map((api, i) => (
                            <Badge key={i} variant="default">{api}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Performance Stats */}
                <div className="bg-gray-900/50 rounded-lg p-6">
                  <h3 className="font-semibold text-white mb-4">Performance Statistics</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-400">Success Rate</p>
                      <p className="text-2xl font-bold text-green-400">{selectedTemplate.successRate}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Deployments</p>
                      <p className="text-2xl font-bold text-neural-400">{selectedTemplate.deployments.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Avg. Runtime</p>
                      <p className="text-2xl font-bold text-honey-400">2.3h</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">User Rating</p>
                      <div className="flex items-center gap-1 mt-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} size={16} className={i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-600'} />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-800">
                <div className="flex gap-3">
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedTemplate(null)}
                    className="flex-1"
                  >
                    Close Preview
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => {
                      handleDeployTemplate(selectedTemplate)
                      setSelectedTemplate(null)
                    }}
                    className="flex-1 group"
                  >
                    <Icons.Deploy size={20} className="mr-2 group-hover:animate-pulse" />
                    Deploy to Colony
                    <ChevronRight size={20} className="ml-2" />
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <style jsx>{`
        .honeycomb-grid > *:nth-child(even) {
          margin-top: 2rem;
        }
      `}</style>
    </div>
  )
}

export default AgentTemplateLibrary