import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { cn } from '../utils/cn';

const NotificationContext = createContext();

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within NotificationProvider');
  }
  return context;
};

const notificationTypes = {
  success: {
    icon: CheckCircle,
    className: 'bg-green-900/20 border-green-500/30 text-green-400',
    iconClassName: 'text-green-400',
  },
  error: {
    icon: AlertCircle,
    className: 'bg-red-900/20 border-red-500/30 text-red-400',
    iconClassName: 'text-red-400',
  },
  warning: {
    icon: AlertTriangle,
    className: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-400',
    iconClassName: 'text-yellow-400',
  },
  info: {
    icon: Info,
    className: 'bg-blue-900/20 border-blue-500/30 text-blue-400',
    iconClassName: 'text-blue-400',
  },
  neural: {
    icon: Info,
    className: 'bg-purple-900/20 border-purple-500/30 text-purple-400',
    iconClassName: 'text-purple-400',
  },
};

const Notification = ({ notification, onClose }) => {
  const config = notificationTypes[notification.type] || notificationTypes.info;
  const Icon = config.icon;

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
      className={cn(
        'relative flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm shadow-xl',
        'min-w-[320px] max-w-[420px]',
        config.className
      )}
    >
      {/* Animated background effect */}
      <motion.div
        className="absolute inset-0 rounded-lg opacity-30"
        style={{
          background: `radial-gradient(circle at 20% 50%, currentColor, transparent)`,
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
      
      <motion.div
        initial={{ rotate: -90 }}
        animate={{ rotate: 0 }}
        transition={{ type: 'spring', stiffness: 200 }}
        className="relative"
      >
        <Icon size={20} className={config.iconClassName} />
      </motion.div>
      
      <div className="flex-1 relative">
        <h4 className="font-semibold text-white">{notification.title}</h4>
        {notification.message && (
          <p className="text-sm mt-1 opacity-90">{notification.message}</p>
        )}
        
        {notification.actions && (
          <div className="flex gap-2 mt-3">
            {notification.actions.map((action, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  action.onClick();
                  onClose(notification.id);
                }}
                className={cn(
                  'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                  'bg-white/10 hover:bg-white/20'
                )}
              >
                {action.label}
              </motion.button>
            ))}
          </div>
        )}
      </div>
      
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => onClose(notification.id)}
        className="relative opacity-60 hover:opacity-100 transition-opacity"
      >
        <X size={16} />
      </motion.button>
      
      {/* Progress bar for auto-dismiss */}
      {notification.duration && (
        <motion.div
          className="absolute bottom-0 left-0 h-0.5 bg-current opacity-30"
          initial={{ width: '100%' }}
          animate={{ width: '0%' }}
          transition={{ duration: notification.duration / 1000, ease: 'linear' }}
        />
      )}
    </motion.div>
  );
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  const notify = useCallback((options) => {
    const id = Date.now();
    const notification = {
      id,
      type: 'info',
      duration: 5000,
      ...options,
    };

    setNotifications((prev) => [...prev, notification]);

    if (notification.duration > 0) {
      setTimeout(() => {
        setNotifications((prev) => prev.filter((n) => n.id !== id));
      }, notification.duration);
    }

    return id;
  }, []);

  const close = useCallback((id) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  }, []);

  const value = {
    notify,
    close,
    success: (title, message, options = {}) =>
      notify({ ...options, type: 'success', title, message }),
    error: (title, message, options = {}) =>
      notify({ ...options, type: 'error', title, message }),
    warning: (title, message, options = {}) =>
      notify({ ...options, type: 'warning', title, message }),
    info: (title, message, options = {}) =>
      notify({ ...options, type: 'info', title, message }),
    neural: (title, message, options = {}) =>
      notify({ ...options, type: 'neural', title, message }),
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Notification container */}
      <div className="fixed bottom-4 right-4 z-50 pointer-events-none">
        <AnimatePresence>
          <motion.div className="space-y-3 pointer-events-auto">
            {notifications.map((notification) => (
              <Notification
                key={notification.id}
                notification={notification}
                onClose={close}
              />
            ))}
          </motion.div>
        </AnimatePresence>
      </div>
    </NotificationContext.Provider>
  );
};

// Agent-specific notification component
export const AgentNotification = ({ agent, status, message }) => {
  const statusConfig = {
    started: { icon: '🚀', color: 'text-blue-400' },
    success: { icon: '✅', color: 'text-green-400' },
    error: { icon: '❌', color: 'text-red-400' },
    processing: { icon: '⚡', color: 'text-yellow-400' },
  };

  const config = statusConfig[status] || statusConfig.processing;

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 100 }}
      className="flex items-center gap-3 p-4 bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-800 shadow-xl"
    >
      <motion.div
        animate={{ rotate: status === 'processing' ? 360 : 0 }}
        transition={{ duration: 2, repeat: status === 'processing' ? Infinity : 0, ease: 'linear' }}
        className="text-2xl"
      >
        {config.icon}
      </motion.div>
      
      <div className="flex-1">
        <h4 className={cn('font-semibold', config.color)}>{agent}</h4>
        <p className="text-sm text-gray-400">{message}</p>
      </div>
      
      {status === 'processing' && (
        <motion.div
          className="w-16 h-1 bg-gray-700 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-purple-500 to-blue-500"
            animate={{ x: ['-100%', '100%'] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />
        </motion.div>
      )}
    </motion.div>
  );
};