import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Mail, 
  TrendingUp, 
  Users, 
  Calendar,
  BarChart3,
  Target,
  Clock,
  Zap,
  ArrowUp,
  ArrowDown
} from 'lucide-react'

const DashboardStats = ({ user }) => {
  const [stats, setStats] = useState({
    totalSequences: 0,
    totalEmails: 0,
    avgConversionRate: 0,
    totalRecipients: 0,
    thisMonth: {
      sequences: 0,
      emails: 0,
      growth: 0
    },
    recentActivity: [],
    topPerformingSequences: [],
    loading: true
  })

  useEffect(() => {
    loadUserStats()
  }, [user])

  const loadUserStats = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/analytics/dashboard`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Convert timestamp strings back to Date objects for recent activity
          const processedData = {
            ...data.data,
            recentActivity: data.data.recentActivity.map(activity => ({
              ...activity,
              timestamp: new Date(activity.timestamp)
            })),
            loading: false
          }
          setStats(processedData)
        } else {
          throw new Error(data.message || 'Failed to load analytics')
        }
      } else {
        throw new Error('Failed to fetch analytics data')
      }
    } catch (error) {
      console.error('Failed to load stats:', error)
      // Fallback to empty state on error
      setStats(prev => ({ 
        ...prev, 
        loading: false,
        totalSequences: 0,
        totalEmails: 0,
        avgConversionRate: 0,
        totalRecipients: 0,
        thisMonth: { sequences: 0, emails: 0, growth: 0 },
        recentActivity: [],
        topPerformingSequences: []
      }))
    }
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`
    return 'Just now'
  }

  const StatCard = ({ title, value, change, icon: Icon, color = 'primary' }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-lg p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              {change >= 0 ? (
                <ArrowUp className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDown className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ml-1 ${
                change >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {Math.abs(change)}% this month
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
      </div>
    </motion.div>
  )

  if (stats.loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Sequences"
          value={stats.totalSequences}
          change={stats.thisMonth.growth}
          icon={Mail}
          color="primary"
        />
        <StatCard
          title="Total Emails Generated"
          value={stats.totalEmails}
          change={15.2}
          icon={BarChart3}
          color="green"
        />
        <StatCard
          title="Average Conversion Rate"
          value={`${stats.avgConversionRate}%`}
          change={8.7}
          icon={Target}
          color="blue"
        />
        <StatCard
          title="Estimated Reach"
          value={stats.totalRecipients.toLocaleString()}
          change={31.4}
          icon={Users}
          color="purple"
        />
      </div>

      {/* Charts and Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <Clock className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {stats.recentActivity.map((activity, index) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    {activity.type === 'sequence_created' ? (
                      <Mail className="h-4 w-4 text-primary-600" />
                    ) : (
                      <Zap className="h-4 w-4 text-primary-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 text-sm">
                      {activity.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium text-green-600">
                    {activity.conversionRate}%
                  </span>
                  <p className="text-xs text-gray-500">conversion</p>
                </div>
              </motion.div>
            ))}
          </div>
          
          <button className="w-full mt-4 text-primary-600 hover:text-primary-700 text-sm font-medium">
            View All Activity
          </button>
        </motion.div>

        {/* Top Performing Sequences */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Top Performing Sequences</h3>
            <TrendingUp className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {stats.topPerformingSequences.map((sequence, index) => (
              <motion.div
                key={sequence.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 text-sm">
                    {sequence.title}
                  </h4>
                  <span className="text-lg font-bold text-green-600">
                    {sequence.conversionRate}%
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{sequence.industry}</span>
                  <span>{sequence.emailsSent.toLocaleString()} emails sent</span>
                </div>
                <div className="mt-2 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${Math.min(sequence.conversionRate, 100)}%` }}
                  ></div>
                </div>
              </motion.div>
            ))}
          </div>
          
          <button className="w-full mt-4 text-primary-600 hover:text-primary-700 text-sm font-medium">
            View All Sequences
          </button>
        </motion.div>
      </div>

      {/* Usage Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-500 to-blue-600 rounded-lg shadow-lg p-6 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-2">Usage This Month</h3>
            <p className="text-primary-100">
              You've generated {stats.thisMonth.sequences} sequences with {stats.thisMonth.emails} emails.
              {stats.thisMonth.growth > 0 && (
                <span className="block mt-1">
                  That's {stats.thisMonth.growth}% more than last month! 🎉
                </span>
              )}
            </p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold">{user?.usage?.sequencesGenerated || 0}</p>
            <p className="text-primary-200 text-sm">
              of {user?.usage?.sequencesLimit === -1 ? '∞' : user?.usage?.sequencesLimit || 3} sequences
            </p>
          </div>
        </div>
        
        {user?.subscription?.type === 'free' && (
          <div className="mt-4 pt-4 border-t border-primary-400">
            <div className="flex items-center justify-between">
              <p className="text-sm text-primary-100">
                Want unlimited sequences? Upgrade to Pro for more features.
              </p>
              <button className="bg-white text-primary-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                Upgrade Now
              </button>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}

export default DashboardStats