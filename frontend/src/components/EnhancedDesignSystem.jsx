import React from 'react'
import { motion } from 'framer-motion'
import { ArrowR<PERSON>, <PERSON><PERSON>les, Zap, CheckCircle, Star } from 'lucide-react'

// Enhanced Button Component
export const EnhancedButton = ({ 
  children, 
  variant = 'primary', 
  size = 'default', 
  animated = true,
  className = '',
  onClick,
  disabled = false,
  icon: Icon,
  ...props 
}) => {
  const baseClasses = "relative overflow-hidden font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-neutral-900"
  
  const variants = {
    primary: "bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white shadow-lg hover:shadow-purple-500/25",
    secondary: "bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-400 hover:to-orange-400 text-white shadow-lg hover:shadow-amber-500/25",
    outline: "border-2 border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white",
    ghost: "text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"
  }
  
  const sizes = {
    small: "px-4 py-2 text-sm rounded-lg",
    default: "px-6 py-3 text-base rounded-xl",
    large: "px-8 py-4 text-lg rounded-xl"
  }

  const MotionButton = animated ? motion.button : 'button'

  return (
    <MotionButton
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
      whileHover={animated && !disabled ? { scale: 1.02, y: -1 } : undefined}
      whileTap={animated && !disabled ? { scale: 0.98 } : undefined}
      transition={{ duration: 0.2 }}
      {...props}
    >
      <span className="relative z-10 flex items-center gap-2">
        {Icon && <Icon className="w-4 h-4" />}
        {children}
      </span>
      {!disabled && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
          initial={{ x: '-100%' }}
          animate={{ x: '100%' }}
          transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 3 }}
        />
      )}
    </MotionButton>
  )
}

// Enhanced Card Component
export const EnhancedCard = ({ 
  children, 
  className = '', 
  hover = true, 
  glow = false,
  animated = true 
}) => {
  const MotionDiv = animated ? motion.div : 'div'
  
  return (
    <MotionDiv
      className={`
        bg-gradient-to-br from-neutral-800/50 to-neutral-900/50 
        backdrop-blur-sm border border-neutral-700/50 rounded-2xl p-6
        ${hover ? 'hover:border-purple-500/50 hover:shadow-lg hover:shadow-purple-500/10' : ''}
        ${glow ? 'shadow-lg shadow-purple-500/20' : ''}
        transition-all duration-300 ${className}
      `}
      whileHover={animated && hover ? { y: -2, scale: 1.01 } : undefined}
      transition={{ duration: 0.3 }}
    >
      {children}
    </MotionDiv>
  )
}

// Enhanced Badge Component
export const EnhancedBadge = ({ 
  children, 
  variant = 'default', 
  size = 'default',
  animated = true 
}) => {
  const variants = {
    default: "bg-purple-500/20 text-purple-300 border-purple-500/30",
    success: "bg-green-500/20 text-green-300 border-green-500/30",
    warning: "bg-amber-500/20 text-amber-300 border-amber-500/30",
    premium: "bg-gradient-to-r from-purple-500/20 to-amber-500/20 text-amber-300 border-purple-500/30"
  }
  
  const sizes = {
    small: "px-2 py-1 text-xs",
    default: "px-3 py-1.5 text-sm",
    large: "px-4 py-2 text-base"
  }

  const MotionSpan = animated ? motion.span : 'span'

  return (
    <MotionSpan
      className={`
        inline-flex items-center rounded-full font-medium border
        ${variants[variant]} ${sizes[size]}
      `}
      whileHover={animated ? { scale: 1.05 } : undefined}
      transition={{ duration: 0.2 }}
    >
      {children}
    </MotionSpan>
  )
}

// Enhanced Feature Card
export const FeatureCard = ({ 
  icon: Icon, 
  title, 
  description, 
  premium = false,
  animated = true 
}) => {
  return (
    <EnhancedCard hover={true} animated={animated} className="group">
      <div className="flex items-start gap-4">
        <div className={`
          flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center
          ${premium 
            ? 'bg-gradient-to-br from-purple-500 to-amber-500' 
            : 'bg-gradient-to-br from-purple-500/20 to-purple-600/20'
          }
          group-hover:scale-110 transition-transform duration-300
        `}>
          <Icon className={`w-6 h-6 ${premium ? 'text-white' : 'text-purple-400'}`} />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">
            {title}
            {premium && (
              <EnhancedBadge variant="premium" size="small" className="ml-2">
                Premium
              </EnhancedBadge>
            )}
          </h3>
          <p className="text-neutral-400 leading-relaxed group-hover:text-neutral-300 transition-colors">
            {description}
          </p>
        </div>
      </div>
    </EnhancedCard>
  )
}

// Enhanced Stats Component
export const StatsCard = ({ value, label, trend, animated = true }) => {
  const MotionDiv = animated ? motion.div : 'div'
  
  return (
    <MotionDiv
      className="text-center group"
      initial={animated ? { opacity: 0, y: 20 } : undefined}
      whileInView={animated ? { opacity: 1, y: 0 } : undefined}
      transition={animated ? { duration: 0.6 } : undefined}
      viewport={{ once: true }}
    >
      <motion.div 
        className="text-4xl md:text-5xl font-bold bg-gradient-to-br from-purple-400 to-amber-400 bg-clip-text text-transparent mb-2"
        whileHover={animated ? { scale: 1.1 } : undefined}
        transition={{ duration: 0.3 }}
      >
        {value}
      </motion.div>
      <div className="text-neutral-400 font-medium">{label}</div>
      {trend && (
        <div className="text-sm text-green-400 mt-1 flex items-center justify-center gap-1">
          <ArrowRight className="w-3 h-3 rotate-[-45deg]" />
          {trend}
        </div>
      )}
    </MotionDiv>
  )
}

// Professional Loading Spinner
export const LoadingSpinner = ({ size = 'default', className = '' }) => {
  const sizes = {
    small: "w-4 h-4",
    default: "w-6 h-6", 
    large: "w-8 h-8"
  }

  return (
    <motion.div
      className={`${sizes[size]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <div className="w-full h-full border-2 border-purple-200 border-t-purple-600 rounded-full"></div>
    </motion.div>
  )
}

// Enhanced Testimonial Card
export const TestimonialCard = ({ name, role, company, content, rating, animated = true }) => {
  return (
    <EnhancedCard hover={true} animated={animated} className="h-full">
      <div className="flex flex-col h-full">
        <div className="flex mb-4">
          {[...Array(rating)].map((_, i) => (
            <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
          ))}
        </div>
        <blockquote className="text-neutral-300 italic mb-6 flex-1 leading-relaxed">
          "{content}"
        </blockquote>
        <div className="border-t border-neutral-700 pt-4">
          <div className="font-semibold text-white">{name}</div>
          <div className="text-sm text-neutral-400">{role}</div>
          <div className="text-sm text-purple-400">{company}</div>
        </div>
      </div>
    </EnhancedCard>
  )
}