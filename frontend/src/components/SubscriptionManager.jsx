import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const SubscriptionManager = () => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [portalLoading, setPortalLoading] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/payment/subscription/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setSubscription(data);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    setPortalLoading(true);
    try {
      const response = await fetch('/api/payment/portal/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          returnUrl: window.location.href
        })
      });
      
      const { url } = await response.json();
      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      alert('Failed to open billing portal. Please try again.');
    } finally {
      setPortalLoading(false);
    }
  };

  const getPlanDisplayName = (plan) => {
    const names = {
      free: 'Free',
      pro: 'Pro',
      business: 'Business',
      enterprise: 'Enterprise'
    };
    return names[plan] || plan;
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'text-green-600 bg-green-100',
      trialing: 'text-blue-600 bg-blue-100',
      past_due: 'text-yellow-600 bg-yellow-100',
      cancelled: 'text-red-600 bg-red-100',
      inactive: 'text-gray-600 bg-gray-100'
    };
    return colors[status] || 'text-gray-600 bg-gray-100';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Subscription Details
      </h3>
      
      <div className="space-y-4">
        {/* Current Plan */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Current Plan:</span>
          <div className="flex items-center space-x-2">
            <span className="font-medium text-gray-900">
              {getPlanDisplayName(subscription?.plan)}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              getStatusColor(subscription?.status)
            }`}>
              {subscription?.status}
            </span>
          </div>
        </div>

        {/* Trial Information */}
        {subscription?.trialEnd && new Date(subscription.trialEnd) > new Date() && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Trial Ends:</span>
            <span className="font-medium text-blue-600">
              {new Date(subscription.trialEnd).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Next Billing Date */}
        {subscription?.hasActiveSubscription && subscription?.currentPeriodEnd && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Next Billing:</span>
            <span className="font-medium text-gray-900">
              {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Cancellation Notice */}
        {subscription?.cancelAtPeriodEnd && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex">
              <svg className="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-yellow-800">
                Your subscription will be cancelled at the end of the current billing period.
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="pt-4 border-t border-gray-200">
          {subscription?.hasActiveSubscription ? (
            <button
              onClick={handleManageSubscription}
              disabled={portalLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {portalLoading ? 'Opening...' : 'Manage Subscription'}
            </button>
          ) : (
            <a
              href="/pricing"
              className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center"
            >
              Upgrade Plan
            </a>
          )}
        </div>

        {/* Help Text */}
        <p className="text-xs text-gray-500 text-center">
          {subscription?.hasActiveSubscription 
            ? 'Manage your billing details, update payment methods, or cancel your subscription.'
            : 'Choose a plan that fits your needs and start your free trial today.'
          }
        </p>
      </div>
    </div>
  );
};

export default SubscriptionManager;