import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Activity, Zap, Cpu, TrendingUp } from 'lucide-react';
import { cn } from '../utils/cn';

const PerformanceMonitor = ({ show = false, position = 'bottom-left' }) => {
  const [fps, setFps] = useState(60);
  const [memory, setMemory] = useState(0);
  const [animationCount, setAnimationCount] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);

  // FPS monitoring
  useEffect(() => {
    if (!show) return;

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId;

    const measureFPS = (currentTime) => {
      frameCount++;
      
      if (currentTime >= lastTime + 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => cancelAnimationFrame(animationId);
  }, [show]);

  // Memory monitoring
  useEffect(() => {
    if (!show || !performance.memory) return;

    const interval = setInterval(() => {
      const usedMemory = performance.memory.usedJSHeapSize / 1048576; // Convert to MB
      setMemory(Math.round(usedMemory));
    }, 1000);

    return () => clearInterval(interval);
  }, [show]);

  // Animation count monitoring
  useEffect(() => {
    if (!show) return;

    const observer = new MutationObserver((mutations) => {
      let count = 0;
      mutations.forEach((mutation) => {
        if (mutation.target.style && mutation.target.style.transform) {
          count++;
        }
      });
      setAnimationCount((prev) => prev + count);
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style'],
      subtree: true,
    });

    return () => observer.disconnect();
  }, [show]);

  const getFPSColor = useCallback((fps) => {
    if (fps >= 55) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  }, []);

  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
  };

  if (!show) return null;

  return (
    <AnimatePresence>
      <motion.div
        className={cn(
          'fixed z-50',
          positionClasses[position]
        )}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
      >
        <motion.div
          className={cn(
            'bg-gray-900/95 backdrop-blur-sm rounded-lg shadow-2xl border border-gray-800',
            'transition-all duration-300',
            isExpanded ? 'w-64' : 'w-32'
          )}
          layout
        >
          {/* Compact view */}
          <motion.button
            className="w-full p-3 flex items-center justify-between text-left"
            onClick={() => setIsExpanded(!isExpanded)}
            whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
          >
            <div className="flex items-center gap-2">
              <Activity size={16} className={getFPSColor(fps)} />
              <span className={cn('font-mono text-sm', getFPSColor(fps))}>
                {fps} FPS
              </span>
            </div>
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <TrendingUp size={14} className="text-gray-400" />
            </motion.div>
          </motion.button>

          {/* Expanded view */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-t border-gray-800 overflow-hidden"
              >
                <div className="p-3 space-y-3">
                  {/* Memory usage */}
                  {performance.memory && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Cpu size={14} className="text-blue-400" />
                        <span className="text-xs text-gray-400">Memory</span>
                      </div>
                      <span className="font-mono text-xs text-blue-400">
                        {memory} MB
                      </span>
                    </div>
                  )}

                  {/* Animation count */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap size={14} className="text-purple-400" />
                      <span className="text-xs text-gray-400">Animations</span>
                    </div>
                    <span className="font-mono text-xs text-purple-400">
                      {animationCount}
                    </span>
                  </div>

                  {/* Performance tips */}
                  <div className="pt-2 border-t border-gray-800">
                    <p className="text-xs text-gray-500">
                      {fps < 30 && '⚠️ Low FPS detected'}
                      {fps >= 30 && fps < 55 && '⚡ Moderate performance'}
                      {fps >= 55 && '✅ Excellent performance'}
                    </p>
                  </div>

                  {/* FPS Graph */}
                  <div className="h-8 bg-gray-800 rounded relative overflow-hidden">
                    <motion.div
                      className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-purple-600/20 to-purple-600/40"
                      animate={{ height: `${(fps / 60) * 100}%` }}
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PerformanceMonitor;