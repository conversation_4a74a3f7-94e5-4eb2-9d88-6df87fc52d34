import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Filter, Copy, Eye, Download, ChevronRight, Tag, Zap, Brain, Hexagon, Network, Activity, Target, Shield, Sparkles, Crown, Layers, GitBranch } from 'lucide-react'
import toast from 'react-hot-toast'
import { colonyTheme, getTextClass, getButtonClass, getBadgeClass, getCardClass, cn } from '../design-system/colony-theme'
import { Icon, HexCard, NeuralPattern, ColonyIcon, NeuralNetworkIcon, SwarmIcon } from '../design-system/colony-icons'

const TemplateLibrary = ({ onSelectTemplate }) => {
  const [templates, setTemplates] = useState([])
  const [filteredTemplates, setFilteredTemplates] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [loading, setLoading] = useState(true)

  // Colony-themed categories
  const categories = [
    { id: 'all', name: 'All Colony Blueprints', count: 12, icon: 'colony', color: 'neural' },
    { id: 'welcome', name: 'Hive Integration', count: 2, icon: 'hexagon', color: 'honey' },
    { id: 'nurture', name: 'Neural Training', count: 3, icon: 'brain', color: 'neural' },
    { id: 'sales', name: 'Swarm Conversion', count: 4, icon: 'swarm', color: 'swarm' },
    { id: 'retention', name: 'Colony Retention', count: 2, icon: 'shield', color: 'neural' },
    { id: 'winback', name: 'Reactivation Protocols', count: 1, icon: 'activate', color: 'honey' }
  ]

  useEffect(() => {
    loadTemplates()
  }, [])

  useEffect(() => {
    filterTemplates()
  }, [searchQuery, selectedCategory, templates])

  const loadTemplates = async () => {
    try {
      // Colony-themed templates
      const mockTemplates = [
        {
          id: 'welcome-1',
          name: 'Hive Welcome Protocol',
          category: 'Hive Integration',
          description: 'Initialize new colony members with neural synchronization',
          tags: ['integration', 'neural-sync', 'new-member'],
          successRate: 92,
          deploymentCount: 12500,
          neuralCapabilities: ['personalization', 'behavior-analysis', 'engagement-tracking'],
          preview: 'Welcome to the {colony_name} Collective! Your neural profile has been initialized...',
          icon: 'hexagon',
          color: 'honey'
        },
        {
          id: 'nurture-1',
          name: 'Knowledge Propagation Network',
          category: 'Neural Training', 
          description: 'Distribute colony intelligence through neural pathways',
          tags: ['training', 'knowledge-transfer', 'neural-growth'],
          successRate: 88,
          deploymentCount: 8900,
          neuralCapabilities: ['adaptive-learning', 'content-optimization', 'engagement-prediction'],
          preview: 'Neural Network Update: 73% of colony members have enhanced their {capability}...',
          icon: 'brain',
          color: 'neural'
        },
        {
          id: 'sales-1',
          name: 'Swarm Convergence Protocol',
          category: 'Swarm Conversion',
          description: 'Activate collective action through synchronized messaging',
          tags: ['conversion', 'swarm-intelligence', 'collective-action'],
          successRate: 96,
          deploymentCount: 34200,
          neuralCapabilities: ['urgency-detection', 'behavioral-triggers', 'conversion-optimization'],
          preview: 'SWARM ALERT: The colony collective is converging on {objective}. Time remaining...',
          icon: 'swarm',
          color: 'swarm'
        },
        {
          id: 'retention-1',
          name: 'Colony Bond Strengthener',
          category: 'Colony Retention',
          description: 'Reinforce neural connections within the colony network',
          tags: ['retention', 'loyalty', 'neural-bonding'],
          successRate: 85,
          deploymentCount: 5600,
          neuralCapabilities: ['sentiment-analysis', 'loyalty-prediction', 'personalized-rewards'],
          preview: 'Colony Status Update: Your contribution to {colony_metric} has strengthened the hive...',
          icon: 'shield',
          color: 'neural'
        },
        {
          id: 'winback-1',
          name: 'Dormant Node Reactivation',
          category: 'Reactivation Protocols',
          description: 'Reawaken inactive neural nodes in the colony network',
          tags: ['reactivation', 'win-back', 'neural-recovery'],
          successRate: 78,
          deploymentCount: 2300,
          neuralCapabilities: ['inactivity-detection', 'personalized-incentives', 're-engagement'],
          preview: 'Neural Reactivation Signal: The colony has evolved since your last connection...',
          icon: 'activate',
          color: 'honey'
        }
      ]
      
      setTemplates(mockTemplates)
      setFilteredTemplates(mockTemplates)
      setLoading(false)
    } catch (error) {
      toast.error('Failed to initialize colony blueprints')
      setLoading(false)
    }
  }

  const filterTemplates = () => {
    let filtered = templates

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => 
        t.category.toLowerCase().includes(selectedCategory.toLowerCase())
      )
    }

    if (searchQuery) {
      filtered = filtered.filter(t =>
        t.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredTemplates(filtered)
  }

  const handleCopyTemplate = (template) => {
    const templateText = `Colony Blueprint: ${template.name}\n\n${template.preview}`
    navigator.clipboard.writeText(templateText)
    toast.success('Blueprint copied to neural buffer!')
  }

  const handleUseTemplate = (template) => {
    if (onSelectTemplate) {
      onSelectTemplate(template)
    } else {
      toast.success(`Deploying blueprint: ${template.name}`)
    }
  }

  return (
    <div className={cn("min-h-screen relative overflow-x-hidden", colonyTheme.gradients.neural)}>
      <NeuralPattern className="opacity-5" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center mb-6">
            <ColonyIcon size={64} animate className="text-neural-600 dark:text-neural-400" />
          </div>
          <h1 className={cn(getTextClass('h1'), "mb-4")}>
            Colony Blueprint Archives
          </h1>
          <p className={cn(getTextClass('bodyLarge'), "max-w-3xl mx-auto")}>
            Access our neural network of proven agent workflows. Each blueprint is optimized 
            through collective intelligence for maximum colony efficiency.
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className={cn(getCardClass(true), "p-6 mb-8")}
        >
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neural-500 h-5 w-5" />
              <input
                type="text"
                placeholder="Search colony blueprints..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  "w-full pl-10 pr-4 py-3 rounded-lg",
                  "border-2 border-neural-200 dark:border-neural-700",
                  "bg-white dark:bg-gray-800",
                  "text-gray-800 dark:text-gray-200",
                  "placeholder-gray-500 dark:placeholder-gray-400",
                  "focus:ring-2 focus:ring-neural-500 focus:border-transparent",
                  "transition-all duration-200"
                )}
              />
            </div>
            
            <div className="flex gap-2 flex-wrap overflow-x-auto md:overflow-x-visible pb-2">
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={cn(
                    "px-4 py-2 rounded-lg font-semibold transition-all duration-200",
                    "flex items-center gap-2",
                    "min-h-[44px] min-w-[44px]", // Ensure touch targets are 44px minimum
                    selectedCategory === category.id
                      ? cn(getButtonClass('primary'), "shadow-lg")
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-neural-50 dark:hover:bg-gray-700 border-2 border-gray-200 dark:border-gray-600"
                  )}
                >
                  <Icon name={category.icon} size={18} />
                  {category.name} ({category.count})
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Templates Grid */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <NeuralNetworkIcon size={48} animate className="text-neural-600" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            <AnimatePresence mode="popLayout">
              {filteredTemplates.map((template, index) => (
                <motion.div
                  key={template.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ delay: index * 0.05 }}
                  className={cn(getCardClass(true), "overflow-hidden group")}
                >
                  {/* Template Header with Neural Pattern */}
                  <div className="relative p-6 border-b-2 border-neural-100 dark:border-neural-800">
                    <div className="absolute top-0 right-0 p-2 opacity-10 group-hover:opacity-20 transition-opacity">
                      <Icon name={template.icon} size={120} />
                    </div>
                    
                    <div className="relative">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <div className={cn(
                            "p-2 rounded-lg",
                            template.color === 'neural' && "bg-neural-100 dark:bg-neural-900",
                            template.color === 'honey' && "bg-honey-100 dark:bg-honey-900",
                            template.color === 'swarm' && "bg-swarm-100 dark:bg-swarm-900"
                          )}>
                            <Icon name={template.icon} size={24} />
                          </div>
                          <div>
                            <h3 className={cn(getTextClass('h5'), "mb-1")}>
                              {template.name}
                            </h3>
                            <p className={cn(getBadgeClass(template.color), "text-xs inline-block")}>
                              {template.category}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Activity className="h-4 w-4 text-swarm-500" />
                          <span className={cn(getTextClass('meta'), "font-bold")}>
                            {template.successRate}%
                          </span>
                        </div>
                      </div>
                      
                      <p className={getTextClass('body')}>
                        {template.description}
                      </p>
                      
                      {/* Neural Capabilities */}
                      <div className="mt-4 mb-4">
                        <p className={cn(getTextClass('metaSmall'), "mb-2 font-semibold")}>
                          Neural Capabilities:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {template.neuralCapabilities.map(capability => (
                            <span
                              key={capability}
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                "bg-neural-100 dark:bg-neural-900 text-neural-700 dark:text-neural-300",
                                "border border-neural-200 dark:border-neural-700"
                              )}
                            >
                              <Sparkles className="h-3 w-3 mr-1" />
                              {capability}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {template.tags.map(tag => (
                          <span
                            key={tag}
                            className={cn(getBadgeClass('gray'), "text-xs")}
                          >
                            <Tag className="h-3 w-3 mr-1 inline" />
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      <div className={getTextClass('meta')}>
                        <Network className="h-3 w-3 inline mr-1" />
                        Deployed {template.deploymentCount.toLocaleString()} times
                      </div>
                    </div>
                  </div>

                  {/* Template Preview */}
                  <div className="p-6 bg-gradient-to-br from-gray-50 to-neural-50 dark:from-gray-800 dark:to-neural-950">
                    <p className={cn(getTextClass('bodySmall'), "italic mb-4 line-clamp-3")}>
                      "{template.preview}"
                    </p>
                    
                    {/* Actions */}
                    <div className="flex gap-2">
                      <button
                        onClick={() => setSelectedTemplate(template)}
                        className={cn(
                          getButtonClass('secondary'),
                          "flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center",
                          "min-h-[44px]" // Ensure touch targets are 44px minimum
                        )}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Analyze
                      </button>
                      <button
                        onClick={() => handleCopyTemplate(template)}
                        className={cn(
                          getButtonClass('secondary'),
                          "flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center",
                          "min-h-[44px]" // Ensure touch targets are 44px minimum
                        )}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        Clone
                      </button>
                      <button
                        onClick={() => handleUseTemplate(template)}
                        className={cn(
                          getButtonClass('primary'),
                          "flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center group",
                          "min-h-[44px]" // Ensure touch targets are 44px minimum
                        )}
                      >
                        Deploy
                        <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredTemplates.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <SwarmIcon size={64} className="mx-auto mb-6 text-neural-300 dark:text-neural-700" />
            <p className={cn(getTextClass('h5'), "mb-4")}>
              No colony blueprints match your search parameters
            </p>
            <button
              onClick={() => {
                setSearchQuery('')
                setSelectedCategory('all')
              }}
              className={getButtonClass('secondary')}
            >
              Reset Neural Filters
            </button>
          </motion.div>
        )}
      </div>

      {/* Template Preview Modal */}
      <AnimatePresence>
        {selectedTemplate && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedTemplate(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className={cn(getCardClass(false), "max-w-2xl w-full max-h-[90vh] overflow-y-auto")}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b-2 border-neural-100 dark:border-neural-800">
                <div className="flex items-start gap-4">
                  <div className={cn(
                    "p-3 rounded-lg",
                    selectedTemplate.color === 'neural' && "bg-neural-100 dark:bg-neural-900",
                    selectedTemplate.color === 'honey' && "bg-honey-100 dark:bg-honey-900",
                    selectedTemplate.color === 'swarm' && "bg-swarm-100 dark:bg-swarm-900"
                  )}>
                    <Icon name={selectedTemplate.icon} size={32} />
                  </div>
                  <div className="flex-1">
                    <h2 className={getTextClass('h3')}>
                      {selectedTemplate.name}
                    </h2>
                    <p className={cn(getTextClass('body'), "mt-1")}>
                      {selectedTemplate.category} • {selectedTemplate.successRate}% success rate
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <div className={cn(
                  "rounded-lg p-6 mb-6",
                  "bg-gradient-to-br from-gray-50 to-neural-50 dark:from-gray-800 dark:to-neural-950",
                  "border-2 border-neural-200 dark:border-neural-700"
                )}>
                  <h3 className={cn(getTextClass('h5'), "mb-3")}>Neural Blueprint Preview</h3>
                  <p className={getTextClass('body')}>
                    {selectedTemplate.preview}
                  </p>
                </div>

                <div className="mb-6">
                  <h3 className={cn(getTextClass('h5'), "mb-3")}>Neural Capabilities</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {selectedTemplate.neuralCapabilities.map(capability => (
                      <div key={capability} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-neural-500 rounded-full animate-pulse" />
                        <span className={getTextClass('body')}>{capability}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={() => handleCopyTemplate(selectedTemplate)}
                    className={cn(getButtonClass('secondary'), "flex-1 px-4 py-3 rounded-lg flex items-center justify-center")}
                  >
                    <Copy className="h-5 w-5 mr-2" />
                    Clone Blueprint
                  </button>
                  <button
                    onClick={() => {
                      handleUseTemplate(selectedTemplate)
                      setSelectedTemplate(null)
                    }}
                    className={cn(getButtonClass('primary'), "flex-1 px-4 py-3 rounded-lg flex items-center justify-center group")}
                  >
                    Deploy to Colony
                    <ChevronRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default TemplateLibrary