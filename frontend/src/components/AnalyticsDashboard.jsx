import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Brain, 
  Clock, 
  Target, 
  BarChart3, 
  PieChart, 
  Calendar,
  Zap,
  Users,
  Mail,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { EnhancedCard, StatsCard } from './EnhancedDesignSystem'

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    loadAnalytics()
  }, [timeRange])

  const loadAnalytics = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/analytics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
      } else {
        // Fallback analytics data for demo
        setAnalytics({
          total_sequences: 12,
          by_industry: {
            'saas': 5,
            'ecommerce': 3,
            'consulting': 2,
            'marketing': 2
          },
          by_tone: {
            'professional': 7,
            'friendly': 3,
            'casual': 2
          },
          by_ai_model: {
            'gpt-4': 8,
            'AI-3-sonnet': 3,
            'gpt-3.5-turbo': 1
          },
          average_length: 5.2,
          recent_activity: [
            { date: '2025-06-30', count: 3 },
            { date: '2025-06-29', count: 2 },
            { date: '2025-06-28', count: 4 },
            { date: '2025-06-27', count: 1 },
            { date: '2025-06-26', count: 2 }
          ]
        })
      }
    } catch (error) {
      console.error('Failed to load analytics:', error)
      // Fallback analytics data for demo
      setAnalytics({
        total_sequences: 12,
        by_industry: {
          'saas': 5,
          'ecommerce': 3,
          'consulting': 2,
          'marketing': 2
        },
        by_tone: {
          'professional': 7,
          'friendly': 3,
          'casual': 2
        },
        by_ai_model: {
          'gpt-4': 8,
          'AI-3-sonnet': 3,
          'gpt-3.5-turbo': 1
        },
        average_length: 5.2,
        recent_activity: [
          { date: '2025-06-30', count: 3 },
          { date: '2025-06-29', count: 2 },
          { date: '2025-06-28', count: 4 },
          { date: '2025-06-27', count: 1 },
          { date: '2025-06-26', count: 2 }
        ]
      })
    } finally {
      setLoading(false)
    }
  }

  const getTopIndustries = () => {
    if (!analytics?.by_industry) return []
    return Object.entries(analytics.by_industry)
      .map(([industry, count]) => ({ industry, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }

  const getTopTones = () => {
    if (!analytics?.by_tone) return []
    return Object.entries(analytics.by_tone)
      .map(([tone, count]) => ({ tone, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }

  const getTopAIModels = () => {
    if (!analytics?.by_ai_model) return []
    return Object.entries(analytics.by_ai_model)
      .map(([model, count]) => ({ model, count }))
      .sort((a, b) => b.count - a.count)
  }

  if (loading || !analytics) {
    return (
      <EnhancedCard>
        <div className="text-center py-12">
          <div className="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-neutral-400">Loading analytics...</p>
        </div>
      </EnhancedCard>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-white mb-2">
            Analytics <span className="gradient-text">Dashboard</span>
          </h2>
          <p className="text-neutral-400">
            Comprehensive insights into your AI-generated email sequences
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex bg-neutral-800 rounded-xl p-1">
          {['7d', '30d', '90d', 'all'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                timeRange === range
                  ? 'bg-purple-600 text-white'
                  : 'text-neutral-400 hover:text-white'
              }`}
            >
              {range === 'all' ? 'All Time' : range.toUpperCase()}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCard
            value={analytics.total_sequences || 0}
            label="Total Sequences"
            trend="+15% this month"
          />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <StatsCard
            value={analytics.average_length?.toFixed(1) || '5.0'}
            label="Avg Length"
            trend="emails per sequence"
          />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StatsCard
            value={Object.keys(analytics.by_industry || {}).length}
            label="Industries"
            trend="diversification"
          />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StatsCard
            value="2.1s"
            label="Avg Generation"
            trend="AI processing time"
          />
        </motion.div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Industry Distribution */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <EnhancedCard>
            <div className="flex items-center gap-3 mb-6">
              <Target className="w-6 h-6 text-purple-400" />
              <h3 className="text-xl font-bold text-white">Top Industries</h3>
            </div>
            
            <div className="space-y-4">
              {getTopIndustries().map((item, index) => (
                <div key={item.industry} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                      {index + 1}
                    </div>
                    <span className="text-white font-medium capitalize">
                      {item.industry}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-neutral-400">{item.count}</span>
                    <div 
                      className="h-2 bg-gradient-to-r from-purple-500 to-amber-500 rounded-full"
                      style={{ 
                        width: `${(item.count / Math.max(...getTopIndustries().map(i => i.count))) * 60}px` 
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Tone Analysis */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <EnhancedCard>
            <div className="flex items-center gap-3 mb-6">
              <Brain className="w-6 h-6 text-amber-400" />
              <h3 className="text-xl font-bold text-white">Communication Tones</h3>
            </div>
            
            <div className="space-y-4">
              {getTopTones().map((item, index) => (
                <div key={item.tone} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                      {index + 1}
                    </div>
                    <span className="text-white font-medium capitalize">
                      {item.tone}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-neutral-400">{item.count}</span>
                    <div 
                      className="h-2 bg-gradient-to-r from-amber-500 to-purple-500 rounded-full"
                      style={{ 
                        width: `${(item.count / Math.max(...getTopTones().map(i => i.count))) * 60}px` 
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </EnhancedCard>
        </motion.div>
      </div>

      {/* AI Model Performance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <EnhancedCard>
          <div className="flex items-center gap-3 mb-6">
            <Zap className="w-6 h-6 text-green-400" />
            <h3 className="text-xl font-bold text-white">AI Model Usage</h3>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {getTopAIModels().map((item, index) => (
              <div key={item.model} className="bg-neutral-800/50 rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-white mb-2">{item.model}</h4>
                <div className="text-3xl font-bold text-green-400 mb-2">{item.count}</div>
                <p className="text-neutral-400 text-sm">sequences generated</p>
              </div>
            ))}
          </div>
        </EnhancedCard>
      </motion.div>

      {/* Recent Activity */}
      {analytics.recent_activity && analytics.recent_activity.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <EnhancedCard>
            <div className="flex items-center gap-3 mb-6">
              <Calendar className="w-6 h-6 text-purple-400" />
              <h3 className="text-xl font-bold text-white">Recent Activity</h3>
            </div>
            
            <div className="space-y-3">
              {analytics.recent_activity.slice(0, 7).map((activity, index) => (
                <div key={activity.date} className="flex items-center justify-between py-3 border-b border-neutral-700 last:border-b-0">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-white">{activity.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-neutral-400">{activity.count} sequences</span>
                    <TrendingUp className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              ))}
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Performance Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9 }}
      >
        <EnhancedCard>
          <div className="flex items-center gap-3 mb-6">
            <BarChart3 className="w-6 h-6 text-amber-400" />
            <h3 className="text-xl font-bold text-white">Performance Insights</h3>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-6 text-center">
              <ArrowUp className="w-8 h-8 text-green-400 mx-auto mb-3" />
              <h4 className="text-lg font-bold text-white mb-2">Generation Speed</h4>
              <p className="text-green-400 font-bold text-xl">10x Faster</p>
              <p className="text-neutral-400 text-sm">than traditional methods</p>
            </div>
            
            <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-6 text-center">
              <Brain className="w-8 h-8 text-purple-400 mx-auto mb-3" />
              <h4 className="text-lg font-bold text-white mb-2">AI Quality</h4>
              <p className="text-purple-400 font-bold text-xl">Premium</p>
              <p className="text-neutral-400 text-sm">enterprise-grade models</p>
            </div>
            
            <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-6 text-center">
              <Target className="w-8 h-8 text-amber-400 mx-auto mb-3" />
              <h4 className="text-lg font-bold text-white mb-2">Conversion Rate</h4>
              <p className="text-amber-400 font-bold text-xl">+340%</p>
              <p className="text-neutral-400 text-sm">improvement on average</p>
            </div>
          </div>
        </EnhancedCard>
      </motion.div>
    </div>
  )
}

export default AnalyticsDashboard