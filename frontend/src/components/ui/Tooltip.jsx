import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../utils/cn';
import { shouldReduceMotion } from '../../utils/animations';

const Tooltip = ({
  children,
  content,
  placement = 'top',
  delay = 500,
  className,
  contentClassName,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState(null);
  const reduceMotion = shouldReduceMotion();

  const showTooltip = () => {
    const id = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const placementStyles = {
    top: {
      initial: { opacity: 0, y: 10, x: '-50%' },
      animate: { opacity: 1, y: 0, x: '-50%' },
      exit: { opacity: 0, y: 10, x: '-50%' },
      className: 'bottom-full left-1/2 -translate-x-1/2 mb-2',
    },
    bottom: {
      initial: { opacity: 0, y: -10, x: '-50%' },
      animate: { opacity: 1, y: 0, x: '-50%' },
      exit: { opacity: 0, y: -10, x: '-50%' },
      className: 'top-full left-1/2 -translate-x-1/2 mt-2',
    },
    left: {
      initial: { opacity: 0, x: 10, y: '-50%' },
      animate: { opacity: 1, x: 0, y: '-50%' },
      exit: { opacity: 0, x: 10, y: '-50%' },
      className: 'right-full top-1/2 -translate-y-1/2 mr-2',
    },
    right: {
      initial: { opacity: 0, x: -10, y: '-50%' },
      animate: { opacity: 1, x: 0, y: '-50%' },
      exit: { opacity: 0, x: -10, y: '-50%' },
      className: 'left-full top-1/2 -translate-y-1/2 ml-2',
    },
  };

  const currentPlacement = placementStyles[placement];

  return (
    <div
      className={cn("relative inline-block", className)}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && content && (
          <motion.div
            className={cn(
              "absolute z-50 pointer-events-none",
              currentPlacement.className
            )}
            initial={reduceMotion ? { opacity: 0 } : currentPlacement.initial}
            animate={reduceMotion ? { opacity: 1 } : currentPlacement.animate}
            exit={reduceMotion ? { opacity: 0 } : currentPlacement.exit}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
            <div
              className={cn(
                "px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-xl",
                "border border-gray-800",
                "backdrop-blur-sm",
                contentClassName
              )}
            >
              {content}
              
              {/* Arrow */}
              <div
                className={cn(
                  "absolute w-2 h-2 bg-gray-900 border-gray-800 transform rotate-45",
                  placement === 'top' && "top-full left-1/2 -translate-x-1/2 -translate-y-1/2 border-r border-b",
                  placement === 'bottom' && "bottom-full left-1/2 -translate-x-1/2 translate-y-1/2 border-l border-t",
                  placement === 'left' && "left-full top-1/2 -translate-y-1/2 -translate-x-1/2 border-t border-r",
                  placement === 'right' && "right-full top-1/2 -translate-y-1/2 translate-x-1/2 border-b border-l"
                )}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Animated tooltip with additional features
export const AnimatedTooltip = ({
  children,
  items = [],
  placement = 'top',
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const reduceMotion = shouldReduceMotion();

  return (
    <div
      className={cn("relative inline-block", className)}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && items.length > 0 && (
          <motion.div
            className={cn(
              "absolute z-50 pointer-events-none",
              placement === 'top' && "bottom-full left-1/2 -translate-x-1/2 mb-2",
              placement === 'bottom' && "top-full left-1/2 -translate-x-1/2 mt-2"
            )}
            initial={{ opacity: 0, scale: 0.95, y: placement === 'top' ? 10 : -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: placement === 'top' ? 10 : -10 }}
            transition={{ duration: 0.2 }}
          >
            <div className="bg-gray-900 rounded-lg shadow-2xl border border-gray-800 p-3 min-w-[200px]">
              <motion.div className="space-y-2">
                {items.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between text-sm"
                  >
                    <span className="text-gray-400">{item.label}:</span>
                    <span className="font-medium text-white ml-2">{item.value}</span>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Tooltip;