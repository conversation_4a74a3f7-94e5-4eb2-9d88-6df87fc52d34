import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../utils/cn'
import { shouldReduceMotion } from '../../utils/animations'

const badgeVariants = {
  default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
  secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
  destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
  outline: "text-foreground",
  success: "border-transparent bg-green-500 text-white hover:bg-green-600",
  warning: "border-transparent bg-yellow-500 text-white hover:bg-yellow-600",
  info: "border-transparent bg-blue-500 text-white hover:bg-blue-600",
  neural: "border-purple-500/30 bg-purple-900/20 text-purple-400 hover:bg-purple-900/30",
  glow: "border-transparent bg-gradient-to-r from-purple-600 to-blue-600 text-white",
}

function Badge({ 
  className, 
  variant = "default",
  animate = true,
  pulse = false,
  glow = false,
  children,
  count,
  max = 99,
  ...props 
}) {
  const reduceMotion = shouldReduceMotion();
  const Component = animate && !reduceMotion ? motion.div : 'div';
  
  const animationProps = animate && !reduceMotion ? {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0, opacity: 0 },
    transition: { type: 'spring', stiffness: 500, damping: 30 }
  } : {};

  // Handle count display
  const displayCount = count !== undefined ? (
    count > max ? `${max}+` : count
  ) : null;

  return (
    <Component
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        badgeVariants[variant],
        pulse && !reduceMotion && "animate-pulse",
        glow && "shadow-lg",
        glow && variant === 'neural' && "shadow-purple-500/25",
        glow && variant === 'success' && "shadow-green-500/25",
        className
      )}
      {...animationProps}
      {...props}
    >
      {displayCount !== null ? (
        <AnimatePresence mode="wait">
          <motion.span
            key={displayCount}
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {displayCount}
          </motion.span>
        </AnimatePresence>
      ) : (
        children
      )}
    </Component>
  )
}

// Animated badge group for status changes
export function AnimatedBadgeGroup({ badges = [], className }) {
  return (
    <AnimatePresence>
      <div className={cn("flex flex-wrap gap-2", className)}>
        {badges.map((badge) => (
          <Badge
            key={badge.id || badge.label}
            variant={badge.variant}
            animate
            {...badge}
          >
            {badge.label}
          </Badge>
        ))}
      </div>
    </AnimatePresence>
  )
}

// Live indicator badge
export function LiveBadge({ className, ...props }) {
  const reduceMotion = shouldReduceMotion();
  
  return (
    <Badge
      variant="destructive"
      className={cn("relative", className)}
      {...props}
    >
      <span className="relative flex items-center gap-1.5">
        <span className="relative flex h-2 w-2">
          {!reduceMotion && (
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75" />
          )}
          <span className="relative inline-flex rounded-full h-2 w-2 bg-white" />
        </span>
        LIVE
      </span>
    </Badge>
  )
}

export { Badge }
