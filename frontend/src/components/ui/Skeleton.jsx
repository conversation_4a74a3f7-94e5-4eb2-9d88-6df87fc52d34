import React from 'react';
import { cn } from '../../utils/cn';
import { shouldReduceMotion } from '../../utils/animations';

const Skeleton = ({ 
  className, 
  variant = 'default',
  animate = true,
  ...props 
}) => {
  const reduceMotion = shouldReduceMotion();
  
  const variants = {
    default: 'bg-gray-800/50',
    light: 'bg-gray-700/30',
    dark: 'bg-gray-900/50',
    purple: 'bg-purple-900/20',
  };

  return (
    <div
      className={cn(
        "rounded-md",
        variants[variant],
        animate && !reduceMotion && "animate-pulse",
        "relative overflow-hidden",
        className
      )}
      {...props}
    >
      {animate && !reduceMotion && (
        <div
          className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite]"
          style={{
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent)',
          }}
        />
      )}
    </div>
  );
};

// Skeleton variants for common UI patterns
export const SkeletonCard = ({ className, ...props }) => (
  <div className={cn("space-y-3", className)} {...props}>
    <Skeleton className="h-32 w-full" />
    <div className="space-y-2">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  </div>
);

export const SkeletonText = ({ lines = 3, className, ...props }) => (
  <div className={cn("space-y-2", className)} {...props}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        className={cn(
          "h-4",
          i === lines - 1 ? "w-3/4" : "w-full"
        )}
      />
    ))}
  </div>
);

export const SkeletonAvatar = ({ size = 'md', className, ...props }) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  return (
    <Skeleton
      className={cn("rounded-full", sizeClasses[size], className)}
      {...props}
    />
  );
};

export const SkeletonButton = ({ size = 'default', className, ...props }) => {
  const sizeClasses = {
    sm: 'h-9 w-20',
    default: 'h-10 w-24',
    lg: 'h-11 w-32',
  };

  return (
    <Skeleton
      className={cn("rounded-md", sizeClasses[size], className)}
      {...props}
    />
  );
};

export const SkeletonAgentCard = ({ className, ...props }) => (
  <div className={cn("p-6 space-y-4", className)} {...props}>
    <div className="flex items-start justify-between">
      <div className="flex items-center space-x-3">
        <SkeletonAvatar size="lg" />
        <div className="space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-3 w-24" />
        </div>
      </div>
      <Skeleton className="h-6 w-16 rounded-full" />
    </div>
    <SkeletonText lines={2} />
    <div className="flex gap-2">
      <SkeletonButton size="sm" />
      <SkeletonButton size="sm" />
    </div>
  </div>
);

export const SkeletonDashboard = ({ className, ...props }) => (
  <div className={cn("space-y-6", className)} {...props}>
    {/* Header */}
    <div className="flex items-center justify-between">
      <Skeleton className="h-8 w-48" />
      <div className="flex gap-2">
        <SkeletonButton />
        <SkeletonButton />
      </div>
    </div>
    
    {/* Stats Grid */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="p-4 space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-3 w-24" />
        </div>
      ))}
    </div>
    
    {/* Content Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <SkeletonCard key={i} />
      ))}
    </div>
  </div>
);

// Add shimmer animation to Tailwind
if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes shimmer {
      100% {
        transform: translateX(100%);
      }
    }
  `;
  document.head.appendChild(style);
}

export { Skeleton };