import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../utils/cn'
import { shouldReduceMotion } from '../../utils/animations'

const buttonVariants = {
  default: "bg-primary text-primary-foreground hover:bg-primary/90",
  destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
  outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  ghost: "hover:bg-accent hover:text-accent-foreground",
  link: "text-primary underline-offset-4 hover:underline",
  glow: "bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-purple-500/25",
  neural: "bg-gray-900 text-purple-400 border border-purple-500/30 hover:bg-purple-900/20 hover:border-purple-400/50",
}

const buttonSizes = {
  default: "h-10 px-4 py-2",
  sm: "h-9 rounded-md px-3",
  lg: "h-11 rounded-md px-8",
  icon: "h-10 w-10",
}

const Button = React.forwardRef(({ 
  className, 
  variant = "default", 
  size = "default",
  animate = true,
  ripple = true,
  pulse = false,
  children,
  onClick,
  ...props 
}, ref) => {
  const [ripples, setRipples] = useState([]);
  const reduceMotion = shouldReduceMotion();

  const handleClick = (e) => {
    if (ripple && !reduceMotion) {
      const button = e.currentTarget;
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const rippleId = Date.now();

      setRipples(prev => [...prev, { id: rippleId, x, y }]);
      
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== rippleId));
      }, 600);
    }

    onClick?.(e);
  };

  const buttonContent = (
    <>
      {children}
      {ripple && ripples.map(ripple => (
        <motion.span
          key={ripple.id}
          className="absolute rounded-full bg-white/30 pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
          }}
          initial={{ width: 0, height: 0, x: 0, y: 0, opacity: 1 }}
          animate={{ 
            width: 200, 
            height: 200, 
            x: -100, 
            y: -100, 
            opacity: 0 
          }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        />
      ))}
    </>
  );

  const Component = animate ? motion.button : 'button';
  
  const animationProps = animate && !reduceMotion ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { type: 'spring', stiffness: 400, damping: 30 }
  } : {};

  return (
    <Component
      className={cn(
        "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        "relative overflow-hidden",
        "transform-gpu will-change-transform",
        buttonVariants[variant],
        buttonSizes[size],
        pulse && !reduceMotion && "animate-pulse",
        className
      )}
      ref={ref}
      onClick={handleClick}
      {...animationProps}
      {...props}
    >
      {buttonContent}
    </Component>
  )
})
Button.displayName = "Button"

export { Button }
