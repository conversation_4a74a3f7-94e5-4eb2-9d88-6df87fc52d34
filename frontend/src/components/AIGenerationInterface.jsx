import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Brain, 
  Zap, 
  Sparkles, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Wand2,
  Target,
  Users,
  Building,
  Megaphone,
  ArrowRight
} from 'lucide-react'
import { <PERSON>hanced<PERSON>utton, <PERSON>hancedCard, LoadingSpinner } from './EnhancedDesignSystem'

const AIGenerationInterface = ({ onGenerate, isGenerating = false }) => {
  const [formData, setFormData] = useState({
    industry: '',
    audience: '',
    goal: '',
    tone: 'professional',
    length: 5,
    aiModel: 'gpt-4'
  })
  const [step, setStep] = useState(1)
  const [errors, setErrors] = useState({})

  const industries = [
    { id: 'saas', name: 'SaaS & Technology', icon: Brain },
    { id: 'ecommerce', name: 'E-commerce & Retail', icon: Building },
    { id: 'consulting', name: 'Consulting & Services', icon: Users },
    { id: 'education', name: 'Education & Training', icon: Target },
    { id: 'healthcare', name: 'Healthcare & Wellness', icon: CheckCircle },
    { id: 'finance', name: 'Finance & Insurance', icon: Zap },
    { id: 'marketing', name: 'Marketing & Advertising', icon: Megaphone },
    { id: 'other', name: 'Other Industry', icon: Sparkles }
  ]

  const tones = [
    { id: 'professional', name: 'Professional', desc: 'Formal and business-focused' },
    { id: 'friendly', name: 'Friendly', desc: 'Warm and approachable' },
    { id: 'casual', name: 'Casual', desc: 'Relaxed and conversational' },
    { id: 'urgent', name: 'Urgent', desc: 'Direct and action-oriented' },
    { id: 'educational', name: 'Educational', desc: 'Informative and helpful' }
  ]

  const aiModels = [
    { id: 'gpt-4', name: 'GPT-4', desc: 'Most advanced reasoning', premium: true },
    { id: 'AI-3-sonnet', name: 'AI 3', desc: 'Superior writing quality', premium: true },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5', desc: 'Fast and reliable', premium: false }
  ]

  const validateStep = (stepNumber) => {
    const newErrors = {}
    
    switch (stepNumber) {
      case 1:
        if (!formData.industry) newErrors.industry = 'Please select an industry'
        break
      case 2:
        if (!formData.audience) newErrors.audience = 'Please describe your target audience'
        if (!formData.goal) newErrors.goal = 'Please specify your goal'
        break
      case 3:
        // All fields have defaults, no validation needed
        break
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1)
    }
  }

  const handleGenerate = () => {
    if (validateStep(3)) {
      onGenerate(formData)
    }
  }

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  return (
    <EnhancedCard className="max-w-4xl mx-auto">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-8">
        {[1, 2, 3].map((stepNum) => (
          <div key={stepNum} className="flex items-center">
            <motion.div
              className={`
                w-10 h-10 rounded-full flex items-center justify-center font-semibold
                ${step >= stepNum 
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white' 
                  : 'bg-neutral-700 text-neutral-400'
                }
              `}
              animate={{ scale: step === stepNum ? 1.1 : 1 }}
              transition={{ duration: 0.3 }}
            >
              {step > stepNum ? <CheckCircle className="w-5 h-5" /> : stepNum}
            </motion.div>
            {stepNum < 3 && (
              <div className={`w-20 h-1 mx-4 rounded ${step > stepNum ? 'bg-purple-500' : 'bg-neutral-700'}`} />
            )}
          </div>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {/* Step 1: Industry Selection */}
        {step === 1 && (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-white mb-2">Select Your Industry</h2>
            <p className="text-neutral-400 mb-6">Choose the industry that best describes your business</p>
            
            <div className="grid md:grid-cols-2 gap-4 mb-8">
              {industries.map((industry) => {
                const Icon = industry.icon
                return (
                  <motion.button
                    key={industry.id}
                    onClick={() => updateFormData('industry', industry.id)}
                    className={`
                      p-4 rounded-xl border-2 transition-all duration-300 text-left
                      ${formData.industry === industry.id
                        ? 'border-purple-500 bg-purple-500/10 text-white'
                        : 'border-neutral-600 hover:border-neutral-500 text-neutral-300'
                      }
                    `}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-6 h-6" />
                      <span className="font-medium">{industry.name}</span>
                    </div>
                  </motion.button>
                )
              })}
            </div>
            
            {errors.industry && (
              <p className="text-red-400 text-sm mb-4 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {errors.industry}
              </p>
            )}
            
            <EnhancedButton 
              onClick={handleNext}
              disabled={!formData.industry}
              icon={ArrowRight}
              className="w-full"
            >
              Continue
            </EnhancedButton>
          </motion.div>
        )}

        {/* Step 2: Audience & Goal */}
        {step === 2 && (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-white mb-2">Define Your Audience & Goal</h2>
            <p className="text-neutral-400 mb-6">Tell us about your target audience and what you want to achieve</p>
            
            <div className="space-y-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Target Audience
                </label>
                <textarea
                  value={formData.audience}
                  onChange={(e) => updateFormData('audience', e.target.value)}
                  placeholder="e.g., Small business owners, Marketing managers, Enterprise decision makers..."
                  className="w-full p-4 bg-neutral-800 border border-neutral-600 rounded-xl text-white placeholder-neutral-500 focus:outline-none focus:border-purple-500 resize-none"
                  rows={3}
                />
                {errors.audience && (
                  <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    {errors.audience}
                  </p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Primary Goal
                </label>
                <textarea
                  value={formData.goal}
                  onChange={(e) => updateFormData('goal', e.target.value)}
                  placeholder="e.g., Convert trial users to paid subscriptions, Drive webinar registrations, Increase product sales..."
                  className="w-full p-4 bg-neutral-800 border border-neutral-600 rounded-xl text-white placeholder-neutral-500 focus:outline-none focus:border-purple-500 resize-none"
                  rows={3}
                />
                {errors.goal && (
                  <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    {errors.goal}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex gap-4">
              <EnhancedButton 
                variant="outline" 
                onClick={() => setStep(1)}
                className="flex-1"
              >
                Back
              </EnhancedButton>
              <EnhancedButton 
                onClick={handleNext}
                disabled={!formData.audience || !formData.goal}
                icon={ArrowRight}
                className="flex-1"
              >
                Continue
              </EnhancedButton>
            </div>
          </motion.div>
        )}

        {/* Step 3: Configuration */}
        {step === 3 && (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-white mb-2">Configure Your Sequence</h2>
            <p className="text-neutral-400 mb-6">Customize the tone, length, and AI model for your email sequence</p>
            
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {/* Tone Selection */}
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-3">Tone</label>
                <div className="space-y-2">
                  {tones.map((tone) => (
                    <motion.button
                      key={tone.id}
                      onClick={() => updateFormData('tone', tone.id)}
                      className={`
                        w-full p-3 rounded-lg border text-left transition-all duration-300
                        ${formData.tone === tone.id
                          ? 'border-purple-500 bg-purple-500/10 text-white'
                          : 'border-neutral-600 hover:border-neutral-500 text-neutral-300'
                        }
                      `}
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="font-medium">{tone.name}</div>
                      <div className="text-sm text-neutral-500">{tone.desc}</div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* AI Model & Length */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-3">
                    Email Sequence Length
                  </label>
                  <div className="flex items-center gap-4">
                    <input
                      type="range"
                      min="3"
                      max="10"
                      value={formData.length}
                      onChange={(e) => updateFormData('length', parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-lg font-medium">
                      {formData.length} emails
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-3">AI Model</label>
                  <div className="space-y-2">
                    {aiModels.map((model) => (
                      <motion.button
                        key={model.id}
                        onClick={() => updateFormData('aiModel', model.id)}
                        className={`
                          w-full p-3 rounded-lg border text-left transition-all duration-300
                          ${formData.aiModel === model.id
                            ? 'border-purple-500 bg-purple-500/10 text-white'
                            : 'border-neutral-600 hover:border-neutral-500 text-neutral-300'
                          }
                        `}
                        whileHover={{ scale: 1.01 }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{model.name}</div>
                            <div className="text-sm text-neutral-500">{model.desc}</div>
                          </div>
                          {model.premium && (
                            <span className="bg-gradient-to-r from-purple-500 to-amber-500 text-white px-2 py-1 rounded text-xs font-medium">
                              Premium
                            </span>
                          )}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex gap-4">
              <EnhancedButton 
                variant="outline" 
                onClick={() => setStep(2)}
                className="flex-1"
              >
                Back
              </EnhancedButton>
              <EnhancedButton 
                onClick={handleGenerate}
                disabled={isGenerating}
                icon={isGenerating ? Loader2 : Wand2}
                className="flex-1 bg-gradient-to-r from-purple-600 to-amber-600 hover:from-purple-500 hover:to-amber-500"
              >
                {isGenerating ? (
                  <>
                    <LoadingSpinner size="small" className="mr-2" />
                    Generating...
                  </>
                ) : (
                  'Generate Sequence'
                )}
              </EnhancedButton>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </EnhancedCard>
  )
}

export default AIGenerationInterface