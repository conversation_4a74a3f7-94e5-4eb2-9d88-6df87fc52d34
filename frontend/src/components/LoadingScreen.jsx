import React from 'react';
import { motion } from 'framer-motion';
import { Skeleton, SkeletonDashboard } from './ui/Skeleton';
import { cn } from '../utils/cn';

const LoadingScreen = ({ 
  variant = 'default',
  message = 'Loading...',
  className 
}) => {
  const variants = {
    default: Default<PERSON>oader,
    dashboard: Dashboard<PERSON>oa<PERSON>,
    agent: AgentLoader,
    neural: NeuralLoader,
  };

  const LoaderComponent = variants[variant] || variants.default;

  return (
    <div className={cn("min-h-screen flex items-center justify-center", className)}>
      <LoaderComponent message={message} />
    </div>
  );
};

// Default loader with animated logo
const DefaultLoader = ({ message }) => (
  <div className="text-center space-y-4">
    <motion.div
      className="w-16 h-16 mx-auto"
      animate={{
        rotate: 360,
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      }}
    >
      <div className="w-full h-full rounded-lg bg-gradient-to-tr from-purple-600 to-blue-600" />
    </motion.div>
    
    <motion.p
      className="text-gray-400"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.2 }}
    >
      {message}
    </motion.p>
  </div>
);

// Dashboard skeleton loader
const DashboardLoader = () => (
  <div className="w-full max-w-7xl mx-auto p-6">
    <SkeletonDashboard />
  </div>
);

// Agent-specific loader
const AgentLoader = ({ message }) => (
  <div className="text-center space-y-6">
    {/* Hexagon loader */}
    <div className="relative w-32 h-32 mx-auto">
      <svg viewBox="0 0 120 120" className="w-full h-full">
        <defs>
          <linearGradient id="agent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>
        
        {/* Animated hexagon */}
        <motion.polygon
          points="60,10 100,35 100,85 60,110 20,85 20,35"
          fill="none"
          stroke="url(#agent-gradient)"
          strokeWidth="2"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Center pulse */}
        <motion.circle
          cx="60"
          cy="60"
          r="20"
          fill="url(#agent-gradient)"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </svg>
    </div>
    
    <div className="space-y-2">
      <h3 className="text-lg font-semibold text-purple-400">Initializing Agents</h3>
      <p className="text-gray-400 text-sm">{message}</p>
    </div>
    
    {/* Progress dots */}
    <div className="flex justify-center gap-2">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 rounded-full bg-purple-500"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  </div>
);

// Neural network loader
const NeuralLoader = ({ message }) => (
  <div className="relative">
    {/* Neural nodes */}
    <svg width="200" height="200" className="mx-auto">
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>
      
      {/* Connection lines */}
      {[
        { x1: 50, y1: 50, x2: 150, y2: 50 },
        { x1: 50, y1: 150, x2: 150, y2: 150 },
        { x1: 50, y1: 50, x2: 100, y2: 100 },
        { x1: 150, y1: 50, x2: 100, y2: 100 },
        { x1: 50, y1: 150, x2: 100, y2: 100 },
        { x1: 150, y1: 150, x2: 100, y2: 100 },
      ].map((line, i) => (
        <motion.line
          key={i}
          x1={line.x1}
          y1={line.y1}
          x2={line.x2}
          y2={line.y2}
          stroke="url(#neural-gradient)"
          strokeWidth="2"
          strokeDasharray="5 5"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{
            pathLength: [0, 1, 0],
            opacity: [0, 0.6, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
      
      {/* Neural nodes */}
      {[
        { cx: 50, cy: 50, delay: 0 },
        { cx: 150, cy: 50, delay: 0.3 },
        { cx: 50, cy: 150, delay: 0.6 },
        { cx: 150, cy: 150, delay: 0.9 },
        { cx: 100, cy: 100, delay: 1.2 },
      ].map((node, i) => (
        <motion.circle
          key={i}
          cx={node.cx}
          cy={node.cy}
          r="8"
          fill="url(#neural-gradient)"
          filter="url(#glow)"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: node.delay,
            ease: "easeInOut"
          }}
        />
      ))}
      
      <defs>
        <linearGradient id="neural-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#8b5cf6" />
          <stop offset="100%" stopColor="#3b82f6" />
        </linearGradient>
      </defs>
    </svg>
    
    <motion.p
      className="text-center text-gray-400 mt-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5 }}
    >
      {message}
    </motion.p>
  </div>
);

export default LoadingScreen;