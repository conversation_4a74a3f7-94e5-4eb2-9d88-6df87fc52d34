import React, { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Crown, Users, Search, Mail, MessageSquare, BarChart3, Target, Brain,
  Zap, Clock, CheckCircle, AlertCircle, Play, Pause, Settings,
  Plus, Trash2, Copy, ArrowRight, Activity, TrendingUp
} from 'lucide-react'

/**
 * NeuroColony Advanced Workflow Canvas
 * N8N-style visual workflow builder with colony intelligence
 * Real-time execution monitoring and sophisticated node types
 */

const WorkflowCanvas = ({ workflow, onWorkflowChange, onExecute, executionStatus }) => {
  const [nodes, setNodes] = useState(workflow?.nodes || [])
  const [connections, setConnections] = useState(workflow?.connections || [])
  const [selectedNode, setSelectedNode] = useState(null)
  const [draggedNode, setDraggedNode] = useState(null)
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionStart, setConnectionStart] = useState(null)
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const canvasRef = useRef(null)

  // Node types with enhanced capabilities
  const nodeTypes = {
    trigger: {
      icon: Zap,
      color: 'from-yellow-500 to-orange-500',
      category: 'Triggers',
      variants: [
        { id: 'schedule', name: 'Schedule', description: 'Time-based trigger' },
        { id: 'webhook', name: 'Webhook', description: 'HTTP endpoint trigger' },
        { id: 'email_received', name: 'Email Received', description: 'Email trigger' },
        { id: 'form_submit', name: 'Form Submit', description: 'Form submission trigger' }
      ]
    },
    queen_agent: {
      icon: Crown,
      color: 'from-purple-500 to-pink-500',
      category: 'Colony Intelligence',
      variants: [
        { id: 'email_orchestrator', name: 'Email Orchestrator', description: 'Coordinates email campaigns' },
        { id: 'campaign_manager', name: 'Campaign Manager', description: 'Manages marketing campaigns' },
        { id: 'workflow_controller', name: 'Workflow Controller', description: 'Controls workflow execution' }
      ]
    },
    worker_agent: {
      icon: Users,
      color: 'from-blue-500 to-cyan-500',
      category: 'Colony Intelligence',
      variants: [
        { id: 'email_generator', name: 'Email Generator', description: 'Generates email content' },
        { id: 'subject_optimizer', name: 'Subject Optimizer', description: 'Optimizes subject lines' },
        { id: 'content_personalizer', name: 'Content Personalizer', description: 'Personalizes content' },
        { id: 'social_poster', name: 'Social Poster', description: 'Posts to social media' }
      ]
    },
    scout_agent: {
      icon: Search,
      color: 'from-green-500 to-emerald-500',
      category: 'Colony Intelligence',
      variants: [
        { id: 'performance_monitor', name: 'Performance Monitor', description: 'Monitors campaign performance' },
        { id: 'lead_scout', name: 'Lead Scout', description: 'Discovers new leads' },
        { id: 'competitor_scout', name: 'Competitor Scout', description: 'Monitors competitors' }
      ]
    },
    condition: {
      icon: Target,
      color: 'from-indigo-500 to-purple-500',
      category: 'Logic',
      variants: [
        { id: 'if_condition', name: 'If Condition', description: 'Conditional branching' },
        { id: 'switch', name: 'Switch', description: 'Multiple condition branching' },
        { id: 'filter', name: 'Filter', description: 'Filter data based on criteria' }
      ]
    },
    integration: {
      icon: Activity,
      color: 'from-teal-500 to-blue-500',
      category: 'Integrations',
      variants: [
        { id: 'mailchimp', name: 'Email Platform', description: 'Email platform integration' },
        { id: 'hubspot', name: 'CRM Platform A', description: 'Enterprise CRM integration' },
        { id: 'salesforce', name: 'CRM Platform B', description: 'Cloud CRM integration' },
        { id: 'google_analytics', name: 'Google Analytics', description: 'Analytics integration' }
      ]
    },
    action: {
      icon: TrendingUp,
      color: 'from-red-500 to-pink-500',
      category: 'Actions',
      variants: [
        { id: 'send_email', name: 'Send Email', description: 'Send email message' },
        { id: 'update_crm', name: 'Update CRM', description: 'Update CRM record' },
        { id: 'create_task', name: 'Create Task', description: 'Create task or reminder' },
        { id: 'send_notification', name: 'Send Notification', description: 'Send notification' }
      ]
    }
  }

  // Update workflow when nodes or connections change
  useEffect(() => {
    if (onWorkflowChange) {
      onWorkflowChange({
        ...workflow,
        nodes,
        connections
      })
    }
  }, [nodes, connections])

  // Handle node drag and drop
  const handleNodeDrop = useCallback((e) => {
    e.preventDefault()
    const rect = canvasRef.current.getBoundingClientRect()
    const x = (e.clientX - rect.left - canvasOffset.x) / zoom
    const y = (e.clientY - rect.top - canvasOffset.y) / zoom
    
    if (draggedNode) {
      const newNode = {
        id: `node_${Date.now()}`,
        type: draggedNode.type,
        variant: draggedNode.variant,
        position: { x, y },
        config: {},
        label: draggedNode.name,
        status: 'idle'
      }
      
      setNodes(prev => [...prev, newNode])
      setDraggedNode(null)
    }
  }, [draggedNode, canvasOffset, zoom])

  // Handle connection creation
  const startConnection = (nodeId, port) => {
    setIsConnecting(true)
    setConnectionStart({ nodeId, port })
  }

  const completeConnection = (nodeId, port) => {
    if (connectionStart && connectionStart.nodeId !== nodeId) {
      const newConnection = {
        id: `conn_${Date.now()}`,
        source: connectionStart,
        target: { nodeId, port }
      }
      setConnections(prev => [...prev, newConnection])
    }
    setIsConnecting(false)
    setConnectionStart(null)
  }

  // Node execution status colors
  const getNodeStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'ring-2 ring-blue-500 ring-opacity-50'
      case 'completed':
        return 'ring-2 ring-green-500 ring-opacity-50'
      case 'error':
        return 'ring-2 ring-red-500 ring-opacity-50'
      default:
        return ''
    }
  }

  // Get execution status for node
  const getNodeExecutionStatus = (nodeId) => {
    return executionStatus?.nodeStatuses?.[nodeId] || 'idle'
  }

  return (
    <div className="flex h-full bg-gray-50">
      {/* Node Palette */}
      <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Node Palette</h3>
          
          {Object.entries(nodeTypes).map(([typeKey, nodeType]) => (
            <div key={typeKey} className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <nodeType.icon className="w-4 h-4" />
                {nodeType.category}
              </h4>
              
              <div className="space-y-2">
                {nodeType.variants.map((variant) => (
                  <motion.div
                    key={variant.id}
                    draggable
                    onDragStart={() => setDraggedNode({ type: typeKey, variant: variant.id, name: variant.name })}
                    className={`p-3 rounded-lg border-2 border-dashed border-gray-300 cursor-grab active:cursor-grabbing hover:border-gray-400 transition-colors bg-gradient-to-r ${nodeType.color} bg-opacity-10`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center gap-3">
                      <nodeType.icon className="w-5 h-5 text-gray-600" />
                      <div>
                        <p className="font-medium text-sm text-gray-900">{variant.name}</p>
                        <p className="text-xs text-gray-600">{variant.description}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 relative overflow-hidden">
        <div
          ref={canvasRef}
          className="w-full h-full relative"
          onDrop={handleNodeDrop}
          onDragOver={(e) => e.preventDefault()}
          style={{
            backgroundImage: `radial-gradient(circle, #e5e7eb 1px, transparent 1px)`,
            backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
            backgroundPosition: `${canvasOffset.x}px ${canvasOffset.y}px`
          }}
        >
          {/* Connections */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            {connections.map((connection) => {
              const sourceNode = nodes.find(n => n.id === connection.source.nodeId)
              const targetNode = nodes.find(n => n.id === connection.target.nodeId)
              
              if (!sourceNode || !targetNode) return null
              
              const sourceX = (sourceNode.position.x + 150) * zoom + canvasOffset.x
              const sourceY = (sourceNode.position.y + 40) * zoom + canvasOffset.y
              const targetX = targetNode.position.x * zoom + canvasOffset.x
              const targetY = (targetNode.position.y + 40) * zoom + canvasOffset.y
              
              return (
                <g key={connection.id}>
                  <path
                    d={`M ${sourceX} ${sourceY} C ${sourceX + 50} ${sourceY} ${targetX - 50} ${targetY} ${targetX} ${targetY}`}
                    stroke="#6b7280"
                    strokeWidth="2"
                    fill="none"
                    className="drop-shadow-sm"
                  />
                  <circle
                    cx={targetX}
                    cy={targetY}
                    r="4"
                    fill="#6b7280"
                  />
                </g>
              )
            })}
          </svg>

          {/* Nodes */}
          {nodes.map((node) => {
            const nodeType = nodeTypes[node.type]
            const Icon = nodeType?.icon || Activity
            const status = getNodeExecutionStatus(node.id)
            
            return (
              <motion.div
                key={node.id}
                className={`absolute bg-white rounded-lg shadow-lg border-2 border-gray-200 cursor-pointer select-none ${getNodeStatusColor(status)}`}
                style={{
                  left: node.position.x * zoom + canvasOffset.x,
                  top: node.position.y * zoom + canvasOffset.y,
                  transform: `scale(${zoom})`
                }}
                onClick={() => setSelectedNode(node)}
                whileHover={{ scale: zoom * 1.05 }}
                drag
                dragMomentum={false}
                onDragEnd={(e, info) => {
                  const newX = (node.position.x * zoom + info.offset.x) / zoom
                  const newY = (node.position.y * zoom + info.offset.y) / zoom
                  
                  setNodes(prev => prev.map(n => 
                    n.id === node.id 
                      ? { ...n, position: { x: newX, y: newY } }
                      : n
                  ))
                }}
              >
                <div className="p-4 w-64">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${nodeType?.color || 'from-gray-500 to-gray-600'}`}>
                        <Icon className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-sm text-gray-900">{node.label}</p>
                        <p className="text-xs text-gray-600">{node.type}</p>
                      </div>
                    </div>
                    
                    {/* Status indicator */}
                    <div className="flex items-center gap-1">
                      {status === 'running' && <Activity className="w-4 h-4 text-blue-500 animate-pulse" />}
                      {status === 'completed' && <CheckCircle className="w-4 h-4 text-green-500" />}
                      {status === 'error' && <AlertCircle className="w-4 h-4 text-red-500" />}
                    </div>
                  </div>
                  
                  {/* Connection points */}
                  <div className="flex justify-between">
                    <div
                      className="w-3 h-3 bg-gray-400 rounded-full cursor-pointer hover:bg-gray-600"
                      onClick={() => completeConnection(node.id, 'input')}
                    />
                    <div
                      className="w-3 h-3 bg-blue-500 rounded-full cursor-pointer hover:bg-blue-600"
                      onClick={() => startConnection(node.id, 'output')}
                    />
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Canvas Controls */}
        <div className="absolute top-4 right-4 flex gap-2">
          <button
            onClick={() => setZoom(prev => Math.min(prev + 0.1, 2))}
            className="p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <Plus className="w-4 h-4" />
          </button>
          <button
            onClick={() => setZoom(prev => Math.max(prev - 0.1, 0.5))}
            className="p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          <button
            onClick={() => onExecute && onExecute()}
            disabled={executionStatus?.isRunning}
            className="p-2 bg-green-500 text-white rounded-lg shadow-md hover:shadow-lg transition-shadow disabled:opacity-50"
          >
            {executionStatus?.isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
        </div>

        {/* Execution Status Panel */}
        {executionStatus?.isRunning && (
          <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-900">Workflow Execution</h4>
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-blue-500 animate-pulse" />
                <span className="text-sm text-gray-600">Running...</span>
              </div>
            </div>
            
            <div className="space-y-2">
              {Object.entries(executionStatus.nodeStatuses || {}).map(([nodeId, status]) => {
                const node = nodes.find(n => n.id === nodeId)
                return (
                  <div key={nodeId} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">{node?.label || nodeId}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      status === 'completed' ? 'bg-green-100 text-green-800' :
                      status === 'running' ? 'bg-blue-100 text-blue-800' :
                      status === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {status}
                    </span>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>

      {/* Node Configuration Panel */}
      {selectedNode && (
        <div className="w-80 bg-white border-l border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Node Configuration</h3>
            <button
              onClick={() => setSelectedNode(null)}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Node Name
              </label>
              <input
                type="text"
                value={selectedNode.label}
                onChange={(e) => {
                  setNodes(prev => prev.map(n => 
                    n.id === selectedNode.id 
                      ? { ...n, label: e.target.value }
                      : n
                  ))
                  setSelectedNode({ ...selectedNode, label: e.target.value })
                }}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={selectedNode.description || ''}
                onChange={(e) => {
                  setNodes(prev => prev.map(n => 
                    n.id === selectedNode.id 
                      ? { ...n, description: e.target.value }
                      : n
                  ))
                  setSelectedNode({ ...selectedNode, description: e.target.value })
                }}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows="3"
              />
            </div>
            
            {/* Node-specific configuration would go here */}
            <div className="pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600">
                Node-specific configuration options will appear here based on the selected node type.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkflowCanvas
