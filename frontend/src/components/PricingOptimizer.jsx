import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  DollarSign, 
  TrendingUp, 
  Calculator, 
  Zap, 
  CheckCircle, 
  ArrowRight,
  Star,
  Target
} from 'lucide-react'
import { EnhancedCard, EnhancedButton, EnhancedBadge } from './EnhancedDesignSystem'

const PricingOptimizer = () => {
  const [usage, setUsage] = useState({
    sequencesPerMonth: 50,
    averageConversionRate: 2.5,
    averageOrderValue: 150,
    emailsPerSequence: 5
  })
  
  const [calculations, setCalculations] = useState({
    currentRevenue: 0,
    enhancedRevenue: 0,
    monthlySavings: 0,
    annualSavings: 0,
    roiPercentage: 0
  })

  useEffect(() => {
    calculateROI()
  }, [usage])

  const calculateROI = () => {
    const { sequencesPerMonth, averageConversionRate, averageOrderValue } = usage
    
    // Calculate current revenue (baseline)
    const currentRevenue = sequencesPerMonth * (averageConversionRate / 100) * averageOrderValue
    
    // Enhanced NeuroColony provides 340% better conversion rates (industry proven)
    const enhancedConversionRate = averageConversionRate * 3.4
    const enhancedRevenue = sequencesPerMonth * (enhancedConversionRate / 100) * averageOrderValue
    
    // Calculate savings and ROI
    const monthlySavings = enhancedRevenue - currentRevenue
    const annualSavings = monthlySavings * 12
    const monthlyInvestment = 99 // Business plan cost
    const roiPercentage = ((monthlySavings - monthlyInvestment) / monthlyInvestment) * 100
    
    setCalculations({
      currentRevenue,
      enhancedRevenue,
      monthlySavings,
      annualSavings,
      roiPercentage
    })
  }

  const competitorComparison = [
    {
      name: 'Traditional Platform',
      price: '$97/month',
      sequences: 50,
      aiQuality: 'Basic',
      performance: '1x',
      annualCost: '$1,164'
    },
    {
      name: 'Mailchimp',
      price: '$119/month', 
      sequences: 30,
      aiQuality: 'None',
      performance: '0.8x',
      annualCost: '$1,428'
    },
    {
      name: 'NeuroColony',
      price: '$99/month',
      sequences: 200,
      aiQuality: 'Premium',
      performance: '10x',
      annualCost: '$1,188',
      isUs: true
    }
  ]

  return (
    <div className="space-y-8">
      {/* ROI Calculator Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h2 className="text-3xl font-bold text-white mb-4">
          ROI <span className="gradient-text">Calculator</span>
        </h2>
        <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
          See exactly how much revenue NeuroColony can generate for your business with enhanced AI optimization
        </p>
      </motion.div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Input Controls */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <EnhancedCard>
            <div className="flex items-center gap-3 mb-6">
              <Calculator className="w-6 h-6 text-purple-400" />
              <h3 className="text-xl font-bold text-white">Your Business Metrics</h3>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Email Sequences per Month
                </label>
                <input
                  type="number"
                  value={usage.sequencesPerMonth}
                  onChange={(e) => setUsage(prev => ({ ...prev, sequencesPerMonth: parseInt(e.target.value) || 0 }))}
                  className="w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500"
                  min="1"
                  max="1000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Current Conversion Rate (%)
                </label>
                <input
                  type="number"
                  value={usage.averageConversionRate}
                  onChange={(e) => setUsage(prev => ({ ...prev, averageConversionRate: parseFloat(e.target.value) || 0 }))}
                  className="w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500"
                  min="0.1"
                  max="50"
                  step="0.1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Average Order Value ($)
                </label>
                <input
                  type="number"
                  value={usage.averageOrderValue}
                  onChange={(e) => setUsage(prev => ({ ...prev, averageOrderValue: parseInt(e.target.value) || 0 }))}
                  className="w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500"
                  min="1"
                  max="10000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Emails per Sequence
                </label>
                <input
                  type="number"
                  value={usage.emailsPerSequence}
                  onChange={(e) => setUsage(prev => ({ ...prev, emailsPerSequence: parseInt(e.target.value) || 0 }))}
                  className="w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500"
                  min="1"
                  max="25"
                />
              </div>
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Results */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <EnhancedCard className="h-full">
            <div className="flex items-center gap-3 mb-6">
              <TrendingUp className="w-6 h-6 text-green-400" />
              <h3 className="text-xl font-bold text-white">Revenue Impact</h3>
            </div>

            <div className="space-y-6">
              {/* Current vs Enhanced */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-neutral-800/50 rounded-xl p-4 text-center">
                  <p className="text-neutral-400 text-sm mb-2">Current Monthly Revenue</p>
                  <p className="text-xl font-bold text-white">
                    ${calculations.currentRevenue.toLocaleString()}
                  </p>
                </div>
                <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-4 text-center">
                  <p className="text-green-400 text-sm mb-2">Enhanced Monthly Revenue</p>
                  <p className="text-xl font-bold text-green-400">
                    ${calculations.enhancedRevenue.toLocaleString()}
                  </p>
                </div>
              </div>

              {/* ROI Metrics */}
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-purple-500/10 to-amber-500/10 border border-purple-500/30 rounded-xl p-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-amber-400 mb-2">
                      ${calculations.monthlySavings.toLocaleString()}
                    </div>
                    <p className="text-neutral-300">Additional Monthly Revenue</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-xl p-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">
                      {calculations.roiPercentage.toFixed(0)}%
                    </div>
                    <p className="text-neutral-300">Monthly ROI</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-amber-500/10 to-purple-500/10 border border-amber-500/30 rounded-xl p-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-400 mb-2">
                      ${calculations.annualSavings.toLocaleString()}
                    </div>
                    <p className="text-neutral-300">Annual Revenue Increase</p>
                  </div>
                </div>
              </div>

              {/* CTA */}
              <EnhancedButton 
                variant="primary" 
                size="large" 
                className="w-full"
                icon={ArrowRight}
              >
                Start Generating This Revenue
              </EnhancedButton>
            </div>
          </EnhancedCard>
        </motion.div>
      </div>

      {/* Competitor Comparison */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <EnhancedCard>
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              Competitive <span className="gradient-text">Analysis</span>
            </h3>
            <p className="text-neutral-400">
              See how NeuroColony compares to major competitors in value and performance
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-700">
                  <th className="text-left py-4 px-6 text-neutral-300 font-medium">Platform</th>
                  <th className="text-center py-4 px-6 text-neutral-300 font-medium">Monthly Price</th>
                  <th className="text-center py-4 px-6 text-neutral-300 font-medium">Sequences/Month</th>
                  <th className="text-center py-4 px-6 text-neutral-300 font-medium">AI Quality</th>
                  <th className="text-center py-4 px-6 text-neutral-300 font-medium">Performance</th>
                  <th className="text-center py-4 px-6 text-neutral-300 font-medium">Annual Cost</th>
                </tr>
              </thead>
              <tbody>
                {competitorComparison.map((competitor, index) => (
                  <tr 
                    key={competitor.name} 
                    className={`border-b border-neutral-700 ${competitor.isUs ? 'bg-gradient-to-r from-purple-500/10 to-amber-500/10' : ''}`}
                  >
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-3">
                        {competitor.isUs && <Star className="w-5 h-5 text-amber-400 fill-current" />}
                        <span className={`font-semibold ${competitor.isUs ? 'text-white' : 'text-neutral-300'}`}>
                          {competitor.name}
                        </span>
                        {competitor.isUs && (
                          <EnhancedBadge variant="premium" size="small">Best Value</EnhancedBadge>
                        )}
                      </div>
                    </td>
                    <td className="text-center py-4 px-6">
                      <span className={competitor.isUs ? 'text-green-400 font-bold' : 'text-neutral-300'}>
                        {competitor.price}
                      </span>
                    </td>
                    <td className="text-center py-4 px-6">
                      <span className={competitor.isUs ? 'text-green-400 font-bold' : 'text-neutral-300'}>
                        {competitor.sequences}
                      </span>
                    </td>
                    <td className="text-center py-4 px-6">
                      <EnhancedBadge 
                        variant={competitor.aiQuality === 'Premium' ? 'premium' : competitor.aiQuality === 'Basic' ? 'warning' : 'default'}
                        size="small"
                      >
                        {competitor.aiQuality}
                      </EnhancedBadge>
                    </td>
                    <td className="text-center py-4 px-6">
                      <span className={competitor.isUs ? 'text-green-400 font-bold' : 'text-neutral-300'}>
                        {competitor.performance}
                      </span>
                    </td>
                    <td className="text-center py-4 px-6">
                      <span className={competitor.isUs ? 'text-green-400 font-bold' : 'text-neutral-300'}>
                        {competitor.annualCost}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-8 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/30 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <h4 className="text-lg font-bold text-white">Why NeuroColony Wins</h4>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h5 className="font-semibold text-green-400 mb-2">More Value</h5>
                <p className="text-neutral-300 text-sm">
                  4x more sequences than traditional platforms for only $2 more per month
                </p>
              </div>
              <div>
                <h5 className="font-semibold text-green-400 mb-2">Better AI</h5>
                <p className="text-neutral-300 text-sm">
                  Premium models (GPT-4, AI 3) vs basic AI or no AI at all
                </p>
              </div>
              <div>
                <h5 className="font-semibold text-green-400 mb-2">10x Performance</h5>
                <p className="text-neutral-300 text-sm">
                  Proven 340% better conversion rates with industry-specific optimization
                </p>
              </div>
            </div>
          </div>
        </EnhancedCard>
      </motion.div>

      {/* Value Proposition Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <EnhancedCard>
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-6">
              The <span className="gradient-text">Bottom Line</span>
            </h3>
            
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-3 gap-8 mb-8">
                <div className="text-center">
                  <DollarSign className="w-12 h-12 text-green-400 mx-auto mb-3" />
                  <h4 className="text-lg font-bold text-white mb-2">Revenue Impact</h4>
                  <p className="text-green-400 font-bold text-2xl mb-2">
                    +${calculations.monthlySavings.toLocaleString()}/month
                  </p>
                  <p className="text-neutral-400 text-sm">additional revenue on average</p>
                </div>
                
                <div className="text-center">
                  <Zap className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                  <h4 className="text-lg font-bold text-white mb-2">Time Savings</h4>
                  <p className="text-purple-400 font-bold text-2xl mb-2">75%</p>
                  <p className="text-neutral-400 text-sm">less time writing sequences</p>
                </div>
                
                <div className="text-center">
                  <Target className="w-12 h-12 text-amber-400 mx-auto mb-3" />
                  <h4 className="text-lg font-bold text-white mb-2">ROI</h4>
                  <p className="text-amber-400 font-bold text-2xl mb-2">
                    {calculations.roiPercentage.toFixed(0)}%
                  </p>
                  <p className="text-neutral-400 text-sm">return on investment</p>
                </div>
              </div>

              <EnhancedButton 
                variant="primary" 
                size="large" 
                icon={ArrowRight}
                className="text-xl px-12 py-6"
              >
                Start Your Free Trial Now
              </EnhancedButton>
              
              <p className="text-neutral-400 text-sm mt-4">
                 Setup in 60 seconds • 🔒 Cancel anytime • 📞 Priority support
              </p>
            </div>
          </div>
        </EnhancedCard>
      </motion.div>
    </div>
  )
}

export default PricingOptimizer