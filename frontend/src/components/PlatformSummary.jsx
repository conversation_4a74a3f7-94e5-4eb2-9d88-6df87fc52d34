import React from 'react'
import { motion } from 'framer-motion'
import { 
  Bot, 
  Workflow, 
  Network, 
  Users, 
  Zap, 
  TrendingUp,
  Shield,
  Globe
} from 'lucide-react'

const PlatformSummary = () => {
  const stats = [
    {
      icon: <Bot className="h-8 w-8 text-purple-400" />,
      value: "50+",
      label: "AI Agents",
      description: "Specialized marketing automation agents"
    },
    {
      icon: <Workflow className="h-8 w-8 text-blue-400" />,
      value: "Visual",
      label: "Workflow Builder",
      description: "Drag-and-drop automation canvas"
    },
    {
      icon: <Network className="h-8 w-8 text-green-400" />,
      value: "40+",
      label: "Integrations",
      description: "Marketing platform connections"
    },
    {
      icon: <Users className="h-8 w-8 text-amber-400" />,
      value: "Team",
      label: "Collaboration",
      description: "Multi-user workspaces"
    }
  ]

  const capabilities = [
    {
      icon: <Zap className="h-6 w-6 text-yellow-400" />,
      title: "Advanced AI Powered",
      description: "Advanced AI reasoning for marketing automation"
    },
    {
      icon: <TrendingUp className="h-6 w-6 text-green-400" />,
      title: "Performance Analytics",
      description: "Real-time metrics and conversion tracking"
    },
    {
      icon: <Shield className="h-6 w-6 text-blue-400" />,
      title: "Enterprise Security",
      description: "SOC 2 compliance and data protection"
    },
    {
      icon: <Globe className="h-6 w-6 text-purple-400" />,
      title: "Global Scale",
      description: "Multi-region deployment and CDN"
    }
  ]

  return (
    <div className="max-w-6xl mx-auto px-4 py-12">
      {/* Platform Overview */}
      <motion.div 
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Complete AI Agent Platform
        </h2>
        <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
          Transform your marketing operations with intelligent automation that scales from startups to enterprise.
        </p>
      </motion.div>

      {/* Core Stats */}
      <div className="grid md:grid-cols-4 gap-6 mb-12">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            className="card text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <div className="mb-4 flex justify-center">
              {stat.icon}
            </div>
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-lg font-semibold text-neutral-300 mb-2">{stat.label}</div>
            <div className="text-sm text-neutral-400">{stat.description}</div>
          </motion.div>
        ))}
      </div>

      {/* Platform Capabilities */}
      <motion.div
        className="grid md:grid-cols-2 gap-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        {capabilities.map((capability, index) => (
          <div key={index} className="flex items-start space-x-4">
            <div className="flex-shrink-0 p-2 bg-neutral-800/50 rounded-lg">
              {capability.icon}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-1">
                {capability.title}
              </h3>
              <p className="text-neutral-400">
                {capability.description}
              </p>
            </div>
          </div>
        ))}
      </motion.div>

      {/* Value Proposition */}
      <motion.div
        className="mt-12 p-8 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-2xl text-center"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <h3 className="text-2xl font-bold text-white mb-4">
          Why Choose NeuroColony?
        </h3>
        <div className="grid md:grid-cols-3 gap-6 text-left">
          <div>
            <div className="text-purple-400 font-bold text-lg mb-2"> Built for Marketing</div>
            <p className="text-neutral-300 text-sm">
              Unlike generic automation tools, we're purpose-built for marketing teams with specialized agents and workflows.
            </p>
          </div>
          <div>
            <div className="text-blue-400 font-bold text-lg mb-2">🧠 AI-First Approach</div>
            <p className="text-neutral-300 text-sm">
              Every feature is enhanced with Advanced AI intelligence, making your automations smarter and more effective.
            </p>
          </div>
          <div>
            <div className="text-green-400 font-bold text-lg mb-2"> Proven Results</div>
            <p className="text-neutral-300 text-sm">
              Our customers see 3x better conversion rates and 75% time savings on marketing operations.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default PlatformSummary