import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Menu, X, User, LogOut, Bot, Layers, Network, Brain, Hexagon, Shield, Activity, Crown, GitBranch, Sparkles } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import UsageIndicator from './UsageIndicator'
import Logo from './Logo'
import { colonyTheme, getTextClass, getButtonClass, cn } from '../design-system/colony-theme'
import { Icon, NeuralPattern } from '../design-system/colony-icons'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()
  const { user, logout, isAuthenticated } = useAuth()

  const handleLogout = async () => {
    await logout()
    navigate('/')
    setIsOpen(false)
  }

  return (
    <nav className="backdrop-blur-md bg-gradient-to-r from-gray-900/95 via-neural-950/95 to-gray-900/95 border-b border-neural-500/30 sticky top-0 z-50">
      <div className="absolute inset-0 opacity-5">
        <NeuralPattern />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* NeuroColony Branding */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center group">
              <Logo className="h-10 w-auto" variant="full" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/pricing"
              className={cn(
                "font-medium transition-all duration-200 flex items-center gap-2",
                "text-gray-300 hover:text-neural-400",
                "hover:scale-105"
              )}
            >
              <Crown className="w-4 h-4" />
              Colony Tiers
            </Link>
            <Link
              to="/templates"
              className={cn(
                "font-medium transition-all duration-200 flex items-center gap-2",
                "text-gray-300 hover:text-honey-400",
                "hover:scale-105"
              )}
            >
              <Hexagon className="w-4 h-4" />
              Agent Blueprints
            </Link>
            <Link
              to="/contact"
              className={cn(
                "font-medium transition-all duration-200 flex items-center gap-2",
                "text-gray-300 hover:text-neural-400",
                "hover:scale-105"
              )}
            >
              <Network className="w-4 h-4" />
              Neural Network
            </Link>
            
            {isAuthenticated && user ? (
              <div className="flex items-center space-x-4">
                <UsageIndicator user={user} compact={true} />
                <Link 
                  to="/colony-command" 
                  className={cn(
                    "font-bold transition-all duration-200 flex items-center gap-2",
                    "px-4 py-2 rounded-lg",
                    "bg-gradient-to-r from-honey-500/20 to-honey-600/20",
                    "border border-honey-500/40",
                    "text-honey-300 hover:text-honey-200",
                    "hover:from-honey-500/30 hover:to-honey-600/30",
                    "hover:scale-105 hover:shadow-lg hover:shadow-honey-500/20"
                  )}
                >
                  <Icon name="colony" size={18} />
                  Colony Command
                </Link>
                <Link
                  to="/agent-marketplace"
                  className={cn(
                    "font-medium transition-all duration-200 flex items-center gap-2",
                    "text-neural-300 hover:text-neural-200",
                    "hover:scale-105"
                  )}
                >
                  <Icon name="swarm" size={18} />
                  Agent Swarm
                </Link>
                <Link
                  to="/integration-hub"
                  className={cn(
                    "font-medium transition-all duration-200 flex items-center gap-2",
                    "text-swarm-300 hover:text-swarm-200",
                    "hover:scale-105"
                  )}
                >
                  <Icon name="network" size={18} />
                  Neural Hub
                </Link>
                <Link 
                  to="/agent-builder" 
                  className={cn(
                    getButtonClass('primary'),
                    "flex items-center gap-2 px-4 py-2 rounded-lg"
                  )}
                >
                  <Icon name="launch" size={18} />
                  Deploy Colony
                </Link>
                
                {/* User Menu */}
                <div className="relative group">
                  <button className={cn(
                    "flex items-center space-x-2 transition-all duration-200",
                    "text-gray-300 hover:text-white",
                    "p-2 rounded-lg hover:bg-neural-500/10"
                  )}>
                    <div className="w-8 h-8 bg-gradient-to-br from-neural-500 to-neural-600 rounded-lg flex items-center justify-center">
                      <User className="w-5 h-5 text-white" />
                    </div>
                    <span className="hidden lg:block font-medium">{user.name}</span>
                  </button>
                  
                  {/* Dropdown */}
                  <div className="absolute right-0 mt-2 w-56 bg-gray-900/95 backdrop-blur-md rounded-lg shadow-xl border border-neural-500/30 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-2">
                      <Link 
                        to="/colony-command" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-honey-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Icon name="colony" size={18} />
                        Colony Overview
                      </Link>
                      <Link 
                        to="/agent-dashboard" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-neural-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Icon name="worker" size={18} />
                        My Agents
                      </Link>
                      <Link 
                        to="/agent-marketplace" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-neural-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Icon name="hive" size={18} />
                        Colony Marketplace
                      </Link>
                      <Link 
                        to="/agent-builder" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-neural-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Icon name="architect" size={18} />
                        Colony Architect
                      </Link>
                      <div className="border-t border-neural-500/20 my-2" />
                      <Link 
                        to="/billing" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-neural-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Crown className="w-4 h-4" />
                        Colony Subscription
                      </Link>
                      <Link 
                        to="/settings" 
                        className={cn(
                          "block px-4 py-2.5 text-sm transition-all duration-200",
                          "text-gray-300 hover:text-white",
                          "hover:bg-neural-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <Shield className="w-4 h-4" />
                        Neural Settings
                      </Link>
                      <div className="border-t border-neural-500/20 my-2" />
                      <button 
                        onClick={handleLogout}
                        className={cn(
                          "w-full text-left px-4 py-2.5 text-sm transition-all duration-200",
                          "text-alert-400 hover:text-alert-300",
                          "hover:bg-alert-500/20",
                          "flex items-center gap-3"
                        )}
                      >
                        <LogOut className="w-4 h-4" />
                        Disconnect Neural Link
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link 
                  to="/login" 
                  className={cn(
                    "font-medium transition-all duration-200",
                    "text-gray-300 hover:text-neural-400",
                    "hover:scale-105"
                  )}
                >
                  Connect Neural Link
                </Link>
                <Link 
                  to="/register" 
                  className={cn(
                    getButtonClass('primary'),
                    "px-4 py-2 rounded-lg flex items-center gap-2"
                  )}
                >
                  <Sparkles className="w-4 h-4" />
                  Join Colony
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={cn(
                "p-2 rounded-lg transition-all duration-200",
                "text-gray-300 hover:text-white",
                "hover:bg-neural-500/10"
              )}
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="md:hidden bg-gray-900/95 backdrop-blur-md border-t border-neural-500/20"
          >
            <div className="px-4 py-4 space-y-2">
              <Link 
                to="/pricing" 
                className={cn(
                  "block py-3 px-3 rounded-lg transition-all duration-200",
                  "text-gray-300 hover:text-white",
                  "hover:bg-neural-500/10",
                  "flex items-center gap-3"
                )}
                onClick={() => setIsOpen(false)}
              >
                <Crown className="w-5 h-5" />
                Colony Tiers
              </Link>
              <Link 
                to="/templates" 
                className={cn(
                  "block py-3 px-3 rounded-lg transition-all duration-200",
                  "text-gray-300 hover:text-white",
                  "hover:bg-honey-500/10",
                  "flex items-center gap-3"
                )}
                onClick={() => setIsOpen(false)}
              >
                <Hexagon className="w-5 h-5" />
                Agent Blueprints
              </Link>
              <Link 
                to="/contact" 
                className={cn(
                  "block py-3 px-3 rounded-lg transition-all duration-200",
                  "text-gray-300 hover:text-white",
                  "hover:bg-neural-500/10",
                  "flex items-center gap-3"
                )}
                onClick={() => setIsOpen(false)}
              >
                <Network className="w-5 h-5" />
                Neural Network
              </Link>
              
              {isAuthenticated && user ? (
                <>
                  <div className="border-t border-neural-500/20 my-2" />
                  <Link 
                    to="/colony-command" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "bg-gradient-to-r from-honey-500/20 to-honey-600/20",
                      "border border-honey-500/40",
                      "text-honey-300 hover:text-honey-200",
                      "flex items-center gap-3"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon name="colony" size={20} />
                    Colony Command
                  </Link>
                  <Link 
                    to="/agent-marketplace" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "text-gray-300 hover:text-white",
                      "hover:bg-neural-500/10",
                      "flex items-center gap-3"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon name="swarm" size={20} />
                    Agent Swarm
                  </Link>
                  <Link 
                    to="/integration-hub" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "text-gray-300 hover:text-white",
                      "hover:bg-neural-500/10",
                      "flex items-center gap-3"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon name="network" size={20} />
                    Neural Hub
                  </Link>
                  <div className="border-t border-neural-500/20 my-2" />
                  <Link 
                    to="/billing" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "text-gray-300 hover:text-white",
                      "hover:bg-neural-500/10",
                      "flex items-center gap-3"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Crown className="w-5 h-5" />
                    Colony Subscription
                  </Link>
                  <Link 
                    to="/settings" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "text-gray-300 hover:text-white",
                      "hover:bg-neural-500/10",
                      "flex items-center gap-3"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Shield className="w-5 h-5" />
                    Neural Settings
                  </Link>
                  <button 
                    onClick={handleLogout}
                    className={cn(
                      "w-full text-left py-3 px-3 rounded-lg transition-all duration-200",
                      "text-alert-400 hover:text-alert-300",
                      "hover:bg-alert-500/20",
                      "flex items-center gap-3"
                    )}
                  >
                    <LogOut className="w-5 h-5" />
                    Disconnect Neural Link
                  </button>
                </>
              ) : (
                <>
                  <div className="border-t border-neural-500/20 my-2" />
                  <Link 
                    to="/login" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "text-gray-300 hover:text-white",
                      "hover:bg-neural-500/10"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    Connect Neural Link
                  </Link>
                  <Link 
                    to="/register" 
                    className={cn(
                      "block py-3 px-3 rounded-lg transition-all duration-200",
                      "bg-gradient-to-r from-neural-600 to-neural-700",
                      "text-white font-medium text-center"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    Join Colony
                  </Link>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}

export default Navbar