import React from 'react';
import { motion } from 'framer-motion';

const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'blue', 
  text = null, 
  fullScreen = false,
  overlay = false 
}) => {
  const sizes = {
    small: 'h-4 w-4',
    medium: 'h-8 w-8', 
    large: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const colors = {
    blue: 'border-blue-600',
    gray: 'border-gray-600',
    white: 'border-white',
    green: 'border-green-600',
    red: 'border-red-600'
  };

  const textSizes = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
    xl: 'text-xl'
  };

  const spinnerClasses = `
    ${sizes[size]} 
    border-2 
    ${colors[color]} 
    border-t-transparent 
    rounded-full 
    animate-spin
  `;

  const content = (
    <div className="flex flex-col items-center justify-center space-y-3">
      {/* Animated Spinner */}
      <motion.div
        className={spinnerClasses}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      />
      
      {/* Loading Text */}
      {text && (
        <motion.p
          className={`${textSizes[size]} font-medium text-gray-600`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
        {content}
      </div>
    );
  }

  if (overlay) {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-40">
        {content}
      </div>
    );
  }

  return content;
};

// Specialized loading components
export const PageLoader = ({ text = "Loading..." }) => (
  <LoadingSpinner size="large" color="blue" text={text} fullScreen />
);

export const ButtonLoader = ({ text = "Loading..." }) => (
  <LoadingSpinner size="small" color="white" text={text} />
);

export const CardLoader = ({ text = "Loading..." }) => (
  <div className="flex items-center justify-center py-12">
    <LoadingSpinner size="medium" color="blue" text={text} />
  </div>
);

export const OverlayLoader = ({ text = "Processing..." }) => (
  <LoadingSpinner size="large" color="blue" text={text} overlay />
);

// AI Generation specific loader with animated dots
export const AIGenerationLoader = ({ stage = "Analyzing your business..." }) => {
  const stages = [
    "Analyzing your business...",
    "Applying psychology frameworks...", 
    "Crafting persuasive copy...",
    "Optimizing for conversions...",
    "Finalizing your sequence..."
  ];

  const [currentStage, setCurrentStage] = React.useState(stage);
  const [stageIndex, setStageIndex] = React.useState(0);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setStageIndex((prev) => (prev + 1) % stages.length);
      setCurrentStage(stages[stageIndex]);
    }, 2000);

    return () => clearInterval(interval);
  }, [stageIndex]);

  return (
    <div className="flex flex-col items-center justify-center space-y-4 py-8">
      {/* Animated AI Brain Icon */}
      <motion.div
        className="relative"
        animate={{ 
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="h-16 w-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
          <motion.div
            className="text-white text-2xl font-bold"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            AI
          </motion.div>
        </div>
        
        {/* Pulsing rings */}
        <motion.div
          className="absolute inset-0 border-2 border-blue-400 rounded-xl"
          animate={{ scale: [1, 1.2, 1], opacity: [0.8, 0.3, 0.8] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        <motion.div
          className="absolute inset-0 border-2 border-purple-400 rounded-xl"
          animate={{ scale: [1, 1.3, 1], opacity: [0.6, 0.2, 0.6] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
        />
      </motion.div>

      {/* Stage Text */}
      <motion.div
        key={currentStage}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="text-center"
      >
        <p className="text-lg font-medium text-gray-700 mb-2">
          {currentStage}
        </p>
        <div className="flex items-center justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-blue-500 rounded-full"
              animate={{ 
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{ 
                duration: 1,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
      </motion.div>

      {/* Progress Bar */}
      <div className="w-64 bg-gray-200 rounded-full h-2">
        <motion.div
          className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
          initial={{ width: "0%" }}
          animate={{ width: "100%" }}
          transition={{ duration: 15, ease: "easeInOut" }}
        />
      </div>
    </div>
  );
};

export default LoadingSpinner;