import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const Logo = ({ className = "h-8 w-auto", variant = "full", animated = false }) => {
  const [isHovered, setIsHovered] = useState(false)
  const [shouldAnimate, setShouldAnimate] = useState(false)

  // Periodic animation every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setShouldAnimate(true)
      setTimeout(() => setShouldAnimate(false), 2000)
    }, 8000)

    return () => clearInterval(interval)
  }, [])
  
  const MotionSVG = animated ? motion.svg : 'svg'
  const MotionCircle = animated ? motion.circle : 'circle'
  const MotionPath = animated ? motion.path : 'path'
  
  if (variant === "icon") {
    return (
      <MotionSVG
        className={className}
        viewBox="0 0 50 50"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={animated ? { duration: 0.8, ease: "easeOut" } : undefined}
      >
        {/* Ant-Inspired Design - Professional Minimalist */}

        {/* Main Ant Body - Elliptical Shape */}
        <motion.ellipse
          cx="25"
          cy="28"
          rx="12"
          ry="8"
          fill="url(#antBodyGradient)"
          filter="url(#glow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.2 } : undefined}
        />

        {/* Ant Head - Circle */}
        <motion.circle
          cx="25"
          cy="15"
          r="6"
          fill="url(#antHeadGradient)"
          filter="url(#glow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.4 } : undefined}
        />

        {/* Ant Thorax - Small Circle */}
        <motion.circle
          cx="25"
          cy="22"
          r="4"
          fill="url(#antThoraxGradient)"
          filter="url(#softGlow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.3 } : undefined}
        />

        {/* Ant Antennae */}
        <motion.path
          d="M22 12 Q18 8 16 6"
          stroke="#2563eb"
          strokeWidth="1.5"
          strokeLinecap="round"
          fill="none"
          initial={animated ? { pathLength: 0 } : undefined}
          animate={animated ? { pathLength: 1 } : undefined}
          transition={animated ? { duration: 0.8, delay: 0.6 } : undefined}
        />
        <motion.path
          d="M28 12 Q32 8 34 6"
          stroke="#2563eb"
          strokeWidth="1.5"
          strokeLinecap="round"
          fill="none"
          initial={animated ? { pathLength: 0 } : undefined}
          animate={animated ? { pathLength: 1 } : undefined}
          transition={animated ? { duration: 0.8, delay: 0.7 } : undefined}
        />

        {/* Antennae Tips */}
        <motion.circle cx="16" cy="6" r="1.5" fill="#7c3aed"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.4, delay: 0.8 } : undefined}
        />
        <motion.circle cx="34" cy="6" r="1.5" fill="#7c3aed"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.4, delay: 0.9 } : undefined}
        />

        {/* Neural Network Connections - Minimalist */}
        <motion.g
          opacity="0.6"
          animate={shouldAnimate ? {
            opacity: [0.6, 1, 0.6],
          } : {}}
          transition={{ duration: 2, ease: "easeInOut" }}
        >
          {/* Connection Nodes */}
          <motion.circle cx="15" cy="25" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, ease: "easeInOut" }}
          />
          <motion.circle cx="35" cy="25" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, delay: 0.3, ease: "easeInOut" }}
          />
          <motion.circle cx="25" cy="38" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, delay: 0.6, ease: "easeInOut" }}
          />

          {/* Connection Lines */}
          <motion.path
            d="M15 25 L35 25"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1 } : undefined}
          />
          <motion.path
            d="M15 25 L25 38"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1.2 } : undefined}
          />
          <motion.path
            d="M35 25 L25 38"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1.4 } : undefined}
          />
        </motion.g>

        {/* Ant-Inspired Gradients & Effects */}
        <defs>
          {/* Ant Body Gradient - Deep Neural Blue */}
          <linearGradient id="antBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#2563eb" />
            <stop offset="50%" stopColor="#1d4ed8" />
            <stop offset="100%" stopColor="#1e40af" />
          </linearGradient>

          {/* Ant Head Gradient - Electric Purple */}
          <linearGradient id="antHeadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#7c3aed" />
            <stop offset="50%" stopColor="#6d28d9" />
            <stop offset="100%" stopColor="#5b21b6" />
          </linearGradient>

          {/* Ant Thorax Gradient - Blend */}
          <linearGradient id="antThoraxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#8b5cf6" />
          </linearGradient>

          {/* Professional Glow Effects */}
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="nodeGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.8" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
      </MotionSVG>
    )
  }

  return (
    <motion.div 
      className={`flex items-center gap-4 ${className}`}
      initial={animated ? { x: -20, opacity: 0 } : undefined}
      animate={animated ? { x: 0, opacity: 1 } : undefined}
      transition={animated ? { duration: 0.8, delay: 0.2 } : undefined}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Ant-Inspired Icon */}
      <motion.svg
        className="h-12 w-12"
        viewBox="0 0 50 50"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        whileHover={{ scale: 1.08, rotate: 2 }}
        animate={shouldAnimate || isHovered ? {
          filter: [
            "drop-shadow(0 0 8px rgba(37, 99, 235, 0.4))",
            "drop-shadow(0 0 16px rgba(37, 99, 235, 0.6))",
            "drop-shadow(0 0 8px rgba(37, 99, 235, 0.4))"
          ]
        } : undefined}
        transition={{
          duration: 0.3,
          filter: { duration: 2, repeat: Infinity, ease: "easeInOut" }
        }}
      >
        {/* Ant-Inspired Design - Professional Minimalist */}

        {/* Main Ant Body - Elliptical Shape */}
        <motion.ellipse
          cx="25"
          cy="28"
          rx="12"
          ry="8"
          fill="url(#antBodyGradient)"
          filter="url(#glow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.2 } : undefined}
        />

        {/* Ant Head - Circle */}
        <motion.circle
          cx="25"
          cy="15"
          r="6"
          fill="url(#antHeadGradient)"
          filter="url(#glow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.4 } : undefined}
        />

        {/* Ant Thorax - Small Circle */}
        <motion.circle
          cx="25"
          cy="22"
          r="4"
          fill="url(#antThoraxGradient)"
          filter="url(#softGlow)"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.3 } : undefined}
        />

        {/* Ant Antennae */}
        <motion.path
          d="M22 12 Q18 8 16 6"
          stroke="#2563eb"
          strokeWidth="1.5"
          strokeLinecap="round"
          fill="none"
          initial={animated ? { pathLength: 0 } : undefined}
          animate={animated ? { pathLength: 1 } : undefined}
          transition={animated ? { duration: 0.8, delay: 0.6 } : undefined}
        />
        <motion.path
          d="M28 12 Q32 8 34 6"
          stroke="#2563eb"
          strokeWidth="1.5"
          strokeLinecap="round"
          fill="none"
          initial={animated ? { pathLength: 0 } : undefined}
          animate={animated ? { pathLength: 1 } : undefined}
          transition={animated ? { duration: 0.8, delay: 0.7 } : undefined}
        />

        {/* Antennae Tips */}
        <motion.circle cx="16" cy="6" r="1.5" fill="#7c3aed"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.4, delay: 0.8 } : undefined}
        />
        <motion.circle cx="34" cy="6" r="1.5" fill="#7c3aed"
          initial={animated ? { scale: 0 } : undefined}
          animate={animated ? { scale: 1 } : undefined}
          transition={animated ? { duration: 0.4, delay: 0.9 } : undefined}
        />

        {/* Neural Network Connections - Minimalist */}
        <motion.g
          opacity="0.6"
          animate={shouldAnimate ? {
            opacity: [0.6, 1, 0.6],
          } : {}}
          transition={{ duration: 2, ease: "easeInOut" }}
        >
          {/* Connection Nodes */}
          <motion.circle cx="15" cy="25" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, ease: "easeInOut" }}
          />
          <motion.circle cx="35" cy="25" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, delay: 0.3, ease: "easeInOut" }}
          />
          <motion.circle cx="25" cy="38" r="1.2" fill="#059669" filter="url(#nodeGlow)"
            animate={shouldAnimate ? { scale: [1, 1.4, 1] } : {}}
            transition={{ duration: 2, delay: 0.6, ease: "easeInOut" }}
          />

          {/* Connection Lines */}
          <motion.path
            d="M15 25 L35 25"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1 } : undefined}
          />
          <motion.path
            d="M15 25 L25 38"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1.2 } : undefined}
          />
          <motion.path
            d="M35 25 L25 38"
            stroke="rgba(5, 150, 105, 0.4)"
            strokeWidth="0.8"
            strokeDasharray="1,1"
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { pathLength: 1 } : undefined}
            transition={animated ? { duration: 1.2, delay: 1.4 } : undefined}
          />
        </motion.g>

        {/* Ant-Inspired Gradients & Effects */}
        <defs>
          {/* Ant Body Gradient - Deep Neural Blue */}
          <linearGradient id="antBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#2563eb" />
            <stop offset="50%" stopColor="#1d4ed8" />
            <stop offset="100%" stopColor="#1e40af" />
          </linearGradient>

          {/* Ant Head Gradient - Electric Purple */}
          <linearGradient id="antHeadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#7c3aed" />
            <stop offset="50%" stopColor="#6d28d9" />
            <stop offset="100%" stopColor="#5b21b6" />
          </linearGradient>

          {/* Ant Thorax Gradient - Blend */}
          <linearGradient id="antThoraxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#8b5cf6" />
          </linearGradient>

          {/* Professional Glow Effects */}
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="nodeGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.8" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
      </motion.svg>
      
      {/* NeuroColony Text */}
      <div className="flex flex-col">
        <motion.span
          className="text-2xl font-bold text-white leading-none tracking-tight"
          initial={animated ? { y: 10, opacity: 0 } : undefined}
          animate={animated ? { y: 0, opacity: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.4 } : undefined}
        >
          Neuro<span className="bg-gradient-to-r from-purple-400 to-emerald-400 bg-clip-text text-transparent">Colony</span>
        </motion.span>
        <motion.span
          className="text-sm text-slate-300 leading-none font-light tracking-wide"
          initial={animated ? { y: 10, opacity: 0 } : undefined}
          animate={animated ? { y: 0, opacity: 1 } : undefined}
          transition={animated ? { duration: 0.6, delay: 0.6 } : undefined}
        >
          AI Email Sequence Platform
        </motion.span>
      </div>
    </motion.div>
  )
}

export default Logo