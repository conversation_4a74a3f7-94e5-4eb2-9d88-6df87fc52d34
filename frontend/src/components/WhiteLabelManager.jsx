import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Palette, Globe, Download, Settings, Eye, Save, 
  Upload, Wand2, <PERSON>rkles, Crown, Building2, Zap
} from 'lucide-react';
import toast from 'react-hot-toast';

const WhiteLabelManager = ({ user }) => {
  const [branding, setBranding] = useState({
    primaryColor: '#6366F1',
    secondaryColor: '#8B5CF6',
    accentColor: '#06B6D4',
    logoUrl: '',
    faviconUrl: '',
    companyName: '',
    supportEmail: '',
    customDomain: '',
    customCss: '',
    footerText: 'Powered by NeuroColony',
    hidePoweredBy: false
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [activeTab, setActiveTab] = useState('branding');

  useEffect(() => {
    // Load current branding settings
    fetchBrandingSettings();
  }, []);

  const fetchBrandingSettings = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/white-label/branding`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setBranding(data.branding);
      }
    } catch (error) {
      console.error('Error fetching branding:', error);
    }
  };

  const saveBrandingSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/white-label/branding`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ branding })
      });

      if (response.ok) {
        toast.success('Branding settings saved successfully!');
      } else {
        toast.error('Failed to save branding settings');
      }
    } catch (error) {
      toast.error('Error saving branding settings');
    } finally {
      setIsLoading(false);
    }
  };

  const setupCustomDomain = async () => {
    if (!branding.customDomain) {
      toast.error('Please enter a custom domain first');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/white-label/domain`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ domain: branding.customDomain })
      });

      if (response.ok) {
        toast.success('Custom domain setup initiated! Check your DNS settings.');
      } else {
        toast.error('Failed to setup custom domain');
      }
    } catch (error) {
      toast.error('Error setting up custom domain');
    } finally {
      setIsLoading(false);
    }
  };

  const exportWhiteLabel = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/white-label/export`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${branding.companyName || 'white-label'}-app.zip`;
        a.click();
        toast.success('White-label app exported successfully!');
      } else {
        toast.error('Failed to export white-label app');
      }
    } catch (error) {
      toast.error('Error exporting white-label app');
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    { id: 'branding', label: 'Branding', icon: Palette },
    { id: 'domain', label: 'Domain', icon: Globe },
    { id: 'export', label: 'Export', icon: Download }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-xl p-8 text-white mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Crown className="h-8 w-8 text-yellow-400" />
              <h1 className="text-3xl font-bold">White-Label Platform</h1>
            </div>
            <p className="text-blue-200 text-lg">
              Transform NeuroColony into your own branded platform with complete customization
            </p>
          </div>
          <div className="text-right">
            <div className="bg-white/10 rounded-lg p-4">
              <Building2 className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
              <div className="text-sm text-blue-200">Enterprise Feature</div>
              <div className="font-bold">Full White-Label</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${
              activeTab === tab.id
                ? 'border-b-2 border-purple-600 text-purple-600'
                : 'text-gray-600 hover:text-purple-600'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Branding Tab */}
      {activeTab === 'branding' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="grid lg:grid-cols-2 gap-8"
        >
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                <Palette className="h-6 w-6 text-purple-600" />
                Brand Colors
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Primary</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={branding.primaryColor}
                      onChange={(e) => setBranding({...branding, primaryColor: e.target.value})}
                      className="w-12 h-12 rounded-lg border-2 border-gray-300"
                    />
                    <input
                      type="text"
                      value={branding.primaryColor}
                      onChange={(e) => setBranding({...branding, primaryColor: e.target.value})}
                      className="flex-1 p-2 border rounded-lg"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Secondary</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={branding.secondaryColor}
                      onChange={(e) => setBranding({...branding, secondaryColor: e.target.value})}
                      className="w-12 h-12 rounded-lg border-2 border-gray-300"
                    />
                    <input
                      type="text"
                      value={branding.secondaryColor}
                      onChange={(e) => setBranding({...branding, secondaryColor: e.target.value})}
                      className="flex-1 p-2 border rounded-lg"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Accent</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={branding.accentColor}
                      onChange={(e) => setBranding({...branding, accentColor: e.target.value})}
                      className="w-12 h-12 rounded-lg border-2 border-gray-300"
                    />
                    <input
                      type="text"
                      value={branding.accentColor}
                      onChange={(e) => setBranding({...branding, accentColor: e.target.value})}
                      className="flex-1 p-2 border rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-bold mb-4">Company Details</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Company Name</label>
                  <input
                    type="text"
                    value={branding.companyName}
                    onChange={(e) => setBranding({...branding, companyName: e.target.value})}
                    className="w-full p-3 border rounded-lg"
                    placeholder="Your Company Name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Support Email</label>
                  <input
                    type="email"
                    value={branding.supportEmail}
                    onChange={(e) => setBranding({...branding, supportEmail: e.target.value})}
                    className="w-full p-3 border rounded-lg"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Logo URL</label>
                  <input
                    type="url"
                    value={branding.logoUrl}
                    onChange={(e) => setBranding({...branding, logoUrl: e.target.value})}
                    className="w-full p-3 border rounded-lg"
                    placeholder="https://yourcompany.com/logo.png"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Footer Text</label>
                  <input
                    type="text"
                    value={branding.footerText}
                    onChange={(e) => setBranding({...branding, footerText: e.target.value})}
                    className="w-full p-3 border rounded-lg"
                    placeholder="© 2025 Your Company"
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={branding.hidePoweredBy}
                    onChange={(e) => setBranding({...branding, hidePoweredBy: e.target.checked})}
                    className="w-4 h-4"
                  />
                  <label className="text-sm">Hide "Powered by NeuroColony"</label>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                <Eye className="h-6 w-6 text-purple-600" />
                Live Preview
              </h3>
              
              <div 
                className="border-2 border-gray-200 rounded-lg p-4 min-h-[300px]"
                style={{
                  backgroundColor: branding.primaryColor + '10',
                  borderColor: branding.primaryColor
                }}
              >
                <div 
                  className="flex items-center gap-3 p-3 rounded-lg mb-4"
                  style={{ backgroundColor: branding.primaryColor, color: 'white' }}
                >
                  {branding.logoUrl ? (
                    <img src={branding.logoUrl} alt="Logo" className="h-8 w-auto" />
                  ) : (
                    <div className="w-8 h-8 bg-white/20 rounded"></div>
                  )}
                  <span className="font-bold">
                    {branding.companyName || 'Your Company'} AI
                  </span>
                </div>
                
                <div className="space-y-3">
                  <div 
                    className="p-3 rounded-lg"
                    style={{ backgroundColor: branding.secondaryColor + '20' }}
                  >
                    <div className="font-medium">Dashboard</div>
                    <div className="text-sm text-gray-600">Your branded dashboard</div>
                  </div>
                  
                  <div 
                    className="p-3 rounded-lg"
                    style={{ backgroundColor: branding.accentColor + '20' }}
                  >
                    <div className="font-medium">Email Generator</div>
                    <div className="text-sm text-gray-600">AI-powered email sequences</div>
                  </div>
                </div>
                
                <div className="mt-4 p-2 text-xs text-center text-gray-500 border-t">
                  {branding.hidePoweredBy ? branding.footerText : `${branding.footerText} | Powered by NeuroColony`}
                </div>
              </div>
            </div>

            <button
              onClick={saveBrandingSettings}
              disabled={isLoading}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-5 w-5" />
                  Save Branding
                </>
              )}
            </button>
          </div>
        </motion.div>
      )}

      {/* Domain Tab */}
      {activeTab === 'domain' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Globe className="h-8 w-8 text-blue-600" />
              Custom Domain Setup
            </h3>
            
            <div className="space-y-6">
              <div>
                <label className="block text-lg font-medium mb-3">Your Custom Domain</label>
                <input
                  type="text"
                  value={branding.customDomain}
                  onChange={(e) => setBranding({...branding, customDomain: e.target.value})}
                  className="w-full p-4 border-2 border-gray-300 rounded-lg text-lg"
                  placeholder="app.yourcompany.com"
                />
                <p className="text-gray-600 mt-2">Enter the domain where you want to host your white-labeled app</p>
              </div>
              
              <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded">
                <h4 className="font-bold text-blue-900 mb-2">DNS Configuration Required</h4>
                <p className="text-blue-800 mb-3">After setup, you'll need to configure these DNS records:</p>
                <div className="bg-white p-3 rounded border font-mono text-sm">
                  <div>Type: CNAME</div>
                  <div>Name: {branding.customDomain || 'app.yourcompany.com'}</div>
                  <div>Value: sequenceai-white-label.herokuapp.com</div>
                </div>
              </div>
              
              <button
                onClick={setupCustomDomain}
                disabled={isLoading || !branding.customDomain}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Setting up...
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5" />
                    Setup Custom Domain
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Export Tab */}
      {activeTab === 'export' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Download className="h-8 w-8 text-green-600" />
              Export White-Label App
            </h3>
            
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 p-6 rounded-lg">
                <h4 className="font-bold text-green-900 mb-3">Ready to Deploy!</h4>
                <p className="text-green-800 mb-4">
                  Export your fully customized white-label application with all your branding applied. 
                  The exported package includes:
                </p>
                <ul className="text-green-800 space-y-1 mb-4">
                  <li>• Complete React application with your branding</li>
                  <li>• Backend API configured for your domain</li>
                  <li>• Docker deployment configuration</li>
                  <li>• SSL certificates and security setup</li>
                  <li>• Database migration scripts</li>
                </ul>
              </div>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h5 className="font-bold mb-3">What's Included</h5>
                  <ul className="text-sm space-y-1 text-gray-700">
                    <li>✓ Frontend with your branding</li>
                    <li>✓ Backend API server</li>
                    <li>✓ Database setup</li>
                    <li>✓ Docker configuration</li>
                    <li>✓ Deployment guide</li>
                  </ul>
                </div>
                
                <div className="bg-yellow-50 p-6 rounded-lg">
                  <h5 className="font-bold mb-3">System Requirements</h5>
                  <ul className="text-sm space-y-1 text-gray-700">
                    <li>• Node.js 18+ and npm</li>
                    <li>• MongoDB 5.0+</li>
                    <li>• Docker (recommended)</li>
                    <li>• SSL certificate for domain</li>
                    <li>• 2GB+ RAM, 10GB storage</li>
                  </ul>
                </div>
              </div>
              
              <button
                onClick={exportWhiteLabel}
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg transition-all flex items-center justify-center gap-3 text-lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-6 w-6" />
                    Export Complete White-Label App
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default WhiteLabelManager;