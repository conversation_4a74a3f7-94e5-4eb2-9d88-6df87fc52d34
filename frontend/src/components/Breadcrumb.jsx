import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

const Breadcrumb = ({ customPath = null }) => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  // Custom breadcrumb mapping
  const breadcrumbNames = {
    'generator': 'Email Generator',
    'templates': 'Templates',
    'dashboard': 'Dashboard',
    'pricing': 'Pricing',
    'about': 'About Us',
    'contact': 'Contact',
    'faq': 'FAQ',
    'terms': 'Terms of Service',
    'privacy': 'Privacy Policy',
    'login': 'Login',
    'register': 'Sign Up',
    'advanced': 'Advanced Features'
  };

  // Use custom path if provided, otherwise use current location
  const paths = customPath ? customPath : pathnames;

  // Don't show breadcrumbs on homepage
  if (paths.length === 0) {
    return null;
  }

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-2 py-3 text-sm">
          {/* Home link */}
          <Link 
            to="/" 
            className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">Home</span>
          </Link>

          {paths.map((path, index) => {
            const isLast = index === paths.length - 1;
            const routeTo = `/${paths.slice(0, index + 1).join('/')}`;
            const displayName = breadcrumbNames[path] || path.charAt(0).toUpperCase() + path.slice(1);

            return (
              <React.Fragment key={path}>
                <ChevronRight className="h-4 w-4 text-gray-400" />
                {isLast ? (
                  <span className="font-medium text-gray-900">
                    {displayName}
                  </span>
                ) : (
                  <Link
                    to={routeTo}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {displayName}
                  </Link>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default Breadcrumb;