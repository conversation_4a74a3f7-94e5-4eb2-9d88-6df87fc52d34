import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../utils/cn';
import { shouldReduceMotion } from '../utils/animations';

const statusConfig = {
  idle: {
    color: 'text-gray-400',
    bg: 'bg-gray-400/20',
    borderColor: 'border-gray-400/30',
    pulseColor: 'bg-gray-400',
    label: 'Idle'
  },
  active: {
    color: 'text-green-400',
    bg: 'bg-green-400/20',
    borderColor: 'border-green-400/30',
    pulseColor: 'bg-green-400',
    label: 'Active'
  },
  processing: {
    color: 'text-blue-400',
    bg: 'bg-blue-400/20',
    borderColor: 'border-blue-400/30',
    pulseColor: 'bg-blue-400',
    label: 'Processing'
  },
  error: {
    color: 'text-red-400',
    bg: 'bg-red-400/20',
    borderColor: 'border-red-400/30',
    pulseColor: 'bg-red-400',
    label: 'Error'
  },
  success: {
    color: 'text-emerald-400',
    bg: 'bg-emerald-400/20',
    borderColor: 'border-emerald-400/30',
    pulseColor: 'bg-emerald-400',
    label: 'Success'
  }
};

const AgentStatusIndicator = ({ 
  status = 'idle', 
  size = 'md', 
  showLabel = true,
  showPulse = true,
  className 
}) => {
  const config = statusConfig[status] || statusConfig.idle;
  const reduceMotion = shouldReduceMotion();
  
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="relative">
        {/* Pulse animation */}
        {showPulse && status === 'active' && !reduceMotion && (
          <motion.div
            className={cn(
              "absolute inset-0 rounded-full",
              config.pulseColor,
              sizeClasses[size]
            )}
            animate={{
              scale: [1, 2, 2],
              opacity: [0.7, 0, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeOut"
            }}
          />
        )}
        
        {/* Status dot */}
        <motion.div
          className={cn(
            "relative rounded-full",
            config.bg,
            "border",
            config.borderColor,
            sizeClasses[size]
          )}
          animate={status === 'processing' && !reduceMotion ? {
            rotate: 360
          } : {}}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          {status === 'processing' && (
            <div className="absolute inset-0 rounded-full">
              <div className="absolute inset-[25%] bg-current rounded-full opacity-60" />
            </div>
          )}
        </motion.div>
      </div>
      
      {showLabel && (
        <AnimatePresence mode="wait">
          <motion.span
            key={status}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "text-sm font-medium",
              config.color
            )}
          >
            {config.label}
          </motion.span>
        </AnimatePresence>
      )}
    </div>
  );
};

// Agent communication animation component
export const AgentCommunicationLine = ({ 
  from, 
  to, 
  active = false,
  color = 'purple' 
}) => {
  const reduceMotion = shouldReduceMotion();
  
  if (!from || !to) return null;

  const colorMap = {
    purple: 'url(#neural-gradient-purple)',
    blue: 'url(#neural-gradient-blue)',
    green: 'url(#neural-gradient-green)'
  };

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: -1 }}
    >
      <defs>
        <linearGradient id="neural-gradient-purple" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.3" />
        </linearGradient>
        <linearGradient id="neural-gradient-blue" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3" />
        </linearGradient>
        <linearGradient id="neural-gradient-green" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#10b981" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#84cc16" stopOpacity="0.3" />
        </linearGradient>
      </defs>
      
      <motion.line
        x1={from.x}
        y1={from.y}
        x2={to.x}
        y2={to.y}
        stroke={colorMap[color]}
        strokeWidth="2"
        strokeDasharray="5 5"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={active ? {
          pathLength: [0, 1],
          opacity: [0, 0.6, 0]
        } : {
          opacity: 0
        }}
        transition={{
          pathLength: { duration: 1.5, ease: "easeInOut" },
          opacity: { duration: 1.5, ease: "easeInOut" },
          repeat: active && !reduceMotion ? Infinity : 0,
          repeatDelay: 0.5
        }}
      />
      
      {active && !reduceMotion && (
        <motion.circle
          r="4"
          fill={colorMap[color]}
          initial={{ x: from.x, y: from.y }}
          animate={{
            x: to.x,
            y: to.y
          }}
          transition={{
            duration: 1.5,
            ease: "easeInOut",
            repeat: Infinity,
            repeatDelay: 0.5
          }}
        />
      )}
    </svg>
  );
};

// Progress ring animation
export const AgentProgressRing = ({ 
  progress = 0, 
  size = 60, 
  strokeWidth = 4,
  className 
}) => {
  const reduceMotion = shouldReduceMotion();
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (progress / 100) * circumference;

  return (
    <div className={cn("relative", className)}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-700"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="url(#progress-gradient)"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={circumference}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset: offset }}
          transition={{
            duration: reduceMotion ? 0 : 0.5,
            ease: "easeOut"
          }}
          strokeLinecap="round"
        />
        
        <defs>
          <linearGradient id="progress-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>
      </svg>
      
      {/* Center text */}
      <div className="absolute inset-0 flex items-center justify-center">
        <motion.span
          className="text-sm font-semibold text-purple-400"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {Math.round(progress)}%
        </motion.span>
      </div>
    </div>
  );
};

export default AgentStatusIndicator;