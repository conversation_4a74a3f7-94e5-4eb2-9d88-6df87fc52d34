import React from 'react';
import { motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import { shouldReduceMotion } from '../utils/animations';

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0,
    y: -20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};

const PageTransition = ({ children }) => {
  const location = useLocation();
  const reduceMotion = shouldReduceMotion();

  if (reduceMotion) {
    return <>{children}</>;
  }

  return (
    <motion.div
      key={location.pathname}
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
      style={{ width: '100%' }}
    >
      {children}
    </motion.div>
  );
};

// Enhanced page transition with custom animations per route
export const RouteTransition = ({ children, variant = 'default' }) => {
  const location = useLocation();
  const reduceMotion = shouldReduceMotion();

  const variants = {
    default: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
    },
    fade: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
    },
    scale: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.9 },
    },
    slideUp: {
      initial: { opacity: 0, y: 50 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -50 },
    },
  };

  if (reduceMotion) {
    return <>{children}</>;
  }

  return (
    <motion.div
      key={location.pathname}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants[variant]}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      style={{ width: '100%', height: '100%' }}
    >
      {children}
    </motion.div>
  );
};

// Loading transition for async components
export const LoadingTransition = ({ isLoading, children, loader }) => {
  return (
    <motion.div
      initial={false}
      animate={isLoading ? { opacity: 0.5 } : { opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      {isLoading && loader}
      {!isLoading && children}
    </motion.div>
  );
};

export default PageTransition;