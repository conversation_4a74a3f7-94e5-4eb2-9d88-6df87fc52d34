import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, Zap, Brain, Target, Mail, BarChart3, 
  Sparkles, Globe, MessageSquare, Mic, Languages, 
  Trophy, Rocket, ArrowUpRight, Palette, Star,
  Users, DollarSign, Activity, Clock, ChevronRight,
  Play, Pause, Settings, Filter, Download, Crown, Building2
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import ThemeToggle from './ThemeToggle';
import '../../styles/premium-theme.css';

const UltraDashboard = ({ user }) => {
  const { theme, isDark } = useTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [isPlaying, setIsPlaying] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  
  // Realistic metrics with growth animations
  const [metrics, setMetrics] = useState({
    sequences: { value: 2847, change: 23, trend: 'up' },
    engagement: { value: 67.3, change: 5.2, trend: 'up' },
    revenue: { value: 45200, change: 12.8, trend: 'up' },
    aiScore: { value: 94, change: 8, trend: 'up' },
    activeUsers: { value: 1.2, change: 15.3, trend: 'up' },
    conversionRate: { value: 4.8, change: 0.8, trend: 'up' }
  });

  const [recentActivity] = useState([
    { type: 'sequence', user: 'Sarah Chen', action: 'Generated welcome sequence', time: '2 min ago', status: 'success' },
    { type: 'translation', user: 'Marcus Rodriguez', action: 'Translated to Spanish', time: '5 min ago', status: 'success' },
    { type: 'optimization', user: 'Emma Thompson', action: 'Optimized subject line', time: '8 min ago', status: 'success' },
    { type: 'analysis', user: 'David Kim', action: 'Analyzed competitor email', time: '12 min ago', status: 'success' },
    { type: 'prediction', user: 'Lisa Wang', action: 'Predicted performance', time: '15 min ago', status: 'success' }
  ]);

  const [aiInsights] = useState([
    { icon: '', text: 'Tuesday 10 AM shows 34% higher engagement for your audience', priority: 'high' },
    { icon: '', text: 'Adding urgency in email 3 increases conversions by 23%', priority: 'medium' },
    { icon: '', text: 'Your storytelling approach resonates best with tech audiences', priority: 'medium' },
    { icon: '', text: 'Subject lines with numbers perform 41% better', priority: 'low' }
  ]);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'insights', label: 'AI Insights', icon: Brain },
    { id: 'activity', label: 'Activity', icon: Activity }
  ];

  const MetricCard = ({ title, value, change, trend, icon: Icon, format = 'number', color = 'primary' }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card-premium relative overflow-hidden group"
    >
      {/* Animated background gradient */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500">
        <div className={`w-full h-full bg-gradient-to-br ${
          color === 'primary' ? 'from-blue-500 to-purple-600' :
          color === 'success' ? 'from-green-500 to-emerald-600' :
          color === 'warning' ? 'from-yellow-500 to-orange-600' :
          'from-purple-500 to-pink-600'
        }`} />
      </div>

      <div className="relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className={`p-3 rounded-xl bg-gradient-to-br ${
            color === 'primary' ? 'from-blue-500/20 to-purple-600/20' :
            color === 'success' ? 'from-green-500/20 to-emerald-600/20' :
            color === 'warning' ? 'from-yellow-500/20 to-orange-600/20' :
            'from-purple-500/20 to-pink-600/20'
          }`}>
            <Icon size={24} className={`${
              color === 'primary' ? 'text-blue-500' :
              color === 'success' ? 'text-green-500' :
              color === 'warning' ? 'text-yellow-500' :
              'text-purple-500'
            }`} />
          </div>
          
          <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend === 'up' ? 'bg-success-bg text-success' : 'bg-error-bg text-error'
          }`}>
            <TrendingUp size={12} className={trend === 'down' ? 'rotate-180' : ''} />
            {change > 0 ? '+' : ''}{change}%
          </div>
        </div>

        <div className="space-y-1">
          <h3 className="text-3xl font-bold text-primary">
            {format === 'currency' ? '$' : ''}{
              format === 'percentage' ? `${value}%` :
              format === 'currency' ? `${(value / 1000).toFixed(1)}K` :
              format === 'decimal' ? `${value}K` :
              value.toLocaleString()
            }
          </h3>
          <p className="text-sm text-secondary">{title}</p>
        </div>

        {/* Animated pulse effect */}
        <motion.div
          className="absolute -top-2 -right-2 w-4 h-4 rounded-full bg-blue-500 opacity-75"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.7, 0.3, 0.7]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
    </motion.div>
  );

  const ActivityItem = ({ item, index }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1 }}
      className="flex items-center gap-4 p-4 rounded-xl hover:bg-surface-hover transition-colors group"
    >
      <div className={`p-2 rounded-lg ${
        item.type === 'sequence' ? 'bg-blue-500/20 text-blue-500' :
        item.type === 'translation' ? 'bg-green-500/20 text-green-500' :
        item.type === 'optimization' ? 'bg-purple-500/20 text-purple-500' :
        item.type === 'analysis' ? 'bg-orange-500/20 text-orange-500' :
        'bg-cyan-500/20 text-cyan-500'
      }`}>
        {item.type === 'sequence' && <Mail size={16} />}
        {item.type === 'translation' && <Languages size={16} />}
        {item.type === 'optimization' && <Sparkles size={16} />}
        {item.type === 'analysis' && <Trophy size={16} />}
        {item.type === 'prediction' && <BarChart3 size={16} />}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-primary truncate">{item.user}</p>
        <p className="text-xs text-secondary">{item.action}</p>
      </div>
      
      <div className="text-xs text-tertiary">{item.time}</div>
      
      <ChevronRight 
        size={16} 
        className="text-tertiary opacity-0 group-hover:opacity-100 transition-opacity" 
      />
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-primary text-primary transition-colors duration-300">
      {/* Ultra-premium animated background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      {/* Theme Toggle */}
      <div className="fixed top-6 right-6 z-50">
        <ThemeToggle />
      </div>

      <div className="relative z-10 container py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-6xl font-bold text-holographic mb-2">
                NeuroColony
              </h1>
              <p className="text-lg text-secondary">
                Ultra-Premium AI Email Intelligence Platform
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn btn-ghost"
              >
                <Settings size={20} />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn btn-ghost"
              >
                <Download size={20} />
                Export
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsPlaying(!isPlaying)}
                className="btn btn-primary"
              >
                {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                {isPlaying ? 'Pause' : 'Live Mode'}
              </motion.button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="glass p-2 rounded-2xl inline-flex">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-sm transition-all
                  ${activeTab === tab.id 
                    ? 'bg-gradient-primary text-white shadow-glow' 
                    : 'text-secondary hover:text-primary hover:bg-surface-hover'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <tab.icon size={18} />
                {tab.label}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Main Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Key Metrics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <MetricCard
                  title="Email Sequences Generated"
                  value={metrics.sequences.value}
                  change={metrics.sequences.change}
                  trend={metrics.sequences.trend}
                  icon={Mail}
                  color="primary"
                />
                <MetricCard
                  title="Average Engagement Rate"
                  value={metrics.engagement.value}
                  change={metrics.engagement.change}
                  trend={metrics.engagement.trend}
                  icon={Target}
                  format="percentage"
                  color="success"
                />
                <MetricCard
                  title="Revenue Generated"
                  value={metrics.revenue.value}
                  change={metrics.revenue.change}
                  trend={metrics.revenue.trend}
                  icon={DollarSign}
                  format="currency"
                  color="warning"
                />
                <MetricCard
                  title="AI Quality Score"
                  value={metrics.aiScore.value}
                  change={metrics.aiScore.change}
                  trend={metrics.aiScore.trend}
                  icon={Brain}
                  color="purple"
                />
                <MetricCard
                  title="Active Users"
                  value={metrics.activeUsers.value}
                  change={metrics.activeUsers.change}
                  trend={metrics.activeUsers.trend}
                  icon={Users}
                  format="decimal"
                  color="success"
                />
                <MetricCard
                  title="Conversion Rate"
                  value={metrics.conversionRate.value}
                  change={metrics.conversionRate.change}
                  trend={metrics.conversionRate.trend}
                  icon={TrendingUp}
                  format="percentage"
                  color="primary"
                />
              </div>

              {/* Enterprise Features Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <h3 className="text-2xl font-bold text-primary mb-6 flex items-center gap-3">
                  <Crown className="text-yellow-500" />
                  Enterprise Features
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/white-label" className="group">
                    <motion.div
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="card-premium group-hover:border-purple-500/50 transition-all duration-300"
                    >
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-600/20">
                          <Palette className="h-6 w-6 text-purple-500" />
                        </div>
                        <div>
                          <h4 className="font-bold text-primary">White-Label Platform</h4>
                          <p className="text-sm text-secondary">Custom branding & domains</p>
                        </div>
                      </div>
                      <p className="text-xs text-tertiary mb-3">
                        Transform NeuroColony into your own branded platform with complete customization
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                          Enterprise
                        </span>
                        <ArrowUpRight className="h-4 w-4 text-purple-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </motion.div>
                  </Link>

                  <Link to="/revenue-optimizer" className="group">
                    <motion.div
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="card-premium group-hover:border-green-500/50 transition-all duration-300"
                    >
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-600/20">
                          <TrendingUp className="h-6 w-6 text-green-500" />
                        </div>
                        <div>
                          <h4 className="font-bold text-primary">Revenue Optimizer</h4>
                          <p className="text-sm text-secondary">AI-powered growth insights</p>
                        </div>
                      </div>
                      <p className="text-xs text-tertiary mb-3">
                        Advanced AI analyzes your metrics to suggest revenue optimization strategies
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                          AI-Powered
                        </span>
                        <ArrowUpRight className="h-4 w-4 text-green-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </motion.div>
                  </Link>

                  <Link to="/enterprise-analytics" className="group">
                    <motion.div
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="card-premium group-hover:border-blue-500/50 transition-all duration-300"
                    >
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-600/20">
                          <Building2 className="h-6 w-6 text-blue-500" />
                        </div>
                        <div>
                          <h4 className="font-bold text-primary">Enterprise Analytics</h4>
                          <p className="text-sm text-secondary">Advanced business intelligence</p>
                        </div>
                      </div>
                      <p className="text-xs text-tertiary mb-3">
                        Comprehensive analytics dashboard with custom reporting and team insights
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                          Business Intel
                        </span>
                        <ArrowUpRight className="h-4 w-4 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </motion.div>
                  </Link>
                </div>
              </motion.div>

              {/* AI Insights Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="card-premium"
                >
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-primary flex items-center gap-3">
                      <Sparkles className="text-yellow-500" />
                      AI Insights
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      className="btn btn-ghost btn-sm"
                    >
                      View All
                    </motion.button>
                  </div>
                  
                  <div className="space-y-4">
                    {aiInsights.map((insight, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`
                          p-4 rounded-xl border-l-4 transition-all hover:bg-surface-hover
                          ${insight.priority === 'high' ? 'border-red-500 bg-red-500/5' :
                            insight.priority === 'medium' ? 'border-yellow-500 bg-yellow-500/5' :
                            'border-green-500 bg-green-500/5'
                          }
                        `}
                      >
                        <div className="flex items-start gap-3">
                          <span className="text-2xl">{insight.icon}</span>
                          <p className="text-sm text-secondary flex-1">{insight.text}</p>
                          <span className={`
                            px-2 py-1 rounded-full text-xs font-medium
                            ${insight.priority === 'high' ? 'bg-red-100 text-red-700' :
                              insight.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }
                          `}>
                            {insight.priority}
                          </span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="card-premium"
                >
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-primary flex items-center gap-3">
                      <Activity className="text-green-500" />
                      Recent Activity
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      className="btn btn-ghost btn-sm"
                    >
                      View All
                    </motion.button>
                  </div>
                  
                  <div className="space-y-2">
                    {recentActivity.map((item, index) => (
                      <ActivityItem key={index} item={item} index={index} />
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}

          {activeTab === 'analytics' && (
            <motion.div
              key="analytics"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="card-premium text-center py-16"
            >
              <BarChart3 size={64} className="mx-auto mb-4 text-purple-500" />
              <h3 className="text-2xl font-bold text-primary mb-2">Advanced Analytics</h3>
              <p className="text-secondary">Detailed analytics dashboard coming soon</p>
            </motion.div>
          )}

          {activeTab === 'insights' && (
            <motion.div
              key="insights"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="card-premium text-center py-16"
            >
              <Brain size={64} className="mx-auto mb-4 text-purple-500" />
              <h3 className="text-2xl font-bold text-primary mb-2">AI Insights Engine</h3>
              <p className="text-secondary">Deep AI analysis and recommendations</p>
            </motion.div>
          )}

          {activeTab === 'activity' && (
            <motion.div
              key="activity"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="card-premium text-center py-16"
            >
              <Activity size={64} className="mx-auto mb-4 text-purple-500" />
              <h3 className="text-2xl font-bold text-primary mb-2">Activity Timeline</h3>
              <p className="text-secondary">Complete activity history and logs</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default UltraDashboard;