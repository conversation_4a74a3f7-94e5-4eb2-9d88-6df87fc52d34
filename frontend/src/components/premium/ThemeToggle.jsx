import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sun, Moon, Monitor } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

const ThemeToggle = ({ showLabel = false, variant = 'button' }) => {
  const { theme, toggleTheme, setTheme } = useTheme();

  if (variant === 'dropdown') {
    return (
      <div className="relative">
        <motion.div 
          className="glass p-2 rounded-xl"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center gap-1">
            <motion.button
              onClick={() => setTheme('light')}
              className={`p-2 rounded-lg transition-all ${
                theme === 'light' 
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg' 
                  : 'text-secondary hover:bg-surface-hover'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Sun size={16} />
            </motion.button>
            
            <motion.button
              onClick={() => setTheme('dark')}
              className={`p-2 rounded-lg transition-all ${
                theme === 'dark' 
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg' 
                  : 'text-secondary hover:bg-surface-hover'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Moon size={16} />
            </motion.button>
            
            <motion.button
              onClick={() => setTheme('system')}
              className={`p-2 rounded-lg transition-all ${
                theme === 'system' 
                  ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg' 
                  : 'text-secondary hover:bg-surface-hover'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Monitor size={16} />
            </motion.button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <motion.button
      onClick={toggleTheme}
      className={`
        relative overflow-hidden rounded-full p-3 transition-all duration-300
        glass hover:scale-105 active:scale-95
        ${showLabel ? 'flex items-center gap-3 px-4' : ''}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label="Toggle theme"
    >
      <div className="relative w-6 h-6">
        <AnimatePresence mode="wait">
          {theme === 'dark' ? (
            <motion.div
              key="moon"
              initial={{ rotate: -90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: 90, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <Moon size={20} className="text-purple-400" />
            </motion.div>
          ) : (
            <motion.div
              key="sun"
              initial={{ rotate: 90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: -90, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <Sun size={20} className="text-yellow-500" />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {showLabel && (
        <span className="text-sm font-medium text-primary">
          {theme === 'dark' ? 'Dark Mode' : 'Light Mode'}
        </span>
      )}

      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full opacity-0"
        animate={{
          opacity: theme === 'dark' ? [0, 0.3, 0] : [0, 0.2, 0],
          scale: [1, 1.2, 1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          background: theme === 'dark' 
            ? 'radial-gradient(circle, rgba(147, 51, 234, 0.4) 0%, transparent 70%)'
            : 'radial-gradient(circle, rgba(251, 191, 36, 0.4) 0%, transparent 70%)'
        }}
      />
    </motion.button>
  );
};

export default ThemeToggle;