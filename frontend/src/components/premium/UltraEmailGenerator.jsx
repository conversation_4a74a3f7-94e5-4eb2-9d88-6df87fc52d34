import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wand2, Globe, Brain, Trophy, Mic, Mic<PERSON>ff, Languages, 
  BarChart3, Sparkles, Copy, Check, ChevronRight, Loader2, 
  AlertCircle, Play, Pause, Zap, Target, MessageSquare,
  Upload, Download, Settings, Filter, Search, X, Plus,
  ArrowRight, Star, Rocket, Crown, Diamond
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import ThemeToggle from './ThemeToggle';
import '../../styles/premium-theme.css';

const UltraEmailGenerator = ({ user }) => {
  const { isDark } = useTheme();
  const [activeMode, setActiveMode] = useState('generate');
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [copiedItems, setCopiedItems] = useState({});
  const fileInputRef = useRef(null);
  
  // Enhanced form states
  const [emailContent, setEmailContent] = useState({
    subject: '',
    body: ''
  });
  
  const [businessInfo, setBusinessInfo] = useState({
    industry: '',
    productService: '',
    targetAudience: '',
    pricePoint: '',
    tone: 'professional',
    goal: 'conversion'
  });
  
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [competitorEmail, setCompetitorEmail] = useState({
    subject: '',
    body: ''
  });

  // Voice recording state
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioBlob, setAudioBlob] = useState(null);

  const modes = [
    {
      id: 'generate',
      icon: Wand2,
      title: 'AI Generation',
      description: 'Create complete email sequences with advanced AI',
      color: 'from-purple-500 to-blue-500',
      badge: 'Popular',
      badgeColor: 'bg-green-500'
    },
    {
      id: 'translate',
      icon: Languages,
      title: 'Multi-Language',
      description: 'Translate and adapt for global markets',
      color: 'from-blue-500 to-cyan-500',
      badge: 'New',
      badgeColor: 'bg-blue-500'
    },
    {
      id: 'predict',
      icon: BarChart3,
      title: 'Performance Prediction',
      description: 'AI-powered success forecasting',
      color: 'from-cyan-500 to-green-500',
      badge: 'AI',
      badgeColor: 'bg-purple-500'
    },
    {
      id: 'optimize',
      icon: Sparkles,
      title: 'Subject Optimizer',
      description: 'Psychological trigger optimization',
      color: 'from-green-500 to-yellow-500',
      badge: 'Pro',
      badgeColor: 'bg-yellow-500'
    },
    {
      id: 'analyze',
      icon: Trophy,
      title: 'Competitor Analysis',
      description: 'Strategic intelligence gathering',
      color: 'from-yellow-500 to-red-500',
      badge: 'Elite',
      badgeColor: 'bg-red-500'
    }
  ];

  const languages = [
    { code: 'es', name: 'Spanish', flag: '🇪🇸', users: '500M+' },
    { code: 'fr', name: 'French', flag: '🇫🇷', users: '280M+' },
    { code: 'de', name: 'German', flag: '🇩🇪', users: '100M+' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵', users: '125M+' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹', users: '260M+' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳', users: '1.1B+' },
    { code: 'it', name: 'Italian', flag: '🇮🇹', users: '65M+' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷', users: '77M+' }
  ];

  const tones = [
    { id: 'professional', label: 'Professional', emoji: '💼' },
    { id: 'friendly', label: 'Friendly', emoji: '😊' },
    { id: 'casual', label: 'Casual', emoji: '' },
    { id: 'urgent', label: 'Urgent', emoji: '' },
    { id: 'luxury', label: 'Luxury', emoji: '' },
    { id: 'playful', label: 'Playful', emoji: '🎉' }
  ];

  const goals = [
    { id: 'conversion', label: 'Drive Sales', emoji: '' },
    { id: 'engagement', label: 'Increase Engagement', emoji: '' },
    { id: 'nurture', label: 'Nurture Leads', emoji: '🌱' },
    { id: 'retention', label: 'Retain Customers', emoji: '' },
    { id: 'awareness', label: 'Build Awareness', emoji: '📢' },
    { id: 'education', label: 'Educate Users', emoji: '📚' }
  ];

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const chunks = [];

      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      setMediaRecorder(recorder);
      recorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Recording error:', error);
      setError('Microphone access denied');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const handleGenerate = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/sequences/generate', {
        businessInfo,
        settings: {
          sequenceLength: 7,
          tone: businessInfo.tone,
          primaryGoal: businessInfo.goal,
          includeCTA: true,
          includePersonalization: true
        }
      });

      setResults({
        type: 'sequence',
        data: response.data.sequence
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Generation failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async (text, itemId) => {
    await navigator.clipboard.writeText(text);
    setCopiedItems({ ...copiedItems, [itemId]: true });
    setTimeout(() => {
      setCopiedItems({ ...copiedItems, [itemId]: false });
    }, 2000);
  };

  const ModeCard = ({ mode, isActive, onClick }) => (
    <motion.div
      onClick={onClick}
      className={`
        relative overflow-hidden cursor-pointer group
        ${isActive ? 'card-premium' : 'card hover:scale-[1.02]'}
        transition-all duration-300
      `}
      whileHover={{ y: -4 }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Animated background */}
      <div className={`
        absolute inset-0 bg-gradient-to-br ${mode.color} opacity-0 
        ${isActive ? 'opacity-10' : 'group-hover:opacity-5'} 
        transition-opacity duration-300
      `} />
      
      {/* Badge */}
      {mode.badge && (
        <div className={`
          absolute top-4 right-4 px-2 py-1 rounded-full text-xs font-bold text-white
          ${mode.badgeColor}
        `}>
          {mode.badge}
        </div>
      )}

      <div className="relative z-10">
        <div className={`
          w-12 h-12 rounded-xl bg-gradient-to-br ${mode.color} 
          flex items-center justify-center mb-4 group-hover:scale-110 transition-transform
        `}>
          <mode.icon size={24} className="text-white" />
        </div>
        
        <h3 className="text-lg font-bold text-primary mb-2">{mode.title}</h3>
        <p className="text-sm text-secondary">{mode.description}</p>

        {/* Active indicator */}
        {isActive && (
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500"
            layoutId="activeMode"
          />
        )}
      </div>
    </motion.div>
  );

  const InputField = ({ label, value, onChange, placeholder, multiline = false, rows = 3 }) => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-primary">{label}</label>
      {multiline ? (
        <textarea
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          rows={rows}
          className="input resize-none"
        />
      ) : (
        <input
          type="text"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className="input"
        />
      )}
    </div>
  );

  const SelectField = ({ label, value, onChange, options }) => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-primary">{label}</label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {options.map((option) => (
          <motion.button
            key={option.id}
            onClick={() => onChange(option.id)}
            className={`
              p-3 rounded-xl border transition-all text-left
              ${value === option.id 
                ? 'border-purple-500 bg-purple-500/10 text-primary' 
                : 'border-border hover:border-purple-300 text-secondary hover:text-primary'
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">{option.emoji}</span>
              <span className="text-sm font-medium">{option.label}</span>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-primary text-primary transition-colors duration-300">
      {/* Ultra-premium animated background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      {/* Theme Toggle */}
      <div className="fixed top-6 right-6 z-50">
        <ThemeToggle />
      </div>

      <div className="relative z-10 container py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold text-holographic mb-4">
            Ultra Email Generator
          </h1>
          <p className="text-xl text-secondary max-w-2xl mx-auto">
            Harness the power of advanced AI to create world-class email sequences that convert
          </p>
        </motion.div>

        {/* Mode Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-primary mb-6 text-center">Choose Your AI Mode</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {modes.map((mode) => (
              <ModeCard
                key={mode.id}
                mode={mode}
                isActive={activeMode === mode.id}
                onClick={() => setActiveMode(mode.id)}
              />
            ))}
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="card-premium"
          >
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-blue-500">
                <Settings size={24} className="text-white" />
              </div>
              <h2 className="text-2xl font-bold text-primary">
                {modes.find(m => m.id === activeMode)?.title} Settings
              </h2>
            </div>

            <div className="space-y-6">
              {/* Universal Business Info */}
              {(activeMode === 'generate' || activeMode === 'optimize' || activeMode === 'analyze') && (
                <>
                  <InputField
                    label="Industry"
                    value={businessInfo.industry}
                    onChange={(e) => setBusinessInfo({...businessInfo, industry: e.target.value})}
                    placeholder="e.g., SaaS, E-commerce, Healthcare"
                  />
                  
                  <InputField
                    label="Product/Service"
                    value={businessInfo.productService}
                    onChange={(e) => setBusinessInfo({...businessInfo, productService: e.target.value})}
                    placeholder="e.g., Email Marketing Platform"
                  />
                  
                  <InputField
                    label="Target Audience"
                    value={businessInfo.targetAudience}
                    onChange={(e) => setBusinessInfo({...businessInfo, targetAudience: e.target.value})}
                    placeholder="e.g., Small Business Owners"
                  />
                  
                  <InputField
                    label="Price Point"
                    value={businessInfo.pricePoint}
                    onChange={(e) => setBusinessInfo({...businessInfo, pricePoint: e.target.value})}
                    placeholder="e.g., $29/month"
                  />
                </>
              )}

              {/* Generate Mode Specific */}
              {activeMode === 'generate' && (
                <>
                  <SelectField
                    label="Tone"
                    value={businessInfo.tone}
                    onChange={(value) => setBusinessInfo({...businessInfo, tone: value})}
                    options={tones}
                  />
                  
                  <SelectField
                    label="Primary Goal"
                    value={businessInfo.goal}
                    onChange={(value) => setBusinessInfo({...businessInfo, goal: value})}
                    options={goals}
                  />

                  {/* Voice Input */}
                  <div className="p-6 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-2xl border border-purple-500/20">
                    <div className="flex items-center gap-3 mb-4">
                      <Mic className="text-purple-500" size={20} />
                      <h3 className="font-semibold text-primary">Voice Input (Beta)</h3>
                    </div>
                    <p className="text-sm text-secondary mb-4">
                      Speak your ideas and let AI transform them into perfect email sequences
                    </p>
                    <div className="flex items-center gap-4">
                      <motion.button
                        onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                        className={`
                          btn flex-1 ${isRecording ? 'bg-red-500 hover:bg-red-600' : 'btn-primary'}
                        `}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {isRecording ? (
                          <>
                            <MicOff size={20} />
                            Stop Recording
                          </>
                        ) : (
                          <>
                            <Mic size={20} />
                            Start Recording
                          </>
                        )}
                      </motion.button>
                      {audioBlob && (
                        <motion.button
                          onClick={() => console.log('Process voice')}
                          className="btn btn-secondary"
                          whileHover={{ scale: 1.02 }}
                        >
                          Process
                        </motion.button>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Email Content for other modes */}
              {(activeMode === 'translate' || activeMode === 'predict' || activeMode === 'optimize') && (
                <>
                  <InputField
                    label="Email Subject"
                    value={emailContent.subject}
                    onChange={(e) => setEmailContent({...emailContent, subject: e.target.value})}
                    placeholder="Enter your email subject line"
                  />
                  
                  {(activeMode === 'translate' || activeMode === 'predict') && (
                    <InputField
                      label="Email Body"
                      value={emailContent.body}
                      onChange={(e) => setEmailContent({...emailContent, body: e.target.value})}
                      placeholder="Enter your email content"
                      multiline
                      rows={8}
                    />
                  )}
                </>
              )}

              {/* Language Selection for Translate */}
              {activeMode === 'translate' && (
                <div className="space-y-3">
                  <label className="text-sm font-medium text-primary">Target Languages</label>
                  <div className="grid grid-cols-2 gap-3">
                    {languages.map((lang) => (
                      <motion.label
                        key={lang.code}
                        className={`
                          flex items-center gap-3 p-4 rounded-xl cursor-pointer transition-all
                          ${selectedLanguages.includes(lang.code)
                            ? 'bg-purple-500/20 border border-purple-500 text-primary'
                            : 'bg-surface-hover border border-border-subtle hover:border-purple-300'
                          }
                        `}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <input
                          type="checkbox"
                          checked={selectedLanguages.includes(lang.code)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedLanguages([...selectedLanguages, lang.code]);
                            } else {
                              setSelectedLanguages(selectedLanguages.filter(l => l !== lang.code));
                            }
                          }}
                          className="sr-only"
                        />
                        <span className="text-2xl">{lang.flag}</span>
                        <div className="flex-1">
                          <div className="font-medium">{lang.name}</div>
                          <div className="text-xs text-secondary">{lang.users} speakers</div>
                        </div>
                        {selectedLanguages.includes(lang.code) && (
                          <Check size={20} className="text-purple-500" />
                        )}
                      </motion.label>
                    ))}
                  </div>
                </div>
              )}

              {/* Competitor Analysis */}
              {activeMode === 'analyze' && (
                <>
                  <InputField
                    label="Competitor Email Subject"
                    value={competitorEmail.subject}
                    onChange={(e) => setCompetitorEmail({...competitorEmail, subject: e.target.value})}
                    placeholder="Enter competitor's subject line"
                  />
                  
                  <InputField
                    label="Competitor Email Body"
                    value={competitorEmail.body}
                    onChange={(e) => setCompetitorEmail({...competitorEmail, body: e.target.value})}
                    placeholder="Paste competitor's email content"
                    multiline
                    rows={8}
                  />
                </>
              )}

              {/* Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-red-500/20 border border-red-500/30 rounded-xl flex items-center gap-3"
                >
                  <AlertCircle size={20} className="text-red-400" />
                  <span className="text-red-400">{error}</span>
                </motion.div>
              )}

              {/* Generate Button */}
              <motion.button
                onClick={handleGenerate}
                disabled={isProcessing}
                className="btn btn-primary w-full text-lg py-4"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="animate-spin" size={20} />
                    Processing with AI...
                  </>
                ) : (
                  <>
                    <Rocket size={20} />
                    Generate with Ultra AI
                  </>
                )}
              </motion.button>
            </div>
          </motion.div>

          {/* Results Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="card-premium"
          >
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-blue-500">
                <Star size={24} className="text-white" />
              </div>
              <h2 className="text-2xl font-bold text-primary">AI Results</h2>
            </div>

            <div className="min-h-[400px] flex items-center justify-center">
              <AnimatePresence mode="wait">
                {isProcessing ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="text-center"
                  >
                    <div className="loader-dots w-16 h-16 mx-auto mb-6"></div>
                    <h3 className="text-xl font-semibold text-primary mb-2">AI is crafting magic...</h3>
                    <p className="text-secondary">Analyzing your inputs with quantum intelligence</p>
                  </motion.div>
                ) : results ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="w-full"
                  >
                    <div className="space-y-6">
                      <div className="text-center p-6 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl border border-green-500/20">
                        <Trophy size={48} className="mx-auto mb-4 text-green-500" />
                        <h3 className="text-xl font-bold text-primary mb-2">Success!</h3>
                        <p className="text-secondary">Your AI-powered content is ready</p>
                      </div>
                      
                      <motion.button
                        onClick={() => setResults(null)}
                        className="btn btn-secondary w-full"
                        whileHover={{ scale: 1.02 }}
                      >
                        Generate Another
                      </motion.button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center"
                  >
                    <Brain size={64} className="mx-auto mb-6 text-purple-500 opacity-50" />
                    <h3 className="text-xl font-semibold text-primary mb-2">Ready for Ultra AI</h3>
                    <p className="text-secondary">Configure your settings and generate world-class content</p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UltraEmailGenerator;