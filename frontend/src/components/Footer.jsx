import { Link } from 'react-router-dom'
import { Brain, Hexagon, Network, Shield, Crown, GitBranch, Activity, Sparkles } from 'lucide-react'
import { colonyTheme, getTextClass, getBadgeClass, cn } from '../design-system/colony-theme'
import { Icon, NeuralPattern, ColonyIcon } from '../design-system/colony-icons'

const Footer = () => {
  return (
    <footer className="relative bg-gradient-to-b from-gray-900 to-black border-t border-neural-500/20">
      <div className="absolute inset-0 opacity-5">
        <NeuralPattern />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <ColonyIcon size={48} className="text-neural-500" />
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-neural-400 to-honey-400 bg-clip-text text-transparent">
                  NeuroColony
                </h2>
                <p className="text-gray-400 text-sm font-medium">AI Agent Platform</p>
              </div>
            </div>
            <p className={cn(getTextClass('body'), "mb-6 max-w-md text-gray-300")}>
              Orchestrate intelligent agent colonies that transform your business operations. 
              Built with swarm intelligence, powered by neural networks.
            </p>
            <div className="flex space-x-6">
              <a 
                href="#" 
                className="text-gray-400 hover:text-neural-400 transition-all duration-200 hover:scale-110"
                aria-label="Neural Network"
              >
                <Network className="h-6 w-6" />
              </a>
              <a 
                href="#" 
                className="text-gray-400 hover:text-honey-400 transition-all duration-200 hover:scale-110"
                aria-label="Colony Hive"
              >
                <Hexagon className="h-6 w-6" />
              </a>
              <a 
                href="#" 
                className="text-gray-400 hover:text-swarm-400 transition-all duration-200 hover:scale-110"
                aria-label="Agent Intelligence"
              >
                <Brain className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Colony Solutions */}
          <div>
            <h3 className={cn(getTextClass('h5'), "mb-4 flex items-center gap-2")}>
              <Icon name="swarm" size={20} />
              Colony Solutions
            </h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  to="/colony-command" 
                  className="text-gray-400 hover:text-neural-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-neural-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Colony Command Center
                </Link>
              </li>
              <li>
                <Link 
                  to="/agent-builder" 
                  className="text-gray-400 hover:text-neural-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-neural-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Agent Architect
                </Link>
              </li>
              <li>
                <Link 
                  to="/templates" 
                  className="text-gray-400 hover:text-honey-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-honey-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Colony Blueprints
                </Link>
              </li>
              <li>
                <Link 
                  to="/integration-hub" 
                  className="text-gray-400 hover:text-swarm-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-swarm-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Neural Integrations
                </Link>
              </li>
            </ul>
          </div>

          {/* Hive Resources */}
          <div>
            <h3 className={cn(getTextClass('h5'), "mb-4 flex items-center gap-2")}>
              <Icon name="hive" size={20} />
              Hive Resources
            </h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  to="/about" 
                  className="text-gray-400 hover:text-neural-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-neural-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  About the Colony
                </Link>
              </li>
              <li>
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-neural-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-neural-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Neural Blog
                </a>
              </li>
              <li>
                <Link 
                  to="/contact" 
                  className="text-gray-400 hover:text-honey-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-honey-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Connect to Hive
                </Link>
              </li>
              <li>
                <Link 
                  to="/faq" 
                  className="text-gray-400 hover:text-swarm-400 transition-all duration-200 flex items-center gap-2 group"
                >
                  <span className="w-1.5 h-1.5 bg-swarm-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  Colony Knowledge Base
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-neural-500/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <p className={cn(getTextClass('meta'), "text-gray-400")}>
                © 2025 NeuroColony. Neural rights reserved.
              </p>
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-swarm-500 animate-pulse" />
                <span className={cn(getTextClass('metaSmall'), "text-gray-500")}>
                  Colony Status: Active
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <Link 
                to="/privacy" 
                className={cn(
                  getTextClass('meta'),
                  "text-gray-400 hover:text-neural-400 transition-all duration-200"
                )}
              >
                Neural Privacy
              </Link>
              <Link 
                to="/terms" 
                className={cn(
                  getTextClass('meta'),
                  "text-gray-400 hover:text-neural-400 transition-all duration-200"
                )}
              >
                Colony Terms
              </Link>
              <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-neural-500/10 border border-neural-500/20">
                <Shield className="w-4 h-4 text-neural-400" />
                <span className={cn(getTextClass('metaSmall'), "text-neural-400 font-medium")}>
                  Secure Colony
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer