import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Check, X, Star, TrendingUp, Zap, Shield } from 'lucide-react'

const PricingComparison = () => {
  const [billingPeriod, setBillingPeriod] = useState('monthly')

  const competitors = [
    {
      name: "Platform A",
      price: "$97",
      sequences: "50",
      ai: "Basic",
      support: "Email",
      integrations: "Limited",
      analytics: "Basic"
    },
    {
      name: "Platform B",
      price: "$230",
      sequences: "Unlimited*",
      ai: "None",
      support: "Email", 
      integrations: "Good",
      analytics: "Advanced"
    },
    {
      name: "Platform C",
      price: "$145",
      sequences: "Unlimited*",
      ai: "Basic",
      support: "Chat",
      integrations: "Excellent",
      analytics: "Advanced"
    },
    {
      name: "NeuroColony",
      price: "$99",
      originalPrice: "$297",
      sequences: "200 + Overage",
      ai: "AI 3 + GPT-4",
      support: "Priority",
      integrations: "Unlimited",
      analytics: "AI-Powered",
      highlight: true
    }
  ]

  const features = [
    {
      category: "AI Models",
      items: [
        { name: "GPT-4 Access", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "AI 3 Access", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "Gemini Pro Access", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "Custom AI Training", platformA: false, platformB: false, platformC: false, sequenceai: true }
      ]
    },
    {
      category: "Sequences & Generation",
      items: [
        { name: "Unlimited Sequences", platformA: false, platformB: true, platformC: true, sequenceai: true },
        { name: "AI-Generated Content", platformA: "basic", platformB: false, platformC: "basic", sequenceai: true },
        { name: "Psychology Optimization", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "A/B Testing", platformA: true, platformB: true, platformC: true, sequenceai: true }
      ]
    },
    {
      category: "Analytics & Insights",
      items: [
        { name: "Real-time Analytics", platformA: true, platformB: true, platformC: true, sequenceai: true },
        { name: "Revenue Attribution", platformA: false, platformB: "basic", platformC: true, sequenceai: true },
        { name: "AI Performance Insights", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "Predictive Analytics", platformA: false, platformB: false, platformC: false, sequenceai: true }
      ]
    },
    {
      category: "Enterprise Features",
      items: [
        { name: "White-label Solution", platformA: false, platformB: false, platformC: false, sequenceai: true },
        { name: "API Access", platformA: "limited", platformB: true, platformC: true, sequenceai: true },
        { name: "Enterprise SSO", platformA: false, platformB: "paid", platformC: "paid", sequenceai: true },
        { name: "Custom Integrations", platformA: false, platformB: "paid", platformC: "paid", sequenceai: true }
      ]
    }
  ]

  const renderFeatureValue = (value) => {
    if (value === true) {
      return <Check className="w-5 h-5 text-green-400" />
    } else if (value === false) {
      return <X className="w-5 h-5 text-red-400" />
    } else if (value === "basic" || value === "limited") {
      return <span className="text-amber-400 text-sm">Limited</span>
    } else if (value === "paid") {
      return <span className="text-red-400 text-sm">Paid Add-on</span>
    } else {
      return <Check className="w-5 h-5 text-green-400" />
    }
  }

  return (
    <div className="py-16">
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-6">
          Why Switch From <span className="gradient-text">Expensive Alternatives</span>?
        </h2>
        <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
          Save <span className="text-green-400 font-bold">$2,376/year</span> while getting superior AI capabilities
        </p>
      </div>

      {/* Quick Comparison Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-16">
        {competitors.map((competitor, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`card text-center ${competitor.highlight ? 'border-glow scale-105' : ''}`}
          >
            {competitor.highlight && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-full text-sm font-bold">
                  🏆 BEST VALUE
                </span>
              </div>
            )}
            
            <h3 className="text-lg font-bold text-white mb-3">{competitor.name}</h3>
            
            <div className="mb-4">
              {competitor.originalPrice && (
                <div className="text-sm text-neutral-500 line-through">
                  {competitor.originalPrice}/month
                </div>
              )}
              <div className="text-2xl font-bold text-white">{competitor.price}/month</div>
              {competitor.highlight && (
                <div className="text-green-400 text-sm font-medium">
                  Save ${parseInt(competitor.originalPrice.substring(1)) - parseInt(competitor.price.substring(1))}/month
                </div>
              )}
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-neutral-400">Sequences:</span>
                <span className="text-white">{competitor.sequences}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-400">AI Models:</span>
                <span className="text-white">{competitor.ai}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-400">Support:</span>
                <span className="text-white">{competitor.support}</span>
              </div>
            </div>

            {competitor.highlight && (
              <div className="mt-4">
                <a
                  href="/register?plan=business&promo=40OFF"
                  className="btn btn-primary w-full text-sm"
                >
                  Get 40% Off Now
                </a>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Detailed Feature Comparison */}
      <div className="bg-gradient-to-b from-neutral-900 to-neutral-950 rounded-2xl p-8">
        <h3 className="text-2xl font-bold text-center mb-8">
          Detailed Feature Comparison
        </h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-neutral-700">
                <th className="text-left py-4 text-neutral-300">Features</th>
                <th className="text-center py-4 text-neutral-400 w-24">Platform A<br/><span className="text-xs">$97/mo</span></th>
                <th className="text-center py-4 text-neutral-400 w-24">Platform B<br/><span className="text-xs">$230/mo</span></th>
                <th className="text-center py-4 text-neutral-400 w-24">Platform C<br/><span className="text-xs">$145/mo</span></th>
                <th className="text-center py-4 text-green-400 w-24 bg-green-400/10 rounded-lg">
                  NeuroColony<br/>
                  <span className="text-xs line-through text-neutral-500">$297/mo</span><br/>
                  <span className="text-xs font-bold">$99/mo</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {features.map((category, categoryIndex) => (
                <React.Fragment key={categoryIndex}>
                  <tr>
                    <td colSpan={5} className="py-4">
                      <h4 className="text-lg font-semibold text-white">{category.category}</h4>
                    </td>
                  </tr>
                  {category.items.map((feature, featureIndex) => (
                    <tr key={featureIndex} className="border-b border-neutral-800">
                      <td className="py-3 text-neutral-300">{feature.name}</td>
                      <td className="text-center py-3">{renderFeatureValue(feature.platformA)}</td>
                      <td className="text-center py-3">{renderFeatureValue(feature.platformB)}</td>
                      <td className="text-center py-3">{renderFeatureValue(feature.platformC)}</td>
                      <td className="text-center py-3 bg-green-400/5">
                        {renderFeatureValue(feature.sequenceai)}
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* ROI Calculator */}
        <div className="mt-12 bg-gradient-to-r from-purple-500/10 to-green-500/10 rounded-xl p-6">
          <h3 className="text-xl font-bold text-white mb-4 text-center">
             Your Annual Savings with NeuroColony
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400 mb-2">vs Platform A</div>
              <div className="text-neutral-300">Save <span className="text-green-400 font-bold">$816/year</span></div>
              <div className="text-sm text-neutral-400">($97 - $29) × 12 months</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400 mb-2">vs Platform B</div>
              <div className="text-neutral-300">Save <span className="text-green-400 font-bold">$1,572/year</span></div>
              <div className="text-sm text-neutral-400">($230 - $99) × 12 months</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400 mb-2">vs Platform C</div>
              <div className="text-neutral-300">Save <span className="text-green-400 font-bold">$552/year</span></div>
              <div className="text-sm text-neutral-400">($145 - $99) × 12 months</div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <div className="text-2xl font-bold text-green-400 mb-2">
              Average Savings: $980/year
            </div>
            <p className="text-neutral-300">
              Plus you get superior AI capabilities that competitors charge extra for
            </p>
          </div>
        </div>

        {/* Migration Offer */}
        <div className="mt-8 bg-amber-500/10 border border-amber-500/30 rounded-xl p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-3">
             Free Migration from Any Competitor
          </h3>
          <p className="text-neutral-300 mb-4">
            Switching from other email marketing platforms? We'll migrate your data for FREE and give you an additional 20% off your first year.
          </p>
          <a
            href="/register?migration=true&promo=MIGRATE20"
            className="btn btn-secondary"
          >
            Start Free Migration
          </a>
        </div>
      </div>
    </div>
  )
}

export default PricingComparison