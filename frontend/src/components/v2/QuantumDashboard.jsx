import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, Zap, Brain, Target, Mail, 
  BarChart3, Sparkles, Globe, MessageSquare,
  Mic, Languages, Trophy, Rocket
} from 'lucide-react';
import '../../styles/v2-theme.css';

const QuantumDashboard = () => {
  const [activeMetric, setActiveMetric] = useState(null);
  const [aiInsights, setAiInsights] = useState([]);
  const [voiceMode, setVoiceMode] = useState(false);

  // Animated metrics data
  const metrics = [
    { 
      id: 'sequences', 
      value: '2,847', 
      change: '+23%', 
      label: 'Email Sequences',
      icon: Mail,
      color: 'var(--quantum-purple)'
    },
    { 
      id: 'engagement', 
      value: '67.3%', 
      change: '+5.2%', 
      label: 'Avg Engagement',
      icon: Target,
      color: 'var(--quantum-blue)'
    },
    { 
      id: 'revenue', 
      value: '$45.2K', 
      change: '+12%', 
      label: 'Generated Revenue',
      icon: TrendingUp,
      color: 'var(--quantum-cyan)'
    },
    { 
      id: 'ai-score', 
      value: '94/100', 
      change: '+8', 
      label: 'AI Quality Score',
      icon: Brain,
      color: 'var(--quantum-pink)'
    }
  ];

  // New AI Features
  const newFeatures = [
    {
      icon: Mic,
      title: 'Voice-to-Email AI',
      description: 'Speak your ideas, get perfect email sequences',
      status: 'new'
    },
    {
      icon: Languages,
      title: 'Multi-Language Support',
      description: 'Generate emails in 50+ languages with cultural adaptation',
      status: 'new'
    },
    {
      icon: BarChart3,
      title: 'Performance Prediction',
      description: 'AI predicts email success before sending',
      status: 'coming'
    },
    {
      icon: Trophy,
      title: 'Competitor Analysis',
      description: 'Analyze and outperform competitor emails',
      status: 'coming'
    }
  ];

  // Generate AI insights
  useEffect(() => {
    const insights = [
      "Your welcome emails perform 34% better with personalized subject lines",
      "Tuesday 10 AM shows highest engagement for your audience",
      "Adding urgency in email 3 increases conversions by 23%",
      "Your storytelling approach resonates best with tech audiences"
    ];
    setAiInsights(insights);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 p-6">
      <div className="particle-bg"></div>
      
      {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-5xl font-bold holographic mb-2">
          NeuroColony Dashboard
        </h1>
        <p className="text-gray-300 text-lg">
          Quantum-powered email intelligence at your fingertips
        </p>
      </motion.div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="glass-card p-6 cursor-pointer"
            onMouseEnter={() => setActiveMetric(metric.id)}
            onMouseLeave={() => setActiveMetric(null)}
          >
            <div className="flex items-start justify-between mb-4">
              <metric.icon 
                size={24} 
                style={{ color: metric.color }}
                className="floating"
              />
              <span className={`text-sm font-semibold ${
                metric.change.startsWith('+') ? 'text-green-400' : 'text-red-400'
              }`}>
                {metric.change}
              </span>
            </div>
            <h3 className="text-3xl font-bold text-white mb-1">
              {metric.value}
            </h3>
            <p className="text-gray-400 text-sm">{metric.label}</p>
            
            <AnimatePresence>
              {activeMetric === metric.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="mt-4 pt-4 border-t border-gray-700"
                >
                  <div className="h-20 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded"></div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* AI Insights Section */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.4 }}
        className="glass-card p-6 mb-8"
      >
        <div className="flex items-center mb-4">
          <Sparkles className="text-yellow-400 mr-2" />
          <h2 className="text-2xl font-bold text-white">AI Insights</h2>
        </div>
        <div className="space-y-3">
          {aiInsights.map((insight, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
              className="flex items-start"
            >
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 mt-2 mr-3"></div>
              <p className="text-gray-300">{insight}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* New Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {newFeatures.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 + index * 0.1 }}
            className="glass-card p-6 card-3d"
          >
            <div className="flex items-start justify-between mb-4">
              <feature.icon size={32} className="text-purple-400" />
              {feature.status === 'new' && (
                <span className="px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-semibold">
                  NEW
                </span>
              )}
              {feature.status === 'coming' && (
                <span className="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-xs font-semibold">
                  COMING SOON
                </span>
              )}
            </div>
            <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
            <p className="text-gray-400">{feature.description}</p>
          </motion.div>
        ))}
      </div>

      {/* Voice Mode Toggle */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="fixed bottom-8 right-8"
      >
        <button
          onClick={() => setVoiceMode(!voiceMode)}
          className={`quantum-button flex items-center space-x-2 ${
            voiceMode ? 'bg-gradient-to-r from-red-500 to-pink-500' : ''
          }`}
        >
          <Mic size={20} />
          <span>{voiceMode ? 'Voice Mode Active' : 'Enable Voice Mode'}</span>
        </button>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.8 }}
        className="fixed bottom-8 left-8 flex space-x-4"
      >
        <button className="glass-card p-4 rounded-full hover:scale-110 transition-transform">
          <MessageSquare size={24} className="text-purple-400" />
        </button>
        <button className="glass-card p-4 rounded-full hover:scale-110 transition-transform">
          <Globe size={24} className="text-blue-400" />
        </button>
        <button className="glass-card p-4 rounded-full hover:scale-110 transition-transform">
          <Rocket size={24} className="text-cyan-400" />
        </button>
      </motion.div>
    </div>
  );
};

export default QuantumDashboard;