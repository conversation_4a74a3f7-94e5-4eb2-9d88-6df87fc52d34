import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wand2, Globe, Brain, Trophy, Mic, MicOff,
  Languages, BarChart3, Sparkles, Copy, Check,
  ChevronRight, Loader2, AlertCircle
} from 'lucide-react';
import '../../styles/v2-theme.css';

const AdvancedEmailGenerator = ({ user }) => {
  const [activeFeature, setActiveFeature] = useState('generate');
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [copiedItems, setCopiedItems] = useState({});
  
  // Form states
  const [emailContent, setEmailContent] = useState({
    subject: '',
    body: ''
  });
  
  const [businessInfo, setBusinessInfo] = useState({
    industry: '',
    productService: '',
    targetAudience: '',
    pricePoint: '',
    tone: 'professional'
  });
  
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [competitorEmail, setCompetitorEmail] = useState({
    subject: '',
    body: ''
  });

  // Voice recording state
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioBlob, setAudioBlob] = useState(null);

  const features = [
    {
      id: 'generate',
      icon: Wand2,
      title: 'AI Email Generation',
      description: 'Generate complete email sequences with AI',
      color: 'from-purple-500 to-blue-500'
    },
    {
      id: 'translate',
      icon: Languages,
      title: 'Multi-Language',
      description: 'Translate and adapt emails for global markets',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'predict',
      icon: BarChart3,
      title: 'Performance Prediction',
      description: 'Predict email success before sending',
      color: 'from-cyan-500 to-green-500'
    },
    {
      id: 'optimize',
      icon: Sparkles,
      title: 'Subject Line Optimizer',
      description: 'Create high-converting subject lines',
      color: 'from-green-500 to-yellow-500'
    },
    {
      id: 'analyze',
      icon: Trophy,
      title: 'Competitor Analysis',
      description: 'Analyze and outperform competitor emails',
      color: 'from-yellow-500 to-red-500'
    }
  ];

  const languages = [
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷' }
  ];

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const chunks = [];

      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      setMediaRecorder(recorder);
      recorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Recording error:', error);
      setError('Microphone access denied');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const processVoiceInput = async () => {
    if (!audioBlob) return;

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);
      formData.append('businessInfo', JSON.stringify(businessInfo));

      const response = await api.post('/ai/voice-to-email', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setResults(response.data);
      setAudioBlob(null);
    } catch (error) {
      setError(error.response?.data?.error || 'Voice processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerate = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/sequences/generate', {
        businessInfo,
        settings: {
          sequenceLength: 7,
          tone: businessInfo.tone,
          primaryGoal: 'conversion',
          includeCTA: true,
          includePersonalization: true
        }
      });

      setResults({
        type: 'sequence',
        data: response.data.sequence
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Generation failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTranslate = async () => {
    if (!emailContent.subject || !emailContent.body || selectedLanguages.length === 0) {
      setError('Please provide email content and select languages');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/ai-advanced/translate', {
        emailContent,
        targetLanguages: selectedLanguages
      });

      setResults({
        type: 'translations',
        data: response.data.translations
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Translation failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePredict = async () => {
    if (!emailContent.subject || !emailContent.body) {
      setError('Please provide email subject and body');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/ai-advanced/predict-performance', emailContent);

      setResults({
        type: 'prediction',
        data: response.data.prediction
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Prediction failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleOptimize = async () => {
    if (!emailContent.subject) {
      setError('Please provide a subject line');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/ai-advanced/optimize-subject', {
        originalSubject: emailContent.subject,
        context: businessInfo
      });

      setResults({
        type: 'optimization',
        data: response.data.optimization
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Optimization failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAnalyze = async () => {
    if (!competitorEmail.subject || !competitorEmail.body) {
      setError('Please provide competitor email content');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await api.post('/ai-advanced/analyze-competitor', {
        competitorEmail,
        yourBrand: businessInfo
      });

      setResults({
        type: 'analysis',
        data: response.data.analysis
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Analysis failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async (text, itemId) => {
    await navigator.clipboard.writeText(text);
    setCopiedItems({ ...copiedItems, [itemId]: true });
    setTimeout(() => {
      setCopiedItems({ ...copiedItems, [itemId]: false });
    }, 2000);
  };

  const renderResults = () => {
    if (!results) return null;

    switch (results.type) {
      case 'sequence':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-4">Generated Email Sequence</h3>
            {results.data.emails.map((email, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="glass-card p-6"
              >
                <div className="flex justify-between items-start mb-4">
                  <h4 className="text-xl font-semibold text-white">
                    Email {index + 1} - Day {email.dayDelay}
                  </h4>
                  <button
                    onClick={() => copyToClipboard(
                      `Subject: ${email.subject}\n\n${email.body}`,
                      `email-${index}`
                    )}
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  >
                    {copiedItems[`email-${index}`] ? (
                      <Check size={20} className="text-green-400" />
                    ) : (
                      <Copy size={20} className="text-gray-400" />
                    )}
                  </button>
                </div>
                <p className="text-purple-400 mb-2">Subject: {email.subject}</p>
                <p className="text-gray-300 whitespace-pre-wrap">{email.body}</p>
                {email.psychologyTriggers && (
                  <div className="mt-4 flex flex-wrap gap-2">
                    {email.psychologyTriggers.map((trigger, i) => (
                      <span
                        key={i}
                        className="px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full text-xs"
                      >
                        {trigger}
                      </span>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        );

      case 'translations':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-4">Translated Emails</h3>
            {Object.entries(results.data).map(([lang, translation]) => (
              <motion.div
                key={lang}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="glass-card p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-xl font-semibold text-white flex items-center">
                    <span className="text-2xl mr-2">
                      {languages.find(l => l.code === lang)?.flag}
                    </span>
                    {languages.find(l => l.code === lang)?.name}
                  </h4>
                  <button
                    onClick={() => copyToClipboard(
                      `Subject: ${translation.subject}\n\n${translation.body}`,
                      `trans-${lang}`
                    )}
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  >
                    {copiedItems[`trans-${lang}`] ? (
                      <Check size={20} className="text-green-400" />
                    ) : (
                      <Copy size={20} className="text-gray-400" />
                    )}
                  </button>
                </div>
                <p className="text-blue-400 mb-2">Subject: {translation.subject}</p>
                <p className="text-gray-300 whitespace-pre-wrap mb-4">{translation.body}</p>
                {translation.culturalNotes && (
                  <div className="mt-4 p-4 bg-blue-500/10 rounded-lg">
                    <p className="text-blue-400 font-semibold mb-2">Cultural Notes:</p>
                    <ul className="text-gray-300 space-y-1">
                      {translation.culturalNotes.map((note, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-blue-400 mr-2">•</span>
                          {note}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {translation.bestSendTime && (
                  <p className="text-gray-400 mt-2">
                    Best send time: {translation.bestSendTime}
                  </p>
                )}
              </motion.div>
            ))}
          </div>
        );

      case 'prediction':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-4">Performance Prediction</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(results.data.predictedMetrics).map(([metric, value]) => (
                <motion.div
                  key={metric}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="glass-card p-4 text-center"
                >
                  <p className="text-gray-400 text-sm capitalize">
                    {metric.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                  <p className="text-2xl font-bold text-white mt-1">{value}</p>
                </motion.div>
              ))}
            </div>
            {results.data.insights.length > 0 && (
              <div className="glass-card p-6">
                <h4 className="text-xl font-semibold text-white mb-4">AI Insights</h4>
                <ul className="space-y-2">
                  {results.data.insights.map((insight, i) => (
                    <li key={i} className="flex items-start">
                      <Sparkles size={16} className="text-yellow-400 mr-2 mt-0.5" />
                      <span className="text-gray-300">{insight}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {results.data.recommendations.length > 0 && (
              <div className="glass-card p-6">
                <h4 className="text-xl font-semibold text-white mb-4">Recommendations</h4>
                <ul className="space-y-2">
                  {results.data.recommendations.map((rec, i) => (
                    <li key={i} className="flex items-start">
                      <ChevronRight size={16} className="text-green-400 mr-2 mt-0.5" />
                      <span className="text-gray-300">{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );

      case 'optimization':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-4">Optimized Subject Lines</h3>
            <div className="glass-card p-6">
              <p className="text-gray-400 mb-2">Original:</p>
              <p className="text-white text-lg mb-4">{results.data.original}</p>
              <p className="text-gray-400 mb-4">AI-Optimized Variations:</p>
              <div className="space-y-3">
                {results.data.variations.map((variation, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: i * 0.1 }}
                    className="flex items-center justify-between p-4 bg-white/5 rounded-lg"
                  >
                    <div className="flex-1">
                      <p className="text-white">{variation.text}</p>
                      <div className="flex items-center mt-2 space-x-4">
                        <span className="text-xs text-purple-400">
                          {variation.trigger} trigger
                        </span>
                        <span className="text-xs text-green-400">
                          {(variation.predictedOpenRate * 100).toFixed(1)}% predicted open rate
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => copyToClipboard(variation.text, `opt-${i}`)}
                      className="p-2 hover:bg-white/10 rounded-lg transition-colors ml-4"
                    >
                      {copiedItems[`opt-${i}`] ? (
                        <Check size={20} className="text-green-400" />
                      ) : (
                        <Copy size={20} className="text-gray-400" />
                      )}
                    </button>
                  </motion.div>
                ))}
              </div>
              {results.data.winner && (
                <div className="mt-6 p-4 bg-green-500/20 rounded-lg border border-green-500/30">
                  <p className="text-green-400 font-semibold mb-1">🏆 Recommended Winner:</p>
                  <p className="text-white">{results.data.winner.text}</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'analysis':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-4">Competitor Analysis</h3>
            
            {results.data.strengths.length > 0 && (
              <div className="glass-card p-6">
                <h4 className="text-xl font-semibold text-green-400 mb-4">
                  Competitor Strengths
                </h4>
                <ul className="space-y-2">
                  {results.data.strengths.map((strength, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-green-400 mr-2">✓</span>
                      <span className="text-gray-300">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {results.data.weaknesses.length > 0 && (
              <div className="glass-card p-6">
                <h4 className="text-xl font-semibold text-red-400 mb-4">
                  Competitor Weaknesses
                </h4>
                <ul className="space-y-2">
                  {results.data.weaknesses.map((weakness, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-red-400 mr-2">✗</span>
                      <span className="text-gray-300">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {results.data.opportunities.length > 0 && (
              <div className="glass-card p-6">
                <h4 className="text-xl font-semibold text-blue-400 mb-4">
                  Your Opportunities
                </h4>
                <ul className="space-y-2">
                  {results.data.opportunities.map((opportunity, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-blue-400 mr-2">→</span>
                      <span className="text-gray-300">{opportunity}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {results.data.recommendations.length > 0 && (
              <div className="glass-card p-6 bg-purple-500/10 border border-purple-500/30">
                <h4 className="text-xl font-semibold text-purple-400 mb-4">
                  Strategic Recommendations
                </h4>
                <ul className="space-y-2">
                  {results.data.recommendations.map((rec, i) => (
                    <li key={i} className="flex items-start">
                      <Trophy size={16} className="text-purple-400 mr-2 mt-0.5" />
                      <span className="text-gray-300">{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {results.data.competitiveAdvantage && (
              <div className="text-center p-4">
                <p className="text-gray-400">Competitive Advantage Score</p>
                <p className="text-4xl font-bold holographic">
                  {results.data.competitiveAdvantage}/100
                </p>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 p-6">
      <div className="particle-bg"></div>
      
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        <h1 className="text-5xl font-bold holographic mb-2 text-center">
          Advanced AI Email Suite
        </h1>
        <p className="text-gray-300 text-lg text-center mb-8">
          Next-generation email intelligence powered by quantum AI
        </p>

        {/* Feature Selector */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          {features.map((feature) => (
            <motion.button
              key={feature.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveFeature(feature.id)}
              className={`glass-card p-4 text-center transition-all ${
                activeFeature === feature.id
                  ? 'border-2 border-purple-500 shadow-lg shadow-purple-500/30'
                  : ''
              }`}
            >
              <div className={`w-12 h-12 mx-auto mb-2 rounded-full bg-gradient-to-r ${feature.color} flex items-center justify-center`}>
                <feature.icon size={24} className="text-white" />
              </div>
              <h3 className="text-white font-semibold text-sm">{feature.title}</h3>
              <p className="text-gray-400 text-xs mt-1">{feature.description}</p>
            </motion.button>
          ))}
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="glass-card p-8"
          >
            <h2 className="text-2xl font-bold text-white mb-6">
              {features.find(f => f.id === activeFeature)?.title}
            </h2>

            {/* Dynamic Input Forms */}
            {activeFeature === 'generate' && (
              <div className="space-y-4">
                <div>
                  <label className="text-gray-300 text-sm">Industry</label>
                  <input
                    type="text"
                    value={businessInfo.industry}
                    onChange={(e) => setBusinessInfo({...businessInfo, industry: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="e.g., SaaS, E-commerce, Healthcare"
                  />
                </div>
                <div>
                  <label className="text-gray-300 text-sm">Product/Service</label>
                  <input
                    type="text"
                    value={businessInfo.productService}
                    onChange={(e) => setBusinessInfo({...businessInfo, productService: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="e.g., Email Marketing Platform"
                  />
                </div>
                <div>
                  <label className="text-gray-300 text-sm">Target Audience</label>
                  <input
                    type="text"
                    value={businessInfo.targetAudience}
                    onChange={(e) => setBusinessInfo({...businessInfo, targetAudience: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="e.g., Small Business Owners"
                  />
                </div>
                <div>
                  <label className="text-gray-300 text-sm">Price Point</label>
                  <input
                    type="text"
                    value={businessInfo.pricePoint}
                    onChange={(e) => setBusinessInfo({...businessInfo, pricePoint: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="e.g., $29/month"
                  />
                </div>
                
                {/* Voice Input Option */}
                <div className="mt-6 p-4 bg-purple-500/10 rounded-lg">
                  <p className="text-purple-400 mb-3 flex items-center">
                    <Mic size={20} className="mr-2" />
                    Or use voice input (Beta)
                  </p>
                  <button
                    onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                    className={`quantum-button ${isRecording ? 'bg-red-500' : ''}`}
                  >
                    {isRecording ? (
                      <>
                        <MicOff size={20} className="mr-2" />
                        Stop Recording
                      </>
                    ) : (
                      <>
                        <Mic size={20} className="mr-2" />
                        Start Recording
                      </>
                    )}
                  </button>
                  {audioBlob && (
                    <button
                      onClick={processVoiceInput}
                      className="quantum-button ml-4"
                      disabled={isProcessing}
                    >
                      Process Voice
                    </button>
                  )}
                </div>
              </div>
            )}

            {(activeFeature === 'translate' || activeFeature === 'predict' || activeFeature === 'optimize') && (
              <div className="space-y-4">
                <div>
                  <label className="text-gray-300 text-sm">Email Subject</label>
                  <input
                    type="text"
                    value={emailContent.subject}
                    onChange={(e) => setEmailContent({...emailContent, subject: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="Enter your email subject line"
                  />
                </div>
                {(activeFeature === 'translate' || activeFeature === 'predict') && (
                  <div>
                    <label className="text-gray-300 text-sm">Email Body</label>
                    <textarea
                      value={emailContent.body}
                      onChange={(e) => setEmailContent({...emailContent, body: e.target.value})}
                      className="neon-input w-full mt-1 min-h-[200px]"
                      placeholder="Enter your email content"
                    />
                  </div>
                )}
                {activeFeature === 'translate' && (
                  <div>
                    <label className="text-gray-300 text-sm mb-2 block">Select Languages</label>
                    <div className="grid grid-cols-2 gap-2">
                      {languages.map((lang) => (
                        <label
                          key={lang.code}
                          className={`flex items-center p-3 rounded-lg cursor-pointer transition-all ${
                            selectedLanguages.includes(lang.code)
                              ? 'bg-purple-500/30 border border-purple-500'
                              : 'bg-white/5 hover:bg-white/10'
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={selectedLanguages.includes(lang.code)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedLanguages([...selectedLanguages, lang.code]);
                              } else {
                                setSelectedLanguages(selectedLanguages.filter(l => l !== lang.code));
                              }
                            }}
                            className="sr-only"
                          />
                          <span className="text-2xl mr-2">{lang.flag}</span>
                          <span className="text-white text-sm">{lang.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeFeature === 'analyze' && (
              <div className="space-y-4">
                <div>
                  <label className="text-gray-300 text-sm">Competitor Email Subject</label>
                  <input
                    type="text"
                    value={competitorEmail.subject}
                    onChange={(e) => setCompetitorEmail({...competitorEmail, subject: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="Enter competitor's subject line"
                  />
                </div>
                <div>
                  <label className="text-gray-300 text-sm">Competitor Email Body</label>
                  <textarea
                    value={competitorEmail.body}
                    onChange={(e) => setCompetitorEmail({...competitorEmail, body: e.target.value})}
                    className="neon-input w-full mt-1 min-h-[200px]"
                    placeholder="Paste competitor's email content"
                  />
                </div>
                <div>
                  <label className="text-gray-300 text-sm">Your Brand Info (Optional)</label>
                  <input
                    type="text"
                    value={businessInfo.industry}
                    onChange={(e) => setBusinessInfo({...businessInfo, industry: e.target.value})}
                    className="neon-input w-full mt-1"
                    placeholder="Your industry/niche"
                  />
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center"
              >
                <AlertCircle size={20} className="text-red-400 mr-2" />
                <span className="text-red-400">{error}</span>
              </motion.div>
            )}

            {/* Action Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                switch (activeFeature) {
                  case 'generate': handleGenerate(); break;
                  case 'translate': handleTranslate(); break;
                  case 'predict': handlePredict(); break;
                  case 'optimize': handleOptimize(); break;
                  case 'analyze': handleAnalyze(); break;
                }
              }}
              disabled={isProcessing}
              className="quantum-button w-full mt-6 flex items-center justify-center"
            >
              {isProcessing ? (
                <>
                  <Loader2 size={20} className="mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Wand2 size={20} className="mr-2" />
                  {activeFeature === 'generate' && 'Generate Sequence'}
                  {activeFeature === 'translate' && 'Translate Email'}
                  {activeFeature === 'predict' && 'Predict Performance'}
                  {activeFeature === 'optimize' && 'Optimize Subject'}
                  {activeFeature === 'analyze' && 'Analyze Competitor'}
                </>
              )}
            </motion.button>
          </motion.div>

          {/* Results Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="glass-card p-8 max-h-[800px] overflow-y-auto"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Results</h2>
            
            <AnimatePresence mode="wait">
              {isProcessing ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex flex-col items-center justify-center h-64"
                >
                  <div className="quantum-loader mb-4"></div>
                  <p className="text-gray-400">AI is working its magic...</p>
                </motion.div>
              ) : results ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  {renderResults()}
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center text-gray-400 py-16"
                >
                  <Brain size={48} className="mx-auto mb-4 opacity-50" />
                  <p>Your AI-powered results will appear here</p>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default AdvancedEmailGenerator;