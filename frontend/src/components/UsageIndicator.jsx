import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { BarChart3, AlertTriangle, CheckCircle } from 'lucide-react'

const UsageIndicator = ({ user, compact = false }) => {
  const [usageStats, setUsageStats] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUsageStats()
  }, [])

  const loadUsageStats = async () => {
    try {
      const response = await fetch('/api/usage/stats', {
        headers: { 
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setUsageStats(data.data)
      }

    } catch (error) {
      console.error('Failed to load usage stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading || !usageStats) {
    return compact ? null : (
      <div className="animate-pulse bg-gray-200 h-16 rounded-lg"></div>
    )
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default: return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      default: return <CheckCircle className="h-4 w-4" />
    }
  }

  if (compact) {
    return (
      <motion.div 
        className={`inline-flex items-center space-x-2 px-3 py-1.5 rounded-full border text-sm ${getStatusColor(usageStats.status)}`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <BarChart3 className="h-3 w-3" />
        <span>{usageStats.sequencesGenerated}/{usageStats.sequencesLimit}</span>
        {usageStats.overageSequences > 0 && (
          <span className="text-xs">+{usageStats.overageSequences} overage</span>
        )}
      </motion.div>
    )
  }

  return (
    <motion.div 
      className={`border rounded-lg p-4 ${getStatusColor(usageStats.status)}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon(usageStats.status)}
          <h3 className="font-medium">Usage This Month</h3>
        </div>
        <span className="text-sm font-semibold">
          {usageStats.usagePercentage}% Used
        </span>
      </div>
      
      <div className="mb-3">
        <div className="flex justify-between text-sm mb-1">
          <span>Sequences Generated</span>
          <span>{usageStats.sequencesGenerated} / {usageStats.sequencesLimit}</span>
        </div>
        <div className="w-full bg-white/50 rounded-full h-2">
          <motion.div 
            className={`h-2 rounded-full ${
              usageStats.status === 'critical' ? 'bg-red-500' :
              usageStats.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(usageStats.usagePercentage, 100)}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
        </div>
      </div>

      {usageStats.overageSequences > 0 && (
        <div className="text-sm">
          <span className="font-medium">Overage:</span> {usageStats.overageSequences} sequences (${usageStats.overageCharges})
        </div>
      )}
      
      {usageStats.canGoOverage && !user?.usage?.notifications?.overageConsentGiven && 
       usageStats.sequencesGenerated >= usageStats.sequencesLimit && (
        <div className="mt-2 text-sm">
          <span className="font-medium">Enable overage billing to continue at $3/sequence</span>
        </div>
      )}
    </motion.div>
  )
}

export default UsageIndicator