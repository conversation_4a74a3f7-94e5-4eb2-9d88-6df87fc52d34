import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp, 
  Calendar,
  DollarSign,
  Settings,
  Bell,
  Info
} from 'lucide-react'
import toast from 'react-hot-toast'

const UsageDashboard = ({ user }) => {
  const [usageStats, setUsageStats] = useState(null)
  const [usageHistory, setUsageHistory] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showOverageConsent, setShowOverageConsent] = useState(false)

  useEffect(() => {
    loadUsageData()
  }, [])

  const loadUsageData = async () => {
    try {
      const [statsResponse, historyResponse] = await Promise.all([
        fetch('/api/usage/stats', {
          headers: { 
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/usage/history', {
          headers: { 
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
      ])

      const [statsData, historyData] = await Promise.all([
        statsResponse.json(),
        historyResponse.json()
      ])

      if (statsData.success) {
        setUsageStats(statsData.data)
      }

      if (historyData.success) {
        setUsageHistory(historyData.data)
      }

    } catch (error) {
      console.error('Failed to load usage data:', error)
      toast.error('Failed to load usage statistics')
    } finally {
      setLoading(false)
    }
  }

  const handleOverageConsent = async () => {
    try {
      const response = await fetch('/api/usage/overage-consent', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Overage billing enabled! You can now continue generating sequences.')
        setUsageStats(data.data.stats)
        setShowOverageConsent(false)
      } else {
        toast.error(data.message || 'Failed to enable overage billing')
      }

    } catch (error) {
      console.error('Overage consent error:', error)
      toast.error('Failed to enable overage billing')
    }
  }

  const handleRevokeOverage = async () => {
    try {
      const response = await fetch('/api/usage/overage-consent', {
        method: 'DELETE',
        headers: { 
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Overage billing disabled')
        setUsageStats(data.data.stats)
      } else {
        toast.error(data.message || 'Failed to disable overage billing')
      }

    } catch (error) {
      console.error('Overage revocation error:', error)
      toast.error('Failed to disable overage billing')
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!usageStats) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center text-gray-500">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p>Unable to load usage statistics</p>
        </div>
      </div>
    )
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default: return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'critical': return <AlertTriangle className="h-5 w-5" />
      case 'warning': return <Bell className="h-5 w-5" />
      default: return <CheckCircle className="h-5 w-5" />
    }
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Usage Overview */}
      <motion.div 
        className="bg-white rounded-lg shadow-sm border"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Usage Overview</h3>
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${getStatusColor(usageStats.status)}`}>
              {getStatusIcon(usageStats.status)}
              <span className="text-sm font-medium">
                {usageStats.usagePercentage}% Used
              </span>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Sequences Generated</span>
              <span>{usageStats.sequencesGenerated} / {usageStats.sequencesLimit}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div 
                className={`h-3 rounded-full ${
                  usageStats.status === 'critical' ? 'bg-red-500' :
                  usageStats.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                initial={{ width: 0 }}
                animate={{ width: `${Math.min(usageStats.usagePercentage, 100)}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <BarChart3 className="h-8 w-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {usageStats.sequencesGenerated}
              </div>
              <div className="text-sm text-gray-600">Sequences Generated</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Calendar className="h-8 w-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {Math.max(0, usageStats.sequencesLimit - usageStats.sequencesGenerated)}
              </div>
              <div className="text-sm text-gray-600">Remaining</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Calendar className="h-8 w-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {formatDate(usageStats.periodEnd)}
              </div>
              <div className="text-sm text-gray-600">Period Ends</div>
            </div>
          </div>

          {/* Overage Section */}
          {usageStats.canGoOverage && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-blue-900 mb-2">Overage Billing</h4>
                  <p className="text-sm text-blue-700 mb-3">
                    Continue generating sequences beyond your limit at ${usageStats.overageRate} each. 
                    {usageStats.overageSequences > 0 && (
                      <span className="font-medium">
                        {' '}Current overage: {usageStats.overageSequences} sequences (${usageStats.overageCharges})
                      </span>
                    )}
                  </p>
                  
                  <div className="flex items-center space-x-3">
                    {user?.usage?.notifications?.overageConsentGiven ? (
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-700 font-medium">Enabled</span>
                        <button
                          onClick={handleRevokeOverage}
                          className="text-sm text-blue-600 hover:text-blue-800 underline"
                        >
                          Disable
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setShowOverageConsent(true)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Enable Overage Billing
                      </button>
                    )}
                  </div>
                </div>
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          )}
        </div>
      </motion.div>

      {/* Projections */}
      {usageHistory?.projections && (
        <motion.div 
          className="bg-white rounded-lg shadow-sm border"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900">Usage Projections</h3>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <TrendingUp className="h-8 w-8 text-primary-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">
                  {usageHistory.projections.estimatedMonthlyUsage}
                </div>
                <div className="text-sm text-gray-600">Projected Monthly Usage</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <BarChart3 className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">
                  {usageHistory.projections.estimatedOverageSequences}
                </div>
                <div className="text-sm text-gray-600">Projected Overage</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">
                  ${usageHistory.projections.estimatedOverageCharges}
                </div>
                <div className="text-sm text-gray-600">Projected Overage Cost</div>
              </div>
            </div>
            
            {usageHistory.projections.estimatedOverageCharges > 0 && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Info className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    Based on current usage, you may incur ${usageHistory.projections.estimatedOverageCharges} in overage charges this month.
                  </span>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Usage History */}
      {usageHistory?.history && usageHistory.history.length > 0 && (
        <motion.div 
          className="bg-white rounded-lg shadow-sm border"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900">Usage History</h3>
          </div>
          
          <div className="p-6">
            <div className="space-y-3">
              {usageHistory.history.map((period, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{period.period}</div>
                    <div className="text-sm text-gray-600">
                      {period.sequencesGenerated} sequences generated
                      {period.overageSequences > 0 && (
                        <span className="text-orange-600">
                          {' '}+ {period.overageSequences} overage
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    {period.overageCharges > 0 ? (
                      <div className="text-orange-600 font-medium">
                        ${period.overageCharges}
                      </div>
                    ) : (
                      <div className="text-green-600 font-medium">
                        No overage
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Overage Consent Modal */}
      {showOverageConsent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div 
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Enable Overage Billing</h3>
            
            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                You've reached your monthly limit. Enable overage billing to continue generating sequences.
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Overage Pricing</h4>
                <p className="text-blue-700 text-sm">
                  <strong>${usageStats.overageRate} per additional sequence</strong>
                </p>
                <p className="text-blue-700 text-sm mt-1">
                  You'll only pay for what you use beyond your plan limit. 
                  Overage charges are billed at the end of your monthly cycle.
                </p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowOverageConsent(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleOverageConsent}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                Enable Overage
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default UsageDashboard