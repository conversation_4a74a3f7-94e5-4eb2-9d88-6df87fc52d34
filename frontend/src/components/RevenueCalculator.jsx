import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp, DollarSign, Calculator, Target } from 'lucide-react'

const RevenueCalculator = () => {
  const [inputs, setInputs] = useState({
    emailList: 5000,
    currentConversion: 2.5,
    averageOrderValue: 100,
    emailsPerMonth: 4
  })

  const [results, setResults] = useState({
    currentRevenue: 0,
    sequenceAIRevenue: 0,
    revenueIncrease: 0,
    roiMultiple: 0,
    yearlyIncrease: 0
  })

  useEffect(() => {
    calculateRevenue()
  }, [inputs])

  const calculateRevenue = () => {
    const { emailList, currentConversion, averageOrderValue, emailsPerMonth } = inputs
    
    // Current revenue calculation
    const currentMonthlyRevenue = (emailList * emailsPerMonth * (currentConversion / 100) * averageOrderValue)
    
    // NeuroColony improved conversion (conservative 340% increase)
    const improvedConversion = currentConversion * 3.4 // 340% increase
    const sequenceAIMonthlyRevenue = (emailList * emailsPerMonth * (improvedConversion / 100) * averageOrderValue)
    
    const monthlyIncrease = sequenceAIMonthlyRevenue - currentMonthlyRevenue
    const yearlyIncrease = monthlyIncrease * 12
    const roiMultiple = sequenceAIMonthlyRevenue / currentMonthlyRevenue

    setResults({
      currentRevenue: currentMonthlyRevenue,
      sequenceAIRevenue: sequenceAIMonthlyRevenue,
      revenueIncrease: monthlyIncrease,
      roiMultiple: roiMultiple,
      yearlyIncrease: yearlyIncrease
    })
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  return (
    <div className="py-16">
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-6">
          Calculate Your <span className="gradient-text">Revenue Potential</span>
        </h2>
        <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
          See exactly how much more money you'll make with NeuroColony's proven 340% conversion increase
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
        {/* Calculator Inputs */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="card"
        >
          <div className="flex items-center mb-6">
            <Calculator className="w-6 h-6 text-purple-400 mr-3" />
            <h3 className="text-2xl font-bold text-white">Your Current Metrics</h3>
          </div>

          <div className="space-y-6">
            <div>
              <label className="form-label">Email List Size</label>
              <input
                type="range"
                min="1000"
                max="100000"
                step="1000"
                value={inputs.emailList}
                onChange={(e) => setInputs({...inputs, emailList: parseInt(e.target.value)})}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-neutral-400 mt-1">
                <span>1K</span>
                <span className="font-bold text-purple-400">{formatNumber(inputs.emailList)} subscribers</span>
                <span>100K</span>
              </div>
            </div>

            <div>
              <label className="form-label">Current Conversion Rate (%)</label>
              <input
                type="range"
                min="0.5"
                max="10"
                step="0.1"
                value={inputs.currentConversion}
                onChange={(e) => setInputs({...inputs, currentConversion: parseFloat(e.target.value)})}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-neutral-400 mt-1">
                <span>0.5%</span>
                <span className="font-bold text-purple-400">{inputs.currentConversion}%</span>
                <span>10%</span>
              </div>
            </div>

            <div>
              <label className="form-label">Average Order Value ($)</label>
              <input
                type="range"
                min="10"
                max="1000"
                step="10"
                value={inputs.averageOrderValue}
                onChange={(e) => setInputs({...inputs, averageOrderValue: parseInt(e.target.value)})}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-neutral-400 mt-1">
                <span>$10</span>
                <span className="font-bold text-purple-400">${inputs.averageOrderValue}</span>
                <span>$1,000</span>
              </div>
            </div>

            <div>
              <label className="form-label">Emails Sent Per Month</label>
              <input
                type="range"
                min="1"
                max="20"
                step="1"
                value={inputs.emailsPerMonth}
                onChange={(e) => setInputs({...inputs, emailsPerMonth: parseInt(e.target.value)})}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-neutral-400 mt-1">
                <span>1</span>
                <span className="font-bold text-purple-400">{inputs.emailsPerMonth} emails</span>
                <span>20</span>
              </div>
            </div>
          </div>

          {/* Industry Benchmarks */}
          <div className="mt-8 p-4 bg-neutral-800/50 rounded-lg">
            <h4 className="font-semibold text-white mb-2"> Industry Benchmarks</h4>
            <div className="text-sm text-neutral-400 space-y-1">
              <p>• E-commerce: 1-3% conversion rate</p>
              <p>• SaaS: 2-5% conversion rate</p>
              <p>• Services: 1-4% conversion rate</p>
              <p>• NeuroColony users: 5-15% conversion rate</p>
            </div>
          </div>
        </motion.div>

        {/* Results */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          {/* Current vs NeuroColony */}
          <div className="card">
            <div className="flex items-center mb-6">
              <TrendingUp className="w-6 h-6 text-green-400 mr-3" />
              <h3 className="text-2xl font-bold text-white">Revenue Projection</h3>
            </div>

            <div className="space-y-6">
              {/* Current Revenue */}
              <div className="text-center p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                <div className="text-sm text-neutral-400 mb-1">Current Monthly Revenue</div>
                <div className="text-2xl font-bold text-red-400">{formatCurrency(results.currentRevenue)}</div>
              </div>

              {/* Arrow */}
              <div className="text-center">
                <div className="text-3xl">⬇️</div>
                <div className="text-sm text-green-400 font-medium">340% Conversion Increase</div>
              </div>

              {/* NeuroColony Revenue */}
              <div className="text-center p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <div className="text-sm text-neutral-400 mb-1">With NeuroColony</div>
                <div className="text-3xl font-bold text-green-400">{formatCurrency(results.sequenceAIRevenue)}</div>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="card text-center">
              <DollarSign className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-sm text-neutral-400">Monthly Increase</div>
              <div className="text-xl font-bold text-green-400">{formatCurrency(results.revenueIncrease)}</div>
            </div>

            <div className="card text-center">
              <Target className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-sm text-neutral-400">ROI Multiple</div>
              <div className="text-xl font-bold text-purple-400">{results.roiMultiple.toFixed(1)}x</div>
            </div>
          </div>

          {/* Yearly Projection */}
          <div className="card bg-gradient-to-r from-purple-500/20 to-green-500/20 border-glow">
            <div className="text-center">
              <h4 className="text-lg font-bold text-white mb-2"> Your Yearly Revenue Increase</h4>
              <div className="text-4xl font-bold text-green-400 mb-2">{formatCurrency(results.yearlyIncrease)}</div>
              <div className="text-neutral-300">Additional revenue per year with NeuroColony</div>
            </div>

            {/* ROI vs Cost */}
            <div className="mt-6 p-4 bg-neutral-800/50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-neutral-400">NeuroColony Cost (Business Plan):</span>
                <span className="text-white font-bold">$1,188/year</span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-neutral-400">Your Revenue Increase:</span>
                <span className="text-green-400 font-bold">{formatCurrency(results.yearlyIncrease)}/year</span>
              </div>
              <div className="border-t border-neutral-600 mt-2 pt-2">
                <div className="flex justify-between items-center">
                  <span className="text-white font-bold">Net Profit:</span>
                  <span className="text-green-400 font-bold text-xl">
                    {formatCurrency(results.yearlyIncrease - 1188)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <a
              href="/register?promo=40OFF&calculator=true"
              className="btn btn-primary text-lg px-8 py-4 pulse-animation"
            >
               Start Making {formatCurrency(results.revenueIncrease)}/Month
            </a>
            <p className="text-sm text-neutral-400 mt-2">
              Professional platform • Cancel anytime
            </p>
          </div>
        </motion.div>
      </div>

      {/* Success Stories */}
      <div className="mt-16 text-center">
        <h3 className="text-2xl font-bold text-white mb-8">
          Real Results from Real Customers
        </h3>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="card text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">$127K</div>
            <div className="text-neutral-300 mb-2">Monthly Revenue Increase</div>
            <div className="text-sm text-neutral-400">"Sarah, E-commerce Store"</div>
          </div>
          
          <div className="card text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">$89K</div>
            <div className="text-neutral-300 mb-2">First Quarter Increase</div>
            <div className="text-sm text-neutral-400">"Marcus, SaaS Founder"</div>
          </div>
          
          <div className="card text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">$245K</div>
            <div className="text-neutral-300 mb-2">Annual Revenue Boost</div>
            <div className="text-sm text-neutral-400">"Emily, Digital Agency"</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RevenueCalculator