import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  AlertTriangle, 
  Bell, 
  X, 
  Zap, 
  DollarSign,
  ArrowUpRight,
  CheckCircle
} from 'lucide-react'
import { Link } from 'react-router-dom'
import toast from 'react-hot-toast'

const UsageNotification = ({ user }) => {
  const [notifications, setNotifications] = useState([])
  const [dismissedNotifications, setDismissedNotifications] = useState(new Set())

  useEffect(() => {
    if (user) {
      checkUsageNotifications()
    }
  }, [user])

  const checkUsageNotifications = async () => {
    try {
      const response = await fetch('/api/usage/stats', {
        headers: { 
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        const stats = data.data
        const newNotifications = []

        // Check for usage warnings
        if (stats.status === 'warning' && stats.usagePercentage >= 80) {
          newNotifications.push({
            id: 'usage_warning',
            type: 'warning',
            title: 'Approaching Usage Limit',
            message: `You've used ${stats.usagePercentage}% of your monthly sequences (${stats.sequencesGenerated}/${stats.sequencesLimit})`,
            stats,
            priority: 'medium'
          })
        }

        // Check for critical usage
        if (stats.status === 'critical' && stats.usagePercentage >= 95) {
          newNotifications.push({
            id: 'usage_critical',
            type: 'critical',
            title: 'Critical Usage Alert',
            message: `You're very close to your limit! ${stats.sequencesGenerated}/${stats.sequencesLimit} sequences used`,
            stats,
            priority: 'high'
          })
        }

        // Check for limit reached
        if (stats.sequencesGenerated >= stats.sequencesLimit) {
          if (stats.canGoOverage && !user?.usage?.notifications?.overageConsentGiven) {
            newNotifications.push({
              id: 'overage_consent',
              type: 'overage',
              title: 'Usage Limit Reached',
              message: `Enable overage billing to continue at $${stats.overageRate} per sequence`,
              stats,
              priority: 'high'
            })
          } else if (!stats.canGoOverage) {
            newNotifications.push({
              id: 'upgrade_required',
              type: 'upgrade',
              title: 'Upgrade Required',
              message: 'You\'ve reached your monthly limit. Upgrade to continue generating sequences.',
              stats,
              priority: 'high'
            })
          }
        }

        // Filter out dismissed notifications
        const filteredNotifications = newNotifications.filter(
          notification => !dismissedNotifications.has(notification.id)
        )

        setNotifications(filteredNotifications)
      }

    } catch (error) {
      console.error('Failed to check usage notifications:', error)
    }
  }

  const dismissNotification = (notificationId) => {
    setDismissedNotifications(prev => new Set([...prev, notificationId]))
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  const handleOverageConsent = async () => {
    try {
      const response = await fetch('/api/usage/overage-consent', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Overage billing enabled! You can now continue generating sequences.')
        dismissNotification('overage_consent')
        // Refresh the page to update user state
        window.location.reload()
      } else {
        toast.error(data.message || 'Failed to enable overage billing')
      }

    } catch (error) {
      console.error('Overage consent error:', error)
      toast.error('Failed to enable overage billing')
    }
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <Bell className="h-5 w-5 text-yellow-500" />
      case 'overage':
        return <DollarSign className="h-5 w-5 text-blue-500" />
      case 'upgrade':
        return <ArrowUpRight className="h-5 w-5 text-purple-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const getNotificationColors = (type) => {
    switch (type) {
      case 'critical':
        return 'border-red-200 bg-red-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'overage':
        return 'border-blue-200 bg-blue-50'
      case 'upgrade':
        return 'border-purple-200 bg-purple-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  if (notifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-20 right-4 z-50 space-y-3 max-w-sm">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            className={`border rounded-lg p-4 shadow-lg backdrop-blur-sm ${getNotificationColors(notification.type)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-gray-900 mb-1">
                    {notification.title}
                  </h4>
                  <p className="text-sm text-gray-700 mb-3">
                    {notification.message}
                  </p>
                  
                  {/* Action buttons */}
                  <div className="flex items-center space-x-2">
                    {notification.type === 'overage' && (
                      <button
                        onClick={handleOverageConsent}
                        className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1.5 rounded font-medium transition-colors"
                      >
                        Enable Overage
                      </button>
                    )}
                    
                    {notification.type === 'upgrade' && (
                      <Link
                        to="/pricing"
                        className="bg-purple-600 hover:bg-purple-700 text-white text-xs px-3 py-1.5 rounded font-medium transition-colors"
                      >
                        Upgrade Plan
                      </Link>
                    )}
                    
                    {(notification.type === 'warning' || notification.type === 'critical') && (
                      <Link
                        to="/dashboard"
                        className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1.5 rounded font-medium transition-colors"
                      >
                        View Usage
                      </Link>
                    )}
                  </div>
                </div>
              </div>
              
              <button
                onClick={() => dismissNotification(notification.id)}
                className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

export default UsageNotification