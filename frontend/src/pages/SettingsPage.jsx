import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  User, 
  Mail, 
  Bell, 
  Shield, 
  Key, 
  Palette, 
  Globe,
  Database,
  Download,
  Upload,
  Trash2,
  Save,
  Eye,
  EyeOff,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import AnimatedBackground from '../components/AnimatedBackground'

const SettingsPage = () => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [settings, setSettings] = useState({
    profile: {
      name: user?.name || '',
      email: user?.email || '',
      company: '',
      timezone: 'UTC-8',
      avatar: null
    },
    notifications: {
      email: true,
      usage: true,
      billing: true,
      marketing: false,
      security: true
    },
    security: {
      twoFactor: false,
      loginAlerts: true,
      dataEncryption: true
    },
    preferences: {
      theme: 'dark',
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      autoSave: true
    }
  })

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Palette },
    { id: 'data', label: 'Data & Privacy', icon: Database }
  ]

  const handleSave = () => {
    // Save settings logic
    console.log('Saving settings:', settings)
  }

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Profile Information</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">Full Name</label>
            <input
              type="text"
              className="form-input"
              value={settings.profile.name}
              onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
            />
          </div>
          <div>
            <label className="form-label">Email Address</label>
            <input
              type="email"
              className="form-input"
              value={settings.profile.email}
              onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
            />
          </div>
          <div>
            <label className="form-label">Company</label>
            <input
              type="text"
              className="form-input"
              value={settings.profile.company}
              onChange={(e) => handleSettingChange('profile', 'company', e.target.value)}
              placeholder="Your company name"
            />
          </div>
          <div>
            <label className="form-label">Timezone</label>
            <select
              className="form-input"
              value={settings.profile.timezone}
              onChange={(e) => handleSettingChange('profile', 'timezone', e.target.value)}
            >
              <option value="UTC-8">Pacific Time (UTC-8)</option>
              <option value="UTC-5">Eastern Time (UTC-5)</option>
              <option value="UTC+0">UTC</option>
              <option value="UTC+1">Central European Time (UTC+1)</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Profile Picture</h3>
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-amber-400 rounded-full flex items-center justify-center text-white text-2xl font-bold">
            {settings.profile.name.charAt(0).toUpperCase()}
          </div>
          <div>
            <button className="btn btn-outline mb-2">
              <Upload className="w-4 h-4 mr-2" />
              Upload New
            </button>
            <p className="text-sm text-neutral-400">JPG, PNG up to 5MB</p>
          </div>
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Email Notifications</h3>
        <div className="space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-4 bg-neutral-800/30 rounded-lg">
              <div>
                <div className="text-white font-medium capitalize">
                  {key === 'email' ? 'General Updates' : 
                   key === 'usage' ? 'Usage Alerts' :
                   key === 'billing' ? 'Billing Notifications' :
                   key === 'marketing' ? 'Marketing & News' : 
                   'Security Alerts'}
                </div>
                <div className="text-sm text-neutral-400">
                  {key === 'email' ? 'Product updates and announcements' :
                   key === 'usage' ? 'When approaching usage limits' :
                   key === 'billing' ? 'Invoices and payment updates' :
                   key === 'marketing' ? 'Tips, tutorials, and special offers' :
                   'Login alerts and security notifications'}
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={value}
                  onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
                />
                <div className="w-11 h-6 bg-neutral-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Authentication</h3>
        <div className="space-y-4">
          <div className="p-4 bg-neutral-800/30 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-white font-medium">Two-Factor Authentication</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                settings.security.twoFactor ? 'bg-green-500/20 text-green-300' : 'bg-neutral-600/20 text-neutral-400'
              }`}>
                {settings.security.twoFactor ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <p className="text-sm text-neutral-400 mb-3">
              Add an extra layer of security to your account
            </p>
            <button className="btn btn-outline">
              {settings.security.twoFactor ? 'Disable 2FA' : 'Enable 2FA'}
            </button>
          </div>

          <div className="p-4 bg-neutral-800/30 rounded-lg">
            <div className="text-white font-medium mb-2">Change Password</div>
            <div className="grid gap-4">
              <div>
                <label className="form-label">Current Password</label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="form-input pr-10"
                    placeholder="Enter current password"
                  />
                  <button
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-white"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
              <div>
                <label className="form-label">New Password</label>
                <input
                  type="password"
                  className="form-input"
                  placeholder="Enter new password"
                />
              </div>
              <button className="btn btn-primary w-fit">Update Password</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderDataSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Data Management</h3>
        <div className="space-y-4">
          <div className="p-4 bg-neutral-800/30 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <Download className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Export Data</span>
            </div>
            <p className="text-sm text-neutral-400 mb-3">
              Download all your sequences, templates, and account data
            </p>
            <button className="btn btn-outline">Export My Data</button>
          </div>

          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              <span className="text-red-300 font-medium">Delete Account</span>
            </div>
            <p className="text-sm text-red-200 mb-3">
              Permanently delete your account and all associated data. This action cannot be undone.
            </p>
            <button className="btn bg-red-600 hover:bg-red-700 text-white">Delete Account</button>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 to-neutral-900">
      <AnimatedBackground />
      
      <div className="relative">
        {/* Header */}
        <div className="pt-24 pb-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl font-bold mb-4">
                <span className="gradient-text">Account Settings</span>
              </h1>
              <p className="text-xl text-neutral-300">
                Manage your account, preferences, and security settings
              </p>
            </motion.div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
          <div className="grid lg:grid-cols-4 gap-8">
            
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="card-premium"
              >
                <nav className="space-y-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                          activeTab === tab.id
                            ? 'bg-purple-500/20 text-purple-300 border-purple-500/30'
                            : 'text-neutral-300 hover:bg-neutral-800/50 hover:text-white'
                        }`}
                      >
                        <Icon className="w-5 h-5" />
                        {tab.label}
                      </button>
                    )
                  })}
                </nav>
              </motion.div>
            </div>

            {/* Content Area */}
            <div className="lg:col-span-3">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="card-premium"
              >
                {activeTab === 'profile' && renderProfileSettings()}
                {activeTab === 'notifications' && renderNotificationSettings()}
                {activeTab === 'security' && renderSecuritySettings()}
                {activeTab === 'data' && renderDataSettings()}
                {activeTab === 'preferences' && (
                  <div className="text-center text-neutral-400 py-12">
                    Preferences settings coming soon
                  </div>
                )}

                {/* Save Button */}
                <div className="mt-8 pt-6 border-t border-neutral-700 flex justify-end">
                  <button 
                    onClick={handleSave}
                    className="btn-premium ripple flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    Save Changes
                  </button>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage