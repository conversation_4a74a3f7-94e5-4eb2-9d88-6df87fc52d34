import React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Cpu, 
  Network, 
  Zap, 
  Activity,
  Database,
  Shield,
  Sparkles
} from 'lucide-react';

const ThemeShowcase = () => {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Neural Background Pattern */}
      <div className="neural-bg"></div>
      <div className="hexagon-pattern"></div>
      
      {/* Neural Particles */}
      <div className="neural-particle"></div>
      <div className="neural-particle"></div>
      <div className="neural-particle"></div>
      
      <div className="relative z-10 p-8">
        {/* Header */}
        <header className="text-center mb-16">
          <motion.h1 
            className="text-6xl font-bold text-gradient-neural mb-4"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            NeuroColony Theme Showcase
          </motion.h1>
          <motion.p 
            className="text-xl text-gray-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Experience the power of neural network aesthetics
          </motion.p>
        </header>

        {/* Button Showcase */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-purple">Buttons</h2>
          <div className="flex flex-wrap gap-4">
            <button className="btn-primary">
              Primary Button
            </button>
            <button className="btn-secondary">
              Secondary Button
            </button>
            <button className="btn-colony">
              <Zap className="w-5 h-5" />
              Colony Button
            </button>
            <button className="btn-colony shine-effect">
              <Sparkles className="w-5 h-5" />
              Shine Effect
            </button>
          </div>
        </section>

        {/* Card Showcase */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-pink">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div 
              className="neural-card p-6"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Brain className="w-12 h-12 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Neural Card</h3>
              <p className="text-gray-300">
                Experience the power of AI-driven design with neural network patterns.
              </p>
            </motion.div>

            <motion.div 
              className="glass-card p-6"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Network className="w-12 h-12 text-pink-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Glass Morphism</h3>
              <p className="text-gray-300">
                Elegant glass effect with backdrop blur and subtle borders.
              </p>
            </motion.div>

            <motion.div 
              className="hex-card p-6 neural-pulse"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Cpu className="w-12 h-12 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Hexagonal Design</h3>
              <p className="text-gray-300">
                Colony-inspired hexagonal patterns with neural pulse effects.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Status Indicators */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-purple">Status Indicators</h2>
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-3">
              <div className="status-online"></div>
              <span className="text-gray-300">Online</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="status-processing"></div>
              <span className="text-gray-300">Processing</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="neural-loader"></div>
              <span className="text-gray-300">Loading</span>
            </div>
          </div>
        </section>

        {/* Form Elements */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-pink">Form Elements</h2>
          <div className="max-w-md space-y-4">
            <input 
              type="text" 
              placeholder="Neural input field" 
              className="neural-input w-full"
            />
            <textarea 
              placeholder="Neural textarea with glass effect" 
              className="neural-input w-full h-32"
              rows="4"
            />
            <select className="neural-input w-full">
              <option>Select an option</option>
              <option>Neural Network</option>
              <option>Colony Intelligence</option>
              <option>Swarm Computing</option>
            </select>
          </div>
        </section>

        {/* Agent Cards */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-purple">Agent Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { icon: Brain, title: "Neural Agent", status: "Active" },
              { icon: Database, title: "Data Processor", status: "Processing" },
              { icon: Shield, title: "Security Guard", status: "Monitoring" },
              { icon: Activity, title: "Performance Monitor", status: "Analyzing" }
            ].map((agent, index) => (
              <motion.div 
                key={index}
                className="agent-card p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <agent.icon className="w-10 h-10 text-purple-400 mb-4" />
                <h4 className="text-lg font-bold text-white mb-2">{agent.title}</h4>
                <p className="text-sm text-gray-400 mb-4">{agent.status}</p>
                <div className="flex items-center gap-2">
                  <div className="status-online"></div>
                  <span className="text-xs text-gray-500">Neural Activity</span>
                </div>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Command Center Grid */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-pink">Command Center</h2>
          <div className="command-grid">
            <div className="glass-card p-8 text-center">
              <div className="text-5xl font-bold text-purple-400 mb-2">2,847</div>
              <div className="text-gray-400">Active Neurons</div>
            </div>
            <div className="glass-card p-8 text-center">
              <div className="text-5xl font-bold text-pink-400 mb-2">98.7%</div>
              <div className="text-gray-400">Colony Health</div>
            </div>
            <div className="glass-card p-8 text-center">
              <div className="text-5xl font-bold text-purple-400 mb-2">1.2M</div>
              <div className="text-gray-400">Synapses Fired</div>
            </div>
          </div>
        </section>

        {/* Animations Showcase */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-purple">Animations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="glass-card p-6 text-center">
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full neural-pulse"></div>
              <p className="text-gray-300">Neural Pulse</p>
            </div>
            <div className="glass-card p-6 text-center">
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg morph-shape"></div>
              <p className="text-gray-300">Morphing Shape</p>
            </div>
            <div className="glass-card p-6 text-center">
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full energy-field"></div>
              <p className="text-gray-300">Energy Field</p>
            </div>
          </div>
        </section>

        {/* Typography */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-glow-pink">Typography</h2>
          <div className="space-y-4">
            <h1 className="text-5xl font-bold text-white">Heading 1 - Neural Network</h1>
            <h2 className="text-4xl font-bold text-gray-100">Heading 2 - Colony System</h2>
            <h3 className="text-3xl font-bold text-gray-200">Heading 3 - Agent Protocol</h3>
            <p className="text-lg text-gray-300">
              Regular paragraph text with proper contrast for readability. The NeuroColony 
              design system ensures all text meets WCAG AA standards.
            </p>
            <p className="text-gradient-neural text-2xl font-bold">
              Gradient text for special emphasis
            </p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ThemeShowcase;