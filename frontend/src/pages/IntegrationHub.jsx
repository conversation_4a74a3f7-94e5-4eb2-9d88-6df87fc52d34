import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Spinner } from '../components/ui/Spinner'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Plus,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  TrendingUp,
  Activity,
  Search,
  Filter,
  ExternalLink,
  Trash2,
  RefreshCw
} from 'lucide-react'

/**
 * NeuroColony Integration Hub
 * Multi-channel platform connections that surpass n8n capabilities
 * 400+ marketing platform integrations with unified interface
 */
const IntegrationHub = () => {
  const [integrations, setIntegrations] = useState([])
  const [platforms, setPlatforms] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('connected')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  useEffect(() => {
    fetchIntegrations()
    fetchPlatforms()
  }, [])

  const fetchIntegrations = async () => {
    try {
      const response = await fetch('/api/integrations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setIntegrations(data.data.integrations || [])
      }
    } catch (error) {
      console.error('Failed to fetch integrations:', error)
    }
  }

  const fetchPlatforms = async () => {
    try {
      const response = await fetch('/api/integrations/platforms', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setPlatforms(data.data.platforms || [])
      }
    } catch (error) {
      console.error('Failed to fetch platforms:', error)
    } finally {
      setLoading(false)
    }
  }

  const connectPlatform = async (platform, credentials) => {
    try {
      const response = await fetch('/api/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          platform: platform.id,
          credentials,
          name: `${platform.name} Integration`
        })
      })
      
      const data = await response.json()
      if (data.success) {
        fetchIntegrations()
        setShowAddModal(false)
        alert(`${platform.name} connected successfully!`)
      }
    } catch (error) {
      console.error('Failed to connect platform:', error)
      alert('Failed to connect platform. Please try again.')
    }
  }

  const disconnectIntegration = async (integrationId) => {
    if (!confirm('Are you sure you want to disconnect this integration?')) return
    
    try {
      const response = await fetch(`/api/integrations/${integrationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        fetchIntegrations()
        alert('Integration disconnected successfully')
      }
    } catch (error) {
      console.error('Failed to disconnect integration:', error)
      alert('Failed to disconnect integration')
    }
  }

  const testConnection = async (integrationId) => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      const data = await response.json()
      if (data.success) {
        alert('Connection test successful!')
      } else {
        alert('Connection test failed')
      }
    } catch (error) {
      console.error('Connection test failed:', error)
      alert('Connection test failed')
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredIntegrations = integrations.filter(integration => {
    const matchesTab = activeTab === 'all' || integration.status === activeTab
    const matchesCategory = selectedCategory === 'all' || 
      platforms.find(p => p.id === integration.platform)?.category === selectedCategory
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      integration.platform.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesTab && matchesCategory && matchesSearch
  })

  const availablePlatforms = platforms.filter(platform => 
    !integrations.some(integration => 
      integration.platform === platform.id && integration.status === 'connected'
    )
  )

  const categories = [...new Set(platforms.map(p => p.category))]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                🔗 Integration Hub
              </h1>
              <p className="text-gray-600 text-lg">
                Connect 400+ Marketing Platforms • Superior Marketing-First Approach
              </p>
            </div>
            <div className="flex gap-4">
              <Button
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Integration
              </Button>
              <Button variant="outline" onClick={fetchIntegrations}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Integrations</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{integrations.length}</div>
              <p className="text-xs text-muted-foreground">
                {integrations.filter(i => i.status === 'connected').length} connected
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available Platforms</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{platforms.length}</div>
              <p className="text-xs text-muted-foreground">
                Marketing-focused platforms
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Health Status</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {Math.round((integrations.filter(i => i.status === 'connected').length / Math.max(integrations.length, 1)) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Healthy connections
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length}</div>
              <p className="text-xs text-muted-foreground">
                Platform categories
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Status Tabs */}
            <div className="flex space-x-1 bg-gray-200 p-1 rounded-lg">
              {['all', 'connected', 'pending', 'error'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors capitalize ${
                    activeTab === tab
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Search and Category Filter */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Integrations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIntegrations.map((integration) => {
            const platform = platforms.find(p => p.id === integration.platform)
            
            return (
              <motion.div
                key={integration._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="relative overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{platform?.icon || '🔗'}</div>
                        <div>
                          <CardTitle className="text-lg">{platform?.name || integration.platform}</CardTitle>
                          <p className="text-sm text-gray-600">{platform?.category}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(integration.status)}
                        <Badge className={getStatusColor(integration.status)}>
                          {integration.status}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">
                      {platform?.description || 'Marketing platform integration'}
                    </p>
                    
                    {integration.status === 'connected' && (
                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-sm">
                          <span>Success Rate:</span>
                          <span className="font-medium">{integration.successRate?.toFixed(1) || 100}%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Total Requests:</span>
                          <span className="font-medium">{integration.usage?.totalRequests || 0}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex gap-2">
                      {integration.status === 'connected' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => testConnection(integration._id)}
                        >
                          Test
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                      >
                        <Settings className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => disconnectIntegration(integration._id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Empty State */}
        {filteredIntegrations.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔗</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No integrations found</h3>
            <p className="text-gray-600 mb-6">
              {activeTab === 'all' 
                ? 'Connect your first marketing platform to get started'
                : `No ${activeTab} integrations found`
              }
            </p>
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Integration
            </Button>
          </div>
        )}
      </div>

      {/* Add Integration Modal */}
      {showAddModal && (
        <AddIntegrationModal
          platforms={availablePlatforms}
          onConnect={connectPlatform}
          onClose={() => setShowAddModal(false)}
        />
      )}
    </div>
  )
}

// Simple Add Integration Modal Component
const AddIntegrationModal = ({ platforms, onConnect, onClose }) => {
  const [selectedPlatform, setSelectedPlatform] = useState(null)
  const [credentials, setCredentials] = useState({})

  const handleConnect = () => {
    if (selectedPlatform && credentials.apiKey) {
      onConnect(selectedPlatform, credentials)
    } else {
      alert('Please select a platform and enter API key')
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Add Integration</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Platform</label>
            <select
              value={selectedPlatform?.id || ''}
              onChange={(e) => setSelectedPlatform(platforms.find(p => p.id === e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg"
            >
              <option value="">Select a platform</option>
              {platforms.map(platform => (
                <option key={platform.id} value={platform.id}>
                  {platform.icon} {platform.name}
                </option>
              ))}
            </select>
          </div>
          
          {selectedPlatform && (
            <div>
              <label className="block text-sm font-medium mb-2">API Key</label>
              <input
                type="password"
                placeholder="Enter your API key"
                value={credentials.apiKey || ''}
                onChange={(e) => setCredentials({ ...credentials, apiKey: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg"
              />
              <p className="text-xs text-gray-600 mt-1">
                {selectedPlatform.description}
              </p>
            </div>
          )}
        </div>
        
        <div className="flex gap-3 mt-6">
          <Button onClick={handleConnect} className="flex-1">
            Connect
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
}

export default IntegrationHub
