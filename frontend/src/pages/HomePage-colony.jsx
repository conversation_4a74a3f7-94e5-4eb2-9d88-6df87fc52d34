import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Bot, 
  Layers, 
  Network, 
  Zap, 
  Shield, 
  Users, 
  TrendingUp,
  ChevronRight,
  Play,
  Check,
  Star,
  ArrowRight,
  Sparkles,
  GitBranch,
  Activity,
  Database,
  Cloud,
  Lock,
  BarChart,
  Target,
  Palette,
  Globe,
  Building2,
  Rocket
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { Icons } from '../design-system/icons.jsx';
import { colors } from '../design-system/colors';
import AnimatedBackground from '../components/AnimatedBackground';

// Animated colony visualization component
const ColonyVisualization = () => {
  return (
    <div className="relative w-full h-full">
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 800 600">
        {/* Neural network connections */}
        <defs>
          <linearGradient id="neural-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={colors.neural[400]} stopOpacity="0.2" />
            <stop offset="100%" stopColor={colors.network[400]} stopOpacity="0.2" />
          </linearGradient>
        </defs>
        
        {/* Animated connections */}
        <g className="animate-pulse">
          <line x1="400" y1="100" x2="200" y2="250" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="400" y1="100" x2="600" y2="250" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="200" y1="250" x2="100" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="200" y1="250" x2="300" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="600" y1="250" x2="500" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="600" y1="250" x2="700" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
        </g>
        
        {/* Queen agent */}
        <motion.g
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [1, 0.8, 1]
          }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <circle cx="400" cy="100" r="40" fill={colors.colony[500]} opacity="0.2" />
          <circle cx="400" cy="100" r="30" fill={colors.colony[500]} opacity="0.4" />
          <foreignObject x="380" y="80" width="40" height="40">
            <Icons.QueenAgent size={40} color={colors.colony[400]} />
          </foreignObject>
        </motion.g>
        
        {/* Worker agents */}
        <motion.g
          animate={{ 
            rotate: [0, 360],
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <circle cx="200" cy="250" r="25" fill={colors.neural[500]} opacity="0.3" />
          <foreignObject x="185" y="235" width="30" height="30">
            <Icons.WorkerAgent size={30} color={colors.neural[400]} />
          </foreignObject>
        </motion.g>
        
        <motion.g
          animate={{ 
            rotate: [0, -360],
          }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        >
          <circle cx="600" cy="250" r="25" fill={colors.neural[500]} opacity="0.3" />
          <foreignObject x="585" y="235" width="30" height="30">
            <Icons.WorkerAgent size={30} color={colors.neural[400]} />
          </foreignObject>
        </motion.g>
        
        {/* Scout agents */}
        {[
          { x: 100, y: 400 },
          { x: 300, y: 400 },
          { x: 500, y: 400 },
          { x: 700, y: 400 }
        ].map((pos, i) => (
          <motion.g
            key={i}
            animate={{ 
              y: [0, -10, 0],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{ duration: 2, delay: i * 0.5, repeat: Infinity }}
          >
            <circle cx={pos.x} cy={pos.y} r="20" fill={colors.network[500]} opacity="0.3" />
            <foreignObject x={pos.x - 12} y={pos.y - 12} width="24" height="24">
              <Icons.ScoutAgent size={24} color={colors.network[400]} />
            </foreignObject>
          </motion.g>
        ))}
      </svg>
    </div>
  );
};

const HomePage = () => {
  const navigate = useNavigate();
  const [activeFeature, setActiveFeature] = useState(0);

  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: Icons.Colony,
      title: "AI Colony Architecture",
      description: "Deploy intelligent agent colonies that work together like a digital hive mind",
      color: "neural"
    },
    {
      icon: Icons.NeuralNetwork,
      title: "Neural Workflows",
      description: "Design complex workflows with agents that learn and adapt to your needs",
      color: "network"
    },
    {
      icon: Icons.Hive,
      title: "Colony Templates",
      description: "Pre-built agent colonies for any task - from data analysis to creative generation",
      color: "honey"
    },
    {
      icon: Icons.Performance,
      title: "Real-time Intelligence",
      description: "Monitor your colony's performance with advanced analytics and insights",
      color: "organic"
    }
  ];

  const useCases = [
    {
      title: "Data Intelligence Colony",
      description: "Deploy a team of data analyst agents to mine insights from your business data",
      agents: 12,
      icon: BarChart,
      gradient: "from-neural-600 to-neural-700"
    },
    {
      title: "Creative Studio Hive",
      description: "AI agents that collaborate to generate content, designs, and creative assets",
      agents: 8,
      icon: Palette,
      gradient: "from-honey-600 to-honey-700"
    },
    {
      title: "DevOps Automation Swarm",
      description: "Automate your entire development pipeline with intelligent agent workflows",
      agents: 15,
      icon: GitBranch,
      gradient: "from-network-600 to-network-700"
    },
    {
      title: "Customer Success Colony",
      description: "AI agents that handle support, engagement, and customer satisfaction",
      agents: 10,
      icon: Users,
      gradient: "from-organic-600 to-organic-700"
    }
  ];

  const stats = [
    { value: "10M+", label: "Tasks Completed", icon: Zap },
    { value: "50K+", label: "Active Colonies", icon: Icons.Hive },
    { value: "99.9%", label: "Uptime", icon: Shield },
    { value: "4.9/5", label: "User Rating", icon: Star }
  ];

  return (
    <div className="min-h-screen bg-system-background overflow-x-hidden">
      <AnimatedBackground variant="network" />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-32 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Badge variant="primary" className="mb-4 inline-flex items-center gap-2">
                <Icons.Active size={16} />
                AI Agent Colony Platform
              </Badge>
              
              <h1 className="text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                Deploy Your Own
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-honey-400 to-neural-400 block">
                  AI Agent Colony
                </span>
              </h1>
              
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Create intelligent AI colonies where specialized agents collaborate like a digital 
                hive mind. From data analysis to creative generation, your agents work together 
                to solve complex problems autonomously.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Button
                  size="lg"
                  onClick={() => navigate('/register')}
                  className="group bg-gradient-to-r from-honey-500 to-honey-600 hover:from-honey-600 hover:to-honey-700"
                >
                  <Icons.Deploy size={20} className="mr-2 group-hover:animate-pulse" />
                  Start Building Colony
                  <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
                
                <Button
                  size="lg"
                  variant="outline"
                  onClick={() => navigate('/templates')}
                  className="group"
                >
                  <Icons.Blueprint size={20} className="mr-2" />
                  Browse Templates
                  <Play size={16} className="ml-2 group-hover:scale-110 transition-transform" />
                </Button>
              </div>
              
              <div className="flex items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <Check size={16} className="text-green-400" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check size={16} className="text-green-400" />
                  <span>5 free colonies</span>
                </div>
              </div>
            </motion.div>
            
            {/* Right Visual */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="relative h-[600px] hidden lg:block"
            >
              <ColonyVisualization />
              
              {/* Floating feature cards */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80"
                >
                  <Card className={`p-6 bg-${features[activeFeature].color}-500/10 border-${features[activeFeature].color}-500/50`}>
                    <features[activeFeature].icon size={32} className={`text-${features[activeFeature].color}-400 mb-3`} />
                    <h3 className="text-lg font-semibold text-white mb-2">{features[activeFeature].title}</h3>
                    <p className="text-sm text-gray-300">{features[activeFeature].description}</p>
                  </Card>
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 border-y border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <stat.icon size={32} className="text-honey-400 mx-auto mb-3" />
                <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
                <div className="text-sm text-gray-400">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <Badge variant="success" className="mb-4">
              <Sparkles size={16} className="mr-1" />
              Popular Colony Types
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">
              Pre-Built Colonies for Every Need
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Deploy specialized AI colonies designed for specific business challenges. 
              Each colony includes pre-configured agents with defined roles and workflows.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-6">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full group hover:shadow-xl transition-all duration-300">
                  <div className={`h-2 bg-gradient-to-r ${useCase.gradient}`} />
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="p-3 bg-gray-800 rounded-lg">
                        <useCase.icon size={24} className="text-white" />
                      </div>
                      <Badge variant="default">
                        <Bot size={14} className="mr-1" />
                        {useCase.agents} agents
                      </Badge>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-honey-400 transition-colors">
                      {useCase.title}
                    </h3>
                    <p className="text-gray-300 mb-4">{useCase.description}</p>
                    <Button variant="ghost" size="sm" className="group-hover:text-honey-400">
                      Deploy Colony
                      <ChevronRight size={16} className="ml-1 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gray-900/50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <Badge variant="primary" className="mb-4">
              <Activity size={16} className="mr-1" />
              How It Works
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">
              Build Your Colony in Minutes
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our platform makes it simple to create, deploy, and manage AI agent colonies
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Choose Your Agents",
                description: "Select from our library of specialized AI agents or create custom ones for your specific needs",
                icon: Icons.WorkerAgent,
                color: "neural"
              },
              {
                step: "02",
                title: "Design Workflows",
                description: "Connect agents in visual workflows, defining how they collaborate and share information",
                icon: Icons.Workflow,
                color: "network"
              },
              {
                step: "03",
                title: "Deploy & Monitor",
                description: "Launch your colony and watch as agents work together autonomously to complete tasks",
                icon: Icons.Deploy,
                color: "honey"
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
              >
                <Card className={`p-8 text-center border-${step.color}-500/30 hover:border-${step.color}-500/50 transition-colors`}>
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-${step.color}-500/20 mb-6`}>
                    <span className={`text-2xl font-bold text-${step.color}-400`}>{step.step}</span>
                  </div>
                  <step.icon size={48} className={`text-${step.color}-400 mx-auto mb-4`} />
                  <h3 className="text-xl font-semibold text-white mb-3">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <Badge variant="warning" className="mb-4">
              <Zap size={16} className="mr-1" />
              Platform Features
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need to Build AI Colonies
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { icon: Brain, title: "Advanced AI Models", description: "Powered by GPT-4, Claude, and custom models" },
              { icon: Shield, title: "Enterprise Security", description: "SOC 2 compliant with end-to-end encryption" },
              { icon: Database, title: "Scalable Infrastructure", description: "Handle millions of tasks with auto-scaling" },
              { icon: GitBranch, title: "Version Control", description: "Track changes and rollback colony configurations" },
              { icon: Globe, title: "Global Deployment", description: "Deploy colonies across multiple regions" },
              { icon: Lock, title: "Access Control", description: "Fine-grained permissions for team collaboration" }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="p-6 h-full hover:shadow-lg transition-shadow">
                  <feature.icon size={32} className="text-honey-400 mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Section */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-gray-900/50 to-transparent">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              <Badge variant="error" className="mb-4">
                <Building2 size={16} className="mr-1" />
                Enterprise Ready
              </Badge>
              <h2 className="text-4xl font-bold text-white mb-6">
                Built for Scale and Compliance
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                NeuroColony provides enterprise-grade features for organizations that need 
                reliable, secure, and scalable AI automation.
              </p>
              
              <div className="space-y-4 mb-8">
                {[
                  "99.9% uptime SLA guarantee",
                  "SOC 2 Type II certified",
                  "GDPR and CCPA compliant",
                  "24/7 dedicated support",
                  "Custom AI model training",
                  "On-premise deployment options"
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check size={20} className="text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">{item}</span>
                  </div>
                ))}
              </div>
              
              <Button size="lg" variant="outline" onClick={() => navigate('/contact')}>
                Talk to Sales
                <ArrowRight size={20} className="ml-2" />
              </Button>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="relative"
            >
              <Card className="p-8 bg-gradient-to-br from-neural-600/20 to-neural-700/10">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">500+</div>
                    <div className="text-sm text-gray-400">Enterprise Customers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">10M+</div>
                    <div className="text-sm text-gray-400">Daily Tasks</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">50ms</div>
                    <div className="text-sm text-gray-400">Avg Response Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">99.9%</div>
                    <div className="text-sm text-gray-400">Uptime</div>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <Icons.Hive size={64} className="text-honey-400 mx-auto mb-8" />
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Build Your AI Colony?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of teams using NeuroColony to automate complex workflows 
              with intelligent AI agents.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                onClick={() => navigate('/register')}
                className="group bg-gradient-to-r from-honey-500 to-neural-500 hover:from-honey-600 hover:to-neural-600"
              >
                <Rocket size={20} className="mr-2 group-hover:animate-pulse" />
                Start Free Trial
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                onClick={() => navigate('/templates')}
              >
                View Demo Colony
                <Play size={16} className="ml-2" />
              </Button>
            </div>
            
            <p className="text-sm text-gray-500 mt-6">
              No credit card required • 5 free colonies • Cancel anytime
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;