import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { Plus, Zap } from 'lucide-react'
import DashboardStats from '../components/DashboardStats'
import UsageDashboard from '../components/UsageDashboard'
import UsageIndicator from '../components/UsageIndicator'

const Dashboard = ({ user }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.h1 
            className="text-3xl font-bold text-gray-900"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            Welcome back, {user?.name}! 
          </motion.h1>
          <p className="text-gray-600 mt-2">
            Ready to create some high-converting email sequences?
          </p>
        </div>

        {/* Quick Actions */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h2 className="text-xl font-bold mb-2">Generate Your Next Sequence</h2>
                <p className="text-primary-100 mb-4">
                  Create a high-converting email sequence in under 60 seconds
                </p>
                
                {/* Usage indicator in action bar */}
                <div className="flex items-center">
                  <UsageIndicator user={user} compact={true} />
                </div>
              </div>
              <div className="flex gap-3">
                <Link 
                  to="/templates"
                  className="bg-white/10 text-white hover:bg-white/20 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center"
                >
                  Browse Templates
                </Link>
                <Link 
                  to="/generator"
                  className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center"
                >
                  <Zap className="h-5 w-5 mr-2" />
                  Generate Now
                </Link>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Usage Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <UsageDashboard user={user} />
        </motion.div>

        {/* Dashboard Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <DashboardStats user={user} />
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard