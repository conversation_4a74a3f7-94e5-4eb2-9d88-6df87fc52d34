import { motion } from 'framer-motion'
import { Check, ArrowRight } from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

const PricingPage = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)

  const plans = [
    {
      name: 'Starter Colony',
      price: 29,
      description: 'Perfect for small businesses',
      agentLimit: '5 Active Agents',
      computeHours: '50 hours/month',
      features: [
        '5 AI agents (Worker & Scout types)',
        'Natural language workflow generation',
        'Basic email sequence automation',
        'Social media content creation',
        'Standard AI models (GPT-4)',
        'Community support',
        'Basic analytics dashboard'
      ],
      popular: false,
      badge: null,
      agentTypes: ['Worker Agents', 'Scout Agents'],
      workflows: '10 active workflows'
    },
    {
      name: 'Professional Colony',
      price: 89,
      description: 'For growing teams and agencies',
      agentLimit: '25 Active Agents',
      computeHours: '200 hours/month',
      features: [
        'All Starter features',
        '25 AI agents (including Queen agents)',
        'Advanced workflow orchestration',
        'Competitor monitoring & intelligence',
        'A/B testing and optimization',
        'Premium AI models (Claude 4)',
        'Priority support',
        'Team collaboration tools',
        'Advanced analytics & reporting',
        'API access'
      ],
      popular: true,
      badge: 'Most Popular',
      agentTypes: ['Queen Agents', 'Worker Agents', 'Scout Agents'],
      workflows: '50 active workflows'
    },
    {
      name: 'Enterprise Colony',
      price: 289,
      description: 'For large organizations',
      agentLimit: 'Unlimited Agents',
      computeHours: 'Unlimited',
      features: [
        'Everything in Professional',
        'Unlimited AI agents & workflows',
        'Custom agent development',
        'White-label platform',
        'Advanced security & compliance',
        'Dedicated success manager',
        'SLA guarantees (99.9% uptime)',
        'Custom integrations',
        'On-premise deployment options',
        'Advanced colony intelligence',
        'Custom AI model training'
      ],
      popular: false,
      badge: 'Enterprise',
      agentTypes: ['All Agent Types + Custom'],
      workflows: 'Unlimited workflows'
    },
    {
      name: 'Colony Command',
      price: null,
      description: 'Ultimate AI automation',
      agentLimit: 'Custom Architecture',
      computeHours: 'Dedicated Resources',
      features: [
        'Everything in Enterprise',
        'Custom colony architecture',
        'Dedicated AI infrastructure',
        'Strategic consulting & training',
        'Custom agent species development',
        'Advanced compliance (SOC 2, HIPAA)',
        'Multi-region deployment',
        'Revenue sharing opportunities',
        '24/7 dedicated support',
        'Custom SLA agreements'
      ],
      popular: false,
      badge: 'Custom',
      agentTypes: ['Fully Custom Colony'],
      workflows: 'Enterprise-scale automation'
    }
  ]

  const handlePlanSelect = async (planName) => {
    if (loading) return
    
    setLoading(true)
    
    try {
      if (!user) {
        navigate('/register')
        return
      }

      if (planName === 'Custom') {
        navigate('/contact')
        return
      }

      // Navigate to dashboard after selection
      navigate('/dashboard-working')
    } catch (error) {
      console.error('Plan selection error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-purple-950/20 to-slate-950">
      {/* Cosmic Background Effect */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]" />
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
      </div>

      <div className="relative px-6 py-32">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-6xl font-light text-white mb-6">
              🐜 Colony Intelligence
              <span className="block bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent font-medium">
                Pricing
              </span>
            </h1>
            <p className="text-xl text-slate-300 font-light max-w-3xl mx-auto mb-4">
              Build your AI agent colony that works autonomously 24/7. Superior to traditional workflow platforms with natural language workflow generation and intelligent agent collaboration.
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-slate-400">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>14-day free trial</span>
              </div>
            </div>
          </motion.div>

          {/* Pricing Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative p-6 rounded-2xl border transition-all duration-500 hover:scale-105 ${
                  plan.popular
                    ? 'bg-gradient-to-br from-purple-900/30 to-blue-900/30 border-purple-500/50 shadow-2xl shadow-purple-500/20'
                    : 'bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700/50 hover:border-purple-500/30'
                }`}
              >
                {plan.badge && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      plan.popular
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                        : 'bg-slate-700 text-slate-300'
                    }`}>
                      {plan.badge}
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-medium text-white mb-2">{plan.name}</h3>
                  <p className="text-slate-400 text-sm mb-4">{plan.description}</p>

                  {/* Agent Limits */}
                  <div className="mb-4 p-3 bg-slate-800/50 rounded-lg">
                    <div className="text-sm text-purple-300 font-medium">{plan.agentLimit}</div>
                    <div className="text-xs text-slate-400">{plan.computeHours}</div>
                  </div>

                  <div className="mb-4">
                    {plan.price ? (
                      <div>
                        <span className="text-3xl font-light text-white">${plan.price}</span>
                        <span className="text-slate-400 ml-1 text-sm">/month</span>
                      </div>
                    ) : (
                      <span className="text-2xl font-light text-white">Custom</span>
                    )}
                  </div>

                  {/* Agent Types */}
                  <div className="mb-4">
                    <div className="text-xs text-slate-400 mb-2">Agent Types:</div>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {plan.agentTypes.map((type, i) => (
                        <span key={i} className="text-xs bg-slate-700/50 text-slate-300 px-2 py-1 rounded">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
                      <span className="text-slate-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handlePlanSelect(plan.name)}
                  disabled={loading}
                  className={`w-full py-4 rounded-xl font-medium text-lg transition-all duration-300 flex items-center justify-center gap-3 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-2xl hover:shadow-purple-500/25'
                      : 'border border-slate-600 text-slate-300 hover:bg-slate-800/50 hover:border-purple-500/50'
                  }`}
                >
                  {plan.name === 'Custom' ? 'Contact Sales' : 'Get Started'}
                  <ArrowRight className="w-5 h-5" />
                </button>
              </motion.div>
            ))}
          </div>

          {/* Why Choose NeuroColony Over n8n */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-light text-white mb-4">
                Why NeuroColony Beats Traditional Platforms
              </h2>
              <p className="text-slate-300 max-w-2xl mx-auto">
                See how our AI agent colony platform surpasses traditional workflow automation tools
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {/* NeuroColony Advantages */}
              <div className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 border border-purple-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                  🐜 NeuroColony Colony Intelligence
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                    <span className="text-slate-300 text-sm">
                      <strong>Natural Language Workflow Generation:</strong> Describe what you want in plain English, AI builds the workflow
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                    <span className="text-slate-300 text-sm">
                      <strong>Autonomous Agent Collaboration:</strong> AI agents work together like a colony, no manual coordination needed
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                    <span className="text-slate-300 text-sm">
                      <strong>AI-Native Platform:</strong> Built from the ground up for AI, not retrofitted
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                    <span className="text-slate-300 text-sm">
                      <strong>Cloud-First:</strong> No server management, instant deployment, automatic scaling
                    </span>
                  </li>
                </ul>
              </div>

              {/* n8n Limitations */}
              <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-slate-300 mb-4 flex items-center gap-2">
                  ⚙️ Traditional Workflow Automation
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                    <span className="text-slate-400 text-sm">
                      <strong>Complex Visual Builder:</strong> Requires technical knowledge to create workflows
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                    <span className="text-slate-400 text-sm">
                      <strong>Manual Coordination:</strong> You must manually connect and coordinate all workflow steps
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                    <span className="text-slate-400 text-sm">
                      <strong>Limited AI Integration:</strong> Basic AI capabilities, not AI-native
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                    <span className="text-slate-400 text-sm">
                      <strong>Self-Hosting Complexity:</strong> Requires server management and maintenance
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-20 text-center"
          >
            <h2 className="text-2xl font-light text-white mb-8">
              Questions? We're here to help.
            </h2>
            <button
              onClick={() => navigate('/contact')}
              className="inline-flex items-center gap-3 px-6 py-3 border border-slate-600 text-slate-300 rounded-xl hover:bg-slate-800/50 transition-all duration-300"
            >
              Contact Support
              <ArrowRight className="w-4 h-4" />
            </button>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default PricingPage