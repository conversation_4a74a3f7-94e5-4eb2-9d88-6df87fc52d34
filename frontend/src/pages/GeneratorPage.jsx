import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Zap, Loader2, Download, FileText, Share, Settings } from 'lucide-react'
import toast from 'react-hot-toast'
import { exportSequence, exportFormats } from '../utils/exportUtils'
import UsageIndicator from '../components/UsageIndicator'

const GeneratorPage = ({ user }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    businessInfo: {
      industry: '',
      productService: '',
      targetAudience: '',
      pricePoint: '',
      uniqueSellingProposition: '',
      mainBenefit: '',
      painPoint: ''
    },
    generationSettings: {
      sequenceLength: 7,
      tone: 'professional',
      primaryGoal: 'sales',
      includeCTA: true,
      includePersonalization: true
    }
  })
  
  const [loading, setLoading] = useState(false)
  const [generatedSequence, setGeneratedSequence] = useState(null)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [showExportMenu, setShowExportMenu] = useState(false)

  useEffect(() => {
    // Check if user selected a template from the template library
    const savedTemplate = localStorage.getItem('selectedTemplate')
    if (savedTemplate) {
      try {
        const template = JSON.parse(savedTemplate)
        setSelectedTemplate(template)
        setFormData(prev => ({
          ...prev,
          title: template.name + ' Email Sequence'
        }))
        localStorage.removeItem('selectedTemplate')
        toast.success(`Using template: ${template.name}`)
      } catch (error) {
        console.error('Error loading template:', error)
      }
    }
  }, [])

  const handleExport = (format) => {
    try {
      const result = exportSequence(generatedSequence, format)
      toast.success(`Exported as ${result.filename}`)
      setShowExportMenu(false)
    } catch (error) {
      toast.error('Export failed: ' + error.message)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Check usage permission first
      const token = localStorage.getItem('token')
      const permissionResponse = await fetch(`${import.meta.env.VITE_API_URL}/usage/check-generation`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const permissionData = await permissionResponse.json()

      if (!permissionData.success || !permissionData.data.canGenerate) {
        const stats = permissionData.data?.stats
        
        if (permissionData.data?.requiresOverageConsent) {
          const overageRate = stats?.overageRate || 3
          const confirmOverage = window.confirm(
            `You've reached your monthly limit of ${stats.sequencesLimit} sequences. ` +
            `Enable overage billing to continue at $${overageRate} per additional sequence?`
          )
          
          if (confirmOverage) {
            const consentResponse = await fetch(`${import.meta.env.VITE_API_URL}/usage/overage-consent`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            })
            
            const consentData = await consentResponse.json()
            
            if (!consentData.success) {
              toast.error(consentData.message || 'Failed to enable overage billing')
              setLoading(false)
              return
            }
            
            toast.success('Overage billing enabled! Continuing with generation...')
          } else {
            setLoading(false)
            return
          }
        } else {
          toast.error(permissionData.data?.message || 'Usage limit reached. Please upgrade your plan.')
          setLoading(false)
          return
        }
      }

      // Proceed with generation
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        setGeneratedSequence(data.data)
        
        // Show appropriate success message
        let message = 'Sequence generated successfully!'
        if (data.usage?.isOverage) {
          message += ` Overage charge: $${data.usage.overageCharge}`
        }
        
        toast.success(message)
      } else {
        if (data.data?.requiresOverageConsent) {
          toast.error(data.message + ' Please enable overage billing to continue.')
        } else {
          toast.error(data.message || 'Generation failed')
        }
      }
    } catch (error) {
      console.error('Generation error:', error)
      toast.error('Generation failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (generatedSequence) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg p-8"
          >
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                🎉 Your Email Sequence is Ready!
              </h1>
              <p className="text-gray-600">
                Here's your AI-generated email sequence that's designed to convert.
              </p>
            </div>

            <div className="space-y-6">
              {generatedSequence.emails?.map((email, index) => (
                <div key={index} className="border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">
                      Email {index + 1} - Day {email.dayDelay}
                    </h3>
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">
                      Score: {email.conversionScore || 85}
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">Subject Line:</h4>
                    <p className="text-gray-700 bg-gray-50 p-3 rounded">
                      {email.subject}
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Email Body:</h4>
                    <div className="text-gray-700 bg-gray-50 p-4 rounded whitespace-pre-wrap">
                      {email.body}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  setGeneratedSequence(null)
                  setSelectedTemplate(null)
                  setFormData({
                    title: '',
                    description: '',
                    businessInfo: {
                      industry: '',
                      productService: '',
                      targetAudience: '',
                      pricePoint: '',
                      uniqueSellingProposition: '',
                      mainBenefit: '',
                      painPoint: ''
                    },
                    generationSettings: {
                      sequenceLength: 7,
                      tone: 'professional',
                      primaryGoal: 'sales',
                      includeCTA: true,
                      includePersonalization: true
                    }
                  })
                }}
                className="btn-secondary flex items-center justify-center"
              >
                <Zap className="h-4 w-4 mr-2" />
                Generate Another
              </button>
              
              <div className="relative">
                <button
                  onClick={() => setShowExportMenu(!showExportMenu)}
                  className="btn-primary flex items-center justify-center"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Sequence
                </button>
                
                {showExportMenu && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border py-2 z-10">
                    <div className="px-4 py-2 border-b">
                      <p className="text-sm font-medium text-gray-900">Export Options</p>
                    </div>
                    
                    <div className="py-1">
                      <h4 className="px-4 py-2 text-xs font-medium text-gray-500 uppercase">General Formats</h4>
                      <button
                        onClick={() => handleExport(exportFormats.HTML)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FileText className="h-4 w-4 mr-3" />
                        HTML Document
                      </button>
                      <button
                        onClick={() => handleExport(exportFormats.CSV)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FileText className="h-4 w-4 mr-3" />
                        CSV Spreadsheet
                      </button>
                      <button
                        onClick={() => handleExport(exportFormats.TXT)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FileText className="h-4 w-4 mr-3" />
                        Text File
                      </button>
                      <button
                        onClick={() => handleExport(exportFormats.JSON)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FileText className="h-4 w-4 mr-3" />
                        JSON Data
                      </button>
                    </div>
                    
                    <div className="py-1 border-t">
                      <h4 className="px-4 py-2 text-xs font-medium text-gray-500 uppercase">Email Platforms</h4>
                      <button
                        onClick={() => handleExport(exportFormats.MAILCHIMP)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Share className="h-4 w-4 mr-3" />
                        Email Platform A
                      </button>
                      <button
                        onClick={() => handleExport(exportFormats.KLAVIYO)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Share className="h-4 w-4 mr-3" />
                        Email Platform B
                      </button>
                    </div>
                  </div>
                )}
              </div>
              
              <Link 
                to={`/advanced/${generatedSequence._id}`}
                className="btn-secondary flex items-center justify-center"
              >
                <Settings className="h-4 w-4 mr-2" />
                Advanced Features
              </Link>
              
              <button 
                onClick={() => window.open('/templates', '_blank')}
                className="btn-secondary flex items-center justify-center"
              >
                <FileText className="h-4 w-4 mr-2" />
                Browse Templates
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Generate Your Email Sequence
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            Tell us about your business and we'll create a high-converting email sequence in seconds.
          </p>
          
          {/* Usage Indicator */}
          {user && (
            <div className="flex justify-center mb-6">
              <UsageIndicator user={user} compact={true} />
            </div>
          )}
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="bg-white rounded-lg shadow-lg p-8 space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {/* Basic Info */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sequence Title
              </label>
              <input
                type="text"
                required
                className="input-field"
                placeholder="e.g., Fitness Coaching Welcome Series"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
              />
            </div>
          </div>

          {/* Business Info */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">Business Details</h2>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industry
                </label>
                <select
                  required
                  className="input-field"
                  value={formData.businessInfo.industry}
                  onChange={(e) => setFormData({
                    ...formData,
                    businessInfo: {...formData.businessInfo, industry: e.target.value}
                  })}
                >
                  <option value="">Select Industry</option>
                  <option value="fitness">Fitness & Health</option>
                  <option value="coaching">Coaching & Consulting</option>
                  <option value="ecommerce">E-commerce</option>
                  <option value="saas">SaaS & Technology</option>
                  <option value="realestate">Real Estate</option>
                  <option value="education">Education & Training</option>
                  <option value="finance">Finance & Insurance</option>
                  <option value="other">Other</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Point
                </label>
                <select
                  required
                  className="input-field"
                  value={formData.businessInfo.pricePoint}
                  onChange={(e) => setFormData({
                    ...formData,
                    businessInfo: {...formData.businessInfo, pricePoint: e.target.value}
                  })}
                >
                  <option value="">Select Price Range</option>
                  <option value="under-100">Under $100</option>
                  <option value="100-500">$100 - $500</option>
                  <option value="500-2000">$500 - $2,000</option>
                  <option value="2000-10000">$2,000 - $10,000</option>
                  <option value="over-10000">Over $10,000</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product/Service Description
              </label>
              <textarea
                required
                className="input-field"
                rows="3"
                placeholder="Describe what you sell and how it helps people..."
                value={formData.businessInfo.productService}
                onChange={(e) => setFormData({
                  ...formData,
                  businessInfo: {...formData.businessInfo, productService: e.target.value}
                })}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Audience
              </label>
              <input
                type="text"
                required
                className="input-field"
                placeholder="e.g., Busy working moms who want to lose weight"
                value={formData.businessInfo.targetAudience}
                onChange={(e) => setFormData({
                  ...formData,
                  businessInfo: {...formData.businessInfo, targetAudience: e.target.value}
                })}
              />
            </div>
          </div>

          {/* Generation Settings */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">Sequence Settings</h2>
            
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Number of Emails
                </label>
                <select
                  className="input-field"
                  value={formData.generationSettings.sequenceLength}
                  onChange={(e) => setFormData({
                    ...formData,
                    generationSettings: {...formData.generationSettings, sequenceLength: parseInt(e.target.value)}
                  })}
                >
                  <option value="5">5 Emails</option>
                  <option value="7">7 Emails</option>
                  <option value="10">10 Emails</option>
                  <option value="14">14 Emails</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tone
                </label>
                <select
                  className="input-field"
                  value={formData.generationSettings.tone}
                  onChange={(e) => setFormData({
                    ...formData,
                    generationSettings: {...formData.generationSettings, tone: e.target.value}
                  })}
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="friendly">Friendly</option>
                  <option value="authoritative">Authoritative</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Goal
                </label>
                <select
                  className="input-field"
                  value={formData.generationSettings.primaryGoal}
                  onChange={(e) => setFormData({
                    ...formData,
                    generationSettings: {...formData.generationSettings, primaryGoal: e.target.value}
                  })}
                >
                  <option value="sales">Drive Sales</option>
                  <option value="nurture">Nurture Leads</option>
                  <option value="onboarding">Onboard Customers</option>
                  <option value="retention">Retain Customers</option>
                  <option value="upsell">Upsell/Cross-sell</option>
                </select>
              </div>
            </div>
          </div>

          <div className="pt-6">
            <button
              type="submit"
              disabled={loading}
              className="btn-primary w-full flex items-center justify-center text-lg py-4"
            >
              {loading ? (
                <>
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Generating Your Sequence...
                </>
              ) : (
                <>
                  <Zap className="h-5 w-5 mr-2" />
                  Generate Email Sequence
                </>
              )}
            </button>
          </div>
        </motion.form>
      </div>
    </div>
  )
}

export default GeneratorPage