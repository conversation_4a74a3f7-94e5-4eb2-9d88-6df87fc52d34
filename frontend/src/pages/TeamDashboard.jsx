import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Plus, 
  Settings, 
  Mail, 
  Crown, 
  Shield, 
  Edit, 
  Eye,
  Workflow,
  Bot,
  Network,
  TrendingUp,
  Calendar,
  MessageSquare
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

const TeamDashboard = () => {
  const { user } = useAuth()
  const [teams, setTeams] = useState([])
  const [selectedTeam, setSelectedTeam] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showInviteModal, setShowInviteModal] = useState(false)

  useEffect(() => {
    fetchUserTeams()
  }, [])

  const fetchUserTeams = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/teams/my-teams`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setTeams(data.data || [])
        if (data.data?.length > 0) {
          setSelectedTeam(data.data[0])
        }
      }
    } catch (error) {
      console.error('Failed to fetch teams:', error)
      toast.error('Failed to load teams')
    } finally {
      setLoading(false)
    }
  }

  const createTeam = async (teamData) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/teams/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(teamData)
      })

      if (response.ok) {
        const data = await response.json()
        toast.success('Team created successfully!')
        setShowCreateModal(false)
        fetchUserTeams()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create team')
      }
    } catch (error) {
      console.error('Create team error:', error)
      toast.error('Failed to create team')
    }
  }

  const inviteToTeam = async (inviteData) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/teams/${selectedTeam.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(inviteData)
      })

      if (response.ok) {
        toast.success('Invitation sent successfully!')
        setShowInviteModal(false)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to send invitation')
      }
    } catch (error) {
      console.error('Invite error:', error)
      toast.error('Failed to send invitation')
    }
  }

  const getRoleIcon = (role) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4 text-yellow-400" />
      case 'admin': return <Shield className="h-4 w-4 text-blue-400" />
      case 'editor': return <Edit className="h-4 w-4 text-green-400" />
      default: return <Eye className="h-4 w-4 text-gray-400" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 flex items-center justify-center">
        <div className="text-white">Loading teams...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Team Collaboration</h1>
            <p className="text-neutral-300">Manage your AI agent teams and workflows</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Team
          </button>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Teams Sidebar */}
          <div className="lg:col-span-1">
            <div className="card">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Your Teams
              </h3>
              <div className="space-y-2">
                {teams.map((team) => (
                  <button
                    key={team.id}
                    onClick={() => setSelectedTeam(team)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedTeam?.id === team.id
                        ? 'bg-purple-500/20 border border-purple-500/30'
                        : 'hover:bg-neutral-700/50'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {getRoleIcon(team.userRole)}
                      <span className="text-white font-medium">{team.name}</span>
                    </div>
                    <div className="text-sm text-neutral-400">
                      {team.stats.memberCount} members • {team.stats.workflowCount} workflows
                    </div>
                  </button>
                ))}
                {teams.length === 0 && (
                  <div className="text-center py-8 text-neutral-400">
                    <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p className="mb-4">No teams yet</p>
                    <button
                      onClick={() => setShowCreateModal(true)}
                      className="btn btn-outline text-sm"
                    >
                      Create Your First Team
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedTeam ? (
              <div className="space-y-6">
                {/* Team Header */}
                <div className="card">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-2">{selectedTeam.name}</h2>
                      <p className="text-neutral-300">{selectedTeam.description}</p>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowInviteModal(true)}
                        className="btn btn-outline text-sm flex items-center gap-2"
                      >
                        <Mail className="h-4 w-4" />
                        Invite
                      </button>
                      <button className="btn btn-ghost text-sm">
                        <Settings className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Team Stats */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-400 mb-1">
                        {selectedTeam.stats.memberCount}
                      </div>
                      <div className="text-sm text-neutral-400">Members</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-400 mb-1">
                        {selectedTeam.stats.workflowCount}
                      </div>
                      <div className="text-sm text-neutral-400">Workflows</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-400 mb-1">
                        {selectedTeam.stats.agentCount}
                      </div>
                      <div className="text-sm text-neutral-400">AI Agents</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-amber-400 mb-1">
                        {selectedTeam.stats.executionCount}
                      </div>
                      <div className="text-sm text-neutral-400">Executions</div>
                    </div>
                  </div>
                </div>

                {/* Team Members */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Team Members
                  </h3>
                  <div className="space-y-3">
                    {selectedTeam.members?.map((member, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-neutral-800/50">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center text-white font-semibold">
                            {member.email?.charAt(0).toUpperCase() || '?'}
                          </div>
                          <div>
                            <div className="text-white font-medium">{member.email}</div>
                            <div className="text-sm text-neutral-400">
                              Joined {new Date(member.joinedAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getRoleIcon(member.role)}
                          <span className="text-sm text-neutral-300 capitalize">{member.role}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="card text-center">
                    <Bot className="h-8 w-8 text-purple-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Deploy AI Agents</h4>
                    <p className="text-sm text-neutral-400 mb-4">Launch collaborative AI workflows</p>
                    <button className="btn btn-outline text-sm w-full">Launch Agents</button>
                  </div>
                  <div className="card text-center">
                    <Workflow className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Build Workflows</h4>
                    <p className="text-sm text-neutral-400 mb-4">Create team automation flows</p>
                    <button className="btn btn-outline text-sm w-full">Build Now</button>
                  </div>
                  <div className="card text-center">
                    <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">View Analytics</h4>
                    <p className="text-sm text-neutral-400 mb-4">Track team performance</p>
                    <button className="btn btn-outline text-sm w-full">View Stats</button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="card text-center py-12">
                <Users className="h-16 w-16 text-neutral-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Select a Team</h3>
                <p className="text-neutral-400">Choose a team from the sidebar to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Team Modal */}
      {showCreateModal && (
        <CreateTeamModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={createTeam}
        />
      )}

      {/* Invite Modal */}
      {showInviteModal && (
        <InviteModal
          team={selectedTeam}
          onClose={() => setShowInviteModal(false)}
          onSubmit={inviteToTeam}
        />
      )}
    </div>
  )
}

const CreateTeamModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.name.trim()) {
      onSubmit(formData)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-neutral-800 rounded-lg p-6 w-full max-w-md mx-4"
      >
        <h3 className="text-xl font-semibold text-white mb-4">Create New Team</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">
              Team Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="input w-full"
              placeholder="Enter team name"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="input w-full h-20 resize-none"
              placeholder="Optional team description"
            />
          </div>
          <div className="flex gap-3 pt-4">
            <button type="button" onClick={onClose} className="btn btn-ghost flex-1">
              Cancel
            </button>
            <button type="submit" className="btn btn-primary flex-1">
              Create Team
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

const InviteModal = ({ team, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    email: '',
    role: 'viewer',
    message: ''
  })

  const roles = [
    { id: 'viewer', name: 'Viewer', description: 'Can view and execute workflows' },
    { id: 'editor', name: 'Editor', description: 'Can create and edit workflows' },
    { id: 'admin', name: 'Admin', description: 'Can manage team and workflows' }
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.email.trim()) {
      onSubmit(formData)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-neutral-800 rounded-lg p-6 w-full max-w-md mx-4"
      >
        <h3 className="text-xl font-semibold text-white mb-4">
          Invite to {team?.name}
        </h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="input w-full"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">
              Role
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="input w-full"
            >
              {roles.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name} - {role.description}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">
              Message
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              className="input w-full h-20 resize-none"
              placeholder="Optional invitation message"
            />
          </div>
          <div className="flex gap-3 pt-4">
            <button type="button" onClick={onClose} className="btn btn-ghost flex-1">
              Cancel
            </button>
            <button type="submit" className="btn btn-primary flex-1">
              Send Invitation
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

export default TeamDashboard