import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>ap, Loader2, FileText, CheckCircle } from 'lucide-react'
import toast from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

const SimpleGeneratorPage = () => {
  const { getApiClient } = useAuth()
  const [formData, setFormData] = useState({
    prompt: '',
    title: '',
    sequenceLength: 3,
    tone: 'professional',
    industry: 'general'
  })
  const [loading, setLoading] = useState(false)
  const [generatedSequence, setGeneratedSequence] = useState(null)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleGenerate = async (e) => {
    e.preventDefault()
    
    if (!formData.prompt || formData.prompt.length < 10) {
      toast.error('Please provide a prompt with at least 10 characters')
      return
    }
    
    if (!formData.title) {
      toast.error('Please provide a title for your sequence')
      return
    }

    setLoading(true)
    setGeneratedSequence(null)

    try {
      const apiClient = getApiClient()
      const response = await apiClient.post('/sequences/generate', formData)
      
      if (response.data.success) {
        setGeneratedSequence(response.data.sequence)
        toast.success('Email sequence generated successfully!')
      } else {
        toast.error(response.data.message || 'Failed to generate sequence')
      }
    } catch (error) {
      console.error('Generation error:', error)
      toast.error(error.response?.data?.message || 'Failed to generate sequence')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-4">
             Email Sequence Generator
          </h1>
          <p className="text-gray-300 text-lg">
            Generate professional email sequences powered by AI templates
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Generation Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <Zap className="h-6 w-6 mr-2 text-amber-400" />
              Generate Sequence
            </h2>

            <form onSubmit={handleGenerate} className="space-y-6">
              <div>
                <label className="block text-white font-medium mb-2">
                  Sequence Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="e.g., Software Sales Sequence"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20"
                  required
                />
              </div>

              <div>
                <label className="block text-white font-medium mb-2">
                  Description/Prompt *
                </label>
                <textarea
                  name="prompt"
                  value={formData.prompt}
                  onChange={handleInputChange}
                  placeholder="Describe what you want to promote and who your target audience is..."
                  rows={4}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20"
                  required
                />
                <p className="text-gray-400 text-sm mt-1">
                  Minimum 10 characters required
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white font-medium mb-2">
                    Email Count
                  </label>
                  <select
                    name="sequenceLength"
                    value={formData.sequenceLength}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20"
                  >
                    <option value={2}>2 emails</option>
                    <option value={3}>3 emails</option>
                    <option value={4}>4 emails</option>
                    <option value={5}>5 emails</option>
                  </select>
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">
                    Tone
                  </label>
                  <select
                    name="tone"
                    value={formData.tone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20"
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-white font-medium mb-2">
                  Industry
                </label>
                <input
                  type="text"
                  name="industry"
                  value={formData.industry}
                  onChange={handleInputChange}
                  placeholder="e.g., software, ecommerce, consulting"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-amber-400 focus:ring-2 focus:ring-amber-400/20"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-amber-400 to-orange-500 text-black font-bold py-4 px-6 rounded-lg hover:from-amber-500 hover:to-orange-600 transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5 mr-2" />
                    Generate Sequence
                  </>
                )}
              </button>
            </form>
          </motion.div>

          {/* Generated Sequence Display */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <FileText className="h-6 w-6 mr-2 text-blue-400" />
              Generated Sequence
            </h2>

            {!generatedSequence && !loading && (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400">
                  Fill out the form and click generate to create your email sequence
                </p>
              </div>
            )}

            {loading && (
              <div className="text-center py-12">
                <Loader2 className="h-16 w-16 text-amber-400 mx-auto mb-4 animate-spin" />
                <p className="text-gray-300">Generating your email sequence...</p>
              </div>
            )}

            {generatedSequence && (
              <div className="space-y-6">
                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                  <div className="flex items-center text-green-400 mb-2">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    <span className="font-medium">Sequence Generated Successfully</span>
                  </div>
                  <p className="text-green-300 text-sm">
                    Title: {generatedSequence.title}
                  </p>
                  <p className="text-green-300 text-sm">
                    Emails: {generatedSequence.emails?.length || 0}
                  </p>
                </div>

                <div className="space-y-4">
                  {generatedSequence.emails?.map((email, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-white font-semibold">
                          Email {email.emailNumber || index + 1}
                        </h3>
                        <span className="text-xs text-gray-400 bg-white/10 px-2 py-1 rounded">
                          {generatedSequence.tone}
                        </span>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <p className="text-amber-400 text-sm font-medium">Subject:</p>
                          <p className="text-white">{email.subject}</p>
                        </div>
                        <div>
                          <p className="text-blue-400 text-sm font-medium">Content:</p>
                          <p className="text-gray-300 text-sm leading-relaxed">
                            {email.content}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default SimpleGeneratorPage