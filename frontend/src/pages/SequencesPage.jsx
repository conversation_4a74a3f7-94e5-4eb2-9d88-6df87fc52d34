import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileText, Plus, Trash2, Eye, Calendar, Tag, Loader2 } from 'lucide-react'
import toast from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'
import { Link } from 'react-router-dom'

const SequencesPage = () => {
  const { getApiClient } = useAuth()
  const [sequences, setSequences] = useState([])
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState(null)

  useEffect(() => {
    loadSequences()
  }, [])

  const loadSequences = async () => {
    setLoading(true)
    try {
      const apiClient = getApiClient()
      const response = await apiClient.get('/sequences')
      
      if (response.data.success) {
        setSequences(response.data.sequences)
      } else {
        toast.error('Failed to load sequences')
      }
    } catch (error) {
      console.error('Failed to load sequences:', error)
      toast.error('Failed to load sequences')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (sequenceId) => {
    if (!confirm('Are you sure you want to delete this sequence?')) {
      return
    }

    setDeleting(sequenceId)
    try {
      const apiClient = getApiClient()
      const response = await apiClient.delete(`/sequences/${sequenceId}`)
      
      if (response.data.success) {
        setSequences(prev => prev.filter(seq => seq._id !== sequenceId))
        toast.success('Sequence deleted successfully')
      } else {
        toast.error('Failed to delete sequence')
      }
    } catch (error) {
      console.error('Failed to delete sequence:', error)
      toast.error('Failed to delete sequence')
    } finally {
      setDeleting(null)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'paused': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'completed': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center mb-8"
        >
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
               Email Sequences
            </h1>
            <p className="text-gray-300">
              Manage and view your generated email sequences
            </p>
          </div>
          
          <Link 
            to="/generator-simple"
            className="bg-gradient-to-r from-amber-400 to-orange-500 text-black font-bold py-3 px-6 rounded-lg hover:from-amber-500 hover:to-orange-600 transition-all duration-200 flex items-center"
          >
            <Plus className="h-5 w-5 mr-2" />
            Create New Sequence
          </Link>
        </motion.div>

        {/* Loading State */}
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Loader2 className="h-12 w-12 text-amber-400 mx-auto mb-4 animate-spin" />
            <p className="text-gray-300">Loading your sequences...</p>
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && sequences.length === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-16"
          >
            <FileText className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-white mb-4">
              No sequences yet
            </h2>
            <p className="text-gray-400 mb-8 max-w-md mx-auto">
              Create your first email sequence to get started with automated email marketing
            </p>
            <Link 
              to="/generator-simple"
              className="bg-gradient-to-r from-amber-400 to-orange-500 text-black font-bold py-3 px-6 rounded-lg hover:from-amber-500 hover:to-orange-600 transition-all duration-200 inline-flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              Create Your First Sequence
            </Link>
          </motion.div>
        )}

        {/* Sequences Grid */}
        {!loading && sequences.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {sequences.map((sequence, index) => (
              <motion.div
                key={sequence._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:border-white/30 transition-all duration-200"
              >
                {/* Header */}
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-bold text-white truncate mb-1">
                      {sequence.title}
                    </h3>
                    <p className="text-gray-400 text-sm line-clamp-2">
                      {sequence.description}
                    </p>
                  </div>
                  
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => handleDelete(sequence._id)}
                      disabled={deleting === sequence._id}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-all duration-200 disabled:opacity-50"
                    >
                      {deleting === sequence._id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center text-blue-400">
                      <FileText className="h-4 w-4 mr-1" />
                      <span>{sequence.emails?.length || 0} emails</span>
                    </div>
                    <div className="flex items-center text-amber-400">
                      <Tag className="h-4 w-4 mr-1" />
                      <span className="capitalize">{sequence.industry}</span>
                    </div>
                  </div>
                  
                  <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(sequence.status)}`}>
                    {sequence.status}
                  </span>
                </div>

                {/* Metadata */}
                <div className="space-y-2 text-sm text-gray-400">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>Created {formatDate(sequence.createdAt)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="capitalize">Tone: {sequence.tone}</span>
                    <span className="text-xs bg-white/10 px-2 py-1 rounded">
                      {sequence.aiModel}
                    </span>
                  </div>
                </div>

                {/* Preview of first email */}
                {sequence.emails && sequence.emails.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <p className="text-xs text-gray-500 mb-1">First email preview:</p>
                    <p className="text-sm text-white font-medium truncate">
                      {sequence.emails[0].subject}
                    </p>
                    <p className="text-xs text-gray-400 line-clamp-2 mt-1">
                      {sequence.emails[0].content}
                    </p>
                  </div>
                )}
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Stats Summary */}
        {!loading && sequences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-12 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
          >
            <h3 className="text-lg font-bold text-white mb-4"> Your Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-amber-400">
                  {sequences.length}
                </div>
                <div className="text-sm text-gray-400">Total Sequences</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {sequences.reduce((sum, seq) => sum + (seq.emails?.length || 0), 0)}
                </div>
                <div className="text-sm text-gray-400">Total Emails</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {sequences.filter(seq => seq.status === 'active').length}
                </div>
                <div className="text-sm text-gray-400">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-400">
                  {sequences.filter(seq => seq.status === 'draft').length}
                </div>
                <div className="text-sm text-gray-400">Drafts</div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default SequencesPage