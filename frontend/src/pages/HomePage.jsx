import React, { useState, useEffect } from 'react';
import { useN<PERSON>gate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Bot, 
  Layers, 
  Network, 
  Zap, 
  Shield, 
  Users, 
  TrendingUp,
  ChevronRight,
  Play,
  Check,
  Star,
  ArrowRight,
  Sparkles,
  GitBranch,
  Activity,
  Database,
  Cloud,
  Lock,
  BarChart,
  Target,
  Palette,
  Globe,
  Building2,
  Rocket,
  Crown,
  Hexagon
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { colonyTheme, getTextClass, getButtonClass, getBadgeClass, getCardClass, cn } from '../design-system/colony-theme';
import { Icon, NeuralPattern, ColonyIcon, SwarmIcon, NeuralNetworkIcon, HexCard } from '../design-system/colony-icons';
import AnimatedBackground from '../components/AnimatedBackground';

// Animated colony visualization component
const ColonyVisualization = () => {
  return (
    <div className="relative w-full h-full">
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 800 600">
        {/* Neural network connections */}
        <defs>
          <linearGradient id="neural-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#0ea5e9" stopOpacity="0.4" />
            <stop offset="100%" stopColor="#f59e0b" stopOpacity="0.4" />
          </linearGradient>
        </defs>
        
        {/* Animated connections */}
        <g className="animate-pulse">
          <line x1="400" y1="100" x2="200" y2="250" stroke="url(#neural-gradient)" strokeWidth="3" />
          <line x1="400" y1="100" x2="600" y2="250" stroke="url(#neural-gradient)" strokeWidth="3" />
          <line x1="200" y1="250" x2="100" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="200" y1="250" x2="300" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="600" y1="250" x2="500" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
          <line x1="600" y1="250" x2="700" y2="400" stroke="url(#neural-gradient)" strokeWidth="2" />
        </g>
        
        {/* Queen agent */}
        <motion.g
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [1, 0.8, 1]
          }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <circle cx="400" cy="100" r="50" fill="#f59e0b" opacity="0.2" />
          <circle cx="400" cy="100" r="35" fill="#f59e0b" opacity="0.4" />
          <foreignObject x="375" y="75" width="50" height="50">
            <Crown className="w-12 h-12 text-honey-500" />
          </foreignObject>
        </motion.g>
        
        {/* Worker agents */}
        <motion.g
          animate={{ 
            rotate: [0, 360],
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <circle cx="200" cy="250" r="30" fill="#0ea5e9" opacity="0.3" />
          <foreignObject x="180" y="230" width="40" height="40">
            <Bot className="w-10 h-10 text-neural-500" />
          </foreignObject>
        </motion.g>
        
        <motion.g
          animate={{ 
            rotate: [0, -360],
          }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        >
          <circle cx="600" cy="250" r="30" fill="#0ea5e9" opacity="0.3" />
          <foreignObject x="580" y="230" width="40" height="40">
            <Bot className="w-10 h-10 text-neural-500" />
          </foreignObject>
        </motion.g>
        
        {/* Scout agents */}
        {[
          { x: 100, y: 400 },
          { x: 300, y: 400 },
          { x: 500, y: 400 },
          { x: 700, y: 400 }
        ].map((pos, i) => (
          <motion.g
            key={i}
            animate={{ 
              y: [0, -15, 0],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{ duration: 2, delay: i * 0.5, repeat: Infinity }}
          >
            <circle cx={pos.x} cy={pos.y} r="25" fill="#22c55e" opacity="0.3" />
            <foreignObject x={pos.x - 15} y={pos.y - 15} width="30" height="30">
              <Target className="w-8 h-8 text-swarm-500" />
            </foreignObject>
          </motion.g>
        ))}
      </svg>
    </div>
  );
};

const HomePage = () => {
  const navigate = useNavigate();
  const [activeFeature, setActiveFeature] = useState(0);

  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: 'colony',
      title: "AI Colony Architecture",
      description: "Deploy intelligent agent colonies that work together like a digital hive mind",
      color: "neural"
    },
    {
      icon: 'neural',
      title: "Neural Workflows",
      description: "Design complex workflows with agents that learn and adapt to your needs",
      color: "honey"
    },
    {
      icon: 'swarm',
      title: "Swarm Intelligence",
      description: "Harness collective intelligence for unprecedented automation power",
      color: "swarm"
    }
  ];

  const benefits = [
    {
      icon: <Activity className="w-8 h-8" />,
      title: "10x Productivity",
      description: "Automate complex tasks with intelligent agent swarms",
      metric: "92% faster",
      color: "neural"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Enterprise Security",
      description: "Bank-level encryption and neural firewall protection",
      metric: "SOC 2 Compliant",
      color: "honey"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Infinite Scalability",
      description: "From single agents to massive colony deployments",
      metric: "∞ agents",
      color: "swarm"
    }
  ];

  const stats = [
    { value: "50K+", label: "Active Colonies", icon: <Hexagon className="w-6 h-6" /> },
    { value: "2.5M", label: "Agent Executions", icon: <Bot className="w-6 h-6" /> },
    { value: "99.9%", label: "Colony Uptime", icon: <Activity className="w-6 h-6" /> },
    { value: "150ms", label: "Neural Response", icon: <Zap className="w-6 h-6" /> }
  ];

  return (
    <div className={cn("min-h-screen relative overflow-hidden", colonyTheme.gradients.neural)}>
      <AnimatedBackground />
      <NeuralPattern className="opacity-3" />
      
      {/* Hero Section */}
      <section className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            {/* Neural Badge */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-neural-500/10 border border-neural-500/30 mb-6"
            >
              <Sparkles className="w-4 h-4 text-neural-400" />
              <span className={cn(getTextClass('meta'), "text-neural-400")}>
                Next-Gen AI Colony Platform
              </span>
            </motion.div>

            {/* Main Heading */}
            <h1 className={cn(getTextClass('h1'), "mb-6")}>
              <span className="block text-4xl md:text-6xl font-bold bg-gradient-to-r from-neural-400 via-honey-400 to-swarm-400 bg-clip-text text-transparent">
                Orchestrate AI Agent Colonies
              </span>
              <span className="block text-3xl md:text-5xl mt-2">
                That Think, Learn, and Scale
              </span>
            </h1>

            <p className={cn(getTextClass('bodyLarge'), "mb-8 max-w-2xl mx-auto")}>
              Deploy intelligent agent swarms that work together to automate complex workflows, 
              analyze data, and transform your business operations with collective neural intelligence.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/register')}
                className={cn(
                  getButtonClass('primary'),
                  "px-8 py-4 rounded-lg text-lg flex items-center gap-3 group"
                )}
              >
                <Rocket className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                Deploy Your First Colony
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/demo')}
                className={cn(
                  getButtonClass('secondary'),
                  "px-8 py-4 rounded-lg text-lg flex items-center gap-3"
                )}
              >
                <Play className="w-5 h-5" />
                Watch Colony Demo
              </motion.button>
            </div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="mt-12 flex items-center justify-center gap-8 flex-wrap"
            >
              {stats.map((stat, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="text-neural-400">{stat.icon}</div>
                  <div className="text-left">
                    <p className={cn(getTextClass('h4'), "text-gray-900 dark:text-white")}>
                      {stat.value}
                    </p>
                    <p className={getTextClass('metaSmall')}>{stat.label}</p>
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Colony Visualization Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-neural-50/50 to-transparent dark:via-neural-950/50" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              <Badge className={cn(getBadgeClass('neural'), "mb-4")}>
                <Brain className="w-4 h-4 mr-1" />
                Colony Intelligence
              </Badge>
              <h2 className={getTextClass('h2')}>
                Watch Your Colony <span className="text-neural-600 dark:text-neural-400">Think and Evolve</span>
              </h2>
              <p className={cn(getTextClass('bodyLarge'), "mt-4 mb-8")}>
                Our AI colonies operate like digital organisms, with specialized agents working 
                in perfect harmony to achieve your goals.
              </p>
              
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    viewport={{ once: true }}
                    onClick={() => setActiveFeature(index)}
                    className={cn(
                      "flex items-start gap-4 p-4 rounded-lg cursor-pointer transition-all",
                      activeFeature === index 
                        ? "bg-white dark:bg-gray-800 shadow-lg border-2 border-neural-500"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    )}
                  >
                    <div className={cn(
                      "p-3 rounded-lg",
                      activeFeature === index
                        ? "bg-gradient-to-br from-neural-500 to-neural-600"
                        : "bg-gray-100 dark:bg-gray-800"
                    )}>
                      <Icon 
                        name={feature.icon} 
                        size={24} 
                        className={activeFeature === index ? "text-white" : ""} 
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className={getTextClass('h5')}>{feature.title}</h3>
                      <p className={getTextClass('body')}>{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="relative h-[600px]"
            >
              <ColonyVisualization />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <Badge className={cn(getBadgeClass('honey'), "mb-4")}>
              <Zap className="w-4 h-4 mr-1" />
              Colony Benefits
            </Badge>
            <h2 className={getTextClass('h2')}>
              Unlock <span className="text-honey-600 dark:text-honey-400">Exponential Growth</span>
            </h2>
            <p className={cn(getTextClass('bodyLarge'), "mt-4 max-w-3xl mx-auto")}>
              Our neural colonies deliver measurable results from day one
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className={cn(getCardClass(true), "p-8 text-center")}
              >
                <div className={cn(
                  "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                  "bg-gradient-to-br",
                  benefit.color === 'neural' && "from-neural-100 to-neural-200 dark:from-neural-800 dark:to-neural-900",
                  benefit.color === 'honey' && "from-honey-100 to-honey-200 dark:from-honey-800 dark:to-honey-900",
                  benefit.color === 'swarm' && "from-swarm-100 to-swarm-200 dark:from-swarm-800 dark:to-swarm-900"
                )}>
                  {React.cloneElement(benefit.icon, { 
                    className: cn(
                      "w-8 h-8",
                      benefit.color === 'neural' && "text-neural-600 dark:text-neural-400",
                      benefit.color === 'honey' && "text-honey-600 dark:text-honey-400",
                      benefit.color === 'swarm' && "text-swarm-600 dark:text-swarm-400"
                    )
                  })}
                </div>
                <h3 className={getTextClass('h4')}>{benefit.title}</h3>
                <p className={cn(getTextClass('body'), "mt-2 mb-4")}>{benefit.description}</p>
                <p className={cn(
                  getTextClass('h3'),
                  benefit.color === 'neural' && "text-neural-600 dark:text-neural-400",
                  benefit.color === 'honey' && "text-honey-600 dark:text-honey-400",
                  benefit.color === 'swarm' && "text-swarm-600 dark:text-swarm-400"
                )}>
                  {benefit.metric}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Section */}
      <section className="py-20 relative bg-gradient-to-br from-gray-900 to-neural-950">
        <div className="absolute inset-0">
          <NeuralPattern className="opacity-10" />
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              <Badge className="bg-neural-500/20 text-neural-300 border-neural-500/30 mb-4">
                <Building2 className="w-4 h-4 mr-1" />
                Enterprise Ready
              </Badge>
              <h2 className={cn(getTextClass('h2'), "text-white")}>
                Built for <span className="text-neural-400">Enterprise Scale</span>
              </h2>
              <p className={cn(getTextClass('bodyLarge'), "mt-4 mb-8 text-gray-300")}>
                Deploy with confidence knowing your colonies are protected by enterprise-grade 
                security and can scale to millions of operations.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                {[
                  { icon: Lock, label: "SOC 2 Compliant" },
                  { icon: Shield, label: "99.9% Uptime SLA" },
                  { icon: Cloud, label: "Multi-Region Deploy" },
                  { icon: Database, label: "Automated Backups" }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-3 p-4 bg-white/5 rounded-lg border border-white/10"
                  >
                    <item.icon className="w-5 h-5 text-neural-400" />
                    <span className="text-gray-300 font-medium">{item.label}</span>
                  </motion.div>
                ))}
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/enterprise')}
                className={cn(
                  "mt-8 px-6 py-3 rounded-lg",
                  "bg-white text-gray-900 font-semibold",
                  "hover:bg-gray-100 transition-all"
                )}
              >
                Learn About Enterprise
                <ArrowRight className="w-4 h-4 inline ml-2" />
              </motion.button>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="aspect-square relative">
                <div className="absolute inset-0 bg-gradient-to-br from-neural-500/20 to-neural-600/20 rounded-3xl" />
                <div className="absolute inset-4 bg-gradient-to-br from-neural-500/10 to-neural-600/10 rounded-2xl" />
                <div className="absolute inset-8 bg-gradient-to-br from-neural-500/5 to-neural-600/5 rounded-xl" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Shield className="w-32 h-32 text-neural-400" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className={cn(getCardClass(), "p-12 relative overflow-hidden")}
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-neural-500/20 to-honey-500/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-br from-swarm-500/20 to-neural-500/20 rounded-full blur-3xl" />
            
            <div className="relative">
              <ColonyIcon size={64} className="mx-auto mb-6" animate />
              <h2 className={getTextClass('h2')}>
                Ready to Build Your AI Colony?
              </h2>
              <p className={cn(getTextClass('bodyLarge'), "mt-4 mb-8 max-w-2xl mx-auto")}>
                Join thousands of companies using NeuroColony to automate workflows and scale operations 
                with intelligent agent swarms.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/register')}
                  className={cn(
                    getButtonClass('primary'),
                    "px-8 py-4 rounded-lg text-lg flex items-center gap-3"
                  )}
                >
                  <Sparkles className="w-5 h-5" />
                  Start Free Trial
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/contact')}
                  className={cn(
                    getButtonClass('secondary'),
                    "px-8 py-4 rounded-lg text-lg"
                  )}
                >
                  Talk to Colony Expert
                </motion.button>
              </div>
              
              <p className={cn(getTextClass('meta'), "mt-6")}>
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;