import { motion } from 'framer-motion'
import { Check, Zap, Loader2 } from 'lucide-react'
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { 
  LimitedTimeOffer, 
  SocialProofNotifications, 
  ExitIntentPopup 
} from '../components/ConversionOptimizer'
import PricingComparison from '../components/PricingComparison'
import RevenueCalculator from '../components/RevenueCalculator'
import Logo from '../components/Logo'

const PricingPage = ({ user }) => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [plans, setPlans] = useState([])

  const defaultPlans = [
    {
      id: 'starter',
      name: 'Starter',
      price: '$39',
      period: 'month',
      description: 'Perfect for small marketing teams',
      features: [
        '10 AI agents included',
        'Basic workflow builder',
        '5 platform integrations',
        'Email sequences + optimization',
        'Community support'
      ],
      cta: 'Start Free Trial',
      popular: false,
      baseLimit: 10,
      overageRate: 5
    },
    {
      id: 'professional',
      name: 'Professional',
      price: '$99',
      period: 'month',
      description: 'For growing marketing teams',
      features: [
        '50 AI agents included',
        'Advanced workflow builder',
        'Unlimited integrations',
        'Team collaboration',
        'Priority support',
        'Advanced analytics',
        'Advanced AI access',
        '$5 per additional agent'
      ],
      cta: 'Start Pro Plan',
      popular: true,
      baseLimit: 50,
      overageRate: 5
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: '$299',
      period: 'month',
      description: 'For large organizations',
      features: [
        'Unlimited AI agents',
        'Custom agent development',
        'White-label platform',
        'Enterprise SSO',
        'Dedicated success manager',
        'API access',
        'Custom integrations',
        'Priority implementation'
      ],
      cta: 'Contact Sales',
      popular: false,
      baseLimit: 999999,
      overageRate: 0
    }
  ]

  useEffect(() => {
    // Load plans from API or use defaults
    const loadPlans = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/payments/plans`)
        const data = await response.json()
        
        if (data.success) {
          // Merge API plans with defaults
          const mergedPlans = defaultPlans.map(plan => {
            const apiPlan = data.data[plan.id]
            return apiPlan ? { ...plan, ...apiPlan } : plan
          })
          setPlans(mergedPlans)
        } else {
          setPlans(defaultPlans)
        }
      } catch (error) {
        console.error('Failed to load plans:', error)
        setPlans(defaultPlans)
      }
    }
    
    loadPlans()
  }, [])

  const handlePlanSelect = async (planId) => {
    if (planId === 'free') {
      if (!user) {
        navigate('/register')
        return
      }
      navigate('/dashboard')
      return
    }

    if (!user) {
      toast.error('Please sign up to select a paid plan')
      navigate('/register')
      return
    }

    setLoading(true)
    
    try {
      // For now, redirect to dashboard with a message
      // In production, this would integrate with Stripe Elements
      toast.success(`${planId.charAt(0).toUpperCase() + planId.slice(1)} plan selected! Stripe integration coming soon.`)
      navigate('/dashboard')
    } catch (error) {
      toast.error('Failed to process payment')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950">
      {/* Limited Time Offer Banner */}
      <LimitedTimeOffer discount={40} />
      
      {/* Social Proof Notifications */}
      <SocialProofNotifications />
      
      {/* Exit Intent Popup */}
      <ExitIntentPopup />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            🚨 <span className="gradient-text">Last 24 Hours:</span> Save 40% Forever
          </h1>
          <p className="text-xl text-neutral-300 max-w-3xl mx-auto mb-8">
            Lock in these prices before they increase. <span className="text-red-400 font-bold">This offer expires tonight.</span><br />
            Join 10,000+ businesses generating <span className="text-green-400 font-bold">$50M+ in email revenue</span>.
          </p>
          
          {/* Savings Alert */}
          <div className="max-w-4xl mx-auto bg-green-500/10 border border-green-500/30 rounded-lg p-6 mb-8">
            <div className="text-green-400 font-bold text-lg mb-2">
               You Save $2,376/Year vs Traditional Platforms + Get Superior AI
            </div>
            <div className="text-neutral-300">
              Traditional Platform Pro: $97/month × 12 = $1,164/year<br />
              NeuroColony Business: $99/month × 12 = $1,188/year<br />
              <span className="text-green-400 font-bold">You get 200 sequences vs 50 + AI 3 AI for only $24 more per year</span>
            </div>
          </div>
        </motion.div>

        {/* Revenue Calculator Section */}
        <RevenueCalculator />
        
        {/* Pricing Comparison Section */}
        <PricingComparison />

        <div className="grid lg:grid-cols-3 gap-8 max-w-5xl mx-auto mt-16">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id || index}
              className={`relative card ${
                plan.popular ? 'border-glow scale-105' : ''
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                    🏆 BEST VALUE - Save $198/month
                  </div>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-4">{plan.name}</h3>
                
                {/* Show original price crossed out */}
                <div className="mb-4">
                  {plan.popular && (
                    <div className="text-neutral-500 line-through text-lg mb-1">$297/month</div>
                  )}
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  <span className="text-neutral-400">/{plan.period}</span>
                  {plan.popular && (
                    <div className="text-green-400 text-sm font-bold mt-1">SAVE $198/MONTH</div>
                  )}
                </div>
                <p className="text-neutral-400">{plan.description}</p>
                
                {/* Urgency indicator */}
                {plan.popular && (
                  <div className="mt-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3">
                    <div className="text-red-400 font-bold text-sm">⚠️ Only 12 spots left at this price</div>
                  </div>
                )}
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <Check className="h-5 w-5 text-purple-400 mr-3 flex-shrink-0" />
                    <span className="text-neutral-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handlePlanSelect(plan.id)}
                disabled={loading}
                className={`w-full py-4 px-6 rounded-lg font-bold transition-all duration-200 flex items-center justify-center ${
                  plan.popular
                    ? 'btn btn-primary pulse-animation'
                    : 'btn btn-ghost'
                }`}
              >
                {loading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  plan.popular ? ' Lock In 40% Off Forever' : plan.cta
                )}
              </button>
              
              {plan.popular && (
                <p className="text-xs text-center mt-2 text-amber-400">
                   Most customers see ROI within 7 days
                </p>
              )}
            </motion.div>
          ))}
        </div>

        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          {/* Final CTA Section */}
          <div className="bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30 rounded-2xl p-8 mb-8 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-white mb-4">
              🚨 Final Warning: Prices Increase at Midnight
            </h3>
            <p className="text-xl text-neutral-300 mb-6">
              This is your <span className="text-red-400 font-bold">LAST CHANCE</span> to lock in these prices.<br />
              After tonight, prices return to $297/month for Business plan.
            </p>
            
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-neutral-800/50 rounded-lg p-6">
                <div className="text-red-400 font-bold text-lg mb-2">❌ After Tonight</div>
                <div className="text-white text-2xl font-bold">$297/month</div>
                <div className="text-neutral-400">Regular pricing returns</div>
              </div>
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-6">
                <div className="text-green-400 font-bold text-lg mb-2"> Right Now</div>
                <div className="text-white text-2xl font-bold">$99/month</div>
                <div className="text-green-400">Save $198/month forever</div>
              </div>
            </div>

            <a
              href="/register?promo=40OFF&urgent=true"
              className="btn btn-primary text-xl px-12 py-6 pulse-animation"
            >
               LOCK IN 40% OFF BEFORE MIDNIGHT
            </a>
          </div>

          {/* Professional Features */}
          <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-8 mb-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-6"> Professional Platform Features</h3>
            <div className="grid md:grid-cols-4 gap-6 text-left">
              <div className="text-center">
                <div className="text-3xl mb-2">🔒</div>
                <div className="text-green-400 font-semibold mb-2">Secure Platform</div>
                <p className="text-neutral-400 text-sm">
                  Enterprise-grade security
                </p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2"></div>
                <div className="text-purple-400 font-semibold mb-2">Setup in 60 Seconds</div>
                <p className="text-neutral-400 text-sm">
                  Start generating sequences immediately
                </p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2"></div>
                <div className="text-amber-400 font-semibold mb-2">Proven Results</div>
                <p className="text-neutral-400 text-sm">
                  340% better conversion rates
                </p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2">🏆</div>
                <div className="text-green-400 font-semibold mb-2">99.9% Uptime</div>
                <p className="text-neutral-400 text-sm">
                  Enterprise reliability guaranteed
                </p>
              </div>
            </div>
          </div>
          
          <p className="text-neutral-400 mb-4 text-lg">
             Setup in 60 seconds • 🔒 Secure platform • 📞 24/7 priority support
          </p>
          <div className="flex justify-center items-center space-x-8 text-sm text-neutral-500">
            <span>✓ Cancel anytime</span>
            <span>✓ Keep all generated sequences</span>
            <span>✓ Data export included</span>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default PricingPage