import React, { useState, useCallback, useRef, useEffect } from 'react'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Spinner } from '../components/ui/Spinner'
import {
  Trash2, Play, Save, Settings, Plus, ArrowRight, X,
  Crown, Users, Search, Zap, TrendingUp, Activity,
  Mail, MessageSquare, BarChart3, Target, Brain, Workflow
} from 'lucide-react'
import WorkflowCanvas from '../components/WorkflowCanvas'

// Helper functions for colony visualization
const getAgentIcon = (agentType, category) => {
  const iconMap = {
    queen: Crown,
    worker: {
      email_marketing: Mail,
      content_creation: MessageSquare,
      analytics_reporting: BarChart3,
      lead_generation: Target,
      ai_orchestration: Brain
    },
    scout: Search
  }

  if (agentType === 'worker' && iconMap.worker[category]) {
    return iconMap.worker[category]
  }
  return iconMap[agentType] || Activity
}

const getAgentColor = (agentType) => {
  const colorMap = {
    queen: 'from-purple-500 to-pink-500',
    worker: 'from-blue-500 to-cyan-500',
    scout: 'from-green-500 to-emerald-500'
  }
  return colorMap[agentType] || 'from-gray-500 to-gray-600'
}

/**
 * NeuroColony Visual Agent Builder - Colony Intelligence Workflow Canvas
 * N8N-style drag-and-drop interface with Queen/Worker/Scout agent coordination
 * Marketing-first approach with ant-inspired colony visualization
 */
const AgentBuilder = () => {
  const [workflow, setWorkflow] = useState({
    id: null,
    name: 'Untitled Workflow',
    description: '',
    nodes: [],
    connections: [],
    triggers: []
  })
  
  const [selectedNode, setSelectedNode] = useState(null)
  const [availableAgents, setAvailableAgents] = useState([])
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 })
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionLogs, setExecutionLogs] = useState([])
  const [executionStatus, setExecutionStatus] = useState({
    isRunning: false,
    nodeStatuses: {}
  })
  const canvasRef = useRef(null)

  useEffect(() => {
    fetchAvailableAgents()
  }, [])

  const fetchAvailableAgents = async () => {
    try {
      const response = await fetch('/api/colony/agents', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()

      if (data.success) {
        // Organize agents by colony hierarchy for enhanced workflow building
        const organizedAgents = data.data.agents.map(agent => ({
          ...agent,
          id: agent._id,
          type: agent.agentType, // queen, worker, scout
          category: agent.category,
          capabilities: agent.capabilities || [],
          icon: getAgentIcon(agent.agentType, agent.category),
          color: getAgentColor(agent.agentType)
        }))
        setAvailableAgents(organizedAgents)
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    }
  }

  const handleAddNode = useCallback((agentType, position) => {
    const newNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: agentType.id,
      name: agentType.name,
      icon: agentType.icon,
      position: position || { x: 100, y: 100 },
      inputs: agentType.inputs || [],
      outputs: agentType.outputs || [],
      configuration: {},
      category: agentType.category
    }
    
    setWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode]
    }))
    
    setSelectedNode(newNode)
  }, [])

  const handleNodeUpdate = useCallback((nodeId, updates) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      )
    }))
  }, [])

  const handleDeleteNode = useCallback((nodeId) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(node => node.id !== nodeId),
      connections: prev.connections.filter(conn => 
        conn.source !== nodeId && conn.target !== nodeId
      )
    }))
    setSelectedNode(null)
  }, [])

  const handleConnect = useCallback((sourceId, targetId) => {
    const newConnection = {
      id: `conn_${Date.now()}`,
      source: sourceId,
      target: targetId,
      type: 'data_flow'
    }
    
    setWorkflow(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection]
    }))
  }, [])

  const handleSaveWorkflow = async () => {
    try {
      const response = await fetch('/api/agents/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: workflow.name,
          description: workflow.description,
          agents: workflow.nodes,
          connections: workflow.connections
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        alert('Workflow saved successfully!')
        setWorkflow(prev => ({ ...prev, id: data.data.id }))
      } else {
        alert(`Failed to save workflow: ${data.message}`)
      }
    } catch (error) {
      console.error('Save workflow error:', error)
      alert('Failed to save workflow')
    }
  }

  const handleExecuteWorkflow = async () => {
    if (workflow.nodes.length === 0) {
      alert('Please add at least one agent to the workflow')
      return
    }

    setIsExecuting(true)
    setExecutionLogs([])
    
    try {
      // Execute workflow nodes in sequence (simplified)
      for (const node of workflow.nodes) {
        const logEntry = {
          nodeId: node.id,
          nodeName: node.name,
          status: 'running',
          timestamp: new Date(),
          message: `Executing ${node.name}...`
        }
        
        setExecutionLogs(prev => [...prev, logEntry])

        // Execute the agent
        try {
          const sampleInputs = getSampleInputsForAgent(node.type)
          
          const response = await fetch(`/api/agents/${node.type}/execute`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ 
              inputs: sampleInputs,
              context: { workflowId: workflow.id, nodeId: node.id }
            })
          })

          const result = await response.json()
          
          const successLog = {
            nodeId: node.id,
            nodeName: node.name,
            status: 'completed',
            timestamp: new Date(),
            message: `${node.name} completed successfully`,
            result: result.success ? 'Success' : result.message
          }
          
          setExecutionLogs(prev => [...prev.slice(0, -1), successLog])
          
        } catch (nodeError) {
          const errorLog = {
            nodeId: node.id,
            nodeName: node.name,
            status: 'failed',
            timestamp: new Date(),
            message: `${node.name} failed: ${nodeError.message}`,
            error: nodeError.message
          }
          
          setExecutionLogs(prev => [...prev.slice(0, -1), errorLog])
        }
        
        // Add delay between executions
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
    } catch (error) {
      console.error('Workflow execution error:', error)
    } finally {
      setIsExecuting(false)
    }
  }

  const getSampleInputsForAgent = (agentType) => {
    const samples = {
      'email-sequence-generator': {
        businessInfo: {
          industry: 'Marketing Technology',
          productService: 'Email Marketing Platform',
          targetAudience: 'Small business owners',
          pricePoint: '$99/month'
        },
        sequenceSettings: {
          sequenceLength: 5,
          tone: 'professional',
          primaryGoal: 'sales'
        }
      },
      'subject-line-optimizer': {
        originalSubject: 'Boost Your Marketing ROI Today',
        audienceInfo: {
          industry: 'Marketing',
          audience: 'Marketing professionals'
        }
      },
      'audience-segmentation': {
        customerData: [
          { id: 1, engagement: 'high', purchases: 5 },
          { id: 2, engagement: 'medium', purchases: 2 }
        ],
        behaviorMetrics: {
          emailOpenRate: 0.25,
          clickThroughRate: 0.03
        }
      }
    }
    return samples[agentType] || {}
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex bg-gray-50">
        {/* Left Sidebar - Agent Library */}
        <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Agent Library</h2>
            <p className="text-sm text-gray-600">Drag agents to canvas</p>
          </div>
          
          <div className="p-4 space-y-4">
            {Object.entries(
              availableAgents.reduce((categories, agent) => {
                const category = agent.category || 'uncategorized'
                if (!categories[category]) categories[category] = []
                categories[category].push(agent)
                return categories
              }, {})
            ).map(([category, categoryAgents]) => (
              <div key={category}>
                <h3 className="text-sm font-medium text-gray-700 mb-2 capitalize">
                  {category.replace('-', ' ')}
                </h3>
                <div className="space-y-2">
                  {categoryAgents.map(agent => (
                    <DraggableAgent key={agent.id} agent={agent} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 flex flex-col">
          {/* Toolbar */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <input
                  type="text"
                  value={workflow.name}
                  onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
                  className="text-xl font-semibold bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1"
                  placeholder="Workflow Name"
                />
                <Badge variant="secondary" className="text-xs">
                  {workflow.nodes.length} nodes
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSaveWorkflow}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
                <Button
                  size="sm"
                  onClick={handleExecuteWorkflow}
                  disabled={isExecuting || workflow.nodes.length === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isExecuting ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Running...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Execute
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 relative overflow-hidden">
            <DropCanvas
              ref={canvasRef}
              workflow={workflow}
              onAddNode={handleAddNode}
              onNodeUpdate={handleNodeUpdate}
              onDeleteNode={handleDeleteNode}
              onConnect={handleConnect}
              selectedNode={selectedNode}
              onSelectNode={setSelectedNode}
            />
          </div>
        </div>

        {/* Right Sidebar - Node Configuration */}
        {selectedNode && (
          <div className="w-80 bg-white border-l border-gray-200">
            <NodeConfigPanel
              node={selectedNode}
              onUpdate={(updates) => handleNodeUpdate(selectedNode.id, updates)}
              onClose={() => setSelectedNode(null)}
            />
          </div>
        )}

        {/* Execution Logs Panel */}
        {executionLogs.length > 0 && (
          <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border max-h-48 overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="font-medium">Execution Logs</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setExecutionLogs([])}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            <div className="p-4 space-y-2 max-h-32 overflow-y-auto">
              {executionLogs.map((log, index) => (
                <div
                  key={index}
                  className={`text-sm p-2 rounded ${
                    log.status === 'completed' ? 'bg-green-50 text-green-800' :
                    log.status === 'failed' ? 'bg-red-50 text-red-800' :
                    'bg-blue-50 text-blue-800'
                  }`}
                >
                  <div className="font-medium">{log.nodeName}</div>
                  <div>{log.message}</div>
                  <div className="text-xs opacity-75">{log.timestamp.toLocaleTimeString()}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </DndProvider>
  )
}

// Draggable Agent Component
const DraggableAgent = ({ agent }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'agent',
    item: agent,
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }))

  return (
    <div
      ref={drag}
      className={`p-3 bg-gray-50 rounded-lg border cursor-move hover:shadow-md transition-shadow ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className="text-xl">{agent.icon}</div>
        <div>
          <div className="font-medium text-sm">{agent.name}</div>
          <div className="text-xs text-gray-500 line-clamp-2">
            {agent.description}
          </div>
        </div>
      </div>
    </div>
  )
}

// Drop Canvas Component
const DropCanvas = React.forwardRef(({
  workflow,
  onAddNode,
  onNodeUpdate,
  onDeleteNode,
  onConnect,
  selectedNode,
  onSelectNode
}, ref) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'agent',
    drop: (item, monitor) => {
      const offset = monitor.getClientOffset()
      const canvasRect = ref.current.getBoundingClientRect()
      const position = {
        x: offset.x - canvasRect.left,
        y: offset.y - canvasRect.top
      }
      onAddNode(item, position)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  }))

  return (
    <div
      ref={(element) => {
        ref.current = element
        drop(element)
      }}
      className={`w-full h-full relative bg-gray-50 ${
        isOver ? 'bg-blue-50' : ''
      }`}
      style={{
        backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
        backgroundSize: '20px 20px'
      }}
    >
      {workflow.nodes.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-4">🎨</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Start Building Your Workflow
            </h3>
            <p className="text-gray-600">
              Drag agents from the left panel to create your marketing automation
            </p>
          </div>
        </div>
      )}

      {/* Render workflow nodes */}
      {workflow.nodes.map(node => (
        <WorkflowNode
          key={node.id}
          node={node}
          isSelected={selectedNode?.id === node.id}
          onSelect={() => onSelectNode(node)}
          onUpdate={(updates) => onNodeUpdate(node.id, updates)}
          onDelete={() => onDeleteNode(node.id)}
          onConnect={onConnect}
        />
      ))}

      {/* Render connections */}
      <svg className="absolute inset-0 pointer-events-none">
        {workflow.connections.map(connection => {
          const sourceNode = workflow.nodes.find(n => n.id === connection.source)
          const targetNode = workflow.nodes.find(n => n.id === connection.target)
          
          if (!sourceNode || !targetNode) return null
          
          const sourceX = sourceNode.position.x + 150
          const sourceY = sourceNode.position.y + 40
          const targetX = targetNode.position.x
          const targetY = targetNode.position.y + 40
          
          return (
            <line
              key={connection.id}
              x1={sourceX}
              y1={sourceY}
              x2={targetX}
              y2={targetY}
              stroke="#6b7280"
              strokeWidth="2"
              markerEnd="url(#arrowhead)"
            />
          )
        })}
        
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
          </marker>
        </defs>
      </svg>
    </div>
  )
})

// Workflow Node Component
const WorkflowNode = ({ node, isSelected, onSelect, onUpdate, onDelete, onConnect }) => {
  const [position, setPosition] = useState(node.position)

  const handleMouseDown = (e) => {
    e.preventDefault()
    const startX = e.clientX - position.x
    const startY = e.clientY - position.y

    const handleMouseMove = (e) => {
      const newPosition = {
        x: e.clientX - startX,
        y: e.clientY - startY
      }
      setPosition(newPosition)
      onUpdate({ position: newPosition })
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  return (
    <div
      className={`absolute bg-white rounded-lg border-2 shadow-lg cursor-move w-64 ${
        isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'
      }`}
      style={{ left: position.x, top: position.y }}
      onMouseDown={handleMouseDown}
      onClick={onSelect}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className="text-xl">{node.icon}</div>
            <div className="font-medium text-sm">{node.name}</div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="text-gray-400 hover:text-red-500 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
        
        <div className="text-xs text-gray-500 mb-3">
          Category: {node.category?.replace('-', ' ')}
        </div>
        
        <div className="space-y-2">
          <div className="text-xs">
            <div className="font-medium text-gray-700">Inputs:</div>
            <div className="text-gray-500">{node.inputs?.join(', ') || 'None'}</div>
          </div>
          <div className="text-xs">
            <div className="font-medium text-gray-700">Outputs:</div>
            <div className="text-gray-500">{node.outputs?.join(', ') || 'None'}</div>
          </div>
        </div>
      </div>
      
      {/* Connection points */}
      <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
        <div className="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-md"></div>
      </div>
      <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-3 h-3 bg-gray-400 rounded-full border-2 border-white shadow-md"></div>
      </div>
    </div>
  )
}

// Node Configuration Panel
const NodeConfigPanel = ({ node, onUpdate, onClose }) => {
  const [config, setConfig] = useState(node.configuration || {})

  const handleConfigChange = (key, value) => {
    const newConfig = { ...config, [key]: value }
    setConfig(newConfig)
    onUpdate({ configuration: newConfig })
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="font-medium">Configure Agent</h3>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <div className="text-2xl">{node.icon}</div>
            <div>
              <div className="font-medium">{node.name}</div>
              <div className="text-sm text-gray-500">{node.category}</div>
            </div>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Agent Name
          </label>
          <input
            type="text"
            value={node.name}
            onChange={(e) => onUpdate({ name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Enabled
          </label>
          <input
            type="checkbox"
            checked={config.enabled !== false}
            onChange={(e) => handleConfigChange('enabled', e.target.checked)}
            className="rounded"
          />
        </div>
        
        {/* Agent-specific configuration fields would go here */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Timeout (seconds)
          </label>
          <input
            type="number"
            value={config.timeout || 30}
            onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            min="1"
            max="300"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Retry Attempts
          </label>
          <input
            type="number"
            value={config.retries || 1}
            onChange={(e) => handleConfigChange('retries', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            min="0"
            max="5"
          />
        </div>
      </div>
    </div>
  )
}

export default AgentBuilder