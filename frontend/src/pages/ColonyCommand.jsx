import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Activity, 
  Layers, 
  GitBranch, 
  BarChart3, 
  Shield, 
  Sparkles,
  CircuitBoard,
  Network,
  Cpu,
  Bot,
  Workflow,
  Database,
  Cloud,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge, LiveBadge } from '../components/ui/Badge';
import { Icons } from '../design-system/icons.jsx';
import { colors } from '../design-system/colors';
import AnimatedBackground from '../components/AnimatedBackground';
import AgentStatusIndicator, { AgentCommunicationLine, AgentProgressRing } from '../components/AgentStatusIndicator';
import { useScrollAnimation, useCountAnimation } from '../hooks/useScrollAnimation';
import { animations } from '../utils/animations';

// Colony health calculation
const calculateColonyHealth = (metrics) => {
  const { activeAgents, successRate, resourceUsage } = metrics;
  const agentScore = (activeAgents / 10) * 30; // 30% weight
  const successScore = successRate * 40; // 40% weight
  const resourceScore = (1 - resourceUsage) * 30; // 30% weight
  return Math.round(agentScore + successScore + resourceScore);
};

const ColonyCommand = () => {
  const navigate = useNavigate();
  const [selectedView, setSelectedView] = useState('overview');
  const [colonyMetrics, setColonyMetrics] = useState({
    activeAgents: 7,
    totalAgents: 12,
    successRate: 0.89,
    tasksCompleted: 1247,
    resourceUsage: 0.45,
    activeWorkflows: 3,
  });

  const [recentActivity, setRecentActivity] = useState([
    { id: 1, agent: 'Data Analyzer Queen', task: 'Completed market analysis', time: '2m ago', status: 'success' },
    { id: 2, agent: 'Content Worker #3', task: 'Generated 5 blog posts', time: '5m ago', status: 'success' },
    { id: 3, agent: 'Integration Scout', task: 'Monitoring API endpoints', time: '10m ago', status: 'active' },
    { id: 4, agent: 'Security Soldier', task: 'Blocked suspicious request', time: '15m ago', status: 'warning' },
  ]);

  const views = {
    overview: {
      title: 'Colony Overview',
      icon: Icons.Colony,
      description: 'Real-time colony health and activity monitoring'
    },
    agents: {
      title: 'Agent Management',
      icon: Icons.WorkerAgent,
      description: 'Configure and deploy your AI agents'
    },
    workflows: {
      title: 'Neural Workflows',
      icon: Icons.Workflow,
      description: 'Design and monitor agent collaboration patterns'
    },
    analytics: {
      title: 'Colony Intelligence',
      icon: Icons.Performance,
      description: 'Deep insights and optimization recommendations'
    },
    team: {
      title: 'Team Collaboration',
      icon: Users,
      description: 'Manage team access and shared colonies'
    }
  };

  const colonyHealth = calculateColonyHealth(colonyMetrics);

  return (
    <div className="min-h-screen bg-system-background">
      <AnimatedBackground variant="particles" />
      
      {/* Header */}
      <div className="relative z-10 border-b border-gray-800 bg-system-surface/50 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Icons.Hive size={32} className="text-honey-500" />
              <div>
                <h1 className="text-xl font-bold text-white">Colony Command Center</h1>
                <p className="text-sm text-gray-400">Neural coordination hub</p>
              </div>
            </div>
            
            {/* View Tabs */}
            <div className="flex items-center gap-2">
              {Object.entries(views).map(([key, view]) => (
                <button
                  key={key}
                  onClick={() => setSelectedView(key)}
                  className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                    selectedView === key
                      ? 'bg-neural-500/20 text-neural-400 border border-neural-500/50'
                      : 'text-gray-400 hover:text-white hover:bg-white/5'
                  }`}
                >
                  <view.icon size={20} className="inline-block mr-2" />
                  {view.title}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Colony Status Bar */}
        <motion.div 
          className="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4"
          variants={animations.staggerContainer}
          initial="hidden"
          animate="show"
        >
          <motion.div variants={animations.staggerItem}>
            <Card 
              className="bg-gradient-to-br from-neural-600/20 to-neural-700/10 border-neural-500/30"
              hover
              glow
              delay={0.1}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  >
                    <Icons.HealthIcon size={20} className="text-neural-400" />
                  </motion.div>
                  <Badge variant={colonyHealth > 70 ? 'success' : colonyHealth > 40 ? 'warning' : 'error'} animate>
                    {colonyHealth}% Healthy
                  </Badge>
                </div>
                <h3 className="text-lg font-semibold text-white">Colony Health</h3>
                <div className="mt-2 h-2 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div 
                    className="h-full bg-gradient-to-r from-neural-500 to-network-500"
                    initial={{ width: 0 }}
                    animate={{ width: `${colonyHealth}%` }}
                    transition={{ duration: 1, ease: "easeOut", delay: 0.5 }}
                  />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div variants={animations.staggerItem}>
            <Card 
              className="bg-gradient-to-br from-organic-600/20 to-organic-700/10 border-organic-500/30"
              hover
              glow
              delay={0.2}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Bot size={20} className="text-organic-400" />
                  <motion.span 
                    className="text-2xl font-bold text-white"
                    key={colonyMetrics.activeAgents}
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {useCountAnimation(colonyMetrics.activeAgents, 1000)}
                  </motion.span>
                </div>
                <h3 className="text-lg font-semibold text-white">Active Agents</h3>
                <p className="text-sm text-gray-400">of {colonyMetrics.totalAgents} total</p>
                <AgentStatusIndicator status="active" size="sm" showLabel={false} className="mt-2" />
              </div>
            </Card>
          </motion.div>

          <motion.div variants={animations.staggerItem}>
            <Card 
              className="bg-gradient-to-br from-honey-600/20 to-honey-700/10 border-honey-500/30"
              hover
              glow
              delay={0.3}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <TrendingUp size={20} className="text-honey-400" />
                  <motion.span 
                    className="text-2xl font-bold text-white"
                    key={colonyMetrics.successRate}
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {useCountAnimation(colonyMetrics.successRate * 100, 1500)}%
                  </motion.span>
                </div>
                <h3 className="text-lg font-semibold text-white">Success Rate</h3>
                <p className="text-sm text-gray-400">Last 24 hours</p>
                <AgentProgressRing progress={colonyMetrics.successRate * 100} size={40} className="mt-2" />
              </div>
            </Card>
          </motion.div>

          <motion.div variants={animations.staggerItem}>
            <Card 
              className="bg-gradient-to-br from-colony-600/20 to-colony-700/10 border-colony-500/30"
              hover
              glow
              delay={0.4}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Zap size={20} className="text-colony-400" />
                  <motion.span 
                    className="text-2xl font-bold text-white"
                    key={colonyMetrics.tasksCompleted}
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {useCountAnimation(colonyMetrics.tasksCompleted, 2000)}
                  </motion.span>
                </div>
                <h3 className="text-lg font-semibold text-white">Tasks Completed</h3>
                <p className="text-sm text-gray-400">This month</p>
                <LiveBadge className="mt-2" />
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Dynamic Content Based on Selected View */}
        {selectedView === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Colony Visualization */}
            <div className="lg:col-span-2">
              <Card className="h-full">
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                    <Icons.NeuralNetwork size={24} className="text-neural-400" />
                    Live Colony Network
                  </h2>
                  
                  {/* Placeholder for network visualization */}
                  <div className="relative h-96 bg-gray-900/50 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <Icons.Colony size={120} className="text-neural-500/20 mb-4" />
                        <p className="text-gray-500">Neural network visualization</p>
                        <p className="text-sm text-gray-600">Showing agent interactions in real-time</p>
                      </div>
                    </div>
                    
                    {/* Animated nodes */}
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-3 h-3 bg-neural-400 rounded-full animate-pulse"
                        style={{
                          top: `${20 + i * 15}%`,
                          left: `${15 + i * 20}%`,
                          animationDelay: `${i * 0.2}s`
                        }}
                      />
                    ))}
                  </div>
                </div>
              </Card>
            </div>

            {/* Recent Activity */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="h-full" hover glow>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Activity size={24} className="text-network-400" />
                    </motion.div>
                    Recent Activity
                    <Badge variant="neural" pulse className="ml-auto">
                      {recentActivity.filter(a => a.status === 'active').length} Active
                    </Badge>
                  </h2>
                  
                  <AnimatePresence>
                    <motion.div className="space-y-3">
                      {recentActivity.map((activity, index) => (
                        <motion.div 
                          key={activity.id} 
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.02 }}
                          className="flex items-start gap-3 p-3 rounded-lg bg-gray-800/50 hover:bg-gray-800/70 transition-all duration-200 relative overflow-hidden"
                        >
                          <motion.div 
                            className="mt-1"
                            animate={activity.status === 'active' ? { rotate: 360 } : {}}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          >
                            {activity.status === 'success' && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ type: "spring", stiffness: 500 }}
                              >
                                <CheckCircle size={16} className="text-green-400" />
                              </motion.div>
                            )}
                            {activity.status === 'active' && <Icons.Processing size={16} color={colors.network[400]} />}
                            {activity.status === 'warning' && (
                              <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{ duration: 1, repeat: Infinity }}
                              >
                                <AlertCircle size={16} className="text-yellow-400" />
                              </motion.div>
                            )}
                          </motion.div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-white">{activity.agent}</p>
                            <p className="text-xs text-gray-400">{activity.task}</p>
                          </div>
                          <span className="text-xs text-gray-500">{activity.time}</span>
                          
                          {/* Activity status indicator line */}
                          {activity.status === 'active' && (
                            <motion.div
                              className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-neural-500 to-network-500"
                              initial={{ width: 0 }}
                              animate={{ width: '100%' }}
                              transition={{ duration: 2, repeat: Infinity }}
                            />
                          )}
                        </motion.div>
                      ))}
                    </motion.div>
                  </AnimatePresence>
                  
                  <Button variant="ghost" className="w-full mt-4" onClick={() => setSelectedView('analytics')}>
                    View All Activity
                  </Button>
                </div>
              </Card>
            </motion.div>
          </div>
        )}

        {selectedView === 'agents' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Quick Actions */}
            <Card className="lg:col-span-full">
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4"
                    onClick={() => navigate('/agent-builder')}
                  >
                    <Icons.Blueprint size={24} />
                    <span>Create Agent</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4"
                    onClick={() => navigate('/agent-marketplace')}
                  >
                    <Icons.Hive size={24} />
                    <span>Browse Templates</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4"
                  >
                    <Icons.Deploy size={24} />
                    <span>Deploy Colony</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4"
                  >
                    <Icons.Configure size={24} />
                    <span>Settings</span>
                  </Button>
                </div>
              </div>
            </Card>

            {/* Agent Cards */}
            <Card className="bg-gradient-to-br from-colony-600/10 to-transparent">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white flex items-center gap-2">
                    <Icons.QueenAgent size={24} className="text-colony-400" />
                    Data Analyzer Queen
                  </h3>
                  <Badge variant="success">Active</Badge>
                </div>
                <p className="text-sm text-gray-400 mb-4">
                  Orchestrates data analysis workflows and manages worker agents
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Tasks Completed</span>
                    <span className="text-white font-medium">342</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Success Rate</span>
                    <span className="text-white font-medium">94%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Workers</span>
                    <span className="text-white font-medium">3 active</span>
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <Button size="sm" variant="ghost">Configure</Button>
                  <Button size="sm" variant="primary">View Details</Button>
                </div>
              </div>
            </Card>

            <Card className="bg-gradient-to-br from-neural-600/10 to-transparent">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white flex items-center gap-2">
                    <Icons.WorkerAgent size={24} className="text-neural-400" />
                    Content Generator
                  </h3>
                  <Badge variant="warning">Processing</Badge>
                </div>
                <p className="text-sm text-gray-400 mb-4">
                  Creates high-quality content based on templates and guidelines
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Current Task</span>
                    <span className="text-white font-medium">Blog series</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Progress</span>
                    <span className="text-white font-medium">67%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Est. Time</span>
                    <span className="text-white font-medium">15 min</span>
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <Button size="sm" variant="ghost">Pause</Button>
                  <Button size="sm" variant="primary">Monitor</Button>
                </div>
              </div>
            </Card>

            <Card className="bg-gradient-to-br from-network-600/10 to-transparent">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white flex items-center gap-2">
                    <Icons.ScoutAgent size={24} className="text-network-400" />
                    API Monitor Scout
                  </h3>
                  <Badge variant="default">Idle</Badge>
                </div>
                <p className="text-sm text-gray-400 mb-4">
                  Monitors external APIs and reports on availability and performance
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Last Check</span>
                    <span className="text-white font-medium">2m ago</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Endpoints</span>
                    <span className="text-white font-medium">12 active</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Alerts</span>
                    <span className="text-white font-medium">0</span>
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <Button size="sm" variant="ghost">Configure</Button>
                  <Button size="sm" variant="primary">Activate</Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {selectedView === 'workflows' && (
          <div className="space-y-6">
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Active Neural Workflows</h2>
                <div className="text-center py-12">
                  <Icons.Workflow size={64} className="text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">Workflow canvas coming soon</p>
                  <Button className="mt-4" onClick={() => navigate('/workflow-builder')}>
                    Create New Workflow
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {selectedView === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Performance Metrics</h2>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-400">CPU Usage</span>
                      <span className="text-sm text-white">{(colonyMetrics.resourceUsage * 100).toFixed(0)}%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-neural-500 to-network-500"
                        style={{ width: `${colonyMetrics.resourceUsage * 100}%` }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-400">Memory Usage</span>
                      <span className="text-sm text-white">62%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-organic-500 to-honey-500" style={{ width: '62%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-400">Network I/O</span>
                      <span className="text-sm text-white">38%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-colony-500 to-neural-500" style={{ width: '38%' }} />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Colony Statistics</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <p className="text-2xl font-bold text-white">{colonyMetrics.activeWorkflows}</p>
                    <p className="text-sm text-gray-400">Active Workflows</p>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <p className="text-2xl font-bold text-white">24/7</p>
                    <p className="text-sm text-gray-400">Uptime</p>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <p className="text-2xl font-bold text-white">1.2K</p>
                    <p className="text-sm text-gray-400">API Calls/Day</p>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <p className="text-2xl font-bold text-white">15ms</p>
                    <p className="text-sm text-gray-400">Avg Response</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {selectedView === 'team' && (
          <div className="space-y-6">
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-4">Team Collaboration Hub</h2>
                <div className="text-center py-12">
                  <Users size={64} className="text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">Team features coming soon</p>
                  <p className="text-sm text-gray-500 mt-2">Manage team access, permissions, and shared colonies</p>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default ColonyCommand;