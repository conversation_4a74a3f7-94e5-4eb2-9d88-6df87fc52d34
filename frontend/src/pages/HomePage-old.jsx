import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { 
  Zap, 
  TrendingUp, 
  Users, 
  Mail, 
  Brain, 
  DollarSign,
  Star,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Target,
  Shield,
  Cpu,
  Database,
  Globe,
  Bot,
  Workflow,
  Puzzle,
  Network,
  Layers,
  Rocket
} from 'lucide-react'
import Logo from '../components/Logo'
import AnimatedBackground from '../components/AnimatedBackground'

const HomePage = () => {
  const features = [
    {
      icon: <Bot className="h-8 w-8 text-purple-400" />,
      title: "AI Agent Army",
      description: "Deploy 50+ specialized marketing agents for email generation, optimization, segmentation, and automation workflows."
    },
    {
      icon: <Workflow className="h-8 w-8 text-amber-400" />,
      title: "Visual Workflow Builder",
      description: "Create complex marketing automations with drag-and-drop canvas, just like  but purpose-built for marketing teams."
    },
    {
      icon: <Network className="h-8 w-8 text-purple-400" />,
      title: "Platform Integrations",
      description: "Connect to all major email marketing platforms and 40+ marketing tools with one-click setup."
    },
    {
      icon: <Brain className="h-8 w-8 text-amber-400" />,
      title: "Advanced AI Powered",
      description: "Advanced AI reasoning with marketing-trained prompts for content that converts better than human-written copy."
    },
    {
      icon: <Rocket className="h-8 w-8 text-purple-400" />,
      title: "Agent Marketplace",
      description: "Install 'automation agents' - specialized agents for every marketing need, from community creators worldwide."
    },
    {
      icon: <DollarSign className="h-8 w-8 text-amber-400" />,
      title: "Flexible Billing",
      description: "Usage-based pricing with overage protection, transparent billing, and enterprise volume discounts."
    }
  ]

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Marketing Director",
      company: "TechStart Inc.",
      content: "NeuroColony's AI agents transformed our entire marketing workflow. We now automate everything from email sequences to audience segmentation in one platform.",
      rating: 5
    },
    {
      name: "Marcus Rodriguez",
      role: "Founder",
      company: "GrowthLab",
      content: "The visual workflow builder is incredible. We built complex marketing automations in minutes, not weeks. It's like having  but for marketers.",
      rating: 5
    },
    {
      name: "Emily Watson",
      role: "Head of Growth",
      company: "ScaleUp Co.",
      content: "50+ AI agents at our fingertips plus seamless integrations to all our tools. NeuroColony replaced 5 different marketing platforms for us.",
      rating: 5
    }
  ]

  const plans = [
    {
      name: "Starter",
      price: "$39",
      period: "month",
      features: [
        "10 AI agents included",
        "Basic workflow builder",
        "5 platform integrations",
        "Email sequences + optimization",
        "Community support"
      ],
      popular: false
    },
    {
      name: "Professional", 
      price: "$99",
      period: "month",
      features: [
        "50 AI agents included",
        "Advanced workflow builder",
        "Unlimited integrations",
        "Team collaboration",
        "Priority support"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "$299",
      period: "month",
      features: [
        "Unlimited AI agents",
        "Custom agent development",
        "White-label platform",
        "Enterprise SSO",
        "Dedicated success manager"
      ],
      popular: false
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedBackground />

      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        <div 
          className="absolute inset-0 opacity-20"
          style={{ background: 'var(--gradient-hero)' }}
        />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="mb-8 flex justify-center">
              <Logo className="h-24 w-auto" animated={true} />
            </div>
            
            <div className="mb-6">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                <Bot className="w-4 h-4 mr-2" />
                AI Agent Platform • Visual Workflow Builder
              </span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              The Ultimate <span className="gradient-text">AI Agent Platform</span><br />
              For Marketing Automation
            </h1>
            
            <p className="text-xl md:text-2xl text-neutral-300 mb-8 max-w-3xl mx-auto">
              Deploy 50+ specialized AI agents with visual workflow automation. Like , but purpose-built for marketing teams with Advanced AI intelligence.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/agent-dashboard" className="btn-premium ripple text-lg px-8 py-4 inline-flex items-center">
                Launch AI Agents
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link to="/agent-builder" className="btn btn-outline text-lg px-8 py-4 interactive-glow">
                Build Workflows →
              </Link>
            </div>
            
            <div className="mt-8 text-sm text-neutral-400 flex items-center justify-center gap-6">
              <span className="flex items-center gap-2">
                <Bot className="w-4 h-4 text-purple-400" />
                50+ AI Agents
              </span>
              <span className="flex items-center gap-2">
                <Workflow className="w-4 h-4 text-purple-400" />
                Visual Builder
              </span>
              <span className="flex items-center gap-2">
                <Network className="w-4 h-4 text-purple-400" />
                40+ Integrations
              </span>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-b from-neutral-950 to-neutral-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">AI Agent Platform</span> Features
            </h2>
            <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
              Everything you need to build, deploy, and scale marketing automation workflows with artificial intelligence.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-premium interactive-glow"
              >
                <div className="mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-neutral-400 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text-gold">Real Results</span> From Real Customers
            </h2>
            <p className="text-xl text-neutral-300">
              Join thousands of businesses improving their email marketing results
            </p>
            
            {/* Key Benefits */}
            <div className="grid md:grid-cols-3 gap-8 mt-12 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-4xl font-bold text-purple-400 mb-2">3x</div>
                <div className="text-neutral-400">Better Conversion Rates</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-400 mb-2">75%</div>
                <div className="text-neutral-400">Time Savings</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-400 mb-2">24/7</div>
                <div className="text-neutral-400">AI Automation</div>
              </div>
            </div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card"
              >
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                  ))}
                </div>
                <p className="text-neutral-300 mb-6 italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-neutral-400">
                    {testimonial.role} at {testimonial.company}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 bg-gradient-to-b from-neutral-900 to-neutral-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Professional</span> Pricing Plans
            </h2>
            <p className="text-xl text-neutral-300">
              Choose the plan that fits your business needs. All plans include premium AI models.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`card relative ${plan.popular ? 'border-glow scale-105' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-white mb-4">{plan.name}</h3>
                  
                  <div className="text-6xl font-bold text-white mb-2">{plan.price}</div>
                  <span className="text-neutral-400">/{plan.period}</span>
                  <p className="text-sm text-neutral-400 mt-2">+ $3 per additional sequence</p>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-purple-400 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-neutral-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link 
                  to={`/register?plan=${plan.name.toLowerCase()}`}
                  className={`w-full ${plan.popular ? 'btn btn-primary' : 'btn btn-ghost'}`}
                >
                  {plan.popular ? 'Get Started' : 'Choose Plan'}
                </Link>
                
                {plan.popular && (
                  <p className="text-xs text-center mt-2 text-amber-400">
                     Most popular choice
                  </p>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to <span className="gradient-text">Transform</span> Your Email Marketing?
            </h2>
            <p className="text-xl text-neutral-300 mb-8">
              Join thousands of businesses using AI to create better email sequences.<br />
              Start your free trial today and see the difference.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn btn-primary text-lg px-8 py-4 text-center">
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link to="/contact" className="btn btn-secondary text-lg px-8 py-4">
                Contact Sales
              </Link>
            </div>

            {/* Trust Building */}
            <div className="mt-8 max-w-2xl mx-auto bg-purple-500/10 border border-purple-500/30 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-3"> Our Commitment</h3>
              <div className="text-neutral-300 text-sm space-y-2">
                <p> Professional AI technology</p>
                <p> Cancel anytime, keep your sequences</p>
                <p> Dedicated customer support</p>
                <p> 99.9% uptime reliability</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default HomePage