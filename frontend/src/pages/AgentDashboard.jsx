import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Brain, Hexagon, Network, Activity, Shield, Zap, Crown, GitBranch, Layers, Target, Sparkles } from 'lucide-react'
import { colonyTheme, getTextClass, getButtonClass, getBadgeClass, getCardClass, cn } from '../design-system/colony-theme'
import { Icon, NeuralPattern, ColonyIcon, SwarmIcon, NeuralNetworkIcon } from '../design-system/colony-icons'
import { motion, AnimatePresence } from 'framer-motion'

// NeuroColony Agent Dashboard - Neural Command Center
const AgentDashboard = () => {
  const [agents, setAgents] = useState({})
  const [runningExecutions, setRunningExecutions] = useState([])
  const [colonyStatus, setColonyStatus] = useState({})
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')

  useEffect(() => {
    fetchAgents()
    fetchRunningExecutions()
    fetchColonyStatus()
    
    const interval = setInterval(() => {
      fetchRunningExecutions()
      fetchColonyStatus()
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/agents', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()

      if (data.success) {
        const organizedAgents = data.data.agents.reduce((acc, agent) => {
          const category = agent.category || 'uncategorized'
          if (!acc[category]) acc[category] = []
          acc[category].push(agent)
          return acc
        }, {})
        setAgents(organizedAgents)
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchColonyStatus = async () => {
    try {
      const response = await fetch('/api/colony/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()

      if (data.success) {
        setColonyStatus({
          agentsByType: data.data.agentsByType || [],
          performance: data.data.performance || [],
          activeWorkflows: data.data.activeWorkflows || 0,
          totalExecutions: data.data.totalExecutions || 0,
          successRate: data.data.successRate || 0
        })
      }
    } catch (error) {
      console.error('Failed to fetch colony status:', error)
    }
  }

  const fetchRunningExecutions = async () => {
    try {
      const response = await fetch('/api/agents/executions/running', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setRunningExecutions(data.data.executions)
      }
    } catch (error) {
      console.error('Failed to fetch running executions:', error)
    }
  }

  const executeAgent = async (agentId, inputs = {}) => {
    try {
      const response = await fetch(`/api/agents/${agentId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ inputs })
      })
      const data = await response.json()
      
      if (data.success) {
        fetchRunningExecutions()
      }
    } catch (error) {
      console.error('Failed to execute agent:', error)
    }
  }

  const filteredAgents = selectedCategory === 'all' 
    ? Object.values(agents).flat() 
    : agents[selectedCategory] || []

  // Colony category configurations with proper theming
  const categories = [
    { id: 'all', name: 'All Agents', icon: 'colony', color: 'neural' },
    { id: 'marketing', name: 'Marketing Swarm', icon: 'swarm', color: 'honey' },
    { id: 'analytics', name: 'Analytics Hive', icon: 'analyze', color: 'neural' },
    { id: 'optimization', name: 'Optimization Colony', icon: 'optimize', color: 'swarm' },
    { id: 'automation', name: 'Automation Network', icon: 'network', color: 'neural' }
  ]

  if (loading) {
    return (
      <div className={cn("min-h-screen flex items-center justify-center", colonyTheme.gradients.neural)}>
        <div className="text-center">
          <NeuralNetworkIcon size={48} animate className="mx-auto mb-4" />
          <p className={getTextClass('h5')}>Initializing Neural Colony...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("min-h-screen relative", colonyTheme.gradients.neural)}>
      <NeuralPattern className="opacity-5" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <ColonyIcon size={56} animate />
              <div>
                <h1 className={cn(getTextClass('h1'), "flex items-center gap-3")}>
                  Neural Colony Command
                </h1>
                <p className={getTextClass('bodyLarge')}>
                  Monitor and orchestrate your AI agent swarms
                </p>
              </div>
            </div>
            
            {/* Colony Metrics */}
            <div className="flex items-center gap-6">
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className={cn(getCardClass(), "px-6 py-4")}
              >
                <div className="flex items-center gap-3">
                  <Icon name="statusActive" size={24} />
                  <div>
                    <div className={cn(getTextClass('h3'), "text-swarm-600 dark:text-swarm-400")}>
                      {colonyStatus.activeWorkflows || 0}
                    </div>
                    <div className={getTextClass('meta')}>Active Missions</div>
                  </div>
                </div>
              </motion.div>
              
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className={cn(getCardClass(), "px-6 py-4")}
              >
                <div className="flex items-center gap-3">
                  <Icon name="growth" size={24} />
                  <div>
                    <div className={cn(getTextClass('h3'), "text-neural-600 dark:text-neural-400")}>
                      {colonyStatus.successRate || 0}%
                    </div>
                    <div className={getTextClass('meta')}>Success Rate</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Category Filters */}
          <div className="flex gap-3 flex-wrap">
            {categories.map(category => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedCategory(category.id)}
                className={cn(
                  "px-4 py-2 rounded-lg font-semibold transition-all duration-200",
                  "flex items-center gap-2",
                  selectedCategory === category.id
                    ? cn(getButtonClass('primary'), "shadow-lg")
                    : cn(
                        "bg-white dark:bg-gray-800",
                        "text-gray-700 dark:text-gray-300",
                        "hover:bg-neural-50 dark:hover:bg-gray-700",
                        "border-2 border-gray-300 dark:border-gray-600"
                      )
                )}
              >
                <Icon name={category.icon} size={18} />
                {category.name}
                <span className={cn(
                  "ml-2 px-2 py-0.5 rounded-full text-xs font-medium",
                  selectedCategory === category.id
                    ? "bg-white/20 text-white"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                )}>
                  {category.id === 'all' 
                    ? Object.values(agents).flat().length 
                    : agents[category.id]?.length || 0}
                </span>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Running Executions */}
        {runningExecutions.length > 0 && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h2 className={cn(getTextClass('h4'), "mb-4 flex items-center gap-2")}>
              <Icon name="statusProcessing" size={24} />
              Active Neural Processes
            </h2>
            <div className="grid gap-4">
              {runningExecutions.map((execution, index) => (
                <motion.div
                  key={execution.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={cn(getCardClass(), "p-4")}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon name="worker" size={24} />
                      <div>
                        <p className={getTextClass('h5')}>{execution.agentName}</p>
                        <p className={getTextClass('meta')}>
                          Started {new Date(execution.startTime).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className={getTextClass('meta')}>Progress</p>
                        <div className="w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-gradient-to-r from-neural-500 to-neural-600 transition-all duration-500"
                            style={{ width: `${execution.progress || 0}%` }}
                          />
                        </div>
                      </div>
                      <Badge variant="warning" className="animate-pulse">
                        <Icon name="statusProcessing" size={16} />
                        Processing
                      </Badge>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Agent Grid */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <h2 className={cn(getTextClass('h4'), "mb-6 flex items-center gap-2")}>
            <Icon name="swarm" size={24} />
            Colony Agents
          </h2>
          
          {filteredAgents.length === 0 ? (
            <div className={cn(getCardClass(), "p-12 text-center")}>
              <SwarmIcon size={64} className="mx-auto mb-4 text-gray-400" />
              <p className={getTextClass('h5')}>No agents in this category</p>
              <p className={cn(getTextClass('body'), "mt-2")}>
                Deploy new agents to expand your colony
              </p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence mode="popLayout">
                {filteredAgents.map((agent, index) => (
                  <motion.div
                    key={agent.id}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ y: -4 }}
                    className={cn(getCardClass(true), "overflow-hidden")}
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <div className={cn(
                            "p-3 rounded-lg",
                            "bg-gradient-to-br from-neural-100 to-neural-200",
                            "dark:from-neural-800 dark:to-neural-900"
                          )}>
                            <Icon name={agent.icon || 'worker'} size={24} />
                          </div>
                          <div className="flex-1">
                            <h3 className={getTextClass('h5')}>{agent.name}</h3>
                            <p className={cn(getBadgeClass('neural'), "text-xs mt-1 inline-block")}>
                              {agent.category}
                            </p>
                          </div>
                        </div>
                        <Badge 
                          variant={agent.status === 'active' ? 'success' : 'default'}
                          className="flex items-center gap-1"
                        >
                          <Icon 
                            name={agent.status === 'active' ? 'statusActive' : 'statusIdle'} 
                            size={12} 
                          />
                          {agent.status || 'Ready'}
                        </Badge>
                      </div>
                      
                      <p className={cn(getTextClass('body'), "mb-4")}>
                        {agent.description}
                      </p>
                      
                      {/* Agent Stats */}
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                          <p className={cn(getTextClass('h5'), "text-neural-600 dark:text-neural-400")}>
                            {agent.executions || 0}
                          </p>
                          <p className={getTextClass('metaSmall')}>Runs</p>
                        </div>
                        <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                          <p className={cn(getTextClass('h5'), "text-swarm-600 dark:text-swarm-400")}>
                            {agent.successRate || 95}%
                          </p>
                          <p className={getTextClass('metaSmall')}>Success</p>
                        </div>
                        <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                          <p className={cn(getTextClass('h5'), "text-honey-600 dark:text-honey-400")}>
                            {agent.avgTime || '2.3'}s
                          </p>
                          <p className={getTextClass('metaSmall')}>Avg Time</p>
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        <button
                          onClick={() => executeAgent(agent.id)}
                          className={cn(
                            getButtonClass('primary'),
                            "flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-2"
                          )}
                        >
                          <Zap className="w-4 h-4" />
                          Deploy Agent
                        </button>
                        <button
                          className={cn(
                            getButtonClass('secondary'),
                            "px-3 py-2 rounded-lg text-sm"
                          )}
                        >
                          <Shield className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </motion.div>

        {/* Colony Performance Metrics */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-12"
        >
          <h2 className={cn(getTextClass('h4'), "mb-6 flex items-center gap-2")}>
            <Icon name="intelligence" size={24} />
            Colony Intelligence Metrics
          </h2>
          
          <div className="grid md:grid-cols-4 gap-6">
            <motion.div whileHover={{ scale: 1.05 }} className={cn(getCardClass(), "p-6 text-center")}>
              <Icon name="colony" size={48} className="mx-auto mb-3" />
              <p className={cn(getTextClass('h2'), "text-neural-600 dark:text-neural-400")}>
                {colonyStatus.totalExecutions || 0}
              </p>
              <p className={getTextClass('meta')}>Total Missions</p>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className={cn(getCardClass(), "p-6 text-center")}>
              <Icon name="swarm" size={48} className="mx-auto mb-3" />
              <p className={cn(getTextClass('h2'), "text-swarm-600 dark:text-swarm-400")}>
                {Object.values(agents).flat().length}
              </p>
              <p className={getTextClass('meta')}>Active Agents</p>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className={cn(getCardClass(), "p-6 text-center")}>
              <Icon name="growth" size={48} className="mx-auto mb-3" />
              <p className={cn(getTextClass('h2'), "text-honey-600 dark:text-honey-400")}>
                {colonyStatus.successRate || 0}%
              </p>
              <p className={getTextClass('meta')}>Success Rate</p>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className={cn(getCardClass(), "p-6 text-center")}>
              <Icon name="neural" size={48} className="mx-auto mb-3" />
              <p className={cn(getTextClass('h2'), "text-neural-600 dark:text-neural-400")}>
                24/7
              </p>
              <p className={getTextClass('meta')}>Colony Uptime</p>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default AgentDashboard