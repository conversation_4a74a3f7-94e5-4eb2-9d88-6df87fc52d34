import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const ContactPage = () => {
  const [activeTab, setActiveTab] = useState('support');
  const [contactInfo, setContactInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Support form state
  const [supportForm, setSupportForm] = useState({
    name: '',
    email: '',
    issueType: '',
    subject: '',
    message: ''
  });

  // Contact form state
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    company: '',
    contactReason: '',
    message: ''
  });

  useEffect(() => {
    fetchContactInfo();
    
    // Pre-fill user info if logged in
    if (user) {
      setSupportForm(prev => ({
        ...prev,
        name: user.name || '',
        email: user.email || ''
      }));
      setContactForm(prev => ({
        ...prev,
        name: user.name || '',
        email: user.email || ''
      }));
    }
  }, [user]);

  const fetchContactInfo = async () => {
    try {
      const response = await fetch('/api/contact/info');
      const data = await response.json();
      if (data.success) {
        setContactInfo(data.data);
      }
    } catch (error) {
      console.error('Error fetching contact info:', error);
    }
  };

  const handleSupportSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/contact/support', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { 'Authorization': `Bearer ${localStorage.getItem('token')}` })
        },
        body: JSON.stringify(supportForm)
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess(true);
        setSupportForm({
          name: user?.name || '',
          email: user?.email || '',
          issueType: '',
          subject: '',
          message: ''
        });
      } else {
        setError(data.error || 'Failed to submit support ticket');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/contact/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contactForm)
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess(true);
        setContactForm({
          name: '',
          email: '',
          company: '',
          contactReason: '',
          message: ''
        });
      } else {
        setError(data.error || 'Failed to send message');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 flex items-center justify-center p-6">
        <div className="max-w-lg w-full">
          <div className="glass rounded-2xl p-8 text-center fade-in">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            <h2 className="text-2xl font-bold text-white mb-4">
              Message Sent Successfully!
            </h2>
            
            <p className="text-neutral-300 mb-6">
              Thank you for reaching out. We've received your {activeTab === 'support' ? 'support ticket' : 'message'} and will get back to you soon.
            </p>
            
            <div className="space-y-3">
              <button
                onClick={() => {
                  setSuccess(false);
                  setActiveTab('support');
                }}
                className="btn btn-primary w-full"
              >
                Submit Another Request
              </button>
              
              <a
                href="/dashboard"
                className="btn btn-ghost w-full"
              >
                Return to Dashboard
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/20 to-purple-800/20 border-b border-purple-500/20">
        <div className="max-w-4xl mx-auto px-6 py-12">
          <h1 className="text-4xl font-bold gradient-text mb-4">
            Get in Touch
          </h1>
          <p className="text-neutral-300 text-lg">
            We're here to help. Choose how you'd like to reach us.
          </p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-neutral-900/50 p-1 rounded-xl">
            <button
              onClick={() => setActiveTab('support')}
              className={`flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'support'
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                  : 'text-neutral-400 hover:text-white hover:bg-neutral-800/50'
              }`}
            >
              🛟 Get Help
            </button>
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'contact'
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                  : 'text-neutral-400 hover:text-white hover:bg-neutral-800/50'
              }`}
            >
              💼 Contact Us
            </button>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            {activeTab === 'support' ? (
              <SupportForm
                form={supportForm}
                setForm={setSupportForm}
                onSubmit={handleSupportSubmit}
                loading={loading}
                error={error}
                categories={contactInfo?.supportCategories || []}
              />
            ) : (
              <ContactForm
                form={contactForm}
                setForm={setContactForm}
                onSubmit={handleContactSubmit}
                loading={loading}
                error={error}
                reasons={contactInfo?.contactReasons || []}
              />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Response Time */}
            <div className="glass rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                 Response Time
              </h3>
              <div className="space-y-3 text-sm">
                {contactInfo?.responseTime && Object.entries(contactInfo.responseTime).map(([plan, time]) => (
                  <div key={plan} className="flex justify-between">
                    <span className="text-neutral-400 capitalize">{plan}:</span>
                    <span className={`font-medium ${
                      user?.subscriptionPlan === plan ? 'text-purple-400' : 'text-neutral-300'
                    }`}>
                      {time}
                    </span>
                  </div>
                ))}
              </div>
              
              {(!user || user.subscriptionPlan === 'free') && (
                <div className="mt-4 pt-4 border-t border-neutral-700">
                  <a
                    href="/pricing"
                    className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                  >
                    Upgrade for faster support →
                  </a>
                </div>
              )}
            </div>

            {/* FAQ */}
            <div className="glass rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                ❓ Quick Answers
              </h3>
              <div className="space-y-4">
                {contactInfo?.faq?.slice(0, 3).map((item, index) => (
                  <div key={index}>
                    <h4 className="text-sm font-medium text-white mb-1">
                      {item.question}
                    </h4>
                    <p className="text-xs text-neutral-400">
                      {item.answer}
                    </p>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 pt-4 border-t border-neutral-700">
                <a
                  href="/help"
                  className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                >
                  View all FAQs →
                </a>
              </div>
            </div>

            {/* Status */}
            <div className="glass rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                 System Status
              </h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-neutral-300">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Support Form Component
const SupportForm = ({ form, setForm, onSubmit, loading, error, categories }) => {
  return (
    <div className="glass rounded-xl p-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">
          🛟 Get Help
        </h2>
        <p className="text-neutral-400">
          Describe your issue and we'll help you resolve it quickly.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={onSubmit} className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">Name *</label>
            <input
              type="text"
              value={form.name}
              onChange={(e) => setForm({ ...form, name: e.target.value })}
              className="form-input"
              required
            />
          </div>
          <div>
            <label className="form-label">Email *</label>
            <input
              type="email"
              value={form.email}
              onChange={(e) => setForm({ ...form, email: e.target.value })}
              className="form-input"
              required
            />
          </div>
        </div>

        <div>
          <label className="form-label">Issue Category *</label>
          <select
            value={form.issueType}
            onChange={(e) => setForm({ ...form, issueType: e.target.value })}
            className="form-input"
            required
          >
            <option value="">Select a category...</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.title}
              </option>
            ))}
          </select>
          {form.issueType && (
            <p className="mt-1 text-xs text-neutral-400">
              {categories.find(c => c.id === form.issueType)?.description}
            </p>
          )}
        </div>

        <div>
          <label className="form-label">Subject *</label>
          <input
            type="text"
            value={form.subject}
            onChange={(e) => setForm({ ...form, subject: e.target.value })}
            className="form-input"
            placeholder="Brief description of your issue"
            required
          />
        </div>

        <div>
          <label className="form-label">Message *</label>
          <textarea
            value={form.message}
            onChange={(e) => setForm({ ...form, message: e.target.value })}
            className="form-input min-h-[120px] resize-none"
            placeholder="Please provide as much detail as possible to help us assist you better..."
            maxLength={2000}
            required
          />
          <div className="mt-1 text-xs text-neutral-500 text-right">
            {form.message.length}/2000 characters
          </div>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="btn btn-primary w-full"
        >
          {loading ? (
            <>
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              Submitting...
            </>
          ) : (
            'Submit Support Ticket'
          )}
        </button>
      </form>
    </div>
  );
};

// Contact Form Component  
const ContactForm = ({ form, setForm, onSubmit, loading, error, reasons }) => {
  return (
    <div className="glass rounded-xl p-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">
          💼 Contact Us
        </h2>
        <p className="text-neutral-400">
          Get in touch for business inquiries, partnerships, or general questions.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={onSubmit} className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">Name *</label>
            <input
              type="text"
              value={form.name}
              onChange={(e) => setForm({ ...form, name: e.target.value })}
              className="form-input"
              required
            />
          </div>
          <div>
            <label className="form-label">Email *</label>
            <input
              type="email"
              value={form.email}
              onChange={(e) => setForm({ ...form, email: e.target.value })}
              className="form-input"
              required
            />
          </div>
        </div>

        <div>
          <label className="form-label">Company</label>
          <input
            type="text"
            value={form.company}
            onChange={(e) => setForm({ ...form, company: e.target.value })}
            className="form-input"
            placeholder="Your company name (optional)"
          />
        </div>

        <div>
          <label className="form-label">Contact Reason *</label>
          <select
            value={form.contactReason}
            onChange={(e) => setForm({ ...form, contactReason: e.target.value })}
            className="form-input"
            required
          >
            <option value="">Select a reason...</option>
            {reasons.map((reason) => (
              <option key={reason.id} value={reason.id}>
                {reason.title}
              </option>
            ))}
          </select>
          {form.contactReason && (
            <p className="mt-1 text-xs text-neutral-400">
              {reasons.find(r => r.id === form.contactReason)?.description}
            </p>
          )}
        </div>

        <div>
          <label className="form-label">Message *</label>
          <textarea
            value={form.message}
            onChange={(e) => setForm({ ...form, message: e.target.value })}
            className="form-input min-h-[120px] resize-none"
            placeholder="Tell us more about your inquiry..."
            maxLength={2000}
            required
          />
          <div className="mt-1 text-xs text-neutral-500 text-right">
            {form.message.length}/2000 characters
          </div>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="btn btn-secondary w-full"
        >
          {loading ? (
            <>
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              Sending...
            </>
          ) : (
            'Send Message'
          )}
        </button>
      </form>
    </div>
  );
};

export default ContactPage;