import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Spinner } from '../components/ui/Spinner'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Crown,
  Users,
  Search,
  Zap,
  TrendingUp,
  Activity,
  Plus,
  Play,
  Settings,
  BarChart3,
  MessageSquare,
  Workflow,
  Mail,
  Target,
  Brain,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'

/**
 * NeuroColony Colony Intelligence Dashboard
 * Comprehensive overview of Queen/Worker/Scout agent ecosystem
 * Marketing-first approach with real-time colony performance
 */
const ColonyDashboard = () => {
  const [colonyStatus, setColonyStatus] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [recentActivity, setRecentActivity] = useState([])
  const [performanceMetrics, setPerformanceMetrics] = useState({})

  useEffect(() => {
    fetchColonyStatus()
    fetchRecentActivity()
    
    // Poll for updates every 30 seconds
    const interval = setInterval(() => {
      fetchColonyStatus()
      fetchRecentActivity()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [])

  const fetchColonyStatus = async () => {
    try {
      const response = await fetch('/api/colony/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setColonyStatus(data.data)
        setPerformanceMetrics(data.data.performance || {})
      }
    } catch (error) {
      console.error('Failed to fetch colony status:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRecentActivity = async () => {
    try {
      const response = await fetch('/api/colony/agents?limit=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setRecentActivity(data.data.agents || [])
      }
    } catch (error) {
      console.error('Failed to fetch recent activity:', error)
    }
  }

  const createNewColony = async () => {
    try {
      const response = await fetch('/api/colony/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          useTemplates: true,
          marketingContext: {
            industry: 'general',
            targetAudience: 'small_business',
            objectives: ['lead_generation', 'email_marketing', 'conversion_optimization']
          }
        })
      })
      
      const data = await response.json()
      if (data.success) {
        // Refresh colony status
        fetchColonyStatus()
        alert('Marketing colony created successfully!')
      }
    } catch (error) {
      console.error('Failed to create colony:', error)
      alert('Failed to create colony. Please try again.')
    }
  }

  const getAgentTypeIcon = (agentType) => {
    const icons = {
      queen: Crown,
      worker: Users,
      scout: Search
    }
    return icons[agentType] || Activity
  }

  const getAgentTypeColor = (agentType) => {
    const colors = {
      queen: 'from-purple-500 to-pink-500',
      worker: 'from-blue-500 to-cyan-500',
      scout: 'from-green-500 to-emerald-500'
    }
    return colors[agentType] || 'from-gray-500 to-gray-600'
  }

  const getCategoryIcon = (category) => {
    const icons = {
      email_marketing: Mail,
      content_creation: MessageSquare,
      analytics_reporting: BarChart3,
      lead_generation: Target,
      ai_orchestration: Brain
    }
    return icons[category] || Activity
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                🐜 NeuroColony Intelligence
              </h1>
              <p className="text-gray-600 text-lg">
                AI Agent Platform That Surpasses N8N • Marketing-First Colony Architecture
              </p>
            </div>
            <div className="flex gap-4">
              <Button
                onClick={createNewColony}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Marketing Colony
              </Button>
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Colony Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {colonyStatus?.agentsByType?.map((agentType) => {
            const Icon = getAgentTypeIcon(agentType._id)
            const colorClass = getAgentTypeColor(agentType._id)
            
            return (
              <motion.div
                key={agentType._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="relative overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${colorClass} opacity-10`} />
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium capitalize">
                      {agentType._id} Agents
                    </CardTitle>
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{agentType.count}</div>
                    <p className="text-xs text-muted-foreground">
                      {agentType.avgSuccessRate?.toFixed(1) || 0}% avg success rate
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500 opacity-10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Workflows
                </CardTitle>
                <Workflow className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{colonyStatus?.activeWorkflows || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Running automations
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-200 p-1 rounded-lg w-fit">
            {['overview', 'agents', 'performance', 'activity'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors capitalize ${
                  activeTab === tab
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              {/* Colony Hierarchy Visualization */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Crown className="w-5 h-5 text-purple-500" />
                    Colony Hierarchy
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {colonyStatus?.agentsByType?.map((agentType) => (
                      <div key={agentType._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          {React.createElement(getAgentTypeIcon(agentType._id), {
                            className: `w-5 h-5 ${
                              agentType._id === 'queen' ? 'text-purple-500' :
                              agentType._id === 'worker' ? 'text-blue-500' : 'text-green-500'
                            }`
                          })}
                          <div>
                            <p className="font-medium capitalize">{agentType._id} Agents</p>
                            <p className="text-sm text-gray-600">{agentType.count} active</p>
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {agentType.avgSuccessRate?.toFixed(1) || 0}% success
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Executions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5 text-blue-500" />
                    Recent Executions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {colonyStatus?.recentExecutions?.slice(0, 5).map((execution, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          {React.createElement(getCategoryIcon(execution.agent?.category), {
                            className: "w-4 h-4 text-gray-600"
                          })}
                          <div>
                            <p className="font-medium text-sm">{execution.agent?.name}</p>
                            <p className="text-xs text-gray-600">
                              {new Date(execution.createdAt).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {execution.status === 'completed' ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : execution.status === 'failed' ? (
                            <AlertCircle className="w-4 h-4 text-red-500" />
                          ) : (
                            <Clock className="w-4 h-4 text-yellow-500" />
                          )}
                          <Badge variant={execution.status === 'completed' ? 'success' : 'secondary'}>
                            {execution.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Quick Actions */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button className="h-20 flex flex-col items-center justify-center gap-2">
                  <Plus className="w-6 h-6" />
                  <span>Create Agent</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                  <Workflow className="w-6 h-6" />
                  <span>Build Workflow</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                  <BarChart3 className="w-6 h-6" />
                  <span>View Analytics</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default ColonyDashboard
