import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { 
  Brain, 
  TrendingUp, 
  Zap, 
  Target, 
  DollarSign, 
  Users, 
  Mail, 
  BarChart3,
  Plus,
  ArrowRight,
  Sparkles,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { EnhancedCard, EnhancedButton, StatsCard } from '../components/EnhancedDesignSystem'
import AnalyticsDashboard from '../components/AnalyticsDashboard'
import PricingOptimizer from '../components/PricingOptimizer'

const BusinessDashboard = () => {
  const [user, setUser] = useState(null)
  const [usage, setUsage] = useState(null)
  const [recentSequences, setRecentSequences] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      const token = localStorage.getItem('token')
      
      // Load user profile
      const profileResponse = await fetch(`${import.meta.env.VITE_API_URL}/auth/profile`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (profileResponse.ok) {
        const profileData = await profileResponse.json()
        setUser(profileData.user)
      } else {
        // Fallback user data
        setUser({ name: 'Demo User', email: '<EMAIL>' })
      }

      // Load usage data
      const usageResponse = await fetch(`${import.meta.env.VITE_API_URL}/usage`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.usage)
      } else {
        // Fallback usage data
        setUsage({
          current_usage: 12,
          plan_limit: 75,
          overage_enabled: false,
          overage_count: 0
        })
      }

      // Load recent sequences
      const sequencesResponse = await fetch(`${import.meta.env.VITE_API_URL}/sequences?limit=5`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (sequencesResponse.ok) {
        const sequencesData = await sequencesResponse.json()
        setRecentSequences(sequencesData.sequences || [])
      } else {
        // Fallback sequences data
        setRecentSequences([
          {
            id: 1,
            title: 'SaaS Onboarding Sequence',
            description: 'Professional onboarding for trial users',
            industry: 'saas',
            length: 5,
            status: 'active',
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            title: 'E-commerce Welcome Series',
            description: 'New customer welcome sequence',
            industry: 'ecommerce',
            length: 3,
            status: 'draft',
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ])
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      // Set fallback data to prevent crashes
      setUser({ name: 'Demo User', email: '<EMAIL>' })
      setUsage({
        current_usage: 12,
        plan_limit: 75,
        overage_enabled: false,
        overage_count: 0
      })
      setRecentSequences([
        {
          id: 1,
          title: 'Demo SaaS Sequence',
          description: 'Example sequence for demonstration',
          industry: 'saas',
          length: 5,
          status: 'active',
          created_at: new Date().toISOString()
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const getUsagePercentage = () => {
    if (!usage) return 0
    return Math.round((usage.current_usage / usage.plan_limit) * 100)
  }

  const getUsageStatus = () => {
    const percentage = getUsagePercentage()
    if (percentage >= 95) return { status: 'critical', color: 'red', message: 'Critical: Near limit' }
    if (percentage >= 80) return { status: 'warning', color: 'amber', message: 'Warning: High usage' }
    return { status: 'normal', color: 'green', message: 'Normal usage' }
  }

  const quickActions = [
    {
      title: 'Enhanced Generator',
      description: 'Create AI-optimized sequences with industry targeting',
      icon: Brain,
      href: '/generator-enhanced',
      color: 'purple',
      badge: 'New'
    },
    {
      title: 'Pricing Calculator',
      description: 'Calculate ROI and revenue potential',
      icon: DollarSign,
      href: '#pricing',
      color: 'green',
      onClick: () => setActiveTab('pricing')
    },
    {
      title: 'Analytics Deep Dive',
      description: 'Comprehensive sequence performance insights',
      icon: BarChart3,
      href: '#analytics',
      color: 'blue',
      onClick: () => setActiveTab('analytics')
    },
    {
      title: 'Ultra Generator',
      description: 'Premium AI models with advanced features',
      icon: Sparkles,
      href: '/generator-ultra',
      color: 'amber',
      badge: 'Premium'
    }
  ]

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Target },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'pricing', name: 'ROI Calculator', icon: DollarSign }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-neutral-400 text-lg">Loading your business dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                Business <span className="gradient-text">Dashboard</span>
              </h1>
              <p className="text-xl text-neutral-300">
                Welcome back, {user?.name || 'there'}! Here's your business performance overview.
              </p>
            </div>
            
            <Link to="/generator-enhanced">
              <EnhancedButton variant="primary" size="large" icon={Plus}>
                Create Sequence
              </EnhancedButton>
            </Link>
          </div>

          {/* Tab Navigation */}
          <div className="flex bg-neutral-800/50 rounded-xl p-1 backdrop-blur-sm">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg'
                      : 'text-neutral-400 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              )
            })}
          </div>
        </motion.div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid md:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <StatsCard
                  value={usage?.current_usage || 0}
                  label="Sequences Used"
                  trend={`${usage?.plan_limit - (usage?.current_usage || 0)} remaining`}
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <StatsCard
                  value={recentSequences.length}
                  label="Recent Sequences"
                  trend="this period"
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <StatsCard
                  value="340%"
                  label="Avg Conversion Boost"
                  trend="vs traditional methods"
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <StatsCard
                  value="2.1s"
                  label="Avg Generation Time"
                  trend="enterprise performance"
                />
              </motion.div>
            </div>

            {/* Usage Status */}
            {usage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <EnhancedCard>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-white">Usage Status</h3>
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                      getUsageStatus().color === 'red' ? 'bg-red-500/20 text-red-400' :
                      getUsageStatus().color === 'amber' ? 'bg-amber-500/20 text-amber-400' :
                      'bg-green-500/20 text-green-400'
                    }`}>
                      {getUsageStatus().color === 'red' ? <AlertTriangle className="w-4 h-4" /> :
                       getUsageStatus().color === 'amber' ? <Clock className="w-4 h-4" /> :
                       <CheckCircle className="w-4 h-4" />}
                      {getUsageStatus().message}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm text-neutral-400 mb-2">
                        <span>Current Usage</span>
                        <span>{usage.current_usage} / {usage.plan_limit} sequences</span>
                      </div>
                      <div className="w-full bg-neutral-700 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-300 ${
                            getUsageStatus().color === 'red' ? 'bg-gradient-to-r from-red-500 to-red-600' :
                            getUsageStatus().color === 'amber' ? 'bg-gradient-to-r from-amber-500 to-amber-600' :
                            'bg-gradient-to-r from-green-500 to-green-600'
                          }`}
                          style={{ width: `${Math.min(getUsagePercentage(), 100)}%` }}
                        />
                      </div>
                    </div>
                    
                    {usage.overage_enabled && usage.overage_count > 0 && (
                      <div className="bg-amber-500/10 border border-amber-500/30 rounded-lg p-4">
                        <div className="flex items-center gap-2 text-amber-400 font-medium">
                          <DollarSign className="w-4 h-4" />
                          Overage Usage: {usage.overage_count} sequences (${(usage.overage_count * 3).toFixed(2)})
                        </div>
                      </div>
                    )}
                  </div>
                </EnhancedCard>
              </motion.div>
            )}

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <EnhancedCard>
                <h3 className="text-xl font-bold text-white mb-6">Quick Actions</h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon
                    return (
                      <Link
                        key={index}
                        to={action.href}
                        onClick={action.onClick}
                        className="group p-6 bg-neutral-800/50 hover:bg-neutral-700/50 border border-neutral-700 hover:border-purple-500/50 rounded-xl transition-all duration-300"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className={`p-3 rounded-lg bg-gradient-to-r ${
                            action.color === 'purple' ? 'from-purple-500 to-purple-600' :
                            action.color === 'green' ? 'from-green-500 to-green-600' :
                            action.color === 'blue' ? 'from-blue-500 to-blue-600' :
                            'from-amber-500 to-amber-600'
                          }`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          {action.badge && (
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              action.badge === 'New' ? 'bg-green-500/20 text-green-400' :
                              'bg-purple-500/20 text-purple-400'
                            }`}>
                              {action.badge}
                            </span>
                          )}
                        </div>
                        <h4 className="font-semibold text-white group-hover:text-purple-300 transition-colors mb-2">
                          {action.title}
                        </h4>
                        <p className="text-neutral-400 text-sm group-hover:text-neutral-300 transition-colors">
                          {action.description}
                        </p>
                      </Link>
                    )
                  })}
                </div>
              </EnhancedCard>
            </motion.div>

            {/* Recent Sequences */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <EnhancedCard>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white">Recent Sequences</h3>
                  <Link to="/sequences" className="text-purple-400 hover:text-purple-300 transition-colors">
                    View All →
                  </Link>
                </div>
                
                {recentSequences.length > 0 ? (
                  <div className="space-y-4">
                    {recentSequences.map((sequence, index) => (
                      <div key={sequence.id} className="p-4 bg-neutral-800/50 rounded-lg border border-neutral-700">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-white mb-1">{sequence.title}</h4>
                            <p className="text-neutral-400 text-sm">{sequence.description}</p>
                            <div className="flex items-center gap-4 mt-2 text-xs text-neutral-500">
                              <span>Industry: {sequence.industry}</span>
                              <span>Length: {sequence.length} emails</span>
                              <span>Created: {new Date(sequence.created_at).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              sequence.status === 'active' ? 'bg-green-500/20 text-green-400' :
                              sequence.status === 'draft' ? 'bg-amber-500/20 text-amber-400' :
                              'bg-neutral-500/20 text-neutral-400'
                            }`}>
                              {sequence.status}
                            </span>
                            <Link 
                              to={`/sequences/${sequence.id}`}
                              className="p-2 text-purple-400 hover:text-purple-300 transition-colors"
                            >
                              <ArrowRight className="w-4 h-4" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Mail className="w-16 h-16 text-neutral-600 mx-auto mb-4" />
                    <p className="text-neutral-400 text-lg mb-4">No sequences yet</p>
                    <Link to="/generator-enhanced">
                      <EnhancedButton variant="primary" icon={Plus}>
                        Create Your First Sequence
                      </EnhancedButton>
                    </Link>
                  </div>
                )}
              </EnhancedCard>
            </motion.div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <AnalyticsDashboard />
          </motion.div>
        )}

        {activeTab === 'pricing' && (
          <motion.div
            key="pricing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <PricingOptimizer />
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default BusinessDashboard