import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Users,
  Activity,
  Server,
  Zap,
  AlertCircle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Clock,
  BarChart3,
  Network,
  Shield,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Eye,
  ChevronRight,
  Crown,
  Bot,
  Search,
  Plus,
  Cpu,
  HardDrive,
  Wifi,
  GitBranch,
  Layers,
  Target,
  Workflow
} from 'lucide-react';
import io from 'socket.io-client';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api';

const ColonyCommandCenter = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [socket, setSocket] = useState(null);

  // Colony state
  const [colonies, setColonies] = useState([]);
  const [selectedColony, setSelectedColony] = useState(null);
  const [colonyHealth, setColonyHealth] = useState(null);
  const [agents, setAgents] = useState({ queens: [], workers: [], scouts: [] });
  const [workflows, setWorkflows] = useState([]);
  const [executions, setExecutions] = useState([]);

  // Real-time metrics
  const [metrics, setMetrics] = useState({
    totalExecutions: 0,
    successRate: 95,
    avgResponseTime: 250,
    activeAgents: 0,
    queueDepth: { queen: 0, worker: 0, scout: 0, priority: 0 }
  });

  // Performance data
  const [performanceData, setPerformanceData] = useState([]);
  const [resourceData, setResourceData] = useState([]);
  const [agentMetrics, setAgentMetrics] = useState([]);

  // UI state
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAgentDetails, setShowAgentDetails] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [isPaused, setIsPaused] = useState(false);

  // Initialize WebSocket connection
  useEffect(() => {
    const socketUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:5002';
    const newSocket = io(socketUrl, {
      auth: { token: localStorage.getItem('token') }
    });

    newSocket.on('connect', () => {
      console.log('Connected to Colony Intelligence');
    });

    newSocket.on('colony:health:checked', (data) => {
      if (data.colonyId === selectedColony?.id) {
        setColonyHealth(data.health);
      }
    });

    newSocket.on('metrics:updated', (data) => {
      setMetrics(prev => ({ ...prev, ...data }));
    });

    newSocket.on('agent:created', (data) => {
      if (data.colonyId === selectedColony?.id) {
        fetchAgents();
      }
    });

    newSocket.on('execution:started', (data) => {
      setExecutions(prev => [data, ...prev.slice(0, 49)]);
    });

    newSocket.on('execution:completed', (data) => {
      setExecutions(prev => 
        prev.map(e => e.executionId === data.executionId ? { ...e, ...data } : e)
      );
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [selectedColony]);

  // Fetch colonies
  const fetchColonies = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/colony/list`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setColonies(data.colonies || []);
      
      if (data.colonies.length > 0 && !selectedColony) {
        setSelectedColony(data.colonies[0]);
      }
    } catch (error) {
      console.error('Failed to fetch colonies:', error);
    }
  }, [selectedColony]);

  // Fetch colony health
  const fetchColonyHealth = useCallback(async () => {
    if (!selectedColony) return;

    try {
      const response = await fetch(`${API_URL}/colony/${selectedColony.id}/health`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setColonyHealth(data);
    } catch (error) {
      console.error('Failed to fetch colony health:', error);
    }
  }, [selectedColony]);

  // Fetch agents
  const fetchAgents = useCallback(async () => {
    if (!selectedColony) return;

    try {
      const response = await fetch(`${API_URL}/colony/${selectedColony.id}/agents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setAgents(data.agents || { queens: [], workers: [], scouts: [] });
    } catch (error) {
      console.error('Failed to fetch agents:', error);
    }
  }, [selectedColony]);

  // Fetch workflows
  const fetchWorkflows = useCallback(async () => {
    if (!selectedColony) return;

    try {
      const response = await fetch(`${API_URL}/workflows?colonyId=${selectedColony.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (error) {
      console.error('Failed to fetch workflows:', error);
    }
  }, [selectedColony]);

  // Fetch executions
  const fetchExecutions = useCallback(async () => {
    if (!selectedColony) return;

    try {
      const response = await fetch(`${API_URL}/colony/${selectedColony.id}/executions?limit=50`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setExecutions(data.executions || []);
    } catch (error) {
      console.error('Failed to fetch executions:', error);
    }
  }, [selectedColony]);

  // Generate performance data
  useEffect(() => {
    const generateData = () => {
      const now = new Date();
      const newPoint = {
        time: now.toLocaleTimeString(),
        executions: Math.floor(Math.random() * 50) + metrics.totalExecutions % 100,
        successRate: Math.max(85, Math.min(100, metrics.successRate + (Math.random() - 0.5) * 5)),
        responseTime: Math.max(100, Math.min(500, metrics.avgResponseTime + (Math.random() - 0.5) * 50))
      };

      setPerformanceData(prev => [...prev.slice(-19), newPoint]);

      // Update resource data
      setResourceData([
        { name: 'CPU', value: Math.random() * 30 + 40, limit: 80 },
        { name: 'Memory', value: Math.random() * 1000 + 2000, limit: 4096 },
        { name: 'Queue Depth', value: Object.values(metrics.queueDepth).reduce((a, b) => a + b, 0), limit: 100 }
      ]);

      // Update agent metrics
      setAgentMetrics([
        { type: 'Queens', active: agents.queens.length, performance: 95 },
        { type: 'Workers', active: agents.workers.length, performance: 88 },
        { type: 'Scouts', active: agents.scouts.length, performance: 92 }
      ]);
    };

    if (!isPaused) {
      const interval = setInterval(generateData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [metrics, agents, refreshInterval, isPaused]);

  // Initial data fetch
  useEffect(() => {
    setLoading(true);
    Promise.all([
      fetchColonies(),
      fetchColonyHealth(),
      fetchAgents(),
      fetchWorkflows(),
      fetchExecutions()
    ]).finally(() => setLoading(false));
  }, [fetchColonies, fetchColonyHealth, fetchAgents, fetchWorkflows, fetchExecutions]);

  // Refresh data periodically
  useEffect(() => {
    if (!isPaused && selectedColony) {
      const interval = setInterval(() => {
        fetchColonyHealth();
        fetchAgents();
        fetchExecutions();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [selectedColony, refreshInterval, isPaused, fetchColonyHealth, fetchAgents, fetchExecutions]);

  // Create new colony
  const handleCreateColony = async (colonyData) => {
    try {
      const response = await fetch(`${API_URL}/colony/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(colonyData)
      });
      
      if (response.ok) {
        const newColony = await response.json();
        setColonies(prev => [...prev, newColony]);
        setSelectedColony(newColony);
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('Failed to create colony:', error);
    }
  };

  // Execute workflow
  const handleExecuteWorkflow = async (workflowId) => {
    try {
      const response = await fetch(`${API_URL}/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ inputs: {} })
      });
      
      if (response.ok) {
        const execution = await response.json();
        console.log('Workflow execution started:', execution);
      }
    } catch (error) {
      console.error('Failed to execute workflow:', error);
    }
  };

  // Health status component
  const HealthIndicator = ({ status, score }) => {
    const colors = {
      excellent: 'text-green-500',
      healthy: 'text-green-400',
      warning: 'text-yellow-500',
      degraded: 'text-orange-500',
      critical: 'text-red-500'
    };

    const icons = {
      excellent: CheckCircle,
      healthy: CheckCircle,
      warning: AlertCircle,
      degraded: AlertCircle,
      critical: XCircle
    };

    const Icon = icons[status] || AlertCircle;

    return (
      <div className="flex items-center space-x-2">
        <Icon className={`w-5 h-5 ${colors[status]}`} />
        <span className={`font-semibold ${colors[status]}`}>
          {status.charAt(0).toUpperCase() + status.slice(1)} ({score}%)
        </span>
      </div>
    );
  };

  // Agent card component
  const AgentCard = ({ agent, type }) => {
    const typeIcons = {
      queen: Crown,
      worker: Bot,
      scout: Search
    };

    const TypeIcon = typeIcons[type] || Bot;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors cursor-pointer"
        onClick={() => setShowAgentDetails(agent)}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              type === 'queen' ? 'bg-purple-500/20' :
              type === 'worker' ? 'bg-blue-500/20' :
              'bg-green-500/20'
            }`}>
              <TypeIcon className={`w-5 h-5 ${
                type === 'queen' ? 'text-purple-400' :
                type === 'worker' ? 'text-blue-400' :
                'text-green-400'
              }`} />
            </div>
            <div>
              <h4 className="font-semibold text-white">{agent.name}</h4>
              <p className="text-sm text-gray-400">{agent.category}</p>
            </div>
          </div>
          <div className={`px-2 py-1 rounded text-xs ${
            agent.status === 'active' ? 'bg-green-500/20 text-green-400' :
            agent.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' :
            'bg-gray-600 text-gray-400'
          }`}>
            {agent.status}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Success Rate</span>
            <span className="text-white font-medium">{agent.metrics?.successRate || 0}%</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Executions</span>
            <span className="text-white font-medium">{agent.metrics?.executions || 0}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Avg Time</span>
            <span className="text-white font-medium">{agent.metrics?.avgExecutionTime || 0}ms</span>
          </div>
        </div>

        <div className="mt-3 pt-3 border-t border-gray-700">
          <div className="flex flex-wrap gap-1">
            {agent.capabilities?.slice(0, 3).map((cap, idx) => (
              <span key={idx} className="text-xs bg-gray-700 px-2 py-1 rounded">
                {typeof cap === 'string' ? cap : cap.name}
              </span>
            ))}
            {agent.capabilities?.length > 3 && (
              <span className="text-xs text-gray-500">+{agent.capabilities.length - 3} more</span>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Brain className="w-16 h-16 text-purple-500 mx-auto mb-4 animate-pulse" />
          <h2 className="text-xl font-semibold text-white mb-2">Initializing Colony Command Center</h2>
          <p className="text-gray-400">Connecting to colony intelligence...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white flex items-center space-x-2">
                <Network className="w-8 h-8 text-purple-500" />
                <span>Colony Command Center</span>
              </h1>
              
              {/* Colony Selector */}
              <select
                value={selectedColony?.id || ''}
                onChange={(e) => {
                  const colony = colonies.find(c => c.id === e.target.value);
                  setSelectedColony(colony);
                }}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600"
              >
                {colonies.map(colony => (
                  <option key={colony.id} value={colony.id}>{colony.name}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-4">
              {/* Health Status */}
              {colonyHealth && (
                <HealthIndicator status={colonyHealth.status} score={colonyHealth.overall} />
              )}

              {/* Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsPaused(!isPaused)}
                  className="p-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                  title={isPaused ? 'Resume' : 'Pause'}
                >
                  {isPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
                </button>
                
                <button
                  onClick={() => {
                    fetchColonyHealth();
                    fetchAgents();
                    fetchExecutions();
                  }}
                  className="p-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                  title="Refresh"
                >
                  <RefreshCw className="w-5 h-5" />
                </button>

                <button
                  onClick={() => setShowCreateModal(true)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
                >
                  <Plus className="w-5 h-5" />
                  <span>New Colony</span>
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-6 mt-6">
            {['overview', 'agents', 'workflows', 'monitoring', 'settings'].map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`pb-2 px-1 border-b-2 transition-colors ${
                  activeTab === tab
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-white'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <Activity className="w-8 h-8 text-blue-500" />
                  <span className="text-2xl font-bold text-white">{metrics.totalExecutions}</span>
                </div>
                <h3 className="text-gray-400 text-sm">Total Executions</h3>
                <div className="flex items-center mt-2 text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-500">+12.5%</span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <CheckCircle className="w-8 h-8 text-green-500" />
                  <span className="text-2xl font-bold text-white">{metrics.successRate}%</span>
                </div>
                <h3 className="text-gray-400 text-sm">Success Rate</h3>
                <div className="flex items-center mt-2 text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-500">+2.3%</span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <Clock className="w-8 h-8 text-purple-500" />
                  <span className="text-2xl font-bold text-white">{metrics.avgResponseTime}ms</span>
                </div>
                <h3 className="text-gray-400 text-sm">Avg Response Time</h3>
                <div className="flex items-center mt-2 text-sm">
                  <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-500">-18ms</span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <Users className="w-8 h-8 text-orange-500" />
                  <span className="text-2xl font-bold text-white">
                    {agents.queens.length + agents.workers.length + agents.scouts.length}
                  </span>
                </div>
                <h3 className="text-gray-400 text-sm">Active Agents</h3>
                <div className="flex items-center mt-2 text-sm space-x-3">
                  <span className="text-purple-400">{agents.queens.length} Queens</span>
                  <span className="text-blue-400">{agents.workers.length} Workers</span>
                  <span className="text-green-400">{agents.scouts.length} Scouts</span>
                </div>
              </motion.div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-blue-500" />
                  Performance Metrics
                </h3>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="time" stroke="#9CA3AF" />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip
                      contentStyle={{ backgroundColor: '#1F2937', border: 'none' }}
                      labelStyle={{ color: '#9CA3AF' }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="executions"
                      stroke="#3B82F6"
                      strokeWidth={2}
                      dot={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="successRate"
                      stroke="#10B981"
                      strokeWidth={2}
                      dot={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="responseTime"
                      stroke="#F59E0B"
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </motion.div>

              {/* Resource Usage */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Server className="w-5 h-5 mr-2 text-purple-500" />
                  Resource Usage
                </h3>
                <div className="space-y-4">
                  {resourceData.map((resource, idx) => (
                    <div key={idx}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">{resource.name}</span>
                        <span className="text-white">
                          {resource.name === 'Memory' 
                            ? `${resource.value.toFixed(0)} / ${resource.limit} MB`
                            : `${resource.value.toFixed(0)} / ${resource.limit}`
                          }
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${(resource.value / resource.limit) * 100}%` }}
                          className={`h-full rounded-full ${
                            resource.value / resource.limit > 0.8 ? 'bg-red-500' :
                            resource.value / resource.limit > 0.6 ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                {/* Agent Performance Radar */}
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Agent Performance</h4>
                  <ResponsiveContainer width="100%" height={200}>
                    <RadarChart data={agentMetrics}>
                      <PolarGrid stroke="#374151" />
                      <PolarAngleAxis dataKey="type" stroke="#9CA3AF" />
                      <PolarRadiusAxis stroke="#9CA3AF" />
                      <Radar
                        name="Active"
                        dataKey="active"
                        stroke="#3B82F6"
                        fill="#3B82F6"
                        fillOpacity={0.6}
                      />
                      <Radar
                        name="Performance"
                        dataKey="performance"
                        stroke="#10B981"
                        fill="#10B981"
                        fillOpacity={0.6}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </motion.div>
            </div>

            {/* Recent Executions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-green-500" />
                Recent Executions
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left border-b border-gray-700">
                      <th className="pb-3 text-sm font-medium text-gray-400">Execution ID</th>
                      <th className="pb-3 text-sm font-medium text-gray-400">Type</th>
                      <th className="pb-3 text-sm font-medium text-gray-400">Agent</th>
                      <th className="pb-3 text-sm font-medium text-gray-400">Status</th>
                      <th className="pb-3 text-sm font-medium text-gray-400">Duration</th>
                      <th className="pb-3 text-sm font-medium text-gray-400">Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {executions.slice(0, 10).map((execution) => (
                      <tr key={execution.executionId} className="border-b border-gray-700/50">
                        <td className="py-3 text-sm text-gray-300 font-mono">
                          {execution.executionId.slice(0, 8)}...
                        </td>
                        <td className="py-3 text-sm">
                          <span className={`px-2 py-1 rounded text-xs ${
                            execution.agentType === 'queen' ? 'bg-purple-500/20 text-purple-400' :
                            execution.agentType === 'worker' ? 'bg-blue-500/20 text-blue-400' :
                            'bg-green-500/20 text-green-400'
                          }`}>
                            {execution.agentType}
                          </span>
                        </td>
                        <td className="py-3 text-sm text-gray-300">
                          {execution.agentName || 'Unknown'}
                        </td>
                        <td className="py-3 text-sm">
                          <span className={`px-2 py-1 rounded text-xs ${
                            execution.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                            execution.status === 'running' ? 'bg-blue-500/20 text-blue-400' :
                            'bg-red-500/20 text-red-400'
                          }`}>
                            {execution.status}
                          </span>
                        </td>
                        <td className="py-3 text-sm text-gray-300">
                          {execution.duration ? `${execution.duration}ms` : '-'}
                        </td>
                        <td className="py-3 text-sm text-gray-400">
                          {new Date(execution.startTime).toLocaleTimeString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </motion.div>
          </div>
        )}

        {activeTab === 'agents' && (
          <div className="space-y-6">
            {/* Agent Categories */}
            <div className="space-y-6">
              {/* Queens */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Crown className="w-5 h-5 mr-2 text-purple-500" />
                  Queen Agents ({agents.queens.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.queens.map(agent => (
                    <AgentCard key={agent._id} agent={agent} type="queen" />
                  ))}
                </div>
              </div>

              {/* Workers */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Bot className="w-5 h-5 mr-2 text-blue-500" />
                  Worker Agents ({agents.workers.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.workers.map(agent => (
                    <AgentCard key={agent._id} agent={agent} type="worker" />
                  ))}
                </div>
              </div>

              {/* Scouts */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Search className="w-5 h-5 mr-2 text-green-500" />
                  Scout Agents ({agents.scouts.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.scouts.map(agent => (
                    <AgentCard key={agent._id} agent={agent} type="scout" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'workflows' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Workflow className="w-5 h-5 mr-2 text-purple-500" />
                Colony Workflows
              </h3>
              <button
                onClick={() => navigate('/workflows/create')}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
              >
                <Plus className="w-5 h-5" />
                <span>Create Workflow</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {workflows.map(workflow => (
                <motion.div
                  key={workflow._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-semibold text-white">{workflow.name}</h4>
                      <p className="text-sm text-gray-400 mt-1">{workflow.description}</p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs ${
                      workflow.status === 'active' ? 'bg-green-500/20 text-green-400' :
                      workflow.status === 'draft' ? 'bg-gray-600 text-gray-400' :
                      'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {workflow.status}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-400">
                    <span>{workflow.nodes?.length || 0} nodes</span>
                    <span>{workflow.trigger?.type || 'manual'}</span>
                  </div>

                  <div className="mt-4 flex space-x-2">
                    <button
                      onClick={() => handleExecuteWorkflow(workflow._id)}
                      className="flex-1 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors flex items-center justify-center space-x-1"
                    >
                      <Play className="w-4 h-4" />
                      <span>Execute</span>
                    </button>
                    <button
                      onClick={() => navigate(`/workflows/${workflow._id}/edit`)}
                      className="flex-1 px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors"
                    >
                      Edit
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'monitoring' && (
          <div className="space-y-6">
            {/* Health Recommendations */}
            {colonyHealth?.recommendations && colonyHealth.recommendations.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2 text-yellow-500" />
                  Health Recommendations
                </h3>
                <div className="space-y-3">
                  {colonyHealth.recommendations.map((rec, idx) => (
                    <div key={idx} className="flex items-start space-x-3">
                      <div className={`mt-1 w-2 h-2 rounded-full ${
                        rec.priority === 'high' ? 'bg-red-500' :
                        rec.priority === 'medium' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-white">{rec.message}</p>
                        <p className="text-sm text-gray-400 mt-1">{rec.action}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Queue Status */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Layers className="w-5 h-5 mr-2 text-blue-500" />
                Queue Status
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(colonyHealth?.queues || {}).map(([name, stats]) => (
                  <div key={name} className="bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium text-white mb-2 capitalize">{name} Queue</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Waiting</span>
                        <span className={`font-medium ${
                          stats.health === 'critical' ? 'text-red-400' :
                          stats.health === 'warning' ? 'text-yellow-400' :
                          'text-green-400'
                        }`}>{stats.waiting}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Active</span>
                        <span className="text-white">{stats.active}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Completed</span>
                        <span className="text-white">{stats.completed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Throughput</span>
                        <span className="text-white">{stats.throughput?.toFixed(1)}/min</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2 text-purple-500" />
                Colony Settings
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Auto-refresh Interval
                  </label>
                  <select
                    value={refreshInterval}
                    onChange={(e) => setRefreshInterval(Number(e.target.value))}
                    className="w-full bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600"
                  >
                    <option value={1000}>1 second</option>
                    <option value={5000}>5 seconds</option>
                    <option value={10000}>10 seconds</option>
                    <option value={30000}>30 seconds</option>
                    <option value={60000}>1 minute</option>
                  </select>
                </div>

                {selectedColony && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Resource Limits
                      </label>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-300">Max Queens</span>
                          <span className="text-white">{selectedColony.configuration?.maxQueens || 3}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Max Workers</span>
                          <span className="text-white">{selectedColony.configuration?.maxWorkers || 50}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-300">Max Scouts</span>
                          <span className="text-white">{selectedColony.configuration?.maxScouts || 10}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Auto-scaling
                      </label>
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedColony.configuration?.autoScaling || false}
                          onChange={() => {/* Handle toggle */}}
                          className="form-checkbox h-5 w-5 text-purple-600"
                        />
                        <span className="text-gray-300">Enable automatic agent scaling</span>
                      </label>
                    </div>
                  </>
                )}
              </div>
            </motion.div>
          </div>
        )}
      </div>

      {/* Agent Details Modal */}
      <AnimatePresence>
        {showAgentDetails && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowAgentDetails(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-white">{showAgentDetails.name}</h3>
                <button
                  onClick={() => setShowAgentDetails(null)}
                  className="text-gray-400 hover:text-white"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Description</h4>
                  <p className="text-gray-300">{showAgentDetails.description}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Capabilities</h4>
                  <div className="space-y-2">
                    {showAgentDetails.capabilities?.map((cap, idx) => (
                      <div key={idx} className="bg-gray-700 rounded p-3">
                        <h5 className="font-medium text-white">
                          {typeof cap === 'string' ? cap : cap.name}
                        </h5>
                        {typeof cap === 'object' && (
                          <p className="text-sm text-gray-400 mt-1">{cap.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Configuration</h4>
                  <pre className="bg-gray-900 rounded p-3 text-sm text-gray-300 overflow-x-auto">
                    {JSON.stringify(showAgentDetails.configuration, null, 2)}
                  </pre>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ColonyCommandCenter;