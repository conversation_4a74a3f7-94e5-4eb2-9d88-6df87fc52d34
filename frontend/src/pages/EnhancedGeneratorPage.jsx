import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Brain, Sparkles, TrendingUp, Zap } from 'lucide-react'
import toast from 'react-hot-toast'
import AIGenerationInterface from '../components/AIGenerationInterface'
import { EnhancedCard, StatsCard } from '../components/EnhancedDesignSystem'

const EnhancedGeneratorPage = () => {
  const navigate = useNavigate()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSequence, setGeneratedSequence] = useState(null)
  const [stats, setStats] = useState({
    totalSequences: 0,
    avgGenerationTime: '2.3s',
    aiModel: 'GPT-4',
    successRate: '99.9%'
  })

  useEffect(() => {
    // Load user statistics
    const loadStats = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/analytics`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          setStats(prev => ({
            ...prev,
            totalSequences: data.analytics.total_sequences || 0
          }))
        }
      } catch (error) {
        console.error('Failed to load analytics:', error)
      }
    }

    loadStats()
  }, [])

  const handleGenerate = async (formData) => {
    setIsGenerating(true)
    setGeneratedSequence(null)

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/generate-enhanced`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (response.ok) {
        setGeneratedSequence(data.sequence)
        toast.success(`Enhanced sequence generated in ${data.generation_time_ms}ms!`)
        
        // Update stats
        setStats(prev => ({
          ...prev,
          totalSequences: prev.totalSequences + 1,
          avgGenerationTime: `${(data.generation_time_ms / 1000).toFixed(1)}s`
        }))
      } else {
        if (data.code === 'USAGE_LIMIT_EXCEEDED') {
          toast.error('Usage limit exceeded. Please upgrade your plan or enable overage billing.')
          navigate('/pricing')
        } else {
          toast.error(data.error || 'Failed to generate sequence')
        }
      }
    } catch (error) {
      console.error('Generation error:', error)
      toast.error('Failed to generate sequence. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSaveSequence = () => {
    if (generatedSequence) {
      navigate(`/sequences/${generatedSequence.id}`)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Brain className="w-8 h-8 text-purple-400" />
            <h1 className="text-4xl font-bold text-white">
              Enhanced AI <span className="gradient-text">Generator</span>
            </h1>
            <Sparkles className="w-8 h-8 text-amber-400" />
          </div>
          <p className="text-xl text-neutral-300 max-w-2xl mx-auto">
            Professional email sequence generation with advanced AI optimization and industry-specific targeting
          </p>
        </motion.div>

        {/* Stats Row */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          <StatsCard 
            value={stats.totalSequences}
            label="Sequences Generated"
            trend="+12% this month"
          />
          <StatsCard 
            value={stats.avgGenerationTime}
            label="Avg Generation Time"
            trend="10x faster than competitors"
          />
          <StatsCard 
            value={stats.aiModel}
            label="AI Model"
            trend="Premium quality"
          />
          <StatsCard 
            value={stats.successRate}
            label="Success Rate"
            trend="Enterprise reliability"
          />
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Generation Interface */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <AIGenerationInterface 
              onGenerate={handleGenerate}
              isGenerating={isGenerating}
            />
          </motion.div>

          {/* Results Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <EnhancedCard className="h-full">
              <div className="flex items-center gap-3 mb-6">
                <TrendingUp className="w-6 h-6 text-green-400" />
                <h2 className="text-2xl font-bold text-white">Generated Results</h2>
              </div>

              {!generatedSequence && !isGenerating && (
                <div className="text-center py-12">
                  <Zap className="w-16 h-16 text-purple-400 mx-auto mb-4 opacity-50" />
                  <p className="text-neutral-400 text-lg">
                    Configure your sequence settings and click Generate to see results here
                  </p>
                </div>
              )}

              {isGenerating && (
                <div className="text-center py-12">
                  <div className="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-purple-300 text-lg font-medium">
                    Generating enhanced sequence with AI optimization...
                  </p>
                  <p className="text-neutral-400 text-sm mt-2">
                    This may take a few seconds for optimal results
                  </p>
                </div>
              )}

              {generatedSequence && (
                <div className="space-y-6">
                  {/* Sequence Info */}
                  <div className="bg-gradient-to-r from-purple-500/10 to-amber-500/10 border border-purple-500/30 rounded-xl p-6">
                    <h3 className="text-xl font-bold text-white mb-2">
                      {generatedSequence.title}
                    </h3>
                    <p className="text-neutral-300 mb-4">
                      {generatedSequence.description}
                    </p>
                    
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-neutral-400">Industry:</span>
                        <span className="text-white ml-2 font-medium">
                          {generatedSequence.industry}
                        </span>
                      </div>
                      <div>
                        <span className="text-neutral-400">Tone:</span>
                        <span className="text-white ml-2 font-medium">
                          {generatedSequence.tone}
                        </span>
                      </div>
                      <div>
                        <span className="text-neutral-400">Length:</span>
                        <span className="text-white ml-2 font-medium">
                          {generatedSequence.length} emails
                        </span>
                      </div>
                      <div>
                        <span className="text-neutral-400">AI Model:</span>
                        <span className="text-white ml-2 font-medium">
                          {generatedSequence.content?.ai_model || 'GPT-4'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  {generatedSequence.content?.generation_time && (
                    <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Zap className="w-5 h-5 text-green-400" />
                        <span className="text-green-400 font-medium">Performance Metrics</span>
                      </div>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-neutral-400">Generation Time:</span>
                          <span className="text-white ml-2 font-medium">
                            {(generatedSequence.content.generation_time / 1000).toFixed(2)}s
                          </span>
                        </div>
                        <div>
                          <span className="text-neutral-400">Version:</span>
                          <span className="text-white ml-2 font-medium">
                            {generatedSequence.content.version || '2.0'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Features */}
                  {generatedSequence.content?.enhanced_features && (
                    <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Sparkles className="w-5 h-5 text-purple-400" />
                        <span className="text-purple-400 font-medium">Enhanced Features</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {generatedSequence.content.enhanced_features.map((feature, index) => (
                          <span 
                            key={index}
                            className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <button
                      onClick={handleSaveSequence}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
                    >
                      View Full Sequence
                    </button>
                    <button
                      onClick={() => setGeneratedSequence(null)}
                      className="px-6 py-3 border-2 border-neutral-600 text-neutral-300 hover:bg-neutral-600 hover:text-white rounded-xl transition-all duration-300"
                    >
                      Generate Another
                    </button>
                  </div>
                </div>
              )}
            </EnhancedCard>
          </motion.div>
        </div>

        {/* Features Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-12"
        >
          <EnhancedCard>
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">
                Enhanced AI <span className="gradient-text">Capabilities</span>
              </h3>
              <p className="text-neutral-400">
                Professional features for superior email sequence generation
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <Brain className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                <h4 className="text-lg font-semibold text-white mb-2">Industry Intelligence</h4>
                <p className="text-neutral-400 text-sm">
                  Specialized prompts and context for each industry with proven conversion strategies
                </p>
              </div>
              <div className="text-center">
                <Sparkles className="w-12 h-12 text-amber-400 mx-auto mb-3" />
                <h4 className="text-lg font-semibold text-white mb-2">Advanced Optimization</h4>
                <p className="text-neutral-400 text-sm">
                  Professional formatting, tone consistency, and conversion-focused content structure
                </p>
              </div>
              <div className="text-center">
                <TrendingUp className="w-12 h-12 text-green-400 mx-auto mb-3" />
                <h4 className="text-lg font-semibold text-white mb-2">Performance Analytics</h4>
                <p className="text-neutral-400 text-sm">
                  Detailed analytics, generation metrics, and continuous improvement insights
                </p>
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      </div>
    </div>
  )
}

export default EnhancedGeneratorPage