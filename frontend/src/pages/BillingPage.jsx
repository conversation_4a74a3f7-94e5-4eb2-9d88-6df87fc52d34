import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CreditCard, 
  Download, 
  Eye, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  Settings,
  Shield
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import AnimatedBackground from '../components/AnimatedBackground'

const BillingPage = () => {
  const { user } = useAuth()
  const [billingData, setBillingData] = useState({
    currentPlan: 'Pro',
    monthlySpend: 89,
    nextBilling: '2025-08-01',
    usage: {
      sequences: 45,
      limit: 75,
      overages: 0
    },
    invoices: [
      {
        id: 'inv_001',
        date: '2025-07-01',
        amount: 89,
        status: 'paid',
        description: 'Pro Plan - July 2025'
      },
      {
        id: 'inv_002', 
        date: '2025-06-01',
        amount: 89,
        status: 'paid',
        description: 'Pro Plan - June 2025'
      }
    ],
    paymentMethod: {
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      expiry: '12/27'
    }
  })

  const usagePercentage = (billingData.usage.sequences / billingData.usage.limit) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 to-neutral-900">
      <AnimatedBackground />
      
      <div className="relative">
        {/* Header */}
        <div className="pt-24 pb-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl font-bold mb-4">
                <span className="gradient-text">Billing & Usage</span>
              </h1>
              <p className="text-xl text-neutral-300">
                Manage your subscription, usage, and payment methods
              </p>
            </motion.div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
          <div className="grid lg:grid-cols-3 gap-8">
            
            {/* Current Plan & Usage */}
            <div className="lg:col-span-2 space-y-6">
              
              {/* Current Plan */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="card-premium"
              >
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Current Plan</h2>
                    <p className="text-neutral-300">{billingData.currentPlan} Subscription</p>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-white">${billingData.monthlySpend}</div>
                    <div className="text-sm text-neutral-400">per month</div>
                  </div>
                </div>

                <div className="flex items-center gap-4 mb-6">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-neutral-300">Active subscription</span>
                  <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">
                    Auto-renew enabled
                  </span>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <div className="text-sm text-neutral-400 mb-1">Next billing date</div>
                    <div className="text-white font-semibold flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      {billingData.nextBilling}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-neutral-400 mb-1">Payment method</div>
                    <div className="text-white font-semibold flex items-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      •••• {billingData.paymentMethod.last4}
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-neutral-700 flex gap-3">
                  <button className="btn btn-outline flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Manage Plan
                  </button>
                  <button className="btn btn-ghost flex items-center gap-2">
                    <CreditCard className="w-4 h-4" />
                    Update Payment
                  </button>
                </div>
              </motion.div>

              {/* Usage Tracking */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="card-premium"
              >
                <h3 className="text-xl font-bold text-white mb-4">Current Usage</h3>
                
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-neutral-300">Email Sequences</span>
                    <span className="text-white font-semibold">
                      {billingData.usage.sequences} / {billingData.usage.limit}
                    </span>
                  </div>
                  <div className="w-full bg-neutral-700 rounded-full h-3">
                    <motion.div
                      className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-amber-400"
                      initial={{ width: 0 }}
                      animate={{ width: `${usagePercentage}%` }}
                      transition={{ delay: 0.5, duration: 1 }}
                    />
                  </div>
                  <div className="text-sm text-neutral-400 mt-1">
                    {Math.round(usagePercentage)}% of monthly allocation used
                  </div>
                </div>

                {billingData.usage.overages > 0 && (
                  <div className="p-4 bg-amber-500/10 border border-amber-500/20 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="w-5 h-5 text-amber-400 mt-0.5" />
                      <div>
                        <div className="text-amber-300 font-medium">Overage Usage</div>
                        <div className="text-sm text-amber-200 mt-1">
                          You have {billingData.usage.overages} overage sequences this month.
                          Additional charges will apply.
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid md:grid-cols-3 gap-4 mt-6">
                  <div className="text-center p-4 bg-neutral-800/50 rounded-lg">
                    <TrendingUp className="w-6 h-6 text-green-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white">+23%</div>
                    <div className="text-sm text-neutral-400">vs last month</div>
                  </div>
                  <div className="text-center p-4 bg-neutral-800/50 rounded-lg">
                    <Clock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white">12 days</div>
                    <div className="text-sm text-neutral-400">until reset</div>
                  </div>
                  <div className="text-center p-4 bg-neutral-800/50 rounded-lg">
                    <DollarSign className="w-6 h-6 text-purple-400 mx-auto mb-2" />
                    <div className="text-lg font-bold text-white">$0</div>
                    <div className="text-sm text-neutral-400">overages</div>
                  </div>
                </div>
              </motion.div>

              {/* Recent Invoices */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="card-premium"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white">Recent Invoices</h3>
                  <button className="text-purple-400 hover:text-purple-300 text-sm font-medium">
                    View All
                  </button>
                </div>

                <div className="space-y-4">
                  {billingData.invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 bg-neutral-800/30 rounded-lg hover:bg-neutral-800/50 transition-colors">
                      <div>
                        <div className="text-white font-medium">{invoice.description}</div>
                        <div className="text-sm text-neutral-400">{invoice.date}</div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-white font-semibold">${invoice.amount}</div>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            invoice.status === 'paid' 
                              ? 'bg-green-500/20 text-green-300' 
                              : 'bg-yellow-500/20 text-yellow-300'
                          }`}>
                            {invoice.status}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <button className="p-2 hover:bg-neutral-700 rounded-lg transition-colors">
                            <Eye className="w-4 h-4 text-neutral-400" />
                          </button>
                          <button className="p-2 hover:bg-neutral-700 rounded-lg transition-colors">
                            <Download className="w-4 h-4 text-neutral-400" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              
              {/* Quick Actions */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="card-premium"
              >
                <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button className="w-full btn btn-primary justify-between">
                    Upgrade Plan
                    <ArrowUpRight className="w-4 h-4" />
                  </button>
                  <button className="w-full btn btn-outline justify-between">
                    Download Invoice
                    <Download className="w-4 h-4" />
                  </button>
                  <button className="w-full btn btn-ghost justify-between">
                    Update Billing
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </motion.div>

              {/* Security */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="card-premium"
              >
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="w-5 h-5 text-green-400" />
                  <h3 className="text-lg font-bold text-white">Security</h3>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-neutral-300">Payment Security</span>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-neutral-300">Data Encryption</span>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-neutral-300">SOC 2 Compliant</span>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              </motion.div>

              {/* Support */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="card-premium"
              >
                <h3 className="text-lg font-bold text-white mb-4">Need Help?</h3>
                <p className="text-neutral-300 text-sm mb-4">
                  Our billing team is here to help with any questions about your account.
                </p>
                <button className="w-full btn btn-outline">
                  Contact Support
                </button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BillingPage