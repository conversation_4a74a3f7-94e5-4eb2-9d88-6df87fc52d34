import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Home, Search, ArrowLeft, MessageSquare } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* 404 Number */}
          <div className="mb-8">
            <h1 className="text-9xl font-bold text-blue-600 opacity-50">404</h1>
          </div>

          {/* Main Content */}
          <div className="mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Page Not Found
            </h2>
            <p className="text-xl text-gray-600 mb-6">
              Sorry, we couldn't find the page you're looking for. The page might have been moved, 
              deleted, or you may have typed the URL incorrectly.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Home className="h-5 w-5 mr-2" />
              Back to Home
            </Link>
            
            <Link
              to="/contact"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MessageSquare className="h-5 w-5 mr-2" />
              Contact Support
            </Link>
          </div>

          {/* Helpful Links */}
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Looking for something specific?
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Popular Pages</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>
                    <Link to="/generator" className="hover:text-blue-600 transition-colors">
                      → AI Email Generator
                    </Link>
                  </li>
                  <li>
                    <Link to="/pricing" className="hover:text-blue-600 transition-colors">
                      → Pricing Plans
                    </Link>
                  </li>
                  <li>
                    <Link to="/templates" className="hover:text-blue-600 transition-colors">
                      → Email Templates
                    </Link>
                  </li>
                  <li>
                    <Link to="/dashboard" className="hover:text-blue-600 transition-colors">
                      → User Dashboard
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Help & Support</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>
                    <Link to="/faq" className="hover:text-blue-600 transition-colors">
                      → Frequently Asked Questions
                    </Link>
                  </li>
                  <li>
                    <Link to="/about" className="hover:text-blue-600 transition-colors">
                      → About NeuroColony
                    </Link>
                  </li>
                  <li>
                    <Link to="/contact" className="hover:text-blue-600 transition-colors">
                      → Contact Us
                    </Link>
                  </li>
                  <li>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="hover:text-blue-600 transition-colors"
                    >
                      → Email Support
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Search Suggestion */}
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <div className="flex items-center justify-center text-blue-700 mb-2">
              <Search className="h-5 w-5 mr-2" />
              <span className="font-medium">Tip</span>
            </div>
            <p className="text-blue-700 text-sm">
              If you're looking for a specific feature or page, try using the search function 
              in our help center or contact our support team.
            </p>
          </div>

          {/* Back Button */}
          <div className="mt-8">
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go back to previous page
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default NotFound;