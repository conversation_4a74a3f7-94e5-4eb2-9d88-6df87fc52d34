import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import {
  ArrowRight,
  Sparkles,
  Zap,
  Brain,
  Users,
  TrendingUp,
  Shield,
  Clock,
  Mail,
  Target,
  CheckCircle,
  Star
} from 'lucide-react'
import Logo from '../components/Logo'
import { Button } from '../components/ui/DesignSystem'

const HomePage = () => {
  // Social Proof Stats
  const stats = [
    { number: "10,000+", label: "Businesses Using NeuroColony" },
    { number: "300%", label: "Average Increase in Email Conversions" },
    { number: "20+", label: "Hours Saved Per Week" },
    { number: "99.9%", label: "Uptime Reliability" }
  ]

  // Feature Highlights with Ant-Inspired Icons
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Intelligence",
      description: "Transform strangers into customers with advanced neural networks that understand human psychology and buying behavior."
    },
    {
      icon: Users,
      title: "Colony Collaboration",
      description: "Work together like an ant colony - seamless team collaboration with shared templates, insights, and optimization strategies."
    },
    {
      icon: TrendingUp,
      title: "Systematic Growth",
      description: "Build efficient pathways to revenue with data-driven sequences that adapt and optimize automatically for maximum conversion."
    }
  ]

  // Benefits Section
  const benefits = [
    "Generate high-converting email sequences in minutes",
    "A/B test automatically to find winning combinations",
    "Integrate with your existing email platforms",
    "Scale from startup to enterprise with confidence",
    "Get actionable insights and performance analytics",
    "Access premium templates from top marketers"
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-blue-950/20 to-slate-950">
      {/* Ant-Inspired Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(37,99,235,0.1),transparent_50%)]" />

        {/* Neural Network Pattern */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-emerald-500/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '4s' }} />

        {/* Ant Trail Effect */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400/30 rounded-full animate-ping" style={{ animationDelay: '1s' }} />
        <div className="absolute top-32 left-20 w-2 h-2 bg-purple-400/30 rounded-full animate-ping" style={{ animationDelay: '2s' }} />
        <div className="absolute top-44 left-32 w-2 h-2 bg-emerald-400/30 rounded-full animate-ping" style={{ animationDelay: '3s' }} />
      </div>

      {/* Hero Section */}
      <section className="relative px-6 pt-32 pb-20">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <h1 className="text-5xl md:text-7xl font-light text-white mb-6 leading-tight">
              Transform Strangers Into
              <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 bg-clip-text text-transparent font-medium">
                Customers with AI
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 font-light max-w-3xl mx-auto leading-relaxed mb-8">
              NeuroColony's AI creates high-converting email sequences that turn prospects into loyal customers. Join 10,000+ businesses growing with intelligent automation.
            </p>

            {/* Social Proof */}
            <div className="flex flex-wrap justify-center gap-8 mb-8 text-sm text-slate-400">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span>4.9/5 from 2,000+ reviews</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-emerald-400" />
                <span>SOC 2 Compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-blue-400" />
                <span>10,000+ Active Users</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Button
              variant="primary"
              size="xl"
              className="group flex items-center gap-3"
              onClick={() => window.location.href = '/register'}
            >
              Start Free Trial
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="xl"
              className="flex items-center gap-3"
              onClick={() => window.location.href = '/demo'}
            >
              <Zap className="w-5 h-5" />
              Watch Demo
            </Button>
          </motion.div>

          {/* Stats Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
          >
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-white mb-1">
                  {stat.number}
                </div>
                <div className="text-sm text-slate-400">
                  {stat.label}
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Features Section - Ant Colony Intelligence */}
      <section className="relative px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-white mb-4">
              Powered by Colony Intelligence
            </h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Like an ant colony working together, our AI creates systematic pathways to convert prospects into customers with remarkable efficiency.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group relative p-8 rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 hover:border-blue-500/50 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/10"
                >
                  <div className="relative">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-white mb-3 group-hover:text-blue-300 transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-slate-400 leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/5 group-hover:to-purple-500/5 rounded-2xl transition-all duration-500" />
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="relative px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-light text-white mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-lg text-slate-400">
              From AI-powered generation to advanced analytics, NeuroColony provides the complete toolkit for email marketing success.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-4">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-3 p-4 rounded-xl bg-slate-800/30 border border-slate-700/30"
              >
                <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                <span className="text-slate-300">{benefit}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Teaser Section */}
      <section className="relative px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-light text-white mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-lg text-slate-400">
              Start free, scale as you grow. No hidden fees, no surprises.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {/* Free Tier */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="p-6 rounded-2xl bg-slate-800/50 border border-slate-700/50"
            >
              <h3 className="text-xl font-medium text-white mb-2">Starter Colony</h3>
              <div className="text-3xl font-bold text-white mb-4">$29<span className="text-lg text-slate-400">/mo</span></div>
              <ul className="text-sm text-slate-400 space-y-2">
                <li>• 5 AI agents</li>
                <li>• 50 compute hours/month</li>
                <li>• Basic email automation</li>
                <li>• Community support</li>
              </ul>
            </motion.div>

            {/* Professional Tier - Highlighted */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="p-6 rounded-2xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 border border-blue-500/50 relative"
            >
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
              <h3 className="text-xl font-medium text-white mb-2">Professional Colony</h3>
              <div className="text-3xl font-bold text-white mb-4">$89<span className="text-lg text-slate-400">/mo</span></div>
              <ul className="text-sm text-slate-300 space-y-2">
                <li>• 25 AI agents</li>
                <li>• 200 compute hours/month</li>
                <li>• Advanced workflows</li>
                <li>• Priority support</li>
              </ul>
            </motion.div>

            {/* Business Tier */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="p-6 rounded-2xl bg-slate-800/50 border border-slate-700/50"
            >
              <h3 className="text-xl font-medium text-white mb-2">Enterprise Colony</h3>
              <div className="text-3xl font-bold text-white mb-4">$289<span className="text-lg text-slate-400">/mo</span></div>
              <ul className="text-sm text-slate-400 space-y-2">
                <li>• Unlimited AI agents</li>
                <li>• Unlimited compute</li>
                <li>• White-label platform</li>
                <li>• Dedicated support</li>
              </ul>
            </motion.div>
          </div>

          <Button
            variant="outline"
            size="lg"
            onClick={() => window.location.href = '/pricing'}
          >
            View Full Pricing Details
          </Button>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="relative px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative p-12 rounded-3xl bg-gradient-to-br from-slate-800/30 to-slate-900/30 border border-slate-700/30 backdrop-blur-sm"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-3xl" />
            <div className="relative">
              <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-light text-white mb-6">
                Ready to Build Your Email Empire?
              </h2>
              <p className="text-lg text-slate-300 mb-8 max-w-2xl mx-auto">
                Join thousands of businesses using NeuroColony to create systematic, high-converting email sequences that drive real results.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="primary"
                  size="xl"
                  className="flex items-center gap-3"
                  onClick={() => window.location.href = '/register'}
                >
                  <Zap className="w-5 h-5" />
                  Start Free Trial
                  <ArrowRight className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="xl"
                  className="text-slate-300 hover:text-white"
                  onClick={() => window.location.href = '/contact'}
                >
                  Talk to Sales
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="mt-8 pt-8 border-t border-slate-700/50">
                <p className="text-sm text-slate-400 mb-4">Trusted by industry leaders</p>
                <div className="flex justify-center items-center gap-8 opacity-60">
                  <div className="text-slate-500 font-medium">TechCorp</div>
                  <div className="text-slate-500 font-medium">GrowthCo</div>
                  <div className="text-slate-500 font-medium">ScaleUp Inc</div>
                  <div className="text-slate-500 font-medium">InnovateLab</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default HomePage