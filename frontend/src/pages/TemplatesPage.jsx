import { useNavigate } from 'react-router-dom'
import TemplateLibrary from '../components/TemplateLibrary'
import toast from 'react-hot-toast'

const TemplatesPage = ({ user }) => {
  const navigate = useNavigate()

  const handleSelectTemplate = (template) => {
    if (!user) {
      toast.error('Please sign in to deploy agent colonies')
      navigate('/login')
      return
    }

    // Store selected template for deployment
    localStorage.setItem('selectedAgentTemplate', JSON.stringify(template))
    toast.success(`Colony template "${template.name}" ready for deployment!`)
    navigate('/colony-command')
  }

  return (
    <TemplateLibrary onSelectTemplate={handleSelectTemplate} />
  )
}

export default TemplatesPage