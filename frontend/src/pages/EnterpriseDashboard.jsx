import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Building2, 
  Shield, 
  BarChart3, 
  Users, 
  Settings,
  Palette,
  Key,
  Globe,
  TrendingUp,
  AlertTriangle,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

const EnterpriseDashboard = () => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [dashboards, setDashboards] = useState([])
  const [analytics, setAnalytics] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadEnterpriseDashboard()
  }, [])

  const loadEnterpriseDashboard = async () => {
    try {
      // Load available dashboards
      const dashboardResponse = await fetch(`${import.meta.env.VITE_API_URL}/enterprise/analytics/dashboards`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json()
        setDashboards(dashboardData.data || [])
      }

      // Load executive overview dashboard
      const analyticsResponse = await fetch(`${import.meta.env.VITE_API_URL}/enterprise/analytics/dashboards/executive-overview?timeframe=30d`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json()
        setAnalytics(analyticsData.data)
      }
    } catch (error) {
      console.error('Failed to load enterprise dashboard:', error)
      toast.error('Failed to load enterprise dashboard')
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'analytics', name: 'Analytics', icon: TrendingUp },
    { id: 'white-label', name: 'White-Label', icon: Palette },
    { id: 'sso', name: 'SSO', icon: Shield },
    { id: 'teams', name: 'Teams', icon: Users },
    { id: 'settings', name: 'Settings', icon: Settings }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 flex items-center justify-center">
        <div className="text-white">Loading enterprise dashboard...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Building2 className="h-8 w-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">Enterprise Dashboard</h1>
          </div>
          <p className="text-neutral-300">
            Advanced analytics, white-label customization, and enterprise features
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-neutral-800/50 rounded-lg p-1 mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-500 text-white'
                  : 'text-neutral-400 hover:text-white hover:bg-neutral-700/50'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'overview' && <OverviewTab analytics={analytics} />}
          {activeTab === 'analytics' && <AnalyticsTab dashboards={dashboards} />}
          {activeTab === 'white-label' && <WhiteLabelTab />}
          {activeTab === 'sso' && <SSOTab />}
          {activeTab === 'teams' && <TeamsTab />}
          {activeTab === 'settings' && <SettingsTab />}
        </div>
      </div>
    </div>
  )
}

const OverviewTab = ({ analytics }) => {
  const kpis = [
    {
      title: 'Total Revenue',
      value: '$124,500',
      change: '+12.5%',
      trend: 'up',
      icon: <TrendingUp className="h-6 w-6 text-green-400" />
    },
    {
      title: 'Active Users',
      value: '1,247',
      change: '****%',
      trend: 'up',
      icon: <Users className="h-6 w-6 text-blue-400" />
    },
    {
      title: 'Conversion Rate',
      value: '18.2%',
      change: '****%',
      trend: 'up',
      icon: <Zap className="h-6 w-6 text-purple-400" />
    },
    {
      title: 'System Uptime',
      value: '99.9%',
      change: '0.0%',
      trend: 'stable',
      icon: <CheckCircle className="h-6 w-6 text-green-400" />
    }
  ]

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        {kpis.map((kpi, index) => (
          <motion.div
            key={index}
            className="card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <div className="flex items-center justify-between mb-3">
              {kpi.icon}
              <span className={`text-sm font-medium ${
                kpi.trend === 'up' ? 'text-green-400' : 
                kpi.trend === 'down' ? 'text-red-400' : 'text-neutral-400'
              }`}>
                {kpi.change}
              </span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">{kpi.value}</div>
            <div className="text-sm text-neutral-400">{kpi.title}</div>
          </motion.div>
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">Revenue Trend</h3>
          <div className="h-64 flex items-center justify-center bg-neutral-800/30 rounded-lg">
            <span className="text-neutral-400">Revenue Chart Placeholder</span>
          </div>
        </div>
        
        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">User Activity</h3>
          <div className="h-64 flex items-center justify-center bg-neutral-800/30 rounded-lg">
            <span className="text-neutral-400">Activity Chart Placeholder</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {[
            { action: 'New user registered', user: '<EMAIL>', time: '5 minutes ago' },
            { action: 'Dashboard exported', user: '<EMAIL>', time: '15 minutes ago' },
            { action: 'SSO configuration updated', user: '<EMAIL>', time: '1 hour ago' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-neutral-800/30 rounded-lg">
              <div>
                <div className="text-white font-medium">{activity.action}</div>
                <div className="text-sm text-neutral-400">{activity.user}</div>
              </div>
              <div className="text-sm text-neutral-400">{activity.time}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

const AnalyticsTab = ({ dashboards }) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">Analytics Dashboards</h3>
        <div className="flex gap-3">
          <button className="btn btn-outline text-sm">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </button>
          <button className="btn btn-primary text-sm">
            Create Report
          </button>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {dashboards.map((dashboard, index) => (
          <motion.div
            key={dashboard.id}
            className="card cursor-pointer hover:bg-neutral-800/50 transition-colors"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-semibold text-white">{dashboard.name}</h4>
              <Eye className="h-5 w-5 text-neutral-400" />
            </div>
            <p className="text-neutral-400 mb-4">{dashboard.description}</p>
            <div className="flex items-center justify-between">
              <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-sm">
                {dashboard.category}
              </span>
              <button className="text-purple-400 hover:text-purple-300 text-sm">
                View Dashboard →
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Quick Metrics */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">Top Performing Agents</h4>
          <div className="space-y-2">
            {[
              { name: 'Email Sequence Generator', performance: '98.5%' },
              { name: 'Subject Line Optimizer', performance: '96.2%' },
              { name: 'Audience Segmentation', performance: '94.8%' }
            ].map((agent, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-neutral-300">{agent.name}</span>
                <span className="text-green-400 font-medium">{agent.performance}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">System Health</h4>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-neutral-300">API Response Time: 145ms</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-neutral-300">Database: Healthy</span>
            </div>
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
              <span className="text-neutral-300">Cache: 89% Full</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">Recent Exports</h4>
          <div className="space-y-2">
            {[
              { name: 'Q4 Analytics Report.pdf', time: '2h ago' },
              { name: 'User Engagement Data.csv', time: '5h ago' },
              { name: 'Campaign Performance.xlsx', time: '1d ago' }
            ].map((export_, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <span className="text-neutral-300">{export_.name}</span>
                <span className="text-neutral-400">{export_.time}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

const WhiteLabelTab = () => {
  const [branding, setBranding] = useState({
    companyName: 'Your Company',
    primaryColor: '#2563eb',
    secondaryColor: '#1e40af',
    logoUrl: '',
    customDomain: ''
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">White-Label Configuration</h3>
        <button className="btn btn-primary text-sm">
          Save Changes
        </button>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Branding Settings */}
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-4">Branding Settings</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">
                Company Name
              </label>
              <input
                type="text"
                value={branding.companyName}
                onChange={(e) => setBranding({ ...branding, companyName: e.target.value })}
                className="input w-full"
                placeholder="Your Company Name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">
                Logo URL
              </label>
              <input
                type="url"
                value={branding.logoUrl}
                onChange={(e) => setBranding({ ...branding, logoUrl: e.target.value })}
                className="input w-full"
                placeholder="https://example.com/logo.png"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Primary Color
                </label>
                <input
                  type="color"
                  value={branding.primaryColor}
                  onChange={(e) => setBranding({ ...branding, primaryColor: e.target.value })}
                  className="w-full h-10 rounded border border-neutral-600 bg-neutral-800"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  Secondary Color
                </label>
                <input
                  type="color"
                  value={branding.secondaryColor}
                  onChange={(e) => setBranding({ ...branding, secondaryColor: e.target.value })}
                  className="w-full h-10 rounded border border-neutral-600 bg-neutral-800"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">
                Custom Domain
              </label>
              <input
                type="text"
                value={branding.customDomain}
                onChange={(e) => setBranding({ ...branding, customDomain: e.target.value })}
                className="input w-full"
                placeholder="yourdomain.com"
              />
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-4">Preview</h4>
          <div 
            className="border-2 rounded-lg p-6 text-center"
            style={{ 
              borderColor: branding.primaryColor,
              background: `linear-gradient(135deg, ${branding.primaryColor}20, ${branding.secondaryColor}20)`
            }}
          >
            {branding.logoUrl ? (
              <img src={branding.logoUrl} alt="Logo" className="h-12 mx-auto mb-4" />
            ) : (
              <div 
                className="text-2xl font-bold mb-4"
                style={{ color: branding.primaryColor }}
              >
                {branding.companyName}
              </div>
            )}
            <div className="text-white">AI Agent Platform</div>
            <button 
              className="mt-4 px-6 py-2 rounded font-medium text-white"
              style={{ backgroundColor: branding.primaryColor }}
            >
              Launch Agents
            </button>
          </div>
        </div>
      </div>

      {/* Domain Configuration */}
      <div className="card">
        <h4 className="text-lg font-semibold text-white mb-4">Domain Configuration</h4>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-neutral-800/30 rounded-lg p-4">
            <div className="text-green-400 font-medium mb-2">✓ SSL Certificate</div>
            <div className="text-sm text-neutral-400">Valid until Dec 2025</div>
          </div>
          <div className="bg-neutral-800/30 rounded-lg p-4">
            <div className="text-green-400 font-medium mb-2">✓ DNS Configuration</div>
            <div className="text-sm text-neutral-400">Properly configured</div>
          </div>
          <div className="bg-neutral-800/30 rounded-lg p-4">
            <div className="text-yellow-400 font-medium mb-2">⚠ CDN Setup</div>
            <div className="text-sm text-neutral-400">Optimization available</div>
          </div>
        </div>
      </div>
    </div>
  )
}

const SSOTab = () => {
  const [ssoProviders, setSsoProviders] = useState([
    {
      id: 1,
      name: 'Azure Active Directory',
      type: 'SAML',
      status: 'active',
      users: 145,
      lastSync: '2 hours ago'
    },
    {
      id: 2,
      name: 'Google Workspace',
      type: 'OIDC',
      status: 'configured',
      users: 0,
      lastSync: 'Never'
    }
  ])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">SSO Configuration</h3>
        <button className="btn btn-primary text-sm">
          <Key className="h-4 w-4 mr-2" />
          Add Provider
        </button>
      </div>

      {/* SSO Providers */}
      <div className="space-y-4">
        {ssoProviders.map((provider) => (
          <div key={provider.id} className="card">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`w-3 h-3 rounded-full ${
                  provider.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'
                }`} />
                <div>
                  <div className="text-white font-medium">{provider.name}</div>
                  <div className="text-sm text-neutral-400">{provider.type} • {provider.users} users</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs ${
                  provider.status === 'active' 
                    ? 'bg-green-500/20 text-green-300' 
                    : 'bg-yellow-500/20 text-yellow-300'
                }`}>
                  {provider.status}
                </span>
                <button className="btn btn-ghost text-sm">Configure</button>
                <button className="btn btn-outline text-sm">Test</button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* SSO Analytics */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">Login Success Rate</h4>
          <div className="text-3xl font-bold text-green-400 mb-2">98.5%</div>
          <div className="text-sm text-neutral-400">Last 30 days</div>
        </div>
        
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">Total Logins</h4>
          <div className="text-3xl font-bold text-blue-400 mb-2">2,847</div>
          <div className="text-sm text-neutral-400">This month</div>
        </div>
        
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-3">Average Session</h4>
          <div className="text-3xl font-bold text-purple-400 mb-2">4.2h</div>
          <div className="text-sm text-neutral-400">Duration</div>
        </div>
      </div>
    </div>
  )
}

const TeamsTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">Team Management</h3>
        <button className="btn btn-primary text-sm">
          <Users className="h-4 w-4 mr-2" />
          Add Team
        </button>
      </div>

      <div className="card">
        <h4 className="text-lg font-semibold text-white mb-4">Team Overview</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-1">12</div>
            <div className="text-sm text-neutral-400">Total Teams</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-1">145</div>
            <div className="text-sm text-neutral-400">Total Members</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-1">89</div>
            <div className="text-sm text-neutral-400">Active Projects</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-1">1,247</div>
            <div className="text-sm text-neutral-400">Workflows Shared</div>
          </div>
        </div>
      </div>

      <div className="text-center py-12">
        <Users className="h-16 w-16 text-neutral-600 mx-auto mb-4" />
        <h4 className="text-xl font-semibold text-white mb-2">Team Management Coming Soon</h4>
        <p className="text-neutral-400 mb-6">
          Advanced team collaboration features are currently in development.
        </p>
        <button className="btn btn-outline">
          Learn More
        </button>
      </div>
    </div>
  )
}

const SettingsTab = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-white">Enterprise Settings</h3>

      <div className="grid lg:grid-cols-2 gap-6">
        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-4">Security Settings</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">Two-Factor Authentication</div>
                <div className="text-sm text-neutral-400">Required for all users</div>
              </div>
              <button className="btn btn-outline text-sm">Configure</button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">Session Timeout</div>
                <div className="text-sm text-neutral-400">8 hours</div>
              </div>
              <button className="btn btn-outline text-sm">Edit</button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">IP Whitelist</div>
                <div className="text-sm text-neutral-400">12 addresses configured</div>
              </div>
              <button className="btn btn-outline text-sm">Manage</button>
            </div>
          </div>
        </div>

        <div className="card">
          <h4 className="text-lg font-semibold text-white mb-4">Billing & Usage</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">Current Plan</div>
                <div className="text-sm text-neutral-400">Enterprise Pro</div>
              </div>
              <button className="btn btn-outline text-sm">Upgrade</button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">Monthly Usage</div>
                <div className="text-sm text-neutral-400">89% of quota used</div>
              </div>
              <button className="btn btn-outline text-sm">View Details</button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">Next Billing</div>
                <div className="text-sm text-neutral-400">January 15, 2025</div>
              </div>
              <button className="btn btn-outline text-sm">Manage</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EnterpriseDashboard