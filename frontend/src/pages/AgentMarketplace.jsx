import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Spinner } from '../components/ui/Spinner'

/**
 * NeuroColony Agent Marketplace
 * Browse, install, and use marketing automation agents
 */
const AgentMarketplace = () => {
  const [marketplaceData, setMarketplaceData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [installedAgents, setInstalledAgents] = useState(new Set())

  useEffect(() => {
    fetchMarketplaceData()
  }, [])

  const fetchMarketplaceData = async () => {
    try {
      // Fetch marketing agent templates
      const templatesResponse = await fetch('/api/colony/templates', {
        headers: {
          'Authorization': `Bear<PERSON> ${localStorage.getItem('token')}`
        }
      })
      const templatesData = await templatesResponse.json()

      if (templatesData.success) {
        setMarketplaceData({
          featuredAgents: [
            ...templatesData.data.templates.queens,
            ...templatesData.data.templates.workers,
            ...templatesData.data.templates.scouts
          ],
          categories: {
            'Email Marketing': templatesData.data.templates.queens.concat(
              templatesData.data.templates.workers.filter(w => w.category === 'email_marketing')
            ),
            'Content Creation': templatesData.data.templates.workers.filter(w => w.category === 'content_creation'),
            'Analytics & Reporting': templatesData.data.templates.scouts.concat(
              templatesData.data.templates.workers.filter(w => w.category === 'analytics_reporting')
            ),
            'Lead Generation': templatesData.data.templates.workers.filter(w => w.category === 'lead_generation'),
            'AI Orchestration': templatesData.data.templates.queens.filter(q => q.category === 'ai_orchestration')
          },
          totalAgents: templatesData.data.total
        })
      }
    } catch (error) {
      console.error('Failed to fetch marketplace data:', error)
    } finally {
      setLoading(false)
    }
  }

  const installAgent = async (templateKey) => {
    try {
      setInstalledAgents(prev => new Set([...prev, templateKey]))

      const response = await fetch('/api/colony/agents/from-template', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          templateKey,
          customizations: {}
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        alert(`Agent installed successfully! You can now use it in your workflows.`)
      } else {
        setInstalledAgents(prev => {
          const newSet = new Set(prev)
          newSet.delete(agentId)
          return newSet
        })
        alert(`Installation failed: ${data.message}`)
      }
    } catch (error) {
      console.error('Agent installation error:', error)
      setInstalledAgents(prev => {
        const newSet = new Set(prev)
        newSet.delete(agentId)
        return newSet
      })
      alert('Failed to install agent')
    }
  }

  const filteredAgents = marketplaceData?.featured?.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || agent.category === selectedCategory
    
    return matchesSearch && matchesCategory
  }) || []

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner size="lg" />
        <span className="ml-3 text-lg">Loading Agent Marketplace...</span>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
               Agent Marketplace
            </h1>
            <p className="text-gray-600 mt-2">
              Install "automation agents" for marketing automation • One-click installation
            </p>
          </div>
          <div className="flex space-x-4">
            <Button 
              onClick={() => window.location.href = '/agent-dashboard'}
              variant="outline"
            >
              ← Back to Dashboard
            </Button>
            <Button 
              onClick={() => window.location.href = '/create-agent'}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Publish Agent
            </Button>
          </div>
        </div>
      </div>

      {/* Marketplace Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Agents</p>
                <p className="text-2xl font-bold text-blue-900">{marketplaceData?.stats?.totalAgents || 0}</p>
                <p className="text-xs text-blue-500">Ready to install</p>
              </div>
              <div className="text-2xl"></div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Active Installs</p>
                <p className="text-2xl font-bold text-green-900">
                  {marketplaceData?.stats?.activeInstalls?.toLocaleString() || '0'}
                </p>
                <p className="text-xs text-green-500">Across all users</p>
              </div>
              <div className="text-2xl">📦</div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Avg Rating</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {marketplaceData?.stats?.averageRating || '0'}
                </p>
                <p className="text-xs text-yellow-500">Community reviewed</p>
              </div>
              <div className="text-2xl">⭐</div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">New This Week</p>
                <p className="text-2xl font-bold text-purple-900">{marketplaceData?.stats?.newThisWeek || 0}</p>
                <p className="text-xs text-purple-500">Fresh agents</p>
              </div>
              <div className="text-2xl">🆕</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search agents by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Category Filters */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            onClick={() => setSelectedCategory('all')}
            size="sm"
          >
            All Categories
          </Button>
          {marketplaceData?.categories?.map(category => (
            <Button
              key={category.name}
              variant={selectedCategory === category.name.toLowerCase().replace(' ', '-') ? 'default' : 'outline'}
              onClick={() => setSelectedCategory(category.name.toLowerCase().replace(' ', '-'))}
              size="sm"
              className="whitespace-nowrap"
            >
              {category.icon} {category.name} ({category.count})
            </Button>
          ))}
        </div>
      </div>

      {/* Featured Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAgents.map(agent => (
          <AgentCard
            key={agent.id}
            agent={agent}
            isInstalled={installedAgents.has(agent.id)}
            onInstall={() => installAgent(agent.id)}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredAgents.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No agents found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search terms or category filters
            </p>
            <Button onClick={() => {
              setSearchTerm('')
              setSelectedCategory('all')
            }}>
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Community Section */}
      <Card className="mt-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
        <CardHeader>
          <CardTitle className="text-indigo-800 flex items-center">
             Community & Development
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-2"></div>
              <h4 className="font-semibold mb-1">Publish Your Agent</h4>
              <p className="text-sm text-gray-600 mb-3">
                Share your marketing automation with the community
              </p>
              <Button size="sm" variant="outline">
                Get Started
              </Button>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">💡</div>
              <h4 className="font-semibold mb-1">Request an Agent</h4>
              <p className="text-sm text-gray-600 mb-3">
                Need a specific marketing automation? Let us know!
              </p>
              <Button size="sm" variant="outline">
                Submit Request
              </Button>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">📚</div>
              <h4 className="font-semibold mb-1">Developer Docs</h4>
              <p className="text-sm text-gray-600 mb-3">
                Learn how to build powerful marketing agents
              </p>
              <Button size="sm" variant="outline">
                Read Docs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Agent Card Component
const AgentCard = ({ agent, isInstalled, onInstall }) => {
  const [installing, setInstalling] = useState(false)

  const handleInstall = async () => {
    setInstalling(true)
    await onInstall()
    setInstalling(false)
  }

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-3xl">{agent.icon}</div>
            <div>
              <CardTitle className="text-lg">{agent.name}</CardTitle>
              <p className="text-sm text-gray-500">by {agent.author}</p>
            </div>
          </div>
          <div className="flex flex-col items-end space-y-1">
            {agent.price === 'Free' ? (
              <Badge className="bg-green-100 text-green-800">Free</Badge>
            ) : (
              <Badge className="bg-blue-100 text-blue-800">{agent.price}</Badge>
            )}
            <div className="flex items-center text-xs text-gray-500">
              <span className="text-yellow-400 mr-1">⭐</span>
              {agent.rating.toFixed(1)}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {agent.description}
        </p>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {agent.tags.map(tag => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
        
        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <span>📦 {agent.downloads.toLocaleString()} downloads</span>
          <span>📂 {agent.category}</span>
        </div>
        
        {/* Action Button */}
        <div className="space-y-2">
          {isInstalled ? (
            <div className="flex space-x-2">
              <Button 
                size="sm" 
                className="flex-1 bg-green-600 hover:bg-green-700"
                disabled
              >
                 Installed
              </Button>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => window.location.href = `/agents/${agent.id}/configure`}
              >
                Configure
              </Button>
            </div>
          ) : (
            <Button 
              size="sm" 
              className="w-full"
              onClick={handleInstall}
              disabled={installing}
            >
              {installing ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Installing...
                </>
              ) : (
                'Install Agent'
              )}
            </Button>
          )}
          
          <Button 
            size="sm" 
            variant="outline" 
            className="w-full"
            onClick={() => window.open(`/agents/${agent.id}/details`, '_blank')}
          >
            View Details
          </Button>
        </div>
        
        {/* Compatibility */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            💻 Compatible with {agent.compatibility}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export default AgentMarketplace