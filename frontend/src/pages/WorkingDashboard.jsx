import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { 
  Zap, FileText, TrendingUp, Users, Mail, 
  Plus, ArrowRight, CheckCircle, Clock,
  BarChart3, Target, Sparkles, Loader2
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

const WorkingDashboard = () => {
  const { user, getApiClient } = useAuth()
  const [stats, setStats] = useState({
    totalSequences: 0,
    totalEmails: 0,
    activeSequences: 0,
    draftSequences: 0,
    recentSequences: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      const apiClient = getApiClient()
      const response = await apiClient.get('/sequences')
      
      if (response.data.success) {
        const sequences = response.data.sequences
        
        setStats({
          totalSequences: sequences.length,
          totalEmails: sequences.reduce((sum, seq) => sum + (seq.emails?.length || 0), 0),
          activeSequences: sequences.filter(seq => seq.status === 'active').length,
          draftSequences: sequences.filter(seq => seq.status === 'draft').length,
          recentSequences: sequences.slice(0, 3) // Most recent 3
        })
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const quickActions = [
    {
      title: 'Create Sequence',
      description: 'Generate a new email sequence',
      icon: Plus,
      link: '/generator-simple',
      color: 'from-amber-400 to-orange-500',
      textColor: 'text-black'
    },
    {
      title: 'View All Sequences',
      description: 'Manage your sequences',
      icon: FileText,
      link: '/sequences',
      color: 'from-blue-500 to-purple-600',
      textColor: 'text-white'
    },
    {
      title: 'Settings',
      description: 'Account & preferences',
      icon: Target,
      link: '/settings',
      color: 'from-gray-600 to-gray-700',
      textColor: 'text-white'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-purple-950/20 to-slate-950">
      {/* Cosmic Background Effect */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]" />
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
      </div>

      <div className="relative max-w-7xl mx-auto px-6 py-12">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12 text-center"
        >
          <h1 className="text-5xl md:text-6xl font-light text-white mb-4 leading-tight">
            Welcome back,
            <span className="block bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent font-medium">
              {user?.name || 'User'}
            </span>
          </h1>
          <p className="text-xl text-slate-300 font-light max-w-2xl mx-auto">
            Ready to create intelligent automation workflows
          </p>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {[
            { 
              label: 'Total Sequences', 
              value: loading ? '...' : stats.totalSequences, 
              icon: FileText, 
              color: 'text-blue-400',
              bgColor: 'bg-blue-500/20'
            },
            { 
              label: 'Total Emails', 
              value: loading ? '...' : stats.totalEmails, 
              icon: Mail, 
              color: 'text-green-400',
              bgColor: 'bg-green-500/20'
            },
            { 
              label: 'Active', 
              value: loading ? '...' : stats.activeSequences, 
              icon: CheckCircle, 
              color: 'text-amber-400',
              bgColor: 'bg-amber-500/20'
            },
            { 
              label: 'Drafts', 
              value: loading ? '...' : stats.draftSequences, 
              icon: Clock, 
              color: 'text-gray-400',
              bgColor: 'bg-gray-500/20'
            }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 + index * 0.05 }}
              className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-6 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-500"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">{stat.label}</p>
                  <p className="text-2xl font-bold text-white">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-3xl font-light text-white mb-8 text-center">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Link to={action.link}>
                  <div className={`bg-gradient-to-r ${action.color} p-8 rounded-2xl hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className={`font-medium text-xl ${action.textColor} mb-3`}>
                          {action.title}
                        </h3>
                        <p className={`text-sm ${action.textColor} opacity-80`}>
                          {action.description}
                        </p>
                      </div>
                      <action.icon className={`h-8 w-8 ${action.textColor}`} />
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recent Sequences */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-light text-white text-center flex-1">
              Recent Activity
            </h2>
            {stats.totalSequences > 0 && (
              <Link 
                to="/sequences" 
                className="text-amber-400 hover:text-amber-300 flex items-center transition-colors"
              >
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            )}
          </div>

          {loading ? (
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-12 text-center backdrop-blur-sm">
              <Loader2 className="h-12 w-12 text-purple-400 mx-auto mb-6 animate-spin" />
              <p className="text-slate-300 text-lg">Loading recent activity...</p>
            </div>
          ) : stats.recentSequences.length === 0 ? (
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-12 text-center backdrop-blur-sm">
              <Sparkles className="h-20 w-20 text-purple-400 mx-auto mb-6" />
              <h3 className="text-2xl font-light text-white mb-4">Ready to Begin</h3>
              <p className="text-slate-400 mb-8 text-lg max-w-md mx-auto">
                Create your first intelligent automation workflow
              </p>
              <Link 
                to="/generator-simple"
                className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-medium text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25 hover:scale-105"
              >
                <Plus className="h-5 w-5" />
                Create Your First Sequence
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {stats.recentSequences.map((sequence, index) => (
                <motion.div
                  key={sequence._id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-6 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-500"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white mb-1">
                        {sequence.title}
                      </h3>
                      <p className="text-gray-400 text-sm mb-2 line-clamp-1">
                        {sequence.description}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{sequence.emails?.length || 0} emails</span>
                        <span>•</span>
                        <span className="capitalize">{sequence.tone}</span>
                        <span>•</span>
                        <span>{formatDate(sequence.createdAt)}</span>
                      </div>
                    </div>
                    <div className="ml-4 flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        sequence.status === 'active' 
                          ? 'bg-green-500/20 text-green-300' 
                          : 'bg-gray-500/20 text-gray-300'
                      }`}>
                        {sequence.status}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Feature Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="relative bg-gradient-to-br from-slate-800/30 to-slate-900/30 border border-slate-700/30 backdrop-blur-sm rounded-3xl p-12"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-3xl" />
          <div className="relative">
            <h2 className="text-3xl font-light text-white mb-10 text-center">
              Platform Features
            </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: Zap,
                title: 'Intelligent Automation',
                description: 'Advanced AI-powered workflows that adapt automatically',
                color: 'text-purple-400'
              },
              {
                icon: Target,
                title: 'Business Intelligence',
                description: 'Deep insights and predictive analytics',
                color: 'text-blue-400'
              },
              {
                icon: TrendingUp,
                title: 'Performance Optimization',
                description: 'Monitor and enhance your automation workflows',
                color: 'text-green-400'
              }
            ].map((feature, index) => (
              <div key={feature.title} className="text-center">
                <feature.icon className={`h-12 w-12 ${feature.color} mx-auto mb-6`} />
                <h3 className="text-xl font-medium text-white mb-3">{feature.title}</h3>
                <p className="text-slate-400 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default WorkingDashboard