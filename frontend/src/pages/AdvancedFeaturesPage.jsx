import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  TestTube, 
  Calendar, 
  Zap, 
  TrendingUp,
  Settings,
  BarChart3,
  Users
} from 'lucide-react'
import ABTestManager from '../components/ABTestManager'
import SequenceScheduler from '../components/SequenceScheduler'
import toast from 'react-hot-toast'

const AdvancedFeaturesPage = ({ user }) => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [sequence, setSequence] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('ab-testing')

  useEffect(() => {
    loadSequence()
  }, [id])

  const loadSequence = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${import.meta.env.VITE_API_URL}/sequences/${id}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSequence(data.data)
        } else {
          toast.error(data.message || 'Failed to load sequence')
          navigate('/dashboard')
        }
      } else {
        toast.error('Failed to load sequence')
        navigate('/dashboard')
      }
    } catch (error) {
      toast.error('Failed to load sequence')
      navigate('/dashboard')
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    {
      id: 'ab-testing',
      label: 'A/B Testing',
      icon: TestTube,
      description: 'Test different versions to optimize performance'
    },
    {
      id: 'scheduling',
      label: 'Scheduling & Automation',
      icon: Calendar,
      description: 'Automate delivery and set up recurring sends'
    },
    {
      id: 'analytics',
      label: 'Advanced Analytics',
      icon: BarChart3,
      description: 'Deep insights and performance tracking'
    },
    {
      id: 'optimization',
      label: 'AI Optimization',
      icon: Zap,
      description: 'AI-powered suggestions and improvements'
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading sequence...</p>
        </div>
      </div>
    )
  }

  if (!sequence) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Sequence not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate('/dashboard')}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{sequence.title}</h1>
              <p className="text-gray-600 mt-1">Advanced Features & Optimization</p>
            </div>
          </div>

          {/* Sequence Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg p-6 mb-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Emails</span>
                </div>
                <span className="text-2xl font-bold text-gray-900">{sequence.emails?.length || 0}</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Status</span>
                </div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  sequence.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : sequence.status === 'draft'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {sequence.status?.charAt(0).toUpperCase() + sequence.status?.slice(1)}
                </span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Settings className="h-5 w-5 text-purple-500 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Industry</span>
                </div>
                <span className="text-sm text-gray-900 capitalize">{sequence.businessInfo?.industry}</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-5 w-5 text-orange-500 mr-2" />
                  <span className="text-sm font-medium text-gray-600">Created</span>
                </div>
                <span className="text-sm text-gray-900">
                  {new Date(sequence.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Feature Tabs */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {tabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => setActiveTab(tab.id)}
                className={`p-6 rounded-lg text-left transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'bg-white text-gray-900 hover:bg-gray-50 shadow'
                }`}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <tab.icon className={`h-6 w-6 ${
                    activeTab === tab.id ? 'text-white' : 'text-primary-600'
                  }`} />
                  <h3 className="font-semibold">{tab.label}</h3>
                </div>
                <p className={`text-sm ${
                  activeTab === tab.id ? 'text-primary-100' : 'text-gray-600'
                }`}>
                  {tab.description}
                </p>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Feature Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'ab-testing' && (
            <ABTestManager 
              sequenceId={sequence._id} 
              emails={sequence.emails}
            />
          )}

          {activeTab === 'scheduling' && (
            <SequenceScheduler 
              sequenceId={sequence._id} 
              emails={sequence.emails}
              title={sequence.title}
            />
          )}

          {activeTab === 'analytics' && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center space-x-2 mb-6">
                <BarChart3 className="h-6 w-6 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">Advanced Analytics</h3>
              </div>
              
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Detailed Analytics Coming Soon</h4>
                <p className="text-gray-500 mb-6">
                  Get deep insights into email performance, subscriber behavior, and conversion funnels.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Heat Maps</h5>
                    <p className="text-sm text-gray-600">See where subscribers click most</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Funnel Analysis</h5>
                    <p className="text-sm text-gray-600">Track conversion at each step</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Cohort Reports</h5>
                    <p className="text-sm text-gray-600">Analyze subscriber segments</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'optimization' && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Zap className="h-6 w-6 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">AI Optimization</h3>
              </div>
              
              <div className="text-center py-12">
                <Zap className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">AI-Powered Optimization</h4>
                <p className="text-gray-500 mb-6">
                  Get personalized recommendations to improve your email sequence performance.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Smart Send Times</h5>
                    <p className="text-sm text-gray-600">AI determines optimal delivery times</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Content Optimization</h5>
                    <p className="text-sm text-gray-600">Suggest improvements for better engagement</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">Predictive Analytics</h5>
                    <p className="text-sm text-gray-600">Forecast performance and outcomes</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>

        {/* Upgrade Prompt for Free Users */}
        {user?.subscription?.type === 'free' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 bg-gradient-to-r from-primary-600 to-blue-600 rounded-lg shadow-lg p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Unlock Full Advanced Features</h3>
                <p className="text-primary-100">
                  Upgrade to Pro to access unlimited A/B tests, advanced scheduling, and AI optimization.
                </p>
              </div>
              <button
                onClick={() => navigate('/pricing')}
                className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Upgrade Now
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default AdvancedFeaturesPage