import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, Search, MessageSquare, Book, Zap, CreditCard, Settings, Users, Download } from 'lucide-react';

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [openFAQ, setOpenFAQ] = useState(null);

  const categories = [
    { id: 'all', name: 'All Questions', icon: Book, count: 24 },
    { id: 'getting-started', name: 'Getting Started', icon: Zap, count: 6 },
    { id: 'ai-generation', name: 'AI Generation', icon: MessageSquare, count: 8 },
    { id: 'billing', name: 'Billing & Plans', icon: CreditCard, count: 5 },
    { id: 'features', name: 'Features', icon: Settings, count: 3 },
    { id: 'integrations', name: 'Integrations', icon: Download, count: 2 }
  ];

  const faqs = [
    // Getting Started
    {
      id: 1,
      category: 'getting-started',
      question: 'How do I create my first email sequence?',
      answer: 'Getting started is simple! Navigate to the Generator page, fill in your business information (industry, product, target audience), configure your preferences, and click "Generate Sequence." Our AI will create a complete email sequence in under 30 seconds.'
    },
    {
      id: 2,
      category: 'getting-started',
      question: 'Do I need any technical knowledge to use NeuroColony?',
      answer: 'Not at all! NeuroColony is designed for marketers and business owners of all technical levels. Simply fill out the form with your business details, and our AI handles all the complex psychology and copywriting work for you.'
    },
    {
      id: 3,
      category: 'getting-started',
      question: 'How long does it take to generate a sequence?',
      answer: 'Most sequences are generated in 15-30 seconds. Complex sequences with advanced psychology frameworks might take up to 60 seconds. You\'ll see a real-time progress indicator during generation.'
    },
    {
      id: 4,
      category: 'getting-started',
      question: 'Can I edit the generated sequences?',
      answer: 'Yes! All generated sequences are fully editable. You can modify subject lines, email content, timing, and psychology triggers. Think of our AI as your starting point - customize to match your brand voice perfectly.'
    },
    {
      id: 5,
      category: 'getting-started',
      question: 'What information do I need to provide for generation?',
      answer: 'You\'ll need: your industry, product/service description, target audience, price point, and key benefits/pain points. Optional information like unique selling proposition helps create more targeted sequences.'
    },
    {
      id: 6,
      category: 'getting-started',
      question: 'Are there templates I can use?',
      answer: 'Yes! We offer industry-specific templates for common use cases like welcome sequences, product launches, webinar promotions, and nurture campaigns. Templates save time while maintaining quality.'
    },

    // AI Generation
    {
      id: 7,
      category: 'ai-generation',
      question: 'What makes NeuroColony\'s AI different from other tools?',
      answer: 'Our AI uses advanced psychology frameworks including 8 cognitive biases, emotional journey design, and industry-specific optimization. It doesn\'t just write emails - it creates psychological conversion journeys tailored to your audience.'
    },
    {
      id: 8,
      category: 'ai-generation',
      question: 'How does the psychology scoring work?',
      answer: 'Our AI analyzes each email for psychology depth (cognitive biases), copywriting excellence, industry optimization, sequence flow, and conversion predictors. Scores range from 0-100, with 85+ considered world-class quality.'
    },
    {
      id: 9,
      category: 'ai-generation',
      question: 'Can I specify which psychology triggers to use?',
      answer: 'Yes! While our AI automatically selects optimal triggers based on your industry and audience, you can specify preferences like scarcity, social proof, authority, urgency, or reciprocity in the advanced settings.'
    },
    {
      id: 10,
      category: 'ai-generation',
      question: 'What industries does the AI understand?',
      answer: 'Our AI has specialized knowledge for B2B SaaS, E-commerce, Professional Services, Health & Wellness, Education, Real Estate, Financial Services, and more. It adapts language, pain points, and triggers for each industry.'
    },
    {
      id: 11,
      category: 'ai-generation',
      question: 'How accurate are the conversion rate predictions?',
      answer: 'Our predictions are based on analysis of 50,000+ successful email campaigns. Predictions typically fall within 15-20% of actual results. Higher-scored sequences (90+) consistently outperform industry averages by 60-80%.'
    },
    {
      id: 12,
      category: 'ai-generation',
      question: 'Can the AI create sequences in different languages?',
      answer: 'Currently, our AI generates sequences in English with psychology frameworks optimized for English-speaking markets. Multi-language support is planned for Q2 2025.'
    },
    {
      id: 13,
      category: 'ai-generation',
      question: 'What\'s the optimal sequence length?',
      answer: 'It depends on your goals: 3-5 emails for quick conversions, 7 emails for relationship building, 10+ for complex sales cycles. Our AI recommends optimal length based on your industry and price point.'
    },
    {
      id: 14,
      category: 'ai-generation',
      question: 'How does the emotional journey design work?',
      answer: 'Our AI maps emotional states across your sequence: Curiosity → Concern → Hope → Trust → Urgency → Confidence → Commitment. Each email is designed to create specific emotions that naturally lead to the next.'
    },

    // Billing & Plans
    {
      id: 15,
      category: 'billing',
      question: 'What happens when I reach my monthly limit?',
      answer: 'Free users must wait for the next billing cycle or upgrade. Pro and Business users can enable overage billing at $3 per additional sequence. You\'ll receive notifications at 80% and 95% usage.'
    },
    {
      id: 16,
      category: 'billing',
      question: 'How does overage billing work?',
      answer: 'When you reach your monthly limit, you\'ll be prompted to enable overage billing. Once confirmed, additional sequences cost $3 each. Overage charges appear on your next invoice with detailed usage breakdown.'
    },
    {
      id: 17,
      category: 'billing',
      question: 'Can I change plans anytime?',
      answer: 'Yes! Upgrades take effect immediately with prorated billing. Downgrades take effect at the next billing cycle. Your sequences remain accessible regardless of plan changes.'
    },
    {
      id: 18,
      category: 'billing',
      question: 'What is your cancellation policy?',
      answer: 'You can cancel your subscription anytime with no cancellation fees. When you cancel, you keep access to all your generated sequences forever and can still export them in any format.'
    },
    {
      id: 19,
      category: 'billing',
      question: 'Do unused sequences roll over?',
      answer: 'No, sequences don\'t roll over between billing cycles. However, all your generated sequences remain accessible forever, and you can export them anytime regardless of your current plan.'
    },

    // Features
    {
      id: 20,
      category: 'features',
      question: 'What export formats are available?',
      answer: 'You can export sequences as CSV (for spreadsheets), and formats compatible with leading email marketing platforms, plain text, and JSON. We also provide copy-paste ready formats for manual entry.'
    },
    {
      id: 21,
      category: 'features',
      question: 'Can I A/B test my sequences?',
      answer: 'Yes! Our A/B testing feature lets you test subject lines, email content, or send times. The system automatically tracks performance and identifies winning variations.'
    },
    {
      id: 22,
      category: 'features',
      question: 'How does the analytics dashboard work?',
      answer: 'Track open rates, click rates, conversion rates, and revenue per sequence. View performance trends, psychology trigger effectiveness, and get recommendations for optimization.'
    },

    // Integrations
    {
      id: 23,
      category: 'integrations',
      question: 'Which email platforms integrate with NeuroColony?',
      answer: 'We support direct exports to all major email marketing platforms through our universal export formats. For platforms without direct integration, use our universal CSV export format.'
    },
    {
      id: 24,
      category: 'integrations',
      question: 'Can I connect my CRM to NeuroColony?',
      answer: 'Yes! We integrate with all major CRM platforms through our integration hub. This allows automatic sequence creation based on lead source, deal stage, or custom triggers.'
    }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (id) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4">
            <Link to="/" className="text-blue-600 hover:text-blue-700 transition-colors">
              ← Back to Home
            </Link>
            <div className="h-6 border-l border-gray-300"></div>
            <h1 className="text-2xl font-bold text-gray-900">Frequently Asked Questions</h1>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              How Can We Help You?
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Find answers to common questions about NeuroColony's features, billing, and AI generation.
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search for answers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-4 gap-8">
          
          {/* Category Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                      activeCategory === category.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <category.icon className="h-5 w-5" />
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* FAQ List */}
          <div className="lg:col-span-3">
            <div className="space-y-4">
              {filteredFAQs.length > 0 ? (
                filteredFAQs.map((faq, index) => (
                  <motion.div
                    key={faq.id}
                    className="bg-white rounded-lg shadow-sm border border-gray-200"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                    >
                      <h3 className="text-lg font-medium text-gray-900 pr-4">
                        {faq.question}
                      </h3>
                      {openFAQ === faq.id ? (
                        <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                      )}
                    </button>
                    
                    {openFAQ === faq.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="px-6 pb-4"
                      >
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-gray-700 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                ))
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <Search className="h-12 w-12 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-600">
                    Try adjusting your search or selecting a different category.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="bg-gray-900 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold mb-4">Still Have Questions?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Our support team is here to help you succeed with NeuroColony.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                to="/contact"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Contact Support
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="border border-gray-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
              >
                Email Us
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;