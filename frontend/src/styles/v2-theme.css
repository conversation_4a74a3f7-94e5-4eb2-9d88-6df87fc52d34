/* SequenceAI v2.0 - Ultra-Modern Theme */

:root {
  /* Quantum Colors */
  --quantum-purple: #8B5CF6;
  --quantum-blue: #3B82F6;
  --quantum-cyan: #06B6D4;
  --quantum-pink: #EC4899;
  --quantum-indigo: #6366F1;
  
  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --blur-amount: 20px;
  
  /* Dark Mode */
  --dark-bg: #0F0F23;
  --dark-surface: #1A1A2E;
  --dark-accent: #16213E;
  
  /* Neon Glow */
  --neon-glow: 0 0 20px rgba(139, 92, 246, 0.5);
  --hover-glow: 0 0 30px rgba(139, 92, 246, 0.8);
}

/* Glassmorphic Components */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--blur-amount));
  -webkit-backdrop-filter: blur(var(--blur-amount));
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--neon-glow), 0 8px 32px 0 rgba(31, 38, 135, 0.5);
}

/* Quantum Gradient Buttons */
.quantum-button {
  background: linear-gradient(135deg, var(--quantum-purple), var(--quantum-blue));
  border: none;
  padding: 12px 32px;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.quantum-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.quantum-button:hover::before {
  left: 100%;
}

.quantum-button:hover {
  transform: scale(1.05);
  box-shadow: var(--hover-glow);
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Holographic Text */
.holographic {
  background: linear-gradient(45deg, #ff0099, #00ff99, #0099ff, #ff0099);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographic 3s ease-in-out infinite;
}

@keyframes holographic {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 3D Card Effect */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.card-3d:hover {
  transform: rotateY(10deg) rotateX(-10deg);
}

/* Neon Input Fields */
.neon-input {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 10px;
  padding: 12px 20px;
  color: white;
  transition: all 0.3s ease;
}

.neon-input:focus {
  outline: none;
  border-color: var(--quantum-purple);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

/* Particle Background */
.particle-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: 
    radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
}

/* Loading Animation */
.quantum-loader {
  width: 50px;
  height: 50px;
  position: relative;
}

.quantum-loader::before,
.quantum-loader::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: var(--quantum-purple);
  animation: quantum-spin 1s linear infinite;
}

.quantum-loader::after {
  border-top-color: var(--quantum-blue);
  animation-delay: 0.5s;
}

@keyframes quantum-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .quantum-button {
    padding: 10px 24px;
    font-size: 14px;
  }
}