/* NeuroColony Neural Network Animations - GPU Accelerated */

/* Neural Network Background Animation */
@keyframes neural-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.neural-flow-bg {
  background: linear-gradient(
    -45deg,
    #0a0a0a,
    #1a1a1a,
    #8B5CF6,
    #EC4899,
    #1a1a1a,
    #0a0a0a
  );
  background-size: 400% 400%;
  animation: neural-flow 15s ease infinite;
}

/* Particle Connection Animation */
@keyframes particle-connect {
  0% {
    opacity: 0;
    transform: scale(0.5) translateX(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) translateX(50px);
  }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Base animation utilities */
@keyframes neural-pulse {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes data-flow {
  0% {
    stroke-dashoffset: 100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: 0%;
    opacity: 0;
  }
}

@keyframes hexagon-rotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0) translateZ(0); }
  25% { transform: translateY(-10px) translateZ(0); }
  75% { transform: translateY(10px) translateZ(0); }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3),
                0 0 40px rgba(139, 92, 246, 0.1);
  }
  50% { 
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5),
                0 0 60px rgba(139, 92, 246, 0.2);
  }
}

@keyframes success-bounce {
  0% { transform: scale(0.8) translateZ(0); opacity: 0; }
  50% { transform: scale(1.2) translateZ(0); }
  100% { transform: scale(1) translateZ(0); opacity: 1; }
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0) translateZ(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px) translateZ(0); }
  20%, 40%, 60%, 80% { transform: translateX(10px) translateZ(0); }
}

@keyframes slide-up {
  from { 
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to { 
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scale-in {
  from { 
    opacity: 0;
    transform: scale(0.9) translateZ(0);
  }
  to { 
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes connection-pulse {
  0%, 100% {
    stroke-opacity: 0.2;
    stroke-width: 1;
  }
  50% {
    stroke-opacity: 0.8;
    stroke-width: 2;
  }
}

/* Neural network connection lines */
.neural-connection {
  stroke: url(#neural-gradient);
  stroke-dasharray: 100;
  animation: data-flow 3s ease-in-out infinite;
  will-change: stroke-dashoffset, opacity;
}

.neural-node {
  animation: neural-pulse 2s ease-in-out infinite;
  will-change: opacity, transform;
}

/* Card animations */
.card-animated {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-animated:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Hexagonal grid */
.hex-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, filter;
}

.hex-grid-item:hover {
  animation: hexagon-rotate 0.6s ease-in-out;
  filter: brightness(1.2) saturate(1.2);
}

/* Progress animations */
.progress-bar-animated {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(139, 92, 246, 0.3),
    transparent
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  will-change: background-position;
}

/* Agent status indicators */
.agent-active {
  position: relative;
}

.agent-active::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: linear-gradient(45deg, #8b5cf6, #3b82f6);
  animation: glow-pulse 2s ease-in-out infinite;
  opacity: 0.5;
  z-index: -1;
  will-change: box-shadow;
}

/* Success/Error states */
.success-animation {
  animation: success-bounce 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  will-change: transform, opacity;
}

.error-animation {
  animation: error-shake 0.5s ease-in-out;
  will-change: transform;
}

/* Micro-interactions */
.button-press {
  transition: transform 0.15s ease-out;
  will-change: transform;
}

.button-press:active {
  transform: scale(0.95) translateZ(0);
}

/* Floating particles */
.particle {
  animation: particle-float 6s ease-in-out infinite;
  will-change: transform;
}

.particle:nth-child(2n) {
  animation-delay: -2s;
  animation-duration: 8s;
}

.particle:nth-child(3n) {
  animation-delay: -4s;
  animation-duration: 10s;
}

/* Loading skeletons */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  border-radius: 0.375rem;
  will-change: background-position;
}

/* Theme transitions */
.theme-transition {
  transition: background-color 0.3s ease-out,
              color 0.3s ease-out,
              border-color 0.3s ease-out;
}

/* Optimized animations */
.animate-neural-pulse { animation: neural-pulse 2s ease-in-out infinite; }
.animate-data-flow { animation: data-flow 3s ease-in-out infinite; }
.animate-hex-rotate { animation: hexagon-rotate 0.6s ease-in-out; }
.animate-float { animation: particle-float 6s ease-in-out infinite; }
.animate-glow-pulse { animation: glow-pulse 2s ease-in-out infinite; }
.animate-slide-up { animation: slide-up 0.5s ease-out; }
.animate-fade-in { animation: fade-in 0.5s ease-out; }
.animate-scale-in { animation: scale-in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55); }

/* Animation delays */
.animation-delay-100 { animation-delay: 100ms; }
.animation-delay-200 { animation-delay: 200ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-500 { animation-delay: 500ms; }
.animation-delay-1000 { animation-delay: 1000ms; }

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Gradients for neural effects */
svg defs {
  display: none;
}

#neural-gradient stop:first-child {
  stop-color: #8b5cf6;
  stop-opacity: 0.8;
}

#neural-gradient stop:last-child {
  stop-color: #3b82f6;
  stop-opacity: 0.3;
}