/* SequenceAI Global Styles - Cosmic Theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');
@import './logo-enhancements.css';
@import './cosmic-enhancements.css';

/* CSS Variables */
:root {
  /* Colors */
  --color-primary: #8b5cf6;
  --color-primary-dark: #6d28d9;
  --color-primary-light: #a78bfa;
  --color-secondary: #f59e0b;
  --color-secondary-dark: #d97706;
  
  /* Backgrounds */
  --color-background: #0a0a0a;
  --color-surface: #171717;
  --color-surface-light: #262626;
  --color-surface-lighter: #404040;
  
  /* Text */
  --color-text: #fafafa;
  --color-text-muted: #a3a3a3;
  --color-text-subtle: #737373;
  
  /* Borders */
  --color-border: #525252;
  --color-border-light: rgba(139, 92, 246, 0.2);
  
  /* Status */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
  --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-hero: linear-gradient(135deg, #0a0a0a 0%, #171717 25%, #2e1065 75%, #6d28d9 100%);
  --gradient-card: linear-gradient(145deg, #1a1a1a 0%, #2a2a2a 100%);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.5);
  --shadow-purple: 0 10px 15px -3px rgba(139, 92, 246, 0.3);
  
  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
}

/* Base Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: var(--color-background);
  color: var(--color-text);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-dark);
}

/* Selection */
::selection {
  background: var(--color-primary);
  color: white;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-text);
}

h1 {
  font-size: 3rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: 2.25rem;
}

h3 {
  font-size: 1.875rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  color: var(--color-text-muted);
  line-height: 1.7;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-normal) ease;
}

a:hover {
  color: var(--color-primary-light);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-purple);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 25px -5px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.39);
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 25px -5px rgba(245, 158, 11, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: white;
}

.btn-ghost {
  background: rgba(139, 92, 246, 0.1);
  color: var(--color-primary);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.btn-ghost:hover:not(:disabled) {
  background: rgba(139, 92, 246, 0.2);
}

/* Cards */
.card {
  background: var(--gradient-card);
  border: 1px solid var(--color-border-light);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  transition: all var(--duration-normal) ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.card-gradient {
  position: relative;
  overflow: hidden;
}

.card-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
}

/* Form Elements */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(38, 38, 38, 0.8);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  color: var(--color-text);
  font-size: 0.875rem;
  transition: all var(--duration-normal) ease;
}

.form-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  background: rgba(38, 38, 38, 0.9);
}

.form-input::placeholder {
  color: var(--color-text-subtle);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(139, 92, 246, 0.2),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Conversion optimization animations */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
  }
}

/* Urgency effects */
.urgency-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Scarcity indicators */
.scarcity-shake {
  animation: shake 0.5s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Range input styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--color-surface);
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

input[type="range"]:hover {
  opacity: 1;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-background);
}

input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-background);
}

/* Glass Effect */
.glass {
  background: rgba(26, 26, 26, 0.7);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-enhanced {
  background: linear-gradient(45deg, #8b5cf6, #f59e0b, #8b5cf6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.gradient-text-gold {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Utilities */
.text-glow {
  text-shadow: 0 0 10px var(--color-primary);
}

.border-glow {
  box-shadow: 0 0 0 1px var(--color-primary), var(--shadow-glow);
}

/* Responsive */
@media (max-width: 768px) {
  h1 {
    font-size: 2.25rem;
  }
  
  h2 {
    font-size: 1.875rem;
  }
  
  .card {
    padding: 1rem;
  }
  
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8125rem;
  }
}

/* Dark mode enhancements */
.dark {
  color-scheme: dark;
}

/* Floating particles animation */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0.1;
  animation: float 10s infinite ease-in-out;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.1;
  }
  25% { 
    transform: translateY(-30px) translateX(10px) scale(1.1);
    opacity: 0.3;
  }
  50% { 
    transform: translateY(-20px) translateX(-15px) scale(0.9);
    opacity: 0.2;
  }
  75% { 
    transform: translateY(-40px) translateX(20px) scale(1.2);
    opacity: 0.15;
  }
}

/* Interactive hover effects */
.interactive-glow {
  transition: all 0.3s ease;
  position: relative;
}

.interactive-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.interactive-glow:hover::before {
  opacity: 1;
}

/* Sophisticated button effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  background: var(--gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.btn-premium:hover::before {
  left: 100%;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.6);
}

/* Enhanced card effects */
.card-premium {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-premium:hover::before {
  opacity: 1;
}

.card-premium:hover {
  transform: translateY(-8px);
  border-color: var(--color-primary);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

/* Ripple effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.ripple:active::after {
  width: 300px;
  height: 300px;
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
  }
}