// Professional Dark Purple Theme for NeuroColony
// Color palette: Purple, Black, Gold accents

export const theme = {
  // Primary Colors - Purple Gradient
  colors: {
    primary: {
      50: '#f3f1ff',
      100: '#ede9fe',
      200: '#ddd6fe',
      300: '#c4b5fd',
      400: '#a78bfa',
      500: '#8b5cf6',  // Main purple
      600: '#7c3aed',
      700: '#6d28d9',
      800: '#5b21b6',
      900: '#4c1d95',
      950: '#2e1065'
    },
    
    // Secondary Colors - Gold/Amber accents
    secondary: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',  // Main gold
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },
    
    // Neutral Colors - Dark theme
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',  // Main dark
      900: '#171717',  // Darker
      950: '#0a0a0a'   // Darkest
    },
    
    // Status Colors
    success: {
      500: '#10b981',
      600: '#059669',
      700: '#047857'
    },
    warning: {
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309'
    },
    error: {
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c'
    },
    info: {
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8'
    }
  },
  
  // Background gradients
  gradients: {
    primary: 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)',
    secondary: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
    dark: 'linear-gradient(135deg, #171717 0%, #262626 100%)',
    hero: 'linear-gradient(135deg, #0a0a0a 0%, #171717 25%, #2e1065 75%, #6d28d9 100%)',
    card: 'linear-gradient(145deg, #1a1a1a 0%, #2a2a2a 100%)',
    purple: 'linear-gradient(135deg, #6d28d9 0%, #8b5cf6 50%, #a78bfa 100%)'
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem'
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800'
    }
  },
  
  // Spacing
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem'
  },
  
  // Border radius
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px'
  },
  
  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    purple: '0 10px 15px -3px rgba(139, 92, 246, 0.3)',
    gold: '0 10px 15px -3px rgba(245, 158, 11, 0.3)',
    glow: '0 0 20px rgba(139, 92, 246, 0.5)'
  },
  
  // Animation
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms'
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out'
    }
  },
  
  // Component-specific styles
  components: {
    button: {
      primary: {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)',
        hover: 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)',
        shadow: '0 4px 14px 0 rgba(139, 92, 246, 0.39)'
      },
      secondary: {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        hover: 'linear-gradient(135deg, #d97706 0%, #b45309 100%)',
        shadow: '0 4px 14px 0 rgba(245, 158, 11, 0.39)'
      },
      dark: {
        background: '#262626',
        hover: '#404040',
        border: '#525252'
      }
    },
    card: {
      background: 'rgba(26, 26, 26, 0.8)',
      border: 'rgba(139, 92, 246, 0.2)',
      shadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
      backdropFilter: 'blur(10px)'
    },
    input: {
      background: 'rgba(38, 38, 38, 0.8)',
      border: 'rgba(115, 115, 115, 0.3)',
      focus: 'rgba(139, 92, 246, 0.5)'
    }
  }
};

// CSS Custom Properties for dynamic theming
export const cssVariables = {
  '--color-primary': theme.colors.primary[500],
  '--color-primary-dark': theme.colors.primary[700],
  '--color-secondary': theme.colors.secondary[500],
  '--color-background': theme.colors.neutral[950],
  '--color-surface': theme.colors.neutral[900],
  '--color-surface-light': theme.colors.neutral[800],
  '--color-text': theme.colors.neutral[50],
  '--color-text-muted': theme.colors.neutral[400],
  '--gradient-primary': theme.gradients.primary,
  '--gradient-hero': theme.gradients.hero,
  '--shadow-glow': theme.shadows.glow
};

export default theme;