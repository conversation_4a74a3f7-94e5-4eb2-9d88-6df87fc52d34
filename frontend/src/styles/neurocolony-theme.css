/* NeuroColony Ultimate Theme - Purple/Pink Neural Network Design */

/* CSS Variables for consistent theming */
:root {
  /* Primary Purple/Pink Gradient Colors */
  --neural-purple: #8B5CF6;
  --neural-pink: #EC4899;
  --neural-purple-dark: #6B21A8;
  --neural-pink-dark: #BE185D;
  --neural-purple-light: #C084FC;
  --neural-pink-light: #F472B6;
  
  /* Dark backgrounds */
  --bg-primary: #0A0A0A;
  --bg-secondary: #111111;
  --bg-tertiary: #1A1A1A;
  --bg-card: rgba(17, 24, 39, 0.8);
  --bg-card-hover: rgba(31, 41, 55, 0.9);
  
  /* Text colors with proper contrast */
  --text-primary: #F3F4F6;
  --text-secondary: #D1D5DB;
  --text-muted: #9CA3AF;
  --text-dim: #6B7280;
  
  /* Border colors */
  --border-default: rgba(139, 92, 246, 0.2);
  --border-hover: rgba(139, 92, 246, 0.4);
  --border-focus: rgba(236, 72, 153, 0.6);
  
  /* Glow effects */
  --glow-purple: 0 0 40px rgba(139, 92, 246, 0.6);
  --glow-pink: 0 0 40px rgba(236, 72, 153, 0.6);
  --glow-combined: 0 0 40px rgba(139, 92, 246, 0.6), 0 0 60px rgba(236, 72, 153, 0.4);
}

/* Global Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--neural-purple) var(--bg-secondary);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--neural-purple), var(--neural-pink));
  border-radius: 4px;
}

/* Typography with proper contrast */
h1, h2, h3, h4, h5, h6 {
  @apply font-bold;
  color: var(--text-primary);
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

p, span, div {
  color: var(--text-secondary);
}

a {
  color: var(--neural-purple-light);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--neural-pink-light);
  text-shadow: var(--glow-purple);
}

/* Neural Network Background Pattern */
.neural-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.neural-bg::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  background-image: 
    radial-gradient(circle at 20% 30%, var(--neural-purple) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, var(--neural-pink) 0%, transparent 40%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 60%);
  opacity: 0.15;
  animation: neural-drift 20s ease-in-out infinite;
}

@keyframes neural-drift {
  0%, 100% { transform: translate(-5%, -5%) rotate(0deg); }
  33% { transform: translate(5%, -5%) rotate(120deg); }
  66% { transform: translate(-5%, 5%) rotate(240deg); }
}

/* Hexagonal Card Designs */
.hex-card {
  position: relative;
  background: var(--bg-card);
  border: 1px solid var(--border-default);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.hex-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.1) 0%, 
    rgba(236, 72, 153, 0.1) 50%, 
    rgba(139, 92, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.hex-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: var(--border-hover);
  box-shadow: var(--glow-combined);
}

.hex-card:hover::before {
  opacity: 1;
}

/* Neural Pulse Animation */
.neural-pulse {
  position: relative;
  overflow: hidden;
}

.neural-pulse::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--neural-purple) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  animation: pulse-wave 2s ease-out infinite;
}

@keyframes pulse-wave {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Colony Command Center Buttons */
.btn-colony {
  position: relative;
  background: linear-gradient(135deg, var(--neural-purple) 0%, var(--neural-pink) 100%);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-colony::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-colony:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--glow-combined);
}

.btn-colony:hover::before {
  left: 100%;
}

.btn-colony:active {
  transform: translateY(0) scale(0.98);
}

/* Glass Morphism Cards */
.glass-card {
  background: rgba(17, 24, 39, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(139, 92, 246, 0.15);
  transition: all 0.3s ease;
}

.glass-card:hover {
  border-color: rgba(236, 72, 153, 0.4);
  box-shadow: 0 8px 48px 0 rgba(236, 72, 153, 0.25);
  transform: translateY(-2px);
}

/* Neural Network Connection Lines */
.neural-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.neural-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--neural-purple) 50%, 
    transparent 100%);
  transform-origin: left center;
  animation: neural-trace 3s linear infinite;
  opacity: 0.6;
}

@keyframes neural-trace {
  0% {
    transform: scaleX(0) translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: scaleX(1) translateX(100%);
    opacity: 0;
  }
}

/* Status Indicators with Glow */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
}

.status-online {
  background: var(--neural-purple);
  box-shadow: 0 0 20px var(--neural-purple);
  animation: status-pulse 2s ease-in-out infinite;
}

.status-processing {
  background: var(--neural-pink);
  box-shadow: 0 0 20px var(--neural-pink);
  animation: status-spin 1s linear infinite;
}

@keyframes status-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes status-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Elements with Neural Theme */
.neural-input {
  background: rgba(17, 24, 39, 0.5);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: var(--text-primary);
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 44px;
}

.neural-input:focus {
  outline: none;
  border-color: var(--neural-purple);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
  background: rgba(17, 24, 39, 0.8);
}

.neural-input::placeholder {
  color: var(--text-dim);
}

/* Colony Navigation */
.nav-neural {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.nav-item {
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--neural-purple), var(--neural-pink));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-item:hover {
  color: var(--text-primary);
  background: rgba(139, 92, 246, 0.1);
}

.nav-item:hover::before {
  transform: scaleX(1);
}

/* Loading States */
.neural-loader {
  width: 48px;
  height: 48px;
  position: relative;
}

.neural-loader::before,
.neural-loader::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
}

.neural-loader::before {
  border-top-color: var(--neural-purple);
  animation: loader-spin 1s linear infinite;
}

.neural-loader::after {
  border-bottom-color: var(--neural-pink);
  animation: loader-spin 1s linear infinite reverse;
}

@keyframes loader-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltips */
.neural-tooltip {
  background: rgba(17, 24, 39, 0.95);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  font-size: 14px;
}

/* Modal Overlays */
.neural-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

.neural-modal {
  background: var(--bg-secondary);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 16px;
  box-shadow: var(--glow-combined);
}

/* Responsive Touch Targets */
@media (max-width: 768px) {
  .btn-colony,
  .neural-input,
  button,
  a {
    min-height: 44px;
    min-width: 44px;
  }
  
  .nav-item {
    padding: 12px 16px;
  }
}

/* Accessibility - High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #FFFFFF;
    --text-secondary: #F3F4F6;
    --text-muted: #E5E7EB;
    --border-default: rgba(139, 92, 246, 0.5);
    --border-hover: rgba(139, 92, 246, 0.8);
  }
}

/* Print Styles */
@media print {
  .neural-bg,
  .neural-lines,
  .neural-loader {
    display: none;
  }
  
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Colony Agent Cards */
.agent-card {
  @apply glass-card;
  position: relative;
  overflow: hidden;
}

.agent-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg at 50% 50%,
    transparent 0deg,
    var(--neural-purple) 60deg,
    transparent 120deg,
    var(--neural-pink) 180deg,
    transparent 240deg,
    var(--neural-purple) 300deg,
    transparent 360deg
  );
  animation: agent-scan 4s linear infinite;
  opacity: 0.1;
}

@keyframes agent-scan {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Final Polish */
.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s;
}

.shine-effect:hover::after {
  transform: rotate(45deg) translateX(100%);
}

/* Neural Particles Floating */
.neural-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--neural-purple);
  border-radius: 50%;
  opacity: 0;
  animation: particle-float 10s infinite;
}

.neural-particle:nth-child(2) {
  background: var(--neural-pink);
  animation-delay: 2s;
}

.neural-particle:nth-child(3) {
  background: var(--neural-purple-light);
  animation-delay: 4s;
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) scale(1.5);
    opacity: 0;
  }
}

/* Hexagon Pattern Overlay */
.hexagon-pattern {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    repeating-linear-gradient(
      30deg,
      transparent,
      transparent 35px,
      rgba(139, 92, 246, 0.03) 35px,
      rgba(139, 92, 246, 0.03) 70px
    ),
    repeating-linear-gradient(
      -30deg,
      transparent,
      transparent 35px,
      rgba(236, 72, 153, 0.03) 35px,
      rgba(236, 72, 153, 0.03) 70px
    );
  pointer-events: none;
}

/* Command Center Dashboard Grid */
.command-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  position: relative;
}

.command-grid::before {
  content: '';
  position: absolute;
  inset: -50px;
  background: 
    radial-gradient(circle at 30% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 50%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  filter: blur(40px);
  pointer-events: none;
  z-index: -1;
}

/* Neural Network Visualization */
.neural-network-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
}

.neural-node {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--neural-purple);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--neural-purple);
}

.neural-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent,
    var(--neural-purple) 30%,
    var(--neural-pink) 70%,
    transparent
  );
  transform-origin: left center;
  opacity: 0.3;
}

/* Text Glow Effects */
.text-glow-purple {
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
}

.text-glow-pink {
  text-shadow: 0 0 20px rgba(236, 72, 153, 0.6);
}

.text-gradient-neural {
  background: linear-gradient(135deg, var(--neural-purple), var(--neural-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}