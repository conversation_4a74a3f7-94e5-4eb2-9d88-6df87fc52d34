/* Cosmic Typography and Animations */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap');

/* Enhanced CSS Variables */
:root {
  /* Enhanced Colors */
  --color-cosmic-purple: #6366f1;
  --color-cosmic-blue: #3b82f6;
  --color-cosmic-indigo: #4f46e5;
  --color-cosmic-violet: #8b5cf6;
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;
  --color-slate-950: #020617;
  
  /* Cosmic Gradients */
  --gradient-cosmic: linear-gradient(135deg, #6366f1 0%, #8b5cf6 25%, #3b82f6  75%, #1e293b 100%);
  --gradient-cosmic-dark: linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 100%);
  --gradient-cosmic-glow: radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.15) 0%, transparent 70%);
  
  /* Enhanced Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', Monaco, 'Cascadia Code', monospace;
  
  /* Enhanced Animations */
  --duration-cosmic: 600ms;
  --easing-cosmic: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Typography Enhancements */
body {
  font-family: var(--font-primary);
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 500;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Cosmic Button Enhancements */
.btn-cosmic {
  @apply relative overflow-hidden;
  transition: all var(--duration-cosmic) var(--easing-cosmic);
}

.btn-cosmic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-cosmic) var(--easing-cosmic);
}

.btn-cosmic:hover::before {
  left: 100%;
}

/* Enhanced Glass Effect */
.glass-cosmic {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(99, 102, 241, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Cosmic Animations */
@keyframes cosmic-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes cosmic-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.8;
  }
  50% { 
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes cosmic-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
  }
}

@keyframes cosmic-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-cosmic-float {
  animation: cosmic-float 6s ease-in-out infinite;
}

.animate-cosmic-pulse {
  animation: cosmic-pulse 3s ease-in-out infinite;
}

.animate-cosmic-glow {
  animation: cosmic-glow 4s ease-in-out infinite;
}

/* Enhanced Text Effects */
.text-cosmic-gradient {
  @apply bg-gradient-to-r from-indigo-400 via-purple-400 to-blue-400 bg-clip-text text-transparent;
}

.text-shimmer {
  position: relative;
  overflow: hidden;
}

.text-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: cosmic-shimmer 3s infinite;
}

/* Enhanced Card Effects */
.card-cosmic {
  @apply relative rounded-2xl overflow-hidden;
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.5), rgba(15, 23, 42, 0.8));
  border: 1px solid rgba(99, 102, 241, 0.2);
  transition: all var(--duration-cosmic) var(--easing-cosmic);
}

.card-cosmic:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(99, 102, 241, 0.4);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(99, 102, 241, 0.2);
}

.card-cosmic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), transparent 50%, rgba(59, 130, 246, 0.1));
  opacity: 0;
  transition: opacity var(--duration-cosmic) var(--easing-cosmic);
}

.card-cosmic:hover::before {
  opacity: 1;
}

/* Enhanced Input Styles */
.input-cosmic {
  @apply bg-slate-800/50 border border-slate-700 rounded-xl px-4 py-3 text-white placeholder-slate-400;
  backdrop-filter: blur(10px);
  transition: all var(--duration-cosmic) var(--easing-cosmic);
}

.input-cosmic:focus {
  @apply outline-none border-indigo-500 ring-2 ring-indigo-500/20;
  background: rgba(30, 41, 59, 0.8);
}

/* Cosmic Background Patterns */
.bg-cosmic-dots {
  background-image: radial-gradient(circle at 2px 2px, rgba(99, 102, 241, 0.3) 1px, transparent 0);
  background-size: 20px 20px;
}

.bg-cosmic-grid {
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-slate-900);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--color-cosmic-purple), var(--color-cosmic-blue));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--color-cosmic-blue), var(--color-cosmic-purple));
}

/* Enhanced Selection */
::selection {
  background: rgba(99, 102, 241, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(99, 102, 241, 0.3);
  color: #ffffff;
}