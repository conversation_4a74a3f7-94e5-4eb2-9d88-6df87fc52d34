/* NeuroColony Colony Theme CSS */
/* Comprehensive visual enhancements with proper contrast and neural theming */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Root Variables */
:root {
  /* Neural Colors */
  --neural-50: #f0f9ff;
  --neural-100: #e0f2fe;
  --neural-200: #bae6fd;
  --neural-300: #7dd3fc;
  --neural-400: #38bdf8;
  --neural-500: #0ea5e9;
  --neural-600: #0284c7;
  --neural-700: #0369a1;
  --neural-800: #075985;
  --neural-900: #0c4a6e;
  --neural-950: #082f49;
  
  /* Honey Colors */
  --honey-50: #fffbeb;
  --honey-100: #fef3c7;
  --honey-200: #fde68a;
  --honey-300: #fcd34d;
  --honey-400: #fbbf24;
  --honey-500: #f59e0b;
  --honey-600: #d97706;
  --honey-700: #b45309;
  --honey-800: #92400e;
  --honey-900: #78350f;
  --honey-950: #451a03;
  
  /* Swarm Green */
  --swarm-50: #f0fdf4;
  --swarm-100: #dcfce7;
  --swarm-200: #bbf7d0;
  --swarm-300: #86efac;
  --swarm-400: #4ade80;
  --swarm-500: #22c55e;
  --swarm-600: #16a34a;
  --swarm-700: #15803d;
  --swarm-800: #166534;
  --swarm-900: #14532d;
  --swarm-950: #052e16;
  
  /* Alert Red */
  --alert-50: #fef2f2;
  --alert-100: #fee2e2;
  --alert-200: #fecaca;
  --alert-300: #fca5a5;
  --alert-400: #f87171;
  --alert-500: #ef4444;
  --alert-600: #dc2626;
  --alert-700: #b91c1c;
  --alert-800: #991b1b;
  --alert-900: #7f1d1d;
  --alert-950: #450a0a;
}

/* Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #09090b;
    --bg-secondary: #18181b;
    --text-primary: #fafafa;
    --text-secondary: #e4e4e7;
  }
}

/* Base Layer Extensions */
@layer base {
  /* Enhanced Typography with Proper Contrast */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold text-gray-900 dark:text-gray-100;
  }
  
  p {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  /* Ensure all text meets WCAG AA standards */
  .text-muted {
    @apply text-gray-600 dark:text-gray-400;
  }
  
  /* Never use anything lighter than gray-600 for important text */
  .text-meta {
    @apply text-sm font-medium text-gray-600 dark:text-gray-400;
  }
}

/* Component Layer Extensions */
@layer components {
  /* Responsive container */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Mobile-first grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 gap-4;
    @apply sm:grid-cols-2 sm:gap-6;
    @apply lg:grid-cols-3 lg:gap-8;
  }

  /* Touch-friendly button base */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] px-4 py-2;
  }

  /* Neural Card Styles */
  .card-neural {
    @apply bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700;
    @apply hover:shadow-xl hover:border-neural-300 dark:hover:border-neural-600;
    @apply transform hover:scale-[1.02] transition-all duration-200;
  }
  
  /* Hexagonal Cards */
  .hex-card {
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
    @apply bg-white dark:bg-gray-900 shadow-lg;
  }
  
  /* Colony Buttons with High Contrast */
  .btn-colony-primary {
    @apply bg-gradient-to-r from-neural-600 to-neural-700;
    @apply hover:from-neural-700 hover:to-neural-800;
    @apply text-white font-semibold shadow-md hover:shadow-lg;
    @apply transform hover:scale-105 transition-all duration-200;
  }
  
  .btn-colony-secondary {
    @apply bg-white dark:bg-gray-800 border-2 border-neural-600;
    @apply text-neural-700 dark:text-neural-300;
    @apply hover:bg-neural-50 dark:hover:bg-gray-700;
    @apply font-semibold transition-all duration-200;
  }
  
  /* Neural Badges */
  .badge-neural {
    @apply bg-neural-100 text-neural-800 dark:bg-neural-900 dark:text-neural-200;
    @apply font-medium px-2.5 py-0.5 rounded-full;
  }
  
  .badge-honey {
    @apply bg-honey-100 text-honey-800 dark:bg-honey-900 dark:text-honey-200;
    @apply font-medium px-2.5 py-0.5 rounded-full;
  }
  
  .badge-swarm {
    @apply bg-swarm-100 text-swarm-800 dark:bg-swarm-900 dark:text-swarm-200;
    @apply font-medium px-2.5 py-0.5 rounded-full;
  }
  
  /* Status Indicators */
  .status-active {
    @apply w-3 h-3 bg-swarm-500 rounded-full animate-pulse;
  }
  
  .status-processing {
    @apply w-3 h-3 border-2 border-honey-500 border-t-transparent rounded-full animate-spin;
  }
  
  .status-idle {
    @apply w-3 h-3 bg-gray-400 dark:bg-gray-600 rounded-full;
  }
  
  .status-error {
    @apply w-3 h-3 bg-alert-500 rounded-full animate-pulse;
  }
}

/* Utility Layer Extensions */
@layer utilities {
  /* Neural Gradients */
  .gradient-neural {
    @apply bg-gradient-to-br from-neural-50 via-white to-neural-50;
    @apply dark:from-gray-900 dark:via-gray-800 dark:to-neural-950;
  }
  
  .gradient-honey {
    @apply bg-gradient-to-br from-honey-50 via-white to-neural-50;
    @apply dark:from-gray-900 dark:via-gray-800 dark:to-honey-950;
  }
  
  .gradient-dark {
    @apply bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900;
  }
  
  /* Neural Glow Effects */
  .glow-neural {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
  }
  
  .glow-honey {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  }
  
  .glow-swarm {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  }
  
  /* High Contrast Text Utilities */
  .text-high-contrast {
    @apply text-gray-900 dark:text-gray-100 font-semibold;
  }
  
  .text-readable {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  .text-readable-meta {
    @apply text-gray-600 dark:text-gray-400 font-medium;
  }
}

/* Custom Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
  }
  50% { 
    box-shadow: 0 0 30px rgba(14, 165, 233, 0.8);
  }
}

@keyframes neural-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-neural-pulse {
  animation: neural-pulse 2s ease-in-out infinite;
}

/* Fix Low Contrast Issues */
/* Never use text colors lighter than these minimums */
.min-contrast {
  color: rgb(75 85 99); /* gray-600 minimum for normal text */
}

.min-contrast-dark {
  color: rgb(156 163 175); /* gray-400 minimum for dark mode */
}

/* Force high contrast on important UI elements */
.force-contrast {
  @apply text-gray-800 dark:text-gray-200 !important;
}

/* Icon Visibility Fixes */
.icon-visible {
  @apply text-gray-700 dark:text-gray-300;
}

.icon-visible-primary {
  @apply text-neural-600 dark:text-neural-400;
}

.icon-visible-secondary {
  @apply text-honey-600 dark:text-honey-400;
}

.icon-visible-success {
  @apply text-swarm-600 dark:text-swarm-400;
}

/* Empty State Improvements */
.empty-state-icon {
  @apply text-neural-400 dark:text-neural-600;
}

/* Rating Stars Fix */
.star-filled {
  @apply text-honey-400 dark:text-honey-500;
}

.star-empty {
  @apply text-gray-400 dark:text-gray-600;
}

/* Accessibility Enhancements */
.focus-visible-ring {
  @apply focus:ring-2 focus:ring-neural-500 focus:ring-offset-2 focus:outline-none;
}

.hover-scale {
  @apply hover:scale-105 transition-transform duration-200;
}

.active-scale {
  @apply active:scale-95;
}

/* Neural Pattern Background */
.neural-pattern {
  background-image: 
    radial-gradient(circle at 20% 50%, var(--neural-200) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--honey-200) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, var(--swarm-200) 0%, transparent 50%);
  opacity: 0.05;
}

/* Dark Mode Neural Pattern */
@media (prefers-color-scheme: dark) {
  .neural-pattern {
    background-image: 
      radial-gradient(circle at 20% 50%, var(--neural-800) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, var(--honey-800) 0%, transparent 50%),
      radial-gradient(circle at 40% 20%, var(--swarm-800) 0%, transparent 50%);
    opacity: 0.1;
  }
}

/* Ensure all interactive elements have proper contrast */
a, button, [role="button"] {
  @apply min-w-[44px] min-h-[44px]; /* Touch target size */
}

/* Override any remaining low contrast text */
* {
  @apply contrast-more:font-semibold contrast-more:text-gray-900 dark:contrast-more:text-white;
}