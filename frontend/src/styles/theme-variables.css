/* NeuroColony Theme Variables - Single Source of Truth */
/* Import this file to ensure consistent theme application across all components */

:root {
  /* Neural Blue Palette */
  --color-neural-50: 240 249 255;
  --color-neural-100: 224 242 254;
  --color-neural-200: 186 230 253;
  --color-neural-300: 125 211 252;
  --color-neural-400: 56 189 248;
  --color-neural-500: 14 165 233;
  --color-neural-600: 2 132 199;
  --color-neural-700: 3 105 161;
  --color-neural-800: 7 89 133;
  --color-neural-900: 12 74 110;
  --color-neural-950: 8 47 73;

  /* Honey/Amber Palette */
  --color-honey-50: 255 251 235;
  --color-honey-100: 254 243 199;
  --color-honey-200: 253 230 138;
  --color-honey-300: 252 211 77;
  --color-honey-400: 251 191 36;
  --color-honey-500: 245 158 11;
  --color-honey-600: 217 119 6;
  --color-honey-700: 180 83 9;
  --color-honey-800: 146 64 14;
  --color-honey-900: 120 53 15;
  --color-honey-950: 69 26 3;

  /* Swarm Green Palette */
  --color-swarm-50: 240 253 244;
  --color-swarm-100: 220 252 231;
  --color-swarm-200: 187 247 208;
  --color-swarm-300: 134 239 172;
  --color-swarm-400: 74 222 128;
  --color-swarm-500: 34 197 94;
  --color-swarm-600: 22 163 74;
  --color-swarm-700: 21 128 61;
  --color-swarm-800: 22 101 52;
  --color-swarm-900: 20 83 45;
  --color-swarm-950: 5 46 22;

  /* Alert Red Palette */
  --color-alert-50: 254 242 242;
  --color-alert-100: 254 226 226;
  --color-alert-200: 254 202 202;
  --color-alert-300: 252 165 165;
  --color-alert-400: 248 113 113;
  --color-alert-500: 239 68 68;
  --color-alert-600: 220 38 38;
  --color-alert-700: 185 28 28;
  --color-alert-800: 153 27 27;
  --color-alert-900: 127 29 29;
  --color-alert-950: 69 10 10;

  /* Neutral Gray Palette */
  --color-gray-50: 250 250 250;
  --color-gray-100: 244 244 245;
  --color-gray-200: 228 228 231;
  --color-gray-300: 212 212 216;
  --color-gray-400: 161 161 170;
  --color-gray-500: 113 113 122;
  --color-gray-600: 82 82 91;
  --color-gray-700: 63 63 70;
  --color-gray-800: 39 39 42;
  --color-gray-900: 24 24 27;
  --color-gray-950: 9 9 11;

  /* Responsive Spacing */
  --spacing-touch-target: 44px; /* Minimum touch target size */
  --spacing-mobile-padding: 1rem;
  --spacing-tablet-padding: 1.5rem;
  --spacing-desktop-padding: 2rem;

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows with proper opacity */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark mode overrides */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode specific shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  }
}

/* Responsive breakpoints as CSS custom properties */
@media (min-width: 640px) {
  :root {
    --current-breakpoint: 'sm';
  }
}

@media (min-width: 768px) {
  :root {
    --current-breakpoint: 'md';
  }
}

@media (min-width: 1024px) {
  :root {
    --current-breakpoint: 'lg';
  }
}

@media (min-width: 1280px) {
  :root {
    --current-breakpoint: 'xl';
  }
}

@media (min-width: 1536px) {
  :root {
    --current-breakpoint: '2xl';
  }
}