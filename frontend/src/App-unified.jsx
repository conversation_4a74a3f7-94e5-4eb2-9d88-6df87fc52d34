/**
 * Unified App Component
 * Consolidates all route variations into feature-flag controlled routes
 */

import React, { Suspense, lazy } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { ThemeProvider } from './contexts/ThemeContext'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { featureFlags } from './config/features'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import ErrorBoundary from './components/ErrorBoundary'
import { Spinner } from './components/ui/Spinner'
import ProtectedRoute from './components/ProtectedRoute'
import UsageNotification from './components/UsageNotification'
import './styles/globals.css'
import './styles/neurocolony-theme.css'

// Eagerly loaded pages
import HomePage from './pages/HomePage'
import NotFound from './pages/NotFound'

// Lazy load all other pages
const LoginPage = lazy(() => import('./pages/LoginPage'))
const Register = lazy(() => import('./pages/Register'))
const PricingPage = lazy(() => import('./pages/PricingPage'))
const AboutUs = lazy(() => import('./pages/AboutUs'))
const ContactPage = lazy(() => import('./pages/ContactPage'))
const FAQ = lazy(() => import('./pages/FAQ'))
const TermsOfService = lazy(() => import('./pages/TermsOfService'))
const PrivacyPolicy = lazy(() => import('./pages/PrivacyPolicy'))

// Unified components
const UnifiedDashboard = lazy(() => import('./features/dashboard/UnifiedDashboard'))
const UnifiedEmailGenerator = lazy(() => import('./features/generator/UnifiedEmailGenerator'))

// Feature-specific pages
const SequencesPage = lazy(() => import('./pages/SequencesPage'))
const TemplatesPage = lazy(() => import('./pages/TemplatesPage'))
const BillingPage = lazy(() => import('./pages/BillingPage'))
const SettingsPage = lazy(() => import('./pages/SettingsPage'))
const IntegrationHub = lazy(() => import('./pages/IntegrationHub'))

// Agent features (if enabled)
const AgentMarketplace = lazy(() => import('./pages/AgentMarketplace'))
const AgentBuilder = lazy(() => import('./pages/AgentBuilder'))

// Advanced features (if enabled)
const WhiteLabelManager = lazy(() => import('./components/WhiteLabelManager'))
const RevenueOptimizer = lazy(() => import('./components/RevenueOptimizer'))

// Loading component
const PageLoader = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <Spinner size="large" />
  </div>
)

function AppContent() {
  const { user } = useAuth()

  return (
    <ThemeProvider>
      <ErrorBoundary>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          <Navbar />
          <main className="flex-grow">
            <Suspense fallback={<PageLoader />}>
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<HomePage />} />
                <Route path="/pricing" element={<PricingPage />} />
                <Route path="/about" element={<AboutUs />} />
                <Route path="/contact" element={<ContactPage />} />
                <Route path="/faq" element={<FAQ />} />
                <Route path="/terms" element={<TermsOfService />} />
                <Route path="/privacy" element={<PrivacyPolicy />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<Register />} />

                {/* Main dashboard route - unified */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <UnifiedDashboard />
                  </ProtectedRoute>
                } />

                {/* Email generator route - unified */}
                <Route path="/generator" element={
                  <ProtectedRoute>
                    <UnifiedEmailGenerator />
                  </ProtectedRoute>
                } />

                {/* Core features */}
                <Route path="/sequences" element={
                  <ProtectedRoute>
                    <SequencesPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/templates" element={
                  <ProtectedRoute>
                    <TemplatesPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/billing" element={
                  <ProtectedRoute>
                    <BillingPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <SettingsPage />
                  </ProtectedRoute>
                } />

                {/* Integration features */}
                {featureFlags.integrations && (
                  <Route path="/integrations" element={
                    <ProtectedRoute>
                      <IntegrationHub />
                    </ProtectedRoute>
                  } />
                )}

                {/* Agent features */}
                {featureFlags.agents && (
                  <>
                    <Route path="/agents" element={
                      <ProtectedRoute>
                        <AgentMarketplace />
                      </ProtectedRoute>
                    } />
                    <Route path="/agents/builder" element={
                      <ProtectedRoute>
                        <AgentBuilder />
                      </ProtectedRoute>
                    } />
                  </>
                )}

                {/* Enterprise features */}
                {featureFlags.whiteLabel && user?.plan === 'enterprise' && (
                  <Route path="/white-label" element={
                    <ProtectedRoute>
                      <WhiteLabelManager />
                    </ProtectedRoute>
                  } />
                )}

                {featureFlags.revenueOptimizer && ['business', 'enterprise'].includes(user?.plan) && (
                  <Route path="/revenue-optimizer" element={
                    <ProtectedRoute>
                      <RevenueOptimizer />
                    </ProtectedRoute>
                  } />
                )}

                {/* Beta features */}
                {featureFlags.betaFeatures && (
                  <Route path="/beta/*" element={
                    <ProtectedRoute>
                      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                        <div className="text-center">
                          <h1 className="text-3xl font-bold text-gray-900 mb-4">Beta Features</h1>
                          <p className="text-gray-600">Coming soon to early access users</p>
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                )}

                {/* Legacy route redirects */}
                <Route path="/dashboard-*" element={<Navigate to="/dashboard" replace />} />
                <Route path="/generator-*" element={<Navigate to="/generator" replace />} />
                <Route path="/colony-*" element={<Navigate to="/dashboard" replace />} />
                <Route path="/command-center" element={<Navigate to="/dashboard" replace />} />
                <Route path="/agent-dashboard" element={<Navigate to="/agents" replace />} />
                <Route path="/agent-marketplace" element={<Navigate to="/agents" replace />} />

                {/* 404 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
          </main>
          <Footer />
          
          {/* Usage Notifications - only show for authenticated users */}
          {user && <UsageNotification />}
          
          {/* Toast Notifications */}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#f3f4f6',
                border: '1px solid #374151'
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#f3f4f6',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f3f4f6',
                },
              },
            }}
          />
        </div>
      </ErrorBoundary>
    </ThemeProvider>
  )
}

function App() {
  return (
    <Router>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </Router>
  )
}

export default App