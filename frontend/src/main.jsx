import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import App from './App.jsx'
import './index.css'

console.log(' main.jsx loading...')

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

console.log('📦 Query client created')

const root = document.getElementById('root')
console.log(' Root element:', root)

if (!root) {
  console.error('❌ No root element found!')
} else {
  console.log(' Root element found, creating React root...')
  
  try {
    ReactDOM.createRoot(root).render(
      <React.StrictMode>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter>
            <App />
            <Toaster 
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </BrowserRouter>
        </QueryClientProvider>
      </React.StrictMode>,
    )
    console.log('🎉 React app rendered successfully!')
  } catch (error) {
    console.error('💥 Error rendering React app:', error)
  }
}