import { useEffect, useRef, useState } from 'react';
import { useInView } from 'framer-motion';
import { shouldReduceMotion } from '../utils/animations';

// Hook for scroll-triggered animations with performance optimization
export const useScrollAnimation = ({
  threshold = 0.1,
  triggerOnce = true,
  rootMargin = '0px',
  animateIn = true,
} = {}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: triggerOnce,
    margin: rootMargin,
    amount: threshold,
  });
  
  const reduceMotion = shouldReduceMotion();
  const shouldAnimate = animateIn && isInView && !reduceMotion;

  return {
    ref,
    isInView,
    shouldAnimate,
    animationProps: shouldAnimate ? {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.6, ease: 'easeOut' }
    } : {}
  };
};

// Hook for parallax scrolling effects
export const useParallax = (speed = 0.5) => {
  const [offsetY, setOffsetY] = useState(0);
  const ref = useRef(null);
  const reduceMotion = shouldReduceMotion();

  useEffect(() => {
    if (reduceMotion) return;

    const handleScroll = () => {
      if (!ref.current) return;
      
      const rect = ref.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const elementTop = rect.top;
      const elementHeight = rect.height;
      
      // Calculate if element is in viewport
      if (elementTop < windowHeight && elementTop + elementHeight > 0) {
        const scrolled = window.scrollY;
        const rate = scrolled * -speed;
        setOffsetY(rate);
      }
    };

    // Throttle scroll events for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll);
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [speed, reduceMotion]);

  return {
    ref,
    style: reduceMotion ? {} : {
      transform: `translateY(${offsetY}px)`,
      willChange: 'transform',
    }
  };
};

// Hook for stagger animations on children
export const useStaggerAnimation = ({
  staggerDelay = 0.1,
  initialDelay = 0,
  animateIn = true,
} = {}) => {
  const reduceMotion = shouldReduceMotion();
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: initialDelay,
        staggerChildren: reduceMotion ? 0 : staggerDelay,
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: reduceMotion ? 0 : 20,
      scale: reduceMotion ? 1 : 0.95,
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  return {
    containerVariants: animateIn ? containerVariants : {},
    itemVariants: animateIn ? itemVariants : {},
  };
};

// Hook for number counter animations
export const useCountAnimation = (endValue, duration = 2000) => {
  const [count, setCount] = useState(0);
  const reduceMotion = shouldReduceMotion();

  useEffect(() => {
    if (reduceMotion) {
      setCount(endValue);
      return;
    }

    let startTimestamp = null;
    const startValue = 0;

    const step = (timestamp) => {
      if (!startTimestamp) startTimestamp = timestamp;
      const progress = Math.min((timestamp - startTimestamp) / duration, 1);
      
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * (endValue - startValue) + startValue);
      
      setCount(currentCount);
      
      if (progress < 1) {
        window.requestAnimationFrame(step);
      }
    };

    window.requestAnimationFrame(step);
  }, [endValue, duration, reduceMotion]);

  return count;
};

// Hook for mouse position tracking for interactive effects
export const useMousePosition = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const updateMousePosition = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    // Throttle mouse move events
    let ticking = false;
    const throttledMouseMove = (e) => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          updateMousePosition(e);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('mousemove', throttledMouseMove);
    return () => window.removeEventListener('mousemove', throttledMouseMove);
  }, []);

  return mousePosition;
};

// Hook for element hover with magnetic effect
export const useMagneticHover = (strength = 0.3) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const ref = useRef(null);
  const reduceMotion = shouldReduceMotion();

  useEffect(() => {
    if (reduceMotion) return;

    const handleMouseMove = (e) => {
      if (!ref.current) return;

      const rect = ref.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const distanceX = e.clientX - centerX;
      const distanceY = e.clientY - centerY;
      
      const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);
      const maxDistance = 100;
      
      if (distance < maxDistance) {
        const factor = (maxDistance - distance) / maxDistance;
        setPosition({
          x: distanceX * strength * factor,
          y: distanceY * strength * factor,
        });
      } else {
        setPosition({ x: 0, y: 0 });
      }
    };

    const handleMouseLeave = () => {
      setPosition({ x: 0, y: 0 });
    };

    if (ref.current) {
      ref.current.addEventListener('mousemove', handleMouseMove);
      ref.current.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (ref.current) {
        ref.current.removeEventListener('mousemove', handleMouseMove);
        ref.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [strength, reduceMotion]);

  return {
    ref,
    style: reduceMotion ? {} : {
      transform: `translate(${position.x}px, ${position.y}px)`,
      transition: 'transform 0.2s ease-out',
      willChange: 'transform',
    }
  };
};