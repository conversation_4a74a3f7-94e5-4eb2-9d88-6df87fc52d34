<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1F2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo icon -->
  <rect x="8" y="8" width="44" height="44" rx="8" fill="url(#bgGradient)"/>
  
  <!-- AI Brain/Sequence symbol -->
  <g transform="translate(30, 30)">
    <!-- Central node -->
    <circle cx="0" cy="0" r="3" fill="#FBBF24"/>
    
    <!-- Connection lines -->
    <line x1="-8" y1="-8" x2="0" y2="0" stroke="#FBBF24" stroke-width="2"/>
    <line x1="8" y1="-8" x2="0" y2="0" stroke="#FBBF24" stroke-width="2"/>
    <line x1="-8" y1="8" x2="0" y2="0" stroke="#FBBF24" stroke-width="2"/>
    <line x1="8" y1="8" x2="0" y2="0" stroke="#FBBF24" stroke-width="2"/>
    
    <!-- Outer nodes -->
    <circle cx="-8" cy="-8" r="2" fill="#FEF3C7"/>
    <circle cx="8" cy="-8" r="2" fill="#FEF3C7"/>
    <circle cx="-8" cy="8" r="2" fill="#FEF3C7"/>
    <circle cx="8" cy="8" r="2" fill="#FEF3C7"/>
    
    <!-- Spark/Lightning effect -->
    <path d="M 2 -6 L 6 -2 L 4 -2 L 8 2 L 4 -2 L 6 -2 Z" fill="#FCD34D"/>
  </g>
  
  <!-- Text -->
  <text x="65" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#textGradient)">
    Sequence
  </text>
  <text x="65" y="42" font-family="Arial, sans-serif" font-size="16" font-weight="normal" fill="#3B82F6">
    AI
  </text>
</svg>