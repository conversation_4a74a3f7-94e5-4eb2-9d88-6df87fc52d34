# Development Dockerfile for Frontend
FROM node:18-alpine

# Install development tools
RUN apk add --no-cache \
    curl \
    git

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies including dev dependencies
RUN npm install

# Copy source code (will be mounted as volume in docker-compose)
COPY . .

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:3002/ || exit 1

# Default development command (can be overridden in docker-compose)
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3002"]