<svg width="240" height="60" viewBox="0 0 240 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional gradient inspired by Microsoft/Google -->
    <linearGradient id="corporateGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0078D4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#106EBE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1B4B8C;stop-opacity:1" />
    </linearGradient>
    
    <!-- Subtle accent gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCF2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0078D4;stop-opacity:1" />
    </linearGradient>
    
    <!-- Professional shadow -->
    <filter id="corporateShadow">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="0" dy="1" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge> 
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main logo container with corporate styling -->
  <g filter="url(#corporateShadow)">
    <!-- Clean geometric symbol inspired by major tech logos -->
    <rect x="12" y="12" width="36" height="36" rx="6" 
          fill="url(#corporateGradient)" 
          stroke="none"/>
    
    <!-- Modern geometric pattern - inspired by Microsoft's squares/Windows logo concept -->
    <g transform="translate(30, 30)">
      <!-- Central design element - clean and minimal -->
      <rect x="-8" y="-8" width="6" height="6" rx="1" fill="#FFFFFF" opacity="0.9"/>
      <rect x="2" y="-8" width="6" height="6" rx="1" fill="#FFFFFF" opacity="0.7"/>
      <rect x="-8" y="2" width="6" height="6" rx="1" fill="#FFFFFF" opacity="0.7"/>
      <rect x="2" y="2" width="6" height="6" rx="1" fill="url(#accentGradient)"/>
      
      <!-- Subtle connecting lines for AI/sequence concept -->
      <line x1="-2" y1="-5" x2="2" y2="-5" stroke="#FFFFFF" stroke-width="1" opacity="0.6"/>
      <line x1="-5" y1="-2" x2="-5" y2="2" stroke="#FFFFFF" stroke-width="1" opacity="0.6"/>
    </g>
  </g>
  
  <!-- Professional typography inspired by corporate brands -->
  <g font-family="system-ui, -apple-system, 'Segoe UI', Roboto, Arial, sans-serif">
    <!-- Main company name with professional weight -->
    <text x="60" y="28" font-size="22" font-weight="600" fill="#323130" letter-spacing="-0.5px">
      SequenceAI
    </text>
    
    <!-- Professional tagline -->
    <text x="60" y="45" font-size="11" font-weight="400" fill="#605E5C" letter-spacing="0.5px">
      ENTERPRISE AI PLATFORM
    </text>
  </g>
  
  <!-- Optional: Small indicator of enterprise status -->
  <circle cx="220" cy="20" r="3" fill="url(#accentGradient)" opacity="0.8"/>
</svg>