import{Q as D,r as i,j as e,m as s,B as O,S as V,g as W,O as Y,i as J,R as K,h as Q,d as X}from"./index-CVpabiD8.js";import{T as Z}from"./premium-theme-B_OEEgUJ.js";import{W as _}from"./wand-2-DA9i-8H2.js";import{L as ee}from"./languages-B8P6yi0c.js";import{T as I}from"./trophy-BLwcuC8U.js";import{M as P}from"./mic-DfrRXnef.js";import{M as ae}from"./mic-off-BtFHUvjE.js";import{L as se}from"./loader-2-D2DNDVF4.js";import{S as te}from"./star-C2599PJP.js";const he=({user:re})=>{const{isDark:le}=D(),[l,M]=i.useState("generate"),[b,j]=i.useState(!1),[k,y]=i.useState(null),[f,h]=i.useState(null),[ie,oe]=i.useState({});i.useRef(null);const[x,v]=i.useState({subject:"",body:""}),[t,m]=i.useState({industry:"",productService:"",targetAudience:"",pricePoint:"",tone:"professional",goal:"conversion"}),[u,N]=i.useState([]),[p,w]=i.useState({subject:"",body:""}),[g,C]=i.useState(!1),[S,E]=i.useState(null),[R,T]=i.useState(null),z=[{id:"generate",icon:_,title:"AI Generation",description:"Create complete email sequences with advanced AI",color:"from-purple-500 to-blue-500",badge:"Popular",badgeColor:"bg-green-500"},{id:"translate",icon:ee,title:"Multi-Language",description:"Translate and adapt for global markets",color:"from-blue-500 to-cyan-500",badge:"New",badgeColor:"bg-blue-500"},{id:"predict",icon:O,title:"Performance Prediction",description:"AI-powered success forecasting",color:"from-cyan-500 to-green-500",badge:"AI",badgeColor:"bg-purple-500"},{id:"optimize",icon:V,title:"Subject Optimizer",description:"Psychological trigger optimization",color:"from-green-500 to-yellow-500",badge:"Pro",badgeColor:"bg-yellow-500"},{id:"analyze",icon:I,title:"Competitor Analysis",description:"Strategic intelligence gathering",color:"from-yellow-500 to-red-500",badge:"Elite",badgeColor:"bg-red-500"}],B=[{code:"es",name:"Spanish",flag:"🇪🇸",users:"500M+"},{code:"fr",name:"French",flag:"🇫🇷",users:"280M+"},{code:"de",name:"German",flag:"🇩🇪",users:"100M+"},{code:"ja",name:"Japanese",flag:"🇯🇵",users:"125M+"},{code:"pt",name:"Portuguese",flag:"🇵🇹",users:"260M+"},{code:"zh",name:"Chinese",flag:"🇨🇳",users:"1.1B+"},{code:"it",name:"Italian",flag:"🇮🇹",users:"65M+"},{code:"ko",name:"Korean",flag:"🇰🇷",users:"77M+"}],F=[{id:"professional",label:"Professional",emoji:"💼"},{id:"friendly",label:"Friendly",emoji:"😊"},{id:"casual",label:"Casual",emoji:""},{id:"urgent",label:"Urgent",emoji:""},{id:"luxury",label:"Luxury",emoji:""},{id:"playful",label:"Playful",emoji:"🎉"}],L=[{id:"conversion",label:"Drive Sales",emoji:""},{id:"engagement",label:"Increase Engagement",emoji:""},{id:"nurture",label:"Nurture Leads",emoji:"🌱"},{id:"retention",label:"Retain Customers",emoji:""},{id:"awareness",label:"Build Awareness",emoji:"📢"},{id:"education",label:"Educate Users",emoji:"📚"}],G=async()=>{try{const a=await navigator.mediaDevices.getUserMedia({audio:!0}),r=new MediaRecorder(a),o=[];r.ondataavailable=d=>o.push(d.data),r.onstop=()=>{const d=new Blob(o,{type:"audio/webm"});T(d),a.getTracks().forEach(n=>n.stop())},E(r),r.start(),C(!0)}catch(a){console.error("Recording error:",a),h("Microphone access denied")}},H=()=>{S&&g&&(S.stop(),C(!1))},$=async()=>{j(!0),h(null);try{const a=await api.post("/sequences/generate",{businessInfo:t,settings:{sequenceLength:7,tone:t.tone,primaryGoal:t.goal,includeCTA:!0,includePersonalization:!0}});y({type:"sequence",data:a.data.sequence})}catch(a){h(a.response?.data?.error||"Generation failed")}finally{j(!1)}},q=({mode:a,isActive:r,onClick:o})=>e.jsxs(s.div,{onClick:o,className:`
        relative overflow-hidden cursor-pointer group
        ${r?"card-premium":"card hover:scale-[1.02]"}
        transition-all duration-300
      `,whileHover:{y:-4},whileTap:{scale:.98},children:[e.jsx("div",{className:`
        absolute inset-0 bg-gradient-to-br ${a.color} opacity-0 
        ${r?"opacity-10":"group-hover:opacity-5"} 
        transition-opacity duration-300
      `}),a.badge&&e.jsx("div",{className:`
          absolute top-4 right-4 px-2 py-1 rounded-full text-xs font-bold text-white
          ${a.badgeColor}
        `,children:a.badge}),e.jsxs("div",{className:"relative z-10",children:[e.jsx("div",{className:`
          w-12 h-12 rounded-xl bg-gradient-to-br ${a.color} 
          flex items-center justify-center mb-4 group-hover:scale-110 transition-transform
        `,children:e.jsx(a.icon,{size:24,className:"text-white"})}),e.jsx("h3",{className:"text-lg font-bold text-primary mb-2",children:a.title}),e.jsx("p",{className:"text-sm text-secondary",children:a.description}),r&&e.jsx(s.div,{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500",layoutId:"activeMode"})]})]}),c=({label:a,value:r,onChange:o,placeholder:d,multiline:n=!1,rows:U=3})=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-primary",children:a}),n?e.jsx("textarea",{value:r,onChange:o,placeholder:d,rows:U,className:"input resize-none"}):e.jsx("input",{type:"text",value:r,onChange:o,placeholder:d,className:"input"})]}),A=({label:a,value:r,onChange:o,options:d})=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-primary",children:a}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:d.map(n=>e.jsx(s.button,{onClick:()=>o(n.id),className:`
              p-3 rounded-xl border transition-all text-left
              ${r===n.id?"border-purple-500 bg-purple-500/10 text-primary":"border-border hover:border-purple-300 text-secondary hover:text-primary"}
            `,whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg",children:n.emoji}),e.jsx("span",{className:"text-sm font-medium",children:n.label})]})},n.id))})]});return e.jsxs("div",{className:"min-h-screen bg-primary text-primary transition-colors duration-300",children:[e.jsxs("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}})]}),e.jsx("div",{className:"fixed top-6 right-6 z-50",children:e.jsx(Z,{})}),e.jsxs("div",{className:"relative z-10 container py-8",children:[e.jsxs(s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[e.jsx("h1",{className:"text-6xl font-bold text-holographic mb-4",children:"Ultra Email Generator"}),e.jsx("p",{className:"text-xl text-secondary max-w-2xl mx-auto",children:"Harness the power of advanced AI to create world-class email sequences that convert"})]}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-12",children:[e.jsx("h2",{className:"text-2xl font-bold text-primary mb-6 text-center",children:"Choose Your AI Mode"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6",children:z.map(a=>e.jsx(q,{mode:a,isActive:l===a.id,onClick:()=>M(a.id)},a.id))})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(s.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-purple-500 to-blue-500",children:e.jsx(W,{size:24,className:"text-white"})}),e.jsxs("h2",{className:"text-2xl font-bold text-primary",children:[z.find(a=>a.id===l)?.title," Settings"]})]}),e.jsxs("div",{className:"space-y-6",children:[(l==="generate"||l==="optimize"||l==="analyze")&&e.jsxs(e.Fragment,{children:[e.jsx(c,{label:"Industry",value:t.industry,onChange:a=>m({...t,industry:a.target.value}),placeholder:"e.g., SaaS, E-commerce, Healthcare"}),e.jsx(c,{label:"Product/Service",value:t.productService,onChange:a=>m({...t,productService:a.target.value}),placeholder:"e.g., Email Marketing Platform"}),e.jsx(c,{label:"Target Audience",value:t.targetAudience,onChange:a=>m({...t,targetAudience:a.target.value}),placeholder:"e.g., Small Business Owners"}),e.jsx(c,{label:"Price Point",value:t.pricePoint,onChange:a=>m({...t,pricePoint:a.target.value}),placeholder:"e.g., $29/month"})]}),l==="generate"&&e.jsxs(e.Fragment,{children:[e.jsx(A,{label:"Tone",value:t.tone,onChange:a=>m({...t,tone:a}),options:F}),e.jsx(A,{label:"Primary Goal",value:t.goal,onChange:a=>m({...t,goal:a}),options:L}),e.jsxs("div",{className:"p-6 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-2xl border border-purple-500/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(P,{className:"text-purple-500",size:20}),e.jsx("h3",{className:"font-semibold text-primary",children:"Voice Input (Beta)"})]}),e.jsx("p",{className:"text-sm text-secondary mb-4",children:"Speak your ideas and let AI transform them into perfect email sequences"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(s.button,{onClick:g?H:G,className:`
                          btn flex-1 ${g?"bg-red-500 hover:bg-red-600":"btn-primary"}
                        `,whileHover:{scale:1.02},whileTap:{scale:.98},children:g?e.jsxs(e.Fragment,{children:[e.jsx(ae,{size:20}),"Stop Recording"]}):e.jsxs(e.Fragment,{children:[e.jsx(P,{size:20}),"Start Recording"]})}),R&&e.jsx(s.button,{onClick:()=>console.log("Process voice"),className:"btn btn-secondary",whileHover:{scale:1.02},children:"Process"})]})]})]}),(l==="translate"||l==="predict"||l==="optimize")&&e.jsxs(e.Fragment,{children:[e.jsx(c,{label:"Email Subject",value:x.subject,onChange:a=>v({...x,subject:a.target.value}),placeholder:"Enter your email subject line"}),(l==="translate"||l==="predict")&&e.jsx(c,{label:"Email Body",value:x.body,onChange:a=>v({...x,body:a.target.value}),placeholder:"Enter your email content",multiline:!0,rows:8})]}),l==="translate"&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-medium text-primary",children:"Target Languages"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:B.map(a=>e.jsxs(s.label,{className:`
                          flex items-center gap-3 p-4 rounded-xl cursor-pointer transition-all
                          ${u.includes(a.code)?"bg-purple-500/20 border border-purple-500 text-primary":"bg-surface-hover border border-border-subtle hover:border-purple-300"}
                        `,whileHover:{scale:1.02},whileTap:{scale:.98},children:[e.jsx("input",{type:"checkbox",checked:u.includes(a.code),onChange:r=>{r.target.checked?N([...u,a.code]):N(u.filter(o=>o!==a.code))},className:"sr-only"}),e.jsx("span",{className:"text-2xl",children:a.flag}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:a.name}),e.jsxs("div",{className:"text-xs text-secondary",children:[a.users," speakers"]})]}),u.includes(a.code)&&e.jsx(Y,{size:20,className:"text-purple-500"})]},a.code))})]}),l==="analyze"&&e.jsxs(e.Fragment,{children:[e.jsx(c,{label:"Competitor Email Subject",value:p.subject,onChange:a=>w({...p,subject:a.target.value}),placeholder:"Enter competitor's subject line"}),e.jsx(c,{label:"Competitor Email Body",value:p.body,onChange:a=>w({...p,body:a.target.value}),placeholder:"Paste competitor's email content",multiline:!0,rows:8})]}),f&&e.jsxs(s.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"p-4 bg-red-500/20 border border-red-500/30 rounded-xl flex items-center gap-3",children:[e.jsx(J,{size:20,className:"text-red-400"}),e.jsx("span",{className:"text-red-400",children:f})]}),e.jsx(s.button,{onClick:$,disabled:b,className:"btn btn-primary w-full text-lg py-4",whileHover:{scale:1.02},whileTap:{scale:.98},children:b?e.jsxs(e.Fragment,{children:[e.jsx(se,{className:"animate-spin",size:20}),"Processing with AI..."]}):e.jsxs(e.Fragment,{children:[e.jsx(K,{size:20}),"Generate with Ultra AI"]})})]})]}),e.jsxs(s.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-green-500 to-blue-500",children:e.jsx(te,{size:24,className:"text-white"})}),e.jsx("h2",{className:"text-2xl font-bold text-primary",children:"AI Results"})]}),e.jsx("div",{className:"min-h-[400px] flex items-center justify-center",children:e.jsx(Q,{mode:"wait",children:b?e.jsxs(s.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-center",children:[e.jsx("div",{className:"loader-dots w-16 h-16 mx-auto mb-6"}),e.jsx("h3",{className:"text-xl font-semibold text-primary mb-2",children:"AI is crafting magic..."}),e.jsx("p",{className:"text-secondary",children:"Analyzing your inputs with quantum intelligence"})]}):k?e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"w-full",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl border border-green-500/20",children:[e.jsx(I,{size:48,className:"mx-auto mb-4 text-green-500"}),e.jsx("h3",{className:"text-xl font-bold text-primary mb-2",children:"Success!"}),e.jsx("p",{className:"text-secondary",children:"Your AI-powered content is ready"})]}),e.jsx(s.button,{onClick:()=>y(null),className:"btn btn-secondary w-full",whileHover:{scale:1.02},children:"Generate Another"})]})}):e.jsxs(s.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:[e.jsx(X,{size:64,className:"mx-auto mb-6 text-purple-500 opacity-50"}),e.jsx("h3",{className:"text-xl font-semibold text-primary mb-2",children:"Ready for Ultra AI"}),e.jsx("p",{className:"text-secondary",children:"Configure your settings and generate world-class content"})]})})})]})]})]})]})};export{he as default};
