import{m as i,j as r,e as m}from"./index-CVpabiD8.js";const b=({children:o,variant:a="primary",size:t="default",animated:e=!0,className:s="",onClick:l,disabled:n=!1,icon:u,...p})=>{const d="relative overflow-hidden font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-neutral-900",c={primary:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white shadow-lg hover:shadow-purple-500/25",secondary:"bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-400 hover:to-orange-400 text-white shadow-lg hover:shadow-amber-500/25",outline:"border-2 border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white",ghost:"text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"},x={small:"px-4 py-2 text-sm rounded-lg",default:"px-6 py-3 text-base rounded-xl",large:"px-8 py-4 text-lg rounded-xl"},h=e?i.button:"button";return r.jsxs(h,{className:`${d} ${c[a]} ${x[t]} ${s} ${n?"opacity-50 cursor-not-allowed":""}`,onClick:l,disabled:n,whileHover:e&&!n?{scale:1.02,y:-1}:void 0,whileTap:e&&!n?{scale:.98}:void 0,transition:{duration:.2},...p,children:[r.jsxs("span",{className:"relative z-10 flex items-center gap-2",children:[u&&r.jsx(u,{className:"w-4 h-4"}),o]}),!n&&r.jsx(i.div,{className:"absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:1.5,repeat:1/0,repeatDelay:3}})]})},f=({children:o,className:a="",hover:t=!0,glow:e=!1,animated:s=!0})=>{const l=s?i.div:"div";return r.jsx(l,{className:`
        bg-gradient-to-br from-neutral-800/50 to-neutral-900/50 
        backdrop-blur-sm border border-neutral-700/50 rounded-2xl p-6
        ${t?"hover:border-purple-500/50 hover:shadow-lg hover:shadow-purple-500/10":""}
        ${e?"shadow-lg shadow-purple-500/20":""}
        transition-all duration-300 ${a}
      `,whileHover:s&&t?{y:-2,scale:1.01}:void 0,transition:{duration:.3},children:o})},v=({children:o,variant:a="default",size:t="default",animated:e=!0})=>{const s={default:"bg-purple-500/20 text-purple-300 border-purple-500/30",success:"bg-green-500/20 text-green-300 border-green-500/30",warning:"bg-amber-500/20 text-amber-300 border-amber-500/30",premium:"bg-gradient-to-r from-purple-500/20 to-amber-500/20 text-amber-300 border-purple-500/30"},l={small:"px-2 py-1 text-xs",default:"px-3 py-1.5 text-sm",large:"px-4 py-2 text-base"},n=e?i.span:"span";return r.jsx(n,{className:`
        inline-flex items-center rounded-full font-medium border
        ${s[a]} ${l[t]}
      `,whileHover:e?{scale:1.05}:void 0,transition:{duration:.2},children:o})},w=({value:o,label:a,trend:t,animated:e=!0})=>{const s=e?i.div:"div";return r.jsxs(s,{className:"text-center group",initial:e?{opacity:0,y:20}:void 0,whileInView:e?{opacity:1,y:0}:void 0,transition:e?{duration:.6}:void 0,viewport:{once:!0},children:[r.jsx(i.div,{className:"text-4xl md:text-5xl font-bold bg-gradient-to-br from-purple-400 to-amber-400 bg-clip-text text-transparent mb-2",whileHover:e?{scale:1.1}:void 0,transition:{duration:.3},children:o}),r.jsx("div",{className:"text-neutral-400 font-medium",children:a}),t&&r.jsxs("div",{className:"text-sm text-green-400 mt-1 flex items-center justify-center gap-1",children:[r.jsx(m,{className:"w-3 h-3 rotate-[-45deg]"}),t]})]})},y=({size:o="default",className:a=""})=>{const t={small:"w-4 h-4",default:"w-6 h-6",large:"w-8 h-8"};return r.jsx(i.div,{className:`${t[o]} ${a}`,animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:r.jsx("div",{className:"w-full h-full border-2 border-purple-200 border-t-purple-600 rounded-full"})})};export{f as E,y as L,w as S,b as a,v as b};
