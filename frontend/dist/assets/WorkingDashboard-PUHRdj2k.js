import{k as v,r as n,j as e,m as s,M as N,C as w,T as x,L as c,e as S,S as k,Z as C,a as q}from"./index-CVpabiD8.js";import{F as m}from"./file-text-Dp_9XYM2.js";import{C as A}from"./clock-DLEz2ATr.js";import{P as p}from"./plus-CLzd-zUl.js";import{L as D}from"./loader-2-D2DNDVF4.js";const R=()=>{const{user:h,getApiClient:u}=v(),[l,g]=n.useState({totalSequences:0,totalEmails:0,activeSequences:0,draftSequences:0,recentSequences:[]}),[i,d]=n.useState(!0);n.useEffect(()=>{b()},[]);const b=async()=>{d(!0);try{const a=await u().get("/sequences");if(a.data.success){const r=a.data.sequences;g({totalSequences:r.length,totalEmails:r.reduce((o,f)=>o+(f.emails?.length||0),0),activeSequences:r.filter(o=>o.status==="active").length,draftSequences:r.filter(o=>o.status==="draft").length,recentSequences:r.slice(0,3)})}}catch(t){console.error("Failed to load dashboard data:",t)}finally{d(!1)}},j=t=>new Date(t).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),y=[{title:"Create Sequence",description:"Generate a new email sequence",icon:p,link:"/generator-simple",color:"from-amber-400 to-orange-500",textColor:"text-black"},{title:"View All Sequences",description:"Manage your sequences",icon:m,link:"/sequences",color:"from-blue-500 to-purple-600",textColor:"text-white"},{title:"Settings",description:"Account & preferences",icon:x,link:"/settings",color:"from-gray-600 to-gray-700",textColor:"text-white"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-950 via-purple-950/20 to-slate-950",children:[e.jsxs("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"}),e.jsx("div",{className:"absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"3s"}})]}),e.jsxs("div",{className:"relative max-w-7xl mx-auto px-6 py-12",children:[e.jsxs(s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-12 text-center",children:[e.jsxs("h1",{className:"text-5xl md:text-6xl font-light text-white mb-4 leading-tight",children:["Welcome back,",e.jsx("span",{className:"block bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent font-medium",children:h?.name||"User"})]}),e.jsx("p",{className:"text-xl text-slate-300 font-light max-w-2xl mx-auto",children:"Ready to create intelligent automation workflows"})]}),e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[{label:"Total Sequences",value:i?"...":l.totalSequences,icon:m,color:"text-blue-400",bgColor:"bg-blue-500/20"},{label:"Total Emails",value:i?"...":l.totalEmails,icon:N,color:"text-green-400",bgColor:"bg-green-500/20"},{label:"Active",value:i?"...":l.activeSequences,icon:w,color:"text-amber-400",bgColor:"bg-amber-500/20"},{label:"Drafts",value:i?"...":l.draftSequences,icon:A,color:"text-gray-400",bgColor:"bg-gray-500/20"}].map((t,a)=>e.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1+a*.05},className:"bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-6 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-500",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-400 text-sm",children:t.label}),e.jsx("p",{className:"text-2xl font-bold text-white",children:t.value})]}),e.jsx("div",{className:`p-3 rounded-lg ${t.bgColor}`,children:e.jsx(t.icon,{className:`h-6 w-6 ${t.color}`})})]})},t.label))}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-12",children:[e.jsx("h2",{className:"text-3xl font-light text-white mb-8 text-center",children:"Quick Actions"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:y.map((t,a)=>e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2+a*.1},children:e.jsx(c,{to:t.link,children:e.jsx("div",{className:`bg-gradient-to-r ${t.color} p-8 rounded-2xl hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:`font-medium text-xl ${t.textColor} mb-3`,children:t.title}),e.jsx("p",{className:`text-sm ${t.textColor} opacity-80`,children:t.description})]}),e.jsx(t.icon,{className:`h-8 w-8 ${t.textColor}`})]})})})},t.title))})]}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mb-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-light text-white text-center flex-1",children:"Recent Activity"}),l.totalSequences>0&&e.jsxs(c,{to:"/sequences",className:"text-amber-400 hover:text-amber-300 flex items-center transition-colors",children:["View All",e.jsx(S,{className:"h-4 w-4 ml-1"})]})]}),i?e.jsxs("div",{className:"bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-12 text-center backdrop-blur-sm",children:[e.jsx(D,{className:"h-12 w-12 text-purple-400 mx-auto mb-6 animate-spin"}),e.jsx("p",{className:"text-slate-300 text-lg",children:"Loading recent activity..."})]}):l.recentSequences.length===0?e.jsxs("div",{className:"bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-12 text-center backdrop-blur-sm",children:[e.jsx(k,{className:"h-20 w-20 text-purple-400 mx-auto mb-6"}),e.jsx("h3",{className:"text-2xl font-light text-white mb-4",children:"Ready to Begin"}),e.jsx("p",{className:"text-slate-400 mb-8 text-lg max-w-md mx-auto",children:"Create your first intelligent automation workflow"}),e.jsxs(c,{to:"/generator-simple",className:"inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-medium text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25 hover:scale-105",children:[e.jsx(p,{className:"h-5 w-5"}),"Create Your First Sequence"]})]}):e.jsx("div",{className:"space-y-4",children:l.recentSequences.map((t,a)=>e.jsx(s.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3+a*.1},className:"bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-2xl p-6 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-500",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-1",children:t.title}),e.jsx("p",{className:"text-gray-400 text-sm mb-2 line-clamp-1",children:t.description}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[e.jsxs("span",{children:[t.emails?.length||0," emails"]}),e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:t.tone}),e.jsx("span",{children:"•"}),e.jsx("span",{children:j(t.createdAt)})]})]}),e.jsx("div",{className:"ml-4 flex items-center space-x-2",children:e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${t.status==="active"?"bg-green-500/20 text-green-300":"bg-gray-500/20 text-gray-300"}`,children:t.status})})]})},t._id))})]}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"relative bg-gradient-to-br from-slate-800/30 to-slate-900/30 border border-slate-700/30 backdrop-blur-sm rounded-3xl p-12",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-3xl"}),e.jsxs("div",{className:"relative",children:[e.jsx("h2",{className:"text-3xl font-light text-white mb-10 text-center",children:"Platform Features"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[{icon:C,title:"Intelligent Automation",description:"Advanced AI-powered workflows that adapt automatically",color:"text-purple-400"},{icon:x,title:"Business Intelligence",description:"Deep insights and predictive analytics",color:"text-blue-400"},{icon:q,title:"Performance Optimization",description:"Monitor and enhance your automation workflows",color:"text-green-400"}].map((t,a)=>e.jsxs("div",{className:"text-center",children:[e.jsx(t.icon,{className:`h-12 w-12 ${t.color} mx-auto mb-6`}),e.jsx("h3",{className:"text-xl font-medium text-white mb-3",children:t.title}),e.jsx("p",{className:"text-slate-400 leading-relaxed",children:t.description})]},t.title))})]})]})]})]})};export{R as default};
