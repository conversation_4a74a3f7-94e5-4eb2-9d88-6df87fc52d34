import{c as C,r as g,j as e,M as v,B as j,T as M,m as n,Z as O,a as $,V as x,A as N,C as f,D as w,b as R,U as D,L as S}from"./index-CVpabiD8.js";import{U as T}from"./users-f__6LX8a.js";import{C as P}from"./clock-DLEz2ATr.js";import{A as B}from"./arrow-up-C7tOVQTy.js";import{C as q}from"./calendar-DZH_gkxY.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=C("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=C("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),I=({user:l})=>{const[s,h]=g.useState({totalSequences:0,totalEmails:0,avgConversionRate:0,totalRecipients:0,thisMonth:{sequences:0,emails:0,growth:0},recentActivity:[],topPerformingSequences:[],loading:!0});g.useEffect(()=>{c()},[l]);const c=async()=>{try{const a=localStorage.getItem("token"),i=await fetch("http://localhost:5002/api/sequences/analytics/dashboard",{headers:{Authorization:`Bearer ${a}`}});if(i.ok){const r=await i.json();if(r.success){const m={...r.data,recentActivity:r.data.recentActivity.map(o=>({...o,timestamp:new Date(o.timestamp)})),loading:!1};h(m)}else throw new Error(r.message||"Failed to load analytics")}else throw new Error("Failed to fetch analytics data")}catch(a){console.error("Failed to load stats:",a),h(i=>({...i,loading:!1,totalSequences:0,totalEmails:0,avgConversionRate:0,totalRecipients:0,thisMonth:{sequences:0,emails:0,growth:0},recentActivity:[],topPerformingSequences:[]}))}},y=a=>{const r=new Date-a,m=Math.floor(r/(1e3*60*60)),o=Math.floor(r/(1e3*60*60*24));return o>0?`${o} day${o>1?"s":""} ago`:m>0?`${m} hour${m>1?"s":""} ago`:"Just now"},u=({title:a,value:i,change:r,icon:m,color:o="primary"})=>e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:a}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:i}),r!==void 0&&e.jsxs("div",{className:"flex items-center mt-2",children:[r>=0?e.jsx(B,{className:"h-4 w-4 text-green-500"}):e.jsx(G,{className:"h-4 w-4 text-red-500"}),e.jsxs("span",{className:`text-sm font-medium ml-1 ${r>=0?"text-green-500":"text-red-500"}`,children:[Math.abs(r),"% this month"]})]})]}),e.jsx("div",{className:`p-3 rounded-lg bg-${o}-100`,children:e.jsx(m,{className:`h-6 w-6 text-${o}-600`})})]})});return s.loading?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[...Array(4)].map((a,i)=>e.jsx("div",{className:"bg-white rounded-lg shadow-lg p-6 animate-pulse",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]}),e.jsx("div",{className:"h-12 w-12 bg-gray-200 rounded-lg"})]})},i))}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(u,{title:"Total Sequences",value:s.totalSequences,change:s.thisMonth.growth,icon:v,color:"primary"}),e.jsx(u,{title:"Total Emails Generated",value:s.totalEmails,change:15.2,icon:j,color:"green"}),e.jsx(u,{title:"Average Conversion Rate",value:`${s.avgConversionRate}%`,change:8.7,icon:M,color:"blue"}),e.jsx(u,{title:"Estimated Reach",value:s.totalRecipients.toLocaleString(),change:31.4,icon:T,color:"purple"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(n.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"}),e.jsx(P,{className:"h-5 w-5 text-gray-400"})]}),e.jsx("div",{className:"space-y-4",children:s.recentActivity.map((a,i)=>e.jsxs(n.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:i*.1},className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-primary-100 rounded-lg",children:a.type==="sequence_created"?e.jsx(v,{className:"h-4 w-4 text-primary-600"}):e.jsx(O,{className:"h-4 w-4 text-primary-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900 text-sm",children:a.title}),e.jsx("p",{className:"text-xs text-gray-500",children:y(a.timestamp)})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:"text-sm font-medium text-green-600",children:[a.conversionRate,"%"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"conversion"})]})]},a.id))}),e.jsx("button",{className:"w-full mt-4 text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All Activity"})]}),e.jsxs(n.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Performing Sequences"}),e.jsx($,{className:"h-5 w-5 text-gray-400"})]}),e.jsx("div",{className:"space-y-4",children:s.topPerformingSequences.map((a,i)=>e.jsxs(n.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:i*.1},className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900 text-sm",children:a.title}),e.jsxs("span",{className:"text-lg font-bold text-green-600",children:[a.conversionRate,"%"]})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[e.jsx("span",{children:a.industry}),e.jsxs("span",{children:[a.emailsSent.toLocaleString()," emails sent"]})]}),e.jsx("div",{className:"mt-2 bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${Math.min(a.conversionRate,100)}%`}})})]},a.id))}),e.jsx("button",{className:"w-full mt-4 text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All Sequences"})]})]}),e.jsxs(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-r from-primary-500 to-blue-600 rounded-lg shadow-lg p-6 text-white",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Usage This Month"}),e.jsxs("p",{className:"text-primary-100",children:["You've generated ",s.thisMonth.sequences," sequences with ",s.thisMonth.emails," emails.",s.thisMonth.growth>0&&e.jsxs("span",{className:"block mt-1",children:["That's ",s.thisMonth.growth,"% more than last month! 🎉"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-2xl font-bold",children:l?.usage?.sequencesGenerated||0}),e.jsxs("p",{className:"text-primary-200 text-sm",children:["of ",l?.usage?.sequencesLimit===-1?"∞":l?.usage?.sequencesLimit||3," sequences"]})]})]}),l?.subscription?.type==="free"&&e.jsx("div",{className:"mt-4 pt-4 border-t border-primary-400",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-primary-100",children:"Want unlimited sequences? Upgrade to Pro for more features."}),e.jsx("button",{className:"bg-white text-primary-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors",children:"Upgrade Now"})]})})]})]})},F=({user:l})=>{const[s,h]=g.useState(null),[c,y]=g.useState(null),[u,a]=g.useState(!0),[i,r]=g.useState(!1);g.useEffect(()=>{m()},[]);const m=async()=>{try{const[t,d]=await Promise.all([fetch("/api/usage/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}}),fetch("/api/usage/history",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}})]),[p,b]=await Promise.all([t.json(),d.json()]);p.success&&h(p.data),b.success&&y(b.data)}catch(t){console.error("Failed to load usage data:",t),x.error("Failed to load usage statistics")}finally{a(!1)}},o=async()=>{try{const d=await(await fetch("/api/usage/overage-consent",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}})).json();d.success?(x.success("Overage billing enabled! You can now continue generating sequences."),h(d.data.stats),r(!1)):x.error(d.message||"Failed to enable overage billing")}catch(t){console.error("Overage consent error:",t),x.error("Failed to enable overage billing")}},k=async()=>{try{const d=await(await fetch("/api/usage/overage-consent",{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}})).json();d.success?(x.success("Overage billing disabled"),h(d.data.stats)):x.error(d.message||"Failed to disable overage billing")}catch(t){console.error("Overage revocation error:",t),x.error("Failed to disable overage billing")}};if(u)return e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-6"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-20 bg-gray-200 rounded"}),e.jsx("div",{className:"h-20 bg-gray-200 rounded"}),e.jsx("div",{className:"h-20 bg-gray-200 rounded"})]})]})});if(!s)return e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx(N,{className:"h-12 w-12 mx-auto mb-4"}),e.jsx("p",{children:"Unable to load usage statistics"})]})});const A=t=>{switch(t){case"critical":return"text-red-600 bg-red-50 border-red-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";default:return"text-green-600 bg-green-50 border-green-200"}},U=t=>{switch(t){case"critical":return e.jsx(N,{className:"h-5 w-5"});case"warning":return e.jsx(R,{className:"h-5 w-5"});default:return e.jsx(f,{className:"h-5 w-5"})}},E=t=>new Date(t).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(n.div,{className:"bg-white rounded-lg shadow-sm border",initial:{opacity:0,y:20},animate:{opacity:1,y:0},children:[e.jsx("div",{className:"p-6 border-b",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Usage Overview"}),e.jsxs("div",{className:`flex items-center space-x-2 px-3 py-1 rounded-full border ${A(s.status)}`,children:[U(s.status),e.jsxs("span",{className:"text-sm font-medium",children:[s.usagePercentage,"% Used"]})]})]})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[e.jsx("span",{children:"Sequences Generated"}),e.jsxs("span",{children:[s.sequencesGenerated," / ",s.sequencesLimit]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:e.jsx(n.div,{className:`h-3 rounded-full ${s.status==="critical"?"bg-red-500":s.status==="warning"?"bg-yellow-500":"bg-green-500"}`,initial:{width:0},animate:{width:`${Math.min(s.usagePercentage,100)}%`},transition:{duration:1,ease:"easeOut"}})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx(j,{className:"h-8 w-8 text-primary-600 mx-auto mb-2"}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:s.sequencesGenerated}),e.jsx("div",{className:"text-sm text-gray-600",children:"Sequences Generated"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx(q,{className:"h-8 w-8 text-primary-600 mx-auto mb-2"}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:Math.max(0,s.sequencesLimit-s.sequencesGenerated)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Remaining"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx(q,{className:"h-8 w-8 text-primary-600 mx-auto mb-2"}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:E(s.periodEnd)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Period Ends"})]})]}),s.canGoOverage&&e.jsx("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Overage Billing"}),e.jsxs("p",{className:"text-sm text-blue-700 mb-3",children:["Continue generating sequences beyond your limit at $",s.overageRate," each.",s.overageSequences>0&&e.jsxs("span",{className:"font-medium",children:[" ","Current overage: ",s.overageSequences," sequences ($",s.overageCharges,")"]})]}),e.jsx("div",{className:"flex items-center space-x-3",children:l?.usage?.notifications?.overageConsentGiven?e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"text-sm text-green-700 font-medium",children:"Enabled"}),e.jsx("button",{onClick:k,className:"text-sm text-blue-600 hover:text-blue-800 underline",children:"Disable"})]}):e.jsx("button",{onClick:()=>r(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Enable Overage Billing"})})]}),e.jsx(w,{className:"h-6 w-6 text-blue-600"})]})})]})]}),c?.projections&&e.jsxs(n.div,{className:"bg-white rounded-lg shadow-sm border",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[e.jsx("div",{className:"p-6 border-b",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Usage Projections"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx($,{className:"h-8 w-8 text-primary-600 mx-auto mb-2"}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.projections.estimatedMonthlyUsage}),e.jsx("div",{className:"text-sm text-gray-600",children:"Projected Monthly Usage"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx(j,{className:"h-8 w-8 text-orange-600 mx-auto mb-2"}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.projections.estimatedOverageSequences}),e.jsx("div",{className:"text-sm text-gray-600",children:"Projected Overage"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx(w,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",c.projections.estimatedOverageCharges]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Projected Overage Cost"})]})]}),c.projections.estimatedOverageCharges>0&&e.jsx("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"h-4 w-4 text-yellow-600"}),e.jsxs("span",{className:"text-sm text-yellow-800",children:["Based on current usage, you may incur $",c.projections.estimatedOverageCharges," in overage charges this month."]})]})})]})]}),c?.history&&c.history.length>0&&e.jsxs(n.div,{className:"bg-white rounded-lg shadow-sm border",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[e.jsx("div",{className:"p-6 border-b",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Usage History"})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-3",children:c.history.map((t,d)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:t.period}),e.jsxs("div",{className:"text-sm text-gray-600",children:[t.sequencesGenerated," sequences generated",t.overageSequences>0&&e.jsxs("span",{className:"text-orange-600",children:[" ","+ ",t.overageSequences," overage"]})]})]}),e.jsx("div",{className:"text-right",children:t.overageCharges>0?e.jsxs("div",{className:"text-orange-600 font-medium",children:["$",t.overageCharges]}):e.jsx("div",{className:"text-green-600 font-medium",children:"No overage"})})]},d))})})]}),i&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(n.div,{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Enable Overage Billing"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"You've reached your monthly limit. Enable overage billing to continue generating sequences."}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Overage Pricing"}),e.jsx("p",{className:"text-blue-700 text-sm",children:e.jsxs("strong",{children:["$",s.overageRate," per additional sequence"]})}),e.jsx("p",{className:"text-blue-700 text-sm mt-1",children:"You'll only pay for what you use beyond your plan limit. Overage charges are billed at the end of your monthly cycle."})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{onClick:()=>r(!1),className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:o,className:"flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"Enable Overage"})]})]})})]})},Z=({user:l})=>e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs(n.h1,{className:"text-3xl font-bold text-gray-900",initial:{opacity:0,y:20},animate:{opacity:1,y:0},children:["Welcome back, ",l?.name,"!"]}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Ready to create some high-converting email sequences?"})]}),e.jsx(n.div,{className:"mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx("div",{className:"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Generate Your Next Sequence"}),e.jsx("p",{className:"text-primary-100 mb-4",children:"Create a high-converting email sequence in under 60 seconds"}),e.jsx("div",{className:"flex items-center",children:e.jsx(D,{user:l,compact:!0})})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(S,{to:"/templates",className:"bg-white/10 text-white hover:bg-white/20 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center",children:"Browse Templates"}),e.jsxs(S,{to:"/generator",className:"bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center",children:[e.jsx(O,{className:"h-5 w-5 mr-2"}),"Generate Now"]})]})]})})}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:e.jsx(F,{user:l})}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(I,{user:l})})]})});export{Z as default};
