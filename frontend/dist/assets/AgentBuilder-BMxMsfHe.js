import{r as p,j as o,ac as Be,a6 as Ge,a7 as G,P as We,ad as ze,ae as xe,d as Xe,T as Ye,B as Ve,J as Je,M as Qe,p as Ze,W as Ke,w as et}from"./index-CVpabiD8.js";import{S as tt}from"./Spinner-D3g4Pp_K.js";import{S as rt}from"./save-D4vIjgOV.js";import{T as nt}from"./trash-2-CS_FJ-_W.js";const Oe=p.createContext({dragDropManager:void 0});function I(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var ce=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),ue=function(){return Math.random().toString(36).substring(7).split("").join(".")},le={INIT:"@@redux/INIT"+ue(),REPLACE:"@@redux/REPLACE"+ue()};function st(t){if(typeof t!="object"||t===null)return!1;for(var e=t;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function Te(t,e,r){var n;if(typeof e=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(I(0));if(typeof e=="function"&&typeof r>"u"&&(r=e,e=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(I(1));return r(Te)(t,e)}if(typeof t!="function")throw new Error(I(2));var s=t,i=e,a=[],c=a,l=!1;function f(){c===a&&(c=a.slice())}function d(){if(l)throw new Error(I(3));return i}function m(v){if(typeof v!="function")throw new Error(I(4));if(l)throw new Error(I(5));var x=!0;return f(),c.push(v),function(){if(x){if(l)throw new Error(I(6));x=!1,f();var w=c.indexOf(v);c.splice(w,1),a=null}}}function y(v){if(!st(v))throw new Error(I(7));if(typeof v.type>"u")throw new Error(I(8));if(l)throw new Error(I(9));try{l=!0,i=s(i,v)}finally{l=!1}for(var x=a=c,O=0;O<x.length;O++){var w=x[O];w()}return v}function g(v){if(typeof v!="function")throw new Error(I(10));s=v,y({type:le.REPLACE})}function S(){var v,x=m;return v={subscribe:function(w){if(typeof w!="object"||w===null)throw new Error(I(11));function E(){w.next&&w.next(d())}E();var R=x(E);return{unsubscribe:R}}},v[ce]=function(){return this},v}return y({type:le.INIT}),n={dispatch:y,subscribe:m,getState:d,replaceReducer:g},n[ce]=S,n}function h(t,e,...r){if(it()&&e===void 0)throw new Error("invariant requires an error message argument");if(!t){let n;if(e===void 0)n=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let s=0;n=new Error(e.replace(/%s/g,function(){return r[s++]})),n.name="Invariant Violation"}throw n.framesToPop=1,n}}function it(){return typeof process<"u"&&!0}function ot(t,e,r){return e.split(".").reduce((n,s)=>n&&n[s]?n[s]:r||null,t)}function at(t,e){return t.filter(r=>r!==e)}function we(t){return typeof t=="object"}function ct(t,e){const r=new Map,n=i=>{r.set(i,r.has(i)?r.get(i)+1:1)};t.forEach(n),e.forEach(n);const s=[];return r.forEach((i,a)=>{i===1&&s.push(a)}),s}function ut(t,e){return t.filter(r=>e.indexOf(r)>-1)}const re="dnd-core/INIT_COORDS",H="dnd-core/BEGIN_DRAG",ne="dnd-core/PUBLISH_DRAG_SOURCE",F="dnd-core/HOVER",U="dnd-core/DROP",$="dnd-core/END_DRAG";function de(t,e){return{type:re,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}const lt={type:re,payload:{clientOffset:null,sourceClientOffset:null}};function dt(t){return function(r=[],n={publishSource:!0}){const{publishSource:s=!0,clientOffset:i,getSourceClientOffset:a}=n,c=t.getMonitor(),l=t.getRegistry();t.dispatch(de(i)),gt(r,c,l);const f=pt(r,c);if(f==null){t.dispatch(lt);return}let d=null;if(i){if(!a)throw new Error("getSourceClientOffset must be defined");ft(a),d=a(f)}t.dispatch(de(i,d));const y=l.getSource(f).beginDrag(c,f);if(y==null)return;ht(y),l.pinSource(f);const g=l.getSourceType(f);return{type:H,payload:{itemType:g,item:y,sourceId:f,clientOffset:i||null,sourceClientOffset:d||null,isSourcePublic:!!s}}}}function gt(t,e,r){h(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(n){h(r.getSource(n),"Expected sourceIds to be registered.")})}function ft(t){h(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function ht(t){h(we(t),"Item must be an object.")}function pt(t,e){let r=null;for(let n=t.length-1;n>=0;n--)if(e.canDragSource(t[n])){r=t[n];break}return r}function mt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable}))),n.forEach(function(s){mt(t,s,r[s])})}return t}function yt(t){return function(r={}){const n=t.getMonitor(),s=t.getRegistry();Dt(n),xt(n).forEach((a,c)=>{const l=bt(a,c,s,n),f={type:U,payload:{dropResult:vt({},r,l)}};t.dispatch(f)})}}function Dt(t){h(t.isDragging(),"Cannot call drop while not dragging."),h(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function bt(t,e,r,n){const s=r.getTarget(t);let i=s?s.drop(n,t):void 0;return St(i),typeof i>"u"&&(i=e===0?{}:n.getDropResult()),i}function St(t){h(typeof t>"u"||we(t),"Drop result must either be an object or undefined.")}function xt(t){const e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function Ot(t){return function(){const r=t.getMonitor(),n=t.getRegistry();Tt(r);const s=r.getSourceId();return s!=null&&(n.getSource(s,!0).endDrag(r,s),n.unpinSource()),{type:$}}}function Tt(t){h(t.isDragging(),"Cannot call endDrag while not dragging.")}function Q(t,e){return e===null?t===null:Array.isArray(t)?t.some(r=>r===e):t===e}function wt(t){return function(r,{clientOffset:n}={}){It(r);const s=r.slice(0),i=t.getMonitor(),a=t.getRegistry(),c=i.getItemType();return Et(s,a,c),Ct(s,i,a),Nt(s,i,a),{type:F,payload:{targetIds:s,clientOffset:n||null}}}}function It(t){h(Array.isArray(t),"Expected targetIds to be an array.")}function Ct(t,e,r){h(e.isDragging(),"Cannot call hover while not dragging."),h(!e.didDrop(),"Cannot call hover after drop.");for(let n=0;n<t.length;n++){const s=t[n];h(t.lastIndexOf(s)===n,"Expected targetIds to be unique in the passed array.");const i=r.getTarget(s);h(i,"Expected targetIds to be registered.")}}function Et(t,e,r){for(let n=t.length-1;n>=0;n--){const s=t[n],i=e.getTargetType(s);Q(i,r)||t.splice(n,1)}}function Nt(t,e,r){t.forEach(function(n){r.getTarget(n).hover(e,n)})}function Pt(t){return function(){if(t.getMonitor().isDragging())return{type:ne}}}function jt(t){return{beginDrag:dt(t),publishDragSource:Pt(t),hover:wt(t),drop:yt(t),endDrag:Ot(t)}}class Rt{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:r}=this.store;function n(i){return(...a)=>{const c=i.apply(e,a);typeof c<"u"&&r(c)}}const s=jt(this);return Object.keys(s).reduce((i,a)=>{const c=s[a];return i[a]=n(c),i},{})}dispatch(e){this.store.dispatch(e)}constructor(e,r){this.isSetUp=!1,this.handleRefCountChange=()=>{const n=this.store.getState().refCount>0;this.backend&&(n&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!n&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=r,e.subscribe(this.handleRefCountChange)}}function Mt(t,e){return{x:t.x+e.x,y:t.y+e.y}}function Ie(t,e){return{x:t.x-e.x,y:t.y-e.y}}function kt(t){const{clientOffset:e,initialClientOffset:r,initialSourceClientOffset:n}=t;return!e||!r||!n?null:Ie(Mt(e,n),r)}function _t(t){const{clientOffset:e,initialClientOffset:r}=t;return!e||!r?null:Ie(e,r)}const M=[],se=[];M.__IS_NONE__=!0;se.__IS_ALL__=!0;function Lt(t,e){return t===M?!1:t===se||typeof e>"u"?!0:ut(e,t).length>0}class At{subscribeToStateChange(e,r={}){const{handlerIds:n}=r;h(typeof e=="function","listener must be a function."),h(typeof n>"u"||Array.isArray(n),"handlerIds, when specified, must be an array of strings.");let s=this.store.getState().stateId;const i=()=>{const a=this.store.getState(),c=a.stateId;try{c===s||c===s+1&&!Lt(a.dirtyHandlerIds,n)||e()}finally{s=c}};return this.store.subscribe(i)}subscribeToOffsetChange(e){h(typeof e=="function","listener must be a function.");let r=this.store.getState().dragOffset;const n=()=>{const s=this.store.getState().dragOffset;s!==r&&(r=s,e())};return this.store.subscribe(n)}canDragSource(e){if(!e)return!1;const r=this.registry.getSource(e);return h(r,`Expected to find a valid source. sourceId=${e}`),this.isDragging()?!1:r.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const r=this.registry.getTarget(e);if(h(r,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;const n=this.registry.getTargetType(e),s=this.getItemType();return Q(n,s)&&r.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;const r=this.registry.getSource(e,!0);if(h(r,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;const n=this.registry.getSourceType(e),s=this.getItemType();return n!==s?!1:r.isDragging(this,e)}isOverTarget(e,r={shallow:!1}){if(!e)return!1;const{shallow:n}=r;if(!this.isDragging())return!1;const s=this.registry.getTargetType(e),i=this.getItemType();if(i&&!Q(s,i))return!1;const a=this.getTargetIds();if(!a.length)return!1;const c=a.indexOf(e);return n?c===a.length-1:c>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return kt(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return _t(this.store.getState().dragOffset)}constructor(e,r){this.store=e,this.registry=r}}const ge=typeof global<"u"?global:self,Ce=ge.MutationObserver||ge.WebKitMutationObserver;function Ee(t){return function(){const r=setTimeout(s,0),n=setInterval(s,50);function s(){clearTimeout(r),clearInterval(n),t()}}}function Ht(t){let e=1;const r=new Ce(t),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){e=-e,n.data=e}}const Ft=typeof Ce=="function"?Ht:Ee;class Ut{enqueueTask(e){const{queue:r,requestFlush:n}=this;r.length||(n(),this.flushing=!0),r[r.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const r=this.index;if(this.index++,e[r].call(),this.index>this.capacity){for(let n=0,s=e.length-this.index;n<s;n++)e[n]=e[n+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=Ft(this.flush),this.requestErrorThrow=Ee(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class $t{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,r){this.onError=e,this.release=r,this.task=null}}class qt{create(e){const r=this.freeTasks,n=r.length?r.pop():new $t(this.onError,s=>r[r.length]=s);return n.task=e,n}constructor(e){this.onError=e,this.freeTasks=[]}}const Ne=new Ut,Bt=new qt(Ne.registerPendingError);function Gt(t){Ne.enqueueTask(Bt.create(t))}const ie="dnd-core/ADD_SOURCE",oe="dnd-core/ADD_TARGET",ae="dnd-core/REMOVE_SOURCE",q="dnd-core/REMOVE_TARGET";function Wt(t){return{type:ie,payload:{sourceId:t}}}function zt(t){return{type:oe,payload:{targetId:t}}}function Xt(t){return{type:ae,payload:{sourceId:t}}}function Yt(t){return{type:q,payload:{targetId:t}}}function Vt(t){h(typeof t.canDrag=="function","Expected canDrag to be a function."),h(typeof t.beginDrag=="function","Expected beginDrag to be a function."),h(typeof t.endDrag=="function","Expected endDrag to be a function.")}function Jt(t){h(typeof t.canDrop=="function","Expected canDrop to be a function."),h(typeof t.hover=="function","Expected hover to be a function."),h(typeof t.drop=="function","Expected beginDrag to be a function.")}function Z(t,e){if(e&&Array.isArray(t)){t.forEach(r=>Z(r,!1));return}h(typeof t=="string"||typeof t=="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var C;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(C||(C={}));let Qt=0;function Zt(){return Qt++}function Kt(t){const e=Zt().toString();switch(t){case C.SOURCE:return`S${e}`;case C.TARGET:return`T${e}`;default:throw new Error(`Unknown Handler Role: ${t}`)}}function fe(t){switch(t[0]){case"S":return C.SOURCE;case"T":return C.TARGET;default:throw new Error(`Cannot parse handler ID: ${t}`)}}function he(t,e){const r=t.entries();let n=!1;do{const{done:s,value:[,i]}=r.next();if(i===e)return!0;n=!!s}while(!n);return!1}class er{addSource(e,r){Z(e),Vt(r);const n=this.addHandler(C.SOURCE,e,r);return this.store.dispatch(Wt(n)),n}addTarget(e,r){Z(e,!0),Jt(r);const n=this.addHandler(C.TARGET,e,r);return this.store.dispatch(zt(n)),n}containsHandler(e){return he(this.dragSources,e)||he(this.dropTargets,e)}getSource(e,r=!1){return h(this.isSourceId(e),"Expected a valid source ID."),r&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return h(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return h(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return h(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return fe(e)===C.SOURCE}isTargetId(e){return fe(e)===C.TARGET}removeSource(e){h(this.getSource(e),"Expected an existing source."),this.store.dispatch(Xt(e)),Gt(()=>{this.dragSources.delete(e),this.types.delete(e)})}removeTarget(e){h(this.getTarget(e),"Expected an existing target."),this.store.dispatch(Yt(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const r=this.getSource(e);h(r,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=r}unpinSource(){h(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,r,n){const s=Kt(e);return this.types.set(s,r),e===C.SOURCE?this.dragSources.set(s,n):e===C.TARGET&&this.dropTargets.set(s,n),s}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const tr=(t,e)=>t===e;function rr(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function nr(t,e,r=tr){if(t.length!==e.length)return!1;for(let n=0;n<t.length;++n)if(!r(t[n],e[n]))return!1;return!0}function sr(t=M,e){switch(e.type){case F:break;case ie:case oe:case q:case ae:return M;case H:case ne:case $:case U:default:return se}const{targetIds:r=[],prevTargetIds:n=[]}=e.payload,s=ct(r,n);if(!(s.length>0||!nr(r,n)))return M;const a=n[n.length-1],c=r[r.length-1];return a!==c&&(a&&s.push(a),c&&s.push(c)),s}function ir(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function or(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable}))),n.forEach(function(s){ir(t,s,r[s])})}return t}const pe={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function ar(t=pe,e){const{payload:r}=e;switch(e.type){case re:case H:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case F:return rr(t.clientOffset,r.clientOffset)?t:or({},t,{clientOffset:r.clientOffset});case $:case U:return pe;default:return t}}function cr(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function P(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable}))),n.forEach(function(s){cr(t,s,r[s])})}return t}const ur={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function lr(t=ur,e){const{payload:r}=e;switch(e.type){case H:return P({},t,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case ne:return P({},t,{isSourcePublic:!0});case F:return P({},t,{targetIds:r.targetIds});case q:return t.targetIds.indexOf(r.targetId)===-1?t:P({},t,{targetIds:at(t.targetIds,r.targetId)});case U:return P({},t,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case $:return P({},t,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function dr(t=0,e){switch(e.type){case ie:case oe:return t+1;case ae:case q:return t-1;default:return t}}function gr(t=0){return t+1}function fr(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable}))),n.forEach(function(s){fr(t,s,r[s])})}return t}function pr(t={},e){return{dirtyHandlerIds:sr(t.dirtyHandlerIds,{type:e.type,payload:hr({},e.payload,{prevTargetIds:ot(t,"dragOperation.targetIds",[])})}),dragOffset:ar(t.dragOffset,e),refCount:dr(t.refCount,e),dragOperation:lr(t.dragOperation,e),stateId:gr(t.stateId)}}function mr(t,e=void 0,r={},n=!1){const s=vr(n),i=new At(s,new er(s)),a=new Rt(s,i),c=t(a,e,r);return a.receiveBackend(c),a}function vr(t){const e=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return Te(pr,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}function yr(t,e){if(t==null)return{};var r=Dr(t,e),n,s;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(s=0;s<i.length;s++)n=i[s],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Dr(t,e){if(t==null)return{};var r={},n=Object.keys(t),s,i;for(i=0;i<n.length;i++)s=n[i],!(e.indexOf(s)>=0)&&(r[s]=t[s]);return r}let me=0;const A=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var br=p.memo(function(e){var{children:r}=e,n=yr(e,["children"]);const[s,i]=Sr(n);return p.useEffect(()=>{if(i){const a=Pe();return++me,()=>{--me===0&&(a[A]=null)}}},[]),o.jsx(Oe.Provider,{value:s,children:r})});function Sr(t){if("manager"in t)return[{dragDropManager:t.manager},!1];const e=xr(t.backend,t.context,t.options,t.debugMode),r=!t.context;return[e,r]}function xr(t,e=Pe(),r,n){const s=e;return s[A]||(s[A]={dragDropManager:mr(t,e,r,n)}),s[A]}function Pe(){return typeof global<"u"?global:window}var W,ve;function Or(){return ve||(ve=1,W=function t(e,r){if(e===r)return!0;if(e&&r&&typeof e=="object"&&typeof r=="object"){if(e.constructor!==r.constructor)return!1;var n,s,i;if(Array.isArray(e)){if(n=e.length,n!=r.length)return!1;for(s=n;s--!==0;)if(!t(e[s],r[s]))return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if(i=Object.keys(e),n=i.length,n!==Object.keys(r).length)return!1;for(s=n;s--!==0;)if(!Object.prototype.hasOwnProperty.call(r,i[s]))return!1;for(s=n;s--!==0;){var a=i[s];if(!t(e[a],r[a]))return!1}return!0}return e!==e&&r!==r}),W}var Tr=Or();const wr=Be(Tr),N=typeof window<"u"?p.useLayoutEffect:p.useEffect;function Ir(t,e,r){const[n,s]=p.useState(()=>e(t)),i=p.useCallback(()=>{const a=e(t);wr(n,a)||(s(a),r&&r())},[n,t,r]);return N(i),[n,i]}function Cr(t,e,r){const[n,s]=Ir(t,e,r);return N(function(){const a=t.getHandlerId();if(a!=null)return t.subscribeToStateChange(s,{handlerIds:[a]})},[t,s]),n}function je(t,e,r){return Cr(e,t||(()=>({})),()=>r.reconnect())}function Re(t,e){const r=[];return typeof t!="function"&&r.push(t),p.useMemo(()=>typeof t=="function"?t():t,r)}function Er(t){return p.useMemo(()=>t.hooks.dragSource(),[t])}function Nr(t){return p.useMemo(()=>t.hooks.dragPreview(),[t])}let z=!1,X=!1;class Pr{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){h(!z,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return z=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{z=!1}}isDragging(){if(!this.sourceId)return!1;h(!X,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return X=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{X=!1}}subscribeToStateChange(e,r){return this.internalMonitor.subscribeToStateChange(e,r)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,r){return this.internalMonitor.isOverTarget(e,r)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}let Y=!1;class jr{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,r){return this.internalMonitor.subscribeToStateChange(e,r)}canDrop(){if(!this.targetId)return!1;h(!Y,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Y=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Y=!1}}isOver(e){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,e):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}function Rr(t,e,r){const n=r.getRegistry(),s=n.addTarget(t,e);return[s,()=>n.removeTarget(s)]}function Mr(t,e,r){const n=r.getRegistry(),s=n.addSource(t,e);return[s,()=>n.removeSource(s)]}function K(t,e,r,n){let s;if(s!==void 0)return!!s;if(t===e)return!0;if(typeof t!="object"||!t||typeof e!="object"||!e)return!1;const i=Object.keys(t),a=Object.keys(e);if(i.length!==a.length)return!1;const c=Object.prototype.hasOwnProperty.bind(e);for(let l=0;l<i.length;l++){const f=i[l];if(!c(f))return!1;const d=t[f],m=e[f];if(s=void 0,s===!1||s===void 0&&d!==m)return!1}return!0}function ee(t){return t!==null&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function kr(t){if(typeof t.type=="string")return;const e=t.type.displayName||t.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${e} into a <div>, or turn it into a drag source or a drop target itself.`)}function _r(t){return(e=null,r=null)=>{if(!p.isValidElement(e)){const i=e;return t(i,r),i}const n=e;return kr(n),Lr(n,r?i=>t(i,r):t)}}function Me(t){const e={};return Object.keys(t).forEach(r=>{const n=t[r];if(r.endsWith("Ref"))e[r]=t[r];else{const s=_r(n);e[r]=()=>s}}),e}function ye(t,e){typeof t=="function"?t(e):t.current=e}function Lr(t,e){const r=t.ref;return h(typeof r!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?p.cloneElement(t,{ref:n=>{ye(r,n),ye(e,n)}}):p.cloneElement(t,{ref:e})}class Ar{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){const e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){const e=this.dragSource,r=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return r&&this.disconnectDragSource(),this.handlerId?e?(r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)),r):(this.lastConnectedDragSource=e,r):r}reconnectDragPreview(e=!1){const r=this.dragPreview,n=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(n&&this.disconnectDragPreview(),!!this.handlerId){if(!r){this.lastConnectedDragPreview=r;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=r,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,r,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!K(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!K(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=Me({dragSource:(r,n)=>{this.clearDragSource(),this.dragSourceOptions=n||null,ee(r)?this.dragSourceRef=r:this.dragSourceNode=r,this.reconnectDragSource()},dragPreview:(r,n)=>{this.clearDragPreview(),this.dragPreviewOptions=n||null,ee(r)?this.dragPreviewRef=r:this.dragPreviewNode=r,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}class Hr{get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const r=this.dropTarget;if(this.handlerId){if(!r){this.lastConnectedDropTarget=r;return}e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=r,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,r,this.dropTargetOptions))}}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!K(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=Me({dropTarget:(r,n)=>{this.clearDropTarget(),this.dropTargetOptions=n,ee(r)?this.dropTargetRef=r:this.dropTargetNode=r,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}function j(){const{dragDropManager:t}=p.useContext(Oe);return h(t!=null,"Expected drag drop context"),t}function Fr(t,e){const r=j(),n=p.useMemo(()=>new Ar(r.getBackend()),[r]);return N(()=>(n.dragSourceOptions=t||null,n.reconnect(),()=>n.disconnectDragSource()),[n,t]),N(()=>(n.dragPreviewOptions=e||null,n.reconnect(),()=>n.disconnectDragPreview()),[n,e]),n}function Ur(){const t=j();return p.useMemo(()=>new Pr(t),[t])}class $r{beginDrag(){const e=this.spec,r=this.monitor;let n=null;return typeof e.item=="object"?n=e.item:typeof e.item=="function"?n=e.item(r):n={},n??null}canDrag(){const e=this.spec,r=this.monitor;return typeof e.canDrag=="boolean"?e.canDrag:typeof e.canDrag=="function"?e.canDrag(r):!0}isDragging(e,r){const n=this.spec,s=this.monitor,{isDragging:i}=n;return i?i(s):r===e.getSourceId()}endDrag(){const e=this.spec,r=this.monitor,n=this.connector,{end:s}=e;s&&s(r.getItem(),r),n.reconnect()}constructor(e,r,n){this.spec=e,this.monitor=r,this.connector=n}}function qr(t,e,r){const n=p.useMemo(()=>new $r(t,e,r),[e,r]);return p.useEffect(()=>{n.spec=t},[t]),n}function Br(t){return p.useMemo(()=>{const e=t.type;return h(e!=null,"spec.type must be defined"),e},[t])}function Gr(t,e,r){const n=j(),s=qr(t,e,r),i=Br(t);N(function(){if(i!=null){const[c,l]=Mr(i,s,n);return e.receiveHandlerId(c),r.receiveHandlerId(c),l}},[n,e,r,s,i])}function Wr(t,e){const r=Re(t);h(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const n=Ur(),s=Fr(r.options,r.previewOptions);return Gr(r,n,s),[je(r.collect,n,s),Er(s),Nr(s)]}function zr(t){return p.useMemo(()=>t.hooks.dropTarget(),[t])}function Xr(t){const e=j(),r=p.useMemo(()=>new Hr(e.getBackend()),[e]);return N(()=>(r.dropTargetOptions=t||null,r.reconnect(),()=>r.disconnectDropTarget()),[t]),r}function Yr(){const t=j();return p.useMemo(()=>new jr(t),[t])}function Vr(t){const{accept:e}=t;return p.useMemo(()=>(h(t.accept!=null,"accept must be defined"),Array.isArray(e)?e:[e]),[e])}class Jr{canDrop(){const e=this.spec,r=this.monitor;return e.canDrop?e.canDrop(r.getItem(),r):!0}hover(){const e=this.spec,r=this.monitor;e.hover&&e.hover(r.getItem(),r)}drop(){const e=this.spec,r=this.monitor;if(e.drop)return e.drop(r.getItem(),r)}constructor(e,r){this.spec=e,this.monitor=r}}function Qr(t,e){const r=p.useMemo(()=>new Jr(t,e),[e]);return p.useEffect(()=>{r.spec=t},[t]),r}function Zr(t,e,r){const n=j(),s=Qr(t,e),i=Vr(t);N(function(){const[c,l]=Rr(i,s,n);return e.receiveHandlerId(c),r.receiveHandlerId(c),l},[n,e,s,r,i.map(a=>a.toString()).join("|")])}function Kr(t,e){const r=Re(t),n=Yr(),s=Xr(r.options);return Zr(r,n,s),[je(r.collect,n,s),zr(s)]}function ke(t){let e=null;return()=>(e==null&&(e=t()),e)}function en(t,e){return t.filter(r=>r!==e)}function tn(t,e){const r=new Set,n=i=>r.add(i);t.forEach(n),e.forEach(n);const s=[];return r.forEach(i=>s.push(i)),s}class rn{enter(e){const r=this.entered.length,n=s=>this.isNodeInDocument(s)&&(!s.contains||s.contains(e));return this.entered=tn(this.entered.filter(n),[e]),r===0&&this.entered.length>0}leave(e){const r=this.entered.length;return this.entered=en(this.entered.filter(this.isNodeInDocument),e),r>0&&this.entered.length===0}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class nn{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null}})})}loadDataTransfer(e){if(e){const r={};Object.keys(this.config.exposeProperties).forEach(n=>{const s=this.config.exposeProperties[n];s!=null&&(r[n]={value:s(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,r)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,r){return r===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const _e="__NATIVE_FILE__",Le="__NATIVE_URL__",Ae="__NATIVE_TEXT__",He="__NATIVE_HTML__",De=Object.freeze(Object.defineProperty({__proto__:null,FILE:_e,HTML:He,TEXT:Ae,URL:Le},Symbol.toStringTag,{value:"Module"}));function V(t,e,r){const n=e.reduce((s,i)=>s||t.getData(i),"");return n??r}const te={[_e]:{exposeProperties:{files:t=>Array.prototype.slice.call(t.files),items:t=>t.items,dataTransfer:t=>t},matchesTypes:["Files"]},[He]:{exposeProperties:{html:(t,e)=>V(t,e,""),dataTransfer:t=>t},matchesTypes:["Html","text/html"]},[Le]:{exposeProperties:{urls:(t,e)=>V(t,e,"").split(`
`),dataTransfer:t=>t},matchesTypes:["Url","text/uri-list"]},[Ae]:{exposeProperties:{text:(t,e)=>V(t,e,""),dataTransfer:t=>t},matchesTypes:["Text","text/plain"]}};function sn(t,e){const r=te[t];if(!r)throw new Error(`native type ${t} has no configuration`);const n=new nn(r);return n.loadDataTransfer(e),n}function J(t){if(!t)return null;const e=Array.prototype.slice.call(t.types||[]);return Object.keys(te).filter(r=>{const n=te[r];return n?.matchesTypes?n.matchesTypes.some(s=>e.indexOf(s)>-1):!1})[0]||null}const on=ke(()=>/firefox/i.test(navigator.userAgent)),Fe=ke(()=>!!window.safari);class be{interpolate(e){const{xs:r,ys:n,c1s:s,c2s:i,c3s:a}=this;let c=r.length-1;if(e===r[c])return n[c];let l=0,f=a.length-1,d;for(;l<=f;){d=Math.floor(.5*(l+f));const g=r[d];if(g<e)l=d+1;else if(g>e)f=d-1;else return n[d]}c=Math.max(0,f);const m=e-r[c],y=m*m;return n[c]+s[c]*m+i[c]*y+a[c]*m*y}constructor(e,r){const{length:n}=e,s=[];for(let g=0;g<n;g++)s.push(g);s.sort((g,S)=>e[g]<e[S]?-1:1);const i=[],a=[];let c,l;for(let g=0;g<n-1;g++)c=e[g+1]-e[g],l=r[g+1]-r[g],i.push(c),a.push(l/c);const f=[a[0]];for(let g=0;g<i.length-1;g++){const S=a[g],v=a[g+1];if(S*v<=0)f.push(0);else{c=i[g];const x=i[g+1],O=c+x;f.push(3*O/((O+x)/S+(O+c)/v))}}f.push(a[a.length-1]);const d=[],m=[];let y;for(let g=0;g<f.length-1;g++){y=a[g];const S=f[g],v=1/i[g],x=S+f[g+1]-y-y;d.push((y-S-x)*v),m.push(x*v*v)}this.xs=e,this.ys=r,this.c1s=f,this.c2s=d,this.c3s=m}}const an=1;function Ue(t){const e=t.nodeType===an?t:t.parentElement;if(!e)return null;const{top:r,left:n}=e.getBoundingClientRect();return{x:n,y:r}}function L(t){return{x:t.clientX,y:t.clientY}}function cn(t){var e;return t.nodeName==="IMG"&&(on()||!(!((e=document.documentElement)===null||e===void 0)&&e.contains(t)))}function un(t,e,r,n){let s=t?e.width:r,i=t?e.height:n;return Fe()&&t&&(i/=window.devicePixelRatio,s/=window.devicePixelRatio),{dragPreviewWidth:s,dragPreviewHeight:i}}function ln(t,e,r,n,s){const i=cn(e),c=Ue(i?t:e),l={x:r.x-c.x,y:r.y-c.y},{offsetWidth:f,offsetHeight:d}=t,{anchorX:m,anchorY:y}=n,{dragPreviewWidth:g,dragPreviewHeight:S}=un(i,e,f,d),v=()=>{let k=new be([0,.5,1],[l.y,l.y/d*S,l.y+S-d]).interpolate(y);return Fe()&&i&&(k+=(window.devicePixelRatio-1)*S),k},x=()=>new be([0,.5,1],[l.x,l.x/f*g,l.x+g-f]).interpolate(m),{offsetX:O,offsetY:w}=s,E=O===0||O,R=w===0||w;return{x:E?O:x(),y:R?w:v()}}class dn{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var e;return!((e=this.globalContext)===null||e===void 0)&&e.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return((e=this.optionsArgs)===null||e===void 0?void 0:e.rootElement)||this.window}constructor(e,r){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=r}}function gn(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Se(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{},n=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable}))),n.forEach(function(s){gn(t,s,r[s])})}return t}class fn{profile(){var e,r;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((e=this.dragStartSourceIds)===null||e===void 0?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((r=this.dragOverTargetIds)===null||r===void 0?void 0:r.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(e!==void 0){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;if(e!==void 0&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var r;(r=this.window)===null||r===void 0||r.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,r,n){return this.sourcePreviewNodeOptions.set(e,n),this.sourcePreviewNodes.set(e,r),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,r,n){this.sourceNodes.set(e,r),this.sourceNodeOptions.set(e,n);const s=a=>this.handleDragStart(a,e),i=a=>this.handleSelectStart(a);return r.setAttribute("draggable","true"),r.addEventListener("dragstart",s),r.addEventListener("selectstart",i),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),r.removeEventListener("dragstart",s),r.removeEventListener("selectstart",i),r.setAttribute("draggable","false")}}connectDropTarget(e,r){const n=a=>this.handleDragEnter(a,e),s=a=>this.handleDragOver(a,e),i=a=>this.handleDrop(a,e);return r.addEventListener("dragenter",n),r.addEventListener("dragover",s),r.addEventListener("drop",i),()=>{r.removeEventListener("dragenter",n),r.removeEventListener("dragover",s),r.removeEventListener("drop",i)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),r=this.sourceNodeOptions.get(e);return Se({dropEffect:this.altKeyPressed?"copy":"move"},r||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId(),r=this.sourcePreviewNodeOptions.get(e);return Se({anchorX:.5,anchorY:.5,captureDraggingState:!1},r||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(De).some(r=>De[r]===e)}beginDragNativeItem(e,r){this.clearCurrentDragSourceNode(),this.currentNativeSource=sn(e,r),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;const r=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var n;return(n=this.rootElement)===null||n===void 0?void 0:n.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},r)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;(e=this.window)===null||e===void 0||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,r){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(r))}handleDragEnter(e,r){this.dragEnterTargetIds.unshift(r)}handleDragOver(e,r){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(r)}handleDrop(e,r){this.dropTargetIds.unshift(r)}constructor(e,r,n){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=s=>{const i=this.sourceNodes.get(s);return i&&Ue(i)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=s=>!!(s&&this.document&&this.document.body&&this.document.body.contains(s)),this.endDragIfSourceWasRemovedFromDOM=()=>{const s=this.currentDragSourceNode;s==null||this.isNodeInDocument(s)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=s=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(s||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=s=>{if(s.defaultPrevented)return;const{dragStartSourceIds:i}=this;this.dragStartSourceIds=null;const a=L(s);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(i||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:a});const{dataTransfer:c}=s,l=J(c);if(this.monitor.isDragging()){if(c&&typeof c.setDragImage=="function"){const d=this.monitor.getSourceId(),m=this.sourceNodes.get(d),y=this.sourcePreviewNodes.get(d)||m;if(y){const{anchorX:g,anchorY:S,offsetX:v,offsetY:x}=this.getCurrentSourcePreviewNodeOptions(),E=ln(m,y,a,{anchorX:g,anchorY:S},{offsetX:v,offsetY:x});c.setDragImage(y,E.x,E.y)}}try{c?.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(s.target);const{captureDraggingState:f}=this.getCurrentSourcePreviewNodeOptions();f?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(l)this.beginDragNativeItem(l);else{if(c&&!c.types&&(s.target&&!s.target.hasAttribute||!s.target.hasAttribute("draggable")))return;s.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=s=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(s.dataTransfer)}if(!this.enterLeaveCounter.enter(s.target)||this.monitor.isDragging())return;const{dataTransfer:c}=s,l=J(c);l&&this.beginDragNativeItem(l,c)},this.handleTopDragEnter=s=>{const{dragEnterTargetIds:i}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=s.altKey,i.length>0&&this.actions.hover(i,{clientOffset:L(s)}),i.some(c=>this.monitor.canDropOnTarget(c))&&(s.preventDefault(),s.dataTransfer&&(s.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=s=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(s.dataTransfer)}},this.handleTopDragOver=s=>{const{dragOverTargetIds:i}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){s.preventDefault(),s.dataTransfer&&(s.dataTransfer.dropEffect="none");return}this.altKeyPressed=s.altKey,this.lastClientOffset=L(s),this.scheduleHover(i),(i||[]).some(c=>this.monitor.canDropOnTarget(c))?(s.preventDefault(),s.dataTransfer&&(s.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?s.preventDefault():(s.preventDefault(),s.dataTransfer&&(s.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=s=>{this.isDraggingNativeItem()&&s.preventDefault(),this.enterLeaveCounter.leave(s.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=s=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var i;s.preventDefault(),(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(s.dataTransfer)}else J(s.dataTransfer)&&s.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=s=>{const{dropTargetIds:i}=this;this.dropTargetIds=[],this.actions.hover(i,{clientOffset:L(s)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=s=>{const i=s.target;typeof i.dragDrop=="function"&&(i.tagName==="INPUT"||i.tagName==="SELECT"||i.tagName==="TEXTAREA"||i.isContentEditable||(s.preventDefault(),i.dragDrop()))},this.options=new dn(r,n),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new rn(this.isNodeInDocument)}}const hn=function(e,r,n){return new fn(e,r,n)},pn=(t,e)=>{const r={queen:Ke,worker:{email_marketing:Qe,content_creation:Je,analytics_reporting:Ve,lead_generation:Ye,ai_orchestration:Xe},scout:Ze};return t==="worker"&&r.worker[e]?r.worker[e]:r[t]||et},mn=t=>({queen:"from-purple-500 to-pink-500",worker:"from-blue-500 to-cyan-500",scout:"from-green-500 to-emerald-500"})[t]||"from-gray-500 to-gray-600",wn=()=>{const[t,e]=p.useState({id:null,name:"Untitled Workflow",description:"",nodes:[],connections:[],triggers:[]}),[r,n]=p.useState(null),[s,i]=p.useState([]),[a,c]=p.useState({x:0,y:0}),[l,f]=p.useState(!1),[d,m]=p.useState([]),[y,g]=p.useState({isRunning:!1,nodeStatuses:{}}),S=p.useRef(null);p.useEffect(()=>{v()},[]);const v=async()=>{try{const b=await(await fetch("/api/colony/agents",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();if(b.success){const D=b.data.agents.map(T=>({...T,id:T._id,type:T.agentType,category:T.category,capabilities:T.capabilities||[],icon:pn(T.agentType,T.category),color:mn(T.agentType)}));i(D)}}catch(u){console.error("Failed to fetch agents:",u)}},x=p.useCallback((u,b)=>{const D={id:`node_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:u.id,name:u.name,icon:u.icon,position:b||{x:100,y:100},inputs:u.inputs||[],outputs:u.outputs||[],configuration:{},category:u.category};e(T=>({...T,nodes:[...T.nodes,D]})),n(D)},[]),O=p.useCallback((u,b)=>{e(D=>({...D,nodes:D.nodes.map(T=>T.id===u?{...T,...b}:T)}))},[]),w=p.useCallback(u=>{e(b=>({...b,nodes:b.nodes.filter(D=>D.id!==u),connections:b.connections.filter(D=>D.source!==u&&D.target!==u)})),n(null)},[]),E=p.useCallback((u,b)=>{const D={id:`conn_${Date.now()}`,source:u,target:b,type:"data_flow"};e(T=>({...T,connections:[...T.connections,D]}))},[]),R=async()=>{try{const b=await(await fetch("/api/agents/workflows",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({name:t.name,description:t.description,agents:t.nodes,connections:t.connections})})).json();b.success?(alert("Workflow saved successfully!"),e(D=>({...D,id:b.data.id}))):alert(`Failed to save workflow: ${b.message}`)}catch(u){console.error("Save workflow error:",u),alert("Failed to save workflow")}},B=async()=>{if(t.nodes.length===0){alert("Please add at least one agent to the workflow");return}f(!0),m([]);try{for(const u of t.nodes){const b={nodeId:u.id,nodeName:u.name,status:"running",timestamp:new Date,message:`Executing ${u.name}...`};m(D=>[...D,b]);try{const D=k(u.type),_=await(await fetch(`/api/agents/${u.type}/execute`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({inputs:D,context:{workflowId:t.id,nodeId:u.id}})})).json(),$e={nodeId:u.id,nodeName:u.name,status:"completed",timestamp:new Date,message:`${u.name} completed successfully`,result:_.success?"Success":_.message};m(qe=>[...qe.slice(0,-1),$e])}catch(D){const T={nodeId:u.id,nodeName:u.name,status:"failed",timestamp:new Date,message:`${u.name} failed: ${D.message}`,error:D.message};m(_=>[..._.slice(0,-1),T])}await new Promise(D=>setTimeout(D,1e3))}}catch(u){console.error("Workflow execution error:",u)}finally{f(!1)}},k=u=>({"email-sequence-generator":{businessInfo:{industry:"Marketing Technology",productService:"Email Marketing Platform",targetAudience:"Small business owners",pricePoint:"$99/month"},sequenceSettings:{sequenceLength:5,tone:"professional",primaryGoal:"sales"}},"subject-line-optimizer":{originalSubject:"Boost Your Marketing ROI Today",audienceInfo:{industry:"Marketing",audience:"Marketing professionals"}},"audience-segmentation":{customerData:[{id:1,engagement:"high",purchases:5},{id:2,engagement:"medium",purchases:2}],behaviorMetrics:{emailOpenRate:.25,clickThroughRate:.03}}})[u]||{};return o.jsx(br,{backend:hn,children:o.jsxs("div",{className:"h-screen flex bg-gray-50",children:[o.jsxs("div",{className:"w-80 bg-white border-r border-gray-200 overflow-y-auto",children:[o.jsxs("div",{className:"p-4 border-b border-gray-200",children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Agent Library"}),o.jsx("p",{className:"text-sm text-gray-600",children:"Drag agents to canvas"})]}),o.jsx("div",{className:"p-4 space-y-4",children:Object.entries(s.reduce((u,b)=>{const D=b.category||"uncategorized";return u[D]||(u[D]=[]),u[D].push(b),u},{})).map(([u,b])=>o.jsxs("div",{children:[o.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2 capitalize",children:u.replace("-"," ")}),o.jsx("div",{className:"space-y-2",children:b.map(D=>o.jsx(vn,{agent:D},D.id))})]},u))})]}),o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsx("div",{className:"bg-white border-b border-gray-200 p-4",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("input",{type:"text",value:t.name,onChange:u=>e(b=>({...b,name:u.target.value})),className:"text-xl font-semibold bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1",placeholder:"Workflow Name"}),o.jsxs(Ge,{variant:"secondary",className:"text-xs",children:[t.nodes.length," nodes"]})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsxs(G,{variant:"outline",size:"sm",onClick:R,children:[o.jsx(rt,{className:"w-4 h-4 mr-2"}),"Save"]}),o.jsx(G,{size:"sm",onClick:B,disabled:l||t.nodes.length===0,className:"bg-green-600 hover:bg-green-700",children:l?o.jsxs(o.Fragment,{children:[o.jsx(tt,{size:"sm",className:"mr-2"}),"Running..."]}):o.jsxs(o.Fragment,{children:[o.jsx(We,{className:"w-4 h-4 mr-2"}),"Execute"]})})]})]})}),o.jsx("div",{className:"flex-1 relative overflow-hidden",children:o.jsx(yn,{ref:S,workflow:t,onAddNode:x,onNodeUpdate:O,onDeleteNode:w,onConnect:E,selectedNode:r,onSelectNode:n})})]}),r&&o.jsx("div",{className:"w-80 bg-white border-l border-gray-200",children:o.jsx(bn,{node:r,onUpdate:u=>O(r.id,u),onClose:()=>n(null)})}),d.length>0&&o.jsxs("div",{className:"absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border max-h-48 overflow-y-auto",children:[o.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[o.jsx("h3",{className:"font-medium",children:"Execution Logs"}),o.jsx(G,{variant:"outline",size:"sm",onClick:()=>m([]),children:o.jsx(xe,{className:"w-4 h-4"})})]}),o.jsx("div",{className:"p-4 space-y-2 max-h-32 overflow-y-auto",children:d.map((u,b)=>o.jsxs("div",{className:`text-sm p-2 rounded ${u.status==="completed"?"bg-green-50 text-green-800":u.status==="failed"?"bg-red-50 text-red-800":"bg-blue-50 text-blue-800"}`,children:[o.jsx("div",{className:"font-medium",children:u.nodeName}),o.jsx("div",{children:u.message}),o.jsx("div",{className:"text-xs opacity-75",children:u.timestamp.toLocaleTimeString()})]},b))})]})]})})},vn=({agent:t})=>{const[{isDragging:e},r]=Wr(()=>({type:"agent",item:t,collect:n=>({isDragging:n.isDragging()})}));return o.jsx("div",{ref:r,className:`p-3 bg-gray-50 rounded-lg border cursor-move hover:shadow-md transition-shadow ${e?"opacity-50":""}`,children:o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"text-xl",children:t.icon}),o.jsxs("div",{children:[o.jsx("div",{className:"font-medium text-sm",children:t.name}),o.jsx("div",{className:"text-xs text-gray-500 line-clamp-2",children:t.description})]})]})})},yn=ze.forwardRef(({workflow:t,onAddNode:e,onNodeUpdate:r,onDeleteNode:n,onConnect:s,selectedNode:i,onSelectNode:a},c)=>{const[{isOver:l},f]=Kr(()=>({accept:"agent",drop:(d,m)=>{const y=m.getClientOffset(),g=c.current.getBoundingClientRect(),S={x:y.x-g.left,y:y.y-g.top};e(d,S)},collect:d=>({isOver:d.isOver()})}));return o.jsxs("div",{ref:d=>{c.current=d,f(d)},className:`w-full h-full relative bg-gray-50 ${l?"bg-blue-50":""}`,style:{backgroundImage:"radial-gradient(circle, #e5e7eb 1px, transparent 1px)",backgroundSize:"20px 20px"},children:[t.nodes.length===0&&o.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-4xl mb-4",children:"🎨"}),o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Start Building Your Workflow"}),o.jsx("p",{className:"text-gray-600",children:"Drag agents from the left panel to create your marketing automation"})]})}),t.nodes.map(d=>o.jsx(Dn,{node:d,isSelected:i?.id===d.id,onSelect:()=>a(d),onUpdate:m=>r(d.id,m),onDelete:()=>n(d.id),onConnect:s},d.id)),o.jsxs("svg",{className:"absolute inset-0 pointer-events-none",children:[t.connections.map(d=>{const m=t.nodes.find(O=>O.id===d.source),y=t.nodes.find(O=>O.id===d.target);if(!m||!y)return null;const g=m.position.x+150,S=m.position.y+40,v=y.position.x,x=y.position.y+40;return o.jsx("line",{x1:g,y1:S,x2:v,y2:x,stroke:"#6b7280",strokeWidth:"2",markerEnd:"url(#arrowhead)"},d.id)}),o.jsx("defs",{children:o.jsx("marker",{id:"arrowhead",markerWidth:"10",markerHeight:"7",refX:"9",refY:"3.5",orient:"auto",children:o.jsx("polygon",{points:"0 0, 10 3.5, 0 7",fill:"#6b7280"})})})]})]})}),Dn=({node:t,isSelected:e,onSelect:r,onUpdate:n,onDelete:s,onConnect:i})=>{const[a,c]=p.useState(t.position),l=f=>{f.preventDefault();const d=f.clientX-a.x,m=f.clientY-a.y,y=S=>{const v={x:S.clientX-d,y:S.clientY-m};c(v),n({position:v})},g=()=>{document.removeEventListener("mousemove",y),document.removeEventListener("mouseup",g)};document.addEventListener("mousemove",y),document.addEventListener("mouseup",g)};return o.jsxs("div",{className:`absolute bg-white rounded-lg border-2 shadow-lg cursor-move w-64 ${e?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"}`,style:{left:a.x,top:a.y},onMouseDown:l,onClick:r,children:[o.jsxs("div",{className:"p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"text-xl",children:t.icon}),o.jsx("div",{className:"font-medium text-sm",children:t.name})]}),o.jsx("button",{onClick:f=>{f.stopPropagation(),s()},className:"text-gray-400 hover:text-red-500 transition-colors",children:o.jsx(nt,{className:"w-4 h-4"})})]}),o.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Category: ",t.category?.replace("-"," ")]}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"text-xs",children:[o.jsx("div",{className:"font-medium text-gray-700",children:"Inputs:"}),o.jsx("div",{className:"text-gray-500",children:t.inputs?.join(", ")||"None"})]}),o.jsxs("div",{className:"text-xs",children:[o.jsx("div",{className:"font-medium text-gray-700",children:"Outputs:"}),o.jsx("div",{className:"text-gray-500",children:t.outputs?.join(", ")||"None"})]})]})]}),o.jsx("div",{className:"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2",children:o.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-md"})}),o.jsx("div",{className:"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2",children:o.jsx("div",{className:"w-3 h-3 bg-gray-400 rounded-full border-2 border-white shadow-md"})})]})},bn=({node:t,onUpdate:e,onClose:r})=>{const[n,s]=p.useState(t.configuration||{}),i=(a,c)=>{const l={...n,[a]:c};s(l),e({configuration:l})};return o.jsxs("div",{className:"h-full flex flex-col",children:[o.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[o.jsx("h3",{className:"font-medium",children:"Configure Agent"}),o.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:o.jsx(xe,{className:"w-4 h-4"})})]}),o.jsxs("div",{className:"flex-1 p-4 space-y-4 overflow-y-auto",children:[o.jsx("div",{children:o.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[o.jsx("div",{className:"text-2xl",children:t.icon}),o.jsxs("div",{children:[o.jsx("div",{className:"font-medium",children:t.name}),o.jsx("div",{className:"text-sm text-gray-500",children:t.category})]})]})}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Agent Name"}),o.jsx("input",{type:"text",value:t.name,onChange:a=>e({name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enabled"}),o.jsx("input",{type:"checkbox",checked:n.enabled!==!1,onChange:a=>i("enabled",a.target.checked),className:"rounded"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Timeout (seconds)"}),o.jsx("input",{type:"number",value:n.timeout||30,onChange:a=>i("timeout",parseInt(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"300"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Retry Attempts"}),o.jsx("input",{type:"number",value:n.retries||1,onChange:a=>i("retries",parseInt(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"5"})]})]})]})};export{wn as default};
