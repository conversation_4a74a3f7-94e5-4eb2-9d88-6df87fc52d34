import{r as i,j as e,m as a,M as x,T as g,a as h,d as b,h as y,S as j,B as f,J as N,K as v,R as w}from"./index-CVpabiD8.js";/* empty css                 */import{M as r}from"./mic-DfrRXnef.js";import{L as M}from"./languages-B8P6yi0c.js";import{T as A}from"./trophy-BLwcuC8U.js";const k=()=>{const[o,l]=i.useState(null),[c,d]=i.useState([]),[n,m]=i.useState(!1),p=[{id:"sequences",value:"2,847",change:"+23%",label:"Email Sequences",icon:x,color:"var(--quantum-purple)"},{id:"engagement",value:"67.3%",change:"+5.2%",label:"Avg Engagement",icon:g,color:"var(--quantum-blue)"},{id:"revenue",value:"$45.2K",change:"+12%",label:"Generated Revenue",icon:h,color:"var(--quantum-cyan)"},{id:"ai-score",value:"94/100",change:"+8",label:"AI Quality Score",icon:b,color:"var(--quantum-pink)"}],u=[{icon:r,title:"Voice-to-Email AI",description:"Speak your ideas, get perfect email sequences",status:"new"},{icon:M,title:"Multi-Language Support",description:"Generate emails in 50+ languages with cultural adaptation",status:"new"},{icon:f,title:"Performance Prediction",description:"AI predicts email success before sending",status:"coming"},{icon:A,title:"Competitor Analysis",description:"Analyze and outperform competitor emails",status:"coming"}];return i.useEffect(()=>{d(["Your welcome emails perform 34% better with personalized subject lines","Tuesday 10 AM shows highest engagement for your audience","Adding urgency in email 3 increases conversions by 23%","Your storytelling approach resonates best with tech audiences"])},[]),e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 p-6",children:[e.jsx("div",{className:"particle-bg"}),e.jsxs(a.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsx("h1",{className:"text-5xl font-bold holographic mb-2",children:"NeuroColony Dashboard"}),e.jsx("p",{className:"text-gray-300 text-lg",children:"Quantum-powered email intelligence at your fingertips"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:p.map((s,t)=>e.jsxs(a.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:t*.1},className:"glass-card p-6 cursor-pointer",onMouseEnter:()=>l(s.id),onMouseLeave:()=>l(null),children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx(s.icon,{size:24,style:{color:s.color},className:"floating"}),e.jsx("span",{className:`text-sm font-semibold ${s.change.startsWith("+")?"text-green-400":"text-red-400"}`,children:s.change})]}),e.jsx("h3",{className:"text-3xl font-bold text-white mb-1",children:s.value}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.label}),e.jsx(y,{children:o===s.id&&e.jsx(a.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-4 pt-4 border-t border-gray-700",children:e.jsx("div",{className:"h-20 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded"})})})]},s.id))}),e.jsxs(a.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"glass-card p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(j,{className:"text-yellow-400 mr-2"}),e.jsx("h2",{className:"text-2xl font-bold text-white",children:"AI Insights"})]}),e.jsx("div",{className:"space-y-3",children:c.map((s,t)=>e.jsxs(a.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.5+t*.1},className:"flex items-start",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 mt-2 mr-3"}),e.jsx("p",{className:"text-gray-300",children:s})]},t))})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:u.map((s,t)=>e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6+t*.1},className:"glass-card p-6 card-3d",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx(s.icon,{size:32,className:"text-purple-400"}),s.status==="new"&&e.jsx("span",{className:"px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-semibold",children:"NEW"}),s.status==="coming"&&e.jsx("span",{className:"px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-xs font-semibold",children:"COMING SOON"})]}),e.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:s.title}),e.jsx("p",{className:"text-gray-400",children:s.description})]},t))}),e.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1},className:"fixed bottom-8 right-8",children:e.jsxs("button",{onClick:()=>m(!n),className:`quantum-button flex items-center space-x-2 ${n?"bg-gradient-to-r from-red-500 to-pink-500":""}`,children:[e.jsx(r,{size:20}),e.jsx("span",{children:n?"Voice Mode Active":"Enable Voice Mode"})]})}),e.jsxs(a.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.8},className:"fixed bottom-8 left-8 flex space-x-4",children:[e.jsx("button",{className:"glass-card p-4 rounded-full hover:scale-110 transition-transform",children:e.jsx(N,{size:24,className:"text-purple-400"})}),e.jsx("button",{className:"glass-card p-4 rounded-full hover:scale-110 transition-transform",children:e.jsx(v,{size:24,className:"text-blue-400"})}),e.jsx("button",{className:"glass-card p-4 rounded-full hover:scale-110 transition-transform",children:e.jsx(w,{size:24,className:"text-cyan-400"})})]})]})};export{k as default};
