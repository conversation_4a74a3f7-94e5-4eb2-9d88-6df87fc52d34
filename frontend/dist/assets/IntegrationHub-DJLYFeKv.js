import{c as R,r,j as e,a7 as l,_ as H,a8 as x,aa as h,ab as u,Z as K,a9 as f,a as _,w as O,p as q,m as Z,a6 as D,g as J,i as k,C as U}from"./index-CVpabiD8.js";import{S as G}from"./Spinner-D3g4Pp_K.js";import{P as I}from"./plus-CLzd-zUl.js";import{T as Q}from"./trash-2-CS_FJ-_W.js";import{C as V}from"./clock-DLEz2ATr.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=R("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),re=()=>{const[n,p]=r.useState([]),[c,o]=r.useState([]),[y,m]=r.useState(!0),[i,N]=r.useState("connected"),[a,b]=r.useState("all"),[v,A]=r.useState(""),[P,g]=r.useState(!1);r.useEffect(()=>{j(),T()},[]);const j=async()=>{try{const t=await(await fetch("/api/integrations",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();t.success&&p(t.data.integrations||[])}catch(s){console.error("Failed to fetch integrations:",s)}},T=async()=>{try{const t=await(await fetch("/api/integrations/platforms",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();t.success&&o(t.data.platforms||[])}catch(s){console.error("Failed to fetch platforms:",s)}finally{m(!1)}},z=async(s,t)=>{try{(await(await fetch("/api/integrations",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({platform:s.id,credentials:t,name:`${s.name} Integration`})})).json()).success&&(j(),g(!1),alert(`${s.name} connected successfully!`))}catch(d){console.error("Failed to connect platform:",d),alert("Failed to connect platform. Please try again.")}},$=async s=>{if(confirm("Are you sure you want to disconnect this integration?"))try{(await fetch(`/api/integrations/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok&&(j(),alert("Integration disconnected successfully"))}catch(t){console.error("Failed to disconnect integration:",t),alert("Failed to disconnect integration")}},F=async s=>{try{(await(await fetch(`/api/integrations/${s}/test`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json()).success?alert("Connection test successful!"):alert("Connection test failed")}catch(t){console.error("Connection test failed:",t),alert("Connection test failed")}},M=s=>{switch(s){case"connected":return e.jsx(U,{className:"w-4 h-4 text-green-500"});case"error":return e.jsx(k,{className:"w-4 h-4 text-red-500"});case"pending":return e.jsx(V,{className:"w-4 h-4 text-yellow-500"});default:return e.jsx(k,{className:"w-4 h-4 text-gray-500"})}},B=s=>{switch(s){case"connected":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},w=n.filter(s=>{const t=i==="all"||s.status===i,d=a==="all"||c.find(L=>L.id===s.platform)?.category===a,S=s.name.toLowerCase().includes(v.toLowerCase())||s.platform.toLowerCase().includes(v.toLowerCase());return t&&d&&S}),E=c.filter(s=>!n.some(t=>t.platform===s.id&&t.status==="connected")),C=[...new Set(c.map(s=>s.category))];return y?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx(G,{size:"xl"})}):e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6",children:[e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"🔗 Integration Hub"}),e.jsx("p",{className:"text-gray-600 text-lg",children:"Connect 400+ Marketing Platforms • Superior Marketing-First Approach"})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(l,{onClick:()=>g(!0),className:"bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600",children:[e.jsx(I,{className:"w-4 h-4 mr-2"}),"Add Integration"]}),e.jsxs(l,{variant:"outline",onClick:j,children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsxs(x,{children:[e.jsxs(h,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(u,{className:"text-sm font-medium",children:"Total Integrations"}),e.jsx(K,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(f,{children:[e.jsx("div",{className:"text-2xl font-bold",children:n.length}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[n.filter(s=>s.status==="connected").length," connected"]})]})]}),e.jsxs(x,{children:[e.jsxs(h,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(u,{className:"text-sm font-medium",children:"Available Platforms"}),e.jsx(_,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(f,{children:[e.jsx("div",{className:"text-2xl font-bold",children:c.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Marketing-focused platforms"})]})]}),e.jsxs(x,{children:[e.jsxs(h,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(u,{className:"text-sm font-medium",children:"Health Status"}),e.jsx(O,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(f,{children:[e.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[Math.round(n.filter(s=>s.status==="connected").length/Math.max(n.length,1)*100),"%"]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Healthy connections"})]})]}),e.jsxs(x,{children:[e.jsxs(h,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(u,{className:"text-sm font-medium",children:"Categories"}),e.jsx(W,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(f,{children:[e.jsx("div",{className:"text-2xl font-bold",children:C.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Platform categories"})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[e.jsx("div",{className:"flex space-x-1 bg-gray-200 p-1 rounded-lg",children:["all","connected","pending","error"].map(s=>e.jsx("button",{onClick:()=>N(s),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors capitalize ${i===s?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:s},s))}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx("input",{type:"text",placeholder:"Search integrations...",value:v,onChange:s=>A(s.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("select",{value:a,onChange:s=>b(s.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Categories"}),C.map(s=>e.jsx("option",{value:s,children:s},s))]})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:w.map(s=>{const t=c.find(d=>d.id===s.platform);return e.jsx(Z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsxs(x,{className:"relative overflow-hidden hover:shadow-lg transition-shadow",children:[e.jsx(h,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"text-2xl",children:t?.icon||"🔗"}),e.jsxs("div",{children:[e.jsx(u,{className:"text-lg",children:t?.name||s.platform}),e.jsx("p",{className:"text-sm text-gray-600",children:t?.category})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[M(s.status),e.jsx(D,{className:B(s.status),children:s.status})]})]})}),e.jsxs(f,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:t?.description||"Marketing platform integration"}),s.status==="connected"&&e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Success Rate:"}),e.jsxs("span",{className:"font-medium",children:[s.successRate?.toFixed(1)||100,"%"]})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Total Requests:"}),e.jsx("span",{className:"font-medium",children:s.usage?.totalRequests||0})]})]}),e.jsxs("div",{className:"flex gap-2",children:[s.status==="connected"&&e.jsx(l,{size:"sm",variant:"outline",onClick:()=>F(s._id),children:"Test"}),e.jsx(l,{size:"sm",variant:"outline",children:e.jsx(J,{className:"w-4 h-4"})}),e.jsx(l,{size:"sm",variant:"outline",onClick:()=>$(s._id),className:"text-red-600 hover:text-red-700",children:e.jsx(Q,{className:"w-4 h-4"})})]})]})]})},s._id)})}),w.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🔗"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No integrations found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:i==="all"?"Connect your first marketing platform to get started":`No ${i} integrations found`}),e.jsxs(l,{onClick:()=>g(!0),children:[e.jsx(I,{className:"w-4 h-4 mr-2"}),"Add Integration"]})]})]}),P&&e.jsx(X,{platforms:E,onConnect:z,onClose:()=>g(!1)})]})},X=({platforms:n,onConnect:p,onClose:c})=>{const[o,y]=r.useState(null),[m,i]=r.useState({}),N=()=>{o&&m.apiKey?p(o,m):alert("Please select a platform and enter API key")};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Add Integration"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Platform"}),e.jsxs("select",{value:o?.id||"",onChange:a=>y(n.find(b=>b.id===a.target.value)),className:"w-full p-2 border border-gray-300 rounded-lg",children:[e.jsx("option",{value:"",children:"Select a platform"}),n.map(a=>e.jsxs("option",{value:a.id,children:[a.icon," ",a.name]},a.id))]})]}),o&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"API Key"}),e.jsx("input",{type:"password",placeholder:"Enter your API key",value:m.apiKey||"",onChange:a=>i({...m,apiKey:a.target.value}),className:"w-full p-2 border border-gray-300 rounded-lg"}),e.jsx("p",{className:"text-xs text-gray-600 mt-1",children:o.description})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx(l,{onClick:N,className:"flex-1",children:"Connect"}),e.jsx(l,{variant:"outline",onClick:c,children:"Cancel"})]})]})})};export{re as default};
