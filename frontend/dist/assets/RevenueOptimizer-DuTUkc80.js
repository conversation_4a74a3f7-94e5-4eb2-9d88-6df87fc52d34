import{r as l,j as e,m as c,R as S,_ as T,B as $,d as j,a as v,D as O,X as x,T as E,$ as D,Z as L,f as P,V as i}from"./index-CVpabiD8.js";import{U}from"./users-f__6LX8a.js";import{C as y}from"./clock-DLEz2ATr.js";import{A as B}from"./arrow-down-right-D_ob90uq.js";import{S as F}from"./star-C2599PJP.js";const Q=({user:G})=>{const[t,N]=l.useState({totalRevenue:0,monthlyGrowth:0,conversionRate:0,averageLTV:0,churnRate:0,upgradeRate:0}),[h,f]=l.useState([]),[g,p]=l.useState(!1),[d,w]=l.useState("30d"),[o,R]=l.useState("overview");l.useEffect(()=>{u(),m()},[d]);const u=async()=>{try{const s=await fetch(`http://localhost:5002/api/revenue-optimization/analytics?timeframe=${d}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(s.ok){const a=await s.json();N(a.analytics)}}catch(s){console.error("Error fetching revenue data:",s)}},m=async()=>{try{const s=await fetch("http://localhost:5002/api/revenue-optimization/suggestions",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(s.ok){const a=await s.json();f(a.suggestions)}}catch(s){console.error("Error fetching optimizations:",s)}},b=async()=>{p(!0);try{(await fetch("http://localhost:5002/api/revenue-optimization/analyze",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok?(await u(),await m(),i.success("Revenue analysis completed!")):i.error("Failed to run revenue analysis")}catch{i.error("Error running revenue analysis")}finally{p(!1)}},A=async s=>{try{(await fetch(`http://localhost:5002/api/revenue-optimization/apply/${s}`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok?(i.success("Optimization applied successfully!"),m()):i.error("Failed to apply optimization")}catch{i.error("Error applying optimization")}},r=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),n=s=>`${s.toFixed(1)}%`,z=s=>s>=20?"text-green-600 bg-green-100":s>=10?"text-blue-600 bg-blue-100":"text-yellow-600 bg-yellow-100",I=s=>s==="high"?"bg-red-100 text-red-800":s==="medium"?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",k=[{id:"overview",label:"Overview",icon:$},{id:"optimizations",label:"AI Recommendations",icon:j},{id:"trends",label:"Revenue Trends",icon:v}],C=[{value:"7d",label:"Last 7 days"},{value:"30d",label:"Last 30 days"},{value:"90d",label:"Last 90 days"},{value:"1y",label:"Last year"}];return e.jsxs("div",{className:"max-w-7xl mx-auto p-6",children:[e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900 rounded-xl p-8 text-white mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(S,{className:"h-8 w-8 text-green-400"}),e.jsx("h1",{className:"text-3xl font-bold",children:"Revenue Optimization Engine"})]}),e.jsx("p",{className:"text-green-200 text-lg",children:"AI-powered revenue analysis with conversion optimization and growth strategies"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("select",{value:d,onChange:s=>w(s.target.value),className:"bg-white/10 text-white border border-white/20 rounded-lg px-4 py-2",children:C.map(s=>e.jsx("option",{value:s.value,className:"text-black",children:s.label},s.value))}),e.jsx("button",{onClick:b,disabled:g,className:"bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-6 rounded-lg transition-colors flex items-center gap-2",children:g?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"Analyzing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-5 w-5"}),"Analyze Now"]})})]})]})}),e.jsx("div",{className:"flex border-b border-gray-200 mb-8",children:k.map(s=>e.jsxs("button",{onClick:()=>R(s.id),className:`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${o===s.id?"border-b-2 border-green-600 text-green-600":"text-gray-600 hover:text-green-600"}`,children:[e.jsx(s.icon,{className:"h-5 w-5"}),s.label]},s.id))}),o==="overview"&&e.jsxs(c.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border-l-4 border-green-500",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Total Revenue"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r(t.totalRevenue)})]}),e.jsx(O,{className:"h-8 w-8 text-green-500"})]}),e.jsxs("div",{className:"flex items-center mt-2",children:[e.jsx(x,{className:"h-4 w-4 text-green-500 mr-1"}),e.jsxs("span",{className:"text-green-600 text-sm font-medium",children:["+",n(t.monthlyGrowth)," this month"]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border-l-4 border-blue-500",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Conversion Rate"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:n(t.conversionRate)})]}),e.jsx(E,{className:"h-8 w-8 text-blue-500"})]}),e.jsxs("div",{className:"flex items-center mt-2",children:[e.jsx(x,{className:"h-4 w-4 text-blue-500 mr-1"}),e.jsx("span",{className:"text-blue-600 text-sm font-medium",children:"Above industry average"})]})]}),e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border-l-4 border-purple-500",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Average LTV"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r(t.averageLTV)})]}),e.jsx(U,{className:"h-8 w-8 text-purple-500"})]}),e.jsxs("div",{className:"flex items-center mt-2",children:[e.jsx(x,{className:"h-4 w-4 text-purple-500 mr-1"}),e.jsxs("span",{className:"text-purple-600 text-sm font-medium",children:["+",n(t.upgradeRate)," upgrade rate"]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border-l-4 border-orange-500",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Churn Rate"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:n(t.churnRate)})]}),e.jsx(y,{className:"h-8 w-8 text-orange-500"})]}),e.jsxs("div",{className:"flex items-center mt-2",children:[e.jsx(B,{className:"h-4 w-4 text-green-500 mr-1"}),e.jsx("span",{className:"text-green-600 text-sm font-medium",children:"Decreasing (good!)"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center gap-2",children:[e.jsx(D,{className:"h-6 w-6 text-yellow-500"}),"Quick Insights"]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[e.jsx("div",{className:"font-bold text-green-800",children:"Revenue Growth"}),e.jsxs("div",{className:"text-green-700 text-sm mt-1",children:["Your revenue is growing ",n(t.monthlyGrowth)," month-over-month, indicating strong product-market fit."]})]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("div",{className:"font-bold text-blue-800",children:"Conversion Opportunity"}),e.jsx("div",{className:"text-blue-700 text-sm mt-1",children:"Optimizing your email sequences could increase conversions by an estimated 15-25%."})]}),e.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[e.jsx("div",{className:"font-bold text-purple-800",children:"LTV Optimization"}),e.jsxs("div",{className:"text-purple-700 text-sm mt-1",children:["Focus on increasing user engagement to boost lifetime value by ",n(12),"."]})]})]})]})]}),o==="optimizations"&&e.jsx(c.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:h.length===0?e.jsxs("div",{className:"bg-white rounded-xl p-8 shadow-lg text-center",children:[e.jsx(j,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"No Optimizations Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Run an analysis to get AI-powered revenue optimization recommendations."}),e.jsx("button",{onClick:b,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg transition-colors",children:"Run Analysis"})]}):e.jsx("div",{className:"grid gap-6",children:h.map((s,a)=>e.jsxs(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:a*.1},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:border-green-300 transition-colors",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h4",{className:"text-lg font-bold text-gray-900",children:s.title||"Revenue Optimization"}),e.jsxs("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${I(s.priority||"medium")}`,children:[s.priority||"Medium"," Priority"]}),e.jsxs("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${z(s.impact||15)}`,children:["+",s.impact||15,"% Revenue Impact"]})]}),e.jsx("p",{className:"text-gray-600 mb-3",children:s.description||"AI-powered optimization recommendation to increase revenue and improve conversion rates."}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(y,{className:"h-4 w-4"}),s.timeToImplement||"2-3"," days to implement"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(F,{className:"h-4 w-4"}),s.confidenceScore||85,"% confidence"]})]})]}),e.jsxs("button",{onClick:()=>A(s.id||a),className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center gap-2",children:[e.jsx(L,{className:"h-4 w-4"}),"Apply"]})]}),s.metrics&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h5",{className:"font-bold mb-2",children:"Expected Improvements:"}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Conversion Rate"}),e.jsxs("div",{className:"font-bold text-green-600",children:["+",s.metrics.conversionIncrease||12,"%"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Revenue"}),e.jsxs("div",{className:"font-bold text-green-600",children:["+",r(s.metrics.revenueIncrease||2500),"/month"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"User Engagement"}),e.jsxs("div",{className:"font-bold text-green-600",children:["+",s.metrics.engagementIncrease||18,"%"]})]})]})]})]},s.id||a))})}),o==="trends"&&e.jsxs(c.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white rounded-xl p-6 shadow-lg",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-xl font-bold flex items-center gap-2",children:[e.jsx(v,{className:"h-6 w-6 text-green-600"}),"Revenue Trends Analysis"]}),e.jsxs("button",{className:"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2",children:[e.jsx(P,{className:"h-4 w-4"}),"Export Report"]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-4",children:"Revenue Growth Trajectory"}),e.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 text-center",children:[e.jsxs("div",{className:"text-4xl font-bold text-green-600 mb-2",children:["+",n(t.monthlyGrowth)]}),e.jsx("div",{className:"text-gray-600",children:"Monthly Growth Rate"}),e.jsxs("div",{className:"mt-4 text-sm text-gray-500",children:["Projected: ",r(t.totalRevenue*1.15)," next month"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-4",children:"Key Performance Indicators"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"Customer Acquisition Cost"}),e.jsx("span",{className:"font-bold",children:r(45)})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"Monthly Recurring Revenue"}),e.jsx("span",{className:"font-bold",children:r(t.totalRevenue*.8)})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"Revenue per User"}),e.jsx("span",{className:"font-bold",children:r(29)})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"Payback Period"}),e.jsx("span",{className:"font-bold",children:"1.6 months"})]})]})]})]})]})]})};export{Q as default};
