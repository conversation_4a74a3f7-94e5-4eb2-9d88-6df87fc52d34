import{r as i,j as e,a7 as r,a8 as c,a9 as o,aa as w,ab as v,a6 as u}from"./index-CVpabiD8.js";import{S as y}from"./Spinner-D3g4Pp_K.js";const L=()=>{const[t,p]=i.useState(null),[g,h]=i.useState(!0),[n,m]=i.useState("all"),[d,f]=i.useState(""),[k,j]=i.useState(new Set);i.useEffect(()=>{C()},[]);const C=async()=>{try{const a=await(await fetch("/api/colony/templates",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();a.success&&p({featuredAgents:[...a.data.templates.queens,...a.data.templates.workers,...a.data.templates.scouts],categories:{"Email Marketing":a.data.templates.queens.concat(a.data.templates.workers.filter(l=>l.category==="email_marketing")),"Content Creation":a.data.templates.workers.filter(l=>l.category==="content_creation"),"Analytics & Reporting":a.data.templates.scouts.concat(a.data.templates.workers.filter(l=>l.category==="analytics_reporting")),"Lead Generation":a.data.templates.workers.filter(l=>l.category==="lead_generation"),"AI Orchestration":a.data.templates.queens.filter(l=>l.category==="ai_orchestration")},totalAgents:a.data.total})}catch(s){console.error("Failed to fetch marketplace data:",s)}finally{h(!1)}},S=async s=>{try{j(x=>new Set([...x,s]));const l=await(await fetch("/api/colony/agents/from-template",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({templateKey:s,customizations:{}})})).json();l.success?alert("Agent installed successfully! You can now use it in your workflows."):(j(x=>{const b=new Set(x);return b.delete(agentId),b}),alert(`Installation failed: ${l.message}`))}catch(a){console.error("Agent installation error:",a),j(l=>{const x=new Set(l);return x.delete(agentId),x}),alert("Failed to install agent")}},N=t?.featured?.filter(s=>{const a=s.name.toLowerCase().includes(d.toLowerCase())||s.description.toLowerCase().includes(d.toLowerCase()),l=n==="all"||s.category===n;return a&&l})||[];return g?e.jsxs("div",{className:"flex items-center justify-center min-h-screen",children:[e.jsx(y,{size:"lg"}),e.jsx("span",{className:"ml-3 text-lg",children:"Loading Agent Marketplace..."})]}):e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Agent Marketplace"}),e.jsx("p",{className:"text-gray-600 mt-2",children:'Install "automation agents" for marketing automation • One-click installation'})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(r,{onClick:()=>window.location.href="/agent-dashboard",variant:"outline",children:"← Back to Dashboard"}),e.jsx(r,{onClick:()=>window.location.href="/create-agent",className:"bg-purple-600 hover:bg-purple-700",children:"Publish Agent"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8",children:[e.jsx(c,{className:"bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200",children:e.jsx(o,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-600",children:"Total Agents"}),e.jsx("p",{className:"text-2xl font-bold text-blue-900",children:t?.stats?.totalAgents||0}),e.jsx("p",{className:"text-xs text-blue-500",children:"Ready to install"})]}),e.jsx("div",{className:"text-2xl"})]})})}),e.jsx(c,{className:"bg-gradient-to-r from-green-50 to-green-100 border-green-200",children:e.jsx(o,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-green-600",children:"Active Installs"}),e.jsx("p",{className:"text-2xl font-bold text-green-900",children:t?.stats?.activeInstalls?.toLocaleString()||"0"}),e.jsx("p",{className:"text-xs text-green-500",children:"Across all users"})]}),e.jsx("div",{className:"text-2xl",children:"📦"})]})})}),e.jsx(c,{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200",children:e.jsx(o,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-yellow-600",children:"Avg Rating"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-900",children:t?.stats?.averageRating||"0"}),e.jsx("p",{className:"text-xs text-yellow-500",children:"Community reviewed"})]}),e.jsx("div",{className:"text-2xl",children:"⭐"})]})})}),e.jsx(c,{className:"bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200",children:e.jsx(o,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-purple-600",children:"New This Week"}),e.jsx("p",{className:"text-2xl font-bold text-purple-900",children:t?.stats?.newThisWeek||0}),e.jsx("p",{className:"text-xs text-purple-500",children:"Fresh agents"})]}),e.jsx("div",{className:"text-2xl",children:"🆕"})]})})})]}),e.jsxs("div",{className:"mb-6 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search agents by name or description...",value:d,onChange:s=>f(s.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]}),e.jsxs("div",{className:"flex space-x-2 overflow-x-auto pb-2",children:[e.jsx(r,{variant:n==="all"?"default":"outline",onClick:()=>m("all"),size:"sm",children:"All Categories"}),t?.categories?.map(s=>e.jsxs(r,{variant:n===s.name.toLowerCase().replace(" ","-")?"default":"outline",onClick:()=>m(s.name.toLowerCase().replace(" ","-")),size:"sm",className:"whitespace-nowrap",children:[s.icon," ",s.name," (",s.count,")"]},s.name))]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(s=>e.jsx(A,{agent:s,isInstalled:k.has(s.id),onInstall:()=>S(s.id)},s.id))}),N.length===0&&e.jsx(c,{className:"text-center py-12",children:e.jsxs(o,{children:[e.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No agents found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search terms or category filters"}),e.jsx(r,{onClick:()=>{f(""),m("all")},children:"Clear Filters"})]})}),e.jsxs(c,{className:"mt-12 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200",children:[e.jsx(w,{children:e.jsx(v,{className:"text-indigo-800 flex items-center",children:"Community & Development"})}),e.jsx(o,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl mb-2"}),e.jsx("h4",{className:"font-semibold mb-1",children:"Publish Your Agent"}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Share your marketing automation with the community"}),e.jsx(r,{size:"sm",variant:"outline",children:"Get Started"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl mb-2",children:"💡"}),e.jsx("h4",{className:"font-semibold mb-1",children:"Request an Agent"}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Need a specific marketing automation? Let us know!"}),e.jsx(r,{size:"sm",variant:"outline",children:"Submit Request"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl mb-2",children:"📚"}),e.jsx("h4",{className:"font-semibold mb-1",children:"Developer Docs"}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Learn how to build powerful marketing agents"}),e.jsx(r,{size:"sm",variant:"outline",children:"Read Docs"})]})]})})]})]})},A=({agent:t,isInstalled:p,onInstall:g})=>{const[h,n]=i.useState(!1),m=async()=>{n(!0),await g(),n(!1)};return e.jsxs(c,{className:"hover:shadow-lg transition-shadow duration-200",children:[e.jsx(w,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-3xl",children:t.icon}),e.jsxs("div",{children:[e.jsx(v,{className:"text-lg",children:t.name}),e.jsxs("p",{className:"text-sm text-gray-500",children:["by ",t.author]})]})]}),e.jsxs("div",{className:"flex flex-col items-end space-y-1",children:[t.price==="Free"?e.jsx(u,{className:"bg-green-100 text-green-800",children:"Free"}):e.jsx(u,{className:"bg-blue-100 text-blue-800",children:t.price}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsx("span",{className:"text-yellow-400 mr-1",children:"⭐"}),t.rating.toFixed(1)]})]})]})}),e.jsxs(o,{children:[e.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:t.description}),e.jsx("div",{className:"flex flex-wrap gap-1 mb-4",children:t.tags.map(d=>e.jsx(u,{variant:"secondary",className:"text-xs",children:d},d))}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-4",children:[e.jsxs("span",{children:["📦 ",t.downloads.toLocaleString()," downloads"]}),e.jsxs("span",{children:["📂 ",t.category]})]}),e.jsxs("div",{className:"space-y-2",children:[p?e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(r,{size:"sm",className:"flex-1 bg-green-600 hover:bg-green-700",disabled:!0,children:"Installed"}),e.jsx(r,{size:"sm",variant:"outline",onClick:()=>window.location.href=`/agents/${t.id}/configure`,children:"Configure"})]}):e.jsx(r,{size:"sm",className:"w-full",onClick:m,disabled:h,children:h?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Installing..."]}):"Install Agent"}),e.jsx(r,{size:"sm",variant:"outline",className:"w-full",onClick:()=>window.open(`/agents/${t.id}/details`,"_blank"),children:"View Details"})]}),e.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:e.jsxs("p",{className:"text-xs text-gray-500",children:["💻 Compatible with ",t.compatibility]})})]})]})};export{L as default};
