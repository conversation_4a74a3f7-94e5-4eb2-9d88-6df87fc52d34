import{k as d,r as x,j as e,a0 as m,m as t,C as l,a1 as n,g as r,i as o,a as h,D as u,E as j,f as c,X as p,a2 as N}from"./index-CVpabiD8.js";import{C as g}from"./calendar-DZH_gkxY.js";import{C as b}from"./clock-DLEz2ATr.js";const P=()=>{const{user:v}=d(),[s,y]=x.useState({currentPlan:"Pro",monthlySpend:89,nextBilling:"2025-08-01",usage:{sequences:45,limit:75,overages:0},invoices:[{id:"inv_001",date:"2025-07-01",amount:89,status:"paid",description:"Pro Plan - July 2025"},{id:"inv_002",date:"2025-06-01",amount:89,status:"paid",description:"Pro Plan - June 2025"}],paymentMethod:{type:"card",last4:"4242",brand:"Visa",expiry:"12/27"}}),i=s.usage.sequences/s.usage.limit*100;return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-neutral-950 to-neutral-900",children:[e.jsx(m,{}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pt-24 pb-8",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:e.jsx("span",{className:"gradient-text",children:"Billing & Usage"})}),e.jsx("p",{className:"text-xl text-neutral-300",children:"Manage your subscription, usage, and payment methods"})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24",children:e.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Current Plan"}),e.jsxs("p",{className:"text-neutral-300",children:[s.currentPlan," Subscription"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.monthlySpend]}),e.jsx("div",{className:"text-sm text-neutral-400",children:"per month"})]})]}),e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx(l,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-neutral-300",children:"Active subscription"}),e.jsx("span",{className:"px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm",children:"Auto-renew enabled"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-neutral-400 mb-1",children:"Next billing date"}),e.jsxs("div",{className:"text-white font-semibold flex items-center gap-2",children:[e.jsx(g,{className:"w-4 h-4"}),s.nextBilling]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-neutral-400 mb-1",children:"Payment method"}),e.jsxs("div",{className:"text-white font-semibold flex items-center gap-2",children:[e.jsx(n,{className:"w-4 h-4"}),"•••• ",s.paymentMethod.last4]})]})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-neutral-700 flex gap-3",children:[e.jsxs("button",{className:"btn btn-outline flex items-center gap-2",children:[e.jsx(r,{className:"w-4 h-4"}),"Manage Plan"]}),e.jsxs("button",{className:"btn btn-ghost flex items-center gap-2",children:[e.jsx(n,{className:"w-4 h-4"}),"Update Payment"]})]})]}),e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},className:"card-premium",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"Current Usage"}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-neutral-300",children:"Email Sequences"}),e.jsxs("span",{className:"text-white font-semibold",children:[s.usage.sequences," / ",s.usage.limit]})]}),e.jsx("div",{className:"w-full bg-neutral-700 rounded-full h-3",children:e.jsx(t.div,{className:"h-3 rounded-full bg-gradient-to-r from-purple-500 to-amber-400",initial:{width:0},animate:{width:`${i}%`},transition:{delay:.5,duration:1}})}),e.jsxs("div",{className:"text-sm text-neutral-400 mt-1",children:[Math.round(i),"% of monthly allocation used"]})]}),s.usage.overages>0&&e.jsx("div",{className:"p-4 bg-amber-500/10 border border-amber-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(o,{className:"w-5 h-5 text-amber-400 mt-0.5"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-amber-300 font-medium",children:"Overage Usage"}),e.jsxs("div",{className:"text-sm text-amber-200 mt-1",children:["You have ",s.usage.overages," overage sequences this month. Additional charges will apply."]})]})]})}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-4 mt-6",children:[e.jsxs("div",{className:"text-center p-4 bg-neutral-800/50 rounded-lg",children:[e.jsx(h,{className:"w-6 h-6 text-green-400 mx-auto mb-2"}),e.jsx("div",{className:"text-lg font-bold text-white",children:"+23%"}),e.jsx("div",{className:"text-sm text-neutral-400",children:"vs last month"})]}),e.jsxs("div",{className:"text-center p-4 bg-neutral-800/50 rounded-lg",children:[e.jsx(b,{className:"w-6 h-6 text-blue-400 mx-auto mb-2"}),e.jsx("div",{className:"text-lg font-bold text-white",children:"12 days"}),e.jsx("div",{className:"text-sm text-neutral-400",children:"until reset"})]}),e.jsxs("div",{className:"text-center p-4 bg-neutral-800/50 rounded-lg",children:[e.jsx(u,{className:"w-6 h-6 text-purple-400 mx-auto mb-2"}),e.jsx("div",{className:"text-lg font-bold text-white",children:"$0"}),e.jsx("div",{className:"text-sm text-neutral-400",children:"overages"})]})]})]}),e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Recent Invoices"}),e.jsx("button",{className:"text-purple-400 hover:text-purple-300 text-sm font-medium",children:"View All"})]}),e.jsx("div",{className:"space-y-4",children:s.invoices.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-neutral-800/30 rounded-lg hover:bg-neutral-800/50 transition-colors",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:a.description}),e.jsx("div",{className:"text-sm text-neutral-400",children:a.date})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-semibold",children:["$",a.amount]}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${a.status==="paid"?"bg-green-500/20 text-green-300":"bg-yellow-500/20 text-yellow-300"}`,children:a.status})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-2 hover:bg-neutral-700 rounded-lg transition-colors",children:e.jsx(j,{className:"w-4 h-4 text-neutral-400"})}),e.jsx("button",{className:"p-2 hover:bg-neutral-700 rounded-lg transition-colors",children:e.jsx(c,{className:"w-4 h-4 text-neutral-400"})})]})]})]},a.id))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},className:"card-premium",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full btn btn-primary justify-between",children:["Upgrade Plan",e.jsx(p,{className:"w-4 h-4"})]}),e.jsxs("button",{className:"w-full btn btn-outline justify-between",children:["Download Invoice",e.jsx(c,{className:"w-4 h-4"})]}),e.jsxs("button",{className:"w-full btn btn-ghost justify-between",children:["Update Billing",e.jsx(r,{className:"w-4 h-4"})]})]})]}),e.jsxs(t.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.6},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(N,{className:"w-5 h-5 text-green-400"}),e.jsx("h3",{className:"text-lg font-bold text-white",children:"Security"})]}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-neutral-300",children:"Payment Security"}),e.jsx(l,{className:"w-4 h-4 text-green-400"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-neutral-300",children:"Data Encryption"}),e.jsx(l,{className:"w-4 h-4 text-green-400"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-neutral-300",children:"SOC 2 Compliant"}),e.jsx(l,{className:"w-4 h-4 text-green-400"})]})]})]}),e.jsxs(t.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.7},className:"card-premium",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Need Help?"}),e.jsx("p",{className:"text-neutral-300 text-sm mb-4",children:"Our billing team is here to help with any questions about your account."}),e.jsx("button",{className:"w-full btn btn-outline",children:"Contact Support"})]})]})]})})]})]})};export{P as default};
