import{Q as I,r as l,j as e,m as t,g as P,f as R,P as C,B as x,a as d,d as p,w as u,h as $,M as b,T as E,D as U,W as D,L as y,X as h,Y as k,S as j,y as q}from"./index-CVpabiD8.js";import{T as H}from"./premium-theme-B_OEEgUJ.js";import{P as L}from"./pause-CSJHOzUL.js";import{U as M}from"./users-f__6LX8a.js";import{P as B}from"./palette-BJ27JRb6.js";import{L as O}from"./languages-B8P6yi0c.js";import{T as G}from"./trophy-BLwcuC8U.js";const ae=({user:K})=>{const{theme:W,isDark:F}=I(),[c,N]=l.useState("overview"),[m,f]=l.useState(!1),[Q,V]=l.useState("7d"),[a,Y]=l.useState({sequences:{value:2847,change:23,trend:"up"},engagement:{value:67.3,change:5.2,trend:"up"},revenue:{value:45200,change:12.8,trend:"up"},aiScore:{value:94,change:8,trend:"up"},activeUsers:{value:1.2,change:15.3,trend:"up"},conversionRate:{value:4.8,change:.8,trend:"up"}}),[w]=l.useState([{type:"sequence",user:"Sarah Chen",action:"Generated welcome sequence",time:"2 min ago",status:"success"},{type:"translation",user:"Marcus Rodriguez",action:"Translated to Spanish",time:"5 min ago",status:"success"},{type:"optimization",user:"Emma Thompson",action:"Optimized subject line",time:"8 min ago",status:"success"},{type:"analysis",user:"David Kim",action:"Analyzed competitor email",time:"12 min ago",status:"success"},{type:"prediction",user:"Lisa Wang",action:"Predicted performance",time:"15 min ago",status:"success"}]),[A]=l.useState([{icon:"",text:"Tuesday 10 AM shows 34% higher engagement for your audience",priority:"high"},{icon:"",text:"Adding urgency in email 3 increases conversions by 23%",priority:"medium"},{icon:"",text:"Your storytelling approach resonates best with tech audiences",priority:"medium"},{icon:"",text:"Subject lines with numbers perform 41% better",priority:"low"}]),z=[{id:"overview",label:"Overview",icon:x},{id:"analytics",label:"Analytics",icon:d},{id:"insights",label:"AI Insights",icon:p},{id:"activity",label:"Activity",icon:u}],n=({title:s,value:i,change:g,trend:v,icon:S,format:o="number",color:r="primary"})=>e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"card-premium relative overflow-hidden group",children:[e.jsx("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500",children:e.jsx("div",{className:`w-full h-full bg-gradient-to-br ${r==="primary"?"from-blue-500 to-purple-600":r==="success"?"from-green-500 to-emerald-600":r==="warning"?"from-yellow-500 to-orange-600":"from-purple-500 to-pink-600"}`})}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r==="primary"?"from-blue-500/20 to-purple-600/20":r==="success"?"from-green-500/20 to-emerald-600/20":r==="warning"?"from-yellow-500/20 to-orange-600/20":"from-purple-500/20 to-pink-600/20"}`,children:e.jsx(S,{size:24,className:`${r==="primary"?"text-blue-500":r==="success"?"text-green-500":r==="warning"?"text-yellow-500":"text-purple-500"}`})}),e.jsxs("div",{className:`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${v==="up"?"bg-success-bg text-success":"bg-error-bg text-error"}`,children:[e.jsx(d,{size:12,className:v==="down"?"rotate-180":""}),g>0?"+":"",g,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-3xl font-bold text-primary",children:[o==="currency"?"$":"",o==="percentage"?`${i}%`:o==="currency"?`${(i/1e3).toFixed(1)}K`:o==="decimal"?`${i}K`:i.toLocaleString()]}),e.jsx("p",{className:"text-sm text-secondary",children:s})]}),e.jsx(t.div,{className:"absolute -top-2 -right-2 w-4 h-4 rounded-full bg-blue-500 opacity-75",animate:{scale:[1,1.5,1],opacity:[.7,.3,.7]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}})]})]}),T=({item:s,index:i})=>e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:i*.1},className:"flex items-center gap-4 p-4 rounded-xl hover:bg-surface-hover transition-colors group",children:[e.jsxs("div",{className:`p-2 rounded-lg ${s.type==="sequence"?"bg-blue-500/20 text-blue-500":s.type==="translation"?"bg-green-500/20 text-green-500":s.type==="optimization"?"bg-purple-500/20 text-purple-500":s.type==="analysis"?"bg-orange-500/20 text-orange-500":"bg-cyan-500/20 text-cyan-500"}`,children:[s.type==="sequence"&&e.jsx(b,{size:16}),s.type==="translation"&&e.jsx(O,{size:16}),s.type==="optimization"&&e.jsx(j,{size:16}),s.type==="analysis"&&e.jsx(G,{size:16}),s.type==="prediction"&&e.jsx(x,{size:16})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-primary truncate",children:s.user}),e.jsx("p",{className:"text-xs text-secondary",children:s.action})]}),e.jsx("div",{className:"text-xs text-tertiary",children:s.time}),e.jsx(q,{size:16,className:"text-tertiary opacity-0 group-hover:opacity-100 transition-opacity"})]});return e.jsxs("div",{className:"min-h-screen bg-primary text-primary transition-colors duration-300",children:[e.jsxs("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[e.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}})]}),e.jsx("div",{className:"fixed top-6 right-6 z-50",children:e.jsx(H,{})}),e.jsxs("div",{className:"relative z-10 container py-8",children:[e.jsxs(t.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-6xl font-bold text-holographic mb-2",children:"NeuroColony"}),e.jsx("p",{className:"text-lg text-secondary",children:"Ultra-Premium AI Email Intelligence Platform"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(t.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn btn-ghost",children:e.jsx(P,{size:20})}),e.jsxs(t.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn btn-ghost",children:[e.jsx(R,{size:20}),"Export"]}),e.jsxs(t.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f(!m),className:"btn btn-primary",children:[m?e.jsx(L,{size:20}):e.jsx(C,{size:20}),m?"Pause":"Live Mode"]})]})]}),e.jsx("div",{className:"glass p-2 rounded-2xl inline-flex",children:z.map(s=>e.jsxs(t.button,{onClick:()=>N(s.id),className:`
                  flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-sm transition-all
                  ${c===s.id?"bg-gradient-primary text-white shadow-glow":"text-secondary hover:text-primary hover:bg-surface-hover"}
                `,whileHover:{scale:1.02},whileTap:{scale:.98},children:[e.jsx(s.icon,{size:18}),s.label]},s.id))})]}),e.jsxs($,{mode:"wait",children:[c==="overview"&&e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsx(n,{title:"Email Sequences Generated",value:a.sequences.value,change:a.sequences.change,trend:a.sequences.trend,icon:b,color:"primary"}),e.jsx(n,{title:"Average Engagement Rate",value:a.engagement.value,change:a.engagement.change,trend:a.engagement.trend,icon:E,format:"percentage",color:"success"}),e.jsx(n,{title:"Revenue Generated",value:a.revenue.value,change:a.revenue.change,trend:a.revenue.trend,icon:U,format:"currency",color:"warning"}),e.jsx(n,{title:"AI Quality Score",value:a.aiScore.value,change:a.aiScore.change,trend:a.aiScore.trend,icon:p,color:"purple"}),e.jsx(n,{title:"Active Users",value:a.activeUsers.value,change:a.activeUsers.change,trend:a.activeUsers.trend,icon:M,format:"decimal",color:"success"}),e.jsx(n,{title:"Conversion Rate",value:a.conversionRate.value,change:a.conversionRate.change,trend:a.conversionRate.trend,icon:d,format:"percentage",color:"primary"})]}),e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("h3",{className:"text-2xl font-bold text-primary mb-6 flex items-center gap-3",children:[e.jsx(D,{className:"text-yellow-500"}),"Enterprise Features"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsx(y,{to:"/white-label",className:"group",children:e.jsxs(t.div,{whileHover:{scale:1.02,y:-5},className:"card-premium group-hover:border-purple-500/50 transition-all duration-300",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-600/20",children:e.jsx(B,{className:"h-6 w-6 text-purple-500"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold text-primary",children:"White-Label Platform"}),e.jsx("p",{className:"text-sm text-secondary",children:"Custom branding & domains"})]})]}),e.jsx("p",{className:"text-xs text-tertiary mb-3",children:"Transform NeuroColony into your own branded platform with complete customization"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full",children:"Enterprise"}),e.jsx(h,{className:"h-4 w-4 text-purple-500 opacity-0 group-hover:opacity-100 transition-opacity"})]})]})}),e.jsx(y,{to:"/revenue-optimizer",className:"group",children:e.jsxs(t.div,{whileHover:{scale:1.02,y:-5},className:"card-premium group-hover:border-green-500/50 transition-all duration-300",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-600/20",children:e.jsx(d,{className:"h-6 w-6 text-green-500"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold text-primary",children:"Revenue Optimizer"}),e.jsx("p",{className:"text-sm text-secondary",children:"AI-powered growth insights"})]})]}),e.jsx("p",{className:"text-xs text-tertiary mb-3",children:"Advanced AI analyzes your metrics to suggest revenue optimization strategies"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full",children:"AI-Powered"}),e.jsx(h,{className:"h-4 w-4 text-green-500 opacity-0 group-hover:opacity-100 transition-opacity"})]})]})}),e.jsx(y,{to:"/enterprise-analytics",className:"group",children:e.jsxs(t.div,{whileHover:{scale:1.02,y:-5},className:"card-premium group-hover:border-blue-500/50 transition-all duration-300",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-600/20",children:e.jsx(k,{className:"h-6 w-6 text-blue-500"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold text-primary",children:"Enterprise Analytics"}),e.jsx("p",{className:"text-sm text-secondary",children:"Advanced business intelligence"})]})]}),e.jsx("p",{className:"text-xs text-tertiary mb-3",children:"Comprehensive analytics dashboard with custom reporting and team insights"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full",children:"Business Intel"}),e.jsx(h,{className:"h-4 w-4 text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity"})]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-2xl font-bold text-primary flex items-center gap-3",children:[e.jsx(j,{className:"text-yellow-500"}),"AI Insights"]}),e.jsx(t.button,{whileHover:{scale:1.05},className:"btn btn-ghost btn-sm",children:"View All"})]}),e.jsx("div",{className:"space-y-4",children:A.map((s,i)=>e.jsx(t.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:i*.1},className:`
                          p-4 rounded-xl border-l-4 transition-all hover:bg-surface-hover
                          ${s.priority==="high"?"border-red-500 bg-red-500/5":s.priority==="medium"?"border-yellow-500 bg-yellow-500/5":"border-green-500 bg-green-500/5"}
                        `,children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-2xl",children:s.icon}),e.jsx("p",{className:"text-sm text-secondary flex-1",children:s.text}),e.jsx("span",{className:`
                            px-2 py-1 rounded-full text-xs font-medium
                            ${s.priority==="high"?"bg-red-100 text-red-700":s.priority==="medium"?"bg-yellow-100 text-yellow-700":"bg-green-100 text-green-700"}
                          `,children:s.priority})]})},i))})]}),e.jsxs(t.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"card-premium",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-2xl font-bold text-primary flex items-center gap-3",children:[e.jsx(u,{className:"text-green-500"}),"Recent Activity"]}),e.jsx(t.button,{whileHover:{scale:1.05},className:"btn btn-ghost btn-sm",children:"View All"})]}),e.jsx("div",{className:"space-y-2",children:w.map((s,i)=>e.jsx(T,{item:s,index:i},i))})]})]})]},"overview"),c==="analytics"&&e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"card-premium text-center py-16",children:[e.jsx(x,{size:64,className:"mx-auto mb-4 text-purple-500"}),e.jsx("h3",{className:"text-2xl font-bold text-primary mb-2",children:"Advanced Analytics"}),e.jsx("p",{className:"text-secondary",children:"Detailed analytics dashboard coming soon"})]},"analytics"),c==="insights"&&e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"card-premium text-center py-16",children:[e.jsx(p,{size:64,className:"mx-auto mb-4 text-purple-500"}),e.jsx("h3",{className:"text-2xl font-bold text-primary mb-2",children:"AI Insights Engine"}),e.jsx("p",{className:"text-secondary",children:"Deep AI analysis and recommendations"})]},"insights"),c==="activity"&&e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"card-premium text-center py-16",children:[e.jsx(u,{size:64,className:"mx-auto mb-4 text-purple-500"}),e.jsx("h3",{className:"text-2xl font-bold text-primary mb-2",children:"Activity Timeline"}),e.jsx("p",{className:"text-secondary",children:"Complete activity history and logs"})]},"activity")]})]})]})};export{ae as default};
