import{r as i,V as x,j as e,l as s,N as I,m as d,n as E,o as n,p as D,I as g,q as o,s as v,t as F,h as T,v as z,w as $,S as H,x as U,E as W,y as R,z as Q,F as V,u as Y}from"./index-CVpabiD8.js";import{T as _}from"./tag-B82phq9Z.js";import{C as L}from"./copy-WUF2H833.js";const q=({onSelectTemplate:p})=>{const[m,y]=i.useState([]),[u,f]=i.useState([]),[c,j]=i.useState(""),[h,N]=i.useState("all"),[r,b]=i.useState(null),[w,C]=i.useState(!0),P=[{id:"all",name:"All Colony Blueprints",count:12,icon:"colony",color:"neural"},{id:"welcome",name:"Hive Integration",count:2,icon:"hexagon",color:"honey"},{id:"nurture",name:"Neural Training",count:3,icon:"brain",color:"neural"},{id:"sales",name:"Swarm Conversion",count:4,icon:"swarm",color:"swarm"},{id:"retention",name:"Colony Retention",count:2,icon:"shield",color:"neural"},{id:"winback",name:"Reactivation Protocols",count:1,icon:"activate",color:"honey"}];i.useEffect(()=>{A()},[]),i.useEffect(()=>{B()},[c,h,m]);const A=async()=>{try{const a=[{id:"welcome-1",name:"Hive Welcome Protocol",category:"Hive Integration",description:"Initialize new colony members with neural synchronization",tags:["integration","neural-sync","new-member"],successRate:92,deploymentCount:12500,neuralCapabilities:["personalization","behavior-analysis","engagement-tracking"],preview:"Welcome to the {colony_name} Collective! Your neural profile has been initialized...",icon:"hexagon",color:"honey"},{id:"nurture-1",name:"Knowledge Propagation Network",category:"Neural Training",description:"Distribute colony intelligence through neural pathways",tags:["training","knowledge-transfer","neural-growth"],successRate:88,deploymentCount:8900,neuralCapabilities:["adaptive-learning","content-optimization","engagement-prediction"],preview:"Neural Network Update: 73% of colony members have enhanced their {capability}...",icon:"brain",color:"neural"},{id:"sales-1",name:"Swarm Convergence Protocol",category:"Swarm Conversion",description:"Activate collective action through synchronized messaging",tags:["conversion","swarm-intelligence","collective-action"],successRate:96,deploymentCount:34200,neuralCapabilities:["urgency-detection","behavioral-triggers","conversion-optimization"],preview:"SWARM ALERT: The colony collective is converging on {objective}. Time remaining...",icon:"swarm",color:"swarm"},{id:"retention-1",name:"Colony Bond Strengthener",category:"Colony Retention",description:"Reinforce neural connections within the colony network",tags:["retention","loyalty","neural-bonding"],successRate:85,deploymentCount:5600,neuralCapabilities:["sentiment-analysis","loyalty-prediction","personalized-rewards"],preview:"Colony Status Update: Your contribution to {colony_metric} has strengthened the hive...",icon:"shield",color:"neural"},{id:"winback-1",name:"Dormant Node Reactivation",category:"Reactivation Protocols",description:"Reawaken inactive neural nodes in the colony network",tags:["reactivation","win-back","neural-recovery"],successRate:78,deploymentCount:2300,neuralCapabilities:["inactivity-detection","personalized-incentives","re-engagement"],preview:"Neural Reactivation Signal: The colony has evolved since your last connection...",icon:"activate",color:"honey"}];y(a),f(a),C(!1)}catch{x.error("Failed to initialize colony blueprints"),C(!1)}},B=()=>{let a=m;h!=="all"&&(a=a.filter(l=>l.category.toLowerCase().includes(h.toLowerCase()))),c&&(a=a.filter(l=>l.name.toLowerCase().includes(c.toLowerCase())||l.description.toLowerCase().includes(c.toLowerCase())||l.tags.some(t=>t.toLowerCase().includes(c.toLowerCase())))),f(a)},k=a=>{const l=`Colony Blueprint: ${a.name}

${a.preview}`;navigator.clipboard.writeText(l),x.success("Blueprint copied to neural buffer!")},S=a=>{p?p(a):x.success(`Deploying blueprint: ${a.name}`)};return e.jsxs("div",{className:s("min-h-screen relative overflow-x-hidden",V.gradients.neural),children:[e.jsx(I,{className:"opacity-5"}),e.jsxs("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx(E,{size:64,animate:!0,className:"text-neural-600 dark:text-neural-400"})}),e.jsx("h1",{className:s(n("h1"),"mb-4"),children:"Colony Blueprint Archives"}),e.jsx("p",{className:s(n("bodyLarge"),"max-w-3xl mx-auto"),children:"Access our neural network of proven agent workflows. Each blueprint is optimized through collective intelligence for maximum colony efficiency."})]}),e.jsx(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:s(v(!0),"p-6 mb-8"),children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(D,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neural-500 h-5 w-5"}),e.jsx("input",{type:"text",placeholder:"Search colony blueprints...",value:c,onChange:a=>j(a.target.value),className:s("w-full pl-10 pr-4 py-3 rounded-lg","border-2 border-neural-200 dark:border-neural-700","bg-white dark:bg-gray-800","text-gray-800 dark:text-gray-200","placeholder-gray-500 dark:placeholder-gray-400","focus:ring-2 focus:ring-neural-500 focus:border-transparent","transition-all duration-200")})]}),e.jsx("div",{className:"flex gap-2 flex-wrap overflow-x-auto md:overflow-x-visible pb-2",children:P.map(a=>e.jsxs("button",{onClick:()=>N(a.id),className:s("px-4 py-2 rounded-lg font-semibold transition-all duration-200","flex items-center gap-2","min-h-[44px] min-w-[44px]",h===a.id?s(o("primary"),"shadow-lg"):"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-neural-50 dark:hover:bg-gray-700 border-2 border-gray-200 dark:border-gray-600"),children:[e.jsx(g,{name:a.icon,size:18}),a.name," (",a.count,")"]},a.id))})]})}),w?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(F,{size:48,animate:!0,className:"text-neural-600"})}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6",children:e.jsx(T,{mode:"popLayout",children:u.map((a,l)=>e.jsxs(d.div,{layout:!0,initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{delay:l*.05},className:s(v(!0),"overflow-hidden group"),children:[e.jsxs("div",{className:"relative p-6 border-b-2 border-neural-100 dark:border-neural-800",children:[e.jsx("div",{className:"absolute top-0 right-0 p-2 opacity-10 group-hover:opacity-20 transition-opacity",children:e.jsx(g,{name:a.icon,size:120})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:s("p-2 rounded-lg",a.color==="neural"&&"bg-neural-100 dark:bg-neural-900",a.color==="honey"&&"bg-honey-100 dark:bg-honey-900",a.color==="swarm"&&"bg-swarm-100 dark:bg-swarm-900"),children:e.jsx(g,{name:a.icon,size:24})}),e.jsxs("div",{children:[e.jsx("h3",{className:s(n("h5"),"mb-1"),children:a.name}),e.jsx("p",{className:s(z(a.color),"text-xs inline-block"),children:a.category})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx($,{className:"h-4 w-4 text-swarm-500"}),e.jsxs("span",{className:s(n("meta"),"font-bold"),children:[a.successRate,"%"]})]})]}),e.jsx("p",{className:n("body"),children:a.description}),e.jsxs("div",{className:"mt-4 mb-4",children:[e.jsx("p",{className:s(n("metaSmall"),"mb-2 font-semibold"),children:"Neural Capabilities:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:a.neuralCapabilities.map(t=>e.jsxs("span",{className:s("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium","bg-neural-100 dark:bg-neural-900 text-neural-700 dark:text-neural-300","border border-neural-200 dark:border-neural-700"),children:[e.jsx(H,{className:"h-3 w-3 mr-1"}),t]},t))})]}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:a.tags.map(t=>e.jsxs("span",{className:s(z("gray"),"text-xs"),children:[e.jsx(_,{className:"h-3 w-3 mr-1 inline"}),t]},t))}),e.jsxs("div",{className:n("meta"),children:[e.jsx(U,{className:"h-3 w-3 inline mr-1"}),"Deployed ",a.deploymentCount.toLocaleString()," times"]})]})]}),e.jsxs("div",{className:"p-6 bg-gradient-to-br from-gray-50 to-neural-50 dark:from-gray-800 dark:to-neural-950",children:[e.jsxs("p",{className:s(n("bodySmall"),"italic mb-4 line-clamp-3"),children:['"',a.preview,'"']}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>b(a),className:s(o("secondary"),"flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center","min-h-[44px]"),children:[e.jsx(W,{className:"h-4 w-4 mr-1"}),"Analyze"]}),e.jsxs("button",{onClick:()=>k(a),className:s(o("secondary"),"flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center","min-h-[44px]"),children:[e.jsx(L,{className:"h-4 w-4 mr-1"}),"Clone"]}),e.jsxs("button",{onClick:()=>S(a),className:s(o("primary"),"flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center group","min-h-[44px]"),children:["Deploy",e.jsx(R,{className:"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform"})]})]})]})]},a.id))})}),!w&&u.length===0&&e.jsxs(d.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[e.jsx(Q,{size:64,className:"mx-auto mb-6 text-neural-300 dark:text-neural-700"}),e.jsx("p",{className:s(n("h5"),"mb-4"),children:"No colony blueprints match your search parameters"}),e.jsx("button",{onClick:()=>{j(""),N("all")},className:o("secondary"),children:"Reset Neural Filters"})]})]}),e.jsx(T,{children:r&&e.jsx(d.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50",onClick:()=>b(null),children:e.jsxs(d.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:s(v(!1),"max-w-2xl w-full max-h-[90vh] overflow-y-auto"),onClick:a=>a.stopPropagation(),children:[e.jsx("div",{className:"p-6 border-b-2 border-neural-100 dark:border-neural-800",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:s("p-3 rounded-lg",r.color==="neural"&&"bg-neural-100 dark:bg-neural-900",r.color==="honey"&&"bg-honey-100 dark:bg-honey-900",r.color==="swarm"&&"bg-swarm-100 dark:bg-swarm-900"),children:e.jsx(g,{name:r.icon,size:32})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:n("h3"),children:r.name}),e.jsxs("p",{className:s(n("body"),"mt-1"),children:[r.category," • ",r.successRate,"% success rate"]})]})]})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:s("rounded-lg p-6 mb-6","bg-gradient-to-br from-gray-50 to-neural-50 dark:from-gray-800 dark:to-neural-950","border-2 border-neural-200 dark:border-neural-700"),children:[e.jsx("h3",{className:s(n("h5"),"mb-3"),children:"Neural Blueprint Preview"}),e.jsx("p",{className:n("body"),children:r.preview})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:s(n("h5"),"mb-3"),children:"Neural Capabilities"}),e.jsx("div",{className:"grid grid-cols-1 gap-3",children:r.neuralCapabilities.map(a=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-neural-500 rounded-full animate-pulse"}),e.jsx("span",{className:n("body"),children:a})]},a))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>k(r),className:s(o("secondary"),"flex-1 px-4 py-3 rounded-lg flex items-center justify-center"),children:[e.jsx(L,{className:"h-5 w-5 mr-2"}),"Clone Blueprint"]}),e.jsxs("button",{onClick:()=>{S(r),b(null)},className:s(o("primary"),"flex-1 px-4 py-3 rounded-lg flex items-center justify-center group"),children:["Deploy to Colony",e.jsx(R,{className:"h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform"})]})]})]})]})})})]})},O=({user:p})=>{const m=Y(),y=u=>{if(!p){x.error("Please sign in to deploy agent colonies"),m("/login");return}localStorage.setItem("selectedAgentTemplate",JSON.stringify(u)),x.success(`Colony template "${u.name}" ready for deployment!`),m("/colony-command")};return e.jsx(q,{onSelectTemplate:y})};export{O as default};
