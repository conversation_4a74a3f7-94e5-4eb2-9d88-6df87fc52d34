import{r as c,j as e,l as a,t as B,o as t,F as f,N as E,m as r,n as H,I as n,s as l,q as p,a6 as w,z as P,h as O,v as $,Z as F,a2 as M}from"./index-CVpabiD8.js";const L=()=>{const[o,b]=c.useState({}),[j,k]=c.useState([]),[m,z]=c.useState({}),[S,A]=c.useState(!0),[x,C]=c.useState("all");c.useEffect(()=>{I(),u(),y();const s=setInterval(()=>{u(),y()},5e3);return()=>clearInterval(s)},[]);const I=async()=>{try{const i=await(await fetch("/api/agents",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();if(i.success){const h=i.data.agents.reduce((d,v)=>{const g=v.category||"uncategorized";return d[g]||(d[g]=[]),d[g].push(v),d},{});b(h)}}catch(s){console.error("Failed to fetch agents:",s)}finally{A(!1)}},y=async()=>{try{const i=await(await fetch("/api/colony/status",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();i.success&&z({agentsByType:i.data.agentsByType||[],performance:i.data.performance||[],activeWorkflows:i.data.activeWorkflows||0,totalExecutions:i.data.totalExecutions||0,successRate:i.data.successRate||0})}catch(s){console.error("Failed to fetch colony status:",s)}},u=async()=>{try{const i=await(await fetch("/api/agents/executions/running",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();i.success&&k(i.data.executions)}catch(s){console.error("Failed to fetch running executions:",s)}},R=async(s,i={})=>{try{(await(await fetch(`/api/agents/${s}/execute`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({inputs:i})})).json()).success&&u()}catch(h){console.error("Failed to execute agent:",h)}},N=x==="all"?Object.values(o).flat():o[x]||[],T=[{id:"all",name:"All Agents",icon:"colony",color:"neural"},{id:"marketing",name:"Marketing Swarm",icon:"swarm",color:"honey"},{id:"analytics",name:"Analytics Hive",icon:"analyze",color:"neural"},{id:"optimization",name:"Optimization Colony",icon:"optimize",color:"swarm"},{id:"automation",name:"Automation Network",icon:"network",color:"neural"}];return S?e.jsx("div",{className:a("min-h-screen flex items-center justify-center",f.gradients.neural),children:e.jsxs("div",{className:"text-center",children:[e.jsx(B,{size:48,animate:!0,className:"mx-auto mb-4"}),e.jsx("p",{className:t("h5"),children:"Initializing Neural Colony..."})]})}):e.jsxs("div",{className:a("min-h-screen relative",f.gradients.neural),children:[e.jsx(E,{className:"opacity-5"}),e.jsxs("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(H,{size:56,animate:!0}),e.jsxs("div",{children:[e.jsx("h1",{className:a(t("h1"),"flex items-center gap-3"),children:"Neural Colony Command"}),e.jsx("p",{className:t("bodyLarge"),children:"Monitor and orchestrate your AI agent swarms"})]})]}),e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsx(r.div,{whileHover:{scale:1.05},className:a(l(),"px-6 py-4"),children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(n,{name:"statusActive",size:24}),e.jsxs("div",{children:[e.jsx("div",{className:a(t("h3"),"text-swarm-600 dark:text-swarm-400"),children:m.activeWorkflows||0}),e.jsx("div",{className:t("meta"),children:"Active Missions"})]})]})}),e.jsx(r.div,{whileHover:{scale:1.05},className:a(l(),"px-6 py-4"),children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(n,{name:"growth",size:24}),e.jsxs("div",{children:[e.jsxs("div",{className:a(t("h3"),"text-neural-600 dark:text-neural-400"),children:[m.successRate||0,"%"]}),e.jsx("div",{className:t("meta"),children:"Success Rate"})]})]})})]})]}),e.jsx("div",{className:"flex gap-3 flex-wrap",children:T.map(s=>e.jsxs(r.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>C(s.id),className:a("px-4 py-2 rounded-lg font-semibold transition-all duration-200","flex items-center gap-2",x===s.id?a(p("primary"),"shadow-lg"):a("bg-white dark:bg-gray-800","text-gray-700 dark:text-gray-300","hover:bg-neural-50 dark:hover:bg-gray-700","border-2 border-gray-300 dark:border-gray-600")),children:[e.jsx(n,{name:s.icon,size:18}),s.name,e.jsx("span",{className:a("ml-2 px-2 py-0.5 rounded-full text-xs font-medium",x===s.id?"bg-white/20 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"),children:s.id==="all"?Object.values(o).flat().length:o[s.id]?.length||0})]},s.id))})]}),j.length>0&&e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("h2",{className:a(t("h4"),"mb-4 flex items-center gap-2"),children:[e.jsx(n,{name:"statusProcessing",size:24}),"Active Neural Processes"]}),e.jsx("div",{className:"grid gap-4",children:j.map((s,i)=>e.jsx(r.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:i*.1},className:a(l(),"p-4"),children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(n,{name:"worker",size:24}),e.jsxs("div",{children:[e.jsx("p",{className:t("h5"),children:s.agentName}),e.jsxs("p",{className:t("meta"),children:["Started ",new Date(s.startTime).toLocaleTimeString()]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:t("meta"),children:"Progress"}),e.jsx("div",{className:"w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-neural-500 to-neural-600 transition-all duration-500",style:{width:`${s.progress||0}%`}})})]}),e.jsxs(w,{variant:"warning",className:"animate-pulse",children:[e.jsx(n,{name:"statusProcessing",size:16}),"Processing"]})]})]})},s.id))})]}),e.jsxs(r.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[e.jsxs("h2",{className:a(t("h4"),"mb-6 flex items-center gap-2"),children:[e.jsx(n,{name:"swarm",size:24}),"Colony Agents"]}),N.length===0?e.jsxs("div",{className:a(l(),"p-12 text-center"),children:[e.jsx(P,{size:64,className:"mx-auto mb-4 text-gray-400"}),e.jsx("p",{className:t("h5"),children:"No agents in this category"}),e.jsx("p",{className:a(t("body"),"mt-2"),children:"Deploy new agents to expand your colony"})]}):e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.jsx(O,{mode:"popLayout",children:N.map((s,i)=>e.jsx(r.div,{layout:!0,initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{delay:i*.05},whileHover:{y:-4},className:a(l(!0),"overflow-hidden"),children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:a("p-3 rounded-lg","bg-gradient-to-br from-neural-100 to-neural-200","dark:from-neural-800 dark:to-neural-900"),children:e.jsx(n,{name:s.icon||"worker",size:24})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:t("h5"),children:s.name}),e.jsx("p",{className:a($("neural"),"text-xs mt-1 inline-block"),children:s.category})]})]}),e.jsxs(w,{variant:s.status==="active"?"success":"default",className:"flex items-center gap-1",children:[e.jsx(n,{name:s.status==="active"?"statusActive":"statusIdle",size:12}),s.status||"Ready"]})]}),e.jsx("p",{className:a(t("body"),"mb-4"),children:s.description}),e.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-4",children:[e.jsxs("div",{className:"text-center p-2 bg-gray-50 dark:bg-gray-800 rounded",children:[e.jsx("p",{className:a(t("h5"),"text-neural-600 dark:text-neural-400"),children:s.executions||0}),e.jsx("p",{className:t("metaSmall"),children:"Runs"})]}),e.jsxs("div",{className:"text-center p-2 bg-gray-50 dark:bg-gray-800 rounded",children:[e.jsxs("p",{className:a(t("h5"),"text-swarm-600 dark:text-swarm-400"),children:[s.successRate||95,"%"]}),e.jsx("p",{className:t("metaSmall"),children:"Success"})]}),e.jsxs("div",{className:"text-center p-2 bg-gray-50 dark:bg-gray-800 rounded",children:[e.jsxs("p",{className:a(t("h5"),"text-honey-600 dark:text-honey-400"),children:[s.avgTime||"2.3","s"]}),e.jsx("p",{className:t("metaSmall"),children:"Avg Time"})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>R(s.id),className:a(p("primary"),"flex-1 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-2"),children:[e.jsx(F,{className:"w-4 h-4"}),"Deploy Agent"]}),e.jsx("button",{className:a(p("secondary"),"px-3 py-2 rounded-lg text-sm"),children:e.jsx(M,{className:"w-4 h-4"})})]})]})},s.id))})})]}),e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-12",children:[e.jsxs("h2",{className:a(t("h4"),"mb-6 flex items-center gap-2"),children:[e.jsx(n,{name:"intelligence",size:24}),"Colony Intelligence Metrics"]}),e.jsxs("div",{className:"grid md:grid-cols-4 gap-6",children:[e.jsxs(r.div,{whileHover:{scale:1.05},className:a(l(),"p-6 text-center"),children:[e.jsx(n,{name:"colony",size:48,className:"mx-auto mb-3"}),e.jsx("p",{className:a(t("h2"),"text-neural-600 dark:text-neural-400"),children:m.totalExecutions||0}),e.jsx("p",{className:t("meta"),children:"Total Missions"})]}),e.jsxs(r.div,{whileHover:{scale:1.05},className:a(l(),"p-6 text-center"),children:[e.jsx(n,{name:"swarm",size:48,className:"mx-auto mb-3"}),e.jsx("p",{className:a(t("h2"),"text-swarm-600 dark:text-swarm-400"),children:Object.values(o).flat().length}),e.jsx("p",{className:t("meta"),children:"Active Agents"})]}),e.jsxs(r.div,{whileHover:{scale:1.05},className:a(l(),"p-6 text-center"),children:[e.jsx(n,{name:"growth",size:48,className:"mx-auto mb-3"}),e.jsxs("p",{className:a(t("h2"),"text-honey-600 dark:text-honey-400"),children:[m.successRate||0,"%"]}),e.jsx("p",{className:t("meta"),children:"Success Rate"})]}),e.jsxs(r.div,{whileHover:{scale:1.05},className:a(l(),"p-6 text-center"),children:[e.jsx(n,{name:"neural",size:48,className:"mx-auto mb-3"}),e.jsx("p",{className:a(t("h2"),"text-neural-600 dark:text-neural-400"),children:"24/7"}),e.jsx("p",{className:t("meta"),children:"Colony Uptime"})]})]})]})]})]})};export{L as default};
