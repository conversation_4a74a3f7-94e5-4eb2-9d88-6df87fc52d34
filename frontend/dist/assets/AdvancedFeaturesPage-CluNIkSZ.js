import{c as C,r as m,j as e,m as g,P as q,M as I,E as U,a as $,B as T,V as o,g as O,Z as k,i as _,C as V,G as W,u as Y,H as Z}from"./index-CVpabiD8.js";import{T as z}from"./trophy-BLwcuC8U.js";import{U as F}from"./users-f__6LX8a.js";import{C as N}from"./calendar-DZH_gkxY.js";import{P}from"./pause-CSJHOzUL.js";import{C as E}from"./clock-DLEz2ATr.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=C("MousePointer",[["path",{d:"m3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z",key:"y2ucgo"}],["path",{d:"m13 13 6 6",key:"1nhxnf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=C("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=C("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=C("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),K=({sequenceId:p,emails:j})=>{const[y,l]=m.useState([]),[b,v]=m.useState(!1),[u,x]=m.useState(0),[f,a]=m.useState(!1),[n,c]=m.useState({testType:"subject_line",variants:[{name:"Original",content:""},{name:"Variant B",content:""}],trafficSplit:50});m.useEffect(()=>{h()},[p]),m.useEffect(()=>{j&&j[u]&&n.testType==="subject_line"&&c(s=>({...s,variants:[{...s.variants[0],content:j[u].subject},s.variants[1]]}))},[u,j,n.testType]);const h=async()=>{try{const s=localStorage.getItem("token"),i=await fetch(`http://localhost:5002/api/sequences/${p}/ab-test/results`,{headers:{Authorization:`Bearer ${s}`}});if(i.ok){const r=await i.json();r.success&&l(r.data)}}catch(s){console.error("Failed to load A/B test results:",s)}},w=async()=>{v(!0);try{const s=localStorage.getItem("token"),r=await(await fetch(`http://localhost:5002/api/sequences/${p}/ab-test/start`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({emailIndex:u,testType:n.testType,variants:n.variants,trafficSplit:n.trafficSplit})})).json();r.success?(o.success("A/B test started successfully!"),a(!1),h()):o.error(r.message||"Failed to start A/B test")}catch{o.error("Failed to start A/B test")}finally{v(!1)}},A=async s=>{try{const i=localStorage.getItem("token"),d=await(await fetch(`http://localhost:5002/api/sequences/${p}/ab-test/stop`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify({emailIndex:s})})).json();d.success?(o.success("A/B test stopped successfully!"),h()):o.error(d.message||"Failed to stop A/B test")}catch{o.error("Failed to stop A/B test")}},D=async(s,i)=>{try{const r=localStorage.getItem("token"),d=Math.floor(Math.random()*50+20),R=Math.floor(d*(.2+Math.random()*.3)),M=Math.floor(R*(.1+Math.random()*.2)),L=Math.floor(M*(.05+Math.random()*.15));(await(await fetch(`http://localhost:5002/api/sequences/${p}/ab-test/update-metrics`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify({emailIndex:s,variantName:i,metrics:{sent:d,opened:R,clicked:M,converted:L}})})).json()).success&&(o.success(`Simulated metrics for ${i}`),h())}catch{o.error("Failed to simulate metrics")}},B=s=>s.winnerDetermined?"text-green-600":new Date-new Date(s.testStartDate)>7*24*60*60*1e3?"text-yellow-600":"text-blue-600",t=s=>s.winnerDetermined?"Completed":new Date-new Date(s.testStartDate)>7*24*60*60*1e3?"Long Running":"Active";return f?e.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Create A/B Test"})]}),e.jsx("button",{onClick:()=>a(!1),className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Email to Test"}),e.jsx("select",{value:u,onChange:s=>x(parseInt(s.target.value)),className:"input-field",children:j?.map((s,i)=>e.jsxs("option",{value:i,children:["Email ",i+1,": ",s.subject?.substring(0,50),"..."]},i))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Type"}),e.jsxs("select",{value:n.testType,onChange:s=>c({...n,testType:s.target.value}),className:"input-field",children:[e.jsx("option",{value:"subject_line",children:"Subject Line"}),e.jsx("option",{value:"email_content",children:"Email Content"}),e.jsx("option",{value:"send_time",children:"Send Time"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Variants"}),e.jsx("div",{className:"space-y-4",children:n.variants.map((s,i)=>e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:s.name}),e.jsx("textarea",{value:s.content,onChange:r=>{const d=[...n.variants];d[i].content=r.target.value,c({...n,variants:d})},className:"input-field",rows:"3",placeholder:`Enter ${n.testType.replace("_"," ")} for ${s.name}`})]},i))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Traffic Split for Variant B (",n.trafficSplit,"%)"]}),e.jsx("input",{type:"range",min:"10",max:"50",value:n.trafficSplit,onChange:s=>c({...n,trafficSplit:parseInt(s.target.value)}),className:"w-full"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsxs("span",{children:["Original: ",100-n.trafficSplit,"%"]}),e.jsxs("span",{children:["Variant B: ",n.trafficSplit,"%"]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:()=>a(!1),className:"btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:w,disabled:b||!n.variants[0].content||!n.variants[1].content,className:"btn-primary flex items-center",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Starting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Start A/B Test"]})})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"A/B Testing"})]}),e.jsxs("button",{onClick:()=>a(!0),className:"btn-primary flex items-center",children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Create A/B Test"]})]}),y.length>0?e.jsx("div",{className:"space-y-4",children:y.map((s,i)=>e.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:i*.1},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(I,{className:"h-5 w-5 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:["Email ",s.emailIndex+1,": ",s.emailSubject?.substring(0,40),"..."]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Testing: ",s.testType.replace("_"," ")]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`text-sm font-medium ${B(s)}`,children:t(s)}),!s.winnerDetermined&&e.jsx("button",{onClick:()=>A(s.emailIndex),className:"text-red-600 hover:text-red-700 p-1",title:"Stop Test",children:e.jsx(H,{className:"h-4 w-4"})})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.variants.map((r,d)=>e.jsxs("div",{className:`p-4 rounded-lg border ${s.winnerDetermined&&s.winningVariant===r.name?"border-green-500 bg-green-50":"border-gray-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("h5",{className:"font-medium text-gray-900 flex items-center",children:[r.name,s.winnerDetermined&&s.winningVariant===r.name&&e.jsx(z,{className:"h-4 w-4 text-green-600 ml-2"})]}),!s.winnerDetermined&&e.jsx("button",{onClick:()=>D(s.emailIndex,r.name),className:"text-xs text-primary-600 hover:text-primary-700",children:"Simulate Data"})]}),e.jsxs("div",{className:"text-sm text-gray-600 mb-3 font-mono bg-gray-50 p-2 rounded",children:[r.content?.substring(0,60),"..."]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{className:"h-3 w-3 text-gray-400 mr-1"}),e.jsxs("span",{children:[r.sentCount," sent"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(U,{className:"h-3 w-3 text-blue-500 mr-1"}),e.jsxs("span",{children:[r.openRate,"% open"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(G,{className:"h-3 w-3 text-green-500 mr-1"}),e.jsxs("span",{children:[r.clickRate,"% click"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"h-3 w-3 text-purple-500 mr-1"}),e.jsxs("span",{children:[r.conversionRate,"% convert"]})]})]})]},d))}),e.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200 flex items-center justify-between text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(N,{className:"h-3 w-3 mr-1"}),"Started: ",new Date(s.testStartDate).toLocaleDateString()]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(T,{className:"h-3 w-3 mr-1"}),"Split: ",100-s.trafficSplit,"% / ",s.trafficSplit,"%"]})]}),s.winnerDetermined&&s.testEndDate&&e.jsxs("span",{className:"flex items-center text-green-600",children:[e.jsx(z,{className:"h-3 w-3 mr-1"}),"Completed: ",new Date(s.testEndDate).toLocaleDateString()]})]})]},i))}):e.jsxs(g.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12 bg-white rounded-lg shadow-lg",children:[e.jsx(S,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No A/B Tests Yet"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Start testing different versions of your emails to improve conversion rates."}),e.jsxs("button",{onClick:()=>a(!0),className:"btn-primary flex items-center mx-auto",children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Create Your First A/B Test"]})]})]})},Q=({sequenceId:p,emails:j,title:y})=>{const[l,b]=m.useState([]),[v,u]=m.useState(!1),[x,f]=m.useState(!1),[a,n]=m.useState({name:"",startDate:"",startTime:"09:00",timezone:"UTC",frequency:"once",repeatInterval:1,repeatUnit:"weeks",endDate:"",audience:{type:"all",segmentId:"",tags:[],criteria:{}},automationTriggers:{onSignup:!1,onPurchase:!1,onBehavior:!1,behaviorType:""},deliverySettings:{respectTimeZone:!0,pauseOnWeekends:!1,maxEmailsPerDay:1,delayBetweenEmails:1440}});m.useEffect(()=>{c()},[p]);const c=async()=>{b([{id:1,name:"Weekly Newsletter Automation",status:"active",startDate:new Date,nextRun:new Date(Date.now()+2*24*60*60*1e3),frequency:"weekly",audience:{type:"segment",count:1250},emailsSent:3420,openRate:24.5,clickRate:3.2,triggers:["onSignup"]},{id:2,name:"New Customer Onboarding",status:"paused",startDate:new Date(Date.now()-7*24*60*60*1e3),nextRun:null,frequency:"trigger",audience:{type:"all",count:890},emailsSent:2150,openRate:31.2,clickRate:8.7,triggers:["onSignup","onPurchase"]}])},h=async()=>{u(!0);try{await new Promise(s=>setTimeout(s,1e3));const t={id:Date.now(),name:a.name,status:"active",startDate:new Date(a.startDate+"T"+a.startTime),nextRun:new Date(a.startDate+"T"+a.startTime),frequency:a.frequency,audience:{type:a.audience.type,count:Math.floor(Math.random()*1e3+100)},emailsSent:0,openRate:0,clickRate:0,triggers:Object.keys(a.automationTriggers).filter(s=>a.automationTriggers[s])};b(s=>[t,...s]),f(!1),o.success("Sequence scheduled successfully!")}catch{o.error("Failed to create schedule")}finally{u(!1)}},w=async t=>{b(s=>s.map(i=>i.id===t?{...i,status:i.status==="active"?"paused":"active"}:i)),o.success("Schedule status updated")},A=t=>{switch(t){case"active":return"text-green-600 bg-green-100";case"paused":return"text-yellow-600 bg-yellow-100";case"completed":return"text-blue-600 bg-blue-100";case"failed":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},D=t=>{switch(t){case"active":return e.jsx(q,{className:"h-3 w-3"});case"paused":return e.jsx(P,{className:"h-3 w-3"});case"completed":return e.jsx(V,{className:"h-3 w-3"});case"failed":return e.jsx(_,{className:"h-3 w-3"});default:return e.jsx(E,{className:"h-3 w-3"})}},B=t=>{if(!t)return"Not scheduled";const i=t-new Date,r=Math.floor(i/(1e3*60*60*24)),d=Math.floor(i%(1e3*60*60*24)/(1e3*60*60));return r>0?`in ${r} day${r>1?"s":""}`:d>0?`in ${d} hour${d>1?"s":""}`:"Soon"};return x?e.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Schedule Sequence"})]}),e.jsx("button",{onClick:()=>f(!1),className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Schedule Name"}),e.jsx("input",{type:"text",value:a.name,onChange:t=>n({...a,name:t.target.value}),className:"input-field",placeholder:"e.g., Weekly Newsletter"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Frequency"}),e.jsxs("select",{value:a.frequency,onChange:t=>n({...a,frequency:t.target.value}),className:"input-field",children:[e.jsx("option",{value:"once",children:"One Time"}),e.jsx("option",{value:"daily",children:"Daily"}),e.jsx("option",{value:"weekly",children:"Weekly"}),e.jsx("option",{value:"monthly",children:"Monthly"}),e.jsx("option",{value:"trigger",children:"Trigger Based"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),e.jsx("input",{type:"date",value:a.startDate,onChange:t=>n({...a,startDate:t.target.value}),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Time"}),e.jsx("input",{type:"time",value:a.startTime,onChange:t=>n({...a,startTime:t.target.value}),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Timezone"}),e.jsxs("select",{value:a.timezone,onChange:t=>n({...a,timezone:t.target.value}),className:"input-field",children:[e.jsx("option",{value:"UTC",children:"UTC"}),e.jsx("option",{value:"America/New_York",children:"Eastern Time"}),e.jsx("option",{value:"America/Chicago",children:"Central Time"}),e.jsx("option",{value:"America/Denver",children:"Mountain Time"}),e.jsx("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]})]}),a.frequency==="trigger"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Automation Triggers"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.automationTriggers.onSignup,onChange:t=>n({...a,automationTriggers:{...a.automationTriggers,onSignup:t.target.checked}}),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"When someone signs up"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.automationTriggers.onPurchase,onChange:t=>n({...a,automationTriggers:{...a.automationTriggers,onPurchase:t.target.checked}}),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"After a purchase"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.automationTriggers.onBehavior,onChange:t=>n({...a,automationTriggers:{...a.automationTriggers,onBehavior:t.target.checked}}),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Based on behavior"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Target Audience"}),e.jsxs("select",{value:a.audience.type,onChange:t=>n({...a,audience:{...a.audience,type:t.target.value}}),className:"input-field",children:[e.jsx("option",{value:"all",children:"All Subscribers"}),e.jsx("option",{value:"segment",children:"Specific Segment"}),e.jsx("option",{value:"tags",children:"By Tags"}),e.jsx("option",{value:"criteria",children:"Custom Criteria"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Delivery Settings"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.deliverySettings.respectTimeZone,onChange:t=>n({...a,deliverySettings:{...a.deliverySettings,respectTimeZone:t.target.checked}}),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Respect subscriber timezone"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.deliverySettings.pauseOnWeekends,onChange:t=>n({...a,deliverySettings:{...a.deliverySettings,pauseOnWeekends:t.target.checked}}),className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Pause on weekends"})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:()=>f(!1),className:"btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:h,disabled:v||!a.name||!a.startDate,className:"btn-primary flex items-center",children:v?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Create Schedule"]})})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Sequence Scheduling"})]}),e.jsxs("button",{onClick:()=>f(!0),className:"btn-primary flex items-center",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Create Schedule"]})]}),l.length>0?e.jsx("div",{className:"space-y-4",children:l.map((t,s)=>e.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${A(t.status)}`,children:[D(t.status),e.jsx("span",{className:"capitalize",children:t.status})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:t.name}),e.jsx("p",{className:"text-sm text-gray-500",children:t.frequency==="trigger"?"Trigger-based":`${t.frequency.charAt(0).toUpperCase()+t.frequency.slice(1)} delivery`})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>w(t.id),className:`p-2 rounded-lg ${t.status==="active"?"text-yellow-600 hover:bg-yellow-50":"text-green-600 hover:bg-green-50"}`,title:t.status==="active"?"Pause":"Resume",children:t.status==="active"?e.jsx(P,{className:"h-4 w-4"}):e.jsx(q,{className:"h-4 w-4"})}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg",children:e.jsx(O,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(F,{className:"h-4 w-4 text-blue-500 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Audience"})]}),e.jsx("span",{className:"text-lg font-bold text-gray-900",children:t.audience.count.toLocaleString()})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(I,{className:"h-4 w-4 text-green-500 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Sent"})]}),e.jsx("span",{className:"text-lg font-bold text-gray-900",children:t.emailsSent.toLocaleString()})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx($,{className:"h-4 w-4 text-purple-500 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Open Rate"})]}),e.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[t.openRate.toFixed(1),"%"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(k,{className:"h-4 w-4 text-orange-500 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Click Rate"})]}),e.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[t.clickRate.toFixed(1),"%"]})]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200 text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-4 text-gray-500",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),"Started: ",t.startDate.toLocaleDateString()]}),t.nextRun&&e.jsxs("span",{className:"flex items-center",children:[e.jsx(J,{className:"h-3 w-3 mr-1"}),"Next: ",B(t.nextRun)]})]}),t.triggers.length>0&&e.jsx("div",{className:"flex items-center space-x-1",children:t.triggers.map((i,r)=>e.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded",children:i.replace("on","").toLowerCase()},r))})]})]},t.id))}):e.jsxs(g.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12 bg-white rounded-lg shadow-lg",children:[e.jsx(N,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Schedules Yet"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Schedule your email sequence to run automatically at the perfect time."}),e.jsxs("button",{onClick:()=>f(!0),className:"btn-primary flex items-center mx-auto",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Create Your First Schedule"]})]})]})},le=({user:p})=>{const{id:j}=W(),y=Y(),[l,b]=m.useState(null),[v,u]=m.useState(!0),[x,f]=m.useState("ab-testing");m.useEffect(()=>{a()},[j]);const a=async()=>{try{const c=localStorage.getItem("token"),h=await fetch(`http://localhost:5002/api/sequences/${j}`,{headers:{Authorization:`Bearer ${c}`}});if(h.ok){const w=await h.json();w.success?b(w.data):(o.error(w.message||"Failed to load sequence"),y("/dashboard"))}else o.error("Failed to load sequence"),y("/dashboard")}catch{o.error("Failed to load sequence"),y("/dashboard")}finally{u(!1)}},n=[{id:"ab-testing",label:"A/B Testing",icon:S,description:"Test different versions to optimize performance"},{id:"scheduling",label:"Scheduling & Automation",icon:N,description:"Automate delivery and set up recurring sends"},{id:"analytics",label:"Advanced Analytics",icon:T,description:"Deep insights and performance tracking"},{id:"optimization",label:"AI Optimization",icon:k,description:"AI-powered suggestions and improvements"}];return v?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-primary-600 border-t-transparent rounded-full mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Loading sequence..."})]})}):l?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[e.jsx("button",{onClick:()=>y("/dashboard"),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:e.jsx(Z,{className:"h-5 w-5"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:l.title}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Advanced Features & Optimization"})]})]}),e.jsx(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsx(F,{className:"h-5 w-5 text-blue-500 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Emails"})]}),e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:l.emails?.length||0})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsx($,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Status"})]}),e.jsx("span",{className:`text-sm font-medium px-2 py-1 rounded-full ${l.status==="active"?"bg-green-100 text-green-800":l.status==="draft"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:l.status?.charAt(0).toUpperCase()+l.status?.slice(1)})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsx(O,{className:"h-5 w-5 text-purple-500 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Industry"})]}),e.jsx("span",{className:"text-sm text-gray-900 capitalize",children:l.businessInfo?.industry})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsx(N,{className:"h-5 w-5 text-orange-500 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Created"})]}),e.jsx("span",{className:"text-sm text-gray-900",children:new Date(l.createdAt).toLocaleDateString()})]})]})})]}),e.jsx("div",{className:"mb-8",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:n.map((c,h)=>e.jsxs(g.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:h*.1},onClick:()=>f(c.id),className:`p-6 rounded-lg text-left transition-all duration-200 ${x===c.id?"bg-primary-600 text-white shadow-lg":"bg-white text-gray-900 hover:bg-gray-50 shadow"}`,children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(c.icon,{className:`h-6 w-6 ${x===c.id?"text-white":"text-primary-600"}`}),e.jsx("h3",{className:"font-semibold",children:c.label})]}),e.jsx("p",{className:`text-sm ${x===c.id?"text-primary-100":"text-gray-600"}`,children:c.description})]},c.id))})}),e.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[x==="ab-testing"&&e.jsx(K,{sequenceId:l._id,emails:l.emails}),x==="scheduling"&&e.jsx(Q,{sequenceId:l._id,emails:l.emails,title:l.title}),x==="analytics"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[e.jsx(T,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Advanced Analytics"})]}),e.jsxs("div",{className:"text-center py-12",children:[e.jsx(T,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Detailed Analytics Coming Soon"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Get deep insights into email performance, subscriber behavior, and conversion funnels."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Heat Maps"}),e.jsx("p",{className:"text-sm text-gray-600",children:"See where subscribers click most"})]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Funnel Analysis"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track conversion at each step"})]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Cohort Reports"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Analyze subscriber segments"})]})]})]})]}),x==="optimization"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[e.jsx(k,{className:"h-6 w-6 text-primary-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"AI Optimization"})]}),e.jsxs("div",{className:"text-center py-12",children:[e.jsx(k,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"AI-Powered Optimization"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Get personalized recommendations to improve your email sequence performance."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Smart Send Times"}),e.jsx("p",{className:"text-sm text-gray-600",children:"AI determines optimal delivery times"})]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Content Optimization"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Suggest improvements for better engagement"})]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Predictive Analytics"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Forecast performance and outcomes"})]})]})]})]})]},x),p?.subscription?.type==="free"&&e.jsx(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-8 bg-gradient-to-r from-primary-600 to-blue-600 rounded-lg shadow-lg p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Unlock Full Advanced Features"}),e.jsx("p",{className:"text-primary-100",children:"Upgrade to Pro to access unlimited A/B tests, advanced scheduling, and AI optimization."})]}),e.jsx("button",{onClick:()=>y("/pricing"),className:"bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Upgrade Now"})]})})]})}):e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-600",children:"Sequence not found"})})})};export{le as default};
