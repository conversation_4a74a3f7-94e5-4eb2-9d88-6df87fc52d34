import{c as i,Q as d,j as e,m as t,h}from"./index-CVpabiD8.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=i("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l=i("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o=i("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),p=({showLabel:r=!1,variant:n="button"})=>{const{theme:a,toggleTheme:c,setTheme:s}=d();return n==="dropdown"?e.jsx("div",{className:"relative",children:e.jsx(t.div,{className:"glass p-2 rounded-xl",whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(t.button,{onClick:()=>s("light"),className:`p-2 rounded-lg transition-all ${a==="light"?"bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg":"text-secondary hover:bg-surface-hover"}`,whileHover:{scale:1.1},whileTap:{scale:.9},children:e.jsx(o,{size:16})}),e.jsx(t.button,{onClick:()=>s("dark"),className:`p-2 rounded-lg transition-all ${a==="dark"?"bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg":"text-secondary hover:bg-surface-hover"}`,whileHover:{scale:1.1},whileTap:{scale:.9},children:e.jsx(l,{size:16})}),e.jsx(t.button,{onClick:()=>s("system"),className:`p-2 rounded-lg transition-all ${a==="system"?"bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg":"text-secondary hover:bg-surface-hover"}`,whileHover:{scale:1.1},whileTap:{scale:.9},children:e.jsx(m,{size:16})})]})})}):e.jsxs(t.button,{onClick:c,className:`
        relative overflow-hidden rounded-full p-3 transition-all duration-300
        glass hover:scale-105 active:scale-95
        ${r?"flex items-center gap-3 px-4":""}
      `,whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Toggle theme",children:[e.jsx("div",{className:"relative w-6 h-6",children:e.jsx(h,{mode:"wait",children:a==="dark"?e.jsx(t.div,{initial:{rotate:-90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:90,opacity:0},transition:{duration:.3},className:"absolute inset-0 flex items-center justify-center",children:e.jsx(l,{size:20,className:"text-purple-400"})},"moon"):e.jsx(t.div,{initial:{rotate:90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:-90,opacity:0},transition:{duration:.3},className:"absolute inset-0 flex items-center justify-center",children:e.jsx(o,{size:20,className:"text-yellow-500"})},"sun")})}),r&&e.jsx("span",{className:"text-sm font-medium text-primary",children:a==="dark"?"Dark Mode":"Light Mode"}),e.jsx(t.div,{className:"absolute inset-0 rounded-full opacity-0",animate:{opacity:a==="dark"?[0,.3,0]:[0,.2,0],scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},style:{background:a==="dark"?"radial-gradient(circle, rgba(147, 51, 234, 0.4) 0%, transparent 70%)":"radial-gradient(circle, rgba(251, 191, 36, 0.4) 0%, transparent 70%)"}})]})};export{p as T};
