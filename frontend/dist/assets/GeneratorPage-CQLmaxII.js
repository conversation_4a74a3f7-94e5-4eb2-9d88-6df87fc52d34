import{c as I,r as x,V as m,j as e,m as b,Z as w,f as T,L as k,g as O,U as A}from"./index-CVpabiD8.js";import{F as y}from"./file-text-Dp_9XYM2.js";import{L as D}from"./loader-2-D2DNDVF4.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=I("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]),c={CSV:"csv",HTML:"html",TXT:"txt",JSON:"json",MAILCHIMP:"mailchimp",KLAVIYO:"klaviyo"},L=t=>{const s=["Email Number","Day Delay","Subject Line","Email Body","Conversion Score"],a=t.emails?.map((o,r)=>[r+1,o.dayDelay||r+1,`"${o.subject?.replace(/"/g,'""')||""}"`,`"${o.body?.replace(/"/g,'""')||""}"`,o.conversionScore||"N/A"])||[];return[s.join(","),...a.map(o=>o.join(","))].join(`
`)},P=t=>{const s=t.emails?.map((a,n)=>`
    <div class="email-item" style="margin-bottom: 40px; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
      <div class="email-header" style="border-bottom: 1px solid #e5e7eb; padding-bottom: 15px; margin-bottom: 15px;">
        <h3 style="margin: 0; color: #1f2937; font-size: 18px;">Email ${n+1} - Day ${a.dayDelay||n+1}</h3>
        <span style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
          Score: ${a.conversionScore||85}
        </span>
      </div>
      <div class="subject-line" style="margin-bottom: 15px;">
        <h4 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 600;">Subject Line:</h4>
        <p style="margin: 0; background: #f9fafb; padding: 12px; border-radius: 4px; font-family: monospace;">
          ${a.subject||""}
        </p>
      </div>
      <div class="email-body">
        <h4 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 600;">Email Body:</h4>
        <div style="background: #f9fafb; padding: 16px; border-radius: 4px; white-space: pre-wrap; line-height: 1.6;">
          ${a.body||""}
        </div>
      </div>
    </div>
  `).join("")||"";return`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t.title||"Email Sequence"} - NeuroColony Export</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #3b82f6;
        }
        .sequence-info {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .export-info {
            font-size: 12px;
            color: #6b7280;
            margin-top: 40px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 style="margin: 0; color: #1f2937;">${t.title||"Email Sequence"}</h1>
        <p style="margin: 10px 0 0 0; color: #6b7280;">Generated by NeuroColony</p>
    </div>
    
    <div class="sequence-info">
        <h2 style="margin: 0 0 15px 0; color: #374151; font-size: 16px;">Sequence Information</h2>
        <p><strong>Title:</strong> ${t.title||"Untitled Sequence"}</p>
        <p><strong>Total Emails:</strong> ${t.emails?.length||0}</p>
        <p><strong>Sequence Type:</strong> ${t.generationSettings?.primaryGoal||"General"}</p>
        <p><strong>Target Audience:</strong> ${t.businessInfo?.targetAudience||"Not specified"}</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
    </div>
    
    <div class="emails-container">
        ${s}
    </div>
    
    <div class="export-info">
        <p>Exported from NeuroColony on ${new Date().toLocaleDateString()}</p>
        <p>Visit sequenceai.com for more AI-powered email sequences</p>
    </div>
</body>
</html>`},q=t=>{const s=[`EMAIL SEQUENCE: ${t.title||"Untitled Sequence"}`,`Generated by NeuroColony on ${new Date().toLocaleDateString()}`,"","SEQUENCE INFORMATION:",`- Total Emails: ${t.emails?.length||0}`,`- Sequence Type: ${t.generationSettings?.primaryGoal||"General"}`,`- Target Audience: ${t.businessInfo?.targetAudience||"Not specified"}`,"","=".repeat(80),""];return t.emails?.forEach((a,n)=>{s.push(`EMAIL ${n+1} - DAY ${a.dayDelay||n+1}`,"-".repeat(40),"",`SUBJECT: ${a.subject||""}`,"","BODY:",a.body||"","",`CONVERSION SCORE: ${a.conversionScore||"N/A"}`,"","=".repeat(80),"")}),s.push("Generated by NeuroColony","Visit sequenceai.com for more AI-powered email sequences"),s.join(`
`)},G=t=>{const s={title:t.title,exportedAt:new Date().toISOString(),exportedBy:"NeuroColony",sequenceInfo:{totalEmails:t.emails?.length||0,sequenceType:t.generationSettings?.primaryGoal||"General",targetAudience:t.businessInfo?.targetAudience||"Not specified",tone:t.generationSettings?.tone||"Professional",industry:t.businessInfo?.industry||"General"},emails:t.emails?.map((a,n)=>({emailNumber:n+1,dayDelay:a.dayDelay||n+1,subject:a.subject||"",body:a.body||"",conversionScore:a.conversionScore||null,metadata:{generatedAt:new Date().toISOString(),framework:a.framework||"AI-Generated"}}))||[]};return JSON.stringify(s,null,2)},R=t=>{const s=t.emails?.map((a,n)=>({type:"automation",recipients:{list_id:"YOUR_LIST_ID"},settings:{subject_line:a.subject||"",title:`${t.title} - Email ${n+1}`,from_name:"YOUR_COMPANY",reply_to:"<EMAIL>"},content:{html:`<div style="font-family: Arial, sans-serif; line-height: 1.6;">${a.body?.replace(/\n/g,"<br>")||""}</div>`,text:a.body||""},delay:{amount:a.dayDelay||n+1,type:"day"}}))||[];return JSON.stringify({automation_name:t.title||"Untitled Sequence",description:"Email sequence generated by NeuroColony",campaigns:s,notes:"Import instructions: Replace YOUR_LIST_ID, YOUR_COMPANY, and <EMAIL> with your actual values"},null,2)},U=t=>{const s=t.emails?.map((a,n)=>({name:`${t.title} - Email ${n+1}`,subject:a.subject||"",content:a.body||"",delay_minutes:(a.dayDelay||n+1)*24*60,channel:"email"}))||[];return JSON.stringify({flow_name:t.title||"Untitled Sequence",description:"Email sequence generated by NeuroColony",trigger:"custom",messages:s,import_note:"Configure trigger and list settings in Klaviyo after import"},null,2)},M=(t,s,a="text/plain")=>{const n=new Blob([t],{type:a}),o=URL.createObjectURL(n),r=document.createElement("a");r.href=o,r.download=s,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(o)},_=(t,s)=>{const a=new Date().toISOString().split("T")[0],n=`${t.title?.replace(/[^a-z0-9]/gi,"_").toLowerCase()||"email_sequence"}_${a}`;let o,r,d;switch(s){case c.CSV:o=L(t),r=`${n}.csv`,d="text/csv";break;case c.HTML:o=P(t),r=`${n}.html`,d="text/html";break;case c.TXT:o=q(t),r=`${n}.txt`,d="text/plain";break;case c.JSON:o=G(t),r=`${n}.json`,d="application/json";break;case c.MAILCHIMP:o=R(t),r=`${n}_mailchimp.json`,d="application/json";break;case c.KLAVIYO:o=U(t),r=`${n}_klaviyo.json`,d="application/json";break;default:throw new Error(`Unsupported export format: ${s}`)}return M(o,r,d),{filename:r,size:o.length}},K=({user:t})=>{const[s,a]=x.useState({title:"",description:"",businessInfo:{industry:"",productService:"",targetAudience:"",pricePoint:"",uniqueSellingProposition:"",mainBenefit:"",painPoint:""},generationSettings:{sequenceLength:7,tone:"professional",primaryGoal:"sales",includeCTA:!0,includePersonalization:!0}}),[n,o]=x.useState(!1),[r,d]=x.useState(null),[F,f]=x.useState(null),[j,v]=x.useState(!1);x.useEffect(()=>{const i=localStorage.getItem("selectedTemplate");if(i)try{const l=JSON.parse(i);f(l),a(S=>({...S,title:l.name+" Email Sequence"})),localStorage.removeItem("selectedTemplate"),m.success(`Using template: ${l.name}`)}catch(l){console.error("Error loading template:",l)}},[]);const u=i=>{try{const l=_(r,i);m.success(`Exported as ${l.filename}`),v(!1)}catch(l){m.error("Export failed: "+l.message)}},$=async i=>{i.preventDefault(),o(!0);try{const l=localStorage.getItem("token"),g=await(await fetch("http://localhost:5002/api/usage/check-generation",{headers:{Authorization:`Bearer ${l}`}})).json();if(!g.success||!g.data.canGenerate){const h=g.data?.stats;if(g.data?.requiresOverageConsent){const E=h?.overageRate||3;if(window.confirm(`You've reached your monthly limit of ${h.sequencesLimit} sequences. Enable overage billing to continue at $${E} per additional sequence?`)){const N=await(await fetch("http://localhost:5002/api/usage/overage-consent",{method:"POST",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}})).json();if(!N.success){m.error(N.message||"Failed to enable overage billing"),o(!1);return}m.success("Overage billing enabled! Continuing with generation...")}else{o(!1);return}}else{m.error(g.data?.message||"Usage limit reached. Please upgrade your plan."),o(!1);return}}const p=await(await fetch("http://localhost:5002/api/sequences/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l}`},body:JSON.stringify(s)})).json();if(p.success){d(p.data);let h="Sequence generated successfully!";p.usage?.isOverage&&(h+=` Overage charge: $${p.usage.overageCharge}`),m.success(h)}else p.data?.requiresOverageConsent?m.error(p.message+" Please enable overage billing to continue."):m.error(p.message||"Generation failed")}catch(l){console.error("Generation error:",l),m.error("Generation failed. Please try again.")}finally{o(!1)}};return r?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"🎉 Your Email Sequence is Ready!"}),e.jsx("p",{className:"text-gray-600",children:"Here's your AI-generated email sequence that's designed to convert."})]}),e.jsx("div",{className:"space-y-6",children:r.emails?.map((i,l)=>e.jsxs("div",{className:"border rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold",children:["Email ",l+1," - Day ",i.dayDelay]}),e.jsxs("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm",children:["Score: ",i.conversionScore||85]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Subject Line:"}),e.jsx("p",{className:"text-gray-700 bg-gray-50 p-3 rounded",children:i.subject})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Email Body:"}),e.jsx("div",{className:"text-gray-700 bg-gray-50 p-4 rounded whitespace-pre-wrap",children:i.body})]})]},l))}),e.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs("button",{onClick:()=>{d(null),f(null),a({title:"",description:"",businessInfo:{industry:"",productService:"",targetAudience:"",pricePoint:"",uniqueSellingProposition:"",mainBenefit:"",painPoint:""},generationSettings:{sequenceLength:7,tone:"professional",primaryGoal:"sales",includeCTA:!0,includePersonalization:!0}})},className:"btn-secondary flex items-center justify-center",children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Generate Another"]}),e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>v(!j),className:"btn-primary flex items-center justify-center",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Export Sequence"]}),j&&e.jsxs("div",{className:"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border py-2 z-10",children:[e.jsx("div",{className:"px-4 py-2 border-b",children:e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Export Options"})}),e.jsxs("div",{className:"py-1",children:[e.jsx("h4",{className:"px-4 py-2 text-xs font-medium text-gray-500 uppercase",children:"General Formats"}),e.jsxs("button",{onClick:()=>u(c.HTML),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(y,{className:"h-4 w-4 mr-3"}),"HTML Document"]}),e.jsxs("button",{onClick:()=>u(c.CSV),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(y,{className:"h-4 w-4 mr-3"}),"CSV Spreadsheet"]}),e.jsxs("button",{onClick:()=>u(c.TXT),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(y,{className:"h-4 w-4 mr-3"}),"Text File"]}),e.jsxs("button",{onClick:()=>u(c.JSON),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(y,{className:"h-4 w-4 mr-3"}),"JSON Data"]})]}),e.jsxs("div",{className:"py-1 border-t",children:[e.jsx("h4",{className:"px-4 py-2 text-xs font-medium text-gray-500 uppercase",children:"Email Platforms"}),e.jsxs("button",{onClick:()=>u(c.MAILCHIMP),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(C,{className:"h-4 w-4 mr-3"}),"Email Platform A"]}),e.jsxs("button",{onClick:()=>u(c.KLAVIYO),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(C,{className:"h-4 w-4 mr-3"}),"Email Platform B"]})]})]})]}),e.jsxs(k,{to:`/advanced/${r._id}`,className:"btn-secondary flex items-center justify-center",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Advanced Features"]}),e.jsxs("button",{onClick:()=>window.open("/templates","_blank"),className:"btn-secondary flex items-center justify-center",children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Browse Templates"]})]})]})})}):e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Generate Your Email Sequence"}),e.jsx("p",{className:"text-xl text-gray-600 mb-6",children:"Tell us about your business and we'll create a high-converting email sequence in seconds."}),t&&e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx(A,{user:t,compact:!0})})]}),e.jsxs(b.form,{onSubmit:$,className:"bg-white rounded-lg shadow-lg p-8 space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Basic Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sequence Title"}),e.jsx("input",{type:"text",required:!0,className:"input-field",placeholder:"e.g., Fitness Coaching Welcome Series",value:s.title,onChange:i=>a({...s,title:i.target.value})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Business Details"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Industry"}),e.jsxs("select",{required:!0,className:"input-field",value:s.businessInfo.industry,onChange:i=>a({...s,businessInfo:{...s.businessInfo,industry:i.target.value}}),children:[e.jsx("option",{value:"",children:"Select Industry"}),e.jsx("option",{value:"fitness",children:"Fitness & Health"}),e.jsx("option",{value:"coaching",children:"Coaching & Consulting"}),e.jsx("option",{value:"ecommerce",children:"E-commerce"}),e.jsx("option",{value:"saas",children:"SaaS & Technology"}),e.jsx("option",{value:"realestate",children:"Real Estate"}),e.jsx("option",{value:"education",children:"Education & Training"}),e.jsx("option",{value:"finance",children:"Finance & Insurance"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price Point"}),e.jsxs("select",{required:!0,className:"input-field",value:s.businessInfo.pricePoint,onChange:i=>a({...s,businessInfo:{...s.businessInfo,pricePoint:i.target.value}}),children:[e.jsx("option",{value:"",children:"Select Price Range"}),e.jsx("option",{value:"under-100",children:"Under $100"}),e.jsx("option",{value:"100-500",children:"$100 - $500"}),e.jsx("option",{value:"500-2000",children:"$500 - $2,000"}),e.jsx("option",{value:"2000-10000",children:"$2,000 - $10,000"}),e.jsx("option",{value:"over-10000",children:"Over $10,000"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product/Service Description"}),e.jsx("textarea",{required:!0,className:"input-field",rows:"3",placeholder:"Describe what you sell and how it helps people...",value:s.businessInfo.productService,onChange:i=>a({...s,businessInfo:{...s.businessInfo,productService:i.target.value}})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Target Audience"}),e.jsx("input",{type:"text",required:!0,className:"input-field",placeholder:"e.g., Busy working moms who want to lose weight",value:s.businessInfo.targetAudience,onChange:i=>a({...s,businessInfo:{...s.businessInfo,targetAudience:i.target.value}})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Sequence Settings"}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Emails"}),e.jsxs("select",{className:"input-field",value:s.generationSettings.sequenceLength,onChange:i=>a({...s,generationSettings:{...s.generationSettings,sequenceLength:parseInt(i.target.value)}}),children:[e.jsx("option",{value:"5",children:"5 Emails"}),e.jsx("option",{value:"7",children:"7 Emails"}),e.jsx("option",{value:"10",children:"10 Emails"}),e.jsx("option",{value:"14",children:"14 Emails"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tone"}),e.jsxs("select",{className:"input-field",value:s.generationSettings.tone,onChange:i=>a({...s,generationSettings:{...s.generationSettings,tone:i.target.value}}),children:[e.jsx("option",{value:"professional",children:"Professional"}),e.jsx("option",{value:"casual",children:"Casual"}),e.jsx("option",{value:"friendly",children:"Friendly"}),e.jsx("option",{value:"authoritative",children:"Authoritative"}),e.jsx("option",{value:"conversational",children:"Conversational"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Primary Goal"}),e.jsxs("select",{className:"input-field",value:s.generationSettings.primaryGoal,onChange:i=>a({...s,generationSettings:{...s.generationSettings,primaryGoal:i.target.value}}),children:[e.jsx("option",{value:"sales",children:"Drive Sales"}),e.jsx("option",{value:"nurture",children:"Nurture Leads"}),e.jsx("option",{value:"onboarding",children:"Onboard Customers"}),e.jsx("option",{value:"retention",children:"Retain Customers"}),e.jsx("option",{value:"upsell",children:"Upsell/Cross-sell"})]})]})]})]}),e.jsx("div",{className:"pt-6",children:e.jsx("button",{type:"submit",disabled:n,className:"btn-primary w-full flex items-center justify-center text-lg py-4",children:n?e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"animate-spin h-5 w-5 mr-2"}),"Generating Your Sequence..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-5 w-5 mr-2"}),"Generate Email Sequence"]})})})]})]})})};export{K as default};
