import{r as a,j as e,a7 as r,g as M,m,a8 as i,aa as c,ab as n,a9 as l,af as j,h as R,W as p,ad as f,a6 as y,w as x,C as E,i as F,B as v,p as P,d as $,T as W,J as q,M as T}from"./index-CVpabiD8.js";import{S as D}from"./Spinner-D3g4Pp_K.js";import{P as N}from"./plus-CLzd-zUl.js";import{C as H}from"./clock-DLEz2ATr.js";import{U as J}from"./users-f__6LX8a.js";const X=()=>{const[o,w]=a.useState(null),[b,C]=a.useState(!0),[h,k]=a.useState("overview"),[L,A]=a.useState([]),[O,S]=a.useState({});a.useEffect(()=>{d(),g();const s=setInterval(()=>{d(),g()},3e4);return()=>clearInterval(s)},[]);const d=async()=>{try{const t=await(await fetch("/api/colony/status",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();t.success&&(w(t.data),S(t.data.performance||{}))}catch(s){console.error("Failed to fetch colony status:",s)}finally{C(!1)}},g=async()=>{try{const t=await(await fetch("/api/colony/agents?limit=10",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).json();t.success&&A(t.data.agents||[])}catch(s){console.error("Failed to fetch recent activity:",s)}},_=async()=>{try{(await(await fetch("/api/colony/create",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({useTemplates:!0,marketingContext:{industry:"general",targetAudience:"small_business",objectives:["lead_generation","email_marketing","conversion_optimization"]}})})).json()).success&&(d(),alert("Marketing colony created successfully!"))}catch(s){console.error("Failed to create colony:",s),alert("Failed to create colony. Please try again.")}},u=s=>({queen:p,worker:J,scout:P})[s]||x,B=s=>({queen:"from-purple-500 to-pink-500",worker:"from-blue-500 to-cyan-500",scout:"from-green-500 to-emerald-500"})[s]||"from-gray-500 to-gray-600",I=s=>({email_marketing:T,content_creation:q,analytics_reporting:v,lead_generation:W,ai_orchestration:$})[s]||x;return b?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx(D,{size:"xl"})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"🐜 NeuroColony Intelligence"}),e.jsx("p",{className:"text-gray-600 text-lg",children:"AI Agent Platform That Surpasses N8N • Marketing-First Colony Architecture"})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(r,{onClick:_,className:"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"Create Marketing Colony"]}),e.jsxs(r,{variant:"outline",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Settings"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[o?.agentsByType?.map(s=>{const t=u(s._id),z=B(s._id);return e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsxs(i,{className:"relative overflow-hidden",children:[e.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${z} opacity-10`}),e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsxs(n,{className:"text-sm font-medium capitalize",children:[s._id," Agents"]}),e.jsx(t,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:s.count}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[s.avgSuccessRate?.toFixed(1)||0,"% avg success rate"]})]})]})},s._id)}),e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},children:e.jsxs(i,{className:"relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500 opacity-10"}),e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(n,{className:"text-sm font-medium",children:"Active Workflows"}),e.jsx(j,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:o?.activeWorkflows||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Running automations"})]})]})})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"flex space-x-1 bg-gray-200 p-1 rounded-lg w-fit",children:["overview","agents","performance","activity"].map(s=>e.jsx("button",{onClick:()=>k(s),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors capitalize ${h===s?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:s},s))})}),e.jsx(R,{mode:"wait",children:h==="overview"&&e.jsxs(m.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(i,{children:[e.jsx(c,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"w-5 h-5 text-purple-500"}),"Colony Hierarchy"]})}),e.jsx(l,{children:e.jsx("div",{className:"space-y-4",children:o?.agentsByType?.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[f.createElement(u(s._id),{className:`w-5 h-5 ${s._id==="queen"?"text-purple-500":s._id==="worker"?"text-blue-500":"text-green-500"}`}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium capitalize",children:[s._id," Agents"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[s.count," active"]})]})]}),e.jsxs(y,{variant:"secondary",children:[s.avgSuccessRate?.toFixed(1)||0,"% success"]})]},s._id))})})]}),e.jsxs(i,{children:[e.jsx(c,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"w-5 h-5 text-blue-500"}),"Recent Executions"]})}),e.jsx(l,{children:e.jsx("div",{className:"space-y-3",children:o?.recentExecutions?.slice(0,5).map((s,t)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[f.createElement(I(s.agent?.category),{className:"w-4 h-4 text-gray-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:s.agent?.name}),e.jsx("p",{className:"text-xs text-gray-600",children:new Date(s.createdAt).toLocaleTimeString()})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[s.status==="completed"?e.jsx(E,{className:"w-4 h-4 text-green-500"}):s.status==="failed"?e.jsx(F,{className:"w-4 h-4 text-red-500"}):e.jsx(H,{className:"w-4 h-4 text-yellow-500"}),e.jsx(y,{variant:s.status==="completed"?"success":"secondary",children:s.status})]})]},t))})})]})]},"overview")}),e.jsx("div",{className:"mt-8",children:e.jsxs(i,{children:[e.jsx(c,{children:e.jsx(n,{children:"Quick Actions"})}),e.jsx(l,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(r,{className:"h-20 flex flex-col items-center justify-center gap-2",children:[e.jsx(N,{className:"w-6 h-6"}),e.jsx("span",{children:"Create Agent"})]}),e.jsxs(r,{variant:"outline",className:"h-20 flex flex-col items-center justify-center gap-2",children:[e.jsx(j,{className:"w-6 h-6"}),e.jsx("span",{children:"Build Workflow"})]}),e.jsxs(r,{variant:"outline",className:"h-20 flex flex-col items-center justify-center gap-2",children:[e.jsx(v,{className:"w-6 h-6"}),e.jsx("span",{children:"View Analytics"})]})]})})]})})]})})};export{X as default};
