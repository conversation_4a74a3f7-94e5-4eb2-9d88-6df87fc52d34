import{j as e,ag as N,ah as f,m as i,h as k,r as v,u as L,a0 as M,ai as u,a8 as c,a6 as p,aj as A,a as W,Z as I,ak as V,w as H,C as B,i as S,a7 as x}from"./index-CVpabiD8.js";import{U as y}from"./users-f__6LX8a.js";const P=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("path",{d:"M12 2L2 7V12C2 16.5 4.5 20.74 12 22C19.5 20.74 22 16.5 22 12V7L12 2Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 8V16M8 12H16",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("circle",{cx:"12",cy:"12",r:"3",stroke:s,strokeWidth:"2"})]}),z=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("path",{d:"M12 2L17.66 5V11L12 14L6.34 11V5L12 2Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 14L17.66 17V23L12 20L6.34 23V17L12 14Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.34 5L12 8L17.66 5M6.34 17L12 20L17.66 17",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),D=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("circle",{cx:"5",cy:"12",r:"2",stroke:s,strokeWidth:"2"}),e.jsx("circle",{cx:"12",cy:"5",r:"2",stroke:s,strokeWidth:"2"}),e.jsx("circle",{cx:"19",cy:"12",r:"2",stroke:s,strokeWidth:"2"}),e.jsx("circle",{cx:"12",cy:"19",r:"2",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M6.5 10.5L10.5 6.5M13.5 6.5L17.5 10.5M17.5 13.5L13.5 17.5M10.5 17.5L6.5 13.5",stroke:s,strokeWidth:"2",strokeLinecap:"round"}),e.jsx("circle",{cx:"12",cy:"12",r:"3",stroke:s,strokeWidth:"2",fill:s,fillOpacity:"0.2"})]}),R=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("path",{d:"M12 2L14.5 7H19L15.5 10L17 15L12 12L7 15L8.5 10L5 7H9.5L12 2Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:s,fillOpacity:"0.2"}),e.jsx("circle",{cx:"12",cy:"20",r:"2",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M12 15V18",stroke:s,strokeWidth:"2"})]}),T=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("circle",{cx:"12",cy:"8",r:"3",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M12 11V16M9 14L12 16L15 14",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7 20H17",stroke:s,strokeWidth:"2",strokeLinecap:"round"})]}),O=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("circle",{cx:"12",cy:"12",r:"3",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M12 2V6M12 18V22M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M2 12H6M18 12H22M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22",stroke:s,strokeWidth:"2",strokeLinecap:"round"})]}),U=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M3 9H21M9 21V9",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M14 13H18M14 17H18",stroke:s,strokeWidth:"2",strokeLinecap:"round"})]}),Z=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("rect",{x:"3",y:"3",width:"6",height:"6",rx:"1",stroke:s,strokeWidth:"2"}),e.jsx("rect",{x:"15",y:"3",width:"6",height:"6",rx:"1",stroke:s,strokeWidth:"2"}),e.jsx("rect",{x:"9",y:"15",width:"6",height:"6",rx:"1",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M6 9V12H12V15M18 9V12H12V15",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),Q=({size:t=24,color:s="#00bcd4",...a})=>e.jsx("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:e.jsxs("g",{children:[e.jsx("circle",{cx:"12",cy:"12",r:"8",stroke:s,strokeWidth:"2",strokeDasharray:"4 2",fill:"none",children:e.jsx("animateTransform",{attributeName:"transform",type:"rotate",from:"0 12 12",to:"360 12 12",dur:"3s",repeatCount:"indefinite"})}),e.jsx("circle",{cx:"12",cy:"4",r:"2",fill:s,children:e.jsx("animate",{attributeName:"opacity",values:"1;0.3;1",dur:"1.5s",repeatCount:"indefinite"})})]})}),E=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("path",{d:"M3 22V13M8 22V8M13 22V3M18 22V11",stroke:s,strokeWidth:"2",strokeLinecap:"round"}),e.jsx("path",{d:"M3 13L8 8L13 3L18 11",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),$=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("path",{d:"M12 2L19 8.5V15.5L12 22L5 15.5V8.5L12 2Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 8L16 11V15L12 18L8 15V11L12 8Z",stroke:s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:s,fillOpacity:"0.2"}),e.jsx("path",{d:"M12 8V18",stroke:s,strokeWidth:"2"})]}),q=({size:t=24,color:s="currentColor",...a})=>e.jsxs("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[e.jsx("circle",{cx:"12",cy:"12",r:"3",stroke:s,strokeWidth:"2"}),e.jsx("path",{d:"M12 1V6M12 18V23M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M1 12H6M18 12H23M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22",stroke:s,strokeWidth:"2",strokeLinecap:"round"})]}),o={Colony:P,Hive:z,NeuralNetwork:D,QueenAgent:R,WorkerAgent:T,ScoutAgent:O,Blueprint:U,Workflow:Z,Processing:Q,Performance:E,Deploy:$,Configure:q},F={network:{400:"#26c6da"}},b={idle:{color:"text-gray-400",bg:"bg-gray-400/20",borderColor:"border-gray-400/30",pulseColor:"bg-gray-400",label:"Idle"},active:{color:"text-green-400",bg:"bg-green-400/20",borderColor:"border-green-400/30",pulseColor:"bg-green-400",label:"Active"},processing:{color:"text-blue-400",bg:"bg-blue-400/20",borderColor:"border-blue-400/30",pulseColor:"bg-blue-400",label:"Processing"},error:{color:"text-red-400",bg:"bg-red-400/20",borderColor:"border-red-400/30",pulseColor:"bg-red-400",label:"Error"},success:{color:"text-emerald-400",bg:"bg-emerald-400/20",borderColor:"border-emerald-400/30",pulseColor:"bg-emerald-400",label:"Success"}},G=({status:t="idle",size:s="md",showLabel:a=!0,showPulse:r=!0,className:m})=>{const l=b[t]||b.idle,d=N(),g={sm:"w-2 h-2",md:"w-3 h-3",lg:"w-4 h-4"};return e.jsxs("div",{className:f("flex items-center gap-2",m),children:[e.jsxs("div",{className:"relative",children:[r&&t==="active"&&!d&&e.jsx(i.div,{className:f("absolute inset-0 rounded-full",l.pulseColor,g[s]),animate:{scale:[1,2,2],opacity:[.7,0,0]},transition:{duration:2,repeat:1/0,ease:"easeOut"}}),e.jsx(i.div,{className:f("relative rounded-full",l.bg,"border",l.borderColor,g[s]),animate:t==="processing"&&!d?{rotate:360}:{},transition:{duration:2,repeat:1/0,ease:"linear"},children:t==="processing"&&e.jsx("div",{className:"absolute inset-0 rounded-full",children:e.jsx("div",{className:"absolute inset-[25%] bg-current rounded-full opacity-60"})})})]}),a&&e.jsx(k,{mode:"wait",children:e.jsx(i.span,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},transition:{duration:.2},className:f("text-sm font-medium",l.color),children:l.label},t)})]})},K=({progress:t=0,size:s=60,strokeWidth:a=4,className:r})=>{const m=N(),l=(s-a)/2,d=l*2*Math.PI,g=d-t/100*d;return e.jsxs("div",{className:f("relative",r),children:[e.jsxs("svg",{width:s,height:s,className:"transform -rotate-90",children:[e.jsx("circle",{cx:s/2,cy:s/2,r:l,stroke:"currentColor",strokeWidth:a,fill:"none",className:"text-gray-700"}),e.jsx(i.circle,{cx:s/2,cy:s/2,r:l,stroke:"url(#progress-gradient)",strokeWidth:a,fill:"none",strokeDasharray:d,initial:{strokeDashoffset:d},animate:{strokeDashoffset:g},transition:{duration:m?0:.5,ease:"easeOut"},strokeLinecap:"round"}),e.jsx("defs",{children:e.jsxs("linearGradient",{id:"progress-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[e.jsx("stop",{offset:"0%",stopColor:"#8b5cf6"}),e.jsx("stop",{offset:"100%",stopColor:"#3b82f6"})]})})]}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsxs(i.span,{className:"text-sm font-semibold text-purple-400",initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.2},children:[Math.round(t),"%"]})})]})},w=(t,s=2e3)=>{const[a,r]=v.useState(0),m=N();return v.useEffect(()=>{if(m){r(t);return}let l=null;const d=0,g=j=>{l||(l=j);const n=Math.min((j-l)/s,1),h=1-Math.pow(1-n,4),C=Math.floor(h*(t-d)+d);r(C),n<1&&window.requestAnimationFrame(g)};window.requestAnimationFrame(g)},[t,s,m]),a},_=t=>{const{activeAgents:s,successRate:a,resourceUsage:r}=t,m=s/10*30,l=a*40,d=(1-r)*30;return Math.round(m+l+d)},Y=()=>{const t=L(),[s,a]=v.useState("overview"),[r,m]=v.useState({activeAgents:7,totalAgents:12,successRate:.89,tasksCompleted:1247,resourceUsage:.45,activeWorkflows:3}),[l,d]=v.useState([{id:1,agent:"Data Analyzer Queen",task:"Completed market analysis",time:"2m ago",status:"success"},{id:2,agent:"Content Worker #3",task:"Generated 5 blog posts",time:"5m ago",status:"success"},{id:3,agent:"Integration Scout",task:"Monitoring API endpoints",time:"10m ago",status:"active"},{id:4,agent:"Security Soldier",task:"Blocked suspicious request",time:"15m ago",status:"warning"}]),g={overview:{title:"Colony Overview",icon:o.Colony,description:"Real-time colony health and activity monitoring"},agents:{title:"Agent Management",icon:o.WorkerAgent,description:"Configure and deploy your AI agents"},workflows:{title:"Neural Workflows",icon:o.Workflow,description:"Design and monitor agent collaboration patterns"},analytics:{title:"Colony Intelligence",icon:o.Performance,description:"Deep insights and optimization recommendations"},team:{title:"Team Collaboration",icon:y,description:"Manage team access and shared colonies"}},j=_(r);return e.jsxs("div",{className:"min-h-screen bg-system-background",children:[e.jsx(M,{variant:"particles"}),e.jsx("div",{className:"relative z-10 border-b border-gray-800 bg-system-surface/50 backdrop-blur-xl",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex items-center justify-between h-16",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o.Hive,{size:32,className:"text-honey-500"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold text-white",children:"Colony Command Center"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Neural coordination hub"})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:Object.entries(g).map(([n,h])=>e.jsxs("button",{onClick:()=>a(n),className:`px-4 py-2 rounded-lg transition-all duration-200 ${s===n?"bg-neural-500/20 text-neural-400 border border-neural-500/50":"text-gray-400 hover:text-white hover:bg-white/5"}`,children:[e.jsx(h.icon,{size:20,className:"inline-block mr-2"}),h.title]},n))})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs(i.div,{className:"mb-8 grid grid-cols-1 md:grid-cols-4 gap-4",variants:u.staggerContainer,initial:"hidden",animate:"show",children:[e.jsx(i.div,{variants:u.staggerItem,children:e.jsx(c,{className:"bg-gradient-to-br from-neural-600/20 to-neural-700/10 border-neural-500/30",hover:!0,glow:!0,delay:.1,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(i.div,{animate:{rotate:[0,360]},transition:{duration:20,repeat:1/0,ease:"linear"},children:e.jsx(o.HealthIcon,{size:20,className:"text-neural-400"})}),e.jsxs(p,{variant:j>70?"success":j>40?"warning":"error",animate:!0,children:[j,"% Healthy"]})]}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Colony Health"}),e.jsx("div",{className:"mt-2 h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx(i.div,{className:"h-full bg-gradient-to-r from-neural-500 to-network-500",initial:{width:0},animate:{width:`${j}%`},transition:{duration:1,ease:"easeOut",delay:.5}})})]})})}),e.jsx(i.div,{variants:u.staggerItem,children:e.jsx(c,{className:"bg-gradient-to-br from-organic-600/20 to-organic-700/10 border-organic-500/30",hover:!0,glow:!0,delay:.2,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(A,{size:20,className:"text-organic-400"}),e.jsx(i.span,{className:"text-2xl font-bold text-white",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:300},children:w(r.activeAgents,1e3)},r.activeAgents)]}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Active Agents"}),e.jsxs("p",{className:"text-sm text-gray-400",children:["of ",r.totalAgents," total"]}),e.jsx(G,{status:"active",size:"sm",showLabel:!1,className:"mt-2"})]})})}),e.jsx(i.div,{variants:u.staggerItem,children:e.jsx(c,{className:"bg-gradient-to-br from-honey-600/20 to-honey-700/10 border-honey-500/30",hover:!0,glow:!0,delay:.3,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(W,{size:20,className:"text-honey-400"}),e.jsxs(i.span,{className:"text-2xl font-bold text-white",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:300},children:[w(r.successRate*100,1500),"%"]},r.successRate)]}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Success Rate"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Last 24 hours"}),e.jsx(K,{progress:r.successRate*100,size:40,className:"mt-2"})]})})}),e.jsx(i.div,{variants:u.staggerItem,children:e.jsx(c,{className:"bg-gradient-to-br from-colony-600/20 to-colony-700/10 border-colony-500/30",hover:!0,glow:!0,delay:.4,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(I,{size:20,className:"text-colony-400"}),e.jsx(i.span,{className:"text-2xl font-bold text-white",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:300},children:w(r.tasksCompleted,2e3)},r.tasksCompleted)]}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Tasks Completed"}),e.jsx("p",{className:"text-sm text-gray-400",children:"This month"}),e.jsx(V,{className:"mt-2"})]})})})]}),s==="overview"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsx(c,{className:"h-full",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[e.jsx(o.NeuralNetwork,{size:24,className:"text-neural-400"}),"Live Colony Network"]}),e.jsxs("div",{className:"relative h-96 bg-gray-900/50 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(o.Colony,{size:120,className:"text-neural-500/20 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Neural network visualization"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Showing agent interactions in real-time"})]})}),[...Array(5)].map((n,h)=>e.jsx("div",{className:"absolute w-3 h-3 bg-neural-400 rounded-full animate-pulse",style:{top:`${20+h*15}%`,left:`${15+h*20}%`,animationDelay:`${h*.2}s`}},h))]})]})})}),e.jsx(i.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3},children:e.jsx(c,{className:"h-full",hover:!0,glow:!0,children:e.jsxs("div",{className:"p-6",children:[e.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[e.jsx(i.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},children:e.jsx(H,{size:24,className:"text-network-400"})}),"Recent Activity",e.jsxs(p,{variant:"neural",pulse:!0,className:"ml-auto",children:[l.filter(n=>n.status==="active").length," Active"]})]}),e.jsx(k,{children:e.jsx(i.div,{className:"space-y-3",children:l.map((n,h)=>e.jsxs(i.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{delay:h*.1},whileHover:{scale:1.02},className:"flex items-start gap-3 p-3 rounded-lg bg-gray-800/50 hover:bg-gray-800/70 transition-all duration-200 relative overflow-hidden",children:[e.jsxs(i.div,{className:"mt-1",animate:n.status==="active"?{rotate:360}:{},transition:{duration:2,repeat:1/0,ease:"linear"},children:[n.status==="success"&&e.jsx(i.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500},children:e.jsx(B,{size:16,className:"text-green-400"})}),n.status==="active"&&e.jsx(o.Processing,{size:16,color:F.network[400]}),n.status==="warning"&&e.jsx(i.div,{animate:{scale:[1,1.2,1]},transition:{duration:1,repeat:1/0},children:e.jsx(S,{size:16,className:"text-yellow-400"})})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:n.agent}),e.jsx("p",{className:"text-xs text-gray-400",children:n.task})]}),e.jsx("span",{className:"text-xs text-gray-500",children:n.time}),n.status==="active"&&e.jsx(i.div,{className:"absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-neural-500 to-network-500",initial:{width:0},animate:{width:"100%"},transition:{duration:2,repeat:1/0}})]},n.id))})}),e.jsx(x,{variant:"ghost",className:"w-full mt-4",onClick:()=>a("analytics"),children:"View All Activity"})]})})})]}),s==="agents"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:[e.jsx(c,{className:"lg:col-span-full",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs(x,{variant:"outline",className:"flex flex-col items-center gap-2 h-auto py-4",onClick:()=>t("/agent-builder"),children:[e.jsx(o.Blueprint,{size:24}),e.jsx("span",{children:"Create Agent"})]}),e.jsxs(x,{variant:"outline",className:"flex flex-col items-center gap-2 h-auto py-4",onClick:()=>t("/agent-marketplace"),children:[e.jsx(o.Hive,{size:24}),e.jsx("span",{children:"Browse Templates"})]}),e.jsxs(x,{variant:"outline",className:"flex flex-col items-center gap-2 h-auto py-4",children:[e.jsx(o.Deploy,{size:24}),e.jsx("span",{children:"Deploy Colony"})]}),e.jsxs(x,{variant:"outline",className:"flex flex-col items-center gap-2 h-auto py-4",children:[e.jsx(o.Configure,{size:24}),e.jsx("span",{children:"Settings"})]})]})]})}),e.jsx(c,{className:"bg-gradient-to-br from-colony-600/10 to-transparent",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[e.jsx(o.QueenAgent,{size:24,className:"text-colony-400"}),"Data Analyzer Queen"]}),e.jsx(p,{variant:"success",children:"Active"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Orchestrates data analysis workflows and manages worker agents"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Tasks Completed"}),e.jsx("span",{className:"text-white font-medium",children:"342"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Success Rate"}),e.jsx("span",{className:"text-white font-medium",children:"94%"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Workers"}),e.jsx("span",{className:"text-white font-medium",children:"3 active"})]})]}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx(x,{size:"sm",variant:"ghost",children:"Configure"}),e.jsx(x,{size:"sm",variant:"primary",children:"View Details"})]})]})}),e.jsx(c,{className:"bg-gradient-to-br from-neural-600/10 to-transparent",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[e.jsx(o.WorkerAgent,{size:24,className:"text-neural-400"}),"Content Generator"]}),e.jsx(p,{variant:"warning",children:"Processing"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Creates high-quality content based on templates and guidelines"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Current Task"}),e.jsx("span",{className:"text-white font-medium",children:"Blog series"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Progress"}),e.jsx("span",{className:"text-white font-medium",children:"67%"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Est. Time"}),e.jsx("span",{className:"text-white font-medium",children:"15 min"})]})]}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx(x,{size:"sm",variant:"ghost",children:"Pause"}),e.jsx(x,{size:"sm",variant:"primary",children:"Monitor"})]})]})}),e.jsx(c,{className:"bg-gradient-to-br from-network-600/10 to-transparent",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[e.jsx(o.ScoutAgent,{size:24,className:"text-network-400"}),"API Monitor Scout"]}),e.jsx(p,{variant:"default",children:"Idle"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Monitors external APIs and reports on availability and performance"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Last Check"}),e.jsx("span",{className:"text-white font-medium",children:"2m ago"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Endpoints"}),e.jsx("span",{className:"text-white font-medium",children:"12 active"})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Alerts"}),e.jsx("span",{className:"text-white font-medium",children:"0"})]})]}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx(x,{size:"sm",variant:"ghost",children:"Configure"}),e.jsx(x,{size:"sm",variant:"primary",children:"Activate"})]})]})})]}),s==="workflows"&&e.jsx("div",{className:"space-y-6",children:e.jsx(c,{children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Active Neural Workflows"}),e.jsxs("div",{className:"text-center py-12",children:[e.jsx(o.Workflow,{size:64,className:"text-gray-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Workflow canvas coming soon"}),e.jsx(x,{className:"mt-4",onClick:()=>t("/workflow-builder"),children:"Create New Workflow"})]})]})})}),s==="analytics"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(c,{children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Performance Metrics"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"CPU Usage"}),e.jsxs("span",{className:"text-sm text-white",children:[(r.resourceUsage*100).toFixed(0),"%"]})]}),e.jsx("div",{className:"h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-neural-500 to-network-500",style:{width:`${r.resourceUsage*100}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Memory Usage"}),e.jsx("span",{className:"text-sm text-white",children:"62%"})]}),e.jsx("div",{className:"h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-organic-500 to-honey-500",style:{width:"62%"}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Network I/O"}),e.jsx("span",{className:"text-sm text-white",children:"38%"})]}),e.jsx("div",{className:"h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-colony-500 to-neural-500",style:{width:"38%"}})})]})]})]})}),e.jsx(c,{children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Colony Statistics"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[e.jsx("p",{className:"text-2xl font-bold text-white",children:r.activeWorkflows}),e.jsx("p",{className:"text-sm text-gray-400",children:"Active Workflows"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[e.jsx("p",{className:"text-2xl font-bold text-white",children:"24/7"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Uptime"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[e.jsx("p",{className:"text-2xl font-bold text-white",children:"1.2K"}),e.jsx("p",{className:"text-sm text-gray-400",children:"API Calls/Day"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[e.jsx("p",{className:"text-2xl font-bold text-white",children:"15ms"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Avg Response"})]})]})]})})]}),s==="team"&&e.jsx("div",{className:"space-y-6",children:e.jsx(c,{children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Team Collaboration Hub"}),e.jsxs("div",{className:"text-center py-12",children:[e.jsx(y,{size:64,className:"text-gray-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Team features coming soon"}),e.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Manage team access, permissions, and shared colonies"})]})]})})})]})]})};export{Y as default};
