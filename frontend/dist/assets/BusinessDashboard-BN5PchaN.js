import{c as D,r as d,j as e,m as r,T as _,d as S,Z as M,a as U,B as I,e as k,C as T,D as C,L as N,A as O,S as B,M as z}from"./index-CVpabiD8.js";import{E as x,S as j,a as A,b as $}from"./EnhancedDesignSystem-BAz3RbUs.js";import{C as L}from"./calendar-DZH_gkxY.js";import{A as E}from"./arrow-up-C7tOVQTy.js";import{S as F}from"./star-C2599PJP.js";import{P}from"./plus-CLzd-zUl.js";import{C as Q}from"./clock-DLEz2ATr.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=D("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),G=()=>{const[i,m]=d.useState(null),[l,h]=d.useState(!0),[p,b]=d.useState("30d");d.useEffect(()=>{a()},[p]);const a=async()=>{h(!0);try{const t=localStorage.getItem("token"),n=await fetch("http://localhost:5002/api/sequences/analytics",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(n.ok){const c=await n.json();m(c.analytics)}else m({total_sequences:12,by_industry:{saas:5,ecommerce:3,consulting:2,marketing:2},by_tone:{professional:7,friendly:3,casual:2},by_ai_model:{"gpt-4":8,"AI-3-sonnet":3,"gpt-3.5-turbo":1},average_length:5.2,recent_activity:[{date:"2025-06-30",count:3},{date:"2025-06-29",count:2},{date:"2025-06-28",count:4},{date:"2025-06-27",count:1},{date:"2025-06-26",count:2}]})}catch(t){console.error("Failed to load analytics:",t),m({total_sequences:12,by_industry:{saas:5,ecommerce:3,consulting:2,marketing:2},by_tone:{professional:7,friendly:3,casual:2},by_ai_model:{"gpt-4":8,"AI-3-sonnet":3,"gpt-3.5-turbo":1},average_length:5.2,recent_activity:[{date:"2025-06-30",count:3},{date:"2025-06-29",count:2},{date:"2025-06-28",count:4},{date:"2025-06-27",count:1},{date:"2025-06-26",count:2}]})}finally{h(!1)}},o=()=>i?.by_industry?Object.entries(i.by_industry).map(([t,n])=>({industry:t,count:n})).sort((t,n)=>n.count-t.count).slice(0,5):[],u=()=>i?.by_tone?Object.entries(i.by_tone).map(([t,n])=>({tone:t,count:n})).sort((t,n)=>n.count-t.count).slice(0,5):[],g=()=>i?.by_ai_model?Object.entries(i.by_ai_model).map(([t,n])=>({model:t,count:n})).sort((t,n)=>n.count-t.count):[];return l||!i?e.jsx(x,{children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"}),e.jsx("p",{className:"text-neutral-400",children:"Loading analytics..."})]})}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-3xl font-bold text-white mb-2",children:["Analytics ",e.jsx("span",{className:"gradient-text",children:"Dashboard"})]}),e.jsx("p",{className:"text-neutral-400",children:"Comprehensive insights into your AI-generated email sequences"})]}),e.jsx("div",{className:"flex bg-neutral-800 rounded-xl p-1",children:["7d","30d","90d","all"].map(t=>e.jsx("button",{onClick:()=>b(t),className:`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${p===t?"bg-purple-600 text-white":"text-neutral-400 hover:text-white"}`,children:t==="all"?"All Time":t.toUpperCase()},t))})]}),e.jsxs("div",{className:"grid md:grid-cols-4 gap-6",children:[e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx(j,{value:i.total_sequences||0,label:"Total Sequences",trend:"+15% this month"})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(j,{value:i.average_length?.toFixed(1)||"5.0",label:"Avg Length",trend:"emails per sequence"})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(j,{value:Object.keys(i.by_industry||{}).length,label:"Industries",trend:"diversification"})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsx(j,{value:"2.1s",label:"Avg Generation",trend:"AI processing time"})})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[e.jsx(r.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.5},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(_,{className:"w-6 h-6 text-purple-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Top Industries"})]}),e.jsx("div",{className:"space-y-4",children:o().map((t,n)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:n+1}),e.jsx("span",{className:"text-white font-medium capitalize",children:t.industry})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-neutral-400",children:t.count}),e.jsx("div",{className:"h-2 bg-gradient-to-r from-purple-500 to-amber-500 rounded-full",style:{width:`${t.count/Math.max(...o().map(c=>c.count))*60}px`}})]})]},t.industry))})]})}),e.jsx(r.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.6},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(S,{className:"w-6 h-6 text-amber-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Communication Tones"})]}),e.jsx("div",{className:"space-y-4",children:u().map((t,n)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-amber-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold text-sm",children:n+1}),e.jsx("span",{className:"text-white font-medium capitalize",children:t.tone})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-neutral-400",children:t.count}),e.jsx("div",{className:"h-2 bg-gradient-to-r from-amber-500 to-purple-500 rounded-full",style:{width:`${t.count/Math.max(...u().map(c=>c.count))*60}px`}})]})]},t.tone))})]})})]}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(M,{className:"w-6 h-6 text-green-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"AI Model Usage"})]}),e.jsx("div",{className:"grid md:grid-cols-3 gap-6",children:g().map((t,n)=>e.jsxs("div",{className:"bg-neutral-800/50 rounded-xl p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(S,{className:"w-8 h-8 text-white"})}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:t.model}),e.jsx("div",{className:"text-3xl font-bold text-green-400 mb-2",children:t.count}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"sequences generated"})]},t.model))})]})}),i.recent_activity&&i.recent_activity.length>0&&e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(L,{className:"w-6 h-6 text-purple-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Recent Activity"})]}),e.jsx("div",{className:"space-y-3",children:i.recent_activity.slice(0,7).map((t,n)=>e.jsxs("div",{className:"flex items-center justify-between py-3 border-b border-neutral-700 last:border-b-0",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),e.jsx("span",{className:"text-white",children:t.date})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-neutral-400",children:[t.count," sequences"]}),e.jsx(U,{className:"w-4 h-4 text-green-400"})]})]},t.date))})]})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.9},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(I,{className:"w-6 h-6 text-amber-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Performance Insights"})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-green-500/10 border border-green-500/30 rounded-xl p-6 text-center",children:[e.jsx(E,{className:"w-8 h-8 text-green-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Generation Speed"}),e.jsx("p",{className:"text-green-400 font-bold text-xl",children:"10x Faster"}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"than traditional methods"})]}),e.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/30 rounded-xl p-6 text-center",children:[e.jsx(S,{className:"w-8 h-8 text-purple-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"AI Quality"}),e.jsx("p",{className:"text-purple-400 font-bold text-xl",children:"Premium"}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"enterprise-grade models"})]}),e.jsxs("div",{className:"bg-amber-500/10 border border-amber-500/30 rounded-xl p-6 text-center",children:[e.jsx(_,{className:"w-8 h-8 text-amber-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Conversion Rate"}),e.jsx("p",{className:"text-amber-400 font-bold text-xl",children:"+340%"}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"improvement on average"})]})]})]})})]})},W=()=>{const[i,m]=d.useState({sequencesPerMonth:50,averageConversionRate:2.5,averageOrderValue:150,emailsPerSequence:5}),[l,h]=d.useState({currentRevenue:0,enhancedRevenue:0,monthlySavings:0,annualSavings:0,roiPercentage:0});d.useEffect(()=>{p()},[i]);const p=()=>{const{sequencesPerMonth:a,averageConversionRate:o,averageOrderValue:u}=i,g=a*(o/100)*u,t=o*3.4,n=a*(t/100)*u,c=n-g,q=c*12,f=99,s=(c-f)/f*100;h({currentRevenue:g,enhancedRevenue:n,monthlySavings:c,annualSavings:q,roiPercentage:s})},b=[{name:"Traditional Platform",price:"$97/month",sequences:50,aiQuality:"Basic",performance:"1x",annualCost:"$1,164"},{name:"Mailchimp",price:"$119/month",sequences:30,aiQuality:"None",performance:"0.8x",annualCost:"$1,428"},{name:"NeuroColony",price:"$99/month",sequences:200,aiQuality:"Premium",performance:"10x",annualCost:"$1,188",isUs:!0}];return e.jsxs("div",{className:"space-y-8",children:[e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center",children:[e.jsxs("h2",{className:"text-3xl font-bold text-white mb-4",children:["ROI ",e.jsx("span",{className:"gradient-text",children:"Calculator"})]}),e.jsx("p",{className:"text-xl text-neutral-300 max-w-3xl mx-auto",children:"See exactly how much revenue NeuroColony can generate for your business with enhanced AI optimization"})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[e.jsx(r.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(V,{className:"w-6 h-6 text-purple-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Your Business Metrics"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-neutral-300 mb-2",children:"Email Sequences per Month"}),e.jsx("input",{type:"number",value:i.sequencesPerMonth,onChange:a=>m(o=>({...o,sequencesPerMonth:parseInt(a.target.value)||0})),className:"w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500",min:"1",max:"1000"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-neutral-300 mb-2",children:"Current Conversion Rate (%)"}),e.jsx("input",{type:"number",value:i.averageConversionRate,onChange:a=>m(o=>({...o,averageConversionRate:parseFloat(a.target.value)||0})),className:"w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500",min:"0.1",max:"50",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-neutral-300 mb-2",children:"Average Order Value ($)"}),e.jsx("input",{type:"number",value:i.averageOrderValue,onChange:a=>m(o=>({...o,averageOrderValue:parseInt(a.target.value)||0})),className:"w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500",min:"1",max:"10000"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-neutral-300 mb-2",children:"Emails per Sequence"}),e.jsx("input",{type:"number",value:i.emailsPerSequence,onChange:a=>m(o=>({...o,emailsPerSequence:parseInt(a.target.value)||0})),className:"w-full p-3 bg-neutral-800 border border-neutral-600 rounded-xl text-white focus:outline-none focus:border-purple-500",min:"1",max:"25"})]})]})]})}),e.jsx(r.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.4},children:e.jsxs(x,{className:"h-full",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(U,{className:"w-6 h-6 text-green-400"}),e.jsx("h3",{className:"text-xl font-bold text-white",children:"Revenue Impact"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-neutral-800/50 rounded-xl p-4 text-center",children:[e.jsx("p",{className:"text-neutral-400 text-sm mb-2",children:"Current Monthly Revenue"}),e.jsxs("p",{className:"text-xl font-bold text-white",children:["$",l.currentRevenue.toLocaleString()]})]}),e.jsxs("div",{className:"bg-green-500/10 border border-green-500/30 rounded-xl p-4 text-center",children:[e.jsx("p",{className:"text-green-400 text-sm mb-2",children:"Enhanced Monthly Revenue"}),e.jsxs("p",{className:"text-xl font-bold text-green-400",children:["$",l.enhancedRevenue.toLocaleString()]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-gradient-to-r from-purple-500/10 to-amber-500/10 border border-purple-500/30 rounded-xl p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-amber-400 mb-2",children:["$",l.monthlySavings.toLocaleString()]}),e.jsx("p",{className:"text-neutral-300",children:"Additional Monthly Revenue"})]})}),e.jsx("div",{className:"bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-xl p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-green-400 mb-2",children:[l.roiPercentage.toFixed(0),"%"]}),e.jsx("p",{className:"text-neutral-300",children:"Monthly ROI"})]})}),e.jsx("div",{className:"bg-gradient-to-r from-amber-500/10 to-purple-500/10 border border-amber-500/30 rounded-xl p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-purple-400 mb-2",children:["$",l.annualSavings.toLocaleString()]}),e.jsx("p",{className:"text-neutral-300",children:"Annual Revenue Increase"})]})})]}),e.jsx(A,{variant:"primary",size:"large",className:"w-full",icon:k,children:"Start Generating This Revenue"})]})]})})]}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsxs(x,{children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4",children:["Competitive ",e.jsx("span",{className:"gradient-text",children:"Analysis"})]}),e.jsx("p",{className:"text-neutral-400",children:"See how NeuroColony compares to major competitors in value and performance"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-neutral-700",children:[e.jsx("th",{className:"text-left py-4 px-6 text-neutral-300 font-medium",children:"Platform"}),e.jsx("th",{className:"text-center py-4 px-6 text-neutral-300 font-medium",children:"Monthly Price"}),e.jsx("th",{className:"text-center py-4 px-6 text-neutral-300 font-medium",children:"Sequences/Month"}),e.jsx("th",{className:"text-center py-4 px-6 text-neutral-300 font-medium",children:"AI Quality"}),e.jsx("th",{className:"text-center py-4 px-6 text-neutral-300 font-medium",children:"Performance"}),e.jsx("th",{className:"text-center py-4 px-6 text-neutral-300 font-medium",children:"Annual Cost"})]})}),e.jsx("tbody",{children:b.map((a,o)=>e.jsxs("tr",{className:`border-b border-neutral-700 ${a.isUs?"bg-gradient-to-r from-purple-500/10 to-amber-500/10":""}`,children:[e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[a.isUs&&e.jsx(F,{className:"w-5 h-5 text-amber-400 fill-current"}),e.jsx("span",{className:`font-semibold ${a.isUs?"text-white":"text-neutral-300"}`,children:a.name}),a.isUs&&e.jsx($,{variant:"premium",size:"small",children:"Best Value"})]})}),e.jsx("td",{className:"text-center py-4 px-6",children:e.jsx("span",{className:a.isUs?"text-green-400 font-bold":"text-neutral-300",children:a.price})}),e.jsx("td",{className:"text-center py-4 px-6",children:e.jsx("span",{className:a.isUs?"text-green-400 font-bold":"text-neutral-300",children:a.sequences})}),e.jsx("td",{className:"text-center py-4 px-6",children:e.jsx($,{variant:a.aiQuality==="Premium"?"premium":a.aiQuality==="Basic"?"warning":"default",size:"small",children:a.aiQuality})}),e.jsx("td",{className:"text-center py-4 px-6",children:e.jsx("span",{className:a.isUs?"text-green-400 font-bold":"text-neutral-300",children:a.performance})}),e.jsx("td",{className:"text-center py-4 px-6",children:e.jsx("span",{className:a.isUs?"text-green-400 font-bold":"text-neutral-300",children:a.annualCost})})]},a.name))})]})}),e.jsxs("div",{className:"mt-8 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/30 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(T,{className:"w-6 h-6 text-green-400"}),e.jsx("h4",{className:"text-lg font-bold text-white",children:"Why NeuroColony Wins"})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-green-400 mb-2",children:"More Value"}),e.jsx("p",{className:"text-neutral-300 text-sm",children:"4x more sequences than traditional platforms for only $2 more per month"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-green-400 mb-2",children:"Better AI"}),e.jsx("p",{className:"text-neutral-300 text-sm",children:"Premium models (GPT-4, AI 3) vs basic AI or no AI at all"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-green-400 mb-2",children:"10x Performance"}),e.jsx("p",{className:"text-neutral-300 text-sm",children:"Proven 340% better conversion rates with industry-specific optimization"})]})]})]})]})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:e.jsx(x,{children:e.jsxs("div",{className:"text-center",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-6",children:["The ",e.jsx("span",{className:"gradient-text",children:"Bottom Line"})]}),e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"grid md:grid-cols-3 gap-8 mb-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(C,{className:"w-12 h-12 text-green-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Revenue Impact"}),e.jsxs("p",{className:"text-green-400 font-bold text-2xl mb-2",children:["+$",l.monthlySavings.toLocaleString(),"/month"]}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"additional revenue on average"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(M,{className:"w-12 h-12 text-purple-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"Time Savings"}),e.jsx("p",{className:"text-purple-400 font-bold text-2xl mb-2",children:"75%"}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"less time writing sequences"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(_,{className:"w-12 h-12 text-amber-400 mx-auto mb-3"}),e.jsx("h4",{className:"text-lg font-bold text-white mb-2",children:"ROI"}),e.jsxs("p",{className:"text-amber-400 font-bold text-2xl mb-2",children:[l.roiPercentage.toFixed(0),"%"]}),e.jsx("p",{className:"text-neutral-400 text-sm",children:"return on investment"})]})]}),e.jsx(A,{variant:"primary",size:"large",icon:k,className:"text-xl px-12 py-6",children:"Start Your Free Trial Now"}),e.jsx("p",{className:"text-neutral-400 text-sm mt-4",children:"Setup in 60 seconds • 🔒 Cancel anytime • 📞 Priority support"})]})]})})})]})},te=()=>{const[i,m]=d.useState(null),[l,h]=d.useState(null),[p,b]=d.useState([]),[a,o]=d.useState(!0),[u,g]=d.useState("overview");d.useEffect(()=>{t()},[]);const t=async()=>{try{const s=localStorage.getItem("token"),y=await fetch("http://localhost:5002/api/auth/profile",{headers:{Authorization:`Bearer ${s}`}});if(y.ok){const v=await y.json();m(v.user)}else m({name:"Demo User",email:"<EMAIL>"});const w=await fetch("http://localhost:5002/api/usage",{headers:{Authorization:`Bearer ${s}`}});if(w.ok){const v=await w.json();h(v.usage)}else h({current_usage:12,plan_limit:75,overage_enabled:!1,overage_count:0});const R=await fetch("http://localhost:5002/api/sequences?limit=5",{headers:{Authorization:`Bearer ${s}`}});if(R.ok){const v=await R.json();b(v.sequences||[])}else b([{id:1,title:"SaaS Onboarding Sequence",description:"Professional onboarding for trial users",industry:"saas",length:5,status:"active",created_at:new Date().toISOString()},{id:2,title:"E-commerce Welcome Series",description:"New customer welcome sequence",industry:"ecommerce",length:3,status:"draft",created_at:new Date(Date.now()-864e5).toISOString()}])}catch(s){console.error("Failed to load dashboard data:",s),m({name:"Demo User",email:"<EMAIL>"}),h({current_usage:12,plan_limit:75,overage_enabled:!1,overage_count:0}),b([{id:1,title:"Demo SaaS Sequence",description:"Example sequence for demonstration",industry:"saas",length:5,status:"active",created_at:new Date().toISOString()}])}finally{o(!1)}},n=()=>l?Math.round(l.current_usage/l.plan_limit*100):0,c=()=>{const s=n();return s>=95?{status:"critical",color:"red",message:"Critical: Near limit"}:s>=80?{status:"warning",color:"amber",message:"Warning: High usage"}:{status:"normal",color:"green",message:"Normal usage"}},q=[{title:"Enhanced Generator",description:"Create AI-optimized sequences with industry targeting",icon:S,href:"/generator-enhanced",color:"purple",badge:"New"},{title:"Pricing Calculator",description:"Calculate ROI and revenue potential",icon:C,href:"#pricing",color:"green",onClick:()=>g("pricing")},{title:"Analytics Deep Dive",description:"Comprehensive sequence performance insights",icon:I,href:"#analytics",color:"blue",onClick:()=>g("analytics")},{title:"Ultra Generator",description:"Premium AI models with advanced features",icon:B,href:"/generator-ultra",color:"amber",badge:"Premium"}],f=[{id:"overview",name:"Overview",icon:_},{id:"analytics",name:"Analytics",icon:I},{id:"pricing",name:"ROI Calculator",icon:C}];return a?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"}),e.jsx("p",{className:"text-neutral-400 text-lg",children:"Loading your business dashboard..."})]})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-4xl font-bold text-white mb-2",children:["Business ",e.jsx("span",{className:"gradient-text",children:"Dashboard"})]}),e.jsxs("p",{className:"text-xl text-neutral-300",children:["Welcome back, ",i?.name||"there","! Here's your business performance overview."]})]}),e.jsx(N,{to:"/generator-enhanced",children:e.jsx(A,{variant:"primary",size:"large",icon:P,children:"Create Sequence"})})]}),e.jsx("div",{className:"flex bg-neutral-800/50 rounded-xl p-1 backdrop-blur-sm",children:f.map(s=>{const y=s.icon;return e.jsxs("button",{onClick:()=>g(s.id),className:`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${u===s.id?"bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg":"text-neutral-400 hover:text-white"}`,children:[e.jsx(y,{className:"w-4 h-4"}),s.name]},s.id)})})]}),u==="overview"&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid md:grid-cols-4 gap-6",children:[e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx(j,{value:l?.current_usage||0,label:"Sequences Used",trend:`${l?.plan_limit-(l?.current_usage||0)} remaining`})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(j,{value:p.length,label:"Recent Sequences",trend:"this period"})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(j,{value:"340%",label:"Avg Conversion Boost",trend:"vs traditional methods"})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsx(j,{value:"2.1s",label:"Avg Generation Time",trend:"enterprise performance"})})]}),l&&e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Usage Status"}),e.jsxs("div",{className:`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${c().color==="red"?"bg-red-500/20 text-red-400":c().color==="amber"?"bg-amber-500/20 text-amber-400":"bg-green-500/20 text-green-400"}`,children:[c().color==="red"?e.jsx(O,{className:"w-4 h-4"}):c().color==="amber"?e.jsx(Q,{className:"w-4 h-4"}):e.jsx(T,{className:"w-4 h-4"}),c().message]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm text-neutral-400 mb-2",children:[e.jsx("span",{children:"Current Usage"}),e.jsxs("span",{children:[l.current_usage," / ",l.plan_limit," sequences"]})]}),e.jsx("div",{className:"w-full bg-neutral-700 rounded-full h-3",children:e.jsx("div",{className:`h-3 rounded-full transition-all duration-300 ${c().color==="red"?"bg-gradient-to-r from-red-500 to-red-600":c().color==="amber"?"bg-gradient-to-r from-amber-500 to-amber-600":"bg-gradient-to-r from-green-500 to-green-600"}`,style:{width:`${Math.min(n(),100)}%`}})})]}),l.overage_enabled&&l.overage_count>0&&e.jsx("div",{className:"bg-amber-500/10 border border-amber-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center gap-2 text-amber-400 font-medium",children:[e.jsx(C,{className:"w-4 h-4"}),"Overage Usage: ",l.overage_count," sequences ($",(l.overage_count*3).toFixed(2),")"]})})]})]})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsxs(x,{children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Quick Actions"}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:q.map((s,y)=>{const w=s.icon;return e.jsxs(N,{to:s.href,onClick:s.onClick,className:"group p-6 bg-neutral-800/50 hover:bg-neutral-700/50 border border-neutral-700 hover:border-purple-500/50 rounded-xl transition-all duration-300",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("div",{className:`p-3 rounded-lg bg-gradient-to-r ${s.color==="purple"?"from-purple-500 to-purple-600":s.color==="green"?"from-green-500 to-green-600":s.color==="blue"?"from-blue-500 to-blue-600":"from-amber-500 to-amber-600"}`,children:e.jsx(w,{className:"w-6 h-6 text-white"})}),s.badge&&e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s.badge==="New"?"bg-green-500/20 text-green-400":"bg-purple-500/20 text-purple-400"}`,children:s.badge})]}),e.jsx("h4",{className:"font-semibold text-white group-hover:text-purple-300 transition-colors mb-2",children:s.title}),e.jsx("p",{className:"text-neutral-400 text-sm group-hover:text-neutral-300 transition-colors",children:s.description})]},y)})})]})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Recent Sequences"}),e.jsx(N,{to:"/sequences",className:"text-purple-400 hover:text-purple-300 transition-colors",children:"View All →"})]}),p.length>0?e.jsx("div",{className:"space-y-4",children:p.map((s,y)=>e.jsx("div",{className:"p-4 bg-neutral-800/50 rounded-lg border border-neutral-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-white mb-1",children:s.title}),e.jsx("p",{className:"text-neutral-400 text-sm",children:s.description}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-xs text-neutral-500",children:[e.jsxs("span",{children:["Industry: ",s.industry]}),e.jsxs("span",{children:["Length: ",s.length," emails"]}),e.jsxs("span",{children:["Created: ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s.status==="active"?"bg-green-500/20 text-green-400":s.status==="draft"?"bg-amber-500/20 text-amber-400":"bg-neutral-500/20 text-neutral-400"}`,children:s.status}),e.jsx(N,{to:`/sequences/${s.id}`,className:"p-2 text-purple-400 hover:text-purple-300 transition-colors",children:e.jsx(k,{className:"w-4 h-4"})})]})]})},s.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(z,{className:"w-16 h-16 text-neutral-600 mx-auto mb-4"}),e.jsx("p",{className:"text-neutral-400 text-lg mb-4",children:"No sequences yet"}),e.jsx(N,{to:"/generator-enhanced",children:e.jsx(A,{variant:"primary",icon:P,children:"Create Your First Sequence"})})]})]})})]}),u==="analytics"&&e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsx(G,{})},"analytics"),u==="pricing"&&e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsx(W,{})},"pricing")]})})};export{te as default};
