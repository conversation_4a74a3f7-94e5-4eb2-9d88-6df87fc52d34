import{r as i,j as e,m as d,W as v,Y as w,K as x,f as h,E as C,Z as k,S,V as r}from"./index-CVpabiD8.js";import{P as p}from"./palette-BJ27JRb6.js";import{S as D}from"./save-D4vIjgOV.js";const F=({user:B})=>{const[s,l]=i.useState({primaryColor:"#6366F1",secondaryColor:"#8B5CF6",accentColor:"#06B6D4",logoUrl:"",faviconUrl:"",companyName:"",supportEmail:"",customDomain:"",customCss:"",footerText:"Powered by NeuroColony",hidePoweredBy:!1}),[t,o]=i.useState(!1),[E,P]=i.useState(!1),[n,u]=i.useState("branding");i.useEffect(()=>{g()},[]);const g=async()=>{try{const a=await fetch("http://localhost:5002/api/white-label/branding",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.ok){const c=await a.json();l(c.branding)}}catch(a){console.error("Error fetching branding:",a)}},b=async()=>{o(!0);try{(await fetch("http://localhost:5002/api/white-label/branding",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({branding:s})})).ok?r.success("Branding settings saved successfully!"):r.error("Failed to save branding settings")}catch{r.error("Error saving branding settings")}finally{o(!1)}},j=async()=>{if(!s.customDomain){r.error("Please enter a custom domain first");return}o(!0);try{(await fetch("http://localhost:5002/api/white-label/domain",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({domain:s.customDomain})})).ok?r.success("Custom domain setup initiated! Check your DNS settings."):r.error("Failed to setup custom domain")}catch{r.error("Error setting up custom domain")}finally{o(!1)}},y=async()=>{o(!0);try{const a=await fetch("http://localhost:5002/api/white-label/export",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.ok){const c=await a.blob(),f=window.URL.createObjectURL(c),m=document.createElement("a");m.href=f,m.download=`${s.companyName||"white-label"}-app.zip`,m.click(),r.success("White-label app exported successfully!")}else r.error("Failed to export white-label app")}catch{r.error("Error exporting white-label app")}finally{o(!1)}},N=[{id:"branding",label:"Branding",icon:p},{id:"domain",label:"Domain",icon:x},{id:"export",label:"Export",icon:h}];return e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsx(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-xl p-8 text-white mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(v,{className:"h-8 w-8 text-yellow-400"}),e.jsx("h1",{className:"text-3xl font-bold",children:"White-Label Platform"})]}),e.jsx("p",{className:"text-blue-200 text-lg",children:"Transform NeuroColony into your own branded platform with complete customization"})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"bg-white/10 rounded-lg p-4",children:[e.jsx(w,{className:"h-6 w-6 text-yellow-400 mx-auto mb-2"}),e.jsx("div",{className:"text-sm text-blue-200",children:"Enterprise Feature"}),e.jsx("div",{className:"font-bold",children:"Full White-Label"})]})})]})}),e.jsx("div",{className:"flex border-b border-gray-200 mb-8",children:N.map(a=>e.jsxs("button",{onClick:()=>u(a.id),className:`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${n===a.id?"border-b-2 border-purple-600 text-purple-600":"text-gray-600 hover:text-purple-600"}`,children:[e.jsx(a.icon,{className:"h-5 w-5"}),a.label]},a.id))}),n==="branding"&&e.jsxs(d.div,{initial:{opacity:0},animate:{opacity:1},className:"grid lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center gap-2",children:[e.jsx(p,{className:"h-6 w-6 text-purple-600"}),"Brand Colors"]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Primary"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"color",value:s.primaryColor,onChange:a=>l({...s,primaryColor:a.target.value}),className:"w-12 h-12 rounded-lg border-2 border-gray-300"}),e.jsx("input",{type:"text",value:s.primaryColor,onChange:a=>l({...s,primaryColor:a.target.value}),className:"flex-1 p-2 border rounded-lg"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Secondary"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"color",value:s.secondaryColor,onChange:a=>l({...s,secondaryColor:a.target.value}),className:"w-12 h-12 rounded-lg border-2 border-gray-300"}),e.jsx("input",{type:"text",value:s.secondaryColor,onChange:a=>l({...s,secondaryColor:a.target.value}),className:"flex-1 p-2 border rounded-lg"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Accent"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"color",value:s.accentColor,onChange:a=>l({...s,accentColor:a.target.value}),className:"w-12 h-12 rounded-lg border-2 border-gray-300"}),e.jsx("input",{type:"text",value:s.accentColor,onChange:a=>l({...s,accentColor:a.target.value}),className:"flex-1 p-2 border rounded-lg"})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Company Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Company Name"}),e.jsx("input",{type:"text",value:s.companyName,onChange:a=>l({...s,companyName:a.target.value}),className:"w-full p-3 border rounded-lg",placeholder:"Your Company Name"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Support Email"}),e.jsx("input",{type:"email",value:s.supportEmail,onChange:a=>l({...s,supportEmail:a.target.value}),className:"w-full p-3 border rounded-lg",placeholder:"<EMAIL>"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Logo URL"}),e.jsx("input",{type:"url",value:s.logoUrl,onChange:a=>l({...s,logoUrl:a.target.value}),className:"w-full p-3 border rounded-lg",placeholder:"https://yourcompany.com/logo.png"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Footer Text"}),e.jsx("input",{type:"text",value:s.footerText,onChange:a=>l({...s,footerText:a.target.value}),className:"w-full p-3 border rounded-lg",placeholder:"© 2025 Your Company"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:s.hidePoweredBy,onChange:a=>l({...s,hidePoweredBy:a.target.checked}),className:"w-4 h-4"}),e.jsx("label",{className:"text-sm",children:'Hide "Powered by NeuroColony"'})]})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center gap-2",children:[e.jsx(C,{className:"h-6 w-6 text-purple-600"}),"Live Preview"]}),e.jsxs("div",{className:"border-2 border-gray-200 rounded-lg p-4 min-h-[300px]",style:{backgroundColor:s.primaryColor+"10",borderColor:s.primaryColor},children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded-lg mb-4",style:{backgroundColor:s.primaryColor,color:"white"},children:[s.logoUrl?e.jsx("img",{src:s.logoUrl,alt:"Logo",className:"h-8 w-auto"}):e.jsx("div",{className:"w-8 h-8 bg-white/20 rounded"}),e.jsxs("span",{className:"font-bold",children:[s.companyName||"Your Company"," AI"]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-3 rounded-lg",style:{backgroundColor:s.secondaryColor+"20"},children:[e.jsx("div",{className:"font-medium",children:"Dashboard"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Your branded dashboard"})]}),e.jsxs("div",{className:"p-3 rounded-lg",style:{backgroundColor:s.accentColor+"20"},children:[e.jsx("div",{className:"font-medium",children:"Email Generator"}),e.jsx("div",{className:"text-sm text-gray-600",children:"AI-powered email sequences"})]})]}),e.jsx("div",{className:"mt-4 p-2 text-xs text-center text-gray-500 border-t",children:s.hidePoweredBy?s.footerText:`${s.footerText} | Powered by NeuroColony`})]})]}),e.jsx("button",{onClick:b,disabled:t,className:"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center gap-2",children:t?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"h-5 w-5"}),"Save Branding"]})})]})]}),n==="domain"&&e.jsx(d.div,{initial:{opacity:0},animate:{opacity:1},className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[e.jsxs("h3",{className:"text-2xl font-bold mb-6 flex items-center gap-2",children:[e.jsx(x,{className:"h-8 w-8 text-blue-600"}),"Custom Domain Setup"]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-lg font-medium mb-3",children:"Your Custom Domain"}),e.jsx("input",{type:"text",value:s.customDomain,onChange:a=>l({...s,customDomain:a.target.value}),className:"w-full p-4 border-2 border-gray-300 rounded-lg text-lg",placeholder:"app.yourcompany.com"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Enter the domain where you want to host your white-labeled app"})]}),e.jsxs("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 rounded",children:[e.jsx("h4",{className:"font-bold text-blue-900 mb-2",children:"DNS Configuration Required"}),e.jsx("p",{className:"text-blue-800 mb-3",children:"After setup, you'll need to configure these DNS records:"}),e.jsxs("div",{className:"bg-white p-3 rounded border font-mono text-sm",children:[e.jsx("div",{children:"Type: CNAME"}),e.jsxs("div",{children:["Name: ",s.customDomain||"app.yourcompany.com"]}),e.jsx("div",{children:"Value: sequenceai-white-label.herokuapp.com"})]})]}),e.jsx("button",{onClick:j,disabled:t||!s.customDomain,className:"bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50",children:t?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"Setting up..."]}):e.jsxs(e.Fragment,{children:[e.jsx(k,{className:"h-5 w-5"}),"Setup Custom Domain"]})})]})]})}),n==="export"&&e.jsx(d.div,{initial:{opacity:0},animate:{opacity:1},className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[e.jsxs("h3",{className:"text-2xl font-bold mb-6 flex items-center gap-2",children:[e.jsx(h,{className:"h-8 w-8 text-green-600"}),"Export White-Label App"]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 p-6 rounded-lg",children:[e.jsx("h4",{className:"font-bold text-green-900 mb-3",children:"Ready to Deploy!"}),e.jsx("p",{className:"text-green-800 mb-4",children:"Export your fully customized white-label application with all your branding applied. The exported package includes:"}),e.jsxs("ul",{className:"text-green-800 space-y-1 mb-4",children:[e.jsx("li",{children:"• Complete React application with your branding"}),e.jsx("li",{children:"• Backend API configured for your domain"}),e.jsx("li",{children:"• Docker deployment configuration"}),e.jsx("li",{children:"• SSL certificates and security setup"}),e.jsx("li",{children:"• Database migration scripts"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[e.jsx("h5",{className:"font-bold mb-3",children:"What's Included"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-700",children:[e.jsx("li",{children:"✓ Frontend with your branding"}),e.jsx("li",{children:"✓ Backend API server"}),e.jsx("li",{children:"✓ Database setup"}),e.jsx("li",{children:"✓ Docker configuration"}),e.jsx("li",{children:"✓ Deployment guide"})]})]}),e.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg",children:[e.jsx("h5",{className:"font-bold mb-3",children:"System Requirements"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-700",children:[e.jsx("li",{children:"• Node.js 18+ and npm"}),e.jsx("li",{children:"• MongoDB 5.0+"}),e.jsx("li",{children:"• Docker (recommended)"}),e.jsx("li",{children:"• SSL certificate for domain"}),e.jsx("li",{children:"• 2GB+ RAM, 10GB storage"})]})]})]}),e.jsx("button",{onClick:y,disabled:t,className:"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg transition-all flex items-center justify-center gap-3 text-lg",children:t?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-white"}),"Exporting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(S,{className:"h-6 w-6"}),"Export Complete White-Label App"]})})]})]})})]})};export{F as default};
