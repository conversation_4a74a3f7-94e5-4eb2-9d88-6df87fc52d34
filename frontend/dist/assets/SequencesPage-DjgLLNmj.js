import{k as w,r as n,V as i,j as e,m as r,L as h}from"./index-CVpabiD8.js";import{P as u}from"./plus-CLzd-zUl.js";import{L as g}from"./loader-2-D2DNDVF4.js";import{F as p}from"./file-text-Dp_9XYM2.js";import{T as S}from"./trash-2-CS_FJ-_W.js";import{T as C}from"./tag-B82phq9Z.js";import{C as F}from"./calendar-DZH_gkxY.js";const P=()=>{const{getApiClient:c}=w(),[a,d]=n.useState([]),[l,o]=n.useState(!0),[m,x]=n.useState(null);n.useEffect(()=>{b()},[]);const b=async()=>{o(!0);try{const s=await c().get("/sequences");s.data.success?d(s.data.sequences):i.error("Failed to load sequences")}catch(t){console.error("Failed to load sequences:",t),i.error("Failed to load sequences")}finally{o(!1)}},y=async t=>{if(confirm("Are you sure you want to delete this sequence?")){x(t);try{(await c().delete(`/sequences/${t}`)).data.success?(d(f=>f.filter(v=>v._id!==t)),i.success("Sequence deleted successfully")):i.error("Failed to delete sequence")}catch(s){console.error("Failed to delete sequence:",s),i.error("Failed to delete sequence")}finally{x(null)}}},j=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),N=t=>{switch(t){case"draft":return"bg-gray-500/20 text-gray-300 border-gray-500/30";case"active":return"bg-green-500/20 text-green-300 border-green-500/30";case"paused":return"bg-yellow-500/20 text-yellow-300 border-yellow-500/30";case"completed":return"bg-blue-500/20 text-blue-300 border-blue-500/30";default:return"bg-gray-500/20 text-gray-300 border-gray-500/30"}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-neutral-950 via-neutral-900 to-purple-950 p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs(r.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"Email Sequences"}),e.jsx("p",{className:"text-gray-300",children:"Manage and view your generated email sequences"})]}),e.jsxs(h,{to:"/generator-simple",className:"bg-gradient-to-r from-amber-400 to-orange-500 text-black font-bold py-3 px-6 rounded-lg hover:from-amber-500 hover:to-orange-600 transition-all duration-200 flex items-center",children:[e.jsx(u,{className:"h-5 w-5 mr-2"}),"Create New Sequence"]})]}),l&&e.jsxs(r.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12",children:[e.jsx(g,{className:"h-12 w-12 text-amber-400 mx-auto mb-4 animate-spin"}),e.jsx("p",{className:"text-gray-300",children:"Loading your sequences..."})]}),!l&&a.length===0&&e.jsxs(r.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-16",children:[e.jsx(p,{className:"h-24 w-24 text-gray-400 mx-auto mb-6"}),e.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"No sequences yet"}),e.jsx("p",{className:"text-gray-400 mb-8 max-w-md mx-auto",children:"Create your first email sequence to get started with automated email marketing"}),e.jsxs(h,{to:"/generator-simple",className:"bg-gradient-to-r from-amber-400 to-orange-500 text-black font-bold py-3 px-6 rounded-lg hover:from-amber-500 hover:to-orange-600 transition-all duration-200 inline-flex items-center",children:[e.jsx(u,{className:"h-5 w-5 mr-2"}),"Create Your First Sequence"]})]}),!l&&a.length>0&&e.jsx(r.div,{initial:{opacity:0},animate:{opacity:1},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map((t,s)=>e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:border-white/30 transition-all duration-200",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg font-bold text-white truncate mb-1",children:t.title}),e.jsx("p",{className:"text-gray-400 text-sm line-clamp-2",children:t.description})]}),e.jsx("div",{className:"flex space-x-2 ml-4",children:e.jsx("button",{onClick:()=>y(t._id),disabled:m===t._id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-all duration-200 disabled:opacity-50",children:m===t._id?e.jsx(g,{className:"h-4 w-4 animate-spin"}):e.jsx(S,{className:"h-4 w-4"})})})]}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-4 text-sm",children:[e.jsxs("div",{className:"flex items-center text-blue-400",children:[e.jsx(p,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:[t.emails?.length||0," emails"]})]}),e.jsxs("div",{className:"flex items-center text-amber-400",children:[e.jsx(C,{className:"h-4 w-4 mr-1"}),e.jsx("span",{className:"capitalize",children:t.industry})]})]}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full border ${N(t.status)}`,children:t.status})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),e.jsxs("span",{children:["Created ",j(t.createdAt)]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"capitalize",children:["Tone: ",t.tone]}),e.jsx("span",{className:"text-xs bg-white/10 px-2 py-1 rounded",children:t.aiModel})]})]}),t.emails&&t.emails.length>0&&e.jsxs("div",{className:"mt-4 pt-4 border-t border-white/10",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"First email preview:"}),e.jsx("p",{className:"text-sm text-white font-medium truncate",children:t.emails[0].subject}),e.jsx("p",{className:"text-xs text-gray-400 line-clamp-2 mt-1",children:t.emails[0].content})]})]},t._id))}),!l&&a.length>0&&e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-12 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:" Your Statistics"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-amber-400",children:a.length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Sequences"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:a.reduce((t,s)=>t+(s.emails?.length||0),0)}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Emails"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:a.filter(t=>t.status==="active").length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:a.filter(t=>t.status==="draft").length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Drafts"})]})]})]})]})})};export{P as default};
