import{c as v,k as y,r as c,j as e,a0 as w,m as o,a3 as C,b as P,a2 as S,a4 as T,a5 as A,E as U,f as E,A as D}from"./index-CVpabiD8.js";import{P as k}from"./palette-BJ27JRb6.js";import{S as F}from"./save-D4vIjgOV.js";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=v("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),I=()=>{const{user:d}=y(),[t,x]=c.useState("profile"),[n,p]=c.useState(!1),[a,u]=c.useState({profile:{name:d?.name||"",email:d?.email||"",company:"",timezone:"UTC-8",avatar:null},notifications:{email:!0,usage:!0,billing:!0,marketing:!1,security:!0},security:{twoFactor:!1,loginAlerts:!0,dataEncryption:!0},preferences:{theme:"dark",language:"en",dateFormat:"MM/DD/YYYY",autoSave:!0}}),h=[{id:"profile",label:"Profile",icon:C},{id:"notifications",label:"Notifications",icon:P},{id:"security",label:"Security",icon:S},{id:"preferences",label:"Preferences",icon:k},{id:"data",label:"Data & Privacy",icon:T}],f=()=>{console.log("Saving settings:",a)},l=(s,i,r)=>{u(m=>({...m,[s]:{...m[s],[i]:r}}))},j=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Full Name"}),e.jsx("input",{type:"text",className:"form-input",value:a.profile.name,onChange:s=>l("profile","name",s.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Email Address"}),e.jsx("input",{type:"email",className:"form-input",value:a.profile.email,onChange:s=>l("profile","email",s.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Company"}),e.jsx("input",{type:"text",className:"form-input",value:a.profile.company,onChange:s=>l("profile","company",s.target.value),placeholder:"Your company name"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Timezone"}),e.jsxs("select",{className:"form-input",value:a.profile.timezone,onChange:s=>l("profile","timezone",s.target.value),children:[e.jsx("option",{value:"UTC-8",children:"Pacific Time (UTC-8)"}),e.jsx("option",{value:"UTC-5",children:"Eastern Time (UTC-5)"}),e.jsx("option",{value:"UTC+0",children:"UTC"}),e.jsx("option",{value:"UTC+1",children:"Central European Time (UTC+1)"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-purple-500 to-amber-400 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:a.profile.name.charAt(0).toUpperCase()}),e.jsxs("div",{children:[e.jsxs("button",{className:"btn btn-outline mb-2",children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Upload New"]}),e.jsx("p",{className:"text-sm text-neutral-400",children:"JPG, PNG up to 5MB"})]})]})]})]}),b=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Email Notifications"}),e.jsx("div",{className:"space-y-4",children:Object.entries(a.notifications).map(([s,i])=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-neutral-800/30 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium capitalize",children:s==="email"?"General Updates":s==="usage"?"Usage Alerts":s==="billing"?"Billing Notifications":s==="marketing"?"Marketing & News":"Security Alerts"}),e.jsx("div",{className:"text-sm text-neutral-400",children:s==="email"?"Product updates and announcements":s==="usage"?"When approaching usage limits":s==="billing"?"Invoices and payment updates":s==="marketing"?"Tips, tutorials, and special offers":"Login alerts and security notifications"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer",checked:i,onChange:r=>l("notifications",s,r.target.checked)}),e.jsx("div",{className:"w-11 h-6 bg-neutral-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"})]})]},s))})]})}),g=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Authentication"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-neutral-800/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-white font-medium",children:"Two-Factor Authentication"}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${a.security.twoFactor?"bg-green-500/20 text-green-300":"bg-neutral-600/20 text-neutral-400"}`,children:a.security.twoFactor?"Enabled":"Disabled"})]}),e.jsx("p",{className:"text-sm text-neutral-400 mb-3",children:"Add an extra layer of security to your account"}),e.jsx("button",{className:"btn btn-outline",children:a.security.twoFactor?"Disable 2FA":"Enable 2FA"})]}),e.jsxs("div",{className:"p-4 bg-neutral-800/30 rounded-lg",children:[e.jsx("div",{className:"text-white font-medium mb-2",children:"Change Password"}),e.jsxs("div",{className:"grid gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Current Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:n?"text":"password",className:"form-input pr-10",placeholder:"Enter current password"}),e.jsx("button",{onClick:()=>p(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-white",children:n?e.jsx(A,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"New Password"}),e.jsx("input",{type:"password",className:"form-input",placeholder:"Enter new password"})]}),e.jsx("button",{className:"btn btn-primary w-fit",children:"Update Password"})]})]})]})]})}),N=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-neutral-800/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(E,{className:"w-5 h-5 text-blue-400"}),e.jsx("span",{className:"text-white font-medium",children:"Export Data"})]}),e.jsx("p",{className:"text-sm text-neutral-400 mb-3",children:"Download all your sequences, templates, and account data"}),e.jsx("button",{className:"btn btn-outline",children:"Export My Data"})]}),e.jsxs("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(D,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-300 font-medium",children:"Delete Account"})]}),e.jsx("p",{className:"text-sm text-red-200 mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx("button",{className:"btn bg-red-600 hover:bg-red-700 text-white",children:"Delete Account"})]})]})]})});return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-neutral-950 to-neutral-900",children:[e.jsx(w,{}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pt-24 pb-8",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:e.jsx("span",{className:"gradient-text",children:"Account Settings"})}),e.jsx("p",{className:"text-xl text-neutral-300",children:"Manage your account, preferences, and security settings"})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24",children:e.jsxs("div",{className:"grid lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx(o.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},className:"card-premium",children:e.jsx("nav",{className:"space-y-2",children:h.map(s=>{const i=s.icon;return e.jsxs("button",{onClick:()=>x(s.id),className:`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${t===s.id?"bg-purple-500/20 text-purple-300 border-purple-500/30":"text-neutral-300 hover:bg-neutral-800/50 hover:text-white"}`,children:[e.jsx(i,{className:"w-5 h-5"}),s.label]},s.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs(o.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3},className:"card-premium",children:[t==="profile"&&j(),t==="notifications"&&b(),t==="security"&&g(),t==="data"&&N(),t==="preferences"&&e.jsx("div",{className:"text-center text-neutral-400 py-12",children:"Preferences settings coming soon"}),e.jsx("div",{className:"mt-8 pt-6 border-t border-neutral-700 flex justify-end",children:e.jsxs("button",{onClick:f,className:"btn-premium ripple flex items-center gap-2",children:[e.jsx(F,{className:"w-4 h-4"}),"Save Changes"]})})]})})]})})]})]})};export{I as default};
