import{r as m,j as e,m as n,B as Z,S as O,i as Q,h as X,d as _,O as w,y as ee}from"./index-CVpabiD8.js";/* empty css                 */import{W as $}from"./wand-2-DA9i-8H2.js";import{L as se}from"./languages-B8P6yi0c.js";import{T as B}from"./trophy-BLwcuC8U.js";import{M as q}from"./mic-DfrRXnef.js";import{M as te}from"./mic-off-BtFHUvjE.js";import{L as ae}from"./loader-2-D2DNDVF4.js";import{C as S}from"./copy-WUF2H833.js";const he=({user:ie})=>{const[l,L]=m.useState("generate"),[y,c]=m.useState(!1),[a,h]=m.useState(null),[z,i]=m.useState(null),[g,C]=m.useState({}),[o,A]=m.useState({subject:"",body:""}),[r,j]=m.useState({industry:"",productService:"",targetAudience:"",pricePoint:"",tone:"professional"}),[u,P]=m.useState([]),[x,k]=m.useState({subject:"",body:""}),[b,E]=m.useState(!1),[T,M]=m.useState(null),[N,I]=m.useState(null),R=[{id:"generate",icon:$,title:"AI Email Generation",description:"Generate complete email sequences with AI",color:"from-purple-500 to-blue-500"},{id:"translate",icon:se,title:"Multi-Language",description:"Translate and adapt emails for global markets",color:"from-blue-500 to-cyan-500"},{id:"predict",icon:Z,title:"Performance Prediction",description:"Predict email success before sending",color:"from-cyan-500 to-green-500"},{id:"optimize",icon:O,title:"Subject Line Optimizer",description:"Create high-converting subject lines",color:"from-green-500 to-yellow-500"},{id:"analyze",icon:B,title:"Competitor Analysis",description:"Analyze and outperform competitor emails",color:"from-yellow-500 to-red-500"}],f=[{code:"es",name:"Spanish",flag:"🇪🇸"},{code:"fr",name:"French",flag:"🇫🇷"},{code:"de",name:"German",flag:"🇩🇪"},{code:"ja",name:"Japanese",flag:"🇯🇵"},{code:"pt",name:"Portuguese",flag:"🇵🇹"},{code:"zh",name:"Chinese",flag:"🇨🇳"},{code:"it",name:"Italian",flag:"🇮🇹"},{code:"ko",name:"Korean",flag:"🇰🇷"}],F=async()=>{try{const s=await navigator.mediaDevices.getUserMedia({audio:!0}),t=new MediaRecorder(s),d=[];t.ondataavailable=p=>d.push(p.data),t.onstop=()=>{const p=new Blob(d,{type:"audio/webm"});I(p),s.getTracks().forEach(U=>U.stop())},M(t),t.start(),E(!0)}catch(s){console.error("Recording error:",s),i("Microphone access denied")}},G=()=>{T&&b&&(T.stop(),E(!1))},V=async()=>{if(N){c(!0),i(null);try{const s=new FormData;s.append("audio",N),s.append("businessInfo",JSON.stringify(r));const t=await api.post("/ai/voice-to-email",s,{headers:{"Content-Type":"multipart/form-data"}});h(t.data),I(null)}catch(s){i(s.response?.data?.error||"Voice processing failed")}finally{c(!1)}}},D=async()=>{c(!0),i(null);try{const s=await api.post("/sequences/generate",{businessInfo:r,settings:{sequenceLength:7,tone:r.tone,primaryGoal:"conversion",includeCTA:!0,includePersonalization:!0}});h({type:"sequence",data:s.data.sequence})}catch(s){i(s.response?.data?.error||"Generation failed")}finally{c(!1)}},W=async()=>{if(!o.subject||!o.body||u.length===0){i("Please provide email content and select languages");return}c(!0),i(null);try{const s=await api.post("/ai-advanced/translate",{emailContent:o,targetLanguages:u});h({type:"translations",data:s.data.translations})}catch(s){i(s.response?.data?.error||"Translation failed")}finally{c(!1)}},Y=async()=>{if(!o.subject||!o.body){i("Please provide email subject and body");return}c(!0),i(null);try{const s=await api.post("/ai-advanced/predict-performance",o);h({type:"prediction",data:s.data.prediction})}catch(s){i(s.response?.data?.error||"Prediction failed")}finally{c(!1)}},H=async()=>{if(!o.subject){i("Please provide a subject line");return}c(!0),i(null);try{const s=await api.post("/ai-advanced/optimize-subject",{originalSubject:o.subject,context:r});h({type:"optimization",data:s.data.optimization})}catch(s){i(s.response?.data?.error||"Optimization failed")}finally{c(!1)}},J=async()=>{if(!x.subject||!x.body){i("Please provide competitor email content");return}c(!0),i(null);try{const s=await api.post("/ai-advanced/analyze-competitor",{competitorEmail:x,yourBrand:r});h({type:"analysis",data:s.data.analysis})}catch(s){i(s.response?.data?.error||"Analysis failed")}finally{c(!1)}},v=async(s,t)=>{await navigator.clipboard.writeText(s),C({...g,[t]:!0}),setTimeout(()=>{C({...g,[t]:!1})},2e3)},K=()=>{if(!a)return null;switch(a.type){case"sequence":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Generated Email Sequence"}),a.data.emails.map((s,t)=>e.jsxs(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},className:"glass-card p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h4",{className:"text-xl font-semibold text-white",children:["Email ",t+1," - Day ",s.dayDelay]}),e.jsx("button",{onClick:()=>v(`Subject: ${s.subject}

${s.body}`,`email-${t}`),className:"p-2 hover:bg-white/10 rounded-lg transition-colors",children:g[`email-${t}`]?e.jsx(w,{size:20,className:"text-green-400"}):e.jsx(S,{size:20,className:"text-gray-400"})})]}),e.jsxs("p",{className:"text-purple-400 mb-2",children:["Subject: ",s.subject]}),e.jsx("p",{className:"text-gray-300 whitespace-pre-wrap",children:s.body}),s.psychologyTriggers&&e.jsx("div",{className:"mt-4 flex flex-wrap gap-2",children:s.psychologyTriggers.map((d,p)=>e.jsx("span",{className:"px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full text-xs",children:d},p))})]},t))]});case"translations":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Translated Emails"}),Object.entries(a.data).map(([s,t])=>e.jsxs(n.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"glass-card p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h4",{className:"text-xl font-semibold text-white flex items-center",children:[e.jsx("span",{className:"text-2xl mr-2",children:f.find(d=>d.code===s)?.flag}),f.find(d=>d.code===s)?.name]}),e.jsx("button",{onClick:()=>v(`Subject: ${t.subject}

${t.body}`,`trans-${s}`),className:"p-2 hover:bg-white/10 rounded-lg transition-colors",children:g[`trans-${s}`]?e.jsx(w,{size:20,className:"text-green-400"}):e.jsx(S,{size:20,className:"text-gray-400"})})]}),e.jsxs("p",{className:"text-blue-400 mb-2",children:["Subject: ",t.subject]}),e.jsx("p",{className:"text-gray-300 whitespace-pre-wrap mb-4",children:t.body}),t.culturalNotes&&e.jsxs("div",{className:"mt-4 p-4 bg-blue-500/10 rounded-lg",children:[e.jsx("p",{className:"text-blue-400 font-semibold mb-2",children:"Cultural Notes:"}),e.jsx("ul",{className:"text-gray-300 space-y-1",children:t.culturalNotes.map((d,p)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-400 mr-2",children:"•"}),d]},p))})]}),t.bestSendTime&&e.jsxs("p",{className:"text-gray-400 mt-2",children:["Best send time: ",t.bestSendTime]})]},s))]});case"prediction":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Performance Prediction"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Object.entries(a.data.predictedMetrics).map(([s,t])=>e.jsxs(n.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},className:"glass-card p-4 text-center",children:[e.jsx("p",{className:"text-gray-400 text-sm capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),e.jsx("p",{className:"text-2xl font-bold text-white mt-1",children:t})]},s))}),a.data.insights.length>0&&e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("h4",{className:"text-xl font-semibold text-white mb-4",children:"AI Insights"}),e.jsx("ul",{className:"space-y-2",children:a.data.insights.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx(O,{size:16,className:"text-yellow-400 mr-2 mt-0.5"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]}),a.data.recommendations.length>0&&e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("h4",{className:"text-xl font-semibold text-white mb-4",children:"Recommendations"}),e.jsx("ul",{className:"space-y-2",children:a.data.recommendations.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx(ee,{size:16,className:"text-green-400 mr-2 mt-0.5"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]})]});case"optimization":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Optimized Subject Lines"}),e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:"Original:"}),e.jsx("p",{className:"text-white text-lg mb-4",children:a.data.original}),e.jsx("p",{className:"text-gray-400 mb-4",children:"AI-Optimized Variations:"}),e.jsx("div",{className:"space-y-3",children:a.data.variations.map((s,t)=>e.jsxs(n.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:t*.1},className:"flex items-center justify-between p-4 bg-white/5 rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-white",children:s.text}),e.jsxs("div",{className:"flex items-center mt-2 space-x-4",children:[e.jsxs("span",{className:"text-xs text-purple-400",children:[s.trigger," trigger"]}),e.jsxs("span",{className:"text-xs text-green-400",children:[(s.predictedOpenRate*100).toFixed(1),"% predicted open rate"]})]})]}),e.jsx("button",{onClick:()=>v(s.text,`opt-${t}`),className:"p-2 hover:bg-white/10 rounded-lg transition-colors ml-4",children:g[`opt-${t}`]?e.jsx(w,{size:20,className:"text-green-400"}):e.jsx(S,{size:20,className:"text-gray-400"})})]},t))}),a.data.winner&&e.jsxs("div",{className:"mt-6 p-4 bg-green-500/20 rounded-lg border border-green-500/30",children:[e.jsx("p",{className:"text-green-400 font-semibold mb-1",children:"🏆 Recommended Winner:"}),e.jsx("p",{className:"text-white",children:a.data.winner.text})]})]})]});case"analysis":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Competitor Analysis"}),a.data.strengths.length>0&&e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("h4",{className:"text-xl font-semibold text-green-400 mb-4",children:"Competitor Strengths"}),e.jsx("ul",{className:"space-y-2",children:a.data.strengths.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]}),a.data.weaknesses.length>0&&e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("h4",{className:"text-xl font-semibold text-red-400 mb-4",children:"Competitor Weaknesses"}),e.jsx("ul",{className:"space-y-2",children:a.data.weaknesses.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-red-400 mr-2",children:"✗"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]}),a.data.opportunities.length>0&&e.jsxs("div",{className:"glass-card p-6",children:[e.jsx("h4",{className:"text-xl font-semibold text-blue-400 mb-4",children:"Your Opportunities"}),e.jsx("ul",{className:"space-y-2",children:a.data.opportunities.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-blue-400 mr-2",children:"→"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]}),a.data.recommendations.length>0&&e.jsxs("div",{className:"glass-card p-6 bg-purple-500/10 border border-purple-500/30",children:[e.jsx("h4",{className:"text-xl font-semibold text-purple-400 mb-4",children:"Strategic Recommendations"}),e.jsx("ul",{className:"space-y-2",children:a.data.recommendations.map((s,t)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx(B,{size:16,className:"text-purple-400 mr-2 mt-0.5"}),e.jsx("span",{className:"text-gray-300",children:s})]},t))})]}),a.data.competitiveAdvantage&&e.jsxs("div",{className:"text-center p-4",children:[e.jsx("p",{className:"text-gray-400",children:"Competitive Advantage Score"}),e.jsxs("p",{className:"text-4xl font-bold holographic",children:[a.data.competitiveAdvantage,"/100"]})]})]});default:return null}};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 p-6",children:[e.jsx("div",{className:"particle-bg"}),e.jsxs(n.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"max-w-7xl mx-auto",children:[e.jsx("h1",{className:"text-5xl font-bold holographic mb-2 text-center",children:"Advanced AI Email Suite"}),e.jsx("p",{className:"text-gray-300 text-lg text-center mb-8",children:"Next-generation email intelligence powered by quantum AI"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:R.map(s=>e.jsxs(n.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>L(s.id),className:`glass-card p-4 text-center transition-all ${l===s.id?"border-2 border-purple-500 shadow-lg shadow-purple-500/30":""}`,children:[e.jsx("div",{className:`w-12 h-12 mx-auto mb-2 rounded-full bg-gradient-to-r ${s.color} flex items-center justify-center`,children:e.jsx(s.icon,{size:24,className:"text-white"})}),e.jsx("h3",{className:"text-white font-semibold text-sm",children:s.title}),e.jsx("p",{className:"text-gray-400 text-xs mt-1",children:s.description})]},s.id))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(n.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"glass-card p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:R.find(s=>s.id===l)?.title}),l==="generate"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Industry"}),e.jsx("input",{type:"text",value:r.industry,onChange:s=>j({...r,industry:s.target.value}),className:"neon-input w-full mt-1",placeholder:"e.g., SaaS, E-commerce, Healthcare"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Product/Service"}),e.jsx("input",{type:"text",value:r.productService,onChange:s=>j({...r,productService:s.target.value}),className:"neon-input w-full mt-1",placeholder:"e.g., Email Marketing Platform"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Target Audience"}),e.jsx("input",{type:"text",value:r.targetAudience,onChange:s=>j({...r,targetAudience:s.target.value}),className:"neon-input w-full mt-1",placeholder:"e.g., Small Business Owners"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Price Point"}),e.jsx("input",{type:"text",value:r.pricePoint,onChange:s=>j({...r,pricePoint:s.target.value}),className:"neon-input w-full mt-1",placeholder:"e.g., $29/month"})]}),e.jsxs("div",{className:"mt-6 p-4 bg-purple-500/10 rounded-lg",children:[e.jsxs("p",{className:"text-purple-400 mb-3 flex items-center",children:[e.jsx(q,{size:20,className:"mr-2"}),"Or use voice input (Beta)"]}),e.jsx("button",{onClick:b?G:F,className:`quantum-button ${b?"bg-red-500":""}`,children:b?e.jsxs(e.Fragment,{children:[e.jsx(te,{size:20,className:"mr-2"}),"Stop Recording"]}):e.jsxs(e.Fragment,{children:[e.jsx(q,{size:20,className:"mr-2"}),"Start Recording"]})}),N&&e.jsx("button",{onClick:V,className:"quantum-button ml-4",disabled:y,children:"Process Voice"})]})]}),(l==="translate"||l==="predict"||l==="optimize")&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Email Subject"}),e.jsx("input",{type:"text",value:o.subject,onChange:s=>A({...o,subject:s.target.value}),className:"neon-input w-full mt-1",placeholder:"Enter your email subject line"})]}),(l==="translate"||l==="predict")&&e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Email Body"}),e.jsx("textarea",{value:o.body,onChange:s=>A({...o,body:s.target.value}),className:"neon-input w-full mt-1 min-h-[200px]",placeholder:"Enter your email content"})]}),l==="translate"&&e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm mb-2 block",children:"Select Languages"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:f.map(s=>e.jsxs("label",{className:`flex items-center p-3 rounded-lg cursor-pointer transition-all ${u.includes(s.code)?"bg-purple-500/30 border border-purple-500":"bg-white/5 hover:bg-white/10"}`,children:[e.jsx("input",{type:"checkbox",checked:u.includes(s.code),onChange:t=>{t.target.checked?P([...u,s.code]):P(u.filter(d=>d!==s.code))},className:"sr-only"}),e.jsx("span",{className:"text-2xl mr-2",children:s.flag}),e.jsx("span",{className:"text-white text-sm",children:s.name})]},s.code))})]})]}),l==="analyze"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Competitor Email Subject"}),e.jsx("input",{type:"text",value:x.subject,onChange:s=>k({...x,subject:s.target.value}),className:"neon-input w-full mt-1",placeholder:"Enter competitor's subject line"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Competitor Email Body"}),e.jsx("textarea",{value:x.body,onChange:s=>k({...x,body:s.target.value}),className:"neon-input w-full mt-1 min-h-[200px]",placeholder:"Paste competitor's email content"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-gray-300 text-sm",children:"Your Brand Info (Optional)"}),e.jsx("input",{type:"text",value:r.industry,onChange:s=>j({...r,industry:s.target.value}),className:"neon-input w-full mt-1",placeholder:"Your industry/niche"})]})]}),z&&e.jsxs(n.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center",children:[e.jsx(Q,{size:20,className:"text-red-400 mr-2"}),e.jsx("span",{className:"text-red-400",children:z})]}),e.jsx(n.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{switch(l){case"generate":D();break;case"translate":W();break;case"predict":Y();break;case"optimize":H();break;case"analyze":J();break}},disabled:y,className:"quantum-button w-full mt-6 flex items-center justify-center",children:y?e.jsxs(e.Fragment,{children:[e.jsx(ae,{size:20,className:"mr-2 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx($,{size:20,className:"mr-2"}),l==="generate"&&"Generate Sequence",l==="translate"&&"Translate Email",l==="predict"&&"Predict Performance",l==="optimize"&&"Optimize Subject",l==="analyze"&&"Analyze Competitor"]})})]}),e.jsxs(n.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"glass-card p-8 max-h-[800px] overflow-y-auto",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Results"}),e.jsx(X,{mode:"wait",children:y?e.jsxs(n.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center h-64",children:[e.jsx("div",{className:"quantum-loader mb-4"}),e.jsx("p",{className:"text-gray-400",children:"AI is working its magic..."})]}):a?e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},children:K()}):e.jsxs(n.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center text-gray-400 py-16",children:[e.jsx(_,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"Your AI-powered results will appear here"})]})})]})]})]})]})};export{he as default};
