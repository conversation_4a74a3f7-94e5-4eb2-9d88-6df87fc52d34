#!/bin/bash

# NeuroColony Health Check Script
# Verifies all services are running correctly

echo "🏥 NeuroColony Health Check"
echo "=========================="
echo ""

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Health check functions
check_port() {
    local service=$1
    local port=$2
    if lsof -ti:$port > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service (port $port) - RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ $service (port $port) - NOT RUNNING${NC}"
        return 1
    fi
}

check_api_endpoint() {
    local name=$1
    local url=$2
    if curl -s -f -o /dev/null "$url" 2>/dev/null; then
        echo -e "${GREEN}✅ $name - ACCESSIBLE${NC}"
        return 0
    else
        echo -e "${RED}❌ $name - NOT ACCESSIBLE${NC}"
        return 1
    fi
}

check_file_exists() {
    local name=$1
    local file=$2
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $name - EXISTS${NC}"
        return 0
    else
        echo -e "${RED}❌ $name - MISSING${NC}"
        return 1
    fi
}

# Track overall health
HEALTH_SCORE=0
TOTAL_CHECKS=0

# Service checks
echo "1️⃣  Service Status"
echo "-------------------"
((TOTAL_CHECKS++))
if check_port "Backend API" 5002; then ((HEALTH_SCORE++)); fi

((TOTAL_CHECKS++))
if check_port "Frontend Dev Server" 3004; then ((HEALTH_SCORE++)); fi

echo ""

# API endpoint checks
echo "2️⃣  API Health"
echo "---------------"
((TOTAL_CHECKS++))
if check_api_endpoint "Backend Health" "http://localhost:5002/api/health"; then ((HEALTH_SCORE++)); fi

((TOTAL_CHECKS++))
if check_api_endpoint "Frontend Proxy" "http://localhost:3004/api/health"; then ((HEALTH_SCORE++)); fi

echo ""

# Configuration checks
echo "3️⃣  Configuration Files"
echo "-----------------------"
((TOTAL_CHECKS++))
if check_file_exists "Backend .env" "backend/.env"; then ((HEALTH_SCORE++)); fi

((TOTAL_CHECKS++))
if check_file_exists "Frontend .env" "frontend/.env"; then ((HEALTH_SCORE++)); fi

((TOTAL_CHECKS++))
if check_file_exists "Package.json (Backend)" "backend/package.json"; then ((HEALTH_SCORE++)); fi

((TOTAL_CHECKS++))
if check_file_exists "Package.json (Frontend)" "frontend/package.json"; then ((HEALTH_SCORE++)); fi

echo ""

# Database connectivity (if services are running)
echo "4️⃣  Database Connectivity"
echo "-------------------------"
if lsof -ti:5002 > /dev/null 2>&1; then
    ((TOTAL_CHECKS++))
    if curl -s http://localhost:5002/api/health | grep -q "mongodb.*connected" 2>/dev/null; then
        echo -e "${GREEN}✅ MongoDB - CONNECTED${NC}"
        ((HEALTH_SCORE++))
    else
        echo -e "${YELLOW}⚠️  MongoDB - FALLBACK MODE (in-memory)${NC}"
        ((HEALTH_SCORE++))  # Still counts as working in fallback mode
    fi
else
    echo -e "${YELLOW}⚠️  Cannot check - Backend not running${NC}"
fi

echo ""

# Component integrity checks
echo "5️⃣  Component Integrity"
echo "-----------------------"
# Check for the fixed import
if grep -q "import TemplateLibrary from '../components/TemplateLibrary'" frontend/src/pages/TemplatesPage.jsx 2>/dev/null; then
    echo -e "${GREEN}✅ TemplatesPage import - FIXED${NC}"
    ((HEALTH_SCORE++))
    ((TOTAL_CHECKS++))
else
    echo -e "${RED}❌ TemplatesPage import - BROKEN${NC}"
    ((TOTAL_CHECKS++))
fi

# Check ErrorBoundary exists
((TOTAL_CHECKS++))
if [ -f "frontend/src/components/ErrorBoundary.jsx" ]; then
    echo -e "${GREEN}✅ ErrorBoundary component - EXISTS${NC}"
    ((HEALTH_SCORE++))
else
    echo -e "${RED}❌ ErrorBoundary component - MISSING${NC}"
fi

echo ""

# Overall health score
echo "📊 Overall Health Score"
echo "----------------------"
PERCENTAGE=$((HEALTH_SCORE * 100 / TOTAL_CHECKS))

if [ $PERCENTAGE -ge 90 ]; then
    echo -e "${GREEN}🎉 System Health: $HEALTH_SCORE/$TOTAL_CHECKS ($PERCENTAGE%) - EXCELLENT${NC}"
elif [ $PERCENTAGE -ge 70 ]; then
    echo -e "${YELLOW}⚠️  System Health: $HEALTH_SCORE/$TOTAL_CHECKS ($PERCENTAGE%) - NEEDS ATTENTION${NC}"
else
    echo -e "${RED}🚨 System Health: $HEALTH_SCORE/$TOTAL_CHECKS ($PERCENTAGE%) - CRITICAL${NC}"
fi

echo ""

# Recommendations
if [ $PERCENTAGE -lt 100 ]; then
    echo "💡 Recommendations:"
    echo "-------------------"
    
    if ! lsof -ti:5002 > /dev/null 2>&1; then
        echo "• Start the backend: cd backend && npm start"
    fi
    
    if ! lsof -ti:3004 > /dev/null 2>&1; then
        echo "• Start the frontend: cd frontend && npm run dev"
    fi
    
    if ! [ -f "backend/.env" ] || ! [ -f "frontend/.env" ]; then
        echo "• Check environment configuration files"
    fi
    
    echo ""
    echo "Run './start-neurocolony-debug.sh' to start all services"
fi

echo ""