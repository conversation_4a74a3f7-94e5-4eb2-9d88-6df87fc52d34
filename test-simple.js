import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

async function testOllama() {
  try {
    console.log('🧪 Testing Ollama command execution...')
    
    const command = 'ollama run llama3.2:3b "Write one email subject line for an e-commerce store"'
    console.log('Running:', command)
    
    const { stdout, stderr } = await execAsync(command, { 
      timeout: 30000,
      maxBuffer: 1024 * 1024
    })

    if (stderr && !stderr.includes('pulling')) {
      console.log('⚠️ Stderr:', stderr)
    }

    console.log('✅ Success!')
    console.log('Response:', stdout.trim())
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

testOllama()