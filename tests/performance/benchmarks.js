import autocannon from 'autocannon';
import { describe, it, before, after } from 'mocha';
import { expect } from 'chai';
import { spawn } from 'child_process';
import mongoose from 'mongoose';
import Redis from 'ioredis';
import jwt from 'jsonwebtoken';

// Performance benchmarks for NeuroColony
describe('NeuroColony Performance Benchmarks', function() {
  this.timeout(300000); // 5 minutes for performance tests
  
  let serverProcess;
  let authToken;
  let redis;
  const BASE_URL = 'http://localhost:5000';
  
  before(async function() {
    // Start test server
    serverProcess = spawn('npm', ['run', 'start:test'], {
      env: { ...process.env, NODE_ENV: 'test', PORT: 5000 },
      detached: true,
    });
    
    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Setup test database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/neurocolony-perf');
    
    // Setup Redis
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
    });
    
    // Create test user and get auth token
    const User = mongoose.model('User');
    const testUser = await User.create({
      email: '<EMAIL>',
      password: 'PerfTest123!',
      subscription: {
        plan: 'business',
        status: 'active',
      },
    });
    
    authToken = jwt.sign(
      { userId: testUser._id, email: testUser.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });
  
  after(async function() {
    // Cleanup
    if (serverProcess) {
      process.kill(-serverProcess.pid);
    }
    await mongoose.connection.dropDatabase();
    await mongoose.disconnect();
    await redis.quit();
  });
  
  describe('API Endpoint Performance', function() {
    it('should handle 10,000 requests/second for agent listing', async function() {
      const result = await autocannon({
        url: `${BASE_URL}/api/agents`,
        connections: 100,
        pipelining: 10,
        duration: 30,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
      
      console.log('\n=== Agent Listing Performance ===');
      console.log(`Requests/sec: ${result.requests.average}`);
      console.log(`Latency (p50): ${result.latency.p50}ms`);
      console.log(`Latency (p95): ${result.latency.p95}ms`);
      console.log(`Latency (p99): ${result.latency.p99}ms`);
      console.log(`Errors: ${result.errors}`);
      console.log(`Timeouts: ${result.timeouts}`);
      
      // Performance assertions
      expect(result.requests.average).to.be.above(10000);
      expect(result.latency.p99).to.be.below(100);
      expect(result.errors).to.equal(0);
      expect(result.timeouts).to.equal(0);
    });
    
    it('should handle 5,000 concurrent agent executions', async function() {
      const payload = JSON.stringify({
        inputs: {
          productName: 'Performance Test Product',
          targetAudience: 'Developers',
          sequenceGoal: 'Product launch',
        },
      });
      
      const result = await autocannon({
        url: `${BASE_URL}/api/agents/email-sequence-generator/execute`,
        connections: 500,
        amount: 5000,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: payload,
        method: 'POST',
      });
      
      console.log('\n=== Agent Execution Performance ===');
      console.log(`Total requests: ${result.requests.sent}`);
      console.log(`Requests/sec: ${result.requests.average}`);
      console.log(`Latency (p50): ${result.latency.p50}ms`);
      console.log(`Latency (p95): ${result.latency.p95}ms`);
      console.log(`Latency (p99): ${result.latency.p99}ms`);
      console.log(`Errors: ${result.errors}`);
      
      // Performance assertions
      expect(result.requests.sent).to.equal(5000);
      expect(result.latency.p95).to.be.below(1000);
      expect(result.errors).to.be.below(result.requests.sent * 0.01); // <1% error rate
    });
    
    it('should maintain sub-50ms response time under sustained load', async function() {
      const result = await autocannon({
        url: `${BASE_URL}/api/health`,
        connections: 200,
        duration: 60,
        overallRate: 1000, // 1000 req/sec sustained
      });
      
      console.log('\n=== Sustained Load Performance ===');
      console.log(`Duration: 60 seconds`);
      console.log(`Target rate: 1000 req/sec`);
      console.log(`Actual rate: ${result.requests.average} req/sec`);
      console.log(`Latency (p50): ${result.latency.p50}ms`);
      console.log(`Latency (p99): ${result.latency.p99}ms`);
      
      expect(result.latency.p50).to.be.below(50);
      expect(result.latency.p99).to.be.below(100);
    });
  });
  
  describe('Database Performance', function() {
    it('should handle 50,000 agent execution writes/minute', async function() {
      const AgentExecution = mongoose.model('AgentExecution');
      const startTime = Date.now();
      const promises = [];
      
      for (let i = 0; i < 50000; i++) {
        promises.push(
          AgentExecution.create({
            executionId: `perf-test-${i}`,
            agentId: 'email-sequence-generator',
            userId: new mongoose.Types.ObjectId(),
            status: 'completed',
            inputs: { test: true },
            result: { sequences: [] },
            executionTime: Math.random() * 5000,
          })
        );
        
        // Batch inserts to avoid overwhelming
        if (promises.length >= 1000) {
          await Promise.all(promises);
          promises.length = 0;
        }
      }
      
      await Promise.all(promises);
      const duration = (Date.now() - startTime) / 1000;
      const writesPerSecond = 50000 / duration;
      
      console.log('\n=== Database Write Performance ===');
      console.log(`Total writes: 50,000`);
      console.log(`Duration: ${duration.toFixed(2)}s`);
      console.log(`Writes/second: ${writesPerSecond.toFixed(0)}`);
      
      expect(duration).to.be.below(60); // Should complete within 1 minute
      expect(writesPerSecond).to.be.above(800);
    });
    
    it('should query agent executions in under 10ms with indexes', async function() {
      const AgentExecution = mongoose.model('AgentExecution');
      const iterations = 1000;
      const latencies = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = process.hrtime.bigint();
        
        await AgentExecution.find({
          userId: new mongoose.Types.ObjectId(),
          status: 'completed',
          createdAt: { $gte: new Date(Date.now() - 86400000) },
        })
        .sort({ createdAt: -1 })
        .limit(20)
        .lean();
        
        const end = process.hrtime.bigint();
        const latency = Number(end - start) / 1_000_000; // Convert to ms
        latencies.push(latency);
      }
      
      const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      const p95Latency = latencies.sort((a, b) => a - b)[Math.floor(iterations * 0.95)];
      const p99Latency = latencies.sort((a, b) => a - b)[Math.floor(iterations * 0.99)];
      
      console.log('\n=== Database Query Performance ===');
      console.log(`Queries executed: ${iterations}`);
      console.log(`Average latency: ${avgLatency.toFixed(2)}ms`);
      console.log(`P95 latency: ${p95Latency.toFixed(2)}ms`);
      console.log(`P99 latency: ${p99Latency.toFixed(2)}ms`);
      
      expect(avgLatency).to.be.below(10);
      expect(p99Latency).to.be.below(20);
    });
  });
  
  describe('Cache Performance', function() {
    it('should achieve 95% cache hit rate for agent definitions', async function() {
      let hits = 0;
      let misses = 0;
      const totalRequests = 10000;
      const agentIds = [
        'email-sequence-generator',
        'subject-line-optimizer',
        'audience-segmentation',
        'send-time-optimizer',
        'content-personalizer',
      ];
      
      // Warm up cache
      for (const agentId of agentIds) {
        await redis.set(`agent:${agentId}`, JSON.stringify({ id: agentId, name: 'Test Agent' }));
      }
      
      // Simulate requests
      const start = Date.now();
      for (let i = 0; i < totalRequests; i++) {
        const agentId = agentIds[Math.floor(Math.random() * agentIds.length)];
        const cached = await redis.get(`agent:${agentId}`);
        
        if (cached) {
          hits++;
        } else {
          misses++;
          // Simulate cache miss - would normally fetch from DB
          await redis.set(`agent:${agentId}`, JSON.stringify({ id: agentId }), 'EX', 300);
        }
      }
      const duration = Date.now() - start;
      
      const hitRate = (hits / totalRequests) * 100;
      const opsPerSecond = totalRequests / (duration / 1000);
      
      console.log('\n=== Cache Performance ===');
      console.log(`Total operations: ${totalRequests}`);
      console.log(`Cache hits: ${hits}`);
      console.log(`Cache misses: ${misses}`);
      console.log(`Hit rate: ${hitRate.toFixed(2)}%`);
      console.log(`Operations/second: ${opsPerSecond.toFixed(0)}`);
      
      expect(hitRate).to.be.above(95);
      expect(opsPerSecond).to.be.above(50000);
    });
  });
  
  describe('Memory Usage', function() {
    it('should not exceed 512MB under load', async function() {
      const initialMemory = process.memoryUsage().rss / 1024 / 1024;
      console.log(`\n=== Memory Usage Test ===`);
      console.log(`Initial memory: ${initialMemory.toFixed(2)}MB`);
      
      // Generate load
      const promises = [];
      for (let i = 0; i < 1000; i++) {
        promises.push(
          fetch(`${BASE_URL}/api/agents`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
          })
        );
      }
      
      await Promise.all(promises);
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().rss / 1024 / 1024;
      const memoryIncrease = finalMemory - initialMemory;
      
      console.log(`Final memory: ${finalMemory.toFixed(2)}MB`);
      console.log(`Memory increase: ${memoryIncrease.toFixed(2)}MB`);
      
      expect(finalMemory).to.be.below(512);
      expect(memoryIncrease).to.be.below(100);
    });
  });
  
  describe('Concurrent User Simulation', function() {
    it('should handle 1,000 concurrent users performing mixed operations', async function() {
      const operations = [
        { method: 'GET', path: '/api/agents', weight: 40 },
        { method: 'GET', path: '/api/agents/email-sequence-generator', weight: 20 },
        { method: 'POST', path: '/api/agents/email-sequence-generator/execute', weight: 30 },
        { method: 'GET', path: '/api/agents/executions/running', weight: 10 },
      ];
      
      const results = await autocannon({
        url: BASE_URL,
        connections: 1000,
        duration: 120,
        requests: operations.map(op => ({
          method: op.method,
          path: op.path,
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: op.method === 'POST' ? JSON.stringify({
            inputs: {
              productName: 'Concurrent Test',
              targetAudience: 'Users',
              sequenceGoal: 'Testing',
            },
          }) : undefined,
          weight: op.weight,
        })),
      });
      
      console.log('\n=== Concurrent User Simulation ===');
      console.log(`Concurrent connections: 1,000`);
      console.log(`Duration: 120 seconds`);
      console.log(`Total requests: ${results.requests.sent}`);
      console.log(`Requests/sec: ${results.requests.average}`);
      console.log(`Success rate: ${((results.requests.sent - results.errors) / results.requests.sent * 100).toFixed(2)}%`);
      console.log(`Latency (p50): ${results.latency.p50}ms`);
      console.log(`Latency (p95): ${results.latency.p95}ms`);
      console.log(`Latency (p99): ${results.latency.p99}ms`);
      
      const successRate = (results.requests.sent - results.errors) / results.requests.sent;
      expect(successRate).to.be.above(0.99); // >99% success rate
      expect(results.latency.p95).to.be.below(500);
    });
  });
  
  describe('Scaling Performance', function() {
    it('should scale linearly with added resources', async function() {
      const testConfigs = [
        { connections: 100, duration: 30, label: 'Baseline (100 connections)' },
        { connections: 500, duration: 30, label: 'Medium load (500 connections)' },
        { connections: 1000, duration: 30, label: 'High load (1000 connections)' },
      ];
      
      const results = [];
      
      for (const config of testConfigs) {
        const result = await autocannon({
          url: `${BASE_URL}/api/agents`,
          connections: config.connections,
          duration: config.duration,
          headers: {
            'Authorization': `Bearer ${authToken}`,
          },
        });
        
        results.push({
          ...config,
          throughput: result.requests.average,
          latency: result.latency.p95,
        });
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
      
      console.log('\n=== Scaling Performance ===');
      results.forEach(r => {
        console.log(`${r.label}:`);
        console.log(`  Throughput: ${r.throughput.toFixed(0)} req/sec`);
        console.log(`  P95 Latency: ${r.latency}ms`);
      });
      
      // Check that throughput scales somewhat linearly
      const scalingFactor = results[2].throughput / results[0].throughput;
      console.log(`\nScaling factor (1000 vs 100 connections): ${scalingFactor.toFixed(2)}x`);
      
      expect(scalingFactor).to.be.above(5); // At least 5x throughput increase
      expect(results[2].latency).to.be.below(results[0].latency * 3); // Latency doesn't degrade too much
    });
  });
});

// Generate performance report
function generatePerformanceReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    summary: {
      totalTests: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
    },
    metrics: {
      api: {
        throughput: {
          average: results.filter(r => r.category === 'api').reduce((sum, r) => sum + r.throughput, 0) / results.filter(r => r.category === 'api').length,
          unit: 'requests/second',
        },
        latency: {
          p50: results.filter(r => r.category === 'api').reduce((sum, r) => sum + r.latency.p50, 0) / results.filter(r => r.category === 'api').length,
          p95: results.filter(r => r.category === 'api').reduce((sum, r) => sum + r.latency.p95, 0) / results.filter(r => r.category === 'api').length,
          p99: results.filter(r => r.category === 'api').reduce((sum, r) => sum + r.latency.p99, 0) / results.filter(r => r.category === 'api').length,
          unit: 'milliseconds',
        },
      },
      database: {
        writeSpeed: results.find(r => r.name === 'database-writes')?.writesPerSecond || 0,
        queryLatency: results.find(r => r.name === 'database-queries')?.avgLatency || 0,
        unit: 'operations/second',
      },
      cache: {
        hitRate: results.find(r => r.name === 'cache-performance')?.hitRate || 0,
        opsPerSecond: results.find(r => r.name === 'cache-performance')?.opsPerSecond || 0,
      },
    },
    recommendations: [],
  };
  
  // Add recommendations based on results
  if (report.metrics.api.latency.p99 > 100) {
    report.recommendations.push('Consider implementing response caching for frequently accessed endpoints');
  }
  
  if (report.metrics.database.queryLatency > 10) {
    report.recommendations.push('Review database indexes and query optimization');
  }
  
  if (report.metrics.cache.hitRate < 95) {
    report.recommendations.push('Increase cache TTL or implement cache warming strategies');
  }
  
  return report;
}