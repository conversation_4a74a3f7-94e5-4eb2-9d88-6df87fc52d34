#!/bin/bash

echo "🎨 PROJECT REBRANDING SCRIPT"
echo "==========================="

# Get the new name from user
if [ -z "$1" ]; then
    echo "Usage: ./rebrand.sh <NEW_NAME>"
    echo "Example: ./rebrand.sh NODE8"
    echo "Example: ./rebrand.sh 'NODE19 AI'"
    exit 1
fi

NEW_NAME="$1"
OLD_NAME="SequenceAI"

echo "🔄 Rebranding from '$OLD_NAME' to '$NEW_NAME'..."

# Function to replace text in files
replace_in_files() {
    local pattern="$1"
    local replacement="$2"
    local file_pattern="$3"
    
    echo "   Updating: $file_pattern"
    find . -name "$file_pattern" -type f -not -path "./node_modules/*" -not -path "./.git/*" -exec sed -i "s/$pattern/$replacement/g" {} +
}

# Replace in different file types
echo "📝 Updating source files..."
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.js"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.jsx"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.ts"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.tsx"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.json"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.md"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.html"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.yml"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.yaml"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.toml"
replace_in_files "$OLD_NAME" "$NEW_NAME" "*.env"

# Update specific important files
echo "🔧 Updating configuration files..."
replace_in_files "sequenceai" "${NEW_NAME,,}" "*.json"
replace_in_files "sequenceai" "${NEW_NAME,,}" "package.json"
replace_in_files "sequenceai" "${NEW_NAME,,}" "docker-compose.yml"

# Update Docker files
echo "🐳 Updating Docker configurations..."
replace_in_files "sequenceai" "${NEW_NAME,,}" "Dockerfile*"
replace_in_files "sequenceai" "${NEW_NAME,,}" "docker-compose*"

# Update deployment files
echo "🚀 Updating deployment configurations..."
replace_in_files "$OLD_NAME" "$NEW_NAME" "vercel.json"
replace_in_files "$OLD_NAME" "$NEW_NAME" "railway.toml"
replace_in_files "sequenceai" "${NEW_NAME,,}" "vercel.json"
replace_in_files "sequenceai" "${NEW_NAME,,}" "railway.toml"

echo "✅ Rebranding complete!"
echo "📋 Manual steps needed:"
echo "1. Update your domain in .env files"
echo "2. Update any hardcoded URLs"
echo "3. Update README.md title and description"
echo "4. Update package.json name and description"
echo "5. Test the application to ensure everything works"

echo ""
echo "🎯 Your project is now branded as: $NEW_NAME"