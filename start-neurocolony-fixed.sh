#!/bin/bash

# NeuroColony Fixed Startup Script
# Addresses MongoDB connection issues and ensures proper service startup

echo "🧠 NeuroColony AI Agent Platform - Fixed Startup Script"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        echo -e "${YELLOW}⚠️  Port $port is in use by PID $pid${NC}"
        echo -e "${YELLOW}🔄 Killing process...${NC}"
        kill -9 $pid 2>/dev/null
        sleep 1
        echo -e "${GREEN}✅ Port $port cleared${NC}"
    fi
}

echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js found: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js not found. Please install Node.js 18+${NC}"
    exit 1
fi

# Check MongoDB
echo -e "${BLUE}🔍 Checking MongoDB...${NC}"
if pgrep -x mongod > /dev/null; then
    echo -e "${GREEN}✅ MongoDB is running${NC}"
else
    echo -e "${YELLOW}⚠️ MongoDB is not running. Starting MongoDB...${NC}"
    # Try to start MongoDB
    if command_exists mongod; then
        mongod --fork --logpath /tmp/mongodb.log --dbpath /data/db 2>/dev/null || \
        mongod --fork --logpath /tmp/mongodb.log 2>/dev/null || \
        sudo systemctl start mongod 2>/dev/null || \
        echo -e "${RED}❌ Failed to start MongoDB. Please start it manually.${NC}"
    else
        echo -e "${RED}❌ MongoDB not installed. Please install MongoDB.${NC}"
        exit 1
    fi
    sleep 2
    if pgrep -x mongod > /dev/null; then
        echo -e "${GREEN}✅ MongoDB started successfully${NC}"
    else
        echo -e "${RED}❌ MongoDB failed to start${NC}"
        exit 1
    fi
fi

# Clear ports if needed
echo -e "${BLUE}🔍 Checking and clearing ports...${NC}"
kill_port 3004
kill_port 5002

# Fix the agentExecutionService if needed
echo -e "${BLUE}🔧 Applying MongoDB connection fixes...${NC}"
if [ -f "backend/services/agentExecutionService-fixed.js" ]; then
    cp backend/services/agentExecutionService.js backend/services/agentExecutionService-backup.js 2>/dev/null
    cp backend/services/agentExecutionService-fixed.js backend/services/agentExecutionService.js
    echo -e "${GREEN}✅ Applied MongoDB connection fix${NC}"
fi

# Install dependencies if needed
echo -e "${BLUE}🔍 Checking dependencies...${NC}"

if [ ! -d "backend/node_modules" ]; then
    echo -e "${YELLOW}⚠️ Backend dependencies not found. Installing...${NC}"
    cd backend && npm install && cd ..
    echo -e "${GREEN}✅ Backend dependencies installed${NC}"
else
    echo -e "${GREEN}✅ Backend dependencies found${NC}"
fi

if [ ! -d "frontend/node_modules" ]; then
    echo -e "${YELLOW}⚠️ Frontend dependencies not found. Installing...${NC}"
    cd frontend && npm install && cd ..
    echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
else
    echo -e "${GREEN}✅ Frontend dependencies found${NC}"
fi

# Create necessary directories
mkdir -p backend/logs backend/uploads

echo ""
echo -e "${GREEN}🚀 Starting NeuroColony AI Agent Platform...${NC}"
echo ""

# Start backend with proper environment
echo -e "${BLUE}🔧 Starting Backend Server (Port 5002)...${NC}"
cd backend
export PORT=5002
export NODE_ENV=development
export DATABASE_FALLBACK=true
npm start > ../backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait for backend to initialize
echo -e "${YELLOW}⏳ Waiting for backend to initialize...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:5002/api/test > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend server started successfully (PID: $BACKEND_PID)${NC}"
        break
    fi
    sleep 1
done

# Start frontend
echo -e "${BLUE}🎨 Starting Frontend Server (Port 3004)...${NC}"
cd frontend
npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for frontend to initialize
echo -e "${YELLOW}⏳ Waiting for frontend to initialize...${NC}"
sleep 5

echo ""
echo -e "${GREEN}🎉 NeuroColony AI Agent Platform is running!${NC}"
echo ""
echo -e "${BLUE}📱 Access URLs:${NC}"
echo -e "   🌐 Main Platform: ${GREEN}http://localhost:3004${NC}"
echo -e "   🐜 Colony Dashboard: ${GREEN}http://localhost:3004/colony-dashboard${NC}"
echo -e "   🔗 Integration Hub: ${GREEN}http://localhost:3004/integration-hub${NC}"
echo -e "   🤖 Agent Marketplace: ${GREEN}http://localhost:3004/agent-marketplace${NC}"
echo -e "   🎨 Workflow Builder: ${GREEN}http://localhost:3004/agent-builder${NC}"
echo -e "   🔧 Backend API: ${GREEN}http://localhost:5002/api${NC}"
echo ""
echo -e "${BLUE}📋 Logs:${NC}"
echo -e "   Backend: tail -f backend.log"
echo -e "   Frontend: tail -f frontend.log"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
echo ""

# Trap cleanup
trap 'echo -e "\n${YELLOW}🛑 Stopping services...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT TERM

# Keep script running
wait