/**
 * ⚡ Ultra-High Performance Caching System
 * Multi-layer caching with Redis Cluster, in-memory LRU, and edge optimization
 */

import { logger } from '../../backend/utils/logger.js'
import { eventBus } from '../modern/event-system.js'
import redis from 'redis'
import crypto from 'crypto'

class UltraCache {
  constructor() {
    this.layers = new Map()
    this.statistics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      totalRequests: 0,
      responseTime: []
    }
    this.config = {
      // L1 Cache: In-Memory (fastest)
      l1: {
        enabled: true,
        maxItems: 10000,
        ttl: 60000, // 1 minute
        maxMemory: 100 * 1024 * 1024 // 100MB
      },
      // L2 Cache: Redis (fast, distributed)
      l2: {
        enabled: true,
        cluster: true,
        ttl: 300000, // 5 minutes
        maxMemory: '500mb'
      },
      // L3 Cache: Persistent (slower, large capacity)
      l3: {
        enabled: true,
        ttl: 3600000, // 1 hour
        compression: true
      }
    }
    this.initialized = false
  }

  // Initialize ultra-high performance caching
  async initialize() {
    try {
      logger.info('⚡ Initializing Ultra-High Performance Cache System...')

      // Initialize L1 Cache (In-Memory)
      await this.initializeL1Cache()

      // Initialize L2 Cache (Redis Cluster)
      await this.initializeL2Cache()

      // Initialize L3 Cache (Persistent)
      await this.initializeL3Cache()

      // Setup cache warming
      await this.setupCacheWarming()

      // Start performance monitoring
      this.startPerformanceMonitoring()

      this.initialized = true
      logger.info('⚡ Ultra Cache System initialized - Multi-layer caching active')

      return true

    } catch (error) {
      logger.error('Ultra Cache initialization failed:', error)
      return false
    }
  }

  // Initialize L1 In-Memory Cache
  async initializeL1Cache() {
    const L1Cache = await this.createLRUCache()
    
    this.layers.set('l1', {
      name: 'In-Memory LRU',
      cache: L1Cache,
      enabled: this.config.l1.enabled,
      priority: 1,
      averageLatency: 0.001, // Sub-millisecond
      hitRate: 0,
      size: 0
    })

    logger.info('🧠 L1 In-Memory Cache initialized (10K items, <1ms latency)')
  }

  // Initialize L2 Redis Cluster Cache
  async initializeL2Cache() {
    try {
      const redisCluster = await this.createRedisCluster()
      
      this.layers.set('l2', {
        name: 'Redis Cluster',
        cache: redisCluster,
        enabled: this.config.l2.enabled,
        priority: 2,
        averageLatency: 1, // 1ms target
        hitRate: 0,
        size: 0
      })

      logger.info('🔴 L2 Redis Cluster Cache initialized (distributed, 1ms latency)')

    } catch (error) {
      logger.warn('Redis Cluster unavailable, using single Redis instance')
      
      // Fallback to single Redis instance
      const redisClient = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6380'
      })
      
      await redisClient.connect()
      
      this.layers.set('l2', {
        name: 'Redis Single',
        cache: redisClient,
        enabled: true,
        priority: 2,
        averageLatency: 2,
        hitRate: 0,
        size: 0
      })
    }
  }

  // Initialize L3 Persistent Cache
  async initializeL3Cache() {
    const L3Cache = await this.createPersistentCache()
    
    this.layers.set('l3', {
      name: 'Persistent Storage',
      cache: L3Cache,
      enabled: this.config.l3.enabled,
      priority: 3,
      averageLatency: 10, // 10ms target
      hitRate: 0,
      size: 0
    })

    logger.info('💾 L3 Persistent Cache initialized (large capacity, 10ms latency)')
  }

  // Create high-performance LRU cache
  async createLRUCache() {
    return new LRUCache({
      maxItems: this.config.l1.maxItems,
      maxMemory: this.config.l1.maxMemory,
      ttl: this.config.l1.ttl,
      onEviction: (key, value, reason) => {
        this.statistics.evictions++
        logger.debug(`L1 eviction: ${key} (${reason})`)
      }
    })
  }

  // Create Redis cluster connection
  async createRedisCluster() {
    // For production, this would connect to actual Redis Cluster
    // For now, simulate with enhanced single instance
    const client = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6380',
      socket: {
        connectTimeout: 1000,
        commandTimeout: 1000,
        keepAlive: true
      },
      // Cluster simulation config
      retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
          return new Error('Redis server refuses connection')
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          return new Error('Retry time exhausted')
        }
        return Math.min(options.attempt * 100, 3000)
      }
    })

    await client.connect()
    
    // Configure for high performance
    await client.configSet('maxmemory-policy', 'allkeys-lru')
    await client.configSet('timeout', '0')
    
    return client
  }

  // Create persistent cache storage
  async createPersistentCache() {
    return new PersistentCache({
      maxSize: 1000000, // 1M items
      ttl: this.config.l3.ttl,
      compression: this.config.l3.compression,
      path: '/tmp/sequenceai-cache'
    })
  }

  // Ultra-fast get operation with multi-layer fallback
  async get(key, options = {}) {
    const startTime = process.hrtime.bigint()
    this.statistics.totalRequests++

    try {
      // Generate cache key with namespace
      const cacheKey = this.generateCacheKey(key, options.namespace)

      // L1 Cache lookup (fastest)
      if (this.layers.get('l1')?.enabled) {
        const l1Result = await this.getFromLayer('l1', cacheKey)
        if (l1Result !== null) {
          this.recordHit('l1', startTime)
          return this.deserializeValue(l1Result)
        }
      }

      // L2 Cache lookup (fast, distributed)
      if (this.layers.get('l2')?.enabled) {
        const l2Result = await this.getFromLayer('l2', cacheKey)
        if (l2Result !== null) {
          // Promote to L1 for faster future access
          if (this.layers.get('l1')?.enabled) {
            await this.setToLayer('l1', cacheKey, l2Result, options.ttl)
          }
          
          this.recordHit('l2', startTime)
          return this.deserializeValue(l2Result)
        }
      }

      // L3 Cache lookup (larger capacity)
      if (this.layers.get('l3')?.enabled) {
        const l3Result = await this.getFromLayer('l3', cacheKey)
        if (l3Result !== null) {
          // Promote to L2 and L1
          if (this.layers.get('l2')?.enabled) {
            await this.setToLayer('l2', cacheKey, l3Result, options.ttl)
          }
          if (this.layers.get('l1')?.enabled) {
            await this.setToLayer('l1', cacheKey, l3Result, options.ttl)
          }
          
          this.recordHit('l3', startTime)
          return this.deserializeValue(l3Result)
        }
      }

      // Cache miss
      this.recordMiss(startTime)
      return null

    } catch (error) {
      logger.error('Ultra cache get error:', error)
      this.recordMiss(startTime)
      return null
    }
  }

  // Ultra-fast set operation across all layers
  async set(key, value, options = {}) {
    const startTime = process.hrtime.bigint()
    this.statistics.sets++

    try {
      const cacheKey = this.generateCacheKey(key, options.namespace)
      const serializedValue = this.serializeValue(value)
      const ttl = options.ttl || this.config.l1.ttl

      // Set in all enabled layers (write-through strategy)
      const setPromises = []

      if (this.layers.get('l1')?.enabled) {
        setPromises.push(this.setToLayer('l1', cacheKey, serializedValue, ttl))
      }

      if (this.layers.get('l2')?.enabled) {
        setPromises.push(this.setToLayer('l2', cacheKey, serializedValue, ttl))
      }

      if (this.layers.get('l3')?.enabled) {
        setPromises.push(this.setToLayer('l3', cacheKey, serializedValue, ttl))
      }

      // Execute all sets in parallel
      await Promise.allSettled(setPromises)

      const endTime = process.hrtime.bigint()
      const latency = Number(endTime - startTime) / 1000000 // Convert to milliseconds

      logger.debug(`Cache set: ${key} (${latency.toFixed(3)}ms)`)
      return true

    } catch (error) {
      logger.error('Ultra cache set error:', error)
      return false
    }
  }

  // High-performance multi-get operation
  async mget(keys, options = {}) {
    const startTime = process.hrtime.bigint()
    
    try {
      // Batch get from each layer for maximum efficiency
      const results = new Map()
      const remaining = new Set(keys)

      // L1 batch get
      if (this.layers.get('l1')?.enabled && remaining.size > 0) {
        const l1Results = await this.batchGetFromLayer('l1', Array.from(remaining), options.namespace)
        for (const [key, value] of l1Results) {
          if (value !== null) {
            results.set(key, this.deserializeValue(value))
            remaining.delete(key)
          }
        }
      }

      // L2 batch get for remaining keys
      if (this.layers.get('l2')?.enabled && remaining.size > 0) {
        const l2Results = await this.batchGetFromLayer('l2', Array.from(remaining), options.namespace)
        for (const [key, value] of l2Results) {
          if (value !== null) {
            results.set(key, this.deserializeValue(value))
            remaining.delete(key)
            
            // Promote to L1
            if (this.layers.get('l1')?.enabled) {
              const cacheKey = this.generateCacheKey(key, options.namespace)
              await this.setToLayer('l1', cacheKey, value, options.ttl)
            }
          }
        }
      }

      // L3 batch get for remaining keys
      if (this.layers.get('l3')?.enabled && remaining.size > 0) {
        const l3Results = await this.batchGetFromLayer('l3', Array.from(remaining), options.namespace)
        for (const [key, value] of l3Results) {
          if (value !== null) {
            results.set(key, this.deserializeValue(value))
            
            // Promote to L2 and L1
            const cacheKey = this.generateCacheKey(key, options.namespace)
            if (this.layers.get('l2')?.enabled) {
              await this.setToLayer('l2', cacheKey, value, options.ttl)
            }
            if (this.layers.get('l1')?.enabled) {
              await this.setToLayer('l1', cacheKey, value, options.ttl)
            }
          }
        }
      }

      const endTime = process.hrtime.bigint()
      const latency = Number(endTime - startTime) / 1000000

      logger.debug(`Batch get: ${keys.length} keys, ${results.size} hits (${latency.toFixed(3)}ms)`)
      
      return results

    } catch (error) {
      logger.error('Ultra cache mget error:', error)
      return new Map()
    }
  }

  // Get from specific cache layer
  async getFromLayer(layerName, key) {
    const layer = this.layers.get(layerName)
    if (!layer || !layer.enabled) return null

    try {
      const startTime = process.hrtime.bigint()
      let result = null

      switch (layerName) {
        case 'l1':
          result = layer.cache.get(key)
          break
        case 'l2':
          result = await layer.cache.get(key)
          break
        case 'l3':
          result = await layer.cache.get(key)
          break
      }

      const endTime = process.hrtime.bigint()
      const latency = Number(endTime - startTime) / 1000000

      // Update layer statistics
      layer.averageLatency = (layer.averageLatency + latency) / 2

      return result

    } catch (error) {
      logger.error(`Layer ${layerName} get error:`, error)
      return null
    }
  }

  // Set to specific cache layer
  async setToLayer(layerName, key, value, ttl) {
    const layer = this.layers.get(layerName)
    if (!layer || !layer.enabled) return false

    try {
      switch (layerName) {
        case 'l1':
          layer.cache.set(key, value, ttl)
          break
        case 'l2':
          if (ttl) {
            await layer.cache.setEx(key, Math.floor(ttl / 1000), value)
          } else {
            await layer.cache.set(key, value)
          }
          break
        case 'l3':
          await layer.cache.set(key, value, ttl)
          break
      }

      layer.size++
      return true

    } catch (error) {
      logger.error(`Layer ${layerName} set error:`, error)
      return false
    }
  }

  // Batch get from layer
  async batchGetFromLayer(layerName, keys, namespace) {
    const layer = this.layers.get(layerName)
    if (!layer || !layer.enabled) return new Map()

    try {
      const cacheKeys = keys.map(key => this.generateCacheKey(key, namespace))
      const results = new Map()

      switch (layerName) {
        case 'l1':
          for (let i = 0; i < keys.length; i++) {
            const value = layer.cache.get(cacheKeys[i])
            results.set(keys[i], value)
          }
          break
        case 'l2':
          const l2Values = await layer.cache.mGet(cacheKeys)
          for (let i = 0; i < keys.length; i++) {
            results.set(keys[i], l2Values[i])
          }
          break
        case 'l3':
          for (let i = 0; i < keys.length; i++) {
            const value = await layer.cache.get(cacheKeys[i])
            results.set(keys[i], value)
          }
          break
      }

      return results

    } catch (error) {
      logger.error(`Layer ${layerName} batch get error:`, error)
      return new Map()
    }
  }

  // Cache warming for frequently accessed data
  async setupCacheWarming() {
    const warmingData = [
      // Common API responses
      { key: 'health_check', value: { status: 'healthy', timestamp: Date.now() } },
      { key: 'system_config', value: { version: '1.0.0', features: ['ai', 'billing'] } },
      
      // Email sequence templates
      { key: 'welcome_template', value: { subject: 'Welcome!', type: 'template' } },
      { key: 'follow_up_template', value: { subject: 'Follow Up', type: 'template' } },
      
      // User plan configurations
      { key: 'free_plan', value: { sequences: 5, features: ['basic'] } },
      { key: 'pro_plan', value: { sequences: 75, features: ['basic', 'advanced'] } },
      { key: 'business_plan', value: { sequences: 200, features: ['all'] } }
    ]

    for (const item of warmingData) {
      await this.set(item.key, item.value, { namespace: 'warm', ttl: 600000 }) // 10 minutes
    }

    logger.info('🔥 Cache warming completed - frequently accessed data preloaded')
  }

  // Performance monitoring
  startPerformanceMonitoring() {
    setInterval(() => {
      this.generatePerformanceReport()
    }, 60000) // Every minute

    // Real-time performance alerts
    setInterval(() => {
      this.checkPerformanceThresholds()
    }, 5000) // Every 5 seconds
  }

  // Generate performance report
  generatePerformanceReport() {
    const totalHits = this.statistics.hits
    const totalRequests = this.statistics.totalRequests
    const hitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0

    const avgResponseTime = this.statistics.responseTime.length > 0
      ? this.statistics.responseTime.reduce((sum, time) => sum + time, 0) / this.statistics.responseTime.length
      : 0

    const report = {
      timestamp: new Date().toISOString(),
      hitRate: Math.round(hitRate * 100) / 100,
      totalRequests: totalRequests,
      totalHits: totalHits,
      totalMisses: this.statistics.misses,
      averageResponseTime: Math.round(avgResponseTime * 1000) / 1000, // ms
      layers: {}
    }

    // Layer-specific statistics
    for (const [layerName, layer] of this.layers) {
      report.layers[layerName] = {
        enabled: layer.enabled,
        hitRate: Math.round(layer.hitRate * 100) / 100,
        averageLatency: Math.round(layer.averageLatency * 1000) / 1000,
        size: layer.size
      }
    }

    logger.info(`⚡ Cache Performance: ${hitRate.toFixed(1)}% hit rate, ${avgResponseTime.toFixed(3)}ms avg response`)

    // Emit performance metrics
    eventBus.publish('cache.performance', report)

    // Reset statistics for next period
    this.resetStatistics()

    return report
  }

  // Check performance thresholds
  checkPerformanceThresholds() {
    const avgResponseTime = this.statistics.responseTime.length > 0
      ? this.statistics.responseTime.reduce((sum, time) => sum + time, 0) / this.statistics.responseTime.length
      : 0

    // Alert if response time exceeds 1ms
    if (avgResponseTime > 1) {
      eventBus.publish('cache.performance.alert', {
        type: 'high_latency',
        value: avgResponseTime,
        threshold: 1,
        timestamp: new Date().toISOString()
      })
    }

    // Alert if hit rate drops below 80%
    const hitRate = this.statistics.totalRequests > 0 
      ? (this.statistics.hits / this.statistics.totalRequests) * 100 
      : 100

    if (hitRate < 80 && this.statistics.totalRequests > 100) {
      eventBus.publish('cache.performance.alert', {
        type: 'low_hit_rate',
        value: hitRate,
        threshold: 80,
        timestamp: new Date().toISOString()
      })
    }
  }

  // Helper methods
  generateCacheKey(key, namespace = 'default') {
    return `${namespace}:${key}`
  }

  serializeValue(value) {
    try {
      return JSON.stringify({
        data: value,
        timestamp: Date.now(),
        type: typeof value
      })
    } catch (error) {
      logger.error('Serialization error:', error)
      return null
    }
  }

  deserializeValue(serialized) {
    try {
      const parsed = JSON.parse(serialized)
      return parsed.data
    } catch (error) {
      // Fallback: return as-is if not JSON
      return serialized
    }
  }

  recordHit(layer, startTime) {
    this.statistics.hits++
    const endTime = process.hrtime.bigint()
    const latency = Number(endTime - startTime) / 1000000 // Convert to milliseconds
    this.statistics.responseTime.push(latency)
    
    // Update layer hit rate
    const layerObj = this.layers.get(layer)
    if (layerObj) {
      layerObj.hitRate = (layerObj.hitRate || 0) * 0.9 + 0.1 // Exponential smoothing
    }

    // Keep only recent response times
    if (this.statistics.responseTime.length > 1000) {
      this.statistics.responseTime = this.statistics.responseTime.slice(-500)
    }
  }

  recordMiss(startTime) {
    this.statistics.misses++
    const endTime = process.hrtime.bigint()
    const latency = Number(endTime - startTime) / 1000000
    this.statistics.responseTime.push(latency)
  }

  resetStatistics() {
    this.statistics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      totalRequests: 0,
      responseTime: []
    }
  }

  // Get cache statistics
  getStatistics() {
    const totalRequests = this.statistics.totalRequests
    const hitRate = totalRequests > 0 ? (this.statistics.hits / totalRequests) * 100 : 0
    
    return {
      ...this.statistics,
      hitRate: Math.round(hitRate * 100) / 100,
      layers: Object.fromEntries(
        Array.from(this.layers.entries()).map(([name, layer]) => [
          name,
          {
            enabled: layer.enabled,
            hitRate: Math.round(layer.hitRate * 100) / 100,
            averageLatency: Math.round(layer.averageLatency * 1000) / 1000,
            size: layer.size
          }
        ])
      )
    }
  }

  // Cache invalidation
  async invalidate(pattern, namespace = 'default') {
    const fullPattern = this.generateCacheKey(pattern, namespace)
    
    // Invalidate across all layers
    for (const [layerName, layer] of this.layers) {
      if (layer.enabled) {
        try {
          switch (layerName) {
            case 'l1':
              layer.cache.deletePattern(fullPattern)
              break
            case 'l2':
              // Redis pattern deletion
              const keys = await layer.cache.keys(fullPattern)
              if (keys.length > 0) {
                await layer.cache.del(keys)
              }
              break
            case 'l3':
              await layer.cache.deletePattern(fullPattern)
              break
          }
        } catch (error) {
          logger.error(`Invalidation error in ${layerName}:`, error)
        }
      }
    }

    logger.info(`🗑️ Cache invalidated: ${fullPattern}`)
  }
}

// LRU Cache implementation
class LRUCache {
  constructor(options) {
    this.maxItems = options.maxItems || 1000
    this.maxMemory = options.maxMemory || 50 * 1024 * 1024 // 50MB
    this.ttl = options.ttl || 300000 // 5 minutes
    this.onEviction = options.onEviction || (() => {})
    
    this.cache = new Map()
    this.timers = new Map()
    this.memoryUsage = 0
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null
    
    // Move to end (most recently used)
    this.cache.delete(key)
    this.cache.set(key, item)
    
    return item.value
  }

  set(key, value, ttl = this.ttl) {
    // Remove existing item if present
    if (this.cache.has(key)) {
      this.delete(key)
    }

    // Check memory limit
    const itemSize = this.calculateSize(value)
    if (this.memoryUsage + itemSize > this.maxMemory) {
      this.evictLRU()
    }

    // Check item limit
    if (this.cache.size >= this.maxItems) {
      this.evictLRU()
    }

    // Add new item
    const item = { value, size: itemSize, timestamp: Date.now() }
    this.cache.set(key, item)
    this.memoryUsage += itemSize

    // Set TTL timer
    if (ttl > 0) {
      const timer = setTimeout(() => {
        this.delete(key)
      }, ttl)
      this.timers.set(key, timer)
    }
  }

  delete(key) {
    const item = this.cache.get(key)
    if (!item) return false

    this.cache.delete(key)
    this.memoryUsage -= item.size

    // Clear TTL timer
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }

    return true
  }

  evictLRU() {
    // Evict least recently used item (first in Map)
    const firstKey = this.cache.keys().next().value
    if (firstKey) {
      const item = this.cache.get(firstKey)
      this.onEviction(firstKey, item.value, 'lru')
      this.delete(firstKey)
    }
  }

  calculateSize(value) {
    // Rough estimate of memory usage
    return JSON.stringify(value).length * 2 // UTF-16 characters
  }

  deletePattern(pattern) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    const keysToDelete = []
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.delete(key))
  }

  clear() {
    this.cache.clear()
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    this.memoryUsage = 0
  }
}

// Persistent Cache implementation (simplified)
class PersistentCache {
  constructor(options) {
    this.maxSize = options.maxSize || 100000
    this.ttl = options.ttl || 3600000 // 1 hour
    this.compression = options.compression || false
    this.path = options.path || '/tmp/cache'
    this.cache = new Map() // In-memory index
  }

  async get(key) {
    const item = this.cache.get(key)
    if (!item) return null
    
    // Check TTL
    if (Date.now() - item.timestamp > this.ttl) {
      this.delete(key)
      return null
    }
    
    return item.value
  }

  async set(key, value, ttl = this.ttl) {
    // Simple in-memory implementation for demo
    // In production, this would write to disk
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    })
    
    // Evict old items if over limit
    if (this.cache.size > this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }
  }

  async delete(key) {
    return this.cache.delete(key)
  }

  async deletePattern(pattern) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    const keysToDelete = []
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.delete(key))
  }
}

// Create singleton instance
const ultraCache = new UltraCache()

export { UltraCache, ultraCache }