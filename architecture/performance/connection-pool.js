/**
 * 🏊 Ultra-High Performance Connection Pool
 * Advanced connection management with load balancing and resource optimization
 */

import { logger } from '../../backend/utils/logger.js'
import { eventBus } from '../modern/event-system.js'
import mongoose from 'mongoose'
import redis from 'redis'

class UltraConnectionPool {
  constructor() {
    this.pools = new Map()
    this.statistics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      requestsServed: 0,
      averageWaitTime: 0,
      poolEfficiency: 0
    }
    this.config = {
      mongodb: {
        minPoolSize: 5,
        maxPoolSize: 50,
        acquireTimeoutMillis: 1000,
        idleTimeoutMillis: 30000,
        maxIdleTimeMS: 30000,
        heartbeatFrequencyMS: 10000,
        retryWrites: true,
        retryReads: true,
        readPreference: 'primaryPreferred',
        loadBalancing: true
      },
      redis: {
        minPoolSize: 3,
        maxPoolSize: 20,
        acquireTimeoutMillis: 500,
        idleTimeoutMillis: 20000,
        commandTimeout: 1000,
        connectTimeout: 2000,
        lazyConnect: true,
        keepAlive: true
      },
      http: {
        minPoolSize: 10,
        maxPoolSize: 100,
        acquireTimeoutMillis: 2000,
        idleTimeoutMillis: 60000,
        socketTimeout: 30000,
        keepAlive: true,
        maxSockets: 50
      }
    }
    this.loadBalancer = new ConnectionLoadBalancer()
    this.healthMonitor = new PoolHealthMonitor()
    this.initialized = false
  }

  // Initialize ultra-high performance connection pools
  async initialize() {
    try {
      logger.info('🏊 Initializing Ultra-High Performance Connection Pools...')

      // Initialize MongoDB connection pool
      await this.initializeMongoDBPool()

      // Initialize Redis connection pool
      await this.initializeRedisPool()

      // Initialize HTTP connection pool
      await this.initializeHTTPPool()

      // Start pool monitoring
      this.startPoolMonitoring()

      // Setup auto-scaling
      this.setupAutoScaling()

      this.initialized = true
      logger.info('🏊 Ultra Connection Pool System initialized - High-performance pools active')

      return true

    } catch (error) {
      logger.error('Connection pool initialization failed:', error)
      return false
    }
  }

  // Initialize MongoDB connection pool with advanced optimization
  async initializeMongoDBPool() {
    try {
      const config = this.config.mongodb
      const mongoUri = process.env.MONGODB_URI

      if (!mongoUri) {
        throw new Error('MongoDB URI not configured')
      }

      // Advanced MongoDB connection options
      const options = {
        // Pool configuration
        minPoolSize: config.minPoolSize,
        maxPoolSize: config.maxPoolSize,
        maxIdleTimeMS: config.maxIdleTimeMS,
        
        // Performance optimizations
        serverSelectionTimeoutMS: 2000,
        heartbeatFrequencyMS: config.heartbeatFrequencyMS,
        socketTimeoutMS: 30000,
        family: 4, // Use IPv4, skip happy eyeballs
        
        // Reliability
        retryWrites: config.retryWrites,
        retryReads: config.retryReads,
        readPreference: config.readPreference,
        
        // Compression
        compressors: ['zstd', 'zlib', 'snappy'],
        zlibCompressionLevel: 6,
        
        // Buffer settings
        bufferMaxEntries: 0, // Fail fast
        bufferCommands: false,
        
        // Connection monitoring
        monitorCommands: true
      }

      // Create optimized connection
      const connection = await mongoose.createConnection(mongoUri, options)

      // Setup connection event handlers
      this.setupMongoDBEventHandlers(connection)

      const pool = {
        type: 'mongodb',
        connection,
        config,
        statistics: {
          totalQueries: 0,
          averageQueryTime: 0,
          activeQueries: 0,
          connectionCount: config.minPoolSize,
          errorCount: 0,
          lastHealthCheck: Date.now()
        },
        healthStatus: 'healthy'
      }

      this.pools.set('mongodb', pool)
      logger.info(`🗄️ MongoDB pool initialized: ${config.minPoolSize}-${config.maxPoolSize} connections`)

    } catch (error) {
      logger.error('MongoDB pool initialization failed:', error)
      throw error
    }
  }

  // Initialize Redis connection pool
  async initializeRedisPool() {
    try {
      const config = this.config.redis
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6380'

      // Create connection pool with multiple connections
      const connections = []
      
      for (let i = 0; i < config.minPoolSize; i++) {
        const client = redis.createClient({
          url: redisUrl,
          socket: {
            connectTimeout: config.connectTimeout,
            commandTimeout: config.commandTimeout,
            keepAlive: config.keepAlive,
            reconnectStrategy: (retries) => Math.min(retries * 50, 500)
          },
          lazyConnect: config.lazyConnect,
          // Performance optimizations
          pingInterval: 30000,
          retryDelayOnFailover: 100,
          enableOfflineQueue: false,
          maxRetriesPerRequest: 3
        })

        await client.connect()
        connections.push({
          client,
          inUse: false,
          lastUsed: Date.now(),
          createdAt: Date.now(),
          requestCount: 0
        })
      }

      const pool = {
        type: 'redis',
        connections,
        config,
        currentIndex: 0,
        statistics: {
          totalCommands: 0,
          averageCommandTime: 0,
          activeCommands: 0,
          connectionCount: config.minPoolSize,
          errorCount: 0,
          lastHealthCheck: Date.now()
        },
        healthStatus: 'healthy'
      }

      this.pools.set('redis', pool)
      logger.info(`🔴 Redis pool initialized: ${config.minPoolSize} connections ready`)

    } catch (error) {
      logger.error('Redis pool initialization failed:', error)
      throw error
    }
  }

  // Initialize HTTP connection pool
  async initializeHTTPPool() {
    try {
      const config = this.config.http

      // Create HTTP agent with optimized settings
      const http = await import('http')
      const https = await import('https')

      const httpAgent = new http.Agent({
        keepAlive: config.keepAlive,
        keepAliveMsecs: 1000,
        maxSockets: config.maxSockets,
        maxFreeSockets: 10,
        timeout: config.socketTimeout,
        freeSocketTimeout: config.idleTimeoutMillis,
        scheduling: 'fifo'
      })

      const httpsAgent = new https.Agent({
        keepAlive: config.keepAlive,
        keepAliveMsecs: 1000,
        maxSockets: config.maxSockets,
        maxFreeSockets: 10,
        timeout: config.socketTimeout,
        freeSocketTimeout: config.idleTimeoutMillis,
        scheduling: 'fifo'
      })

      const pool = {
        type: 'http',
        httpAgent,
        httpsAgent,
        config,
        statistics: {
          totalRequests: 0,
          averageRequestTime: 0,
          activeRequests: 0,
          socketsCreated: 0,
          socketsDestroyed: 0,
          errorCount: 0,
          lastHealthCheck: Date.now()
        },
        healthStatus: 'healthy'
      }

      this.pools.set('http', pool)
      logger.info(`🌐 HTTP pool initialized: ${config.maxSockets} max sockets per host`)

    } catch (error) {
      logger.error('HTTP pool initialization failed:', error)
      throw error
    }
  }

  // Get optimized MongoDB connection
  async getMongoConnection() {
    const pool = this.pools.get('mongodb')
    if (!pool) {
      throw new Error('MongoDB pool not initialized')
    }

    const startTime = process.hrtime.bigint()

    try {
      // Connection is managed by Mongoose automatically
      const connection = pool.connection

      // Ensure connection is ready
      if (connection.readyState !== 1) {
        await new Promise((resolve, reject) => {
          if (connection.readyState === 1) {
            resolve()
          } else {
            connection.once('open', resolve)
            connection.once('error', reject)
            setTimeout(() => reject(new Error('Connection timeout')), 5000)
          }
        })
      }

      const endTime = process.hrtime.bigint()
      const waitTime = Number(endTime - startTime) / 1000000 // Convert to milliseconds

      // Update statistics
      pool.statistics.totalQueries++
      pool.statistics.activeQueries++
      pool.statistics.averageQueryTime = 
        (pool.statistics.averageQueryTime + waitTime) / 2

      return {
        connection,
        release: () => {
          pool.statistics.activeQueries--
        }
      }

    } catch (error) {
      pool.statistics.errorCount++
      throw error
    }
  }

  // Get optimized Redis connection
  async getRedisConnection() {
    const pool = this.pools.get('redis')
    if (!pool) {
      throw new Error('Redis pool not initialized')
    }

    const startTime = process.hrtime.bigint()

    try {
      // Find available connection using round-robin with health check
      const connection = this.loadBalancer.getRedisConnection(pool.connections)
      
      if (!connection) {
        // Create new connection if pool is not at max
        if (pool.connections.length < pool.config.maxPoolSize) {
          const newConnection = await this.createRedisConnection()
          pool.connections.push(newConnection)
          connection = newConnection
        } else {
          throw new Error('Redis pool exhausted')
        }
      }

      const endTime = process.hrtime.bigint()
      const waitTime = Number(endTime - startTime) / 1000000

      // Mark connection as in use
      connection.inUse = true
      connection.lastUsed = Date.now()
      connection.requestCount++

      // Update statistics
      pool.statistics.totalCommands++
      pool.statistics.activeCommands++
      pool.statistics.averageCommandTime = 
        (pool.statistics.averageCommandTime + waitTime) / 2

      return {
        client: connection.client,
        release: () => {
          connection.inUse = false
          pool.statistics.activeCommands--
        }
      }

    } catch (error) {
      pool.statistics.errorCount++
      throw error
    }
  }

  // Get optimized HTTP agents
  getHTTPAgents() {
    const pool = this.pools.get('http')
    if (!pool) {
      throw new Error('HTTP pool not initialized')
    }

    return {
      httpAgent: pool.httpAgent,
      httpsAgent: pool.httpsAgent
    }
  }

  // Create new Redis connection
  async createRedisConnection() {
    const config = this.config.redis
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6380'

    const client = redis.createClient({
      url: redisUrl,
      socket: {
        connectTimeout: config.connectTimeout,
        commandTimeout: config.commandTimeout,
        keepAlive: config.keepAlive
      }
    })

    await client.connect()

    return {
      client,
      inUse: false,
      lastUsed: Date.now(),
      createdAt: Date.now(),
      requestCount: 0
    }
  }

  // Setup MongoDB event handlers
  setupMongoDBEventHandlers(connection) {
    connection.on('connected', () => {
      logger.info('🗄️ MongoDB connection established')
      this.updatePoolHealth('mongodb', 'healthy')
    })

    connection.on('error', (error) => {
      logger.error('🗄️ MongoDB connection error:', error)
      this.updatePoolHealth('mongodb', 'unhealthy')
    })

    connection.on('disconnected', () => {
      logger.warn('🗄️ MongoDB connection lost')
      this.updatePoolHealth('mongodb', 'degraded')
    })

    connection.on('reconnected', () => {
      logger.info('🗄️ MongoDB reconnected')
      this.updatePoolHealth('mongodb', 'healthy')
    })

    // Monitor connection pool events
    connection.on('connectionPoolCreated', () => {
      logger.info('🏊 MongoDB connection pool created')
    })

    connection.on('connectionPoolCleared', () => {
      logger.warn('🏊 MongoDB connection pool cleared')
    })

    connection.on('connectionCreated', () => {
      this.statistics.totalConnections++
    })

    connection.on('connectionClosed', () => {
      this.statistics.totalConnections--
    })
  }

  // Start pool monitoring
  startPoolMonitoring() {
    // Monitor pool health every 30 seconds
    setInterval(() => {
      this.healthMonitor.checkAllPools(this.pools)
    }, 30000)

    // Generate performance reports every minute
    setInterval(() => {
      this.generatePoolReport()
    }, 60000)

    // Cleanup idle connections every 5 minutes
    setInterval(() => {
      this.cleanupIdleConnections()
    }, 300000)

    logger.info('📊 Pool monitoring started')
  }

  // Setup auto-scaling
  setupAutoScaling() {
    setInterval(() => {
      this.autoScalePools()
    }, 120000) // Check every 2 minutes

    logger.info('🔄 Auto-scaling enabled for connection pools')
  }

  // Auto-scale pools based on demand
  autoScalePools() {
    for (const [poolName, pool] of this.pools) {
      if (poolName === 'redis') {
        this.autoScaleRedisPool(pool)
      }
      // MongoDB auto-scaling is handled by Mongoose
      // HTTP agents auto-scale based on demand
    }
  }

  // Auto-scale Redis pool
  autoScaleRedisPool(pool) {
    const { connections, config, statistics } = pool
    const activeConnections = connections.filter(conn => conn.inUse).length
    const idleConnections = connections.length - activeConnections
    const utilizationRate = activeConnections / connections.length

    // Scale up if utilization is high
    if (utilizationRate > 0.8 && connections.length < config.maxPoolSize) {
      this.scaleUpRedisPool(pool)
    }

    // Scale down if too many idle connections
    if (idleConnections > 5 && connections.length > config.minPoolSize) {
      this.scaleDownRedisPool(pool)
    }
  }

  // Scale up Redis pool
  async scaleUpRedisPool(pool) {
    try {
      const newConnection = await this.createRedisConnection()
      pool.connections.push(newConnection)
      
      logger.info(`📈 Redis pool scaled up: ${pool.connections.length} connections`)
      
      eventBus.publish('pool.scaled', {
        type: 'redis',
        action: 'scale_up',
        newSize: pool.connections.length
      })

    } catch (error) {
      logger.error('Redis pool scale up failed:', error)
    }
  }

  // Scale down Redis pool
  scaleDownRedisPool(pool) {
    const idleConnections = pool.connections.filter(conn => 
      !conn.inUse && Date.now() - conn.lastUsed > 300000 // 5 minutes idle
    )

    if (idleConnections.length > 0 && pool.connections.length > pool.config.minPoolSize) {
      const connectionToRemove = idleConnections[0]
      
      // Close connection
      connectionToRemove.client.disconnect()
      
      // Remove from pool
      pool.connections = pool.connections.filter(conn => conn !== connectionToRemove)
      
      logger.info(`📉 Redis pool scaled down: ${pool.connections.length} connections`)
      
      eventBus.publish('pool.scaled', {
        type: 'redis',
        action: 'scale_down',
        newSize: pool.connections.length
      })
    }
  }

  // Cleanup idle connections
  cleanupIdleConnections() {
    for (const [poolName, pool] of this.pools) {
      if (poolName === 'redis') {
        this.cleanupRedisConnections(pool)
      }
    }
  }

  // Cleanup Redis connections
  cleanupRedisConnections(pool) {
    const now = Date.now()
    const idleTimeout = pool.config.idleTimeoutMillis
    
    pool.connections = pool.connections.filter(connection => {
      const isIdle = !connection.inUse && (now - connection.lastUsed) > idleTimeout
      
      if (isIdle && pool.connections.length > pool.config.minPoolSize) {
        connection.client.disconnect()
        logger.debug('🧹 Removed idle Redis connection')
        return false
      }
      
      return true
    })
  }

  // Update pool health status
  updatePoolHealth(poolName, status) {
    const pool = this.pools.get(poolName)
    if (pool) {
      pool.healthStatus = status
      pool.statistics.lastHealthCheck = Date.now()
    }
  }

  // Generate pool performance report
  generatePoolReport() {
    const report = {
      timestamp: new Date().toISOString(),
      pools: {},
      globalStatistics: this.statistics
    }

    for (const [poolName, pool] of this.pools) {
      report.pools[poolName] = {
        type: pool.type,
        healthStatus: pool.healthStatus,
        statistics: pool.statistics,
        connectionCount: this.getPoolConnectionCount(pool),
        efficiency: this.calculatePoolEfficiency(pool)
      }
    }

    logger.info(`🏊 Pool Report: ${Object.keys(report.pools).length} pools monitored`)
    
    eventBus.publish('pool.performance', report)
    
    return report
  }

  // Get pool connection count
  getPoolConnectionCount(pool) {
    switch (pool.type) {
      case 'mongodb':
        return pool.connection.readyState === 1 ? 1 : 0
      case 'redis':
        return pool.connections.length
      case 'http':
        return pool.httpAgent.sockets ? Object.keys(pool.httpAgent.sockets).length : 0
      default:
        return 0
    }
  }

  // Calculate pool efficiency
  calculatePoolEfficiency(pool) {
    const stats = pool.statistics
    
    if (stats.totalQueries === 0 && stats.totalCommands === 0 && stats.totalRequests === 0) {
      return 100 // No requests, perfect efficiency
    }

    const totalOperations = stats.totalQueries || stats.totalCommands || stats.totalRequests || 1
    const errorRate = (stats.errorCount / totalOperations) * 100
    const efficiency = Math.max(0, 100 - errorRate)

    return Math.round(efficiency * 100) / 100
  }

  // Get comprehensive pool statistics
  getPoolStatistics() {
    const stats = {
      global: this.statistics,
      pools: {}
    }

    for (const [poolName, pool] of this.pools) {
      stats.pools[poolName] = {
        ...pool.statistics,
        healthStatus: pool.healthStatus,
        connectionCount: this.getPoolConnectionCount(pool),
        efficiency: this.calculatePoolEfficiency(pool)
      }
    }

    return stats
  }

  // Execute query with automatic connection management
  async executeQuery(poolType, operation) {
    switch (poolType) {
      case 'mongodb':
        const mongoConn = await this.getMongoConnection()
        try {
          return await operation(mongoConn.connection)
        } finally {
          mongoConn.release()
        }

      case 'redis':
        const redisConn = await this.getRedisConnection()
        try {
          return await operation(redisConn.client)
        } finally {
          redisConn.release()
        }

      default:
        throw new Error(`Unknown pool type: ${poolType}`)
    }
  }

  // Graceful shutdown
  async shutdown() {
    logger.info('🏊 Shutting down connection pools...')

    for (const [poolName, pool] of this.pools) {
      try {
        switch (pool.type) {
          case 'mongodb':
            await pool.connection.close()
            break
          case 'redis':
            await Promise.all(pool.connections.map(conn => conn.client.disconnect()))
            break
          case 'http':
            pool.httpAgent.destroy()
            pool.httpsAgent.destroy()
            break
        }
        logger.info(`✅ ${poolName} pool closed`)
      } catch (error) {
        logger.error(`❌ Error closing ${poolName} pool:`, error)
      }
    }

    logger.info('🏊 All connection pools closed')
  }
}

// Connection Load Balancer
class ConnectionLoadBalancer {
  constructor() {
    this.algorithms = {
      roundRobin: this.roundRobin.bind(this),
      leastConnections: this.leastConnections.bind(this),
      weightedRoundRobin: this.weightedRoundRobin.bind(this)
    }
    this.currentIndex = 0
  }

  getRedisConnection(connections, algorithm = 'leastConnections') {
    const availableConnections = connections.filter(conn => !conn.inUse)
    
    if (availableConnections.length === 0) {
      return null
    }

    const balancer = this.algorithms[algorithm] || this.algorithms.leastConnections
    return balancer(availableConnections)
  }

  roundRobin(connections) {
    const connection = connections[this.currentIndex % connections.length]
    this.currentIndex++
    return connection
  }

  leastConnections(connections) {
    return connections.reduce((least, current) => 
      current.requestCount < least.requestCount ? current : least
    )
  }

  weightedRoundRobin(connections) {
    // Simple implementation - could be enhanced with actual weights
    return this.roundRobin(connections)
  }
}

// Pool Health Monitor
class PoolHealthMonitor {
  constructor() {
    this.healthChecks = {
      mongodb: this.checkMongoDBHealth.bind(this),
      redis: this.checkRedisHealth.bind(this),
      http: this.checkHTTPHealth.bind(this)
    }
  }

  async checkAllPools(pools) {
    for (const [poolName, pool] of pools) {
      try {
        const healthCheck = this.healthChecks[pool.type]
        if (healthCheck) {
          const isHealthy = await healthCheck(pool)
          const newStatus = isHealthy ? 'healthy' : 'unhealthy'
          
          if (pool.healthStatus !== newStatus) {
            logger.info(`🏥 Pool ${poolName} status changed: ${pool.healthStatus} -> ${newStatus}`)
            pool.healthStatus = newStatus
          }
        }
      } catch (error) {
        logger.error(`Health check failed for ${poolName}:`, error)
        pool.healthStatus = 'unhealthy'
      }
    }
  }

  async checkMongoDBHealth(pool) {
    try {
      const connection = pool.connection
      return connection.readyState === 1 // Connected
    } catch (error) {
      return false
    }
  }

  async checkRedisHealth(pool) {
    try {
      const healthyConnections = pool.connections.filter(conn => {
        // Check if connection is responsive
        return conn.client.isReady
      })
      
      return healthyConnections.length >= pool.config.minPoolSize
    } catch (error) {
      return false
    }
  }

  async checkHTTPHealth(pool) {
    // HTTP agents don't need explicit health checks
    // They auto-recover and create connections on demand
    return true
  }
}

// Create singleton instance
const ultraConnectionPool = new UltraConnectionPool()

export { UltraConnectionPool, ultraConnectionPool }