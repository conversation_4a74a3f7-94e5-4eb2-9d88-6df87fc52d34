import { logger } from '../../backend/utils/logger.js'
import { EventEmitter } from 'events'

/**
 * 🏥 Advanced Health Check System
 * Planetary Scale Resilience Engineering - Phase 3
 * 
 * Features:
 * - Multi-dimensional health monitoring
 * - Graceful degradation strategies
 * - Dependency health tracking
 * - Auto-healing capabilities
 * - Performance health metrics
 * - Circuit breaker integration
 */

export class HealthCheckManager extends EventEmitter {
  constructor(options = {}) {
    super()
    
    this.config = {
      checkInterval: options.checkInterval || 30000,        // 30s health checks
      criticalCheckInterval: options.criticalCheckInterval || 5000, // 5s for critical services
      timeoutDuration: options.timeoutDuration || 10000,    // 10s timeout per check
      degradedThreshold: options.degradedThreshold || 0.8,  // 80% success rate
      unhealthyThreshold: options.unhealthyThreshold || 0.5, // 50% success rate
      recoveryThreshold: options.recoveryThreshold || 0.9,  // 90% for recovery
      historySize: options.historySize || 100,              // Keep last 100 checks
      alertThreshold: options.alertThreshold || 3,          // Alert after 3 consecutive failures
      autoHealingEnabled: options.autoHealingEnabled !== false,
      gracefulDegradationEnabled: options.gracefulDegradationEnabled !== false,
      ...options
    }
    
    // Health check registry
    this.checks = new Map()
    this.dependencies = new Map()
    this.intervals = new Map()
    
    // System health state
    this.systemHealth = {
      status: 'healthy',
      lastCheck: Date.now(),
      uptime: process.uptime(),
      checks: {},
      degradedServices: [],
      criticalIssues: [],
      performanceMetrics: {}
    }
    
    // Health history for trend analysis
    this.healthHistory = []
    
    // Performance metrics
    this.performanceMetrics = {
      responseTime: { avg: 0, p95: 0, p99: 0 },
      errorRate: 0,
      throughput: 0,
      memoryUsage: 0,
      cpuUsage: 0
    }
    
    // Auto-healing strategies
    this.healingStrategies = new Map()
    
    // Initialize core system checks
    this.initializeCoreChecks()
    
    logger.info('🏥 Health Check Manager initialized', {
      config: this.config,
      checksRegistered: this.checks.size
    })
  }
  
  /**
   * Register a health check
   */
  registerCheck(name, checkFunction, options = {}) {
    const checkConfig = {
      name,
      checkFunction,
      interval: options.interval || this.config.checkInterval,
      timeout: options.timeout || this.config.timeoutDuration,
      critical: options.critical || false,
      dependencies: options.dependencies || [],
      enabled: options.enabled !== false,
      tags: options.tags || [],
      description: options.description || '',
      lastCheck: null,
      lastResult: null,
      successCount: 0,
      failureCount: 0,
      history: [],
      consecutiveFailures: 0,
      status: 'unknown'
    }
    
    this.checks.set(name, checkConfig)
    
    // Start monitoring if enabled
    if (checkConfig.enabled) {
      this.startCheckInterval(name)
    }
    
    // Register dependencies
    if (checkConfig.dependencies.length > 0) {
      this.dependencies.set(name, checkConfig.dependencies)
    }
    
    logger.info(`🔍 Health check registered: ${name}`, {
      critical: checkConfig.critical,
      interval: checkConfig.interval,
      dependencies: checkConfig.dependencies
    })
    
    this.emit('checkRegistered', { name, config: checkConfig })
    
    return this
  }
  
  /**
   * Start health check interval for a specific check
   */
  startCheckInterval(name) {
    const check = this.checks.get(name)
    if (!check) return
    
    // Clear existing interval
    if (this.intervals.has(name)) {
      clearInterval(this.intervals.get(name))
    }
    
    // Set up new interval
    const interval = setInterval(async () => {
      await this.runCheck(name)
    }, check.interval)
    
    this.intervals.set(name, interval)
    
    // Run initial check
    setImmediate(() => this.runCheck(name))
  }
  
  /**
   * Run a specific health check
   */
  async runCheck(name) {
    const check = this.checks.get(name)
    if (!check || !check.enabled) return
    
    const startTime = Date.now()
    
    try {
      // Check dependencies first
      const dependencyStatus = await this.checkDependencies(name)
      if (!dependencyStatus.healthy) {
        throw new Error(`Dependencies unhealthy: ${dependencyStatus.failed.join(', ')}`)
      }
      
      // Run the actual health check with timeout
      const result = await this.runCheckWithTimeout(check)
      
      // Record success
      const responseTime = Date.now() - startTime
      this.recordCheckResult(name, true, result, responseTime)
      
    } catch (error) {
      // Record failure
      const responseTime = Date.now() - startTime
      this.recordCheckResult(name, false, error, responseTime)
    }
  }
  
  /**
   * Run health check with timeout
   */
  async runCheckWithTimeout(check) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Health check timeout after ${check.timeout}ms`))
      }, check.timeout)
      
      Promise.resolve(check.checkFunction())
        .then(result => {
          clearTimeout(timeout)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timeout)
          reject(error)
        })
    })
  }
  
  /**
   * Check dependencies for a service
   */
  async checkDependencies(serviceName) {
    const dependencies = this.dependencies.get(serviceName) || []
    const failed = []
    
    for (const dependency of dependencies) {
      const depCheck = this.checks.get(dependency)
      if (!depCheck || depCheck.status !== 'healthy') {
        failed.push(dependency)
      }
    }
    
    return {
      healthy: failed.length === 0,
      failed,
      total: dependencies.length
    }
  }
  
  /**
   * Record health check result
   */
  recordCheckResult(name, success, result, responseTime) {
    const check = this.checks.get(name)
    if (!check) return
    
    const now = Date.now()
    const checkResult = {
      timestamp: now,
      success,
      result: success ? result : result.message,
      responseTime,
      status: success ? 'healthy' : 'unhealthy'
    }
    
    // Update check state
    check.lastCheck = now
    check.lastResult = checkResult
    
    if (success) {
      check.successCount++
      check.consecutiveFailures = 0
      check.status = 'healthy'
    } else {
      check.failureCount++
      check.consecutiveFailures++
      check.status = 'unhealthy'
    }
    
    // Add to history
    check.history.push(checkResult)
    if (check.history.length > this.config.historySize) {
      check.history.shift()
    }
    
    // Update system health
    this.updateSystemHealth()
    
    // Trigger alerts if needed
    if (!success && check.consecutiveFailures >= this.config.alertThreshold) {
      this.triggerAlert(name, check, checkResult)
    }
    
    // Attempt auto-healing if enabled
    if (!success && this.config.autoHealingEnabled) {
      this.attemptAutoHealing(name, check, checkResult)
    }
    
    // Emit events
    this.emit(success ? 'checkPassed' : 'checkFailed', {
      name,
      result: checkResult,
      check
    })
    
    logger.debug(`Health check ${success ? 'passed' : 'failed'}: ${name}`, {
      responseTime,
      consecutiveFailures: check.consecutiveFailures,
      status: check.status
    })
  }
  
  /**
   * Update overall system health
   */
  updateSystemHealth() {
    const checks = Array.from(this.checks.values())
    const healthyChecks = checks.filter(c => c.status === 'healthy')
    const criticalChecks = checks.filter(c => c.critical)
    const unhealthyCritical = criticalChecks.filter(c => c.status !== 'healthy')
    
    // Calculate health percentages
    const overallHealth = checks.length > 0 ? healthyChecks.length / checks.length : 1
    const criticalHealth = criticalChecks.length > 0 ? 
      (criticalChecks.length - unhealthyCritical.length) / criticalChecks.length : 1
    
    // Determine system status
    let systemStatus = 'healthy'
    if (unhealthyCritical.length > 0) {
      systemStatus = 'critical'
    } else if (overallHealth < this.config.unhealthyThreshold) {
      systemStatus = 'unhealthy'
    } else if (overallHealth < this.config.degradedThreshold) {
      systemStatus = 'degraded'
    }
    
    // Update system health object
    const previousStatus = this.systemHealth.status
    this.systemHealth = {
      status: systemStatus,
      lastCheck: Date.now(),
      uptime: process.uptime(),
      checks: this.getCheckSummary(),
      degradedServices: this.getDegradedServices(),
      criticalIssues: this.getCriticalIssues(),
      performanceMetrics: this.getPerformanceMetrics(),
      healthPercentage: Math.round(overallHealth * 100),
      criticalHealthPercentage: Math.round(criticalHealth * 100)
    }
    
    // Add to history
    this.addToHealthHistory(this.systemHealth)
    
    // Emit status change event
    if (previousStatus !== systemStatus) {
      this.emit('systemStatusChange', {
        from: previousStatus,
        to: systemStatus,
        timestamp: Date.now(),
        systemHealth: this.systemHealth
      })
      
      logger.warn(`🚨 System health status changed: ${previousStatus} → ${systemStatus}`, {
        healthPercentage: this.systemHealth.healthPercentage,
        criticalIssues: this.systemHealth.criticalIssues.length
      })
    }
  }
  
  /**
   * Get summary of all checks
   */
  getCheckSummary() {
    const summary = {}
    for (const [name, check] of this.checks) {
      summary[name] = {
        status: check.status,
        lastCheck: check.lastCheck,
        consecutiveFailures: check.consecutiveFailures,
        successRate: this.calculateSuccessRate(check),
        avgResponseTime: this.calculateAvgResponseTime(check),
        critical: check.critical
      }
    }
    return summary
  }
  
  /**
   * Get list of degraded services
   */
  getDegradedServices() {
    return Array.from(this.checks.entries())
      .filter(([_, check]) => {
        const successRate = this.calculateSuccessRate(check)
        return successRate < this.config.degradedThreshold && successRate >= this.config.unhealthyThreshold
      })
      .map(([name, _]) => name)
  }
  
  /**
   * Get list of critical issues
   */
  getCriticalIssues() {
    return Array.from(this.checks.entries())
      .filter(([_, check]) => check.critical && check.status !== 'healthy')
      .map(([name, check]) => ({
        service: name,
        status: check.status,
        consecutiveFailures: check.consecutiveFailures,
        lastError: check.lastResult?.result,
        since: check.lastCheck
      }))
  }
  
  /**
   * Calculate success rate for a check
   */
  calculateSuccessRate(check) {
    if (check.history.length === 0) return 1
    
    const recentHistory = check.history.slice(-20) // Last 20 checks
    const successes = recentHistory.filter(h => h.success).length
    return successes / recentHistory.length
  }
  
  /**
   * Calculate average response time for a check
   */
  calculateAvgResponseTime(check) {
    if (check.history.length === 0) return 0
    
    const recentHistory = check.history.slice(-20)
    const totalTime = recentHistory.reduce((sum, h) => sum + h.responseTime, 0)
    return Math.round(totalTime / recentHistory.length)
  }
  
  /**
   * Add to health history for trend analysis
   */
  addToHealthHistory(healthSnapshot) {
    this.healthHistory.push({
      ...healthSnapshot,
      timestamp: Date.now()
    })
    
    // Keep only recent history
    if (this.healthHistory.length > this.config.historySize) {
      this.healthHistory.shift()
    }
  }
  
  /**
   * Trigger alert for failed health check
   */
  triggerAlert(name, check, result) {
    const alert = {
      type: 'health_check_failure',
      service: name,
      severity: check.critical ? 'critical' : 'warning',
      message: `Health check failed for ${name}: ${result.result}`,
      consecutiveFailures: check.consecutiveFailures,
      timestamp: Date.now(),
      metadata: {
        responseTime: result.responseTime,
        lastSuccessfulCheck: check.history.findLast(h => h.success)?.timestamp,
        tags: check.tags
      }
    }
    
    this.emit('alert', alert)
    
    logger.error(`🚨 Health check alert: ${name}`, alert)
  }
  
  /**
   * Attempt auto-healing for failed service
   */
  async attemptAutoHealing(name, check, result) {
    const strategy = this.healingStrategies.get(name)
    if (!strategy) return
    
    try {
      logger.info(`🔧 Attempting auto-healing for ${name}`)
      
      const healingResult = await strategy.heal({
        check,
        result,
        systemHealth: this.systemHealth
      })
      
      if (healingResult.success) {
        logger.info(`✅ Auto-healing successful for ${name}`, healingResult)
        this.emit('autoHealingSuccess', { name, result: healingResult })
      } else {
        logger.warn(`❌ Auto-healing failed for ${name}`, healingResult)
        this.emit('autoHealingFailed', { name, result: healingResult })
      }
      
    } catch (error) {
      logger.error(`💥 Auto-healing error for ${name}:`, error)
      this.emit('autoHealingError', { name, error })
    }
  }
  
  /**
   * Register auto-healing strategy
   */
  registerHealingStrategy(serviceName, strategy) {
    this.healingStrategies.set(serviceName, {
      heal: strategy.heal,
      conditions: strategy.conditions || {},
      cooldown: strategy.cooldown || 300000, // 5min default cooldown
      lastAttempt: null
    })
    
    logger.info(`🔧 Auto-healing strategy registered for ${serviceName}`)
  }
  
  /**
   * Get current performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      timestamp: Date.now()
    }
  }
  
  /**
   * Initialize core system health checks
   */
  initializeCoreChecks() {
    // Memory usage check
    this.registerCheck('memory', async () => {
      const memUsage = process.memoryUsage()
      const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100
      
      if (memUsagePercent > 90) {
        throw new Error(`High memory usage: ${memUsagePercent.toFixed(1)}%`)
      }
      
      return { memoryUsage: memUsagePercent, details: memUsage }
    }, { 
      critical: true, 
      interval: 30000,
      description: 'Monitor system memory usage'
    })
    
    // Event loop lag check
    this.registerCheck('eventloop', async () => {
      const start = process.hrtime.bigint()
      await new Promise(resolve => setImmediate(resolve))
      const lag = Number(process.hrtime.bigint() - start) / 1e6 // Convert to ms
      
      if (lag > 100) {
        throw new Error(`High event loop lag: ${lag.toFixed(2)}ms`)
      }
      
      return { eventLoopLag: lag }
    }, { 
      critical: true, 
      interval: 15000,
      description: 'Monitor Node.js event loop performance'
    })
  }
  
  /**
   * Get health check status
   */
  getStatus() {
    return this.systemHealth
  }
  
  /**
   * Get detailed health report
   */
  getDetailedReport() {
    return {
      systemHealth: this.systemHealth,
      checks: Object.fromEntries(
        Array.from(this.checks.entries()).map(([name, check]) => [
          name,
          {
            ...check,
            successRate: this.calculateSuccessRate(check),
            avgResponseTime: this.calculateAvgResponseTime(check),
            history: check.history.slice(-10) // Last 10 results
          }
        ])
      ),
      dependencies: Object.fromEntries(this.dependencies),
      healthHistory: this.healthHistory.slice(-10), // Last 10 snapshots
      performanceMetrics: this.getPerformanceMetrics()
    }
  }
  
  /**
   * Enable/disable a health check
   */
  toggleCheck(name, enabled) {
    const check = this.checks.get(name)
    if (!check) return false
    
    check.enabled = enabled
    
    if (enabled) {
      this.startCheckInterval(name)
    } else {
      const interval = this.intervals.get(name)
      if (interval) {
        clearInterval(interval)
        this.intervals.delete(name)
      }
    }
    
    logger.info(`Health check ${enabled ? 'enabled' : 'disabled'}: ${name}`)
    return true
  }
  
  /**
   * Cleanup and shutdown
   */
  shutdown() {
    // Clear all intervals
    for (const interval of this.intervals.values()) {
      clearInterval(interval)
    }
    this.intervals.clear()
    
    logger.info('🏥 Health Check Manager shutdown complete')
  }
}

// Global health check manager instance
export const healthCheck = new HealthCheckManager()

/**
 * 🌡️ Health Check Express Middleware
 */
export function createHealthCheckMiddleware(path = '/health') {
  return (req, res, next) => {
    if (req.path === path) {
      const detailed = req.query.detailed === 'true'
      const report = detailed ? healthCheck.getDetailedReport() : healthCheck.getStatus()
      
      // Set appropriate HTTP status code
      const status = report.status === 'healthy' ? 200 :
                   report.status === 'degraded' ? 200 :
                   report.status === 'unhealthy' ? 503 : 503
      
      return res.status(status).json(report)
    }
    
    next()
  }
}

/**
 * 🔧 Common Auto-Healing Strategies
 */
export const healingStrategies = {
  // Restart service strategy
  restartService: (serviceName, restartCommand) => ({
    async heal({ check, result }) {
      const { exec } = await import('child_process')
      const { promisify } = await import('util')
      const execAsync = promisify(exec)
      
      try {
        await execAsync(restartCommand)
        return { success: true, action: 'service_restart', service: serviceName }
      } catch (error) {
        return { success: false, action: 'service_restart_failed', error: error.message }
      }
    },
    conditions: { consecutiveFailures: 3 },
    cooldown: 300000 // 5 minutes
  }),
  
  // Clear cache strategy
  clearCache: (cacheService) => ({
    async heal({ check, result }) {
      try {
        await cacheService.clear()
        return { success: true, action: 'cache_cleared' }
      } catch (error) {
        return { success: false, action: 'cache_clear_failed', error: error.message }
      }
    },
    conditions: { consecutiveFailures: 2 },
    cooldown: 60000 // 1 minute
  }),
  
  // Database reconnection strategy
  reconnectDatabase: (dbConnection) => ({
    async heal({ check, result }) {
      try {
        await dbConnection.disconnect()
        await dbConnection.connect()
        return { success: true, action: 'database_reconnected' }
      } catch (error) {
        return { success: false, action: 'database_reconnect_failed', error: error.message }
      }
    },
    conditions: { consecutiveFailures: 2 },
    cooldown: 120000 // 2 minutes
  })
}