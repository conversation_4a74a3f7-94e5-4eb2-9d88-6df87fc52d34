import { logger } from '../../backend/utils/logger.js'
import { EventEmitter } from 'events'

/**
 * 🎭 Advanced Graceful Degradation System
 * Planetary Scale Resilience Engineering - Phase 3
 * 
 * Features:
 * - Feature toggle management with real-time switching
 * - Service dependency mapping and cascade failure prevention
 * - Performance-based degradation triggers
 * - User experience preservation during partial outages
 * - Automatic recovery and feature restoration
 * - A/B testing integration for degraded states
 */

export class GracefulDegradationManager extends EventEmitter {
  constructor(options = {}) {
    super()
    
    this.config = {
      // Performance thresholds
      responseTimeThreshold: options.responseTimeThreshold || 2000,    // 2s
      errorRateThreshold: options.errorRateThreshold || 5,            // 5%
      cpuThreshold: options.cpuThreshold || 80,                       // 80%
      memoryThreshold: options.memoryThreshold || 85,                 // 85%
      
      // Monitoring intervals
      monitoringInterval: options.monitoringInterval || 10000,        // 10s
      degradationCooldown: options.degradationCooldown || 60000,      // 1min
      recoveryCheckInterval: options.recoveryCheckInterval || 30000,   // 30s
      
      // Feature management
      defaultDegradationLevel: options.defaultDegradationLevel || 'minimal',
      autoRecoveryEnabled: options.autoRecoveryEnabled !== false,
      cascadePreventionEnabled: options.cascadePreventionEnabled !== false,
      
      // User experience
      gracefulErrorMessages: options.gracefulErrorMessages !== false,
      featureFallbackEnabled: options.featureFallbackEnabled !== false,
      
      ...options
    }
    
    // System state tracking
    this.systemState = {
      currentLevel: 'normal',
      degradationActive: false,
      degradationReason: null,
      degradationStartTime: null,
      lastPerformanceCheck: null,
      activeFeatures: new Set(),
      disabledFeatures: new Set()
    }
    
    // Feature registry with dependency mapping
    this.features = new Map()
    
    // Service dependency graph
    this.serviceDependencies = new Map()
    
    // Degradation levels with specific feature sets
    this.degradationLevels = new Map()
    
    // Performance metrics tracking
    this.performanceMetrics = {
      responseTime: { current: 0, average: 0, p95: 0 },
      errorRate: { current: 0, average: 0 },
      systemLoad: { cpu: 0, memory: 0, disk: 0 },
      requestCount: 0,
      lastReset: Date.now()
    }
    
    // User experience preservation strategies
    this.fallbackStrategies = new Map()
    
    // Recovery tracking
    this.recoveryHistory = []
    
    // Initialize default configuration
    this.initializeDefaultLevels()
    this.initializeDefaultStrategies()
    this.startMonitoring()
    
    logger.info('🎭 Graceful Degradation Manager initialized', {
      levels: this.degradationLevels.size,
      features: this.features.size
    })
  }
  
  /**
   * Register a feature with degradation capabilities
   */
  registerFeature(name, config) {
    const feature = {
      name,
      priority: config.priority || 1,              // Higher = more important
      category: config.category || 'standard',     // core, standard, enhanced, experimental
      dependencies: config.dependencies || [],     // Other features this depends on
      services: config.services || [],             // External services this requires
      fallbackImplementation: config.fallback || null,
      degradationLevels: config.degradationLevels || ['minimal', 'reduced', 'disabled'],
      enabled: config.enabled !== false,
      canDegrade: config.canDegrade !== false,
      description: config.description || '',
      userImpact: config.userImpact || 'low',      // low, medium, high, critical
      performanceImpact: config.performanceImpact || 'low',
      resourceUsage: config.resourceUsage || { cpu: 10, memory: 10, io: 10 },
      lastToggle: null,
      toggleCount: 0,
      status: 'active'
    }
    
    this.features.set(name, feature)
    this.systemState.activeFeatures.add(name)
    
    // Register service dependencies
    if (feature.services.length > 0) {
      this.serviceDependencies.set(name, feature.services)
    }
    
    logger.info(`🎛️ Feature registered: ${name}`, {
      category: feature.category,
      priority: feature.priority,
      dependencies: feature.dependencies.length,
      services: feature.services.length
    })
    
    this.emit('featureRegistered', { name, feature })
    
    return this
  }
  
  /**
   * Register degradation level
   */
  registerDegradationLevel(level, config) {
    const levelConfig = {
      name: level,
      description: config.description || '',
      enabledFeatures: new Set(config.enabledFeatures || []),
      disabledFeatures: new Set(config.disabledFeatures || []),
      resourceLimits: config.resourceLimits || {},
      performanceTargets: config.performanceTargets || {},
      userExperienceChanges: config.userExperienceChanges || [],
      fallbackBehaviors: config.fallbackBehaviors || {},
      severity: config.severity || 1,              // 1-5 scale
      triggerConditions: config.triggerConditions || {},
      ...config
    }
    
    this.degradationLevels.set(level, levelConfig)
    
    logger.info(`📊 Degradation level registered: ${level}`, {
      enabledFeatures: levelConfig.enabledFeatures.size,
      disabledFeatures: levelConfig.disabledFeatures.size,
      severity: levelConfig.severity
    })
    
    return this
  }
  
  /**
   * Register fallback strategy for a feature
   */
  registerFallbackStrategy(featureName, strategy) {
    const fallbackStrategy = {
      feature: featureName,
      type: strategy.type || 'cached',             // cached, simplified, redirect, disabled
      implementation: strategy.implementation,
      cacheKey: strategy.cacheKey || null,
      cacheTTL: strategy.cacheTTL || 300000,       // 5 minutes
      simplifiedLogic: strategy.simplifiedLogic || null,
      redirectUrl: strategy.redirectUrl || null,
      errorMessage: strategy.errorMessage || 'Feature temporarily unavailable',
      gracefulMessage: strategy.gracefulMessage || 'Using simplified version',
      analyticsEvents: strategy.analyticsEvents || [],
      ...strategy
    }
    
    this.fallbackStrategies.set(featureName, fallbackStrategy)
    
    logger.info(`🔄 Fallback strategy registered for: ${featureName}`, {
      type: fallbackStrategy.type
    })
    
    return this
  }
  
  /**
   * Initialize default degradation levels
   */
  initializeDefaultLevels() {
    // Normal operation - all features enabled
    this.registerDegradationLevel('normal', {
      description: 'Full functionality with all features enabled',
      enabledFeatures: ['all'],
      severity: 0
    })
    
    // Minimal degradation - disable experimental features
    this.registerDegradationLevel('minimal', {
      description: 'Disable experimental and non-essential features',
      disabledFeatures: ['experimental_ai', 'advanced_analytics', 'social_sharing'],
      resourceLimits: { maxConcurrentRequests: 1000 },
      severity: 1,
      triggerConditions: {
        responseTime: 1500,
        errorRate: 3
      }
    })
    
    // Reduced functionality - keep only core features
    this.registerDegradationLevel('reduced', {
      description: 'Core features only, enhanced features disabled',
      enabledFeatures: ['authentication', 'core_api', 'basic_ui'],
      disabledFeatures: ['file_upload', 'real_time_updates', 'notifications'],
      resourceLimits: { maxConcurrentRequests: 500 },
      severity: 2,
      triggerConditions: {
        responseTime: 2500,
        errorRate: 8,
        cpuUsage: 85
      }
    })
    
    // Essential only - absolute minimum functionality
    this.registerDegradationLevel('essential', {
      description: 'Essential features only for basic operation',
      enabledFeatures: ['authentication', 'read_only_api'],
      disabledFeatures: ['write_operations', 'complex_queries', 'file_processing'],
      resourceLimits: { maxConcurrentRequests: 200 },
      severity: 3,
      triggerConditions: {
        responseTime: 5000,
        errorRate: 15,
        cpuUsage: 95,
        memoryUsage: 90
      }
    })
    
    // Emergency mode - read-only with cached responses
    this.registerDegradationLevel('emergency', {
      description: 'Emergency read-only mode with cached responses',
      enabledFeatures: ['cached_read_only'],
      disabledFeatures: ['all_write_operations', 'dynamic_content'],
      resourceLimits: { maxConcurrentRequests: 100 },
      severity: 4,
      triggerConditions: {
        errorRate: 25,
        systemFailures: 5
      }
    })
  }
  
  /**
   * Initialize default fallback strategies
   */
  initializeDefaultStrategies() {
    // AI Generation fallback to cached responses
    this.registerFallbackStrategy('ai_generation', {
      type: 'cached',
      implementation: async (request) => {
        // Return pre-generated popular templates
        return this.getCachedAIResponse(request.type, request.industry)
      },
      gracefulMessage: 'Using pre-optimized template for faster response'
    })
    
    // Real-time features fallback to polling
    this.registerFallbackStrategy('real_time_updates', {
      type: 'simplified',
      implementation: async (request) => {
        // Switch from WebSocket to polling
        return this.switchToPolling(request.userId)
      },
      gracefulMessage: 'Updates will refresh every 30 seconds'
    })
    
    // File upload fallback to simplified uploader
    this.registerFallbackStrategy('advanced_file_upload', {
      type: 'simplified',
      implementation: async (file) => {
        // Use basic file upload without preview/processing
        return this.basicFileUpload(file)
      },
      gracefulMessage: 'Using simplified upload (no preview available)'
    })
    
    // Analytics fallback to essential metrics only
    this.registerFallbackStrategy('advanced_analytics', {
      type: 'cached',
      implementation: async (request) => {
        // Return cached summary instead of real-time calculation
        return this.getCachedAnalytics(request.timeframe)
      },
      gracefulMessage: 'Showing cached analytics data'
    })
  }
  
  /**
   * Check system performance and trigger degradation if needed
   */
  async checkSystemPerformance() {
    try {
      // Gather current performance metrics
      const metrics = await this.gatherPerformanceMetrics()
      this.updatePerformanceMetrics(metrics)
      
      // Determine if degradation is needed
      const degradationNeeded = this.shouldDegrade(metrics)
      
      if (degradationNeeded && !this.systemState.degradationActive) {
        const targetLevel = this.selectDegradationLevel(metrics)
        await this.activateDegradation(targetLevel, 'performance_threshold', metrics)
      } else if (!degradationNeeded && this.systemState.degradationActive) {
        // Check if we can recover
        if (await this.canRecover(metrics)) {
          await this.recoverFromDegradation()
        }
      }
      
      this.systemState.lastPerformanceCheck = Date.now()
      
    } catch (error) {
      logger.error('Performance check failed:', error)
    }
  }
  
  /**
   * Gather current system performance metrics
   */
  async gatherPerformanceMetrics() {
    const metrics = {
      timestamp: Date.now(),
      responseTime: {
        current: await this.measureCurrentResponseTime(),
        p95: await this.getP95ResponseTime(),
        average: await this.getAverageResponseTime()
      },
      errorRate: {
        current: await this.getCurrentErrorRate(),
        average: await this.getAverageErrorRate()
      },
      systemLoad: {
        cpu: await this.getCPUUsage(),
        memory: await this.getMemoryUsage(),
        disk: await this.getDiskUsage()
      },
      serviceHealth: await this.getServiceHealthMetrics(),
      requestCount: await this.getCurrentRequestCount()
    }
    
    return metrics
  }
  
  /**
   * Determine if system degradation is needed
   */
  shouldDegrade(metrics) {
    const conditions = [
      metrics.responseTime.current > this.config.responseTimeThreshold,
      metrics.responseTime.p95 > this.config.responseTimeThreshold * 1.5,
      metrics.errorRate.current > this.config.errorRateThreshold,
      metrics.systemLoad.cpu > this.config.cpuThreshold,
      metrics.systemLoad.memory > this.config.memoryThreshold
    ]
    
    // Need at least 2 conditions to trigger degradation
    const failingConditions = conditions.filter(Boolean).length
    return failingConditions >= 2
  }
  
  /**
   * Select appropriate degradation level based on metrics
   */
  selectDegradationLevel(metrics) {
    // Start with minimal and escalate based on severity
    let targetLevel = 'minimal'
    
    // Check each degradation level's trigger conditions
    for (const [level, config] of this.degradationLevels) {
      if (level === 'normal') continue
      
      const conditions = config.triggerConditions || {}
      let conditionsMet = 0
      let totalConditions = 0
      
      for (const [condition, threshold] of Object.entries(conditions)) {
        totalConditions++
        
        switch (condition) {
          case 'responseTime':
            if (metrics.responseTime.current >= threshold) conditionsMet++
            break
          case 'errorRate':
            if (metrics.errorRate.current >= threshold) conditionsMet++
            break
          case 'cpuUsage':
            if (metrics.systemLoad.cpu >= threshold) conditionsMet++
            break
          case 'memoryUsage':
            if (metrics.systemLoad.memory >= threshold) conditionsMet++
            break
        }
      }
      
      // If most conditions are met, use this level
      if (conditionsMet / totalConditions >= 0.6) {
        targetLevel = level
      }
    }
    
    return targetLevel
  }
  
  /**
   * Activate degradation to specified level
   */
  async activateDegradation(level, reason, metrics) {
    const levelConfig = this.degradationLevels.get(level)
    if (!levelConfig) {
      throw new Error(`Unknown degradation level: ${level}`)
    }
    
    logger.warn(`🎭 Activating graceful degradation: ${level}`, {
      reason,
      metrics: this.summarizeMetrics(metrics)
    })
    
    try {
      // Update system state
      this.systemState.currentLevel = level
      this.systemState.degradationActive = true
      this.systemState.degradationReason = reason
      this.systemState.degradationStartTime = Date.now()
      
      // Apply feature changes
      await this.applyFeatureChanges(levelConfig)
      
      // Apply resource limits
      await this.applyResourceLimits(levelConfig.resourceLimits)
      
      // Activate fallback strategies
      await this.activateFallbackStrategies(levelConfig)
      
      // Update user experience
      await this.updateUserExperience(levelConfig.userExperienceChanges)
      
      this.emit('degradationActivated', {
        level,
        reason,
        timestamp: Date.now(),
        metrics,
        config: levelConfig
      })
      
      logger.info(`✅ Graceful degradation activated: ${level}`)
      
    } catch (error) {
      logger.error(`Failed to activate degradation level ${level}:`, error)
      throw error
    }
  }
  
  /**
   * Apply feature changes for degradation level
   */
  async applyFeatureChanges(levelConfig) {
    // Disable specified features
    for (const featureName of levelConfig.disabledFeatures) {
      if (featureName === 'all_write_operations') {
        await this.disableAllWriteOperations()
      } else {
        await this.disableFeature(featureName)
      }
    }
    
    // If enabledFeatures is specified and not 'all', disable others
    if (levelConfig.enabledFeatures.size > 0 && !levelConfig.enabledFeatures.has('all')) {
      for (const [featureName, feature] of this.features) {
        if (!levelConfig.enabledFeatures.has(featureName) && feature.canDegrade) {
          await this.disableFeature(featureName)
        }
      }
    }
  }
  
  /**
   * Disable a specific feature with graceful fallback
   */
  async disableFeature(featureName) {
    const feature = this.features.get(featureName)
    if (!feature || feature.status === 'disabled') return
    
    logger.info(`🔌 Disabling feature: ${featureName}`)
    
    try {
      // Check for fallback strategy
      const fallback = this.fallbackStrategies.get(featureName)
      if (fallback) {
        await this.activateFeatureFallback(featureName, fallback)
      }
      
      // Update feature status
      feature.status = 'disabled'
      feature.lastToggle = Date.now()
      feature.toggleCount++
      
      // Remove from active features
      this.systemState.activeFeatures.delete(featureName)
      this.systemState.disabledFeatures.add(featureName)
      
      // Handle dependent features
      await this.handleFeatureDependencies(featureName, 'disable')
      
      this.emit('featureDisabled', { featureName, timestamp: Date.now() })
      
    } catch (error) {
      logger.error(`Failed to disable feature ${featureName}:`, error)
    }
  }
  
  /**
   * Enable a specific feature during recovery
   */
  async enableFeature(featureName) {
    const feature = this.features.get(featureName)
    if (!feature || feature.status === 'active') return
    
    logger.info(`🔌 Enabling feature: ${featureName}`)
    
    try {
      // Check dependencies are available
      const dependenciesAvailable = await this.checkFeatureDependencies(featureName)
      if (!dependenciesAvailable) {
        logger.warn(`Cannot enable ${featureName}: dependencies not available`)
        return
      }
      
      // Deactivate fallback if active
      await this.deactivateFeatureFallback(featureName)
      
      // Update feature status
      feature.status = 'active'
      feature.lastToggle = Date.now()
      feature.toggleCount++
      
      // Add to active features
      this.systemState.activeFeatures.add(featureName)
      this.systemState.disabledFeatures.delete(featureName)
      
      this.emit('featureEnabled', { featureName, timestamp: Date.now() })
      
    } catch (error) {
      logger.error(`Failed to enable feature ${featureName}:`, error)
    }
  }
  
  /**
   * Activate fallback strategy for a feature
   */
  async activateFeatureFallback(featureName, fallbackStrategy) {
    logger.info(`🔄 Activating fallback for feature: ${featureName}`)
    
    try {
      switch (fallbackStrategy.type) {
        case 'cached':
          await this.setupCachedFallback(featureName, fallbackStrategy)
          break
          
        case 'simplified':
          await this.setupSimplifiedFallback(featureName, fallbackStrategy)
          break
          
        case 'redirect':
          await this.setupRedirectFallback(featureName, fallbackStrategy)
          break
          
        case 'disabled':
          await this.setupDisabledFallback(featureName, fallbackStrategy)
          break
      }
      
      // Track analytics events
      for (const event of fallbackStrategy.analyticsEvents) {
        this.emit('analyticsEvent', {
          event,
          feature: featureName,
          fallbackType: fallbackStrategy.type,
          timestamp: Date.now()
        })
      }
      
    } catch (error) {
      logger.error(`Failed to activate fallback for ${featureName}:`, error)
    }
  }
  
  /**
   * Check if system can recover from degradation
   */
  async canRecover(metrics) {
    if (!this.systemState.degradationActive) return false
    
    // Must be in degraded state for minimum cooldown period
    const timeDegraded = Date.now() - this.systemState.degradationStartTime
    if (timeDegraded < this.config.degradationCooldown) return false
    
    // Check if metrics are back to healthy levels
    const healthy = [
      metrics.responseTime.current < this.config.responseTimeThreshold * 0.8,
      metrics.errorRate.current < this.config.errorRateThreshold * 0.5,
      metrics.systemLoad.cpu < this.config.cpuThreshold * 0.8,
      metrics.systemLoad.memory < this.config.memoryThreshold * 0.8
    ]
    
    // Need all conditions to be healthy for recovery
    return healthy.every(Boolean)
  }
  
  /**
   * Recover from degradation back to normal operation
   */
  async recoverFromDegradation() {
    logger.info('🎭 Starting recovery from graceful degradation')
    
    try {
      const previousLevel = this.systemState.currentLevel
      const degradationDuration = Date.now() - this.systemState.degradationStartTime
      
      // Gradually re-enable features
      await this.gradualFeatureRecovery()
      
      // Remove resource limits
      await this.removeResourceLimits()
      
      // Deactivate fallback strategies
      await this.deactivateFallbackStrategies()
      
      // Update system state
      this.systemState.currentLevel = 'normal'
      this.systemState.degradationActive = false
      this.systemState.degradationReason = null
      this.systemState.degradationStartTime = null
      
      // Record recovery
      const recoveryRecord = {
        timestamp: Date.now(),
        previousLevel,
        degradationDuration,
        featuresRecovered: this.systemState.activeFeatures.size,
        reason: 'performance_improved'
      }
      
      this.recoveryHistory.push(recoveryRecord)
      
      this.emit('degradationRecovered', recoveryRecord)
      
      logger.info('✅ Recovery from graceful degradation completed', {
        previousLevel,
        duration: degradationDuration,
        featuresRecovered: recoveryRecord.featuresRecovered
      })
      
    } catch (error) {
      logger.error('Failed to recover from degradation:', error)
      throw error
    }
  }
  
  /**
   * Gradually re-enable features during recovery
   */
  async gradualFeatureRecovery() {
    // Sort features by priority (higher priority first)
    const disabledFeatures = Array.from(this.systemState.disabledFeatures)
      .map(name => ({ name, feature: this.features.get(name) }))
      .filter(({ feature }) => feature)
      .sort((a, b) => b.feature.priority - a.feature.priority)
    
    // Re-enable features gradually
    for (const { name } of disabledFeatures) {
      await this.enableFeature(name)
      
      // Small delay between enablements to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  /**
   * Start monitoring system performance
   */
  startMonitoring() {
    // Regular performance checks
    setInterval(async () => {
      await this.checkSystemPerformance()
    }, this.config.monitoringInterval)
    
    // Recovery checks when degraded
    setInterval(async () => {
      if (this.systemState.degradationActive && this.config.autoRecoveryEnabled) {
        const metrics = await this.gatherPerformanceMetrics()
        if (await this.canRecover(metrics)) {
          await this.recoverFromDegradation()
        }
      }
    }, this.config.recoveryCheckInterval)
  }
  
  /**
   * Get current graceful degradation status
   */
  getStatus() {
    return {
      systemState: this.systemState,
      currentLevel: this.systemState.currentLevel,
      degradationActive: this.systemState.degradationActive,
      activeFeatures: Array.from(this.systemState.activeFeatures),
      disabledFeatures: Array.from(this.systemState.disabledFeatures),
      performanceMetrics: this.performanceMetrics,
      availableLevels: Array.from(this.degradationLevels.keys()),
      registeredFeatures: Array.from(this.features.keys()),
      recoveryHistory: this.recoveryHistory.slice(-5) // Last 5 recoveries
    }
  }
  
  /**
   * Force degradation to specific level (for testing/emergency)
   */
  async forceDegradation(level, reason = 'manual') {
    await this.activateDegradation(level, reason, await this.gatherPerformanceMetrics())
  }
  
  /**
   * Force recovery (for testing/manual intervention)
   */
  async forceRecovery() {
    await this.recoverFromDegradation()
  }
  
  // Placeholder implementations for metrics gathering
  async measureCurrentResponseTime() { return Math.random() * 1000 }
  async getP95ResponseTime() { return Math.random() * 2000 }
  async getAverageResponseTime() { return Math.random() * 800 }
  async getCurrentErrorRate() { return Math.random() * 10 }
  async getAverageErrorRate() { return Math.random() * 5 }
  async getCPUUsage() { return Math.random() * 100 }
  async getMemoryUsage() { return Math.random() * 100 }
  async getDiskUsage() { return Math.random() * 100 }
  async getServiceHealthMetrics() { return {} }
  async getCurrentRequestCount() { return Math.floor(Math.random() * 1000) }
  
  updatePerformanceMetrics(metrics) {
    this.performanceMetrics = { ...metrics, lastUpdate: Date.now() }
  }
  
  summarizeMetrics(metrics) {
    return {
      responseTime: metrics.responseTime.current,
      errorRate: metrics.errorRate.current,
      cpu: metrics.systemLoad.cpu,
      memory: metrics.systemLoad.memory
    }
  }
  
  // Placeholder implementations for feature management
  async applyResourceLimits(limits) {}
  async removeResourceLimits() {}
  async updateUserExperience(changes) {}
  async activateFallbackStrategies(config) {}
  async deactivateFallbackStrategies() {}
  async handleFeatureDependencies(featureName, action) {}
  async checkFeatureDependencies(featureName) { return true }
  async deactivateFeatureFallback(featureName) {}
  async setupCachedFallback(featureName, strategy) {}
  async setupSimplifiedFallback(featureName, strategy) {}
  async setupRedirectFallback(featureName, strategy) {}
  async setupDisabledFallback(featureName, strategy) {}
  async disableAllWriteOperations() {}
  async getCachedAIResponse(type, industry) { return {} }
  async switchToPolling(userId) { return {} }
  async basicFileUpload(file) { return {} }
  async getCachedAnalytics(timeframe) { return {} }
}

// Global graceful degradation manager instance
export const gracefulDegradation = new GracefulDegradationManager()