import { logger } from '../../backend/utils/logger.js'
import { EventEmitter } from 'events'

/**
 * 🛡️ Advanced Circuit Breaker Implementation
 * Planetary Scale Resilience Engineering - Phase 3
 * 
 * Features:
 * - Automatic failure detection and recovery
 * - Configurable thresholds and timeout windows
 * - Exponential backoff with jitter
 * - Health monitoring and metrics collection
 * - Multi-level circuit breaking (service, endpoint, operation)
 */

export class CircuitBreaker extends EventEmitter {
  constructor(options = {}) {
    super()
    
    // Configuration with intelligent defaults
    this.config = {
      failureThreshold: options.failureThreshold || 5,        // Failures before opening
      recoveryTimeout: options.recoveryTimeout || 30000,      // 30s recovery window
      monitoringPeriod: options.monitoringPeriod || 60000,    // 1min monitoring window
      expectedResponseTime: options.expectedResponseTime || 1000, // 1s max response
      volumeThreshold: options.volumeThreshold || 10,         // Min requests before triggering
      errorPercentageThreshold: options.errorPercentageThreshold || 50, // 50% error rate
      halfOpenMaxRequests: options.halfOpenMaxRequests || 3,  // Test requests in half-open
      exponentialBackoffBase: options.exponentialBackoffBase || 2,
      maxBackoffTime: options.maxBackoffTime || 300000,       // 5min max backoff
      jitterEnabled: options.jitterEnabled !== false,        // Add randomness to backoff
      ...options
    }
    
    // Circuit states
    this.states = {
      CLOSED: 'CLOSED',      // Normal operation
      OPEN: 'OPEN',          // Failing fast
      HALF_OPEN: 'HALF_OPEN' // Testing recovery
    }
    
    // Current state tracking
    this.state = this.states.CLOSED
    this.failureCount = 0
    this.successCount = 0
    this.requestCount = 0
    this.lastFailureTime = null
    this.nextAttemptTime = null
    this.halfOpenAttempts = 0
    
    // Performance metrics
    this.metrics = {
      totalRequests: 0,
      totalFailures: 0,
      totalSuccesses: 0,
      averageResponseTime: 0,
      lastErrorRate: 0,
      circuitOpenCount: 0,
      circuitHalfOpenCount: 0,
      lastStateChange: Date.now(),
      responseTimeBuckets: new Map(), // For histogram
      errorTypes: new Map()
    }
    
    // Health monitoring
    this.healthCheck = {
      lastCheck: Date.now(),
      consecutiveFailures: 0,
      isHealthy: true
    }
    
    // Setup monitoring intervals
    this.setupMonitoring()
    
    logger.info(`🛡️ Circuit Breaker initialized for ${options.name || 'service'}`, {
      config: this.config,
      state: this.state
    })
  }
  
  /**
   * Execute a function with circuit breaker protection
   */
  async execute(operation, fallback = null) {
    const startTime = Date.now()
    this.metrics.totalRequests++
    this.requestCount++
    
    // Check circuit state
    if (this.state === this.states.OPEN) {
      if (this.canAttemptReset()) {
        this.moveToHalfOpen()
      } else {
        return this.handleCircuitOpen(fallback, startTime)
      }
    }
    
    if (this.state === this.states.HALF_OPEN && this.halfOpenAttempts >= this.config.halfOpenMaxRequests) {
      return this.handleCircuitOpen(fallback, startTime)
    }
    
    try {
      // Execute the operation with timeout
      const result = await this.executeWithTimeout(operation)
      const responseTime = Date.now() - startTime
      
      // Record success
      this.onSuccess(responseTime)
      
      return result
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      // Record failure
      this.onFailure(error, responseTime)
      
      // Return fallback or rethrow
      if (fallback && typeof fallback === 'function') {
        try {
          return await fallback(error)
        } catch (fallbackError) {
          logger.error('Circuit breaker fallback failed', { error: fallbackError })
          throw error
        }
      }
      
      throw error
    }
  }
  
  /**
   * Execute operation with configurable timeout
   */
  async executeWithTimeout(operation) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Operation timeout after ${this.config.expectedResponseTime}ms`))
      }, this.config.expectedResponseTime)
      
      Promise.resolve(operation())
        .then(result => {
          clearTimeout(timeout)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timeout)
          reject(error)
        })
    })
  }
  
  /**
   * Handle successful operation
   */
  onSuccess(responseTime) {
    this.successCount++
    this.metrics.totalSuccesses++
    this.updateResponseTimeMetrics(responseTime)
    
    if (this.state === this.states.HALF_OPEN) {
      this.halfOpenAttempts++
      
      // If we have enough successful half-open attempts, close the circuit
      if (this.halfOpenAttempts >= this.config.halfOpenMaxRequests) {
        this.moveToClosed()
      }
    } else if (this.state === this.states.CLOSED) {
      // Reset failure count on success
      this.failureCount = 0
    }
    
    this.emit('success', { responseTime, state: this.state })
  }
  
  /**
   * Handle failed operation
   */
  onFailure(error, responseTime) {
    this.failureCount++
    this.metrics.totalFailures++
    this.lastFailureTime = Date.now()
    this.updateResponseTimeMetrics(responseTime)
    this.updateErrorMetrics(error)
    
    if (this.state === this.states.HALF_OPEN) {
      // Any failure in half-open state immediately opens the circuit
      this.moveToOpen()
    } else if (this.state === this.states.CLOSED) {
      // Check if we should open the circuit
      if (this.shouldOpenCircuit()) {
        this.moveToOpen()
      }
    }
    
    this.emit('failure', { error, responseTime, state: this.state })
  }
  
  /**
   * Determine if circuit should be opened
   */
  shouldOpenCircuit() {
    // Must have minimum volume
    if (this.requestCount < this.config.volumeThreshold) {
      return false
    }
    
    // Check failure threshold
    if (this.failureCount >= this.config.failureThreshold) {
      return true
    }
    
    // Check error percentage
    const errorRate = (this.failureCount / this.requestCount) * 100
    if (errorRate >= this.config.errorPercentageThreshold) {
      return true
    }
    
    return false
  }
  
  /**
   * Check if we can attempt to reset from OPEN state
   */
  canAttemptReset() {
    if (!this.nextAttemptTime) {
      return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout
    }
    return Date.now() >= this.nextAttemptTime
  }
  
  /**
   * Move circuit to CLOSED state
   */
  moveToClosed() {
    const previousState = this.state
    this.state = this.states.CLOSED
    this.failureCount = 0
    this.halfOpenAttempts = 0
    this.nextAttemptTime = null
    this.metrics.lastStateChange = Date.now()
    
    logger.info('🟢 Circuit breaker CLOSED - Normal operation restored', {
      previousState,
      metrics: this.getMetrics()
    })
    
    this.emit('stateChange', {
      from: previousState,
      to: this.state,
      timestamp: Date.now()
    })
  }
  
  /**
   * Move circuit to OPEN state
   */
  moveToOpen() {
    const previousState = this.state
    this.state = this.states.OPEN
    this.metrics.circuitOpenCount++
    this.metrics.lastStateChange = Date.now()
    
    // Calculate exponential backoff with jitter
    const backoffTime = this.calculateBackoffTime()
    this.nextAttemptTime = Date.now() + backoffTime
    
    logger.warn('🔴 Circuit breaker OPEN - Failing fast', {
      previousState,
      backoffTime,
      nextAttemptTime: new Date(this.nextAttemptTime),
      metrics: this.getMetrics()
    })
    
    this.emit('stateChange', {
      from: previousState,
      to: this.state,
      timestamp: Date.now(),
      backoffTime
    })
  }
  
  /**
   * Move circuit to HALF_OPEN state
   */
  moveToHalfOpen() {
    const previousState = this.state
    this.state = this.states.HALF_OPEN
    this.halfOpenAttempts = 0
    this.metrics.circuitHalfOpenCount++
    this.metrics.lastStateChange = Date.now()
    
    logger.info('🟡 Circuit breaker HALF_OPEN - Testing recovery', {
      previousState,
      maxTestRequests: this.config.halfOpenMaxRequests
    })
    
    this.emit('stateChange', {
      from: previousState,
      to: this.state,
      timestamp: Date.now()
    })
  }
  
  /**
   * Calculate exponential backoff time with jitter
   */
  calculateBackoffTime() {
    const exponentialBackoff = Math.min(
      this.config.recoveryTimeout * Math.pow(this.config.exponentialBackoffBase, this.metrics.circuitOpenCount),
      this.config.maxBackoffTime
    )
    
    if (!this.config.jitterEnabled) {
      return exponentialBackoff
    }
    
    // Add jitter (±25% randomness)
    const jitter = exponentialBackoff * 0.25 * (Math.random() - 0.5) * 2
    return Math.max(exponentialBackoff + jitter, this.config.recoveryTimeout)
  }
  
  /**
   * Handle circuit open state
   */
  async handleCircuitOpen(fallback, startTime) {
    const error = new Error('Circuit breaker is OPEN')
    error.circuitBreakerOpen = true
    
    if (fallback && typeof fallback === 'function') {
      try {
        const result = await fallback(error)
        const responseTime = Date.now() - startTime
        this.emit('fallbackUsed', { responseTime })
        return result
      } catch (fallbackError) {
        logger.error('Circuit breaker fallback failed', { error: fallbackError })
      }
    }
    
    this.emit('circuitOpen', { 
      nextAttemptTime: this.nextAttemptTime,
      backoffRemaining: this.nextAttemptTime - Date.now()
    })
    
    throw error
  }
  
  /**
   * Update response time metrics
   */
  updateResponseTimeMetrics(responseTime) {
    // Update average response time
    const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime
    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalRequests
    
    // Update response time histogram
    const bucket = this.getResponseTimeBucket(responseTime)
    this.metrics.responseTimeBuckets.set(bucket, (this.metrics.responseTimeBuckets.get(bucket) || 0) + 1)
  }
  
  /**
   * Update error metrics
   */
  updateErrorMetrics(error) {
    const errorType = error.name || 'UnknownError'
    this.metrics.errorTypes.set(errorType, (this.metrics.errorTypes.get(errorType) || 0) + 1)
  }
  
  /**
   * Get response time bucket for histogram
   */
  getResponseTimeBucket(responseTime) {
    if (responseTime < 100) return '0-100ms'
    if (responseTime < 500) return '100-500ms'
    if (responseTime < 1000) return '500ms-1s'
    if (responseTime < 5000) return '1-5s'
    return '5s+'
  }
  
  /**
   * Setup monitoring intervals
   */
  setupMonitoring() {
    // Reset request counters periodically
    setInterval(() => {
      this.requestCount = 0
      this.successCount = 0
      
      // Calculate current error rate
      if (this.metrics.totalRequests > 0) {
        this.metrics.lastErrorRate = (this.metrics.totalFailures / this.metrics.totalRequests) * 100
      }
    }, this.config.monitoringPeriod)
    
    // Health check monitoring
    setInterval(() => {
      this.performHealthCheck()
    }, 30000) // Every 30 seconds
  }
  
  /**
   * Perform health check
   */
  performHealthCheck() {
    const now = Date.now()
    const timeSinceLastFailure = this.lastFailureTime ? now - this.lastFailureTime : Infinity
    
    // Consider healthy if no failures in the last monitoring period
    const wasHealthy = this.healthCheck.isHealthy
    this.healthCheck.isHealthy = timeSinceLastFailure > this.config.monitoringPeriod
    this.healthCheck.lastCheck = now
    
    if (wasHealthy !== this.healthCheck.isHealthy) {
      this.emit('healthChange', {
        isHealthy: this.healthCheck.isHealthy,
        timeSinceLastFailure
      })
    }
  }
  
  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      state: this.state,
      ...this.metrics,
      currentFailureCount: this.failureCount,
      currentSuccessCount: this.successCount,
      currentRequestCount: this.requestCount,
      healthCheck: this.healthCheck,
      responseTimeBuckets: Object.fromEntries(this.metrics.responseTimeBuckets),
      errorTypes: Object.fromEntries(this.metrics.errorTypes),
      nextAttemptTime: this.nextAttemptTime
    }
  }
  
  /**
   * Reset circuit breaker to initial state
   */
  reset() {
    this.state = this.states.CLOSED
    this.failureCount = 0
    this.successCount = 0
    this.requestCount = 0
    this.lastFailureTime = null
    this.nextAttemptTime = null
    this.halfOpenAttempts = 0
    
    logger.info('🔄 Circuit breaker manually reset')
    this.emit('reset')
  }
  
  /**
   * Force circuit to open (for testing/maintenance)
   */
  forceOpen(duration = 60000) {
    this.moveToOpen()
    this.nextAttemptTime = Date.now() + duration
    
    logger.warn(`🔴 Circuit breaker forced OPEN for ${duration}ms`)
    this.emit('forceOpen', { duration })
  }
  
  /**
   * Get circuit breaker status summary
   */
  getStatus() {
    return {
      state: this.state,
      isHealthy: this.healthCheck.isHealthy,
      errorRate: this.metrics.lastErrorRate,
      averageResponseTime: this.metrics.averageResponseTime,
      totalRequests: this.metrics.totalRequests,
      circuitOpenCount: this.metrics.circuitOpenCount,
      nextAttemptTime: this.nextAttemptTime
    }
  }
}

/**
 * 🏭 Circuit Breaker Factory
 * Creates and manages multiple circuit breakers for different services
 */
export class CircuitBreakerFactory {
  constructor() {
    this.breakers = new Map()
    this.defaultConfig = {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      monitoringPeriod: 60000,
      expectedResponseTime: 5000,
      volumeThreshold: 10,
      errorPercentageThreshold: 50
    }
  }
  
  /**
   * Create or get circuit breaker for a service
   */
  getBreaker(serviceName, config = {}) {
    if (!this.breakers.has(serviceName)) {
      const breakerConfig = { ...this.defaultConfig, ...config, name: serviceName }
      const breaker = new CircuitBreaker(breakerConfig)
      
      // Add global monitoring
      breaker.on('stateChange', (event) => {
        logger.info(`Circuit breaker state change: ${serviceName}`, event)
      })
      
      breaker.on('failure', (event) => {
        logger.warn(`Circuit breaker failure: ${serviceName}`, {
          error: event.error.message,
          state: event.state
        })
      })
      
      this.breakers.set(serviceName, breaker)
    }
    
    return this.breakers.get(serviceName)
  }
  
  /**
   * Get all circuit breaker statuses
   */
  getAllStatuses() {
    const statuses = {}
    for (const [name, breaker] of this.breakers) {
      statuses[name] = breaker.getStatus()
    }
    return statuses
  }
  
  /**
   * Reset all circuit breakers
   */
  resetAll() {
    for (const breaker of this.breakers.values()) {
      breaker.reset()
    }
  }
}

// Global circuit breaker factory instance
export const circuitBreakerFactory = new CircuitBreakerFactory()

/**
 * 🔌 Circuit Breaker Middleware for Express
 */
export function createCircuitBreakerMiddleware(serviceName, config = {}) {
  const breaker = circuitBreakerFactory.getBreaker(serviceName, config)
  
  return (req, res, next) => {
    // Add circuit breaker to request object
    req.circuitBreaker = breaker
    
    // Add health check endpoint
    if (req.path === '/health/circuit-breaker') {
      return res.json(breaker.getStatus())
    }
    
    next()
  }
}