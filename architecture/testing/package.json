{"name": "@neurocolony/testing-framework", "version": "1.0.0", "description": "Comprehensive testing framework for NeuroColony with DDD patterns", "type": "module", "main": "test-runner.js", "scripts": {"test": "node test-runner.js", "test:unit": "node test-runner.js --unit", "test:integration": "node test-runner.js --integration", "test:e2e": "node test-runner.js --e2e", "test:coverage": "nyc node test-runner.js --coverage", "test:performance": "node test-runner.js --performance", "test:watch": "nodemon --exec 'npm run test:unit'", "test:debug": "node --inspect-brk test-runner.js", "test:parallel": "node test-runner.js --parallel", "test:ci": "node test-runner.js --coverage --exit-on-fail", "test:smoke": "node test-runner.js --grep='smoke|critical'", "test:regression": "node test-runner.js --e2e --performance", "clean": "rm -rf coverage test-reports node_modules/.cache", "docker:test": "docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit", "docker:test:down": "docker-compose -f docker-compose.test.yml down -v"}, "dependencies": {"chai": "^4.3.10", "sinon": "^17.0.1", "supertest": "^6.3.3", "mongodb-memory-server": "^9.1.3", "mongoose": "^8.0.3"}, "devDependencies": {"nyc": "^15.1.0", "nodemon": "^3.0.2", "mocha": "^10.2.0", "c8": "^8.0.1"}, "nyc": {"reporter": ["text", "html", "lcov"], "exclude": ["test/**", "coverage/**", "node_modules/**", "**/*.test.js", "**/*.spec.js"], "check-coverage": true, "statements": 80, "branches": 75, "functions": 80, "lines": 80}, "mocha": {"timeout": 30000, "recursive": true, "exit": true, "spec": ["unit/**/*.js", "integration/**/*.js", "e2e/**/*.js"]}, "engines": {"node": ">=18.0.0"}, "keywords": ["testing", "ddd", "domain-driven-design", "cqrs", "email-sequences", "neurocolony"], "author": "NeuroColony Team", "license": "MIT"}