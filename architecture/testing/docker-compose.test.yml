# Docker Compose for Testing Environment
version: '3.8'

services:
  # Test MongoDB instance
  mongodb-test:
    image: mongo:7
    container_name: neurocolony-test-mongodb
    environment:
      - MONGO_INITDB_DATABASE=neurocolony_test
    ports:
      - "27018:27017"
    volumes:
      - mongodb_test_data:/data/db
    command: mongod --quiet --logpath /dev/null
    networks:
      - test-network

  # Test Redis instance  
  redis-test:
    image: redis:7-alpine
    container_name: neurocolony-test-redis
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass ""
    volumes:
      - redis_test_data:/data
    networks:
      - test-network

  # Test application
  app-test:
    build:
      context: ../../
      dockerfile: Dockerfile.test
    container_name: neurocolony-test-app
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongodb-test:27017/neurocolony_test
      - REDIS_URL=redis://redis-test:6379
      - JWT_SECRET=test-jwt-secret-key-for-testing
      - PORT=3001
      - LOG_LEVEL=error
      # Test-specific environment variables
      - COVERAGE=true
      - PERF_TESTS=false
      - TEST_TIMEOUT=30000
    ports:
      - "3001:3001"
    depends_on:
      - mongodb-test
      - redis-test
    volumes:
      - ../../:/app
      - test_coverage:/app/coverage
      - test_reports:/app/test-reports
    working_dir: /app/architecture/testing
    command: npm run test:ci
    networks:
      - test-network

  # Unit tests runner (standalone)
  unit-tests:
    build:
      context: ../../
      dockerfile: Dockerfile.test
    container_name: neurocolony-unit-tests
    environment:
      - NODE_ENV=test
      - LOG_LEVEL=error
    volumes:
      - ../../:/app
      - test_coverage:/app/coverage
    working_dir: /app/architecture/testing
    command: npm run test:unit
    networks:
      - test-network

  # Integration tests runner
  integration-tests:
    build:
      context: ../../
      dockerfile: Dockerfile.test
    container_name: neurocolony-integration-tests
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongodb-test:27017/neurocolony_test
      - REDIS_URL=redis://redis-test:6379
      - LOG_LEVEL=error
    depends_on:
      - mongodb-test
      - redis-test
    volumes:
      - ../../:/app
      - test_coverage:/app/coverage
    working_dir: /app/architecture/testing
    command: npm run test:integration
    networks:
      - test-network

  # E2E tests runner
  e2e-tests:
    build:
      context: ../../
      dockerfile: Dockerfile.test
    container_name: neurocolony-e2e-tests
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongodb-test:27017/neurocolony_test
      - REDIS_URL=redis://redis-test:6379
      - JWT_SECRET=test-jwt-secret-key-for-testing
      - LOG_LEVEL=error
      - API_BASE_URL=http://app-test:3001
    depends_on:
      - mongodb-test
      - redis-test
      - app-test
    volumes:
      - ../../:/app
      - test_coverage:/app/coverage
      - test_reports:/app/test-reports
    working_dir: /app/architecture/testing
    command: npm run test:e2e
    networks:
      - test-network

  # Performance tests runner
  performance-tests:
    build:
      context: ../../
      dockerfile: Dockerfile.test
    container_name: neurocolony-performance-tests
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongodb-test:27017/neurocolony_test
      - REDIS_URL=redis://redis-test:6379
      - JWT_SECRET=test-jwt-secret-key-for-testing
      - PERF_TESTS=true
      - LOG_LEVEL=error
    depends_on:
      - mongodb-test
      - redis-test
      - app-test
    volumes:
      - ../../:/app
      - test_reports:/app/test-reports
    working_dir: /app/architecture/testing
    command: npm run test:performance
    networks:
      - test-network

  # Test report server (optional)
  test-reports:
    image: nginx:alpine
    container_name: neurocolony-test-reports
    ports:
      - "8080:80"
    volumes:
      - test_reports:/usr/share/nginx/html:ro
      - ./nginx-test.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app-test
    networks:
      - test-network

volumes:
  mongodb_test_data:
    driver: local
  redis_test_data:
    driver: local
  test_coverage:
    driver: local
  test_reports:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16