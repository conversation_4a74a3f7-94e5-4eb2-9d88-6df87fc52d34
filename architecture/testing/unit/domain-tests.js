/**
 * 🧪 Domain Layer Unit Tests
 * Tests for entities, value objects, and domain services
 */

import { expect } from 'chai'
import {
  createUnitTest,
  TestDataFactory
} from '../test-framework.js'

import {
  EmailAddress,
  BusinessInfo,
  SequenceSettings,
  EmailContent,
  Email,
  EmailPerformance,
  EmailSequence
} from '../../ddd/domains/email-sequence/entities.js'

import {
  EmailSequenceGenerationService,
  EmailContentOptimizationService,
  SequencePerformanceAnalysisService,
  SequenceReadyToPublishSpecification,
  HighPerformingSequenceSpecification
} from '../../ddd/domains/email-sequence/services.js'

import {
  DomainValidationError,
  InvariantViolationError,
  IdGenerator
} from '../../ddd/domain-core.js'

// ========================================
// VALUE OBJECT TESTS
// ========================================

describe('🏷️ Value Objects', () => {
  describe('EmailAddress', () => {
    const test = createUnitTest('EmailAddress Value Object')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should create valid email address', () => {
      const email = new EmailAddress('<EMAIL>')
      expect(email.value).to.equal('<EMAIL>')
    })

    it('should normalize email to lowercase', () => {
      const email = new EmailAddress('<EMAIL>')
      expect(email.value).to.equal('<EMAIL>')
    })

    it('should trim whitespace', () => {
      const email = new EmailAddress('  <EMAIL>  ')
      expect(email.value).to.equal('<EMAIL>')
    })

    it('should throw error for invalid email format', () => {
      test.assertDomainError(
        () => new EmailAddress('invalid-email'),
        DomainValidationError,
        'Invalid email address format'
      )
    })

    it('should throw error for empty email', () => {
      test.assertDomainError(
        () => new EmailAddress(''),
        DomainValidationError,
        'Email address is required'
      )
    })

    it('should validate email format correctly', () => {
      expect(EmailAddress.isValid('<EMAIL>')).to.be.true
      expect(EmailAddress.isValid('<EMAIL>')).to.be.true
      expect(EmailAddress.isValid('invalid-email')).to.be.false
      expect(EmailAddress.isValid('@example.com')).to.be.false
      expect(EmailAddress.isValid('test@')).to.be.false
    })

    it('should support equality comparison', () => {
      const email1 = new EmailAddress('<EMAIL>')
      const email2 = new EmailAddress('<EMAIL>')
      const email3 = new EmailAddress('<EMAIL>')

      expect(email1.equals(email2)).to.be.true
      expect(email1.equals(email3)).to.be.false
    })

    it('should support toString method', () => {
      const email = new EmailAddress('<EMAIL>')
      expect(email.toString()).to.equal('<EMAIL>')
    })
  })

  describe('BusinessInfo', () => {
    const test = createUnitTest('BusinessInfo Value Object')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should create valid business info', () => {
      const data = TestDataFactory.validBusinessInfo()
      const businessInfo = new BusinessInfo(data)

      expect(businessInfo.businessName).to.equal(data.businessName)
      expect(businessInfo.industry).to.equal(data.industry)
      expect(businessInfo.targetAudience).to.equal(data.targetAudience)
      expect(businessInfo.goals).to.equal(data.goals)
      expect(businessInfo.tone).to.equal(data.tone)
    })

    it('should require business name', () => {
      const data = { ...TestDataFactory.validBusinessInfo(), businessName: '' }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Business name is required'
      )
    })

    it('should enforce minimum business name length', () => {
      const data = { ...TestDataFactory.validBusinessInfo(), businessName: 'A' }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Business name must be at least 2 characters long'
      )
    })

    it('should enforce maximum business name length', () => {
      const longName = 'A'.repeat(101)
      const data = { ...TestDataFactory.validBusinessInfo(), businessName: longName }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Business name must be no more than 100 characters long'
      )
    })

    it('should require industry', () => {
      const data = { ...TestDataFactory.validBusinessInfo(), industry: '' }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Industry is required'
      )
    })

    it('should require target audience', () => {
      const data = { ...TestDataFactory.validBusinessInfo(), targetAudience: '' }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Target audience is required'
      )
    })

    it('should enforce minimum target audience length', () => {
      const data = { ...TestDataFactory.validBusinessInfo(), targetAudience: 'short' }
      test.assertDomainError(
        () => new BusinessInfo(data),
        DomainValidationError,
        'Target audience must be at least 10 characters long'
      )
    })

    it('should trim all text fields', () => {
      const data = {
        businessName: '  Acme Corp  ',
        industry: '  Technology  ',
        targetAudience: '  Software developers  ',
        goals: '  Increase sales  ',
        tone: 'professional',
        additionalInfo: '  More details  '
      }
      const businessInfo = new BusinessInfo(data)

      expect(businessInfo.businessName).to.equal('Acme Corp')
      expect(businessInfo.industry).to.equal('Technology')
      expect(businessInfo.targetAudience).to.equal('Software developers')
      expect(businessInfo.goals).to.equal('Increase sales')
      expect(businessInfo.additionalInfo).to.equal('More details')
    })

    it('should handle optional fields', () => {
      const data = {
        businessName: 'Acme Corp',
        industry: 'Technology',
        targetAudience: 'Software developers'
      }
      const businessInfo = new BusinessInfo(data)

      expect(businessInfo.goals).to.equal('')
      expect(businessInfo.tone).to.equal('professional')
      expect(businessInfo.additionalInfo).to.equal('')
    })

    it('should identify complete business info', () => {
      const complete = new BusinessInfo(TestDataFactory.validBusinessInfo())
      expect(complete.isComplete()).to.be.true

      const incomplete = new BusinessInfo({
        businessName: 'Acme',
        industry: '',
        targetAudience: 'Developers'
      })
      expect(incomplete.isComplete()).to.be.false
    })

    it('should identify advanced business info', () => {
      const advanced = new BusinessInfo({
        ...TestDataFactory.validBusinessInfo(),
        goals: 'Detailed goals',
        additionalInfo: 'Additional context'
      })
      expect(advanced.hasAdvancedInfo()).to.be.true

      const basic = new BusinessInfo({
        businessName: 'Acme',
        industry: 'Tech',
        targetAudience: 'Developers'
      })
      expect(basic.hasAdvancedInfo()).to.be.false
    })
  })

  describe('SequenceSettings', () => {
    const test = createUnitTest('SequenceSettings Value Object')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should create valid sequence settings', () => {
      const data = TestDataFactory.validSequenceSettings()
      const settings = new SequenceSettings(data)

      expect(settings.sequenceType).to.equal(data.sequenceType)
      expect(settings.emailCount).to.equal(data.emailCount)
      expect(settings.frequency).to.equal(data.frequency)
      expect(settings.personalizeLevel).to.equal(data.personalizeLevel)
      expect(settings.includeCallToAction).to.equal(data.includeCallToAction)
      expect(settings.ctaType).to.equal(data.ctaType)
    })

    it('should use default values', () => {
      const settings = new SequenceSettings({})

      expect(settings.sequenceType).to.equal('nurture')
      expect(settings.emailCount).to.equal(5)
      expect(settings.frequency).to.equal('weekly')
      expect(settings.personalizeLevel).to.equal('medium')
      expect(settings.includeCallToAction).to.be.true
      expect(settings.ctaType).to.equal('soft')
    })

    it('should enforce email count range', () => {
      test.assertDomainError(
        () => new SequenceSettings({ emailCount: 2 }),
        DomainValidationError,
        'Email count must be between 3 and 10'
      )

      test.assertDomainError(
        () => new SequenceSettings({ emailCount: 15 }),
        DomainValidationError,
        'Email count must be between 3 and 10'
      )
    })

    it('should validate sequence type', () => {
      test.assertDomainError(
        () => new SequenceSettings({ sequenceType: 'invalid' }),
        DomainValidationError,
        'Invalid sequence type'
      )

      // Valid types should work
      const validTypes = ['welcome', 'nurture', 'sales', 'onboarding', 'reactivation']
      validTypes.forEach(type => {
        expect(() => new SequenceSettings({ sequenceType: type })).to.not.throw()
      })
    })

    it('should validate frequency', () => {
      test.assertDomainError(
        () => new SequenceSettings({ frequency: 'invalid' }),
        DomainValidationError,
        'Invalid frequency'
      )

      // Valid frequencies should work
      const validFreqs = ['daily', 'every-2-days', 'weekly', 'bi-weekly']
      validFreqs.forEach(freq => {
        expect(() => new SequenceSettings({ frequency: freq })).to.not.throw()
      })
    })

    it('should calculate days between emails correctly', () => {
      const daily = new SequenceSettings({ frequency: 'daily' })
      expect(daily.getDaysBetweenEmails()).to.equal(1)

      const weekly = new SequenceSettings({ frequency: 'weekly' })
      expect(weekly.getDaysBetweenEmails()).to.equal(7)

      const biWeekly = new SequenceSettings({ frequency: 'bi-weekly' })
      expect(biWeekly.getDaysBetweenEmails()).to.equal(14)
    })

    it('should calculate total sequence duration', () => {
      const settings = new SequenceSettings({ 
        emailCount: 5, 
        frequency: 'weekly' 
      })
      expect(settings.getTotalSequenceDuration()).to.equal(35) // 5 emails * 7 days
    })

    it('should identify high-touch sequences', () => {
      const highTouch = new SequenceSettings({
        personalizeLevel: 'high',
        frequency: 'daily'
      })
      expect(highTouch.isHighTouch()).to.be.true

      const normal = new SequenceSettings({
        personalizeLevel: 'medium',
        frequency: 'weekly'
      })
      expect(normal.isHighTouch()).to.be.false
    })
  })

  describe('EmailContent', () => {
    const test = createUnitTest('EmailContent Value Object')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should create valid email content', () => {
      const data = TestDataFactory.validEmailContent()
      const content = new EmailContent(data)

      expect(content.subject).to.equal(data.subject)
      expect(content.body).to.equal(data.body)
      expect(content.metadata).to.include(data.metadata)
    })

    it('should require subject', () => {
      const data = { ...TestDataFactory.validEmailContent(), subject: '' }
      test.assertDomainError(
        () => new EmailContent(data),
        DomainValidationError,
        'Subject is required'
      )
    })

    it('should enforce minimum subject length', () => {
      const data = { ...TestDataFactory.validEmailContent(), subject: 'Hi' }
      test.assertDomainError(
        () => new EmailContent(data),
        DomainValidationError,
        'Subject must be at least 5 characters long'
      )
    })

    it('should enforce maximum subject length', () => {
      const longSubject = 'A'.repeat(201)
      const data = { ...TestDataFactory.validEmailContent(), subject: longSubject }
      test.assertDomainError(
        () => new EmailContent(data),
        DomainValidationError,
        'Subject must be no more than 200 characters long'
      )
    })

    it('should require body', () => {
      const data = { ...TestDataFactory.validEmailContent(), body: '' }
      test.assertDomainError(
        () => new EmailContent(data),
        DomainValidationError,
        'Body is required'
      )
    })

    it('should enforce minimum body length', () => {
      const shortBody = 'Short'
      const data = { ...TestDataFactory.validEmailContent(), body: shortBody }
      test.assertDomainError(
        () => new EmailContent(data),
        DomainValidationError,
        'Body must be at least 50 characters long'
      )
    })

    it('should calculate word count correctly', () => {
      const content = new EmailContent({
        subject: 'Test Subject',
        body: 'This is a test email body with exactly ten words here.'
      })
      expect(content.metadata.wordCount).to.equal(10)
    })

    it('should calculate reading time', () => {
      const longBody = 'word '.repeat(400) // 400 words
      const content = new EmailContent({
        subject: 'Test',
        body: longBody
      })
      expect(content.metadata.estimatedReadTime).to.equal(2) // 400 words / 200 wpm = 2 minutes
    })

    it('should detect personalization tokens', () => {
      const personalizedContent = new EmailContent({
        subject: 'Hello {{firstName}}',
        body: 'Dear {{firstName}}, welcome to {{companyName}}!'
      })
      expect(personalizedContent.metadata.hasPersonalization).to.be.true

      const regularContent = new EmailContent({
        subject: 'Hello there',
        body: 'Welcome to our service!'
      })
      expect(regularContent.metadata.hasPersonalization).to.be.false
    })

    it('should generate HTML from text', () => {
      const content = new EmailContent({
        subject: 'Test',
        body: 'Paragraph 1\n\nParagraph 2\nLine break'
      })
      expect(content.htmlBody).to.include('<p>')
      expect(content.htmlBody).to.include('<br>')
    })

    it('should calculate readability score', () => {
      const easyContent = new EmailContent({
        subject: 'Easy',
        body: 'This is easy. Very simple. Short sentences here.'
      })
      expect(easyContent.getReadabilityScore()).to.equal('easy')

      const difficultContent = new EmailContent({
        subject: 'Complex',
        body: 'This is a very complex sentence with many clauses and subclauses that make it extremely difficult to read and understand for most people.'
      })
      expect(difficultContent.getReadabilityScore()).to.equal('difficult')
    })

    it('should detect call-to-action patterns', () => {
      const withCTA = new EmailContent({
        subject: 'Action Required',
        body: 'Please click here to learn more about our services.'
      })
      expect(withCTA.hasCallToAction()).to.be.true

      const withoutCTA = new EmailContent({
        subject: 'Information',
        body: 'This is just informational content without any actions.'
      })
      expect(withoutCTA.hasCallToAction()).to.be.false
    })
  })
})

// ========================================
// ENTITY TESTS
// ========================================

describe('🏢 Entities', () => {
  describe('Email', () => {
    const test = createUnitTest('Email Entity')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should create valid email entity', () => {
      const content = new EmailContent(TestDataFactory.validEmailContent())
      const email = new Email(IdGenerator.uuid(), {
        sequencePosition: 1,
        content,
        scheduledDays: 0,
        isActive: true
      })

      expect(email.id).to.be.a('string')
      expect(email.sequencePosition).to.equal(1)
      expect(email.content).to.equal(content)
      expect(email.scheduledDays).to.equal(0)
      expect(email.isActive).to.be.true
      expect(email.performance).to.be.instanceOf(EmailPerformance)
    })

    it('should validate sequence position', () => {
      const content = new EmailContent(TestDataFactory.validEmailContent())
      test.assertDomainError(
        () => new Email(IdGenerator.uuid(), {
          sequencePosition: 0,
          content,
          scheduledDays: 0
        }),
        DomainValidationError,
        'Sequence position must be positive'
      )
    })

    it('should validate content instance', () => {
      test.assertDomainError(
        () => new Email(IdGenerator.uuid(), {
          sequencePosition: 1,
          content: { subject: 'test', body: 'test' },
          scheduledDays: 0
        }),
        DomainValidationError,
        'Content must be EmailContent instance'
      )
    })

    it('should update content correctly', () => {
      const content1 = new EmailContent(TestDataFactory.validEmailContent())
      const content2 = new EmailContent({
        ...TestDataFactory.validEmailContent(),
        subject: 'Updated Subject'
      })

      const email = new Email(IdGenerator.uuid(), {
        sequencePosition: 1,
        content: content1,
        scheduledDays: 0
      })

      email.updateContent(content2)
      expect(email.content.subject).to.equal('Updated Subject')
    })

    it('should handle activation and deactivation', () => {
      const content = new EmailContent(TestDataFactory.validEmailContent())
      const email = new Email(IdGenerator.uuid(), {
        sequencePosition: 1,
        content,
        scheduledDays: 0,
        isActive: true
      })

      email.deactivate()
      expect(email.isActive).to.be.false

      email.activate()
      expect(email.isActive).to.be.true
    })

    it('should identify welcome email', () => {
      const content = new EmailContent(TestDataFactory.validEmailContent())
      const welcomeEmail = new Email(IdGenerator.uuid(), {
        sequencePosition: 1,
        content,
        scheduledDays: 0
      })

      const followUpEmail = new Email(IdGenerator.uuid(), {
        sequencePosition: 2,
        content,
        scheduledDays: 7
      })

      expect(welcomeEmail.isWelcomeEmail()).to.be.true
      expect(welcomeEmail.isFollowUpEmail()).to.be.false

      expect(followUpEmail.isWelcomeEmail()).to.be.false
      expect(followUpEmail.isFollowUpEmail()).to.be.true
    })

    it('should update performance metrics', () => {
      const content = new EmailContent(TestDataFactory.validEmailContent())
      const email = new Email(IdGenerator.uuid(), {
        sequencePosition: 1,
        content,
        scheduledDays: 0
      })

      const performanceData = {
        sent: 100,
        delivered: 95,
        opened: 25,
        clicked: 5,
        bounced: 5,
        unsubscribed: 1
      }

      email.updatePerformance(performanceData)
      expect(email.performance.sent).to.equal(100)
      expect(email.performance.getOpenRate()).to.equal(25 / 95 * 100)
      expect(email.performance.getClickRate()).to.equal(5 / 95 * 100)
    })
  })

  describe('EmailPerformance', () => {
    const test = createUnitTest('EmailPerformance Value Object')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    it('should calculate delivery rate correctly', () => {
      const performance = new EmailPerformance({
        sent: 100,
        delivered: 95,
        opened: 25,
        clicked: 5
      })

      expect(performance.getDeliveryRate()).to.equal(95)
    })

    it('should calculate open rate correctly', () => {
      const performance = new EmailPerformance({
        sent: 100,
        delivered: 95,
        opened: 25,
        clicked: 5
      })

      expect(performance.getOpenRate()).to.be.closeTo(26.32, 0.1) // 25/95 * 100
    })

    it('should calculate click rate correctly', () => {
      const performance = new EmailPerformance({
        sent: 100,
        delivered: 95,
        opened: 25,
        clicked: 5
      })

      expect(performance.getClickRate()).to.be.closeTo(5.26, 0.1) // 5/95 * 100
    })

    it('should calculate click-to-open rate correctly', () => {
      const performance = new EmailPerformance({
        sent: 100,
        delivered: 95,
        opened: 25,
        clicked: 5
      })

      expect(performance.getClickToOpenRate()).to.equal(20) // 5/25 * 100
    })

    it('should handle zero division gracefully', () => {
      const performance = new EmailPerformance({
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0
      })

      expect(performance.getDeliveryRate()).to.equal(0)
      expect(performance.getOpenRate()).to.equal(0)
      expect(performance.getClickRate()).to.equal(0)
      expect(performance.getClickToOpenRate()).to.equal(0)
    })

    it('should assess performance correctly', () => {
      const goodPerformance = new EmailPerformance({
        sent: 100,
        delivered: 95,
        opened: 25, // 26.3% open rate
        clicked: 3,  // 3.16% click rate
        bounced: 3   // 3% bounce rate
      })

      expect(goodPerformance.isPerformingWell()).to.be.true
      expect(goodPerformance.needsOptimization()).to.be.false

      const poorPerformance = new EmailPerformance({
        sent: 100,
        delivered: 80,
        opened: 10, // 12.5% open rate
        clicked: 0,  // 0% click rate
        bounced: 15  // 15% bounce rate
      })

      expect(poorPerformance.isPerformingWell()).to.be.false
      expect(poorPerformance.needsOptimization()).to.be.true
    })
  })
})

// ========================================
// AGGREGATE ROOT TESTS
// ========================================

describe('🏛️ Email Sequence Aggregate', () => {
  const test = createUnitTest('EmailSequence Aggregate Root')

  beforeEach(() => test.beforeEach())
  afterEach(() => test.afterEach())

  function createValidSequence() {
    const userEmail = new EmailAddress('<EMAIL>')
    const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
    const settings = new SequenceSettings(TestDataFactory.validSequenceSettings())

    return new EmailSequence(IdGenerator.uuid(), {
      name: 'Test Sequence',
      userEmail,
      businessInfo,
      settings,
      status: 'draft'
    })
  }

  function createValidEmail(position = 1) {
    const content = new EmailContent(TestDataFactory.validEmailContent())
    return new Email(IdGenerator.uuid(), {
      sequencePosition: position,
      content,
      scheduledDays: (position - 1) * 7,
      isActive: true
    })
  }

  it('should create valid email sequence', () => {
    const sequence = createValidSequence()

    expect(sequence.id).to.be.a('string')
    expect(sequence.name).to.equal('Test Sequence')
    expect(sequence.status).to.equal('draft')
    expect(sequence.getEmailCount()).to.equal(0)
  })

  it('should add emails correctly', () => {
    const sequence = createValidSequence()
    const email1 = createValidEmail(1)
    const email2 = createValidEmail(2)

    sequence.addEmail(email1)
    sequence.addEmail(email2)

    expect(sequence.getEmailCount()).to.equal(2)
    expect(sequence.getEmailByPosition(1)).to.equal(email1)
    expect(sequence.getEmailByPosition(2)).to.equal(email2)
  })

  it('should prevent duplicate email positions', () => {
    const sequence = createValidSequence()
    const email1 = createValidEmail(1)
    const email2 = createValidEmail(1) // Same position

    sequence.addEmail(email1)
    
    test.assertDomainError(
      () => sequence.addEmail(email2),
      InvariantViolationError,
      'Email position already exists'
    )
  })

  it('should remove emails correctly', () => {
    const sequence = createValidSequence()
    const email = createValidEmail(1)

    sequence.addEmail(email)
    expect(sequence.getEmailCount()).to.equal(1)

    sequence.removeEmail(1)
    expect(sequence.getEmailCount()).to.equal(0)
    expect(sequence.getEmailByPosition(1)).to.be.undefined
  })

  it('should update email content', () => {
    const sequence = createValidSequence()
    const email = createValidEmail(1)
    sequence.addEmail(email)

    const newContent = new EmailContent({
      subject: 'Updated Subject',
      body: 'Updated body content with more than fifty characters to meet validation requirements.'
    })

    sequence.updateEmail(1, newContent)
    expect(sequence.getEmailByPosition(1).content.subject).to.equal('Updated Subject')
  })

  it('should publish sequence with validation', () => {
    const sequence = createValidSequence()
    
    // Add minimum required emails
    for (let i = 1; i <= 5; i++) {
      sequence.addEmail(createValidEmail(i))
    }

    expect(() => sequence.publish()).to.not.throw()
    expect(sequence.status).to.equal('published')
  })

  it('should prevent publishing incomplete sequence', () => {
    const sequence = createValidSequence()
    // Only add 2 emails (less than minimum of 3)
    sequence.addEmail(createValidEmail(1))
    sequence.addEmail(createValidEmail(2))

    test.assertDomainError(
      () => sequence.publish(),
      InvariantViolationError,
      'Published sequence must have at least 3 emails'
    )
  })

  it('should archive sequence', () => {
    const sequence = createValidSequence()
    sequence.archive()
    expect(sequence.status).to.equal('archived')
  })

  it('should duplicate sequence correctly', () => {
    const sequence = createValidSequence()
    sequence.addEmail(createValidEmail(1))
    sequence.addEmail(createValidEmail(2))

    const duplicate = sequence.duplicate()

    expect(duplicate.id).to.not.equal(sequence.id)
    expect(duplicate.name).to.equal('Test Sequence (Copy)')
    expect(duplicate.status).to.equal('draft')
    expect(duplicate.getEmailCount()).to.equal(2)
    expect(duplicate.metadata.originalSequenceId).to.equal(sequence.id)
  })

  it('should calculate total estimated duration', () => {
    const sequence = createValidSequence()
    sequence.addEmail(createValidEmail(1)) // Day 0
    sequence.addEmail(createValidEmail(2)) // Day 7
    sequence.addEmail(createValidEmail(3)) // Day 14

    expect(sequence.getTotalEstimatedDuration()).to.equal(14)
  })

  it('should get active emails only', () => {
    const sequence = createValidSequence()
    const email1 = createValidEmail(1)
    const email2 = createValidEmail(2)
    const email3 = createValidEmail(3)

    sequence.addEmail(email1)
    sequence.addEmail(email2)
    sequence.addEmail(email3)

    email2.deactivate()

    const activeEmails = sequence.getActiveEmails()
    expect(activeEmails).to.have.length(2)
    expect(activeEmails.find(e => e.id === email2.id)).to.be.undefined
  })

  it('should calculate overall performance', () => {
    const sequence = createValidSequence()
    const email1 = createValidEmail(1)
    const email2 = createValidEmail(2)

    // Add performance data
    email1.updatePerformance({
      sent: 100,
      delivered: 95,
      opened: 25,
      clicked: 5
    })

    email2.updatePerformance({
      sent: 100,
      delivered: 90,
      opened: 20,
      clicked: 3
    })

    sequence.addEmail(email1)
    sequence.addEmail(email2)

    const performance = sequence.getOverallPerformance()
    expect(performance).to.not.be.null
    expect(performance.averageOpenRate).to.be.closeTo(22.5, 0.1) // (25+20)/200 * 100
    expect(performance.averageClickRate).to.be.closeTo(4, 0.1)    // (5+3)/200 * 100
  })

  it('should enforce business invariants', () => {
    const sequence = createValidSequence()

    // Test email position sequence invariant
    const email1 = createValidEmail(1)
    const email3 = createValidEmail(3) // Skip position 2

    sequence.addEmail(email1)
    sequence.addEmail(email3)

    test.assertDomainError(
      () => sequence.enforceInvariants(),
      InvariantViolationError,
      'Email positions must be sequential starting from 1'
    )
  })

  it('should track version for optimistic locking', () => {
    const sequence = createValidSequence()
    const initialVersion = sequence.version

    sequence.addEmail(createValidEmail(1))
    expect(sequence.version).to.equal(initialVersion)

    sequence.incrementVersion()
    expect(sequence.version).to.equal(initialVersion + 1)
  })
})

// ========================================
// SPECIFICATION TESTS
// ========================================

describe('📋 Specifications', () => {
  describe('SequenceReadyToPublishSpecification', () => {
    const test = createUnitTest('SequenceReadyToPublishSpecification')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    function createSequenceForPublishing() {
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 3 })

      const sequence = new EmailSequence(IdGenerator.uuid(), {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      // Add required emails
      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject`,
          body: `This is email ${i} body content with sufficient length to meet requirements.`
        })
        const email = new Email(IdGenerator.uuid(), {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })
        sequence.addEmail(email)
      }

      return sequence
    }

    it('should satisfy specification for ready sequence', () => {
      const spec = new SequenceReadyToPublishSpecification()
      const sequence = createSequenceForPublishing()

      expect(spec.isSatisfiedBy(sequence)).to.be.true
    })

    it('should reject sequence with insufficient emails', () => {
      const spec = new SequenceReadyToPublishSpecification()
      const sequence = createSequenceForPublishing()

      // Remove one email to have only 2
      sequence.removeEmail(3)

      expect(spec.isSatisfiedBy(sequence)).to.be.false
    })

    it('should reject sequence with inactive emails', () => {
      const spec = new SequenceReadyToPublishSpecification()
      const sequence = createSequenceForPublishing()

      // Deactivate one email
      sequence.getEmailByPosition(2).deactivate()

      expect(spec.isSatisfiedBy(sequence)).to.be.false
    })

    it('should reject sequence with insufficient content', () => {
      const spec = new SequenceReadyToPublishSpecification()
      const sequence = createSequenceForPublishing()

      // Update email with insufficient content
      const shortContent = new EmailContent({
        subject: 'Hi',
        body: 'Short'
      })

      test.assertDomainError(
        () => sequence.updateEmail(1, shortContent),
        DomainValidationError
      )
    })
  })

  describe('HighPerformingSequenceSpecification', () => {
    const test = createUnitTest('HighPerformingSequenceSpecification')

    beforeEach(() => test.beforeEach())
    afterEach(() => test.afterEach())

    function createPerformingSequence(openRate, clickRate) {
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 3 })

      const sequence = new EmailSequence(IdGenerator.uuid(), {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'published'
      })

      // Add emails with performance data
      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject`,
          body: `This is email ${i} body content with sufficient length.`
        })
        const email = new Email(IdGenerator.uuid(), {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })

        // Add performance data to achieve desired rates
        email.updatePerformance({
          sent: 100,
          delivered: 95,
          opened: Math.round(95 * openRate / 100),
          clicked: Math.round(95 * clickRate / 100)
        })

        sequence.addEmail(email)
      }

      return sequence
    }

    it('should identify high performing sequence', () => {
      const spec = new HighPerformingSequenceSpecification(25, 3)
      const sequence = createPerformingSequence(30, 5) // Above thresholds

      expect(spec.isSatisfiedBy(sequence)).to.be.true
    })

    it('should reject low performing sequence', () => {
      const spec = new HighPerformingSequenceSpecification(25, 3)
      const sequence = createPerformingSequence(15, 1) // Below thresholds

      expect(spec.isSatisfiedBy(sequence)).to.be.false
    })

    it('should use custom thresholds', () => {
      const spec = new HighPerformingSequenceSpecification(35, 5)
      const sequence = createPerformingSequence(30, 4) // Below custom thresholds

      expect(spec.isSatisfiedBy(sequence)).to.be.false
    })

    it('should reject sequence without performance data', () => {
      const spec = new HighPerformingSequenceSpecification()
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings()

      const sequence = new EmailSequence(IdGenerator.uuid(), {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      expect(spec.isSatisfiedBy(sequence)).to.be.false
    })
  })
})