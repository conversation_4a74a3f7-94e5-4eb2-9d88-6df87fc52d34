/**
 * 🧪 Comprehensive Testing Framework
 * Unit, Integration, and E2E testing infrastructure
 */

import { expect } from 'chai'
import sinon from 'sinon'
import supertest from 'supertest'
import { MongoMemoryServer } from 'mongodb-memory-server'
import mongoose from 'mongoose'
import { createApp } from '../../backend/app.js'
import { logger } from '../../backend/utils/logger.js'

// ========================================
// TEST FRAMEWORK CORE
// ========================================

/**
 * Base Test Framework Class
 */
export class TestFramework {
  constructor() {
    this.mongoServer = null
    this.app = null
    this.agent = null
    this.cleanupTasks = []
  }

  // Global setup for test suite
  async globalSetup() {
    try {
      logger.info('🧪 Setting up test framework...')

      // Start in-memory MongoDB
      this.mongoServer = await MongoMemoryServer.create({
        instance: {
          port: 27018, // Different port from dev
          dbName: 'sequenceai_test'
        }
      })

      const mongoUri = this.mongoServer.getUri()
      process.env.MONGODB_URI = mongoUri
      process.env.NODE_ENV = 'test'
      process.env.JWT_SECRET = 'test-jwt-secret'
      process.env.REDIS_URL = 'redis://localhost:6380' // Test Redis instance

      // Connect to test database
      await mongoose.connect(mongoUri)

      // Create test app
      this.app = createApp()
      this.agent = supertest(this.app)

      logger.info('✅ Test framework setup complete')

    } catch (error) {
      logger.error('❌ Test framework setup failed:', error)
      throw error
    }
  }

  // Global teardown for test suite
  async globalTeardown() {
    try {
      logger.info('🧹 Tearing down test framework...')

      // Run cleanup tasks
      for (const task of this.cleanupTasks) {
        await task()
      }

      // Close database connections
      await mongoose.disconnect()

      // Stop MongoDB server
      if (this.mongoServer) {
        await this.mongoServer.stop()
      }

      logger.info('✅ Test framework teardown complete')

    } catch (error) {
      logger.error('❌ Test framework teardown failed:', error)
      throw error
    }
  }

  // Add cleanup task
  addCleanupTask(task) {
    this.cleanupTasks.push(task)
  }
}

/**
 * Unit Test Base Class
 */
export class UnitTest {
  constructor(description) {
    this.description = description
    this.sandbox = null
    this.mocks = new Map()
    this.stubs = new Map()
  }

  beforeEach() {
    this.sandbox = sinon.createSandbox()
  }

  afterEach() {
    if (this.sandbox) {
      this.sandbox.restore()
    }
    this.mocks.clear()
    this.stubs.clear()
  }

  // Create mock object
  createMock(name, methods = {}) {
    const mock = this.sandbox.mock()
    Object.keys(methods).forEach(method => {
      mock[method] = this.sandbox.stub().returns(methods[method])
    })
    this.mocks.set(name, mock)
    return mock
  }

  // Create stub for method
  createStub(obj, method, returnValue) {
    const stub = this.sandbox.stub(obj, method)
    if (returnValue !== undefined) {
      stub.returns(returnValue)
    }
    this.stubs.set(`${obj.constructor.name}.${method}`, stub)
    return stub
  }

  // Get mock by name
  getMock(name) {
    return this.mocks.get(name)
  }

  // Get stub by name
  getStub(name) {
    return this.stubs.get(name)
  }

  // Assert method was called
  assertCalled(stub, times = 1) {
    expect(stub.callCount).to.equal(times)
  }

  // Assert method was called with specific arguments
  assertCalledWith(stub, ...args) {
    expect(stub.calledWith(...args)).to.be.true
  }

  // Custom domain assertions
  assertDomainError(fn, errorType, message = null) {
    try {
      fn()
      expect.fail('Expected domain error to be thrown')
    } catch (error) {
      expect(error).to.be.instanceOf(errorType)
      if (message) {
        expect(error.message).to.include(message)
      }
    }
  }

  async assertAsyncDomainError(fn, errorType, message = null) {
    try {
      await fn()
      expect.fail('Expected domain error to be thrown')
    } catch (error) {
      expect(error).to.be.instanceOf(errorType)
      if (message) {
        expect(error.message).to.include(message)
      }
    }
  }
}

/**
 * Integration Test Base Class
 */
export class IntegrationTest {
  constructor(description, framework) {
    this.description = description
    this.framework = framework
    this.testData = new Map()
    this.createdEntities = []
  }

  async beforeEach() {
    // Clear collections
    const collections = await mongoose.connection.db.collections()
    for (const collection of collections) {
      await collection.deleteMany({})
    }
    
    this.testData.clear()
    this.createdEntities = []
  }

  async afterEach() {
    // Clean up created entities
    for (const entity of this.createdEntities) {
      try {
        await entity.deleteOne()
      } catch (error) {
        // Entity might already be deleted
      }
    }
  }

  // Create test user
  async createTestUser(userData = {}) {
    const User = (await import('../../backend/models/User.js')).default
    const user = new User({
      name: 'Test User',
      email: '<EMAIL>',
      plan: 'free',
      stripeCustomerId: 'cus_test',
      ...userData
    })
    await user.save()
    this.createdEntities.push(user)
    this.testData.set('user', user)
    return user
  }

  // Create test email sequence
  async createTestSequence(user, sequenceData = {}) {
    const EmailSequence = (await import('../../backend/models/EmailSequence.js')).default
    const sequence = new EmailSequence({
      name: 'Test Sequence',
      userEmail: user.email,
      businessInfo: {
        businessName: 'Test Business',
        industry: 'Technology',
        targetAudience: 'Developers',
        goals: 'Testing',
        tone: 'professional'
      },
      settings: {
        sequenceType: 'nurture',
        emailCount: 5,
        frequency: 'weekly'
      },
      emails: [],
      status: 'draft',
      ...sequenceData
    })
    await sequence.save()
    this.createdEntities.push(sequence)
    this.testData.set('sequence', sequence)
    return sequence
  }

  // Get test data
  getTestData(key) {
    return this.testData.get(key)
  }

  // Database assertions
  async assertEntityExists(Model, query) {
    const entity = await Model.findOne(query)
    expect(entity).to.not.be.null
    return entity
  }

  async assertEntityCount(Model, query, expectedCount) {
    const count = await Model.countDocuments(query)
    expect(count).to.equal(expectedCount)
  }

  async assertEntityNotExists(Model, query) {
    const entity = await Model.findOne(query)
    expect(entity).to.be.null
  }
}

/**
 * E2E Test Base Class
 */
export class E2ETest {
  constructor(description, framework) {
    this.description = description
    this.framework = framework
    this.authToken = null
    this.testUser = null
    this.requestCount = 0
  }

  async beforeEach() {
    // Clear collections
    const collections = await mongoose.connection.db.collections()
    for (const collection of collections) {
      await collection.deleteMany({})
    }

    this.requestCount = 0
  }

  async afterEach() {
    this.authToken = null
    this.testUser = null
  }

  // Authentication helpers
  async registerUser(userData = {}) {
    const defaultUser = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      plan: 'free'
    }

    const response = await this.framework.agent
      .post('/api/auth/register')
      .send({ ...defaultUser, ...userData })
      .expect(201)

    this.testUser = response.body.user
    this.authToken = response.body.token
    return response.body
  }

  async loginUser(email = '<EMAIL>', password = 'TestPassword123!') {
    const response = await this.framework.agent
      .post('/api/auth/login')
      .send({ email, password })
      .expect(200)

    this.testUser = response.body.user
    this.authToken = response.body.token
    return response.body
  }

  // Request helpers with authentication
  get(url) {
    this.requestCount++
    return this.framework.agent
      .get(url)
      .set('Authorization', `Bearer ${this.authToken}`)
  }

  post(url) {
    this.requestCount++
    return this.framework.agent
      .post(url)
      .set('Authorization', `Bearer ${this.authToken}`)
  }

  put(url) {
    this.requestCount++
    return this.framework.agent
      .put(url)
      .set('Authorization', `Bearer ${this.authToken}`)
  }

  delete(url) {
    this.requestCount++
    return this.framework.agent
      .delete(url)
      .set('Authorization', `Bearer ${this.authToken}`)
  }

  // Performance assertions
  async assertResponseTime(requestFn, maxTime = 1000) {
    const start = Date.now()
    await requestFn()
    const duration = Date.now() - start
    expect(duration).to.be.below(maxTime)
    return duration
  }

  // Business logic assertions
  assertValidSequence(sequence) {
    expect(sequence).to.have.property('id')
    expect(sequence).to.have.property('name')
    expect(sequence).to.have.property('userEmail')
    expect(sequence).to.have.property('businessInfo')
    expect(sequence).to.have.property('settings')
    expect(sequence).to.have.property('emails')
    expect(sequence).to.have.property('status')
    expect(sequence.emails).to.be.an('array')
  }

  assertValidEmail(email) {
    expect(email).to.have.property('subject')
    expect(email).to.have.property('body')
    expect(email.subject).to.be.a('string').that.is.not.empty
    expect(email.body).to.be.a('string').that.is.not.empty
  }

  assertUsageTracking(user, expectedCount) {
    expect(user.usage.currentPeriod.sequencesGenerated).to.equal(expectedCount)
  }
}

// ========================================
// TEST UTILITIES
// ========================================

/**
 * Test Data Factory
 */
export class TestDataFactory {
  static validBusinessInfo() {
    return {
      businessName: 'Acme Corp',
      industry: 'Technology',
      targetAudience: 'Software developers and tech enthusiasts',
      goals: 'Increase product adoption and user engagement',
      tone: 'professional',
      additionalInfo: 'Focus on technical benefits and ROI'
    }
  }

  static validSequenceSettings() {
    return {
      sequenceType: 'nurture',
      emailCount: 5,
      frequency: 'weekly',
      personalizeLevel: 'medium',
      includeCallToAction: true,
      ctaType: 'soft'
    }
  }

  static validEmailContent() {
    return {
      subject: 'Welcome to our service!',
      body: 'Thank you for joining us. We are excited to help you achieve your goals and look forward to supporting your journey with our comprehensive solutions.',
      metadata: {
        generatedBy: 'test',
        position: 1
      }
    }
  }

  static validUser(overrides = {}) {
    return {
      name: 'John Doe',
      email: '<EMAIL>',
      plan: 'free',
      stripeCustomerId: 'cus_test123',
      usage: {
        currentPeriod: {
          sequencesGenerated: 0,
          overageSequences: 0,
          overageCharges: 0,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        },
        notifications: {
          usage80Sent: false,
          usage95Sent: false,
          overageConsentGiven: false
        },
        history: []
      },
      ...overrides
    }
  }

  static generateEmailSequences(count = 5) {
    return Array.from({ length: count }, (_, index) => ({
      id: `email_${index + 1}`,
      sequencePosition: index + 1,
      content: {
        subject: `Email ${index + 1} Subject`,
        body: `This is the body content for email ${index + 1} in the sequence. It provides valuable information to the recipient.`,
        metadata: {
          wordCount: 50 + index * 10,
          estimatedReadTime: 1,
          hasPersonalization: false
        }
      },
      scheduledDays: index * 7, // Weekly schedule
      isActive: true,
      performance: {
        sent: 100,
        delivered: 95,
        opened: 20 + index * 2,
        clicked: 2 + index,
        bounced: 5,
        unsubscribed: 1
      }
    }))
  }
}

/**
 * Performance Testing Utilities
 */
export class PerformanceTestUtils {
  static async measureExecutionTime(fn) {
    const start = process.hrtime.bigint()
    const result = await fn()
    const end = process.hrtime.bigint()
    const duration = Number(end - start) / 1000000 // Convert to milliseconds
    
    return {
      result,
      duration,
      formatted: `${duration.toFixed(2)}ms`
    }
  }

  static async loadTest(fn, iterations = 100, concurrency = 10) {
    const results = []
    const batches = Math.ceil(iterations / concurrency)
    
    for (let batch = 0; batch < batches; batch++) {
      const batchPromises = []
      const batchSize = Math.min(concurrency, iterations - batch * concurrency)
      
      for (let i = 0; i < batchSize; i++) {
        batchPromises.push(this.measureExecutionTime(fn))
      }
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }
    
    return this.analyzeResults(results)
  }

  static analyzeResults(results) {
    const durations = results.map(r => r.duration)
    durations.sort((a, b) => a - b)
    
    return {
      totalRequests: results.length,
      averageTime: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minTime: durations[0],
      maxTime: durations[durations.length - 1],
      p50: durations[Math.floor(durations.length * 0.5)],
      p95: durations[Math.floor(durations.length * 0.95)],
      p99: durations[Math.floor(durations.length * 0.99)],
      errors: results.filter(r => r.error).length,
      successRate: (results.filter(r => !r.error).length / results.length) * 100
    }
  }
}

/**
 * Test Reporter
 */
export class TestReporter {
  constructor() {
    this.testResults = []
    this.startTime = null
    this.endTime = null
  }

  start() {
    this.startTime = Date.now()
    logger.info('🧪 Test suite started')
  }

  end() {
    this.endTime = Date.now()
    const duration = this.endTime - this.startTime
    logger.info(`✅ Test suite completed in ${duration}ms`)
    this.generateReport()
  }

  addResult(test, status, error = null, duration = 0) {
    this.testResults.push({
      test: test.description || test.name,
      status,
      error: error?.message,
      duration
    })
  }

  generateReport() {
    const passed = this.testResults.filter(r => r.status === 'passed').length
    const failed = this.testResults.filter(r => r.status === 'failed').length
    const total = this.testResults.length
    
    logger.info('📊 Test Results Summary:')
    logger.info(`   Total: ${total}`)
    logger.info(`   Passed: ${passed}`)
    logger.info(`   Failed: ${failed}`)
    logger.info(`   Success Rate: ${((passed / total) * 100).toFixed(2)}%`)
    
    if (failed > 0) {
      logger.info('❌ Failed Tests:')
      this.testResults
        .filter(r => r.status === 'failed')
        .forEach(test => {
          logger.info(`   - ${test.test}: ${test.error}`)
        })
    }
  }

  exportReport(format = 'json') {
    const report = {
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.status === 'passed').length,
        failed: this.testResults.filter(r => r.status === 'failed').length,
        duration: this.endTime - this.startTime
      },
      tests: this.testResults
    }
    
    if (format === 'json') {
      return JSON.stringify(report, null, 2)
    }
    
    // Could add other formats (XML, HTML, etc.)
    return report
  }
}

// ========================================
// EXPORTS
// ========================================

export const testFramework = new TestFramework()
export const testReporter = new TestReporter()

// Helper functions
export const createUnitTest = (description) => new UnitTest(description)
export const createIntegrationTest = (description) => new IntegrationTest(description, testFramework)
export const createE2ETest = (description) => new E2ETest(description, testFramework)

logger.info('🧪 Test framework initialized')