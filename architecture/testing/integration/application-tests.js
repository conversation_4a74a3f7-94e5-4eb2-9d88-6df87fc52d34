/**
 * 🧪 Application Layer Integration Tests
 * Tests for commands, queries, and handlers with real repositories
 */

import { expect } from 'chai'
import {
  createIntegrationTest,
  TestDataFactory,
  testFramework
} from '../test-framework.js'

import {
  CreateEmailSequenceCommand,
  UpdateEmailContentCommand,
  PublishEmailSequenceCommand,
  CreateEmailSequenceCommandHandler,
  UpdateEmailContentCommandHandler,
  PublishEmailSequenceCommandHandler
} from '../../ddd/application/commands.js'

import {
  GetEmailSequenceByIdQuery,
  GetUserSequencesQuery,
  SearchSequencesQuery,
  GetEmailSequenceByIdQueryHandler,
  GetUserSequencesQueryHandler,
  SearchSequencesQueryHandler
} from '../../ddd/application/queries.js'

import {
  EmailSequence,
  EmailAddress,
  BusinessInfo,
  SequenceSettings,
  EmailContent
} from '../../ddd/domains/email-sequence/entities.js'

import {
  InMemoryEmailSequenceRepository
} from '../../ddd/domains/email-sequence/repositories.js'

import {
  EmailSequenceGenerationService,
  EmailContentOptimizationService
} from '../../ddd/domains/email-sequence/services.js'

import {
  DomainValidationError,
  InvariantViolationError,
  EntityNotFoundError,
  domainEventPublisher
} from '../../ddd/domain-core.js'

// ========================================
// COMMAND HANDLER INTEGRATION TESTS
// ========================================

describe('⚡ Command Handlers Integration', () => {
  describe('CreateEmailSequenceCommandHandler', () => {
    const test = createIntegrationTest('CreateEmailSequenceCommandHandler', testFramework)

    let repository
    let generationService
    let handler

    beforeEach(async () => {
      await test.beforeEach()
      
      // Setup dependencies
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      
      // Mock AI service and content optimizer
      const mockAIService = {
        generateContent: async (prompt) => {
          return `Subject: Welcome to ${prompt.businessName}\n\nBody: Thank you for joining ${prompt.businessName}. We specialize in ${prompt.industry} and help ${prompt.targetAudience} achieve their goals. This is the first email in your ${prompt.sequenceType} sequence.`
        }
      }

      const mockContentOptimizer = {
        optimize: async (content, context) => {
          return content // Return content as-is for testing
        }
      }

      generationService = new EmailSequenceGenerationService(mockAIService, mockContentOptimizer)
      handler = new CreateEmailSequenceCommandHandler(
        repository,
        generationService,
        domainEventPublisher
      )
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should create email sequence successfully', async () => {
      const command = new CreateEmailSequenceCommand({
        userEmail: '<EMAIL>',
        businessName: 'Acme Corp',
        industry: 'Technology',
        targetAudience: 'Software developers and tech enthusiasts',
        goals: 'Increase product adoption',
        tone: 'professional',
        sequenceType: 'nurture',
        emailCount: 5,
        frequency: 'weekly'
      })

      const result = await handler.handle(command)

      expect(result).to.have.property('sequenceId')
      expect(result.name).to.include('Acme Corp')
      expect(result.emailCount).to.equal(5)
      expect(result.status).to.equal('draft')

      // Verify sequence was saved
      const savedSequence = await repository.findById(result.sequenceId)
      expect(savedSequence).to.not.be.null
      expect(savedSequence.getEmailCount()).to.equal(5)
    })

    it('should validate command input', async () => {
      const invalidCommand = new CreateEmailSequenceCommand({
        userEmail: 'invalid-email',
        businessName: 'A', // Too short
        industry: '',
        targetAudience: 'Short', // Too short
        emailCount: 15 // Too many
      })

      try {
        await handler.handle(invalidCommand)
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
      }
    })

    it('should respect usage limits when service provided', async () => {
      const mockUsageService = {
        canUserGenerate: async (email) => ({
          allowed: false,
          reason: 'Monthly limit exceeded'
        })
      }

      const handlerWithUsage = new CreateEmailSequenceCommandHandler(
        repository,
        generationService,
        domainEventPublisher,
        mockUsageService
      )

      const command = new CreateEmailSequenceCommand({
        userEmail: '<EMAIL>',
        businessName: 'Acme Corp',
        industry: 'Technology',
        targetAudience: 'Software developers and tech enthusiasts'
      })

      try {
        await handlerWithUsage.handle(command)
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
        expect(error.message).to.include('Generation not allowed')
      }
    })

    it('should track usage when service provided', async () => {
      const usageTracking = []
      const mockUsageService = {
        canUserGenerate: async (email) => ({ allowed: true }),
        recordGeneration: async (email, data) => {
          usageTracking.push({ email, data })
        }
      }

      const handlerWithUsage = new CreateEmailSequenceCommandHandler(
        repository,
        generationService,
        domainEventPublisher,
        mockUsageService
      )

      const command = new CreateEmailSequenceCommand({
        userEmail: '<EMAIL>',
        businessName: 'Acme Corp',
        industry: 'Technology',
        targetAudience: 'Software developers and tech enthusiasts',
        emailCount: 3
      })

      await handlerWithUsage.handle(command)

      expect(usageTracking).to.have.length(1)
      expect(usageTracking[0].email).to.equal('<EMAIL>')
      expect(usageTracking[0].data.type).to.equal('email_sequence')
      expect(usageTracking[0].data.emailCount).to.equal(3)
    })
  })

  describe('UpdateEmailContentCommandHandler', () => {
    const test = createIntegrationTest('UpdateEmailContentCommandHandler', testFramework)

    let repository
    let contentOptimizer
    let handler
    let sequence

    beforeEach(async () => {
      await test.beforeEach()
      
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      
      contentOptimizer = {
        optimize: async (content, context) => {
          // Add some optimization markers
          return new EmailContent({
            subject: `[OPTIMIZED] ${content.subject}`,
            body: `${content.body}\n\n[This content has been optimized for ${context.businessInfo.industry}]`,
            metadata: { ...content.metadata, optimized: true }
          })
        }
      }

      handler = new UpdateEmailContentCommandHandler(
        repository,
        contentOptimizer,
        domainEventPublisher
      )

      // Create a test sequence
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 3 })

      sequence = new EmailSequence('test-seq-1', {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      // Add emails
      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject`,
          body: `This is email ${i} body content with sufficient length to meet validation requirements.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })
        sequence.addEmail(email)
      }

      await repository.save(sequence)
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should update email content successfully', async () => {
      const command = new UpdateEmailContentCommand({
        sequenceId: 'test-seq-1',
        emailPosition: 1,
        subject: 'Updated Subject',
        body: 'This is the updated body content with sufficient length for validation requirements.'
      })

      const result = await handler.handle(command)

      expect(result.sequenceId).to.equal('test-seq-1')
      expect(result.emailPosition).to.equal(1)
      expect(result.updatedContent.subject).to.include('[OPTIMIZED]')

      // Verify changes were saved
      const updatedSequence = await repository.findById('test-seq-1')
      const updatedEmail = updatedSequence.getEmailByPosition(1)
      expect(updatedEmail.content.subject).to.include('[OPTIMIZED] Updated Subject')
      expect(updatedEmail.content.body).to.include('updated body content')
    })

    it('should prevent updating published sequence', async () => {
      // Publish the sequence first
      sequence.publish()
      await repository.save(sequence)

      const command = new UpdateEmailContentCommand({
        sequenceId: 'test-seq-1',
        emailPosition: 1,
        subject: 'Updated Subject',
        body: 'This is the updated body content with sufficient length.'
      })

      try {
        await handler.handle(command)
        expect.fail('Should have thrown invariant violation error')
      } catch (error) {
        expect(error).to.be.instanceOf(InvariantViolationError)
        expect(error.message).to.include('Cannot modify published sequence')
      }
    })

    it('should validate email content requirements', async () => {
      const command = new UpdateEmailContentCommand({
        sequenceId: 'test-seq-1',
        emailPosition: 1,
        subject: 'OK', // Too short
        body: 'Short' // Too short
      })

      try {
        await handler.handle(command)
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
      }
    })

    it('should handle non-existent sequence', async () => {
      const command = new UpdateEmailContentCommand({
        sequenceId: 'non-existent',
        emailPosition: 1,
        subject: 'Valid Subject',
        body: 'This is valid body content with sufficient length for validation requirements.'
      })

      try {
        await handler.handle(command)
        expect.fail('Should have thrown entity not found error')
      } catch (error) {
        expect(error).to.be.instanceOf(EntityNotFoundError)
      }
    })
  })

  describe('PublishEmailSequenceCommandHandler', () => {
    const test = createIntegrationTest('PublishEmailSequenceCommandHandler', testFramework)

    let repository
    let handler
    let sequence

    beforeEach(async () => {
      await test.beforeEach()
      
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      handler = new PublishEmailSequenceCommandHandler(repository, domainEventPublisher)

      // Create a valid sequence ready for publishing
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 5 })

      sequence = new EmailSequence('test-seq-1', {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      // Add required emails
      for (let i = 1; i <= 5; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject Line`,
          body: `This is email ${i} body content with sufficient length to meet all validation requirements and provide value.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })
        sequence.addEmail(email)
      }

      await repository.save(sequence)
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should publish sequence successfully', async () => {
      const command = new PublishEmailSequenceCommand({
        sequenceId: 'test-seq-1'
      })

      const result = await handler.handle(command)

      expect(result.sequenceId).to.equal('test-seq-1')
      expect(result.status).to.equal('published')
      expect(result.emailCount).to.equal(5)

      // Verify sequence was updated
      const publishedSequence = await repository.findById('test-seq-1')
      expect(publishedSequence.status).to.equal('published')
    })

    it('should validate sequence is ready to publish', async () => {
      // Create sequence with insufficient emails
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 2 })

      const incompleteSequence = new EmailSequence('incomplete-seq', {
        name: 'Incomplete Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      // Add only 2 emails (less than minimum)
      for (let i = 1; i <= 2; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject`,
          body: `This is email ${i} body content with sufficient length.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })
        incompleteSequence.addEmail(email)
      }

      await repository.save(incompleteSequence)

      const command = new PublishEmailSequenceCommand({
        sequenceId: 'incomplete-seq'
      })

      try {
        await handler.handle(command)
        expect.fail('Should have thrown invariant violation error')
      } catch (error) {
        expect(error).to.be.instanceOf(InvariantViolationError)
        expect(error.message).to.include('not ready to publish')
      }
    })

    it('should handle non-existent sequence', async () => {
      const command = new PublishEmailSequenceCommand({
        sequenceId: 'non-existent'
      })

      try {
        await handler.handle(command)
        expect.fail('Should have thrown entity not found error')
      } catch (error) {
        expect(error).to.be.instanceOf(EntityNotFoundError)
      }
    })

    it('should prevent republishing already published sequence', async () => {
      // Publish sequence first
      const command = new PublishEmailSequenceCommand({
        sequenceId: 'test-seq-1'
      })
      await handler.handle(command)

      // Try to publish again
      try {
        await handler.handle(command)
        expect.fail('Should have thrown invariant violation error')
      } catch (error) {
        expect(error).to.be.instanceOf(InvariantViolationError)
        expect(error.message).to.include('already published')
      }
    })
  })
})

// ========================================
// QUERY HANDLER INTEGRATION TESTS
// ========================================

describe('🔍 Query Handlers Integration', () => {
  describe('GetEmailSequenceByIdQueryHandler', () => {
    const test = createIntegrationTest('GetEmailSequenceByIdQueryHandler', testFramework)

    let repository
    let handler
    let sequence

    beforeEach(async () => {
      await test.beforeEach()
      
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      handler = new GetEmailSequenceByIdQueryHandler(repository)

      // Create test sequence
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings({ emailCount: 3 })

      sequence = new EmailSequence('test-seq-1', {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'published'
      })

      // Add emails with performance data
      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i} Subject`,
          body: `This is email ${i} body content with sufficient length for validation.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })

        // Add performance data
        email.updatePerformance({
          sent: 100,
          delivered: 95,
          opened: 25 + i,
          clicked: 3 + i,
          bounced: 5,
          unsubscribed: 1
        })

        sequence.addEmail(email)
      }

      await repository.save(sequence)
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should return complete sequence read model', async () => {
      const query = new GetEmailSequenceByIdQuery('test-seq-1')
      const result = await handler.handle(query)

      expect(result.id).to.equal('test-seq-1')
      expect(result.name).to.equal('Test Sequence')
      expect(result.userEmail).to.equal('<EMAIL>')
      expect(result.status).to.equal('published')

      expect(result.businessInfo).to.have.property('businessName')
      expect(result.businessInfo).to.have.property('industry')
      expect(result.businessInfo).to.have.property('targetAudience')

      expect(result.settings).to.have.property('sequenceType')
      expect(result.settings).to.have.property('emailCount')
      expect(result.settings).to.have.property('frequency')

      expect(result.emails).to.be.an('array').with.length(3)
      expect(result.performance).to.not.be.null
      expect(result.performance.averageOpenRate).to.be.above(0)
    })

    it('should include email performance data', async () => {
      const query = new GetEmailSequenceByIdQuery('test-seq-1')
      const result = await handler.handle(query)

      const firstEmail = result.emails[0]
      expect(firstEmail.performance).to.not.be.null
      expect(firstEmail.performance.sent).to.equal(100)
      expect(firstEmail.performance.openRate).to.be.above(0)
      expect(firstEmail.performance.clickRate).to.be.above(0)
      expect(firstEmail.performance.isPerformingWell).to.be.a('boolean')
    })

    it('should include email content preview', async () => {
      const query = new GetEmailSequenceByIdQuery('test-seq-1')
      const result = await handler.handle(query)

      const firstEmail = result.emails[0]
      expect(firstEmail.subject).to.equal('Email 1 Subject')
      expect(firstEmail.bodyPreview).to.include('This is email 1')
      expect(firstEmail.bodyPreview).to.include('...')
      expect(firstEmail.wordCount).to.be.above(0)
      expect(firstEmail.readabilityScore).to.be.oneOf(['easy', 'moderate', 'difficult'])
    })

    it('should throw error for non-existent sequence', async () => {
      const query = new GetEmailSequenceByIdQuery('non-existent')

      try {
        await handler.handle(query)
        expect.fail('Should have thrown entity not found error')
      } catch (error) {
        expect(error).to.be.instanceOf(EntityNotFoundError)
      }
    })

    it('should validate query input', async () => {
      try {
        const query = new GetEmailSequenceByIdQuery('')
        query.validate()
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
        expect(error.message).to.include('Sequence ID is required')
      }
    })
  })

  describe('GetUserSequencesQueryHandler', () => {
    const test = createIntegrationTest('GetUserSequencesQueryHandler', testFramework)

    let repository
    let handler

    beforeEach(async () => {
      await test.beforeEach()
      
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      handler = new GetUserSequencesQueryHandler(repository)

      // Create multiple sequences for the same user
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())

      for (let i = 1; i <= 5; i++) {
        const settings = new SequenceSettings({ 
          emailCount: 3,
          sequenceType: i % 2 === 0 ? 'sales' : 'nurture'
        })

        const sequence = new EmailSequence(`seq-${i}`, {
          name: `Sequence ${i}`,
          userEmail,
          businessInfo,
          settings,
          status: i <= 2 ? 'published' : 'draft'
        })

        // Add basic emails
        for (let j = 1; j <= 3; j++) {
          const content = new EmailContent({
            subject: `Seq ${i} Email ${j}`,
            body: `Body content for sequence ${i} email ${j} with sufficient length.`
          })
          const email = new Email(`seq-${i}-email-${j}`, {
            sequencePosition: j,
            content,
            scheduledDays: (j - 1) * 7,
            isActive: true
          })
          sequence.addEmail(email)
        }

        await repository.save(sequence)
      }

      // Create sequences for different user
      const otherUserEmail = new EmailAddress('<EMAIL>')
      const otherSequence = new EmailSequence('other-seq', {
        name: 'Other User Sequence',
        userEmail: otherUserEmail,
        businessInfo,
        settings: new SequenceSettings({ emailCount: 3 }),
        status: 'draft'
      })
      await repository.save(otherSequence)
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should return user sequences with pagination', async () => {
      const query = new GetUserSequencesQuery({
        userEmail: '<EMAIL>',
        limit: 3,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(3)
      expect(result.pagination.total).to.equal(5)
      expect(result.pagination.hasMore).to.be.true
      
      // Should not include other user's sequences
      result.sequences.forEach(seq => {
        expect(seq.name).to.not.include('Other User')
      })
    })

    it('should filter by status', async () => {
      const query = new GetUserSequencesQuery({
        userEmail: '<EMAIL>',
        status: 'published',
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(2)
      result.sequences.forEach(seq => {
        expect(seq.status).to.equal('published')
      })
    })

    it('should sort sequences', async () => {
      const query = new GetUserSequencesQuery({
        userEmail: '<EMAIL>',
        sortBy: 'name',
        sortOrder: 'asc',
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      // Check if sorted by name ascending
      for (let i = 1; i < result.sequences.length; i++) {
        expect(result.sequences[i].name >= result.sequences[i-1].name).to.be.true
      }
    })

    it('should return sequence list item format', async () => {
      const query = new GetUserSequencesQuery({
        userEmail: '<EMAIL>',
        limit: 1,
        offset: 0
      })

      const result = await handler.handle(query)
      const sequence = result.sequences[0]

      expect(sequence).to.have.property('id')
      expect(sequence).to.have.property('name')
      expect(sequence).to.have.property('businessName')
      expect(sequence).to.have.property('industry')
      expect(sequence).to.have.property('sequenceType')
      expect(sequence).to.have.property('emailCount')
      expect(sequence).to.have.property('status')
      expect(sequence).to.have.property('estimatedDuration')
      expect(sequence).to.have.property('createdAt')
      expect(sequence).to.have.property('updatedAt')
    })

    it('should validate query parameters', async () => {
      try {
        const query = new GetUserSequencesQuery({
          userEmail: 'invalid-email',
          limit: 150 // Too high
        })
        query.validate()
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
      }
    })
  })

  describe('SearchSequencesQueryHandler', () => {
    const test = createIntegrationTest('SearchSequencesQueryHandler', testFramework)

    let repository
    let handler

    beforeEach(async () => {
      await test.beforeEach()
      
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
      handler = new SearchSequencesQueryHandler(repository)

      // Create sequences with different characteristics for searching
      const sequences = [
        {
          id: 'tech-seq-1',
          name: 'Technology Startup Welcome',
          businessName: 'TechCorp',
          industry: 'Technology',
          targetAudience: 'Software developers',
          sequenceType: 'welcome'
        },
        {
          id: 'health-seq-1', 
          name: 'Healthcare Provider Nurture',
          businessName: 'HealthPlus',
          industry: 'Healthcare',
          targetAudience: 'Medical professionals',
          sequenceType: 'nurture'
        },
        {
          id: 'tech-seq-2',
          name: 'SaaS Product Sales',
          businessName: 'CloudSoft',
          industry: 'Technology',
          targetAudience: 'Business owners',
          sequenceType: 'sales'
        }
      ]

      for (const seqData of sequences) {
        const userEmail = new EmailAddress('<EMAIL>')
        const businessInfo = new BusinessInfo({
          businessName: seqData.businessName,
          industry: seqData.industry,
          targetAudience: seqData.targetAudience,
          goals: 'Test goals',
          tone: 'professional'
        })
        const settings = new SequenceSettings({
          sequenceType: seqData.sequenceType,
          emailCount: 3
        })

        const sequence = new EmailSequence(seqData.id, {
          name: seqData.name,
          userEmail,
          businessInfo,
          settings,
          status: 'published'
        })

        // Add performance for relevance scoring
        for (let i = 1; i <= 3; i++) {
          const content = new EmailContent({
            subject: `Email ${i} Subject`,
            body: `Body content for email ${i} with sufficient length.`
          })
          const email = new Email(`${seqData.id}-email-${i}`, {
            sequencePosition: i,
            content,
            scheduledDays: (i - 1) * 7,
            isActive: true
          })

          if (seqData.id === 'tech-seq-1') {
            // Make this one high performing
            email.updatePerformance({
              sent: 100,
              delivered: 95,
              opened: 35,
              clicked: 8
            })
          }

          sequence.addEmail(email)
        }

        await repository.save(sequence)
      }
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should search by sequence name', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'Technology',
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(1)
      expect(result.sequences[0].name).to.include('Technology')
      expect(result.total).to.equal(1)
    })

    it('should search by business name', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'TechCorp',
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(1)
      expect(result.sequences[0].businessName).to.equal('TechCorp')
    })

    it('should search by industry', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'Technology',
        filters: { industry: 'Technology' },
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(2)
      result.sequences.forEach(seq => {
        expect(seq.industry).to.equal('Technology')
      })
    })

    it('should filter by sequence type', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'Technology',
        filters: { sequenceType: 'welcome' },
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(1)
      expect(result.sequences[0].sequenceType).to.equal('welcome')
    })

    it('should apply pagination', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'seq', // Should match all sequences
        limit: 2,
        offset: 0
      })

      const result = await handler.handle(query)

      expect(result.sequences).to.have.length(2)
      expect(result.hasMore).to.be.true
      expect(result.total).to.equal(3)
    })

    it('should calculate relevance scores', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'Technology',
        limit: 10,
        offset: 0
      })

      const result = await handler.handle(query)

      result.sequences.forEach(seq => {
        expect(seq.relevanceScore).to.be.a('number')
        expect(seq.relevanceScore).to.be.above(0)
      })

      // High performing sequence should have higher relevance
      const highPerfSeq = result.sequences.find(s => s.id === 'tech-seq-1')
      if (highPerfSeq) {
        expect(highPerfSeq.relevanceScore).to.be.above(1.0)
      }
    })

    it('should return search result format', async () => {
      const query = new SearchSequencesQuery({
        searchTerm: 'Technology',
        limit: 1,
        offset: 0
      })

      const result = await handler.handle(query)
      const sequence = result.sequences[0]

      expect(sequence).to.have.property('id')
      expect(sequence).to.have.property('name')
      expect(sequence).to.have.property('businessName')
      expect(sequence).to.have.property('industry')
      expect(sequence).to.have.property('targetAudience')
      expect(sequence).to.have.property('sequenceType')
      expect(sequence).to.have.property('emailCount')
      expect(sequence).to.have.property('status')
      expect(sequence).to.have.property('relevanceScore')
      expect(sequence).to.have.property('createdAt')
      expect(sequence).to.have.property('updatedAt')
    })

    it('should validate search query', async () => {
      try {
        const query = new SearchSequencesQuery({
          searchTerm: 'a', // Too short
          limit: 10,
          offset: 0
        })
        query.validate()
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error).to.be.instanceOf(DomainValidationError)
        expect(error.message).to.include('Search term must be at least 2 characters long')
      }
    })
  })
})

// ========================================
// REPOSITORY INTEGRATION TESTS  
// ========================================

describe('🗄️ Repository Integration', () => {
  describe('InMemoryEmailSequenceRepository', () => {
    const test = createIntegrationTest('InMemoryEmailSequenceRepository', testFramework)

    let repository

    beforeEach(async () => {
      await test.beforeEach()
      repository = new InMemoryEmailSequenceRepository(domainEventPublisher)
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should save and retrieve sequences', async () => {
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings(TestDataFactory.validSequenceSettings())

      const sequence = new EmailSequence('test-seq-1', {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      await repository.save(sequence)
      const retrieved = await repository.findById('test-seq-1')

      expect(retrieved.id).to.equal(sequence.id)
      expect(retrieved.name).to.equal(sequence.name)
      expect(retrieved.userEmail.equals(sequence.userEmail)).to.be.true
    })

    it('should find sequences by user email', async () => {
      const userEmail1 = new EmailAddress('<EMAIL>')
      const userEmail2 = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings(TestDataFactory.validSequenceSettings())

      // Create sequences for different users
      const seq1 = new EmailSequence('seq-1', {
        name: 'Sequence 1',
        userEmail: userEmail1,
        businessInfo,
        settings,
        status: 'draft'
      })

      const seq2 = new EmailSequence('seq-2', {
        name: 'Sequence 2',
        userEmail: userEmail1,
        businessInfo,
        settings,
        status: 'draft'
      })

      const seq3 = new EmailSequence('seq-3', {
        name: 'Sequence 3',
        userEmail: userEmail2,
        businessInfo,
        settings,
        status: 'draft'
      })

      await repository.save(seq1)
      await repository.save(seq2)
      await repository.save(seq3)

      const user1Sequences = await repository.findByUserEmail(userEmail1)
      const user2Sequences = await repository.findByUserEmail(userEmail2)

      expect(user1Sequences).to.have.length(2)
      expect(user2Sequences).to.have.length(1)
    })

    it('should find high performing sequences', async () => {
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings(TestDataFactory.validSequenceSettings())

      // Create high performing sequence
      const highPerfSequence = new EmailSequence('high-perf', {
        name: 'High Performance',
        userEmail,
        businessInfo,
        settings,
        status: 'published'
      })

      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i}`,
          body: `Body content for email ${i} with sufficient length.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })

        // High performance metrics
        email.updatePerformance({
          sent: 100,
          delivered: 95,
          opened: 30, // 31.6% open rate
          clicked: 5   // 5.3% click rate
        })

        highPerfSequence.addEmail(email)
      }

      // Create low performing sequence
      const lowPerfSequence = new EmailSequence('low-perf', {
        name: 'Low Performance',
        userEmail,
        businessInfo,
        settings,
        status: 'published'
      })

      for (let i = 1; i <= 3; i++) {
        const content = new EmailContent({
          subject: `Email ${i}`,
          body: `Body content for email ${i} with sufficient length.`
        })
        const email = new Email(`email-${i}`, {
          sequencePosition: i,
          content,
          scheduledDays: (i - 1) * 7,
          isActive: true
        })

        // Low performance metrics
        email.updatePerformance({
          sent: 100,
          delivered: 80,
          opened: 10, // 12.5% open rate
          clicked: 1   // 1.25% click rate
        })

        lowPerfSequence.addEmail(email)
      }

      await repository.save(highPerfSequence)
      await repository.save(lowPerfSequence)

      const highPerforming = await repository.findHighPerforming()
      const needingOptimization = await repository.findNeedingOptimization()

      expect(highPerforming).to.have.length(1)
      expect(highPerforming[0].id).to.equal('high-perf')

      expect(needingOptimization).to.have.length(1)
      expect(needingOptimization[0].id).to.equal('low-perf')
    })

    it('should handle concurrency conflicts', async () => {
      const userEmail = new EmailAddress('<EMAIL>')
      const businessInfo = new BusinessInfo(TestDataFactory.validBusinessInfo())
      const settings = new SequenceSettings(TestDataFactory.validSequenceSettings())

      const sequence = new EmailSequence('test-seq', {
        name: 'Test Sequence',
        userEmail,
        businessInfo,
        settings,
        status: 'draft'
      })

      await repository.save(sequence)

      // Simulate concurrent modification
      const sequence1 = await repository.findById('test-seq')
      const sequence2 = await repository.findById('test-seq')

      sequence1.incrementVersion()
      await repository.save(sequence1)

      // This should fail due to version conflict
      sequence2.incrementVersion()
      
      try {
        await repository.save(sequence2)
        expect.fail('Should have thrown concurrency error')
      } catch (error) {
        expect(error.name).to.include('ConcurrencyError')
      }
    })
  })
})