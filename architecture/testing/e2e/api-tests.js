/**
 * 🧪 End-to-End API Tests
 * Tests complete user workflows through HTTP API
 */

import { expect } from 'chai'
import {
  createE2ETest,
  TestDataFactory,
  PerformanceTestUtils,
  testFramework
} from '../test-framework.js'

// ========================================
// AUTHENTICATION & USER MANAGEMENT E2E
// ========================================

describe('🔐 Authentication & User Management E2E', () => {
  describe('User Registration and Login Flow', () => {
    const test = createE2ETest('User Authentication Flow', testFramework)

    beforeEach(async () => {
      await test.beforeEach()
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should complete full user registration flow', async () => {
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        plan: 'free'
      }

      const response = await test.registerUser(userData)

      expect(response.user.name).to.equal(userData.name)
      expect(response.user.email).to.equal(userData.email)
      expect(response.user.plan).to.equal(userData.plan)
      expect(response.token).to.be.a('string')

      // Verify user can access protected routes
      const profileResponse = await test.get('/api/auth/profile')
        .expect(200)

      expect(profileResponse.body.user.email).to.equal(userData.email)
    })

    it('should prevent duplicate user registration', async () => {
      await test.registerUser({
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'SecurePassword123!'
      })

      // Try to register same email again
      await testFramework.agent
        .post('/api/auth/register')
        .send({
          name: 'Jane Doe',
          email: '<EMAIL>',
          password: 'AnotherPassword123!'
        })
        .expect(400)
    })

    it('should handle login with correct credentials', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'SecurePassword123!'
      }

      await test.registerUser(userData)
      const loginResponse = await test.loginUser(userData.email, userData.password)

      expect(loginResponse.user.email).to.equal(userData.email)
      expect(loginResponse.token).to.be.a('string')
    })

    it('should reject login with incorrect credentials', async () => {
      await test.registerUser({
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'SecurePassword123!'
      })

      await testFramework.agent
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword'
        })
        .expect(401)
    })

    it('should validate user input during registration', async () => {
      await testFramework.agent
        .post('/api/auth/register')
        .send({
          name: '', // Invalid: empty name
          email: 'invalid-email', // Invalid: bad email format
          password: '123' // Invalid: too short
        })
        .expect(400)
    })
  })
})

// ========================================
// EMAIL SEQUENCE GENERATION E2E
// ========================================

describe('📧 Email Sequence Generation E2E', () => {
  describe('Complete Sequence Generation Flow', () => {
    const test = createE2ETest('Sequence Generation Flow', testFramework)

    beforeEach(async () => {
      await test.beforeEach()
      await test.registerUser({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'pro' // Pro plan for higher limits
      })
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should generate complete email sequence', async () => {
      const sequenceData = {
        businessName: 'TechCorp',
        industry: 'Technology',
        targetAudience: 'Software developers and tech leads',
        goals: 'Increase product adoption and engagement',
        tone: 'professional',
        additionalInfo: 'Focus on technical benefits and ROI',
        sequenceType: 'nurture',
        emailCount: 5,
        frequency: 'weekly',
        personalizeLevel: 'medium',
        includeCallToAction: true,
        ctaType: 'soft'
      }

      const response = await test.post('/api/sequences/generate')
        .send(sequenceData)
        .expect(201)

      expect(response.body.sequence).to.have.property('id')
      expect(response.body.sequence.name).to.include('TechCorp')
      expect(response.body.sequence.emails).to.have.length(5)
      expect(response.body.sequence.status).to.equal('draft')

      // Verify each email has required content
      response.body.sequence.emails.forEach((email, index) => {
        expect(email.subject).to.be.a('string').that.is.not.empty
        expect(email.body).to.be.a('string').that.is.not.empty
        expect(email.position).to.equal(index + 1)
        expect(email.scheduledDays).to.equal(index * 7)
      })
    })

    it('should respect usage limits for free users', async () => {
      // Create free user
      await test.registerUser({
        name: 'Free User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'free'
      })

      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      // Generate sequences up to free limit (5)
      for (let i = 0; i < 5; i++) {
        await test.post('/api/sequences/generate')
          .send({
            ...sequenceData,
            businessName: `TestCorp ${i}`
          })
          .expect(201)
      }

      // 6th sequence should fail
      await test.post('/api/sequences/generate')
        .send({
          ...sequenceData,
          businessName: 'TestCorp 6'
        })
        .expect(403) // Forbidden due to usage limit
    })

    it('should validate business information requirements', async () => {
      const invalidData = {
        businessName: 'A', // Too short
        industry: '', // Missing
        targetAudience: 'Short', // Too short
        emailCount: 15 // Too many
      }

      await test.post('/api/sequences/generate')
        .send(invalidData)
        .expect(400)
    })

    it('should track generation time performance', async () => {
      const sequenceData = {
        businessName: 'PerformanceTest Corp',
        industry: 'Technology',
        targetAudience: 'Software developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      const duration = await test.assertResponseTime(
        () => test.post('/api/sequences/generate').send(sequenceData),
        5000 // Max 5 seconds for generation
      )

      expect(duration).to.be.below(5000)
    })

    it('should handle AI service failures gracefully', async () => {
      // This would require mocking the AI service to fail
      // For now, we'll test with malformed data that might cause issues
      const problematicData = {
        businessName: 'Test Corp',
        industry: 'Technology',
        targetAudience: 'Software developers',
        sequenceType: 'nurture',
        emailCount: 3,
        tone: 'extremely_complex_technical_jargon_with_unicode_😀🎉🚀'
      }

      const response = await test.post('/api/sequences/generate')
        .send(problematicData)
        .expect(201) // Should still succeed with fallback

      expect(response.body.sequence.emails).to.have.length(3)
    })
  })

  describe('Sequence Management Operations', () => {
    const test = createE2ETest('Sequence Management', testFramework)
    let sequenceId

    beforeEach(async () => {
      await test.beforeEach()
      await test.registerUser({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'pro'
      })

      // Create a test sequence
      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Software developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      const response = await test.post('/api/sequences/generate')
        .send(sequenceData)
        .expect(201)

      sequenceId = response.body.sequence.id
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should retrieve sequence by ID', async () => {
      const response = await test.get(`/api/sequences/${sequenceId}`)
        .expect(200)

      expect(response.body.sequence.id).to.equal(sequenceId)
      expect(response.body.sequence.emails).to.have.length(3)
      expect(response.body.sequence.businessInfo).to.have.property('businessName')
    })

    it('should update email content', async () => {
      const updateData = {
        emailPosition: 1,
        subject: 'Updated Subject Line',
        body: 'This is the updated email body content with sufficient length for validation requirements.'
      }

      const response = await test.put(`/api/sequences/${sequenceId}/emails`)
        .send(updateData)
        .expect(200)

      expect(response.body.updatedContent.subject).to.include('Updated Subject')

      // Verify the change was persisted
      const sequenceResponse = await test.get(`/api/sequences/${sequenceId}`)
        .expect(200)

      const updatedEmail = sequenceResponse.body.sequence.emails[0]
      expect(updatedEmail.subject).to.include('Updated Subject')
    })

    it('should publish sequence when ready', async () => {
      const response = await test.post(`/api/sequences/${sequenceId}/publish`)
        .expect(200)

      expect(response.body.sequence.status).to.equal('published')

      // Verify sequence is now published
      const sequenceResponse = await test.get(`/api/sequences/${sequenceId}`)
        .expect(200)

      expect(sequenceResponse.body.sequence.status).to.equal('published')
    })

    it('should archive sequence', async () => {
      const response = await test.post(`/api/sequences/${sequenceId}/archive`)
        .expect(200)

      expect(response.body.sequence.status).to.equal('archived')
    })

    it('should duplicate sequence', async () => {
      const duplicateData = {
        newName: 'Duplicated TestCorp Sequence'
      }

      const response = await test.post(`/api/sequences/${sequenceId}/duplicate`)
        .send(duplicateData)
        .expect(200)

      expect(response.body.duplicateSequence.id).to.not.equal(sequenceId)
      expect(response.body.duplicateSequence.name).to.include('Duplicated')
      expect(response.body.duplicateSequence.emails).to.have.length(3)
      expect(response.body.duplicateSequence.status).to.equal('draft')
    })

    it('should prevent unauthorized access to other users sequences', async () => {
      // Create another user
      await testFramework.agent
        .post('/api/auth/register')
        .send({
          name: 'Other User',
          email: '<EMAIL>',
          password: 'TestPassword123!'
        })
        .expect(201)

      const loginResponse = await testFramework.agent
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        })
        .expect(200)

      // Try to access first user's sequence with second user's token
      await testFramework.agent
        .get(`/api/sequences/${sequenceId}`)
        .set('Authorization', `Bearer ${loginResponse.body.token}`)
        .expect(403) // Forbidden
    })
  })
})

// ========================================
// USAGE TRACKING & BILLING E2E
// ========================================

describe('💳 Usage Tracking & Billing E2E', () => {
  describe('Usage Limits and Tracking', () => {
    const test = createE2ETest('Usage Tracking', testFramework)

    beforeEach(async () => {
      await test.beforeEach()
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should track usage correctly for free plan', async () => {
      await test.registerUser({
        name: 'Free User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'free'
      })

      // Check initial usage
      const initialUsage = await test.get('/api/usage/stats')
        .expect(200)

      expect(initialUsage.body.currentPeriod.sequencesGenerated).to.equal(0)
      expect(initialUsage.body.limits.sequences).to.equal(5)
      expect(initialUsage.body.canGenerate).to.be.true

      // Generate a sequence
      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      await test.post('/api/sequences/generate')
        .send(sequenceData)
        .expect(201)

      // Check updated usage
      const updatedUsage = await test.get('/api/usage/stats')
        .expect(200)

      expect(updatedUsage.body.currentPeriod.sequencesGenerated).to.equal(1)
      expect(updatedUsage.body.usage.percentage).to.equal(20) // 1/5 = 20%
      expect(updatedUsage.body.canGenerate).to.be.true
    })

    it('should enforce usage limits for free plan', async () => {
      await test.registerUser({
        name: 'Free User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'free'
      })

      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      // Generate 5 sequences (free limit)
      for (let i = 0; i < 5; i++) {
        await test.post('/api/sequences/generate')
          .send({
            ...sequenceData,
            businessName: `TestCorp ${i}`
          })
          .expect(201)
      }

      // Check usage at limit
      const limitUsage = await test.get('/api/usage/stats')
        .expect(200)

      expect(limitUsage.body.currentPeriod.sequencesGenerated).to.equal(5)
      expect(limitUsage.body.usage.percentage).to.equal(100)
      expect(limitUsage.body.canGenerate).to.be.false

      // Try to generate 6th sequence
      await test.post('/api/sequences/generate')
        .send({
          ...sequenceData,
          businessName: 'TestCorp 6'
        })
        .expect(403)
    })

    it('should allow overage for pro plan with consent', async () => {
      await test.registerUser({
        name: 'Pro User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'pro'
      })

      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      // Generate sequences up to pro limit (75)
      // For testing, we'll simulate being near the limit
      await test.post('/api/usage/simulate-near-limit')
        .send({ sequencesGenerated: 74 })
        .expect(200)

      // Generate one more to reach limit
      await test.post('/api/sequences/generate')
        .send(sequenceData)
        .expect(201)

      // Check we're at limit
      const limitUsage = await test.get('/api/usage/stats')
        .expect(200)

      expect(limitUsage.body.currentPeriod.sequencesGenerated).to.equal(75)
      expect(limitUsage.body.canGenerate).to.be.false

      // Give overage consent
      await test.post('/api/usage/overage-consent')
        .send({ consent: true })
        .expect(200)

      // Should now be able to generate with overage
      const overageResponse = await test.post('/api/sequences/generate')
        .send({
          ...sequenceData,
          businessName: 'Overage TestCorp'
        })
        .expect(201)

      expect(overageResponse.body.overage).to.be.true
      expect(overageResponse.body.overageCharge).to.equal(3) // $3 per sequence

      // Check updated usage with overage
      const overageUsage = await test.get('/api/usage/stats')
        .expect(200)

      expect(overageUsage.body.currentPeriod.overageSequences).to.equal(1)
      expect(overageUsage.body.currentPeriod.overageCharges).to.equal(3)
    })

    it('should provide usage history and projections', async () => {
      await test.registerUser({
        name: 'Pro User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'pro'
      })

      // Generate some sequences
      const sequenceData = {
        businessName: 'TestCorp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      for (let i = 0; i < 3; i++) {
        await test.post('/api/sequences/generate')
          .send({
            ...sequenceData,
            businessName: `TestCorp ${i}`
          })
          .expect(201)
      }

      const historyResponse = await test.get('/api/usage/history')
        .expect(200)

      expect(historyResponse.body.currentPeriod).to.have.property('sequencesGenerated')
      expect(historyResponse.body.currentPeriod.sequencesGenerated).to.equal(3)
      expect(historyResponse.body.projection).to.have.property('endOfPeriod')
      expect(historyResponse.body.history).to.be.an('array')
    })
  })
})

// ========================================
// PERFORMANCE & SCALABILITY E2E
// ========================================

describe('⚡ Performance & Scalability E2E', () => {
  describe('Load Testing and Performance', () => {
    const test = createE2ETest('Performance Testing', testFramework)

    beforeEach(async () => {
      await test.beforeEach()
      await test.registerUser({
        name: 'Performance User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'business' // Higher limits for load testing
      })
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should handle concurrent sequence generation', async () => {
      const sequenceData = {
        businessName: 'LoadTest Corp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 3
      }

      const concurrentRequests = async () => {
        return test.post('/api/sequences/generate')
          .send({
            ...sequenceData,
            businessName: `LoadTest Corp ${Math.random()}`
          })
      }

      const results = await PerformanceTestUtils.loadTest(
        concurrentRequests,
        20, // 20 iterations
        5   // 5 concurrent
      )

      expect(results.successRate).to.be.above(95) // 95% success rate
      expect(results.averageTime).to.be.below(3000) // Average under 3 seconds
      expect(results.p95).to.be.below(5000) // 95th percentile under 5 seconds
    })

    it('should maintain API response times under load', async () => {
      // Create some sequences first
      for (let i = 0; i < 5; i++) {
        await test.post('/api/sequences/generate')
          .send({
            businessName: `TestCorp ${i}`,
            industry: 'Technology',
            targetAudience: 'Developers',
            sequenceType: 'nurture',
            emailCount: 3
          })
          .expect(201)
      }

      const concurrentReads = async () => {
        return test.get('/api/sequences')
      }

      const results = await PerformanceTestUtils.loadTest(
        concurrentReads,
        50, // 50 iterations
        10  // 10 concurrent
      )

      expect(results.successRate).to.equal(100) // All reads should succeed
      expect(results.averageTime).to.be.below(500) // Average under 500ms
      expect(results.p95).to.be.below(1000) // 95th percentile under 1 second
    })

    it('should handle database operations efficiently', async () => {
      // Test search performance with multiple sequences
      const sequences = []
      for (let i = 0; i < 10; i++) {
        const response = await test.post('/api/sequences/generate')
          .send({
            businessName: `SearchTest Corp ${i}`,
            industry: i % 2 === 0 ? 'Technology' : 'Healthcare',
            targetAudience: 'Professionals',
            sequenceType: 'nurture',
            emailCount: 3
          })
          .expect(201)
        sequences.push(response.body.sequence)
      }

      // Test search performance
      const searchRequest = async () => {
        return test.get('/api/sequences/search')
          .query({ q: 'SearchTest', limit: 5 })
      }

      const searchResults = await PerformanceTestUtils.loadTest(
        searchRequest,
        30, // 30 iterations
        5   // 5 concurrent
      )

      expect(searchResults.successRate).to.equal(100)
      expect(searchResults.averageTime).to.be.below(300) // Average under 300ms
    })

    it('should handle memory efficiently with large sequences', async () => {
      // Generate sequence with maximum emails
      const largeSequenceData = {
        businessName: 'Large Sequence Corp',
        industry: 'Technology',
        targetAudience: 'Enterprise developers and technical decision makers',
        goals: 'Comprehensive onboarding and engagement for enterprise clients',
        sequenceType: 'onboarding',
        emailCount: 10, // Maximum allowed
        frequency: 'daily'
      }

      const startMemory = process.memoryUsage()

      const response = await test.post('/api/sequences/generate')
        .send(largeSequenceData)
        .expect(201)

      expect(response.body.sequence.emails).to.have.length(10)

      // Retrieve the large sequence multiple times
      for (let i = 0; i < 5; i++) {
        await test.get(`/api/sequences/${response.body.sequence.id}`)
          .expect(200)
      }

      const endMemory = process.memoryUsage()
      const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).to.be.below(50 * 1024 * 1024)
    })
  })
})

// ========================================
// ERROR HANDLING & RESILIENCE E2E
// ========================================

describe('🛡️ Error Handling & Resilience E2E', () => {
  describe('Graceful Error Handling', () => {
    const test = createE2ETest('Error Handling', testFramework)

    beforeEach(async () => {
      await test.beforeEach()
      await test.registerUser({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        plan: 'pro'
      })
    })

    afterEach(async () => {
      await test.afterEach()
    })

    it('should handle invalid sequence IDs gracefully', async () => {
      const response = await test.get('/api/sequences/invalid-id')
        .expect(404)

      expect(response.body.error).to.include('not found')
    })

    it('should handle malformed request data', async () => {
      const response = await test.post('/api/sequences/generate')
        .send({
          invalidField: 'invalid data',
          emailCount: 'not a number'
        })
        .expect(400)

      expect(response.body.error).to.be.a('string')
      expect(response.body.validationErrors).to.be.an('array')
    })

    it('should handle database connection issues', async () => {
      // This would require actually simulating DB failures
      // For now, we test timeout scenarios
      const largeSequenceData = {
        businessName: 'Timeout Test Corp',
        industry: 'Technology',
        targetAudience: 'A very long target audience description that might cause processing delays' + 'x'.repeat(1000),
        sequenceType: 'nurture',
        emailCount: 10
      }

      // Should either succeed or fail gracefully within reasonable time
      const duration = await test.assertResponseTime(
        () => test.post('/api/sequences/generate').send(largeSequenceData),
        10000 // Max 10 seconds
      )

      expect(duration).to.be.below(10000)
    })

    it('should maintain data consistency on failures', async () => {
      const sequenceData = {
        businessName: 'Consistency Test Corp',
        industry: 'Technology',
        targetAudience: 'Developers',
        sequenceType: 'nurture',
        emailCount: 5
      }

      // Generate sequence
      const response = await test.post('/api/sequences/generate')
        .send(sequenceData)
        .expect(201)

      const sequenceId = response.body.sequence.id

      // Try to update with invalid data
      await test.put(`/api/sequences/${sequenceId}/emails`)
        .send({
          emailPosition: 1,
          subject: '', // Invalid: empty subject
          body: 'x' // Invalid: too short body
        })
        .expect(400)

      // Verify original sequence is unchanged
      const unchangedResponse = await test.get(`/api/sequences/${sequenceId}`)
        .expect(200)

      expect(unchangedResponse.body.sequence.emails[0].subject).to.not.be.empty
      expect(unchangedResponse.body.sequence.emails[0].body.length).to.be.above(50)
    })

    it('should provide helpful error messages', async () => {
      const response = await test.post('/api/sequences/generate')
        .send({
          businessName: 'A',
          industry: '',
          targetAudience: 'Short',
          emailCount: 15
        })
        .expect(400)

      expect(response.body.validationErrors).to.be.an('array')
      expect(response.body.validationErrors.length).to.be.above(0)

      // Check that error messages are descriptive
      const errorMessages = response.body.validationErrors
      expect(errorMessages.some(msg => msg.includes('Business name'))).to.be.true
      expect(errorMessages.some(msg => msg.includes('Industry'))).to.be.true
      expect(errorMessages.some(msg => msg.includes('Target audience'))).to.be.true
      expect(errorMessages.some(msg => msg.includes('Email count'))).to.be.true
    })
  })
})