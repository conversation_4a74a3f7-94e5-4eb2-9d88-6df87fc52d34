# 🌟 NeuroColony Microservices Architecture
# Production-ready microservices deployment with planetary scale foundation

version: '3.8'

# Shared networks for service communication
networks:
  neurocolony-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  neurocolony-internal:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# Shared volumes for persistent data
volumes:
  mongodb-data:
    driver: local
  redis-cluster-data:
    driver: local
  elasticsearch-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

services:
  # ==========================================================================
  # API GATEWAY - Kong Enterprise
  # ==========================================================================
  api-gateway:
    image: kong/kong-gateway:latest
    container_name: neurocolony-api-gateway
    hostname: api-gateway
    environment:
      KONG_DATABASE: 'off'
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_PROXY_LISTEN: 0.0.0.0:8000
      KONG_PLUGINS: bundled,prometheus,rate-limiting,cors,jwt,oauth2
    volumes:
      - ./architecture/kong/kong.yml:/kong/declarative/kong.yml:ro
      - ./architecture/kong/plugins:/kong/plugins:ro
    ports:
      - "8000:8000"  # Proxy port
      - "8001:8001"  # Admin API
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # ==========================================================================
  # USER MANAGEMENT SERVICE
  # ==========================================================================
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: neurocolony-user-service
    hostname: user-service
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=*******************************************************************************************************************************
      - REDIS_CLUSTER_NODES=redis-node-1:7001,redis-node-2:7002,redis-node-3:7003
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - BCRYPT_SALT_ROUNDS=12
      - RATE_LIMIT_WINDOW=900000  # 15 minutes
      - RATE_LIMIT_MAX_REQUESTS=100
      - SESSION_TIMEOUT=14400000  # 4 hours
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - AUDIT_LOG_ENABLED=true
    volumes:
      - ./architecture/configs/user-service.yml:/app/config/production.yml:ro
      - ./logs/user-service:/app/logs
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - mongodb-primary
      - redis-cluster
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 6
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ==========================================================================
  # AI GENERATION SERVICE
  # ==========================================================================
  ai-generation-service:
    build:
      context: ./services/ai-generation-service
      dockerfile: Dockerfile.gpu
    container_name: neurocolony-ai-generation
    hostname: ai-generation-service
    environment:
      - PYTHON_ENV=production
      - PORT=3002
      - WORKERS=4
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - REDIS_CLUSTER_NODES=redis-node-1:7001,redis-node-2:7002,redis-node-3:7003
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - LOCAL_MODEL_ENDPOINT=http://local-model-server:8080
      - AI_CACHE_TTL=86400  # 24 hours
      - MAX_CONCURRENT_REQUESTS=50
      - REQUEST_TIMEOUT=300  # 5 minutes
      - MODEL_SELECTION_STRATEGY=cost_optimized
      - ENABLE_BATCH_PROCESSING=true
    volumes:
      - ./architecture/configs/ai-generation.yml:/app/config/production.yml:ro
      - ./models:/app/models:ro
      - ./logs/ai-generation:/app/logs
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - redis-cluster
      - local-model-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 60s
      timeout: 30s
      retries: 3
    deploy:
      replicas: 8
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  # ==========================================================================
  # LOCAL AI MODEL SERVER
  # ==========================================================================
  local-model-server:
    image: ollama/ollama:latest
    container_name: neurocolony-local-models
    hostname: local-model-server
    environment:
      - OLLAMA_NUM_PARALLEL=4
      - OLLAMA_MAX_LOADED_MODELS=3
      - OLLAMA_HOST=0.0.0.0:8080
      - OLLAMA_MODELS=/models
    volumes:
      - ./models:/models
      - ./architecture/configs/ollama-config.json:/root/.ollama/config.json:ro
    ports:
      - "11434:8080"
    networks:
      - neurocolony-internal
    deploy:
      resources:
        limits:
          cpus: '8'
          memory: 32G
        reservations:
          cpus: '4'
          memory: 16G
      # For GPU support (uncomment if GPUs available)
      # devices:
      #   - driver: nvidia
      #     count: 1
      #     capabilities: [gpu]

  # ==========================================================================
  # ANALYTICS SERVICE
  # ==========================================================================
  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    container_name: neurocolony-analytics
    hostname: analytics-service
    environment:
      - NODE_ENV=production
      - PORT=3003
      - MONGODB_URI=***********************************************************************************************************************************
      - CLICKHOUSE_HOST=clickhouse-server
      - CLICKHOUSE_PORT=8123
      - CLICKHOUSE_DATABASE=neurocolony_events
      - REDIS_CLUSTER_NODES=redis-node-1:7001,redis-node-2:7002,redis-node-3:7003
      - KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
      - ENABLE_REAL_TIME_ANALYTICS=true
      - BATCH_PROCESSING_INTERVAL=300000  # 5 minutes
      - AGGREGATION_CACHE_TTL=3600  # 1 hour
    volumes:
      - ./architecture/configs/analytics-service.yml:/app/config/production.yml:ro
      - ./logs/analytics:/app/logs
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - mongodb-primary
      - clickhouse-server
      - kafka-1
    deploy:
      replicas: 4
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # ==========================================================================
  # NOTIFICATION SERVICE
  # ==========================================================================
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: neurocolony-notifications
    hostname: notification-service
    environment:
      - NODE_ENV=production
      - PORT=3004
      - REDIS_CLUSTER_NODES=redis-node-1:7001,redis-node-2:7002,redis-node-3:7003
      - KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - WEBPUSH_VAPID_PUBLIC=${WEBPUSH_VAPID_PUBLIC}
      - WEBPUSH_VAPID_PRIVATE=${WEBPUSH_VAPID_PRIVATE}
      - EMAIL_RATE_LIMIT=1000  # per minute
      - SMS_RATE_LIMIT=100     # per minute
      - ENABLE_BATCH_EMAIL=true
      - BATCH_SIZE=100
    volumes:
      - ./architecture/configs/notification-service.yml:/app/config/production.yml:ro
      - ./logs/notifications:/app/logs
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - kafka-1
      - redis-cluster
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ==========================================================================
  # DATABASE CLUSTER - MongoDB ReplicaSet
  # ==========================================================================
  mongodb-primary:
    image: mongo:7.0
    container_name: neurocolony-mongodb-primary
    hostname: mongodb-primary
    command: 
      - mongod
      - --replSet=rs0
      - --bind_ip_all
      - --auth
      - --keyFile=/etc/mongodb/keyfile
      - --wiredTigerCacheSizeGB=4
      - --wiredTigerCollectionBlockCompressor=zstd
      - --wiredTigerIndexPrefixCompression=true
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: neurocolony2025
      MONGO_INITDB_DATABASE: neurocolony
    volumes:
      - mongodb-data:/data/db
      - ./architecture/mongodb/keyfile:/etc/mongodb/keyfile:ro
      - ./architecture/mongodb/mongod.conf:/etc/mongod.conf:ro
      - ./architecture/mongodb/init-replica.js:/docker-entrypoint-initdb.d/init-replica.js:ro
    ports:
      - "27017:27017"
    networks:
      - neurocolony-internal
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  mongodb-secondary:
    image: mongo:7.0
    container_name: neurocolony-mongodb-secondary
    hostname: mongodb-secondary
    command:
      - mongod
      - --replSet=rs0
      - --bind_ip_all
      - --auth
      - --keyFile=/etc/mongodb/keyfile
      - --wiredTigerCacheSizeGB=4
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: neurocolony2025
    volumes:
      - ./architecture/mongodb/keyfile:/etc/mongodb/keyfile:ro
    networks:
      - neurocolony-internal
    depends_on:
      - mongodb-primary
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # ==========================================================================
  # REDIS CLUSTER
  # ==========================================================================
  redis-node-1:
    image: redis:7.2-alpine
    container_name: neurocolony-redis-1
    hostname: redis-node-1
    command: redis-server /etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7001
    volumes:
      - redis-cluster-data:/data
      - ./architecture/redis/redis-cluster.conf:/etc/redis/redis.conf:ro
    ports:
      - "7001:7001"
      - "17001:17001"
    networks:
      - neurocolony-internal

  redis-node-2:
    image: redis:7.2-alpine
    container_name: neurocolony-redis-2
    hostname: redis-node-2
    command: redis-server /etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7002
    volumes:
      - ./architecture/redis/redis-cluster.conf:/etc/redis/redis.conf:ro
    ports:
      - "7002:7002"
      - "17002:17002"
    networks:
      - neurocolony-internal

  redis-node-3:
    image: redis:7.2-alpine
    container_name: neurocolony-redis-3
    hostname: redis-node-3
    command: redis-server /etc/redis/redis.conf --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7003
    volumes:
      - ./architecture/redis/redis-cluster.conf:/etc/redis/redis.conf:ro
    ports:
      - "7003:7003"
      - "17003:17003"
    networks:
      - neurocolony-internal

  # Redis Cluster Initialization
  redis-cluster-init:
    image: redis:7.2-alpine
    container_name: neurocolony-redis-cluster-init
    command: redis-cli --cluster create redis-node-1:7001 redis-node-2:7002 redis-node-3:7003 --cluster-replicas 0 --cluster-yes
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
    networks:
      - neurocolony-internal

  # ==========================================================================
  # EVENT STREAMING - Apache Kafka Cluster
  # ==========================================================================
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: neurocolony-zookeeper
    hostname: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - ./architecture/kafka/zookeeper-data:/var/lib/zookeeper/data
      - ./architecture/kafka/zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - neurocolony-internal

  kafka-1:
    image: confluentinc/cp-kafka:latest
    container_name: neurocolony-kafka-1
    hostname: kafka-1
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:9092,PLAINTEXT_HOST://localhost:19092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 2
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: false
      KAFKA_NUM_PARTITIONS: 12
      KAFKA_DEFAULT_REPLICATION_FACTOR: 2
      KAFKA_LOG_RETENTION_HOURS: 168  # 7 days
      KAFKA_LOG_SEGMENT_BYTES: 1073741824  # 1GB
      KAFKA_COMPRESSION_TYPE: lz4
    volumes:
      - ./architecture/kafka/kafka-1-data:/var/lib/kafka/data
    ports:
      - "19092:19092"
    networks:
      - neurocolony-internal

  kafka-2:
    image: confluentinc/cp-kafka:latest
    container_name: neurocolony-kafka-2
    hostname: kafka-2
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 2
    volumes:
      - ./architecture/kafka/kafka-2-data:/var/lib/kafka/data
    ports:
      - "29092:29092"
    networks:
      - neurocolony-internal

  kafka-3:
    image: confluentinc/cp-kafka:latest
    container_name: neurocolony-kafka-3
    hostname: kafka-3
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-3:9092,PLAINTEXT_HOST://localhost:39092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 2
    volumes:
      - ./architecture/kafka/kafka-3-data:/var/lib/kafka/data
    ports:
      - "39092:39092"
    networks:
      - neurocolony-internal

  # ==========================================================================
  # ANALYTICS DATABASE - ClickHouse
  # ==========================================================================
  clickhouse-server:
    image: clickhouse/clickhouse-server:latest
    container_name: neurocolony-clickhouse
    hostname: clickhouse-server
    environment:
      CLICKHOUSE_DB: neurocolony_events
      CLICKHOUSE_USER: admin
      CLICKHOUSE_PASSWORD: clickhouse2025
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    volumes:
      - ./architecture/clickhouse/data:/var/lib/clickhouse
      - ./architecture/clickhouse/config.xml:/etc/clickhouse-server/config.xml:ro
      - ./architecture/clickhouse/users.xml:/etc/clickhouse-server/users.xml:ro
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native client
    networks:
      - neurocolony-internal
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 16G
        reservations:
          cpus: '2'
          memory: 8G

  # ==========================================================================
  # MONITORING STACK
  # ==========================================================================
  
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: neurocolony-prometheus
    hostname: prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=50GB'
      - '--web.enable-lifecycle'
    volumes:
      - ./architecture/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./architecture/monitoring/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - neurocolony-network
      - neurocolony-internal

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: neurocolony-grafana
    hostname: grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: grafana2025
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
      GF_SERVER_DOMAIN: grafana.neurocolony.com
      GF_SERVER_ROOT_URL: https://grafana.neurocolony.com/
    volumes:
      - grafana-data:/var/lib/grafana
      - ./architecture/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./architecture/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    networks:
      - neurocolony-network
    depends_on:
      - prometheus

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: neurocolony-jaeger
    hostname: jaeger
    environment:
      COLLECTOR_ZIPKIN_HOST_PORT: :9411
      SPAN_STORAGE_TYPE: elasticsearch
      ES_SERVER_URLS: http://elasticsearch:9200
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - elasticsearch

  # Elasticsearch for log aggregation and trace storage
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: neurocolony-elasticsearch
    hostname: elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms4g -Xmx4g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - neurocolony-internal
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: neurocolony-logstash
    hostname: logstash
    volumes:
      - ./architecture/elk/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logs:/logs:ro
    environment:
      LS_JAVA_OPTS: "-Xmx2g -Xms2g"
    networks:
      - neurocolony-internal
    depends_on:
      - elasticsearch

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: neurocolony-kibana
    hostname: kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      SERVER_NAME: kibana.neurocolony.com
    ports:
      - "5601:5601"
    networks:
      - neurocolony-network
    depends_on:
      - elasticsearch

  # ==========================================================================
  # LOAD BALANCER - HAProxy
  # ==========================================================================
  load-balancer:
    image: haproxy:latest
    container_name: neurocolony-load-balancer
    hostname: load-balancer
    volumes:
      - ./architecture/haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./architecture/ssl:/etc/ssl/certs:ro
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # HAProxy stats
    networks:
      - neurocolony-network
    depends_on:
      - api-gateway
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # ==========================================================================
  # CACHE WARMING SERVICE
  # ==========================================================================
  cache-warmer:
    build:
      context: ./services/cache-warmer
      dockerfile: Dockerfile
    container_name: neurocolony-cache-warmer
    hostname: cache-warmer
    environment:
      - NODE_ENV=production
      - REDIS_CLUSTER_NODES=redis-node-1:7001,redis-node-2:7002,redis-node-3:7003
      - API_GATEWAY_URL=http://api-gateway:8000
      - WARM_INTERVAL=3600000  # 1 hour
      - ENABLE_PREDICTIVE_WARMING=true
    volumes:
      - ./architecture/configs/cache-warmer.yml:/app/config/production.yml:ro
    networks:
      - neurocolony-internal
    depends_on:
      - redis-cluster
      - api-gateway

  # ==========================================================================
  # HEALTHCHECK SERVICE
  # ==========================================================================
  healthcheck:
    build:
      context: ./services/healthcheck
      dockerfile: Dockerfile
    container_name: neurocolony-healthcheck
    hostname: healthcheck
    environment:
      - NODE_ENV=production
      - CHECK_INTERVAL=30000  # 30 seconds
      - PROMETHEUS_URL=http://prometheus:9090
      - ALERT_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
    volumes:
      - ./architecture/configs/healthcheck.yml:/app/config/production.yml:ro
    networks:
      - neurocolony-network
      - neurocolony-internal
    depends_on:
      - prometheus

# ==========================================================================
# DEPLOYMENT CONFIGURATION
# ==========================================================================
x-deploy-labels: &deploy-labels
  - "traefik.enable=true"
  - "com.neurocolony.service=true"
  - "com.neurocolony.environment=production"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
    labels: "service,environment"

x-restart-policy: &restart-policy
  restart: unless-stopped

# Apply common configuration to all services
x-common-config: &common-config
  <<: *restart-policy
  logging: *default-logging
  labels: *deploy-labels

# Override for all services
services:
  api-gateway:
    <<: *common-config
  user-service:
    <<: *common-config
  ai-generation-service:
    <<: *common-config
  analytics-service:
    <<: *common-config
  notification-service:
    <<: *common-config
  mongodb-primary:
    <<: *common-config
  mongodb-secondary:
    <<: *common-config