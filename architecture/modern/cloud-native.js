/**
 * ☁️ Cloud-Native Infrastructure Manager
 * Container orchestration, service mesh, and cloud platform integration
 */

import Docker from 'dockerode'
import { logger } from '../../backend/utils/logger.js'
import { eventBus } from './event-system.js'
import k8s from '@kubernetes/client-node'
import yaml from 'js-yaml'

class ContainerOrchestrator {
  constructor() {
    this.docker = new Docker()
    this.containers = new Map()
    this.services = new Map()
    this.networks = new Map()
    this.volumes = new Map()
    this.monitoring = {
      cpu: new Map(),
      memory: new Map(),
      network: new Map()
    }
  }

  // Initialize container platform
  async initialize() {
    try {
      await this.docker.ping()
      logger.info('🐳 Docker connection established')
      
      // Create default network for microservices
      await this.createNetwork('sequenceai-mesh', {
        Driver: 'bridge',
        IPAM: {
          Config: [{
            Subnet: '**********/16',
            Gateway: '**********'
          }]
        },
        Options: {
          'com.docker.network.bridge.enable_icc': 'true',
          'com.docker.network.bridge.enable_ip_masquerade': 'true'
        }
      })

      // Start monitoring
      this.startContainerMonitoring()
      
      return true
    } catch (error) {
      logger.error('Container orchestrator initialization failed:', error)
      return false
    }
  }

  // Create and manage networks
  async createNetwork(name, options = {}) {
    try {
      const existingNetwork = await this.docker.getNetwork(name).inspect().catch(() => null)
      if (existingNetwork) {
        logger.info(`📡 Network already exists: ${name}`)
        this.networks.set(name, existingNetwork)
        return existingNetwork
      }

      const network = await this.docker.createNetwork({
        Name: name,
        ...options
      })

      this.networks.set(name, network)
      logger.info(`📡 Network created: ${name}`)
      
      eventBus.publish('network.created', { name, network: network.id })
      return network

    } catch (error) {
      logger.error(`Network creation failed for ${name}:`, error)
      throw error
    }
  }

  // Deploy microservice container
  async deployService(serviceName, config) {
    try {
      const containerConfig = {
        Image: config.image,
        name: `${serviceName}-${Date.now()}`,
        Env: config.environment || [],
        ExposedPorts: this.createExposedPorts(config.ports || []),
        HostConfig: {
          PortBindings: this.createPortBindings(config.ports || []),
          RestartPolicy: { Name: config.restartPolicy || 'unless-stopped' },
          Memory: config.memory || 512 * 1024 * 1024, // 512MB default
          CpuShares: config.cpuShares || 1024,
          NetworkMode: config.network || 'sequenceai-mesh'
        },
        Labels: {
          'service.name': serviceName,
          'service.version': config.version || 'latest',
          'service.type': config.type || 'microservice',
          'sequenceai.managed': 'true'
        },
        Healthcheck: config.healthcheck ? {
          Test: config.healthcheck.test,
          Interval: config.healthcheck.interval || 30000000000, // 30s in nanoseconds
          Timeout: config.healthcheck.timeout || 10000000000,   // 10s in nanoseconds
          Retries: config.healthcheck.retries || 3
        } : undefined
      }

      // Pull image if needed
      await this.pullImage(config.image)

      // Create and start container
      const container = await this.docker.createContainer(containerConfig)
      await container.start()

      // Store service configuration
      this.services.set(serviceName, {
        container,
        config,
        deployedAt: new Date(),
        status: 'running'
      })

      // Add to monitoring
      this.containers.set(container.id, {
        name: serviceName,
        container,
        lastUpdate: Date.now()
      })

      logger.info(`🚀 Service deployed: ${serviceName} (${container.id.substring(0, 12)})`)
      eventBus.publish('service.deployed', { serviceName, containerId: container.id })

      return {
        serviceName,
        containerId: container.id,
        status: 'deployed'
      }

    } catch (error) {
      logger.error(`Service deployment failed for ${serviceName}:`, error)
      throw error
    }
  }

  // Scale service horizontally
  async scaleService(serviceName, replicas) {
    try {
      const service = this.services.get(serviceName)
      if (!service) {
        throw new Error(`Service not found: ${serviceName}`)
      }

      const currentReplicas = await this.getCurrentReplicas(serviceName)
      const scaleDelta = replicas - currentReplicas

      if (scaleDelta > 0) {
        // Scale up
        for (let i = 0; i < scaleDelta; i++) {
          await this.deployService(`${serviceName}-replica-${currentReplicas + i + 1}`, service.config)
        }
        logger.info(`📈 Scaled up ${serviceName}: ${currentReplicas} -> ${replicas}`)
      } else if (scaleDelta < 0) {
        // Scale down
        const containersToRemove = await this.getServiceContainers(serviceName)
        for (let i = 0; i < Math.abs(scaleDelta); i++) {
          if (containersToRemove[i]) {
            await this.removeContainer(containersToRemove[i].id)
          }
        }
        logger.info(`📉 Scaled down ${serviceName}: ${currentReplicas} -> ${replicas}`)
      }

      eventBus.publish('service.scaled', { serviceName, replicas, previousReplicas: currentReplicas })
      return { serviceName, replicas, previousReplicas: currentReplicas }

    } catch (error) {
      logger.error(`Service scaling failed for ${serviceName}:`, error)
      throw error
    }
  }

  // Auto-scaling based on metrics
  async enableAutoScaling(serviceName, policy) {
    try {
      const autoScalePolicy = {
        minReplicas: policy.minReplicas || 1,
        maxReplicas: policy.maxReplicas || 10,
        targetCPU: policy.targetCPU || 70, // 70% CPU utilization
        targetMemory: policy.targetMemory || 80, // 80% memory utilization
        scaleUpCooldown: policy.scaleUpCooldown || 300000, // 5 minutes
        scaleDownCooldown: policy.scaleDownCooldown || 300000,
        checkInterval: policy.checkInterval || 60000 // 1 minute
      }

      // Start auto-scaling monitoring
      const autoScaleInterval = setInterval(async () => {
        try {
          const metrics = await this.getServiceMetrics(serviceName)
          const currentReplicas = await this.getCurrentReplicas(serviceName)
          
          // Check if scaling is needed
          if (metrics.avgCPU > autoScalePolicy.targetCPU || metrics.avgMemory > autoScalePolicy.targetMemory) {
            // Scale up
            const newReplicas = Math.min(currentReplicas + 1, autoScalePolicy.maxReplicas)
            if (newReplicas > currentReplicas) {
              await this.scaleService(serviceName, newReplicas)
              logger.info(`🔄 Auto-scaled up ${serviceName}: CPU ${metrics.avgCPU}%, Memory ${metrics.avgMemory}%`)
            }
          } else if (metrics.avgCPU < (autoScalePolicy.targetCPU * 0.5) && 
                     metrics.avgMemory < (autoScalePolicy.targetMemory * 0.5)) {
            // Scale down
            const newReplicas = Math.max(currentReplicas - 1, autoScalePolicy.minReplicas)
            if (newReplicas < currentReplicas) {
              await this.scaleService(serviceName, newReplicas)
              logger.info(`🔄 Auto-scaled down ${serviceName}: CPU ${metrics.avgCPU}%, Memory ${metrics.avgMemory}%`)
            }
          }

        } catch (error) {
          logger.error(`Auto-scaling check failed for ${serviceName}:`, error)
        }
      }, autoScalePolicy.checkInterval)

      // Store auto-scaling configuration
      const service = this.services.get(serviceName)
      if (service) {
        service.autoScaling = {
          enabled: true,
          policy: autoScalePolicy,
          interval: autoScaleInterval
        }
      }

      logger.info(`🤖 Auto-scaling enabled for ${serviceName}`)
      eventBus.publish('autoscaling.enabled', { serviceName, policy: autoScalePolicy })

    } catch (error) {
      logger.error(`Auto-scaling setup failed for ${serviceName}:`, error)
      throw error
    }
  }

  // Container monitoring
  startContainerMonitoring() {
    setInterval(async () => {
      try {
        for (const [containerId, containerInfo] of this.containers) {
          const stats = await containerInfo.container.stats({ stream: false })
          
          // Calculate CPU percentage
          const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - stats.precpu_stats.cpu_usage.total_usage
          const systemDelta = stats.cpu_stats.system_cpu_usage - stats.precpu_stats.system_cpu_usage
          const cpuPercent = (cpuDelta / systemDelta) * stats.cpu_stats.online_cpus * 100

          // Calculate memory percentage
          const memoryUsage = stats.memory_stats.usage
          const memoryLimit = stats.memory_stats.limit
          const memoryPercent = (memoryUsage / memoryLimit) * 100

          // Store metrics
          this.monitoring.cpu.set(containerId, cpuPercent || 0)
          this.monitoring.memory.set(containerId, memoryPercent || 0)
          this.monitoring.network.set(containerId, {
            rx: stats.networks?.eth0?.rx_bytes || 0,
            tx: stats.networks?.eth0?.tx_bytes || 0
          })

          // Check for alerts
          if (cpuPercent > 90) {
            eventBus.publish('container.high_cpu', { 
              containerId, 
              serviceName: containerInfo.name, 
              cpuPercent 
            })
          }

          if (memoryPercent > 90) {
            eventBus.publish('container.high_memory', { 
              containerId, 
              serviceName: containerInfo.name, 
              memoryPercent 
            })
          }
        }
      } catch (error) {
        logger.error('Container monitoring error:', error)
      }
    }, 10000) // Monitor every 10 seconds
  }

  // Deploy complete application stack
  async deployStack(stackName, services) {
    try {
      const deploymentResults = []

      for (const [serviceName, config] of Object.entries(services)) {
        try {
          const result = await this.deployService(serviceName, config)
          deploymentResults.push(result)
        } catch (error) {
          logger.error(`Failed to deploy ${serviceName} in stack ${stackName}:`, error)
          deploymentResults.push({
            serviceName,
            status: 'failed',
            error: error.message
          })
        }
      }

      logger.info(`📦 Stack deployed: ${stackName} (${deploymentResults.length} services)`)
      eventBus.publish('stack.deployed', { stackName, services: deploymentResults })

      return {
        stackName,
        services: deploymentResults,
        deployedAt: new Date()
      }

    } catch (error) {
      logger.error(`Stack deployment failed for ${stackName}:`, error)
      throw error
    }
  }

  // Blue-Green deployment
  async blueGreenDeploy(serviceName, newConfig) {
    try {
      logger.info(`🔄 Starting blue-green deployment for ${serviceName}`)

      // Deploy green version
      const greenServiceName = `${serviceName}-green`
      await this.deployService(greenServiceName, newConfig)

      // Health check green version
      const healthCheckPassed = await this.waitForHealthy(greenServiceName, 60000)
      if (!healthCheckPassed) {
        throw new Error('Green version failed health checks')
      }

      // Switch traffic (would integrate with load balancer)
      logger.info(`🔀 Switching traffic to green version: ${serviceName}`)
      
      // Remove blue version after successful switch
      setTimeout(async () => {
        try {
          await this.removeService(serviceName)
          // Rename green to main
          const service = this.services.get(greenServiceName)
          if (service) {
            this.services.set(serviceName, service)
            this.services.delete(greenServiceName)
          }
          logger.info(`✅ Blue-green deployment completed for ${serviceName}`)
        } catch (error) {
          logger.error('Blue-green cleanup error:', error)
        }
      }, 30000) // Wait 30 seconds before cleanup

      eventBus.publish('deployment.blue_green.completed', { serviceName })
      return { serviceName, status: 'completed', deploymentType: 'blue-green' }

    } catch (error) {
      logger.error(`Blue-green deployment failed for ${serviceName}:`, error)
      
      // Rollback: remove failed green version
      try {
        await this.removeService(`${serviceName}-green`)
      } catch (rollbackError) {
        logger.error('Rollback failed:', rollbackError)
      }
      
      throw error
    }
  }

  // Utility methods
  async pullImage(image) {
    try {
      await this.docker.pull(image)
      logger.info(`📥 Image pulled: ${image}`)
    } catch (error) {
      logger.error(`Failed to pull image ${image}:`, error)
      throw error
    }
  }

  createExposedPorts(ports) {
    const exposedPorts = {}
    ports.forEach(port => {
      exposedPorts[`${port}/tcp`] = {}
    })
    return exposedPorts
  }

  createPortBindings(ports) {
    const portBindings = {}
    ports.forEach((port, index) => {
      portBindings[`${port}/tcp`] = [{ HostPort: (port + index).toString() }]
    })
    return portBindings
  }

  async getCurrentReplicas(serviceName) {
    const containers = await this.getServiceContainers(serviceName)
    return containers.length
  }

  async getServiceContainers(serviceName) {
    const containers = await this.docker.listContainers({
      filters: {
        label: [`service.name=${serviceName}`]
      }
    })
    return containers
  }

  async getServiceMetrics(serviceName) {
    const containers = await this.getServiceContainers(serviceName)
    const metrics = {
      avgCPU: 0,
      avgMemory: 0,
      totalContainers: containers.length
    }

    if (containers.length === 0) return metrics

    let totalCPU = 0
    let totalMemory = 0

    for (const container of containers) {
      const cpu = this.monitoring.cpu.get(container.Id) || 0
      const memory = this.monitoring.memory.get(container.Id) || 0
      totalCPU += cpu
      totalMemory += memory
    }

    metrics.avgCPU = totalCPU / containers.length
    metrics.avgMemory = totalMemory / containers.length

    return metrics
  }

  async waitForHealthy(serviceName, timeout = 60000) {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      try {
        const service = this.services.get(serviceName)
        if (service) {
          const container = service.container
          const inspect = await container.inspect()
          
          if (inspect.State.Health?.Status === 'healthy') {
            return true
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds
      } catch (error) {
        logger.warn(`Health check failed for ${serviceName}:`, error)
      }
    }
    
    return false
  }

  async removeContainer(containerId) {
    try {
      const container = this.docker.getContainer(containerId)
      await container.stop()
      await container.remove()
      
      this.containers.delete(containerId)
      this.monitoring.cpu.delete(containerId)
      this.monitoring.memory.delete(containerId)
      this.monitoring.network.delete(containerId)
      
      logger.info(`🗑️ Container removed: ${containerId.substring(0, 12)}`)
    } catch (error) {
      logger.error(`Failed to remove container ${containerId}:`, error)
    }
  }

  async removeService(serviceName) {
    try {
      const containers = await this.getServiceContainers(serviceName)
      
      for (const container of containers) {
        await this.removeContainer(container.Id)
      }
      
      this.services.delete(serviceName)
      logger.info(`🗑️ Service removed: ${serviceName}`)
      
    } catch (error) {
      logger.error(`Failed to remove service ${serviceName}:`, error)
    }
  }

  // Get comprehensive platform status
  getStatus() {
    const services = Array.from(this.services.entries()).map(([name, service]) => ({
      name,
      status: service.status,
      deployedAt: service.deployedAt,
      autoScaling: service.autoScaling?.enabled || false
    }))

    const networks = Array.from(this.networks.keys())
    const totalContainers = this.containers.size

    const metrics = {
      totalCPU: 0,
      totalMemory: 0,
      containerCount: totalContainers
    }

    for (const cpu of this.monitoring.cpu.values()) {
      metrics.totalCPU += cpu
    }
    for (const memory of this.monitoring.memory.values()) {
      metrics.totalMemory += memory
    }

    if (totalContainers > 0) {
      metrics.avgCPU = metrics.totalCPU / totalContainers
      metrics.avgMemory = metrics.totalMemory / totalContainers
    }

    return {
      services,
      networks,
      metrics,
      timestamp: new Date().toISOString()
    }
  }
}

// Kubernetes Integration
class KubernetesManager {
  constructor() {
    this.kubeConfig = new k8s.KubeConfig()
    this.k8sApi = null
    this.appsV1Api = null
    this.initialized = false
  }

  async initialize() {
    try {
      this.kubeConfig.loadFromDefault()
      this.k8sApi = this.kubeConfig.makeApiClient(k8s.CoreV1Api)
      this.appsV1Api = this.kubeConfig.makeApiClient(k8s.AppsV1Api)
      
      // Test connection
      await this.k8sApi.listNamespaces()
      
      this.initialized = true
      logger.info('☸️ Kubernetes client initialized')
      return true
      
    } catch (error) {
      logger.warn('Kubernetes not available, using Docker orchestration:', error.message)
      return false
    }
  }

  async deployToKubernetes(serviceName, config) {
    if (!this.initialized) {
      throw new Error('Kubernetes not initialized')
    }

    try {
      const deployment = this.createDeploymentManifest(serviceName, config)
      const service = this.createServiceManifest(serviceName, config)

      // Apply deployment
      await this.appsV1Api.createNamespacedDeployment('default', deployment)
      
      // Apply service
      await this.k8sApi.createNamespacedService('default', service)

      logger.info(`☸️ Deployed to Kubernetes: ${serviceName}`)
      return { serviceName, platform: 'kubernetes', status: 'deployed' }

    } catch (error) {
      logger.error(`Kubernetes deployment failed for ${serviceName}:`, error)
      throw error
    }
  }

  createDeploymentManifest(serviceName, config) {
    return {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: serviceName,
        labels: {
          app: serviceName,
          'sequenceai.managed': 'true'
        }
      },
      spec: {
        replicas: config.replicas || 1,
        selector: {
          matchLabels: {
            app: serviceName
          }
        },
        template: {
          metadata: {
            labels: {
              app: serviceName
            }
          },
          spec: {
            containers: [{
              name: serviceName,
              image: config.image,
              ports: config.ports?.map(port => ({ containerPort: port })) || [],
              env: config.environment?.map(env => {
                const [name, value] = env.split('=')
                return { name, value }
              }) || [],
              resources: {
                requests: {
                  memory: `${config.memory || 256}Mi`,
                  cpu: `${config.cpu || 250}m`
                },
                limits: {
                  memory: `${config.memoryLimit || 512}Mi`,
                  cpu: `${config.cpuLimit || 500}m`
                }
              },
              livenessProbe: config.healthcheck ? {
                httpGet: {
                  path: '/health',
                  port: config.ports?.[0] || 8080
                },
                initialDelaySeconds: 30,
                periodSeconds: 10
              } : undefined
            }]
          }
        }
      }
    }
  }

  createServiceManifest(serviceName, config) {
    return {
      apiVersion: 'v1',
      kind: 'Service',
      metadata: {
        name: serviceName,
        labels: {
          app: serviceName
        }
      },
      spec: {
        selector: {
          app: serviceName
        },
        ports: config.ports?.map(port => ({
          port,
          targetPort: port,
          protocol: 'TCP'
        })) || [],
        type: config.serviceType || 'ClusterIP'
      }
    }
  }
}

export { ContainerOrchestrator, KubernetesManager }