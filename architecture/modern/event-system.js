/**
 * 🎯 Event-Driven Architecture Core
 * Advanced event system with CQRS, Event Sourcing, and Saga patterns
 */

import { EventEmitter } from 'events'
import { logger } from '../../backend/utils/logger.js'
import redis from 'redis'

class EventStore {
  constructor() {
    this.events = new Map()
    this.snapshots = new Map()
    this.subscribers = new Map()
    this.redisClient = null
    this.initialized = false
  }

  async initialize() {
    try {
      // Connect to Redis for distributed events
      this.redisClient = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6380'
      })
      await this.redisClient.connect()
      
      // Set up Redis pub/sub for distributed events
      this.redisSubscriber = this.redisClient.duplicate()
      await this.redisSubscriber.connect()
      
      this.initialized = true
      logger.info('🎯 Event Store initialized with Redis distributed messaging')
      
      return true
    } catch (error) {
      logger.error('Event Store initialization failed:', error)
      return false
    }
  }

  // Append event to stream (Event Sourcing)
  async appendEvent(streamId, eventType, eventData, expectedVersion = -1) {
    try {
      const event = {
        id: this.generateEventId(),
        streamId,
        eventType,
        data: eventData,
        metadata: {
          timestamp: new Date().toISOString(),
          version: this.getNextVersion(streamId),
          correlationId: eventData.correlationId || this.generateCorrelationId(),
          causationId: eventData.causationId || null
        }
      }

      // Optimistic concurrency check
      if (expectedVersion !== -1) {
        const currentVersion = this.getCurrentVersion(streamId)
        if (currentVersion !== expectedVersion) {
          throw new Error(`Concurrency conflict: expected ${expectedVersion}, got ${currentVersion}`)
        }
      }

      // Store locally
      if (!this.events.has(streamId)) {
        this.events.set(streamId, [])
      }
      this.events.get(streamId).push(event)

      // Publish to Redis for distributed processing
      if (this.initialized) {
        await this.redisClient.publish('events', JSON.stringify(event))
      }

      // Trigger local subscribers
      this.notifySubscribers(eventType, event)

      logger.info(`📝 Event appended: ${eventType} to stream ${streamId}`)
      return event

    } catch (error) {
      logger.error('Failed to append event:', error)
      throw error
    }
  }

  // Get events from stream
  getEvents(streamId, fromVersion = 0) {
    const events = this.events.get(streamId) || []
    return events.filter(event => event.metadata.version >= fromVersion)
  }

  // Subscribe to event types
  subscribe(eventType, handler) {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, [])
    }
    this.subscribers.get(eventType).push(handler)

    // Subscribe to Redis events for distributed handling
    if (this.initialized) {
      this.redisSubscriber.subscribe('events', (message) => {
        try {
          const event = JSON.parse(message)
          if (event.eventType === eventType) {
            handler(event)
          }
        } catch (error) {
          logger.error('Redis event handling error:', error)
        }
      })
    }

    logger.info(`🔔 Subscribed to event type: ${eventType}`)
  }

  // Create snapshot for performance
  createSnapshot(streamId, aggregateData) {
    const events = this.getEvents(streamId)
    const version = events.length > 0 ? events[events.length - 1].metadata.version : 0
    
    this.snapshots.set(streamId, {
      data: aggregateData,
      version,
      timestamp: new Date().toISOString()
    })

    logger.info(`📸 Snapshot created for stream ${streamId} at version ${version}`)
  }

  // Get snapshot
  getSnapshot(streamId) {
    return this.snapshots.get(streamId)
  }

  // Replay events from snapshot
  replayFromSnapshot(streamId) {
    const snapshot = this.getSnapshot(streamId)
    if (!snapshot) {
      return this.getEvents(streamId)
    }

    const eventsAfterSnapshot = this.getEvents(streamId, snapshot.version + 1)
    return {
      snapshot: snapshot.data,
      events: eventsAfterSnapshot
    }
  }

  // Utility methods
  generateEventId() {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  generateCorrelationId() {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getCurrentVersion(streamId) {
    const events = this.events.get(streamId) || []
    return events.length > 0 ? events[events.length - 1].metadata.version : 0
  }

  getNextVersion(streamId) {
    return this.getCurrentVersion(streamId) + 1
  }

  notifySubscribers(eventType, event) {
    const handlers = this.subscribers.get(eventType) || []
    handlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        logger.error(`Event handler error for ${eventType}:`, error)
      }
    })
  }

  // Health check
  async healthCheck() {
    try {
      if (this.initialized) {
        await this.redisClient.ping()
      }
      return {
        status: 'healthy',
        eventsStored: Array.from(this.events.values()).reduce((sum, events) => sum + events.length, 0),
        snapshotsStored: this.snapshots.size,
        subscribersCount: Array.from(this.subscribers.values()).reduce((sum, handlers) => sum + handlers.length, 0),
        redisConnected: this.initialized
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      }
    }
  }
}

// CQRS Command Handler
class CommandHandler {
  constructor(eventStore) {
    this.eventStore = eventStore
    this.handlers = new Map()
  }

  registerHandler(commandType, handler) {
    this.handlers.set(commandType, handler)
    logger.info(`⚡ Command handler registered: ${commandType}`)
  }

  async handle(command) {
    try {
      const handler = this.handlers.get(command.type)
      if (!handler) {
        throw new Error(`No handler registered for command: ${command.type}`)
      }

      logger.info(`🎯 Processing command: ${command.type}`)
      const events = await handler(command, this.eventStore)
      
      // Append generated events
      if (Array.isArray(events)) {
        for (const event of events) {
          await this.eventStore.appendEvent(
            command.aggregateId,
            event.type,
            event.data,
            command.expectedVersion
          )
        }
      }

      return { success: true, eventsGenerated: events?.length || 0 }

    } catch (error) {
      logger.error(`Command handling failed for ${command.type}:`, error)
      throw error
    }
  }
}

// Query Handler for CQRS reads
class QueryHandler {
  constructor() {
    this.handlers = new Map()
    this.projections = new Map()
  }

  registerHandler(queryType, handler) {
    this.handlers.set(queryType, handler)
    logger.info(`🔍 Query handler registered: ${queryType}`)
  }

  registerProjection(eventType, projectionHandler) {
    if (!this.projections.has(eventType)) {
      this.projections.set(eventType, [])
    }
    this.projections.get(eventType).push(projectionHandler)
    logger.info(`📊 Projection registered for event: ${eventType}`)
  }

  async handle(query) {
    try {
      const handler = this.handlers.get(query.type)
      if (!handler) {
        throw new Error(`No handler registered for query: ${query.type}`)
      }

      logger.info(`🔍 Processing query: ${query.type}`)
      return await handler(query)

    } catch (error) {
      logger.error(`Query handling failed for ${query.type}:`, error)
      throw error
    }
  }

  // Update projections based on events
  updateProjections(event) {
    const handlers = this.projections.get(event.eventType) || []
    handlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        logger.error(`Projection update failed for ${event.eventType}:`, error)
      }
    })
  }
}

// Saga Pattern for complex workflows
class SagaManager {
  constructor(eventStore) {
    this.eventStore = eventStore
    this.sagas = new Map()
    this.activeSagas = new Map()
  }

  registerSaga(sagaType, sagaDefinition) {
    this.sagas.set(sagaType, sagaDefinition)
    
    // Subscribe to saga trigger events
    sagaDefinition.triggerEvents.forEach(eventType => {
      this.eventStore.subscribe(eventType, (event) => {
        this.handleSagaEvent(sagaType, event)
      })
    })

    logger.info(`🔄 Saga registered: ${sagaType}`)
  }

  async handleSagaEvent(sagaType, event) {
    try {
      const sagaDefinition = this.sagas.get(sagaType)
      const sagaId = this.generateSagaId(event)
      
      let sagaState = this.activeSagas.get(sagaId) || {
        id: sagaId,
        type: sagaType,
        state: 'started',
        data: {},
        steps: [],
        currentStep: 0
      }

      // Execute saga step
      const result = await sagaDefinition.handler(event, sagaState)
      
      if (result.complete) {
        this.activeSagas.delete(sagaId)
        logger.info(`✅ Saga completed: ${sagaType}/${sagaId}`)
      } else {
        sagaState = { ...sagaState, ...result }
        this.activeSagas.set(sagaId, sagaState)
        logger.info(`⏳ Saga step completed: ${sagaType}/${sagaId} - Step ${sagaState.currentStep}`)
      }

    } catch (error) {
      logger.error(`Saga execution failed for ${sagaType}:`, error)
      // Implement compensation logic here
    }
  }

  generateSagaId(event) {
    return `saga_${event.streamId}_${event.metadata.correlationId}`
  }

  // Get active sagas for monitoring
  getActiveSagas() {
    return Array.from(this.activeSagas.values())
  }
}

// Event Bus for decoupled communication
class EventBus extends EventEmitter {
  constructor() {
    super()
    this.setMaxListeners(100) // Allow many subscribers
  }

  publish(eventType, data) {
    logger.info(`📡 Publishing event: ${eventType}`)
    this.emit(eventType, data)
  }

  subscribe(eventType, handler) {
    logger.info(`📡 Subscribing to: ${eventType}`)
    this.on(eventType, handler)
  }

  unsubscribe(eventType, handler) {
    this.off(eventType, handler)
  }
}

// Export instances
const eventStore = new EventStore()
const commandHandler = new CommandHandler(eventStore)
const queryHandler = new QueryHandler()
const sagaManager = new SagaManager(eventStore)
const eventBus = new EventBus()

export {
  eventStore,
  commandHandler,
  queryHandler,
  sagaManager,
  eventBus,
  EventStore,
  CommandHandler,
  QueryHandler,
  SagaManager,
  EventBus
}