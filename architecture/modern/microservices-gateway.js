/**
 * 🌐 Microservices API Gateway
 * Advanced routing, load balancing, and service mesh coordination
 */

import express from 'express'
import httpProxy from 'http-proxy-middleware'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import cors from 'cors'
import { logger } from '../../backend/utils/logger.js'
import { eventBus } from './event-system.js'

class ServiceRegistry {
  constructor() {
    this.services = new Map()
    this.healthChecks = new Map()
    this.loadBalancers = new Map()
    this.circuitBreakers = new Map()
  }

  // Register a microservice
  registerService(serviceName, instances, options = {}) {
    const serviceConfig = {
      name: serviceName,
      instances: instances.map(instance => ({
        ...instance,
        healthy: true,
        lastHealthCheck: Date.now(),
        responseTime: 0,
        requestCount: 0,
        errorCount: 0
      })),
      loadBalancer: options.loadBalancer || 'round-robin',
      healthCheckInterval: options.healthCheckInterval || 30000,
      maxRetries: options.maxRetries || 3,
      timeout: options.timeout || 10000,
      circuitBreaker: {
        errorThreshold: options.errorThreshold || 5,
        resetTimeout: options.resetTimeout || 60000,
        state: 'closed' // closed, open, half-open
      }
    }

    this.services.set(serviceName, serviceConfig)
    this.initializeLoadBalancer(serviceName, serviceConfig.loadBalancer)
    this.initializeCircuitBreaker(serviceName)
    this.startHealthChecks(serviceName)

    logger.info(`🔗 Service registered: ${serviceName} with ${instances.length} instances`)
    
    // Emit service registration event
    eventBus.publish('service.registered', { serviceName, instances: instances.length })
  }

  // Get healthy instance for service
  getHealthyInstance(serviceName) {
    const service = this.services.get(serviceName)
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`)
    }

    // Check circuit breaker
    const circuitBreaker = this.circuitBreakers.get(serviceName)
    if (circuitBreaker.state === 'open') {
      if (Date.now() - circuitBreaker.lastFailure > service.circuitBreaker.resetTimeout) {
        circuitBreaker.state = 'half-open'
        logger.info(`🔄 Circuit breaker half-open for service: ${serviceName}`)
      } else {
        throw new Error(`Circuit breaker open for service: ${serviceName}`)
      }
    }

    const healthyInstances = service.instances.filter(instance => instance.healthy)
    if (healthyInstances.length === 0) {
      throw new Error(`No healthy instances available for service: ${serviceName}`)
    }

    const loadBalancer = this.loadBalancers.get(serviceName)
    const instance = loadBalancer.getNext(healthyInstances)
    
    // Update request count
    instance.requestCount++
    
    return instance
  }

  // Initialize load balancer for service
  initializeLoadBalancer(serviceName, type) {
    let loadBalancer

    switch (type) {
      case 'round-robin':
        loadBalancer = new RoundRobinLoadBalancer()
        break
      case 'least-connections':
        loadBalancer = new LeastConnectionsLoadBalancer()
        break
      case 'weighted':
        loadBalancer = new WeightedLoadBalancer()
        break
      default:
        loadBalancer = new RoundRobinLoadBalancer()
    }

    this.loadBalancers.set(serviceName, loadBalancer)
  }

  // Initialize circuit breaker
  initializeCircuitBreaker(serviceName) {
    this.circuitBreakers.set(serviceName, {
      state: 'closed',
      errorCount: 0,
      lastFailure: 0,
      successCount: 0
    })
  }

  // Start health checks for service
  startHealthChecks(serviceName) {
    const service = this.services.get(serviceName)
    
    const healthCheckInterval = setInterval(async () => {
      const healthPromises = service.instances.map(async (instance) => {
        try {
          const start = Date.now()
          const response = await fetch(`${instance.url}/health`, { 
            timeout: 5000,
            signal: AbortSignal.timeout(5000)
          })
          
          const responseTime = Date.now() - start
          instance.responseTime = responseTime
          instance.lastHealthCheck = Date.now()
          
          if (response.ok) {
            if (!instance.healthy) {
              logger.info(`✅ Service instance recovered: ${serviceName} - ${instance.url}`)
              eventBus.publish('service.instance.recovered', { serviceName, instance: instance.url })
            }
            instance.healthy = true
          } else {
            instance.healthy = false
            logger.warn(`❌ Service instance unhealthy: ${serviceName} - ${instance.url}`)
            eventBus.publish('service.instance.unhealthy', { serviceName, instance: instance.url })
          }
        } catch (error) {
          if (instance.healthy) {
            logger.error(`💥 Service instance failed: ${serviceName} - ${instance.url}`, error)
            eventBus.publish('service.instance.failed', { serviceName, instance: instance.url, error: error.message })
          }
          instance.healthy = false
          instance.lastHealthCheck = Date.now()
        }
      })

      await Promise.allSettled(healthPromises)
    }, service.healthCheckInterval)

    this.healthChecks.set(serviceName, healthCheckInterval)
  }

  // Record service success
  recordSuccess(serviceName) {
    const circuitBreaker = this.circuitBreakers.get(serviceName)
    if (circuitBreaker) {
      circuitBreaker.errorCount = 0
      circuitBreaker.successCount++
      
      if (circuitBreaker.state === 'half-open' && circuitBreaker.successCount >= 3) {
        circuitBreaker.state = 'closed'
        logger.info(`✅ Circuit breaker closed for service: ${serviceName}`)
      }
    }
  }

  // Record service failure
  recordFailure(serviceName) {
    const service = this.services.get(serviceName)
    const circuitBreaker = this.circuitBreakers.get(serviceName)
    
    if (circuitBreaker) {
      circuitBreaker.errorCount++
      circuitBreaker.lastFailure = Date.now()
      
      if (circuitBreaker.errorCount >= service.circuitBreaker.errorThreshold) {
        circuitBreaker.state = 'open'
        logger.warn(`🔥 Circuit breaker opened for service: ${serviceName}`)
        eventBus.publish('circuit.breaker.opened', { serviceName })
      }
    }
  }

  // Get service statistics
  getServiceStats() {
    const stats = {}
    
    for (const [serviceName, service] of this.services) {
      const circuitBreaker = this.circuitBreakers.get(serviceName)
      const healthyCount = service.instances.filter(i => i.healthy).length
      const totalRequests = service.instances.reduce((sum, i) => sum + i.requestCount, 0)
      const totalErrors = service.instances.reduce((sum, i) => sum + i.errorCount, 0)
      const avgResponseTime = service.instances.reduce((sum, i) => sum + i.responseTime, 0) / service.instances.length

      stats[serviceName] = {
        totalInstances: service.instances.length,
        healthyInstances: healthyCount,
        unhealthyInstances: service.instances.length - healthyCount,
        totalRequests,
        totalErrors,
        errorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
        avgResponseTime: Math.round(avgResponseTime),
        circuitBreakerState: circuitBreaker.state,
        loadBalancerType: service.loadBalancer
      }
    }
    
    return stats
  }

  // Cleanup
  destroy() {
    for (const interval of this.healthChecks.values()) {
      clearInterval(interval)
    }
    this.healthChecks.clear()
  }
}

// Load Balancer Strategies
class RoundRobinLoadBalancer {
  constructor() {
    this.currentIndex = 0
  }

  getNext(instances) {
    const instance = instances[this.currentIndex % instances.length]
    this.currentIndex++
    return instance
  }
}

class LeastConnectionsLoadBalancer {
  getNext(instances) {
    return instances.reduce((least, current) => 
      current.requestCount < least.requestCount ? current : least
    )
  }
}

class WeightedLoadBalancer {
  getNext(instances) {
    const totalWeight = instances.reduce((sum, instance) => sum + (instance.weight || 1), 0)
    let random = Math.random() * totalWeight
    
    for (const instance of instances) {
      random -= (instance.weight || 1)
      if (random <= 0) {
        return instance
      }
    }
    
    return instances[0] // Fallback
  }
}

// API Gateway
class APIGateway {
  constructor() {
    this.app = express()
    this.serviceRegistry = new ServiceRegistry()
    this.routes = new Map()
    this.middleware = []
    this.rateLimiters = new Map()
    
    this.setupMiddleware()
    this.setupRoutes()
  }

  setupMiddleware() {
    // Security
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        }
      }
    }))

    // CORS
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
      credentials: true
    }))

    // Request parsing
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`🌐 ${req.method} ${req.path} - ${req.ip}`)
      next()
    })

    // Global rate limiting
    const globalRateLimit = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // Limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP',
      standardHeaders: true,
      legacyHeaders: false
    })
    this.app.use(globalRateLimit)
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: this.serviceRegistry.getServiceStats()
      })
    })

    // Service discovery endpoint
    this.app.get('/services', (req, res) => {
      res.json(this.serviceRegistry.getServiceStats())
    })

    // Route registration endpoint (for development)
    this.app.post('/gateway/routes', (req, res) => {
      const { path, serviceName, methods = ['GET'] } = req.body
      this.registerRoute(path, serviceName, { methods })
      res.json({ message: 'Route registered successfully' })
    })
  }

  // Register a route to a service
  registerRoute(path, serviceName, options = {}) {
    const routeConfig = {
      serviceName,
      methods: options.methods || ['GET', 'POST', 'PUT', 'DELETE'],
      rateLimit: options.rateLimit,
      auth: options.auth || false,
      cache: options.cache || false,
      timeout: options.timeout || 30000
    }

    this.routes.set(path, routeConfig)

    // Create rate limiter if specified
    if (options.rateLimit) {
      const rateLimiter = rateLimit({
        windowMs: options.rateLimit.windowMs || 60000,
        max: options.rateLimit.max || 100,
        message: `Rate limit exceeded for ${path}`
      })
      this.rateLimiters.set(path, rateLimiter)
    }

    // Set up proxy middleware
    const proxyMiddleware = httpProxy.createProxyMiddleware({
      target: 'http://placeholder', // Will be replaced dynamically
      changeOrigin: true,
      timeout: routeConfig.timeout,
      pathRewrite: options.pathRewrite || {},
      
      router: (req) => {
        try {
          const instance = this.serviceRegistry.getHealthyInstance(serviceName)
          return instance.url
        } catch (error) {
          logger.error(`Route resolution failed for ${serviceName}:`, error)
          return null
        }
      },

      onProxyReq: (proxyReq, req, res) => {
        // Add headers
        proxyReq.setHeader('X-Gateway-Route', path)
        proxyReq.setHeader('X-Request-ID', req.headers['x-request-id'] || this.generateRequestId())
        
        logger.info(`🔄 Proxying ${req.method} ${req.path} to ${serviceName}`)
      },

      onProxyRes: (proxyRes, req, res) => {
        // Record success
        this.serviceRegistry.recordSuccess(serviceName)
        
        // Add response headers
        proxyRes.headers['X-Gateway-Service'] = serviceName
        proxyRes.headers['X-Gateway-Time'] = Date.now()
      },

      onError: (err, req, res) => {
        // Record failure
        this.serviceRegistry.recordFailure(serviceName)
        
        logger.error(`Proxy error for ${serviceName}:`, err)
        
        if (!res.headersSent) {
          res.status(503).json({
            error: 'Service temporarily unavailable',
            service: serviceName,
            requestId: req.headers['x-request-id']
          })
        }
      }
    })

    // Register the route with middleware
    const middlewares = []
    
    // Add rate limiting if configured
    if (this.rateLimiters.has(path)) {
      middlewares.push(this.rateLimiters.get(path))
    }

    // Add authentication if required
    if (routeConfig.auth) {
      middlewares.push(this.authMiddleware)
    }

    // Add the proxy middleware
    middlewares.push(proxyMiddleware)

    // Register for each method
    routeConfig.methods.forEach(method => {
      this.app[method.toLowerCase()](path, ...middlewares)
    })

    logger.info(`🛣️ Route registered: ${routeConfig.methods.join(',')} ${path} -> ${serviceName}`)
  }

  // Authentication middleware
  authMiddleware(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' })
    }

    // Validate token (implement JWT validation)
    try {
      // Add your JWT validation logic here
      next()
    } catch (error) {
      res.status(401).json({ error: 'Invalid token' })
    }
  }

  // Register service with gateway
  registerService(serviceName, instances, options = {}) {
    this.serviceRegistry.registerService(serviceName, instances, options)
  }

  // Generate unique request ID
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Start the gateway
  start(port = 8000) {
    this.app.listen(port, () => {
      logger.info(`🌐 API Gateway started on port ${port}`)
      
      // Register default services
      this.registerDefaultServices()
    })
  }

  // Register default NeuroColony services
  registerDefaultServices() {
    // AI Service
    this.registerService('ai-service', [
      { url: 'http://localhost:5001', weight: 1 }
    ], {
      healthCheckInterval: 30000,
      errorThreshold: 3,
      loadBalancer: 'round-robin'
    })

    // User Service
    this.registerService('user-service', [
      { url: 'http://localhost:5002', weight: 1 }
    ])

    // Billing Service
    this.registerService('billing-service', [
      { url: 'http://localhost:5003', weight: 1 }
    ])

    // Analytics Service
    this.registerService('analytics-service', [
      { url: 'http://localhost:5004', weight: 1 }
    ])

    // Register routes
    this.registerRoute('/api/ai/*', 'ai-service', {
      methods: ['POST', 'GET'],
      rateLimit: { max: 50, windowMs: 60000 },
      auth: true
    })

    this.registerRoute('/api/users/*', 'user-service', {
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      auth: true
    })

    this.registerRoute('/api/billing/*', 'billing-service', {
      methods: ['GET', 'POST'],
      auth: true
    })

    this.registerRoute('/api/analytics/*', 'analytics-service', {
      methods: ['GET'],
      auth: true
    })

    logger.info('🔗 Default services and routes registered')
  }

  // Graceful shutdown
  async shutdown() {
    logger.info('🛑 Shutting down API Gateway...')
    this.serviceRegistry.destroy()
    
    // Additional cleanup logic here
    
    logger.info('✅ API Gateway shutdown complete')
  }
}

export { APIGateway, ServiceRegistry }