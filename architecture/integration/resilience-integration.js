/**
 * 🔗 NeuroColony Resilience Integration
 * Planetary Scale Architecture - Phase 3 Implementation
 * 
 * Integrates the complete resilience system with existing NeuroColony backend
 * providing seamless fault tolerance and high availability features.
 */

import { resilienceEngine, createResilienceMiddleware } from '../resilience/index.js'
import { logger } from '../../backend/utils/logger.js'
import express from 'express'

/**
 * 🏗️ NeuroColony Resilience Integration Service
 */
export class NeuroColonyResilienceIntegration {
  constructor(app, config = {}) {
    this.app = app
    this.config = {
      enableApiProtection: config.enableApiProtection !== false,
      enableHealthEndpoints: config.enableHealthEndpoints !== false,
      enableMetricsEndpoints: config.enableMetricsEndpoints !== false,
      enableAdminEndpoints: config.enableAdminEndpoints !== false,
      adminBasePath: config.adminBasePath || '/admin/resilience',
      metricsBasePath: config.metricsBasePath || '/metrics',
      ...config
    }
    
    this.initialized = false
    this.startupTime = Date.now()
  }
  
  /**
   * Initialize resilience integration with NeuroColony
   */
  async initialize() {
    logger.info('🔗 Initializing NeuroColony Resilience Integration')
    
    try {
      // Initialize the resilience engine
      await resilienceEngine.initialize()
      
      // Setup middleware integration
      this.setupMiddleware()
      
      // Setup health endpoints
      if (this.config.enableHealthEndpoints) {
        this.setupHealthEndpoints()
      }
      
      // Setup metrics endpoints
      if (this.config.enableMetricsEndpoints) {
        this.setupMetricsEndpoints()
      }
      
      // Setup admin endpoints
      if (this.config.enableAdminEndpoints) {
        this.setupAdminEndpoints()
      }
      
      // Integrate with existing NeuroColony services
      await this.integrateWithNeuroColony()
      
      this.initialized = true
      
      logger.info('✅ NeuroColony Resilience Integration completed successfully')
      
      return {
        status: 'initialized',
        timestamp: Date.now(),
        components: resilienceEngine.getEnabledComponents()
      }
      
    } catch (error) {
      logger.error('❌ Failed to initialize resilience integration:', error)
      throw error
    }
  }
  
  /**
   * Setup Express middleware for API protection
   */
  setupMiddleware() {
    if (!this.config.enableApiProtection) return
    
    // Add resilience middleware before existing routes
    const resilienceMiddleware = createResilienceMiddleware({
      circuitBreaker: {
        failureThreshold: 5,
        recoveryTimeout: 30000
      },
      healthCheckPath: '/health'
    })
    
    this.app.use(resilienceMiddleware)
    
    // Add request tracking middleware
    this.app.use(this.createRequestTrackingMiddleware())
    
    // Add graceful degradation middleware
    this.app.use(this.createGracefulDegradationMiddleware())
    
    logger.info('🛡️ Resilience middleware integrated')
  }
  
  /**
   * Setup health check endpoints
   */
  setupHealthEndpoints() {
    // Basic health check
    this.app.get('/health', (req, res) => {
      const health = resilienceEngine.healthCheck.getStatus()
      const statusCode = health.status === 'healthy' ? 200 :
                        health.status === 'degraded' ? 200 : 503
      
      res.status(statusCode).json({
        status: health.status,
        timestamp: Date.now(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      })
    })
    
    // Detailed health check
    this.app.get('/health/detailed', (req, res) => {
      const detailedHealth = resilienceEngine.healthCheck.getDetailedReport()
      res.json(detailedHealth)
    })
    
    // Readiness probe (for Kubernetes)
    this.app.get('/health/ready', (req, res) => {
      const systemStatus = resilienceEngine.getSystemStatus()
      const isReady = systemStatus.overall !== 'critical' && this.initialized
      
      res.status(isReady ? 200 : 503).json({
        ready: isReady,
        status: systemStatus.overall,
        initialized: this.initialized
      })
    })
    
    // Liveness probe (for Kubernetes)
    this.app.get('/health/live', (req, res) => {
      // Simple liveness check - if we can respond, we're alive
      res.json({
        live: true,
        timestamp: Date.now(),
        pid: process.pid
      })
    })
    
    logger.info('🏥 Health check endpoints configured')
  }
  
  /**
   * Setup metrics endpoints
   */
  setupMetricsEndpoints() {
    const metricsRouter = express.Router()
    
    // System metrics
    metricsRouter.get('/system', (req, res) => {
      const systemStatus = resilienceEngine.getSystemStatus()
      res.json({
        ...systemStatus,
        process: {
          pid: process.pid,
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage()
        }
      })
    })
    
    // Circuit breaker metrics
    metricsRouter.get('/circuit-breakers', (req, res) => {
      const statuses = resilienceEngine.circuitBreaker?.getAllStatuses() || {}
      res.json(statuses)
    })
    
    // Health check metrics
    metricsRouter.get('/health-checks', (req, res) => {
      const health = resilienceEngine.healthCheck?.getDetailedReport() || {}
      res.json(health)
    })
    
    // Graceful degradation metrics
    metricsRouter.get('/degradation', (req, res) => {
      const degradation = resilienceEngine.gracefulDegradation?.getStatus() || {}
      res.json(degradation)
    })
    
    // Disaster recovery metrics
    metricsRouter.get('/disaster-recovery', (req, res) => {
      const dr = resilienceEngine.disasterRecovery?.getStatus() || {}
      res.json(dr)
    })
    
    this.app.use(this.config.metricsBasePath, metricsRouter)
    
    logger.info(`📊 Metrics endpoints configured at ${this.config.metricsBasePath}`)
  }
  
  /**
   * Setup admin endpoints for resilience management
   */
  setupAdminEndpoints() {
    const adminRouter = express.Router()
    
    // Admin authentication middleware (basic implementation)
    adminRouter.use(this.createAdminAuthMiddleware())
    
    // Force circuit breaker open/close
    adminRouter.post('/circuit-breaker/:service/:action', async (req, res) => {
      const { service, action } = req.params
      const breaker = resilienceEngine.circuitBreaker?.getBreaker(service)
      
      if (!breaker) {
        return res.status(404).json({ error: 'Circuit breaker not found' })
      }
      
      try {
        if (action === 'open') {
          breaker.forceOpen(req.body.duration || 60000)
        } else if (action === 'reset') {
          breaker.reset()
        } else {
          return res.status(400).json({ error: 'Invalid action' })
        }
        
        res.json({ success: true, service, action })
      } catch (error) {
        res.status(500).json({ error: error.message })
      }
    })
    
    // Force graceful degradation
    adminRouter.post('/degradation/:level', async (req, res) => {
      const { level } = req.params
      
      try {
        if (level === 'recover') {
          await resilienceEngine.gracefulDegradation?.forceRecovery()
        } else {
          await resilienceEngine.gracefulDegradation?.forceDegradation(level, 'admin_forced')
        }
        
        res.json({ success: true, level })
      } catch (error) {
        res.status(500).json({ error: error.message })
      }
    })
    
    // Execute disaster recovery
    adminRouter.post('/disaster-recovery/:scenario', async (req, res) => {
      const { scenario } = req.params
      
      try {
        const result = await resilienceEngine.disasterRecovery?.executeRecovery(scenario, {
          approved: true,
          ...req.body
        })
        
        res.json({ success: true, result })
      } catch (error) {
        res.status(500).json({ error: error.message })
      }
    })
    
    // Execute backup
    adminRouter.post('/backup/:strategy', async (req, res) => {
      const { strategy } = req.params
      
      try {
        const result = await resilienceEngine.disasterRecovery?.executeBackup(strategy, req.body)
        res.json({ success: true, result })
      } catch (error) {
        res.status(500).json({ error: error.message })
      }
    })
    
    // Toggle health check
    adminRouter.post('/health-check/:name/:action', (req, res) => {
      const { name, action } = req.params
      const enabled = action === 'enable'
      
      const success = resilienceEngine.healthCheck?.toggleCheck(name, enabled) || false
      
      if (success) {
        res.json({ success: true, name, enabled })
      } else {
        res.status(404).json({ error: 'Health check not found' })
      }
    })
    
    this.app.use(this.config.adminBasePath, adminRouter)
    
    logger.info(`🔧 Admin endpoints configured at ${this.config.adminBasePath}`)
  }
  
  /**
   * Integrate with existing NeuroColony services
   */
  async integrateWithNeuroColony() {
    // Wrap AI generation service with circuit breaker
    this.wrapAIGenerationService()
    
    // Add health checks for NeuroColony-specific functionality
    this.addNeuroColonyHealthChecks()
    
    // Register NeuroColony features for graceful degradation
    this.registerNeuroColonyFeatures()
    
    // Setup disaster recovery for NeuroColony data
    this.setupNeuroColonyDisasterRecovery()
    
    logger.info('🔗 NeuroColony services integrated with resilience system')
  }
  
  /**
   * Wrap AI generation service with circuit breaker protection
   */
  wrapAIGenerationService() {
    const originalAIService = this.findAIService()
    if (!originalAIService) return
    
    // Wrap with circuit breaker
    const aiCircuitBreaker = resilienceEngine.circuitBreaker?.getBreaker('ai_generation')
    
    if (aiCircuitBreaker) {
      // Override AI generation methods with circuit breaker protection
      this.wrapMethodWithCircuitBreaker(originalAIService, 'generateAdvancedEmailSequence', aiCircuitBreaker)
      this.wrapMethodWithCircuitBreaker(originalAIService, 'generateSequence', aiCircuitBreaker)
    }
  }
  
  /**
   * Add NeuroColony-specific health checks
   */
  addNeuroColonyHealthChecks() {
    // Email sequence generation health check
    resilienceEngine.healthCheck?.registerCheck('email_sequence_generation', async () => {
      const testSequence = await this.testEmailSequenceGeneration()
      return testSequence
    }, {
      interval: 300000, // 5 minutes
      critical: false,
      description: 'Email sequence generation functionality'
    })
    
    // User analytics health check
    resilienceEngine.healthCheck?.registerCheck('user_analytics', async () => {
      const testAnalytics = await this.testUserAnalytics()
      return testAnalytics
    }, {
      interval: 180000, // 3 minutes
      critical: false,
      description: 'User analytics and dashboard functionality'
    })
    
    // Billing system health check
    resilienceEngine.healthCheck?.registerCheck('billing_system', async () => {
      const testBilling = await this.testBillingSystem()
      return testBilling
    }, {
      interval: 600000, // 10 minutes
      critical: true,
      description: 'Billing and subscription management'
    })
  }
  
  /**
   * Register NeuroColony features for graceful degradation
   */
  registerNeuroColonyFeatures() {
    const features = [
      {
        name: 'advanced_ai_generation',
        priority: 9,
        category: 'core',
        services: ['openai', 'anthropic'],
        userImpact: 'high',
        fallback: this.getBasicSequenceTemplate
      },
      {
        name: 'real_time_analytics',
        priority: 6,
        category: 'enhanced',
        userImpact: 'medium',
        fallback: this.getCachedAnalytics
      },
      {
        name: 'advanced_personalization',
        priority: 4,
        category: 'enhanced',
        userImpact: 'low'
      },
      {
        name: 'social_sharing',
        priority: 2,
        category: 'experimental',
        userImpact: 'low'
      }
    ]
    
    features.forEach(feature => {
      resilienceEngine.gracefulDegradation?.registerFeature(feature.name, feature)
    })
  }
  
  /**
   * Setup disaster recovery for NeuroColony data
   */
  setupNeuroColonyDisasterRecovery() {
    // User data backup strategy
    resilienceEngine.disasterRecovery?.registerBackupStrategy('sequenceai_user_data', {
      type: 'incremental',
      frequency: 1800000, // 30 minutes
      targets: ['users', 'email_sequences', 'generated_content'],
      destinations: ['secondary', 'offsite'],
      encryptionEnabled: true
    })
    
    // Analytics data backup
    resilienceEngine.disasterRecovery?.registerBackupStrategy('sequenceai_analytics', {
      type: 'full',
      frequency: 21600000, // 6 hours
      targets: ['analytics_events', 'user_metrics', 'performance_data'],
      destinations: ['secondary', 'offsite']
    })
  }
  
  /**
   * Create request tracking middleware
   */
  createRequestTrackingMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now()
      
      // Track request metrics
      req.resilienceMetrics = {
        startTime,
        route: req.route?.path || req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }
      
      // Override res.end to capture response metrics
      const originalEnd = res.end
      res.end = function(...args) {
        const responseTime = Date.now() - startTime
        
        // Update performance metrics in graceful degradation
        if (resilienceEngine.gracefulDegradation) {
          // This would update real-time performance metrics
          // Implementation depends on your metrics collection system
        }
        
        // Log slow requests
        if (responseTime > 2000) {
          logger.warn('Slow request detected', {
            route: req.resilienceMetrics.route,
            method: req.resilienceMetrics.method,
            responseTime,
            statusCode: res.statusCode
          })
        }
        
        originalEnd.apply(this, args)
      }
      
      next()
    }
  }
  
  /**
   * Create graceful degradation middleware
   */
  createGracefulDegradationMiddleware() {
    return (req, res, next) => {
      // Check if graceful degradation is active
      const degradationStatus = resilienceEngine.gracefulDegradation?.getStatus()
      
      if (degradationStatus?.degradationActive) {
        // Add degradation headers
        res.set('X-Service-Mode', degradationStatus.currentLevel)
        res.set('X-Features-Available', degradationStatus.activeFeatures.join(','))
        
        // Add degradation info to request
        req.degradationInfo = {
          level: degradationStatus.currentLevel,
          activeFeatures: degradationStatus.activeFeatures,
          disabledFeatures: degradationStatus.disabledFeatures
        }
      }
      
      next()
    }
  }
  
  /**
   * Create admin authentication middleware
   */
  createAdminAuthMiddleware() {
    return (req, res, next) => {
      // Basic authentication for admin endpoints
      // In production, this should use proper authentication
      const auth = req.headers.authorization
      
      if (!auth || !auth.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' })
      }
      
      const token = auth.substring(7)
      const adminToken = process.env.ADMIN_TOKEN || 'admin-dev-token'
      
      if (token !== adminToken) {
        return res.status(403).json({ error: 'Invalid admin token' })
      }
      
      next()
    }
  }
  
  /**
   * Helper methods
   */
  findAIService() {
    // This would find the actual AI service instance in your app
    // Implementation depends on how your services are structured
    return null
  }
  
  wrapMethodWithCircuitBreaker(service, methodName, circuitBreaker) {
    const originalMethod = service[methodName]
    if (!originalMethod) return
    
    service[methodName] = async function(...args) {
      return await circuitBreaker.execute(
        () => originalMethod.apply(this, args),
        // Fallback function
        (error) => {
          logger.warn(`Circuit breaker fallback triggered for ${methodName}`)
          return { error: 'Service temporarily unavailable', fallback: true }
        }
      )
    }
  }
  
  async testEmailSequenceGeneration() {
    // Simulate email sequence generation test
    await new Promise(resolve => setTimeout(resolve, 100))
    return { status: 'healthy', responseTime: 100 }
  }
  
  async testUserAnalytics() {
    // Simulate analytics test
    await new Promise(resolve => setTimeout(resolve, 50))
    return { status: 'healthy', responseTime: 50 }
  }
  
  async testBillingSystem() {
    // Simulate billing system test
    await new Promise(resolve => setTimeout(resolve, 200))
    return { status: 'healthy', responseTime: 200 }
  }
  
  getBasicSequenceTemplate(request) {
    return {
      emails: [
        {
          subject: 'Welcome!',
          body: 'Thank you for your interest.',
          dayDelay: 0
        }
      ],
      fallback: true
    }
  }
  
  getCachedAnalytics(request) {
    return {
      users: 1234,
      sequences: 567,
      conversionRate: 12.5,
      cached: true
    }
  }
  
  /**
   * Get integration status
   */
  getStatus() {
    return {
      initialized: this.initialized,
      startupTime: this.startupTime,
      uptime: Date.now() - this.startupTime,
      resilienceEngine: resilienceEngine.getSystemStatus(),
      config: this.config
    }
  }
}

/**
 * 🚀 Easy setup function for NeuroColony Express apps
 */
export async function setupNeuroColonyResilience(app, config = {}) {
  const integration = new NeuroColonyResilienceIntegration(app, config)
  await integration.initialize()
  return integration
}

export default NeuroColonyResilienceIntegration