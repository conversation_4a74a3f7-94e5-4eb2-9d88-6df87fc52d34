/**
 * 🎯 Application Layer - Commands and Command Handlers
 * CQRS pattern implementation for write operations
 */

import {
  Command,
  CommandHandler,
  DomainValidationError,
  InvariantViolationError,
  EntityNotFoundError,
  Validator,
  IdGenerator
} from '../domain-core.js'

import {
  EmailSequence,
  Email,
  EmailContent,
  BusinessInfo,
  SequenceSettings,
  EmailAddress,
  EmailSequenceCreatedEvent,
  SequencePublishedEvent
} from '../domains/email-sequence/entities.js'

import {
  EmailSequenceGenerationService,
  EmailContentOptimizationService,
  SequenceReadyToPublishSpecification
} from '../domains/email-sequence/services.js'

import { logger } from '../../backend/utils/logger.js'

// ========================================
// COMMANDS
// ========================================

/**
 * Create Email Sequence Command
 */
export class CreateEmailSequenceCommand extends Command {
  constructor({
    userEmail,
    businessName,
    industry,
    targetAudience,
    goals = '',
    tone = 'professional',
    additionalInfo = '',
    sequenceType = 'nurture',
    emailCount = 5,
    frequency = 'weekly',
    personalizeLevel = 'medium',
    includeCallToAction = true,
    ctaType = 'soft'
  }) {
    super({
      userEmail,
      businessName,
      industry,
      targetAudience,
      goals,
      tone,
      additionalInfo,
      sequenceType,
      emailCount,
      frequency,
      personalizeLevel,
      includeCallToAction,
      ctaType
    })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.userEmail, 'User email'),
      Validator.email(this.data.userEmail, 'User email'),
      Validator.required(this.data.businessName, 'Business name'),
      Validator.minLength(this.data.businessName, 2, 'Business name'),
      Validator.maxLength(this.data.businessName, 100, 'Business name'),
      Validator.required(this.data.industry, 'Industry'),
      Validator.required(this.data.targetAudience, 'Target audience'),
      Validator.minLength(this.data.targetAudience, 10, 'Target audience'),
      Validator.maxLength(this.data.targetAudience, 500, 'Target audience'),
      Validator.range(this.data.emailCount, 3, 10, 'Email count')
    ])
  }
}

/**
 * Update Email Content Command
 */
export class UpdateEmailContentCommand extends Command {
  constructor({
    sequenceId,
    emailPosition,
    subject,
    body,
    htmlBody = null,
    plainTextBody = null
  }) {
    super({
      sequenceId,
      emailPosition,
      subject,
      body,
      htmlBody,
      plainTextBody
    })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID'),
      Validator.required(this.data.emailPosition, 'Email position'),
      Validator.range(this.data.emailPosition, 1, 10, 'Email position'),
      Validator.required(this.data.subject, 'Subject'),
      Validator.minLength(this.data.subject, 5, 'Subject'),
      Validator.maxLength(this.data.subject, 200, 'Subject'),
      Validator.required(this.data.body, 'Body'),
      Validator.minLength(this.data.body, 50, 'Body'),
      Validator.maxLength(this.data.body, 10000, 'Body')
    ])
  }
}

/**
 * Publish Email Sequence Command
 */
export class PublishEmailSequenceCommand extends Command {
  constructor({ sequenceId }) {
    super({ sequenceId })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID')
    ])
  }
}

/**
 * Archive Email Sequence Command
 */
export class ArchiveEmailSequenceCommand extends Command {
  constructor({ sequenceId, reason = 'user_request' }) {
    super({ sequenceId, reason })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID')
    ])
  }
}

/**
 * Duplicate Email Sequence Command
 */
export class DuplicateEmailSequenceCommand extends Command {
  constructor({ sequenceId, newName = null }) {
    super({ sequenceId, newName })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID')
    ])
  }
}

/**
 * Update Email Performance Command
 */
export class UpdateEmailPerformanceCommand extends Command {
  constructor({
    sequenceId,
    emailPosition,
    sent = 0,
    delivered = 0,
    opened = 0,
    clicked = 0,
    bounced = 0,
    unsubscribed = 0
  }) {
    super({
      sequenceId,
      emailPosition,
      sent,
      delivered,
      opened,
      clicked,
      bounced,
      unsubscribed
    })
  }

  _validateCommand() {
    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID'),
      Validator.required(this.data.emailPosition, 'Email position'),
      Validator.range(this.data.emailPosition, 1, 10, 'Email position'),
      Validator.range(this.data.sent, 0, Number.MAX_SAFE_INTEGER, 'Sent count'),
      Validator.range(this.data.delivered, 0, this.data.sent, 'Delivered count'),
      Validator.range(this.data.opened, 0, this.data.delivered, 'Opened count'),
      Validator.range(this.data.clicked, 0, this.data.opened, 'Clicked count'),
      Validator.range(this.data.bounced, 0, this.data.sent, 'Bounced count'),
      Validator.range(this.data.unsubscribed, 0, this.data.delivered, 'Unsubscribed count')
    ])
  }
}

/**
 * Optimize Email Sequence Command
 */
export class OptimizeEmailSequenceCommand extends Command {
  constructor({ sequenceId, optimizationType = 'comprehensive' }) {
    super({ sequenceId, optimizationType })
  }

  _validateCommand() {
    const validTypes = ['subject_lines', 'content', 'cta', 'comprehensive']
    const typeValidation = validTypes.includes(this.data.optimizationType) 
      ? null 
      : 'Invalid optimization type'

    return Validator.validateAll([
      Validator.required(this.data.sequenceId, 'Sequence ID'),
      typeValidation
    ])
  }
}

// ========================================
// COMMAND HANDLERS
// ========================================

/**
 * Create Email Sequence Command Handler
 */
export class CreateEmailSequenceCommandHandler extends CommandHandler {
  constructor(
    emailSequenceRepository,
    generationService,
    eventPublisher,
    usageService = null
  ) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.generationService = generationService
    this.eventPublisher = eventPublisher
    this.usageService = usageService
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Creating email sequence', {
        commandId: command.commandId,
        userEmail: data.userEmail,
        businessName: data.businessName,
        sequenceType: data.sequenceType
      })

      // Check usage limits if service is available
      if (this.usageService) {
        const canGenerate = await this.usageService.canUserGenerate(data.userEmail)
        if (!canGenerate.allowed) {
          throw new DomainValidationError(`Generation not allowed: ${canGenerate.reason}`)
        }
      }

      // Create value objects
      const userEmail = new EmailAddress(data.userEmail)
      const businessInfo = new BusinessInfo({
        businessName: data.businessName,
        industry: data.industry,
        targetAudience: data.targetAudience,
        goals: data.goals,
        tone: data.tone,
        additionalInfo: data.additionalInfo
      })
      const settings = new SequenceSettings({
        sequenceType: data.sequenceType,
        emailCount: data.emailCount,
        frequency: data.frequency,
        personalizeLevel: data.personalizeLevel,
        includeCallToAction: data.includeCallToAction,
        ctaType: data.ctaType
      })

      // Generate email sequence using domain service
      const sequence = await this.generationService.generateSequence(
        userEmail,
        businessInfo,
        settings
      )

      // Save to repository
      await this.emailSequenceRepository.save(sequence)

      // Track usage if service is available
      if (this.usageService) {
        await this.usageService.recordGeneration(data.userEmail, {
          type: 'email_sequence',
          sequenceId: sequence.id,
          emailCount: data.emailCount
        })
      }

      logger.info('Email sequence created successfully', {
        commandId: command.commandId,
        sequenceId: sequence.id,
        emailCount: sequence.getEmailCount()
      })

      return {
        sequenceId: sequence.id,
        name: sequence.name,
        emailCount: sequence.getEmailCount(),
        estimatedDuration: sequence.getTotalEstimatedDuration(),
        status: sequence.status
      }

    } catch (error) {
      logger.error('Create email sequence command failed', {
        commandId: command.commandId,
        error: error.message,
        userEmail: data.userEmail
      })
      throw error
    }
  }

  async _publishDomainEvents() {
    // Domain events are published by the repository
  }
}

/**
 * Update Email Content Command Handler
 */
export class UpdateEmailContentCommandHandler extends CommandHandler {
  constructor(
    emailSequenceRepository,
    contentOptimizationService,
    eventPublisher
  ) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.contentOptimizationService = contentOptimizationService
    this.eventPublisher = eventPublisher
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Updating email content', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        emailPosition: data.emailPosition
      })

      // Load sequence
      const sequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Check if sequence can be modified
      if (sequence.status === 'published') {
        throw new InvariantViolationError('Cannot modify published sequence')
      }

      // Create new content
      const newContent = new EmailContent({
        subject: data.subject,
        body: data.body,
        htmlBody: data.htmlBody,
        plainTextBody: data.plainTextBody
      })

      // Optimize content
      const optimizedContent = await this.contentOptimizationService.optimize(newContent, {
        businessInfo: sequence.businessInfo,
        settings: sequence.settings,
        position: data.emailPosition
      })

      // Update email in sequence
      sequence.updateEmail(data.emailPosition, optimizedContent)

      // Save changes
      await this.emailSequenceRepository.save(sequence)

      logger.info('Email content updated successfully', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        emailPosition: data.emailPosition,
        wordCount: optimizedContent.metadata.wordCount
      })

      return {
        sequenceId: sequence.id,
        emailPosition: data.emailPosition,
        updatedContent: {
          subject: optimizedContent.subject,
          wordCount: optimizedContent.metadata.wordCount,
          readabilityScore: optimizedContent.getReadabilityScore(),
          hasCallToAction: optimizedContent.hasCallToAction()
        }
      }

    } catch (error) {
      logger.error('Update email content command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Publish Email Sequence Command Handler
 */
export class PublishEmailSequenceCommandHandler extends CommandHandler {
  constructor(emailSequenceRepository, eventPublisher) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.eventPublisher = eventPublisher
    this.readyToPublishSpec = new SequenceReadyToPublishSpecification()
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Publishing email sequence', {
        commandId: command.commandId,
        sequenceId: data.sequenceId
      })

      // Load sequence
      const sequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Check if sequence is ready to publish
      if (!this.readyToPublishSpec.isSatisfiedBy(sequence)) {
        throw new InvariantViolationError('Sequence is not ready to publish')
      }

      // Publish sequence
      sequence.publish()

      // Save changes
      await this.emailSequenceRepository.save(sequence)

      logger.info('Email sequence published successfully', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        emailCount: sequence.getEmailCount()
      })

      return {
        sequenceId: sequence.id,
        status: sequence.status,
        publishedAt: sequence.updatedAt,
        emailCount: sequence.getEmailCount()
      }

    } catch (error) {
      logger.error('Publish email sequence command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Archive Email Sequence Command Handler
 */
export class ArchiveEmailSequenceCommandHandler extends CommandHandler {
  constructor(emailSequenceRepository, eventPublisher) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.eventPublisher = eventPublisher
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Archiving email sequence', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        reason: data.reason
      })

      // Load sequence
      const sequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Archive sequence
      sequence.archive()

      // Save changes
      await this.emailSequenceRepository.save(sequence)

      logger.info('Email sequence archived successfully', {
        commandId: command.commandId,
        sequenceId: data.sequenceId
      })

      return {
        sequenceId: sequence.id,
        status: sequence.status,
        archivedAt: sequence.updatedAt
      }

    } catch (error) {
      logger.error('Archive email sequence command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Duplicate Email Sequence Command Handler
 */
export class DuplicateEmailSequenceCommandHandler extends CommandHandler {
  constructor(emailSequenceRepository, eventPublisher) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.eventPublisher = eventPublisher
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Duplicating email sequence', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        newName: data.newName
      })

      // Load original sequence
      const originalSequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Duplicate sequence
      const duplicateSequence = originalSequence.duplicate()

      // Update name if provided
      if (data.newName) {
        duplicateSequence._name = data.newName
      }

      // Save duplicate
      await this.emailSequenceRepository.save(duplicateSequence)

      logger.info('Email sequence duplicated successfully', {
        commandId: command.commandId,
        originalSequenceId: data.sequenceId,
        duplicateSequenceId: duplicateSequence.id
      })

      return {
        originalSequenceId: originalSequence.id,
        duplicateSequenceId: duplicateSequence.id,
        duplicateName: duplicateSequence.name,
        emailCount: duplicateSequence.getEmailCount()
      }

    } catch (error) {
      logger.error('Duplicate email sequence command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Update Email Performance Command Handler
 */
export class UpdateEmailPerformanceCommandHandler extends CommandHandler {
  constructor(emailSequenceRepository, eventPublisher) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.eventPublisher = eventPublisher
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.debug('Updating email performance', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        emailPosition: data.emailPosition,
        sent: data.sent,
        opened: data.opened
      })

      // Load sequence
      const sequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Get email
      const email = sequence.getEmailByPosition(data.emailPosition)
      if (!email) {
        throw new EntityNotFoundError('Email', `position ${data.emailPosition}`)
      }

      // Update performance
      email.updatePerformance({
        sent: data.sent,
        delivered: data.delivered,
        opened: data.opened,
        clicked: data.clicked,
        bounced: data.bounced,
        unsubscribed: data.unsubscribed,
        lastUpdated: new Date()
      })

      // Save changes
      await this.emailSequenceRepository.save(sequence)

      logger.debug('Email performance updated successfully', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        emailPosition: data.emailPosition,
        openRate: email.performance.getOpenRate(),
        clickRate: email.performance.getClickRate()
      })

      return {
        sequenceId: sequence.id,
        emailPosition: data.emailPosition,
        performance: {
          sent: email.performance.sent,
          delivered: email.performance.delivered,
          opened: email.performance.opened,
          clicked: email.performance.clicked,
          openRate: email.performance.getOpenRate(),
          clickRate: email.performance.getClickRate(),
          bounceRate: email.performance.getBounceRate(),
          unsubscribeRate: email.performance.getUnsubscribeRate()
        }
      }

    } catch (error) {
      logger.error('Update email performance command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Optimize Email Sequence Command Handler
 */
export class OptimizeEmailSequenceCommandHandler extends CommandHandler {
  constructor(
    emailSequenceRepository,
    contentOptimizationService,
    performanceAnalysisService,
    eventPublisher
  ) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.contentOptimizationService = contentOptimizationService
    this.performanceAnalysisService = performanceAnalysisService
    this.eventPublisher = eventPublisher
  }

  async _execute(command) {
    const { data } = command

    try {
      logger.info('Optimizing email sequence', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        optimizationType: data.optimizationType
      })

      // Load sequence
      const sequence = await this.emailSequenceRepository.findById(data.sequenceId)

      // Check if sequence can be optimized
      if (sequence.status !== 'published') {
        throw new InvariantViolationError('Can only optimize published sequences with performance data')
      }

      // Get performance analysis
      const analysis = await this.performanceAnalysisService.analyzeSequencePerformance(sequence)

      const optimizations = []

      // Apply optimizations based on type
      switch (data.optimizationType) {
        case 'subject_lines':
          await this._optimizeSubjectLines(sequence, analysis, optimizations)
          break
        case 'content':
          await this._optimizeContent(sequence, analysis, optimizations)
          break
        case 'cta':
          await this._optimizeCallToActions(sequence, analysis, optimizations)
          break
        case 'comprehensive':
          await this._optimizeSubjectLines(sequence, analysis, optimizations)
          await this._optimizeContent(sequence, analysis, optimizations)
          await this._optimizeCallToActions(sequence, analysis, optimizations)
          break
      }

      // Save changes if any optimizations were applied
      if (optimizations.length > 0) {
        await this.emailSequenceRepository.save(sequence)
      }

      logger.info('Email sequence optimization completed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        optimizationsApplied: optimizations.length
      })

      return {
        sequenceId: sequence.id,
        optimizationType: data.optimizationType,
        optimizationsApplied: optimizations,
        recommendations: analysis.recommendations,
        performanceImprovement: this._calculateExpectedImprovement(optimizations)
      }

    } catch (error) {
      logger.error('Optimize email sequence command failed', {
        commandId: command.commandId,
        sequenceId: data.sequenceId,
        error: error.message
      })
      throw error
    }
  }

  async _optimizeSubjectLines(sequence, analysis, optimizations) {
    // Find emails with low open rates
    const lowPerformingEmails = analysis.emailAnalysis
      .filter(email => email.performance.openRate < 15)

    for (const emailData of lowPerformingEmails) {
      const email = sequence.getEmailByPosition(emailData.position)
      if (!email) continue

      const currentSubject = email.content.subject
      let optimizedSubject = currentSubject

      // Apply subject line optimizations
      if (optimizedSubject.length > 50) {
        optimizedSubject = optimizedSubject.substring(0, 47) + '...'
      }

      if (!optimizedSubject.includes('{{firstName}}') && sequence.settings.personalizeLevel === 'high') {
        optimizedSubject = `{{firstName}}, ${optimizedSubject}`
      }

      if (optimizedSubject !== currentSubject) {
        const newContent = email.content.clone({ subject: optimizedSubject })
        email.updateContent(newContent)
        
        optimizations.push({
          type: 'subject_line',
          emailPosition: emailData.position,
          before: currentSubject,
          after: optimizedSubject,
          reason: 'Improved length and personalization'
        })
      }
    }
  }

  async _optimizeContent(sequence, analysis, optimizations) {
    // Find emails with low click rates
    const lowPerformingEmails = analysis.emailAnalysis
      .filter(email => email.performance.clickRate < 2)

    for (const emailData of lowPerformingEmails) {
      const email = sequence.getEmailByPosition(emailData.position)
      if (!email) continue

      // Optimize content using domain service
      const optimizedContent = await this.contentOptimizationService.optimize(email.content, {
        businessInfo: sequence.businessInfo,
        settings: sequence.settings,
        position: emailData.position
      })

      if (optimizedContent.body !== email.content.body) {
        email.updateContent(optimizedContent)
        
        optimizations.push({
          type: 'content',
          emailPosition: emailData.position,
          changes: 'Improved readability and call-to-action',
          wordCountBefore: email.content.metadata.wordCount,
          wordCountAfter: optimizedContent.metadata.wordCount
        })
      }
    }
  }

  async _optimizeCallToActions(sequence, analysis, optimizations) {
    // Find emails without strong CTAs
    const emailsNeedingCTA = analysis.emailAnalysis
      .filter(email => !email.hasCallToAction || email.performance.clickRate < 1)

    for (const emailData of emailsNeedingCTA) {
      const email = sequence.getEmailByPosition(emailData.position)
      if (!email) continue

      let body = email.content.body

      // Add stronger CTA if missing
      if (!email.content.hasCallToAction()) {
        const cta = this._generateStrongerCTA(sequence.settings.ctaType, emailData.position)
        body = `${body}\n\n${cta}`

        const newContent = email.content.clone({ body })
        email.updateContent(newContent)

        optimizations.push({
          type: 'call_to_action',
          emailPosition: emailData.position,
          change: 'Added stronger call-to-action',
          addedCTA: cta
        })
      }
    }
  }

  _generateStrongerCTA(currentCtaType, position) {
    const strongerCTAs = {
      1: "Ready to see how this can transform your business? Let's chat!",
      2: "Want to dive deeper? Schedule a free 15-minute consultation.",
      3: "Don't miss out – book your strategy session today!",
      4: "Limited spots available – claim your free consultation now!",
      5: "This week only – get started with a special bonus!"
    }

    return strongerCTAs[position] || "Take the next step – let's discuss your specific needs!"
  }

  _calculateExpectedImprovement(optimizations) {
    // Simplified calculation of expected performance improvement
    let expectedOpenRateImprovement = 0
    let expectedClickRateImprovement = 0

    optimizations.forEach(opt => {
      switch (opt.type) {
        case 'subject_line':
          expectedOpenRateImprovement += 5 // 5% improvement expected
          break
        case 'content':
          expectedClickRateImprovement += 2 // 2% improvement expected
          break
        case 'call_to_action':
          expectedClickRateImprovement += 3 // 3% improvement expected
          break
      }
    })

    return {
      openRateImprovement: Math.min(expectedOpenRateImprovement, 25), // Cap at 25%
      clickRateImprovement: Math.min(expectedClickRateImprovement, 15) // Cap at 15%
    }
  }
}