/**
 * 🔍 Application Layer - Queries and Query Handlers
 * CQRS pattern implementation for read operations
 */

import {
  Query,
  QueryHandler,
  EntityNotFoundError,
  DomainValidationError,
  Validator
} from '../domain-core.js'

import {
  EmailSequence,
  EmailAddress
} from '../domains/email-sequence/entities.js'

import {
  HighPerformingSequenceSpecification,
  SequenceNeedsOptimizationSpecification
} from '../domains/email-sequence/services.js'

import { logger } from '../../backend/utils/logger.js'

// ========================================
// QUERIES
// ========================================

/**
 * Get Email Sequence By ID Query
 */
export class GetEmailSequenceByIdQuery extends Query {
  constructor(sequenceId) {
    super({ sequenceId })
  }

  validate() {
    const errors = []
    
    if (!this.criteria.sequenceId) {
      errors.push('Sequence ID is required')
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid query', errors)
    }
  }
}

/**
 * Get User Sequences Query
 */
export class GetUserSequencesQuery extends Query {
  constructor({
    userEmail,
    status = null,
    limit = 50,
    offset = 0,
    sortBy = 'updatedAt',
    sortOrder = 'desc'
  }) {
    super({
      userEmail,
      status,
      limit,
      offset,
      sortBy,
      sortOrder
    })
  }

  validate() {
    const errors = Validator.validateAll([
      Validator.required(this.criteria.userEmail, 'User email'),
      Validator.email(this.criteria.userEmail, 'User email'),
      Validator.range(this.criteria.limit, 1, 100, 'Limit'),
      Validator.range(this.criteria.offset, 0, Number.MAX_SAFE_INTEGER, 'Offset')
    ])

    const validSortFields = ['name', 'createdAt', 'updatedAt', 'status']
    if (!validSortFields.includes(this.criteria.sortBy)) {
      errors.push('Invalid sort field')
    }

    const validSortOrders = ['asc', 'desc']
    if (!validSortOrders.includes(this.criteria.sortOrder)) {
      errors.push('Invalid sort order')
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid query', errors)
    }
  }
}

/**
 * Search Sequences Query
 */
export class SearchSequencesQuery extends Query {
  constructor({
    searchTerm,
    filters = {},
    limit = 50,
    offset = 0
  }) {
    super({
      searchTerm,
      filters,
      limit,
      offset
    })
  }

  validate() {
    const errors = Validator.validateAll([
      Validator.required(this.criteria.searchTerm, 'Search term'),
      Validator.minLength(this.criteria.searchTerm, 2, 'Search term'),
      Validator.maxLength(this.criteria.searchTerm, 100, 'Search term'),
      Validator.range(this.criteria.limit, 1, 100, 'Limit'),
      Validator.range(this.criteria.offset, 0, Number.MAX_SAFE_INTEGER, 'Offset')
    ])

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid search query', errors)
    }
  }
}

/**
 * Get Sequence Performance Query
 */
export class GetSequencePerformanceQuery extends Query {
  constructor({
    sequenceId,
    dateRange = null,
    includeEmailBreakdown = true,
    includeBenchmarkComparison = true
  }) {
    super({
      sequenceId,
      dateRange,
      includeEmailBreakdown,
      includeBenchmarkComparison
    })
  }

  validate() {
    const errors = []
    
    if (!this.criteria.sequenceId) {
      errors.push('Sequence ID is required')
    }

    if (this.criteria.dateRange) {
      if (!this.criteria.dateRange.start || !this.criteria.dateRange.end) {
        errors.push('Date range must include start and end dates')
      }
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid performance query', errors)
    }
  }
}

/**
 * Get High Performing Sequences Query
 */
export class GetHighPerformingSequencesQuery extends Query {
  constructor({
    industry = null,
    sequenceType = null,
    minOpenRate = 25,
    minClickRate = 3,
    limit = 20,
    timeFrame = '30days'
  }) {
    super({
      industry,
      sequenceType,
      minOpenRate,
      minClickRate,
      limit,
      timeFrame
    })
  }

  validate() {
    const errors = Validator.validateAll([
      Validator.range(this.criteria.minOpenRate, 0, 100, 'Minimum open rate'),
      Validator.range(this.criteria.minClickRate, 0, 100, 'Minimum click rate'),
      Validator.range(this.criteria.limit, 1, 100, 'Limit')
    ])

    const validTimeFrames = ['7days', '30days', '90days', '1year']
    if (!validTimeFrames.includes(this.criteria.timeFrame)) {
      errors.push('Invalid time frame')
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid high performing sequences query', errors)
    }
  }
}

/**
 * Get User Statistics Query
 */
export class GetUserStatisticsQuery extends Query {
  constructor({
    userEmail,
    timeFrame = '30days',
    includeComparison = true
  }) {
    super({
      userEmail,
      timeFrame,
      includeComparison
    })
  }

  validate() {
    const errors = Validator.validateAll([
      Validator.required(this.criteria.userEmail, 'User email'),
      Validator.email(this.criteria.userEmail, 'User email')
    ])

    const validTimeFrames = ['7days', '30days', '90days', '1year']
    if (!validTimeFrames.includes(this.criteria.timeFrame)) {
      errors.push('Invalid time frame')
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid user statistics query', errors)
    }
  }
}

/**
 * Get Sequence Templates Query
 */
export class GetSequenceTemplatesQuery extends Query {
  constructor({
    industry = null,
    sequenceType = null,
    complexity = null,
    limit = 50
  }) {
    super({
      industry,
      sequenceType,
      complexity,
      limit
    })
  }

  validate() {
    const errors = Validator.validateAll([
      Validator.range(this.criteria.limit, 1, 100, 'Limit')
    ])

    if (this.criteria.complexity) {
      const validComplexities = ['simple', 'intermediate', 'advanced']
      if (!validComplexities.includes(this.criteria.complexity)) {
        errors.push('Invalid complexity level')
      }
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid templates query', errors)
    }
  }
}

// ========================================
// QUERY HANDLERS
// ========================================

/**
 * Get Email Sequence By ID Query Handler
 */
export class GetEmailSequenceByIdQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
  }

  async _execute(query) {
    const { sequenceId } = query.criteria

    try {
      logger.debug('Retrieving email sequence', {
        queryId: query.queryId,
        sequenceId
      })

      const sequence = await this.emailSequenceRepository.findById(sequenceId)

      // Transform to read model
      return this._toReadModel(sequence)

    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        logger.warn('Email sequence not found', { 
          queryId: query.queryId, 
          sequenceId 
        })
        throw error
      }
      
      logger.error('Failed to retrieve email sequence', {
        queryId: query.queryId,
        sequenceId,
        error: error.message
      })
      throw error
    }
  }

  _toReadModel(sequence) {
    return {
      id: sequence.id,
      name: sequence.name,
      userEmail: sequence.userEmail.value,
      businessInfo: {
        businessName: sequence.businessInfo.businessName,
        industry: sequence.businessInfo.industry,
        targetAudience: sequence.businessInfo.targetAudience,
        goals: sequence.businessInfo.goals,
        tone: sequence.businessInfo.tone,
        additionalInfo: sequence.businessInfo.additionalInfo
      },
      settings: {
        sequenceType: sequence.settings.sequenceType,
        emailCount: sequence.settings.emailCount,
        frequency: sequence.settings.frequency,
        personalizeLevel: sequence.settings.personalizeLevel,
        includeCallToAction: sequence.settings.includeCallToAction,
        ctaType: sequence.settings.ctaType,
        totalDuration: sequence.settings.getTotalSequenceDuration()
      },
      emails: sequence.emails.map(email => ({
        id: email.id,
        position: email.sequencePosition,
        subject: email.content.subject,
        bodyPreview: email.content.body.substring(0, 200) + '...',
        wordCount: email.content.metadata.wordCount,
        readabilityScore: email.content.getReadabilityScore(),
        hasCallToAction: email.content.hasCallToAction(),
        scheduledDays: email.scheduledDays,
        isActive: email.isActive,
        performance: email.performance ? {
          sent: email.performance.sent,
          delivered: email.performance.delivered,
          opened: email.performance.opened,
          clicked: email.performance.clicked,
          openRate: email.performance.getOpenRate(),
          clickRate: email.performance.getClickRate(),
          isPerformingWell: email.performance.isPerformingWell(),
          needsOptimization: email.performance.needsOptimization()
        } : null
      })),
      status: sequence.status,
      metadata: sequence.metadata,
      performance: sequence.getOverallPerformance(),
      estimatedDuration: sequence.getTotalEstimatedDuration(),
      createdAt: sequence.createdAt,
      updatedAt: sequence.updatedAt
    }
  }
}

/**
 * Get User Sequences Query Handler
 */
export class GetUserSequencesQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
  }

  async _execute(query) {
    const { userEmail, status, limit, offset, sortBy, sortOrder } = query.criteria

    try {
      logger.debug('Retrieving user sequences', {
        queryId: query.queryId,
        userEmail,
        status,
        limit,
        offset
      })

      const emailAddress = new EmailAddress(userEmail)
      let sequences = await this.emailSequenceRepository.findByUserEmail(emailAddress)

      // Apply status filter if provided
      if (status) {
        sequences = sequences.filter(seq => seq.status === status)
      }

      // Apply sorting
      sequences.sort((a, b) => {
        const aValue = a[sortBy]
        const bValue = b[sortBy]
        
        if (sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1
        } else {
          return aValue > bValue ? 1 : -1
        }
      })

      // Apply pagination
      const paginatedSequences = sequences.slice(offset, offset + limit)
      const total = sequences.length

      return {
        sequences: paginatedSequences.map(seq => this._toListItemReadModel(seq)),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }

    } catch (error) {
      logger.error('Failed to retrieve user sequences', {
        queryId: query.queryId,
        userEmail,
        error: error.message
      })
      throw error
    }
  }

  _toListItemReadModel(sequence) {
    const performance = sequence.getOverallPerformance()
    
    return {
      id: sequence.id,
      name: sequence.name,
      businessName: sequence.businessInfo.businessName,
      industry: sequence.businessInfo.industry,
      sequenceType: sequence.settings.sequenceType,
      emailCount: sequence.getEmailCount(),
      status: sequence.status,
      performance: performance ? {
        averageOpenRate: performance.averageOpenRate,
        averageClickRate: performance.averageClickRate,
        totalEngagement: performance.totalEngagement
      } : null,
      estimatedDuration: sequence.getTotalEstimatedDuration(),
      createdAt: sequence.createdAt,
      updatedAt: sequence.updatedAt
    }
  }
}

/**
 * Search Sequences Query Handler
 */
export class SearchSequencesQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
  }

  async _execute(query) {
    const { searchTerm, filters, limit, offset } = query.criteria

    try {
      logger.debug('Searching sequences', {
        queryId: query.queryId,
        searchTerm,
        filters,
        limit,
        offset
      })

      const result = await this.emailSequenceRepository.searchSequences(
        searchTerm,
        { ...filters, limit, offset }
      )

      return {
        sequences: result.sequences.map(seq => this._toSearchResultReadModel(seq)),
        total: result.total,
        hasMore: result.hasMore,
        pagination: {
          limit,
          offset
        }
      }

    } catch (error) {
      logger.error('Failed to search sequences', {
        queryId: query.queryId,
        searchTerm,
        error: error.message
      })
      throw error
    }
  }

  _toSearchResultReadModel(sequence) {
    return {
      id: sequence.id,
      name: sequence.name,
      businessName: sequence.businessInfo.businessName,
      industry: sequence.businessInfo.industry,
      targetAudience: sequence.businessInfo.targetAudience,
      sequenceType: sequence.settings.sequenceType,
      emailCount: sequence.getEmailCount(),
      status: sequence.status,
      relevanceScore: this._calculateRelevanceScore(sequence),
      createdAt: sequence.createdAt,
      updatedAt: sequence.updatedAt
    }
  }

  _calculateRelevanceScore(sequence) {
    // Simplified relevance scoring - would be more sophisticated in practice
    let score = 1.0
    
    // Boost recently updated sequences
    const daysSinceUpdate = (Date.now() - sequence.updatedAt.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate < 7) score += 0.2
    if (daysSinceUpdate < 30) score += 0.1
    
    // Boost published sequences
    if (sequence.status === 'published') score += 0.3
    
    // Boost sequences with good performance
    const performance = sequence.getOverallPerformance()
    if (performance?.averageOpenRate > 25) score += 0.2
    if (performance?.averageClickRate > 3) score += 0.2
    
    return Math.round(score * 100) / 100
  }
}

/**
 * Get Sequence Performance Query Handler
 */
export class GetSequencePerformanceQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository, performanceAnalysisService) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.performanceAnalysisService = performanceAnalysisService
  }

  async _execute(query) {
    const { 
      sequenceId, 
      dateRange, 
      includeEmailBreakdown, 
      includeBenchmarkComparison 
    } = query.criteria

    try {
      logger.debug('Retrieving sequence performance', {
        queryId: query.queryId,
        sequenceId,
        includeEmailBreakdown,
        includeBenchmarkComparison
      })

      const sequence = await this.emailSequenceRepository.findById(sequenceId)
      
      // Get performance statistics
      const performanceStats = await this.emailSequenceRepository.getPerformanceStats(
        sequenceId, 
        dateRange
      )

      let analysis = null
      if (includeEmailBreakdown || includeBenchmarkComparison) {
        analysis = await this.performanceAnalysisService.analyzeSequencePerformance(sequence)
      }

      return {
        sequenceId: sequence.id,
        sequenceName: sequence.name,
        overall: performanceStats,
        emailBreakdown: includeEmailBreakdown ? analysis?.emailAnalysis : null,
        benchmarkComparison: includeBenchmarkComparison ? analysis?.benchmarkComparison : null,
        recommendations: analysis?.recommendations || [],
        trends: analysis?.trends,
        generatedAt: new Date()
      }

    } catch (error) {
      logger.error('Failed to retrieve sequence performance', {
        queryId: query.queryId,
        sequenceId,
        error: error.message
      })
      throw error
    }
  }
}

/**
 * Get High Performing Sequences Query Handler
 */
export class GetHighPerformingSequencesQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
    this.highPerformingSpec = new HighPerformingSequenceSpecification()
  }

  async _execute(query) {
    const { 
      industry, 
      sequenceType, 
      minOpenRate, 
      minClickRate, 
      limit,
      timeFrame 
    } = query.criteria

    try {
      logger.debug('Retrieving high performing sequences', {
        queryId: query.queryId,
        industry,
        sequenceType,
        minOpenRate,
        minClickRate,
        limit
      })

      // Update specification with custom thresholds
      this.highPerformingSpec.minOpenRate = minOpenRate
      this.highPerformingSpec.minClickRate = minClickRate

      const criteria = {
        industry,
        sequenceType,
        limit,
        timeFrame
      }

      const sequences = await this.emailSequenceRepository.findHighPerforming(criteria)

      // Apply domain specification filter
      const highPerformingSequences = sequences.filter(seq => 
        this.highPerformingSpec.isSatisfiedBy(seq)
      )

      return {
        sequences: highPerformingSequences.map(seq => this._toHighPerformingReadModel(seq)),
        criteria: {
          minOpenRate,
          minClickRate,
          industry,
          sequenceType,
          timeFrame
        },
        total: highPerformingSequences.length,
        averageMetrics: this._calculateAverageMetrics(highPerformingSequences)
      }

    } catch (error) {
      logger.error('Failed to retrieve high performing sequences', {
        queryId: query.queryId,
        error: error.message
      })
      throw error
    }
  }

  _toHighPerformingReadModel(sequence) {
    const performance = sequence.getOverallPerformance()
    
    return {
      id: sequence.id,
      name: sequence.name,
      businessName: sequence.businessInfo.businessName,
      industry: sequence.businessInfo.industry,
      sequenceType: sequence.settings.sequenceType,
      emailCount: sequence.getEmailCount(),
      performance: {
        openRate: performance.averageOpenRate,
        clickRate: performance.averageClickRate,
        engagement: performance.totalEngagement
      },
      keyFactors: this._identifySuccessFactors(sequence),
      createdAt: sequence.createdAt
    }
  }

  _identifySuccessFactors(sequence) {
    const factors = []
    
    if (sequence.settings.personalizeLevel === 'high') {
      factors.push('High personalization')
    }
    
    if (sequence.settings.frequency === 'weekly') {
      factors.push('Optimal frequency')
    }
    
    if (sequence.businessInfo.hasAdvancedInfo()) {
      factors.push('Detailed business context')
    }
    
    const avgWordCount = sequence.emails.reduce((sum, email) => 
      sum + email.content.metadata.wordCount, 0) / sequence.emails.length
    
    if (avgWordCount < 300) {
      factors.push('Concise content')
    }
    
    return factors
  }

  _calculateAverageMetrics(sequences) {
    if (sequences.length === 0) return null
    
    const totalMetrics = sequences.reduce((acc, seq) => {
      const perf = seq.getOverallPerformance()
      return {
        openRate: acc.openRate + (perf?.averageOpenRate || 0),
        clickRate: acc.clickRate + (perf?.averageClickRate || 0),
        engagement: acc.engagement + (perf?.totalEngagement || 0)
      }
    }, { openRate: 0, clickRate: 0, engagement: 0 })
    
    return {
      averageOpenRate: totalMetrics.openRate / sequences.length,
      averageClickRate: totalMetrics.clickRate / sequences.length,
      averageEngagement: totalMetrics.engagement / sequences.length
    }
  }
}

/**
 * Get User Statistics Query Handler
 */
export class GetUserStatisticsQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
  }

  async _execute(query) {
    const { userEmail, timeFrame, includeComparison } = query.criteria

    try {
      logger.debug('Retrieving user statistics', {
        queryId: query.queryId,
        userEmail,
        timeFrame
      })

      const emailAddress = new EmailAddress(userEmail)
      const sequences = await this.emailSequenceRepository.findByUserEmail(emailAddress)

      // Filter by time frame
      const filteredSequences = this._filterByTimeFrame(sequences, timeFrame)

      const stats = {
        timeFrame,
        totalSequences: filteredSequences.length,
        sequencesByStatus: this._groupByStatus(filteredSequences),
        sequencesByType: this._groupByType(filteredSequences),
        sequencesByIndustry: this._groupByIndustry(filteredSequences),
        performance: this._calculateUserPerformance(filteredSequences),
        growth: this._calculateGrowthMetrics(sequences, timeFrame),
        topPerforming: this._getTopPerformingSequences(filteredSequences, 3)
      }

      if (includeComparison) {
        stats.benchmarkComparison = await this._getBenchmarkComparison(filteredSequences)
      }

      return stats

    } catch (error) {
      logger.error('Failed to retrieve user statistics', {
        queryId: query.queryId,
        userEmail,
        error: error.message
      })
      throw error
    }
  }

  _filterByTimeFrame(sequences, timeFrame) {
    const now = new Date()
    const cutoffDate = new Date()
    
    switch (timeFrame) {
      case '7days':
        cutoffDate.setDate(now.getDate() - 7)
        break
      case '30days':
        cutoffDate.setDate(now.getDate() - 30)
        break
      case '90days':
        cutoffDate.setDate(now.getDate() - 90)
        break
      case '1year':
        cutoffDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        cutoffDate.setDate(now.getDate() - 30)
    }
    
    return sequences.filter(seq => seq.createdAt >= cutoffDate)
  }

  _groupByStatus(sequences) {
    return sequences.reduce((acc, seq) => {
      acc[seq.status] = (acc[seq.status] || 0) + 1
      return acc
    }, {})
  }

  _groupByType(sequences) {
    return sequences.reduce((acc, seq) => {
      const type = seq.settings.sequenceType
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {})
  }

  _groupByIndustry(sequences) {
    return sequences.reduce((acc, seq) => {
      const industry = seq.businessInfo.industry
      acc[industry] = (acc[industry] || 0) + 1
      return acc
    }, {})
  }

  _calculateUserPerformance(sequences) {
    const publishedSequences = sequences.filter(seq => seq.status === 'published')
    
    if (publishedSequences.length === 0) {
      return {
        averageOpenRate: 0,
        averageClickRate: 0,
        totalEngagement: 0,
        sequencesWithData: 0
      }
    }

    const performances = publishedSequences
      .map(seq => seq.getOverallPerformance())
      .filter(perf => perf !== null)

    if (performances.length === 0) {
      return {
        averageOpenRate: 0,
        averageClickRate: 0,
        totalEngagement: 0,
        sequencesWithData: 0
      }
    }

    return {
      averageOpenRate: performances.reduce((sum, p) => sum + p.averageOpenRate, 0) / performances.length,
      averageClickRate: performances.reduce((sum, p) => sum + p.averageClickRate, 0) / performances.length,
      totalEngagement: performances.reduce((sum, p) => sum + p.totalEngagement, 0),
      sequencesWithData: performances.length
    }
  }

  _calculateGrowthMetrics(allSequences, timeFrame) {
    const now = new Date()
    const currentPeriod = this._filterByTimeFrame(allSequences, timeFrame)
    
    // Calculate previous period for comparison
    const previousPeriodStart = new Date()
    const previousPeriodEnd = new Date()
    
    switch (timeFrame) {
      case '7days':
        previousPeriodStart.setDate(now.getDate() - 14)
        previousPeriodEnd.setDate(now.getDate() - 7)
        break
      case '30days':
        previousPeriodStart.setDate(now.getDate() - 60)
        previousPeriodEnd.setDate(now.getDate() - 30)
        break
      case '90days':
        previousPeriodStart.setDate(now.getDate() - 180)
        previousPeriodEnd.setDate(now.getDate() - 90)
        break
      case '1year':
        previousPeriodStart.setFullYear(now.getFullYear() - 2)
        previousPeriodEnd.setFullYear(now.getFullYear() - 1)
        break
    }

    const previousPeriod = allSequences.filter(seq => 
      seq.createdAt >= previousPeriodStart && seq.createdAt < previousPeriodEnd
    )

    const currentCount = currentPeriod.length
    const previousCount = previousPeriod.length
    const growthRate = previousCount > 0 
      ? ((currentCount - previousCount) / previousCount) * 100 
      : currentCount > 0 ? 100 : 0

    return {
      currentPeriod: currentCount,
      previousPeriod: previousCount,
      growthRate: Math.round(growthRate * 10) / 10,
      trend: growthRate > 5 ? 'growing' : growthRate < -5 ? 'declining' : 'stable'
    }
  }

  _getTopPerformingSequences(sequences, limit) {
    return sequences
      .filter(seq => seq.getOverallPerformance() !== null)
      .sort((a, b) => {
        const aPerf = a.getOverallPerformance()
        const bPerf = b.getOverallPerformance()
        return (bPerf.averageOpenRate + bPerf.averageClickRate) - 
               (aPerf.averageOpenRate + aPerf.averageClickRate)
      })
      .slice(0, limit)
      .map(seq => ({
        id: seq.id,
        name: seq.name,
        performance: seq.getOverallPerformance()
      }))
  }

  async _getBenchmarkComparison(sequences) {
    // This would compare user performance to industry benchmarks
    // Simplified implementation
    const userPerf = this._calculateUserPerformance(sequences)
    
    return {
      industry: 'general',
      userOpenRate: userPerf.averageOpenRate,
      benchmarkOpenRate: 22,
      userClickRate: userPerf.averageClickRate,
      benchmarkClickRate: 3,
      userRanking: userPerf.averageOpenRate > 22 ? 'above_average' : 'below_average'
    }
  }
}

/**
 * Get Sequence Templates Query Handler
 */
export class GetSequenceTemplatesQueryHandler extends QueryHandler {
  constructor(emailSequenceRepository) {
    super()
    this.emailSequenceRepository = emailSequenceRepository
  }

  async _execute(query) {
    const { industry, sequenceType, complexity, limit } = query.criteria

    try {
      logger.debug('Retrieving sequence templates', {
        queryId: query.queryId,
        industry,
        sequenceType,
        complexity,
        limit
      })

      // Find high-performing sequences to use as templates
      const highPerformingSequences = await this.emailSequenceRepository.findHighPerforming({
        industry,
        sequenceType,
        limit: limit * 2 // Get more to filter by complexity
      })

      // Filter by complexity if specified
      let filteredSequences = highPerformingSequences
      if (complexity) {
        filteredSequences = this._filterByComplexity(highPerformingSequences, complexity)
      }

      // Take only the requested limit
      const templates = filteredSequences.slice(0, limit)

      return {
        templates: templates.map(seq => this._toTemplateReadModel(seq)),
        criteria: { industry, sequenceType, complexity },
        total: templates.length
      }

    } catch (error) {
      logger.error('Failed to retrieve sequence templates', {
        queryId: query.queryId,
        error: error.message
      })
      throw error
    }
  }

  _filterByComplexity(sequences, complexity) {
    return sequences.filter(seq => {
      const emailCount = seq.getEmailCount()
      const hasAdvancedInfo = seq.businessInfo.hasAdvancedInfo()
      const isHighPersonalization = seq.settings.personalizeLevel === 'high'
      
      switch (complexity) {
        case 'simple':
          return emailCount <= 3 && !hasAdvancedInfo && !isHighPersonalization
        case 'intermediate':
          return emailCount >= 4 && emailCount <= 6
        case 'advanced':
          return emailCount >= 7 || (hasAdvancedInfo && isHighPersonalization)
        default:
          return true
      }
    })
  }

  _toTemplateReadModel(sequence) {
    const performance = sequence.getOverallPerformance()
    
    return {
      id: sequence.id,
      name: sequence.name,
      description: this._generateTemplateDescription(sequence),
      industry: sequence.businessInfo.industry,
      sequenceType: sequence.settings.sequenceType,
      emailCount: sequence.getEmailCount(),
      complexity: this._determineComplexity(sequence),
      performance: performance ? {
        openRate: performance.averageOpenRate,
        clickRate: performance.averageClickRate
      } : null,
      features: this._extractTemplateFeatures(sequence),
      estimatedSetupTime: this._estimateSetupTime(sequence),
      tags: this._generateTemplateTags(sequence)
    }
  }

  _generateTemplateDescription(sequence) {
    const { businessName, industry, targetAudience } = sequence.businessInfo
    const { sequenceType, emailCount } = sequence.settings
    
    return `${emailCount}-email ${sequenceType} sequence for ${industry} businesses targeting ${targetAudience}. Based on ${businessName}'s successful campaign.`
  }

  _determineComplexity(sequence) {
    const emailCount = sequence.getEmailCount()
    const hasAdvancedInfo = sequence.businessInfo.hasAdvancedInfo()
    const isHighPersonalization = sequence.settings.personalizeLevel === 'high'
    
    if (emailCount <= 3 && !hasAdvancedInfo && !isHighPersonalization) {
      return 'simple'
    } else if (emailCount >= 7 || (hasAdvancedInfo && isHighPersonalization)) {
      return 'advanced'
    } else {
      return 'intermediate'
    }
  }

  _extractTemplateFeatures(sequence) {
    const features = []
    
    if (sequence.settings.personalizeLevel === 'high') {
      features.push('High personalization')
    }
    
    if (sequence.settings.includeCallToAction) {
      features.push('Strong call-to-actions')
    }
    
    if (sequence.businessInfo.hasAdvancedInfo()) {
      features.push('Industry-specific content')
    }
    
    const avgReadabilityScore = this._getAverageReadabilityScore(sequence)
    if (avgReadabilityScore === 'easy') {
      features.push('Easy to read')
    }
    
    return features
  }

  _getAverageReadabilityScore(sequence) {
    const scores = sequence.emails.map(email => email.content.getReadabilityScore())
    const easyCount = scores.filter(score => score === 'easy').length
    
    return easyCount > scores.length / 2 ? 'easy' : 'moderate'
  }

  _estimateSetupTime(sequence) {
    const complexity = this._determineComplexity(sequence)
    const setupTimes = {
      'simple': '15-30 minutes',
      'intermediate': '30-60 minutes',
      'advanced': '1-2 hours'
    }
    
    return setupTimes[complexity] || '30-60 minutes'
  }

  _generateTemplateTags(sequence) {
    const tags = []
    
    tags.push(sequence.businessInfo.industry)
    tags.push(sequence.settings.sequenceType)
    tags.push(`${sequence.getEmailCount()}-emails`)
    
    if (sequence.settings.personalizeLevel === 'high') {
      tags.push('personalized')
    }
    
    const performance = sequence.getOverallPerformance()
    if (performance && performance.averageOpenRate > 30) {
      tags.push('high-open-rate')
    }
    
    if (performance && performance.averageClickRate > 5) {
      tags.push('high-click-rate')
    }
    
    return tags
  }
}