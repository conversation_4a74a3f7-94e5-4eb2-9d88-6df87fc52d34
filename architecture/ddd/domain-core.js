/**
 * 🏛️ Domain-Driven Design Core Infrastructure
 * Base classes and patterns for clean architecture implementation
 */

import { logger } from '../../backend/utils/logger.js'
import { eventBus } from '../modern/event-system.js'

// ========================================
// DOMAIN CORE ABSTRACTIONS
// ========================================

/**
 * Base Entity class - Domain objects with identity
 */
export class Entity {
  constructor(id) {
    if (!id) {
      throw new Error('Entity must have an ID')
    }
    this._id = id
    this._domainEvents = []
    this._createdAt = new Date()
    this._updatedAt = new Date()
  }

  get id() {
    return this._id
  }

  get createdAt() {
    return this._createdAt
  }

  get updatedAt() {
    return this._updatedAt
  }

  // Domain event handling
  addDomainEvent(event) {
    this._domainEvents.push(event)
  }

  getDomainEvents() {
    return [...this._domainEvents]
  }

  clearDomainEvents() {
    this._domainEvents = []
  }

  // Equality based on ID
  equals(other) {
    if (!other || !other.id) return false
    return this._id === other.id
  }

  // Track modifications
  markAsUpdated() {
    this._updatedAt = new Date()
  }

  // Validation
  validate() {
    const errors = this._validateEntity()
    if (errors.length > 0) {
      throw new DomainValidationError('Entity validation failed', errors)
    }
  }

  _validateEntity() {
    // Override in subclasses
    return []
  }
}

/**
 * Base Value Object class - Immutable domain concepts
 */
export class ValueObject {
  constructor(props) {
    this._props = Object.freeze({ ...props })
  }

  get props() {
    return this._props
  }

  // Equality based on properties
  equals(other) {
    if (!other || !other._props) return false
    return this._deepEquals(this._props, other._props)
  }

  _deepEquals(obj1, obj2) {
    if (obj1 === obj2) return true
    if (!obj1 || !obj2) return false
    
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)
    
    if (keys1.length !== keys2.length) return false
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false
      if (obj1[key] !== obj2[key]) return false
    }
    
    return true
  }

  // Value objects are immutable
  clone(overrides = {}) {
    return new this.constructor({ ...this._props, ...overrides })
  }
}

/**
 * Base Aggregate Root - Consistency boundary
 */
export class AggregateRoot extends Entity {
  constructor(id) {
    super(id)
    this._version = 0
  }

  get version() {
    return this._version
  }

  // Version control for optimistic locking
  incrementVersion() {
    this._version++
  }

  // Business invariants enforcement
  enforceInvariants() {
    const violations = this._checkInvariants()
    if (violations.length > 0) {
      throw new InvariantViolationError('Business invariants violated', violations)
    }
  }

  _checkInvariants() {
    // Override in subclasses
    return []
  }

  // Apply domain event
  apply(event) {
    this.addDomainEvent(event)
    this._applyEvent(event)
    this.incrementVersion()
    this.markAsUpdated()
  }

  _applyEvent(event) {
    // Override in subclasses to handle specific events
  }
}

/**
 * Domain Event base class
 */
export class DomainEvent {
  constructor(aggregateId, eventData = {}) {
    this.aggregateId = aggregateId
    this.eventId = this._generateEventId()
    this.eventType = this.constructor.name
    this.occurredOn = new Date()
    this.eventVersion = 1
    this.eventData = eventData
  }

  _generateEventId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Serialization for persistence
  toJSON() {
    return {
      aggregateId: this.aggregateId,
      eventId: this.eventId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      eventVersion: this.eventVersion,
      eventData: this.eventData
    }
  }

  static fromJSON(data) {
    const event = new this(data.aggregateId, data.eventData)
    event.eventId = data.eventId
    event.occurredOn = new Date(data.occurredOn)
    event.eventVersion = data.eventVersion
    return event
  }
}

/**
 * Repository interface - Data access abstraction
 */
export class Repository {
  constructor() {
    if (new.target === Repository) {
      throw new Error('Repository is an abstract class')
    }
  }

  // CRUD operations
  async save(aggregate) {
    throw new Error('save method must be implemented')
  }

  async findById(id) {
    throw new Error('findById method must be implemented')
  }

  async findBy(criteria) {
    throw new Error('findBy method must be implemented')
  }

  async delete(id) {
    throw new Error('delete method must be implemented')
  }

  async exists(id) {
    throw new Error('exists method must be implemented')
  }

  // Pagination support
  async findPaginated(criteria, page, limit) {
    throw new Error('findPaginated method must be implemented')
  }

  // Transaction support
  async transaction(fn) {
    throw new Error('transaction method must be implemented')
  }
}

/**
 * Domain Service base class - Complex business logic
 */
export class DomainService {
  constructor() {
    if (new.target === DomainService) {
      throw new Error('DomainService is an abstract class')
    }
  }

  // Template method for service operations
  async execute(input) {
    try {
      await this._validateInput(input)
      const result = await this._performOperation(input)
      await this._publishEvents(result)
      return result
    } catch (error) {
      await this._handleError(error, input)
      throw error
    }
  }

  async _validateInput(input) {
    // Override in subclasses
  }

  async _performOperation(input) {
    throw new Error('_performOperation must be implemented')
  }

  async _publishEvents(result) {
    // Override in subclasses
  }

  async _handleError(error, input) {
    logger.error(`Domain service error in ${this.constructor.name}:`, error)
  }
}

/**
 * Specification pattern for business rules
 */
export class Specification {
  constructor() {
    if (new.target === Specification) {
      throw new Error('Specification is an abstract class')
    }
  }

  isSatisfiedBy(candidate) {
    throw new Error('isSatisfiedBy method must be implemented')
  }

  and(other) {
    return new AndSpecification(this, other)
  }

  or(other) {
    return new OrSpecification(this, other)
  }

  not() {
    return new NotSpecification(this)
  }
}

class AndSpecification extends Specification {
  constructor(left, right) {
    super()
    this.left = left
    this.right = right
  }

  isSatisfiedBy(candidate) {
    return this.left.isSatisfiedBy(candidate) && this.right.isSatisfiedBy(candidate)
  }
}

class OrSpecification extends Specification {
  constructor(left, right) {
    super()
    this.left = left
    this.right = right
  }

  isSatisfiedBy(candidate) {
    return this.left.isSatisfiedBy(candidate) || this.right.isSatisfiedBy(candidate)
  }
}

class NotSpecification extends Specification {
  constructor(spec) {
    super()
    this.spec = spec
  }

  isSatisfiedBy(candidate) {
    return !this.spec.isSatisfiedBy(candidate)
  }
}

// ========================================
// DOMAIN EXCEPTIONS
// ========================================

export class DomainError extends Error {
  constructor(message, code = 'DOMAIN_ERROR') {
    super(message)
    this.name = this.constructor.name
    this.code = code
    this.timestamp = new Date()
  }
}

export class DomainValidationError extends DomainError {
  constructor(message, validationErrors = []) {
    super(message, 'VALIDATION_ERROR')
    this.validationErrors = validationErrors
  }
}

export class InvariantViolationError extends DomainError {
  constructor(message, violations = []) {
    super(message, 'INVARIANT_VIOLATION')
    this.violations = violations
  }
}

export class EntityNotFoundError extends DomainError {
  constructor(entityType, id) {
    super(`${entityType} with ID ${id} not found`, 'ENTITY_NOT_FOUND')
    this.entityType = entityType
    this.entityId = id
  }
}

export class ConcurrencyError extends DomainError {
  constructor(message) {
    super(message, 'CONCURRENCY_ERROR')
  }
}

// ========================================
// APPLICATION LAYER PATTERNS
// ========================================

/**
 * Command pattern for write operations
 */
export class Command {
  constructor(data = {}) {
    this.commandId = this._generateId()
    this.timestamp = new Date()
    this.data = data
  }

  _generateId() {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  validate() {
    const errors = this._validateCommand()
    if (errors.length > 0) {
      throw new DomainValidationError('Command validation failed', errors)
    }
  }

  _validateCommand() {
    // Override in subclasses
    return []
  }
}

/**
 * Query pattern for read operations
 */
export class Query {
  constructor(criteria = {}) {
    this.queryId = this._generateId()
    this.timestamp = new Date()
    this.criteria = criteria
  }

  _generateId() {
    return `qry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

/**
 * Command Handler base class
 */
export class CommandHandler {
  constructor() {
    if (new.target === CommandHandler) {
      throw new Error('CommandHandler is an abstract class')
    }
  }

  async handle(command) {
    try {
      command.validate()
      
      const result = await this._execute(command)
      
      await this._commitTransaction()
      await this._publishDomainEvents()
      
      logger.info(`Command executed: ${command.constructor.name}`, {
        commandId: command.commandId,
        result: result?.id || 'success'
      })
      
      return result
    } catch (error) {
      await this._rollbackTransaction()
      logger.error(`Command failed: ${command.constructor.name}`, {
        commandId: command.commandId,
        error: error.message
      })
      throw error
    }
  }

  async _execute(command) {
    throw new Error('_execute method must be implemented')
  }

  async _commitTransaction() {
    // Override if transaction support needed
  }

  async _rollbackTransaction() {
    // Override if transaction support needed
  }

  async _publishDomainEvents() {
    // Override to publish collected domain events
  }
}

/**
 * Query Handler base class
 */
export class QueryHandler {
  constructor() {
    if (new.target === QueryHandler) {
      throw new Error('QueryHandler is an abstract class')
    }
  }

  async handle(query) {
    try {
      const result = await this._execute(query)
      
      logger.debug(`Query executed: ${query.constructor.name}`, {
        queryId: query.queryId,
        resultCount: Array.isArray(result) ? result.length : 1
      })
      
      return result
    } catch (error) {
      logger.error(`Query failed: ${query.constructor.name}`, {
        queryId: query.queryId,
        error: error.message
      })
      throw error
    }
  }

  async _execute(query) {
    throw new Error('_execute method must be implemented')
  }
}

// ========================================
// INFRASTRUCTURE PATTERNS
// ========================================

/**
 * Unit of Work pattern for transaction management
 */
export class UnitOfWork {
  constructor() {
    this._newEntities = new Set()
    this._dirtyEntities = new Set()
    this._deletedEntities = new Set()
    this._isCommitted = false
  }

  registerNew(entity) {
    if (this._deletedEntities.has(entity)) {
      this._deletedEntities.delete(entity)
    }
    if (!this._dirtyEntities.has(entity)) {
      this._newEntities.add(entity)
    }
  }

  registerDirty(entity) {
    if (!this._newEntities.has(entity) && !this._deletedEntities.has(entity)) {
      this._dirtyEntities.add(entity)
    }
  }

  registerDeleted(entity) {
    if (this._newEntities.has(entity)) {
      this._newEntities.delete(entity)
    } else {
      this._dirtyEntities.delete(entity)
      this._deletedEntities.add(entity)
    }
  }

  async commit() {
    if (this._isCommitted) {
      throw new Error('Unit of Work already committed')
    }

    try {
      // Insert new entities
      for (const entity of this._newEntities) {
        await this._insertEntity(entity)
      }

      // Update dirty entities
      for (const entity of this._dirtyEntities) {
        await this._updateEntity(entity)
      }

      // Delete entities
      for (const entity of this._deletedEntities) {
        await this._deleteEntity(entity)
      }

      this._isCommitted = true
      this._clear()

    } catch (error) {
      await this.rollback()
      throw error
    }
  }

  async rollback() {
    // Rollback changes (implementation depends on persistence layer)
    this._clear()
  }

  _clear() {
    this._newEntities.clear()
    this._dirtyEntities.clear()
    this._deletedEntities.clear()
  }

  async _insertEntity(entity) {
    // Override in concrete implementations
    throw new Error('_insertEntity must be implemented')
  }

  async _updateEntity(entity) {
    // Override in concrete implementations
    throw new Error('_updateEntity must be implemented')
  }

  async _deleteEntity(entity) {
    // Override in concrete implementations
    throw new Error('_deleteEntity must be implemented')
  }
}

/**
 * Domain Event Publisher
 */
export class DomainEventPublisher {
  constructor() {
    this._subscribers = new Map()
  }

  subscribe(eventType, handler) {
    if (!this._subscribers.has(eventType)) {
      this._subscribers.set(eventType, new Set())
    }
    this._subscribers.get(eventType).add(handler)
  }

  unsubscribe(eventType, handler) {
    if (this._subscribers.has(eventType)) {
      this._subscribers.get(eventType).delete(handler)
    }
  }

  async publish(event) {
    const eventType = event.constructor.name
    const handlers = this._subscribers.get(eventType) || new Set()

    logger.info(`Publishing domain event: ${eventType}`, {
      eventId: event.eventId,
      aggregateId: event.aggregateId,
      handlerCount: handlers.size
    })

    // Publish to internal event bus
    eventBus.publish(`domain.${eventType}`, event)

    // Execute registered handlers
    const promises = Array.from(handlers).map(handler => {
      return this._executeHandler(handler, event)
    })

    await Promise.allSettled(promises)
  }

  async _executeHandler(handler, event) {
    try {
      await handler.handle(event)
    } catch (error) {
      logger.error(`Domain event handler failed: ${handler.constructor.name}`, {
        eventId: event.eventId,
        error: error.message
      })
      // Don't rethrow - other handlers should still execute
    }
  }
}

/**
 * Factory pattern for complex object creation
 */
export class Factory {
  constructor() {
    if (new.target === Factory) {
      throw new Error('Factory is an abstract class')
    }
  }

  create(data) {
    this._validateCreationData(data)
    return this._createEntity(data)
  }

  _validateCreationData(data) {
    // Override in subclasses
  }

  _createEntity(data) {
    throw new Error('_createEntity method must be implemented')
  }
}

// ========================================
// UTILITY CLASSES
// ========================================

/**
 * Validation utility
 */
export class Validator {
  static required(value, fieldName) {
    if (value === null || value === undefined || value === '') {
      return `${fieldName} is required`
    }
    return null
  }

  static email(value, fieldName) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (value && !emailRegex.test(value)) {
      return `${fieldName} must be a valid email address`
    }
    return null
  }

  static minLength(value, minLength, fieldName) {
    if (value && value.length < minLength) {
      return `${fieldName} must be at least ${minLength} characters long`
    }
    return null
  }

  static maxLength(value, maxLength, fieldName) {
    if (value && value.length > maxLength) {
      return `${fieldName} must be no more than ${maxLength} characters long`
    }
    return null
  }

  static range(value, min, max, fieldName) {
    if (value !== null && value !== undefined) {
      if (value < min || value > max) {
        return `${fieldName} must be between ${min} and ${max}`
      }
    }
    return null
  }

  static pattern(value, pattern, fieldName, message) {
    if (value && !pattern.test(value)) {
      return message || `${fieldName} format is invalid`
    }
    return null
  }

  static validateAll(validations) {
    return validations.filter(error => error !== null)
  }
}

/**
 * ID generation utility
 */
export class IdGenerator {
  static uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  static shortId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
  }

  static numericId() {
    return Date.now() + Math.floor(Math.random() * 1000)
  }
}

// ========================================
// EXPORTS
// ========================================

// Create singleton instances
export const domainEventPublisher = new DomainEventPublisher()

// Export factory functions
export const createUnitOfWork = () => new UnitOfWork()
export const createValidator = () => new Validator()

logger.info('🏛️ Domain-Driven Design core infrastructure initialized')