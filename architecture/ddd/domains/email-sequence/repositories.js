/**
 * 📧 Email Sequence Domain - Repository Implementations
 * Data persistence abstractions following DDD patterns
 */

import {
  Repository,
  EntityNotFoundError,
  ConcurrencyError,
  DomainValidationError
} from '../../domain-core.js'

import {
  EmailSequence,
  Email,
  EmailContent,
  BusinessInfo,
  SequenceSettings,
  EmailAddress
} from './entities.js'

import { logger } from '../../../backend/utils/logger.js'

// ========================================
// REPOSITORY INTERFACES
// ========================================

/**
 * Email Sequence Repository Interface
 */
export class EmailSequenceRepository extends Repository {
  // CRUD operations
  async save(sequence) {
    throw new Error('save method must be implemented')
  }

  async findById(id) {
    throw new Error('findById method must be implemented')
  }

  async findByUserEmail(email) {
    throw new Error('findByUserEmail method must be implemented')
  }

  async findByStatus(status) {
    throw new Error('findByStatus method must be implemented')
  }

  async findByBusinessName(businessName) {
    throw new Error('findByBusinessName method must be implemented')
  }

  // Complex queries
  async findHighPerforming(criteria = {}) {
    throw new Error('findHighPerforming method must be implemented')
  }

  async findNeedingOptimization() {
    throw new Error('findNeedingOptimization method must be implemented')
  }

  async findRecentByUser(userEmail, limit = 10) {
    throw new Error('findRecentByUser method must be implemented')
  }

  // Analytics queries
  async getPerformanceStats(sequenceId, dateRange) {
    throw new Error('getPerformanceStats method must be implemented')
  }

  async getUserSequenceCount(userEmail) {
    throw new Error('getUserSequenceCount method must be implemented')
  }

  async searchSequences(query, filters = {}) {
    throw new Error('searchSequences method must be implemented')
  }
}

// ========================================
// MONGODB IMPLEMENTATION
// ========================================

/**
 * MongoDB Email Sequence Repository Implementation
 */
export class MongoEmailSequenceRepository extends EmailSequenceRepository {
  constructor(connection, eventPublisher) {
    super()
    this.connection = connection
    this.eventPublisher = eventPublisher
    this.collectionName = 'emailSequences'
  }

  get collection() {
    return this.connection.db.collection(this.collectionName)
  }

  // Save sequence (insert or update)
  async save(sequence) {
    if (!(sequence instanceof EmailSequence)) {
      throw new DomainValidationError('Must be EmailSequence instance')
    }

    try {
      const document = this._toDocument(sequence)
      const filter = { _id: sequence.id }
      
      // Check for version conflicts (optimistic locking)
      if (sequence.version > 0) {
        filter.version = sequence.version - 1
      }

      const result = await this.collection.replaceOne(
        filter,
        document,
        { upsert: true }
      )

      if (sequence.version > 0 && result.matchedCount === 0) {
        throw new ConcurrencyError('Sequence was modified by another process')
      }

      // Publish domain events
      await this._publishDomainEvents(sequence)

      logger.debug('Email sequence saved', {
        sequenceId: sequence.id,
        version: sequence.version,
        isNew: result.upsertedCount > 0
      })

      return sequence

    } catch (error) {
      if (error instanceof ConcurrencyError) {
        throw error
      }
      logger.error('Failed to save email sequence', { sequenceId: sequence.id, error })
      throw error
    }
  }

  // Find by ID
  async findById(id) {
    try {
      const document = await this.collection.findOne({ _id: id })
      
      if (!document) {
        throw new EntityNotFoundError('EmailSequence', id)
      }

      return this._fromDocument(document)

    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        throw error
      }
      logger.error('Failed to find sequence by ID', { sequenceId: id, error })
      throw error
    }
  }

  // Find by user email
  async findByUserEmail(email) {
    try {
      const userEmail = email instanceof EmailAddress ? email.value : email
      
      const documents = await this.collection
        .find({ 'userEmail.value': userEmail })
        .sort({ createdAt: -1 })
        .toArray()

      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find sequences by user email', { email, error })
      throw error
    }
  }

  // Find by status
  async findByStatus(status) {
    try {
      const documents = await this.collection
        .find({ status })
        .sort({ updatedAt: -1 })
        .toArray()

      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find sequences by status', { status, error })
      throw error
    }
  }

  // Find by business name
  async findByBusinessName(businessName) {
    try {
      const documents = await this.collection
        .find({ 
          'businessInfo.businessName': new RegExp(businessName, 'i') 
        })
        .sort({ createdAt: -1 })
        .toArray()

      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find sequences by business name', { businessName, error })
      throw error
    }
  }

  // Find high performing sequences
  async findHighPerforming(criteria = {}) {
    try {
      const pipeline = [
        {
          $match: {
            status: 'published',
            'emails.performance.sent': { $gt: 0 },
            ...this._buildMatchCriteria(criteria)
          }
        },
        {
          $addFields: {
            overallOpenRate: {
              $divide: [
                { $sum: '$emails.performance.opened' },
                { $sum: '$emails.performance.sent' }
              ]
            },
            overallClickRate: {
              $divide: [
                { $sum: '$emails.performance.clicked' },
                { $sum: '$emails.performance.sent' }
              ]
            }
          }
        },
        {
          $match: {
            overallOpenRate: { $gte: 0.25 }, // 25% open rate
            overallClickRate: { $gte: 0.03 }  // 3% click rate
          }
        },
        {
          $sort: { overallOpenRate: -1, overallClickRate: -1 }
        },
        {
          $limit: criteria.limit || 50
        }
      ]

      const documents = await this.collection.aggregate(pipeline).toArray()
      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find high performing sequences', { criteria, error })
      throw error
    }
  }

  // Find sequences needing optimization
  async findNeedingOptimization() {
    try {
      const pipeline = [
        {
          $match: {
            status: 'published',
            'emails.performance.sent': { $gt: 10 } // At least 10 sends for statistical significance
          }
        },
        {
          $addFields: {
            overallOpenRate: {
              $divide: [
                { $sum: '$emails.performance.opened' },
                { $sum: '$emails.performance.sent' }
              ]
            },
            overallClickRate: {
              $divide: [
                { $sum: '$emails.performance.clicked' },
                { $sum: '$emails.performance.sent' }
              ]
            }
          }
        },
        {
          $match: {
            $or: [
              { overallOpenRate: { $lt: 0.15 } }, // Less than 15% open rate
              { overallClickRate: { $lt: 0.01 } }  // Less than 1% click rate
            ]
          }
        },
        {
          $sort: { overallOpenRate: 1, overallClickRate: 1 }
        }
      ]

      const documents = await this.collection.aggregate(pipeline).toArray()
      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find sequences needing optimization', error)
      throw error
    }
  }

  // Find recent sequences by user
  async findRecentByUser(userEmail, limit = 10) {
    try {
      const email = userEmail instanceof EmailAddress ? userEmail.value : userEmail
      
      const documents = await this.collection
        .find({ 'userEmail.value': email })
        .sort({ updatedAt: -1 })
        .limit(limit)
        .toArray()

      return documents.map(doc => this._fromDocument(doc))

    } catch (error) {
      logger.error('Failed to find recent sequences by user', { userEmail, limit, error })
      throw error
    }
  }

  // Get performance statistics
  async getPerformanceStats(sequenceId, dateRange) {
    try {
      const pipeline = [
        { $match: { _id: sequenceId } },
        { $unwind: '$emails' },
        {
          $group: {
            _id: '$_id',
            totalSent: { $sum: '$emails.performance.sent' },
            totalOpened: { $sum: '$emails.performance.opened' },
            totalClicked: { $sum: '$emails.performance.clicked' },
            totalBounced: { $sum: '$emails.performance.bounced' },
            totalUnsubscribed: { $sum: '$emails.performance.unsubscribed' },
            emailCount: { $sum: 1 }
          }
        },
        {
          $addFields: {
            openRate: { $divide: ['$totalOpened', '$totalSent'] },
            clickRate: { $divide: ['$totalClicked', '$totalSent'] },
            bounceRate: { $divide: ['$totalBounced', '$totalSent'] },
            unsubscribeRate: { $divide: ['$totalUnsubscribed', '$totalSent'] }
          }
        }
      ]

      const result = await this.collection.aggregate(pipeline).toArray()
      return result[0] || null

    } catch (error) {
      logger.error('Failed to get performance stats', { sequenceId, error })
      throw error
    }
  }

  // Get user sequence count
  async getUserSequenceCount(userEmail) {
    try {
      const email = userEmail instanceof EmailAddress ? userEmail.value : userEmail
      
      const count = await this.collection.countDocuments({
        'userEmail.value': email
      })

      return count

    } catch (error) {
      logger.error('Failed to get user sequence count', { userEmail, error })
      throw error
    }
  }

  // Search sequences
  async searchSequences(query, filters = {}) {
    try {
      const searchCriteria = {
        $or: [
          { name: new RegExp(query, 'i') },
          { 'businessInfo.businessName': new RegExp(query, 'i') },
          { 'businessInfo.industry': new RegExp(query, 'i') }
        ]
      }

      // Apply filters
      if (filters.status) {
        searchCriteria.status = filters.status
      }

      if (filters.sequenceType) {
        searchCriteria['settings.sequenceType'] = filters.sequenceType
      }

      if (filters.userEmail) {
        searchCriteria['userEmail.value'] = filters.userEmail
      }

      if (filters.dateFrom || filters.dateTo) {
        searchCriteria.createdAt = {}
        if (filters.dateFrom) {
          searchCriteria.createdAt.$gte = new Date(filters.dateFrom)
        }
        if (filters.dateTo) {
          searchCriteria.createdAt.$lte = new Date(filters.dateTo)
        }
      }

      const documents = await this.collection
        .find(searchCriteria)
        .sort({ updatedAt: -1 })
        .limit(filters.limit || 50)
        .skip(filters.offset || 0)
        .toArray()

      const total = await this.collection.countDocuments(searchCriteria)

      return {
        sequences: documents.map(doc => this._fromDocument(doc)),
        total,
        hasMore: (filters.offset || 0) + documents.length < total
      }

    } catch (error) {
      logger.error('Failed to search sequences', { query, filters, error })
      throw error
    }
  }

  // Pagination support
  async findPaginated(criteria, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit
      const matchCriteria = this._buildMatchCriteria(criteria)

      const documents = await this.collection
        .find(matchCriteria)
        .sort({ updatedAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray()

      const total = await this.collection.countDocuments(matchCriteria)
      const totalPages = Math.ceil(total / limit)

      return {
        sequences: documents.map(doc => this._fromDocument(doc)),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }

    } catch (error) {
      logger.error('Failed to find paginated sequences', { criteria, page, limit, error })
      throw error
    }
  }

  // Transaction support
  async transaction(fn) {
    const session = this.connection.startSession()
    
    try {
      await session.withTransaction(async () => {
        await fn(session)
      })
    } finally {
      await session.endSession()
    }
  }

  // Bulk operations
  async bulkUpdate(updates) {
    try {
      const operations = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { $set: update.changes },
          upsert: false
        }
      }))

      const result = await this.collection.bulkWrite(operations)
      
      logger.info('Bulk update completed', {
        updatedCount: result.modifiedCount,
        operationCount: operations.length
      })

      return result

    } catch (error) {
      logger.error('Bulk update failed', { updateCount: updates.length, error })
      throw error
    }
  }

  // Delete sequence
  async delete(id) {
    try {
      const result = await this.collection.deleteOne({ _id: id })
      
      if (result.deletedCount === 0) {
        throw new EntityNotFoundError('EmailSequence', id)
      }

      logger.info('Email sequence deleted', { sequenceId: id })
      return true

    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        throw error
      }
      logger.error('Failed to delete sequence', { sequenceId: id, error })
      throw error
    }
  }

  // Check if sequence exists
  async exists(id) {
    try {
      const count = await this.collection.countDocuments({ _id: id })
      return count > 0
    } catch (error) {
      logger.error('Failed to check sequence existence', { sequenceId: id, error })
      throw error
    }
  }

  // ========================================
  // PRIVATE HELPER METHODS
  // ========================================

  // Convert domain object to database document
  _toDocument(sequence) {
    const emails = sequence.emails.map(email => ({
      _id: email.id,
      sequencePosition: email.sequencePosition,
      content: {
        subject: email.content.subject,
        body: email.content.body,
        htmlBody: email.content.htmlBody,
        plainTextBody: email.content.plainTextBody,
        metadata: email.content.metadata
      },
      scheduledDays: email.scheduledDays,
      isActive: email.isActive,
      performance: {
        sent: email.performance.sent,
        delivered: email.performance.delivered,
        opened: email.performance.opened,
        clicked: email.performance.clicked,
        bounced: email.performance.bounced,
        unsubscribed: email.performance.unsubscribed,
        lastUpdated: email.performance.lastUpdated
      },
      createdAt: email.createdAt,
      updatedAt: email.updatedAt
    }))

    return {
      _id: sequence.id,
      name: sequence.name,
      userEmail: {
        value: sequence.userEmail.value
      },
      businessInfo: {
        businessName: sequence.businessInfo.businessName,
        industry: sequence.businessInfo.industry,
        targetAudience: sequence.businessInfo.targetAudience,
        goals: sequence.businessInfo.goals,
        tone: sequence.businessInfo.tone,
        additionalInfo: sequence.businessInfo.additionalInfo
      },
      settings: {
        sequenceType: sequence.settings.sequenceType,
        emailCount: sequence.settings.emailCount,
        frequency: sequence.settings.frequency,
        personalizeLevel: sequence.settings.personalizeLevel,
        includeCallToAction: sequence.settings.includeCallToAction,
        ctaType: sequence.settings.ctaType,
        followUpDays: sequence.settings.followUpDays
      },
      emails,
      status: sequence.status,
      metadata: sequence.metadata,
      version: sequence.version,
      createdAt: sequence.createdAt,
      updatedAt: sequence.updatedAt
    }
  }

  // Convert database document to domain object
  _fromDocument(document) {
    // Create value objects
    const userEmail = new EmailAddress(document.userEmail.value)
    const businessInfo = new BusinessInfo(document.businessInfo)
    const settings = new SequenceSettings(document.settings)

    // Create email entities
    const emails = document.emails.map(emailDoc => {
      const content = new EmailContent({
        subject: emailDoc.content.subject,
        body: emailDoc.content.body,
        htmlBody: emailDoc.content.htmlBody,
        plainTextBody: emailDoc.content.plainTextBody,
        metadata: emailDoc.content.metadata
      })

      const email = new Email(emailDoc._id, {
        sequencePosition: emailDoc.sequencePosition,
        content,
        scheduledDays: emailDoc.scheduledDays,
        isActive: emailDoc.isActive
      })

      // Set performance data
      if (emailDoc.performance) {
        email.updatePerformance(emailDoc.performance)
      }

      // Set timestamps
      email._createdAt = emailDoc.createdAt
      email._updatedAt = emailDoc.updatedAt

      return email
    })

    // Create sequence aggregate
    const sequence = new EmailSequence(document._id, {
      name: document.name,
      userEmail,
      businessInfo,
      settings,
      emails: [],
      status: document.status,
      metadata: document.metadata
    })

    // Add emails (this will trigger validation)
    emails.forEach(email => sequence.addEmail(email))

    // Set version and timestamps
    sequence._version = document.version || 0
    sequence._createdAt = document.createdAt
    sequence._updatedAt = document.updatedAt

    // Clear domain events (they're from persistence, not new business operations)
    sequence.clearDomainEvents()

    return sequence
  }

  // Build MongoDB match criteria from filters
  _buildMatchCriteria(criteria) {
    const match = {}

    if (criteria.status) {
      match.status = criteria.status
    }

    if (criteria.userEmail) {
      match['userEmail.value'] = criteria.userEmail
    }

    if (criteria.sequenceType) {
      match['settings.sequenceType'] = criteria.sequenceType
    }

    if (criteria.industry) {
      match['businessInfo.industry'] = criteria.industry
    }

    if (criteria.createdAfter) {
      match.createdAt = { $gte: new Date(criteria.createdAfter) }
    }

    if (criteria.createdBefore) {
      match.createdAt = { ...match.createdAt, $lte: new Date(criteria.createdBefore) }
    }

    return match
  }

  // Publish domain events
  async _publishDomainEvents(sequence) {
    const events = sequence.getDomainEvents()
    
    if (events.length === 0) return

    try {
      for (const event of events) {
        await this.eventPublisher.publish(event)
      }

      // Clear events after successful publishing
      sequence.clearDomainEvents()

      logger.debug('Domain events published', {
        sequenceId: sequence.id,
        eventCount: events.length
      })

    } catch (error) {
      logger.error('Failed to publish domain events', {
        sequenceId: sequence.id,
        eventCount: events.length,
        error
      })
      // Don't rethrow - the sequence save was successful
    }
  }
}

// ========================================
// IN-MEMORY IMPLEMENTATION (FOR TESTING)
// ========================================

/**
 * In-Memory Email Sequence Repository (for testing)
 */
export class InMemoryEmailSequenceRepository extends EmailSequenceRepository {
  constructor(eventPublisher) {
    super()
    this.sequences = new Map()
    this.eventPublisher = eventPublisher
  }

  async save(sequence) {
    if (!(sequence instanceof EmailSequence)) {
      throw new DomainValidationError('Must be EmailSequence instance')
    }

    // Check for version conflicts
    const existing = this.sequences.get(sequence.id)
    if (existing && existing.version !== sequence.version - 1) {
      throw new ConcurrencyError('Sequence was modified by another process')
    }

    // Store sequence
    this.sequences.set(sequence.id, this._deepClone(sequence))

    // Publish domain events
    await this._publishDomainEvents(sequence)

    return sequence
  }

  async findById(id) {
    const sequence = this.sequences.get(id)
    if (!sequence) {
      throw new EntityNotFoundError('EmailSequence', id)
    }
    return this._deepClone(sequence)
  }

  async findByUserEmail(email) {
    const userEmail = email instanceof EmailAddress ? email.value : email
    return Array.from(this.sequences.values())
      .filter(seq => seq.userEmail.value === userEmail)
      .map(seq => this._deepClone(seq))
  }

  async findByStatus(status) {
    return Array.from(this.sequences.values())
      .filter(seq => seq.status === status)
      .map(seq => this._deepClone(seq))
  }

  async findByBusinessName(businessName) {
    return Array.from(this.sequences.values())
      .filter(seq => seq.businessInfo.businessName.toLowerCase().includes(businessName.toLowerCase()))
      .map(seq => this._deepClone(seq))
  }

  async findHighPerforming(criteria = {}) {
    return Array.from(this.sequences.values())
      .filter(seq => {
        const performance = seq.getOverallPerformance()
        return performance && 
               performance.averageOpenRate >= 25 && 
               performance.averageClickRate >= 3
      })
      .map(seq => this._deepClone(seq))
  }

  async findNeedingOptimization() {
    return Array.from(this.sequences.values())
      .filter(seq => {
        const performance = seq.getOverallPerformance()
        return performance && 
               (performance.averageOpenRate <= 15 || performance.averageClickRate <= 1)
      })
      .map(seq => this._deepClone(seq))
  }

  async findRecentByUser(userEmail, limit = 10) {
    const email = userEmail instanceof EmailAddress ? userEmail.value : userEmail
    return Array.from(this.sequences.values())
      .filter(seq => seq.userEmail.value === email)
      .sort((a, b) => b.updatedAt - a.updatedAt)
      .slice(0, limit)
      .map(seq => this._deepClone(seq))
  }

  async getPerformanceStats(sequenceId, dateRange) {
    const sequence = this.sequences.get(sequenceId)
    if (!sequence) return null

    return sequence.getOverallPerformance()
  }

  async getUserSequenceCount(userEmail) {
    const email = userEmail instanceof EmailAddress ? userEmail.value : userEmail
    return Array.from(this.sequences.values())
      .filter(seq => seq.userEmail.value === email)
      .length
  }

  async searchSequences(query, filters = {}) {
    let results = Array.from(this.sequences.values())

    // Apply text search
    if (query) {
      const lowerQuery = query.toLowerCase()
      results = results.filter(seq => 
        seq.name.toLowerCase().includes(lowerQuery) ||
        seq.businessInfo.businessName.toLowerCase().includes(lowerQuery) ||
        seq.businessInfo.industry.toLowerCase().includes(lowerQuery)
      )
    }

    // Apply filters
    if (filters.status) {
      results = results.filter(seq => seq.status === filters.status)
    }

    if (filters.sequenceType) {
      results = results.filter(seq => seq.settings.sequenceType === filters.sequenceType)
    }

    if (filters.userEmail) {
      results = results.filter(seq => seq.userEmail.value === filters.userEmail)
    }

    // Sort by updated date
    results.sort((a, b) => b.updatedAt - a.updatedAt)

    // Apply pagination
    const offset = filters.offset || 0
    const limit = filters.limit || 50
    const paginatedResults = results.slice(offset, offset + limit)

    return {
      sequences: paginatedResults.map(seq => this._deepClone(seq)),
      total: results.length,
      hasMore: offset + paginatedResults.length < results.length
    }
  }

  async findPaginated(criteria, page = 1, limit = 20) {
    const skip = (page - 1) * limit
    let results = Array.from(this.sequences.values())

    // Apply criteria filters
    if (criteria.status) {
      results = results.filter(seq => seq.status === criteria.status)
    }

    if (criteria.userEmail) {
      results = results.filter(seq => seq.userEmail.value === criteria.userEmail)
    }

    // Sort and paginate
    results.sort((a, b) => b.updatedAt - a.updatedAt)
    const total = results.length
    const paginatedResults = results.slice(skip, skip + limit)
    const totalPages = Math.ceil(total / limit)

    return {
      sequences: paginatedResults.map(seq => this._deepClone(seq)),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }

  async transaction(fn) {
    // In-memory implementation doesn't need real transactions
    await fn()
  }

  async delete(id) {
    if (!this.sequences.has(id)) {
      throw new EntityNotFoundError('EmailSequence', id)
    }
    
    this.sequences.delete(id)
    return true
  }

  async exists(id) {
    return this.sequences.has(id)
  }

  // Helper methods
  _deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
  }

  async _publishDomainEvents(sequence) {
    if (!this.eventPublisher) return

    const events = sequence.getDomainEvents()
    for (const event of events) {
      await this.eventPublisher.publish(event)
    }
    sequence.clearDomainEvents()
  }

  // Test utilities
  clear() {
    this.sequences.clear()
  }

  size() {
    return this.sequences.size
  }
}