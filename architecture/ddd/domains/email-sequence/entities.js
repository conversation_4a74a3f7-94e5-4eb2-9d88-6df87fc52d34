/**
 * 📧 Email Sequence Domain - Entities and Aggregate Roots
 * Clean domain model using DDD patterns
 */

import { 
  AggregateRoot, 
  Entity, 
  ValueObject, 
  DomainEvent,
  DomainValidationError,
  InvariantViolationError,
  Validator,
  IdGenerator
} from '../../domain-core.js'

// ========================================
// VALUE OBJECTS
// ========================================

/**
 * Email Address value object
 */
export class EmailAddress extends ValueObject {
  constructor(value) {
    const validationErrors = []
    
    if (!value) {
      validationErrors.push('Email address is required')
    } else if (!EmailAddress.isValid(value)) {
      validationErrors.push('Invalid email address format')
    }

    if (validationErrors.length > 0) {
      throw new DomainValidationError('Invalid email address', validationErrors)
    }

    super({ value: value.toLowerCase().trim() })
  }

  get value() {
    return this._props.value
  }

  static isValid(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  toString() {
    return this.value
  }
}

/**
 * Business Information value object
 */
export class BusinessInfo extends ValueObject {
  constructor({ businessName, industry, targetAudience, goals, tone, additionalInfo }) {
    const validationErrors = Validator.validateAll([
      Validator.required(businessName, 'Business name'),
      Validator.minLength(businessName, 2, 'Business name'),
      Validator.maxLength(businessName, 100, 'Business name'),
      Validator.required(industry, 'Industry'),
      Validator.required(targetAudience, 'Target audience'),
      Validator.minLength(targetAudience, 10, 'Target audience'),
      Validator.maxLength(targetAudience, 500, 'Target audience')
    ])

    if (validationErrors.length > 0) {
      throw new DomainValidationError('Invalid business info', validationErrors)
    }

    super({
      businessName: businessName.trim(),
      industry: industry.trim(),
      targetAudience: targetAudience.trim(),
      goals: goals?.trim() || '',
      tone: tone || 'professional',
      additionalInfo: additionalInfo?.trim() || ''
    })
  }

  get businessName() { return this._props.businessName }
  get industry() { return this._props.industry }
  get targetAudience() { return this._props.targetAudience }
  get goals() { return this._props.goals }
  get tone() { return this._props.tone }
  get additionalInfo() { return this._props.additionalInfo }

  // Business rules
  isComplete() {
    return !!(this.businessName && this.industry && this.targetAudience)
  }

  hasAdvancedInfo() {
    return !!(this.goals && this.additionalInfo)
  }
}

/**
 * Sequence Settings value object
 */
export class SequenceSettings extends ValueObject {
  constructor({ 
    sequenceType = 'nurture',
    emailCount = 5,
    frequency = 'weekly',
    personalizeLevel = 'medium',
    includeCallToAction = true,
    ctaType = 'soft',
    followUpDays = [1, 3, 7, 14, 30]
  }) {
    const validationErrors = Validator.validateAll([
      Validator.range(emailCount, 3, 10, 'Email count'),
      this._validateSequenceType(sequenceType),
      this._validateFrequency(frequency),
      this._validatePersonalizeLevel(personalizeLevel),
      this._validateCtaType(ctaType)
    ])

    if (validationErrors.length > 0) {
      throw new DomainValidationError('Invalid sequence settings', validationErrors)
    }

    super({
      sequenceType,
      emailCount: Math.max(3, Math.min(10, emailCount)),
      frequency,
      personalizeLevel,
      includeCallToAction,
      ctaType,
      followUpDays: followUpDays.slice(0, emailCount)
    })
  }

  get sequenceType() { return this._props.sequenceType }
  get emailCount() { return this._props.emailCount }
  get frequency() { return this._props.frequency }
  get personalizeLevel() { return this._props.personalizeLevel }
  get includeCallToAction() { return this._props.includeCallToAction }
  get ctaType() { return this._props.ctaType }
  get followUpDays() { return this._props.followUpDays }

  _validateSequenceType(type) {
    const validTypes = ['welcome', 'nurture', 'sales', 'onboarding', 'reactivation']
    return validTypes.includes(type) ? null : 'Invalid sequence type'
  }

  _validateFrequency(frequency) {
    const validFrequencies = ['daily', 'every-2-days', 'weekly', 'bi-weekly']
    return validFrequencies.includes(frequency) ? null : 'Invalid frequency'
  }

  _validatePersonalizeLevel(level) {
    const validLevels = ['low', 'medium', 'high']
    return validLevels.includes(level) ? null : 'Invalid personalization level'
  }

  _validateCtaType(type) {
    const validTypes = ['none', 'soft', 'medium', 'strong']
    return validTypes.includes(type) ? null : 'Invalid CTA type'
  }

  // Business logic
  getDaysBetweenEmails() {
    const frequencyMap = {
      'daily': 1,
      'every-2-days': 2,
      'weekly': 7,
      'bi-weekly': 14
    }
    return frequencyMap[this.frequency] || 7
  }

  getTotalSequenceDuration() {
    return this.emailCount * this.getDaysBetweenEmails()
  }

  isHighTouch() {
    return this.personalizeLevel === 'high' && this.frequency === 'daily'
  }
}

/**
 * Email Content value object
 */
export class EmailContent extends ValueObject {
  constructor({ subject, body, htmlBody, plainTextBody, metadata = {} }) {
    const validationErrors = Validator.validateAll([
      Validator.required(subject, 'Subject'),
      Validator.minLength(subject, 5, 'Subject'),
      Validator.maxLength(subject, 200, 'Subject'),
      Validator.required(body, 'Body'),
      Validator.minLength(body, 50, 'Body'),
      Validator.maxLength(body, 10000, 'Body')
    ])

    if (validationErrors.length > 0) {
      throw new DomainValidationError('Invalid email content', validationErrors)
    }

    super({
      subject: subject.trim(),
      body: body.trim(),
      htmlBody: htmlBody?.trim() || this._generateHtmlFromText(body),
      plainTextBody: plainTextBody?.trim() || this._generatePlainTextFromHtml(body),
      metadata: {
        wordCount: this._countWords(body),
        estimatedReadTime: this._calculateReadTime(body),
        hasPersonalization: this._hasPersonalizationTokens(body),
        ...metadata
      }
    })
  }

  get subject() { return this._props.subject }
  get body() { return this._props.body }
  get htmlBody() { return this._props.htmlBody }
  get plainTextBody() { return this._props.plainTextBody }
  get metadata() { return this._props.metadata }

  _generateHtmlFromText(text) {
    return text
      .split('\n\n')
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('')
  }

  _generatePlainTextFromHtml(html) {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
  }

  _countWords(text) {
    return text.trim().split(/\s+/).length
  }

  _calculateReadTime(text) {
    const wordsPerMinute = 200
    const words = this._countWords(text)
    return Math.ceil(words / wordsPerMinute)
  }

  _hasPersonalizationTokens(text) {
    return /\{\{[\w\s]+\}\}/.test(text)
  }

  // Business methods
  getReadabilityScore() {
    // Simplified readability calculation
    const sentences = this.body.split(/[.!?]+/).length
    const words = this.metadata.wordCount
    const avgWordsPerSentence = words / sentences
    
    if (avgWordsPerSentence <= 15) return 'easy'
    if (avgWordsPerSentence <= 20) return 'moderate'
    return 'difficult'
  }

  hasCallToAction() {
    const ctaPatterns = [
      /click here/i,
      /learn more/i,
      /get started/i,
      /sign up/i,
      /download/i,
      /schedule/i,
      /contact us/i
    ]
    return ctaPatterns.some(pattern => pattern.test(this.body))
  }
}

// ========================================
// ENTITIES
// ========================================

/**
 * Individual Email entity within a sequence
 */
export class Email extends Entity {
  constructor(id, {
    sequencePosition,
    content,
    scheduledDays,
    isActive = true,
    performance = null
  }) {
    super(id)
    
    this._sequencePosition = sequencePosition
    this._content = content
    this._scheduledDays = scheduledDays
    this._isActive = isActive
    this._performance = performance || new EmailPerformance()
    
    this.validate()
  }

  get sequencePosition() { return this._sequencePosition }
  get content() { return this._content }
  get scheduledDays() { return this._scheduledDays }
  get isActive() { return this._isActive }
  get performance() { return this._performance }

  // Business methods
  updateContent(newContent) {
    if (!(newContent instanceof EmailContent)) {
      throw new DomainValidationError('Content must be EmailContent instance')
    }
    
    this._content = newContent
    this.markAsUpdated()
    this.addDomainEvent(new EmailContentUpdatedEvent(this.id, newContent))
  }

  activate() {
    if (this._isActive) return
    
    this._isActive = true
    this.markAsUpdated()
    this.addDomainEvent(new EmailActivatedEvent(this.id))
  }

  deactivate() {
    if (!this._isActive) return
    
    this._isActive = false
    this.markAsUpdated()
    this.addDomainEvent(new EmailDeactivatedEvent(this.id))
  }

  updatePerformance(metrics) {
    this._performance = new EmailPerformance(metrics)
    this.markAsUpdated()
  }

  // Validation
  _validateEntity() {
    const errors = []
    
    if (this._sequencePosition < 1) {
      errors.push('Sequence position must be positive')
    }
    
    if (!(this._content instanceof EmailContent)) {
      errors.push('Content must be EmailContent instance')
    }
    
    if (this._scheduledDays < 0) {
      errors.push('Scheduled days must be non-negative')
    }
    
    return errors
  }

  // Domain logic
  isWelcomeEmail() {
    return this._sequencePosition === 1
  }

  isFollowUpEmail() {
    return this._sequencePosition > 1
  }

  getDaysFromStart() {
    return this._scheduledDays
  }
}

/**
 * Email Performance value object (nested in Email)
 */
export class EmailPerformance extends ValueObject {
  constructor({
    sent = 0,
    delivered = 0,
    opened = 0,
    clicked = 0,
    bounced = 0,
    unsubscribed = 0,
    lastUpdated = new Date()
  } = {}) {
    super({
      sent,
      delivered,
      opened,
      clicked,
      bounced,
      unsubscribed,
      lastUpdated
    })
  }

  get sent() { return this._props.sent }
  get delivered() { return this._props.delivered }
  get opened() { return this._props.opened }
  get clicked() { return this._props.clicked }
  get bounced() { return this._props.bounced }
  get unsubscribed() { return this._props.unsubscribed }
  get lastUpdated() { return this._props.lastUpdated }

  // Calculated metrics
  getDeliveryRate() {
    return this.sent > 0 ? (this.delivered / this.sent) * 100 : 0
  }

  getOpenRate() {
    return this.delivered > 0 ? (this.opened / this.delivered) * 100 : 0
  }

  getClickRate() {
    return this.delivered > 0 ? (this.clicked / this.delivered) * 100 : 0
  }

  getClickToOpenRate() {
    return this.opened > 0 ? (this.clicked / this.opened) * 100 : 0
  }

  getBounceRate() {
    return this.sent > 0 ? (this.bounced / this.sent) * 100 : 0
  }

  getUnsubscribeRate() {
    return this.delivered > 0 ? (this.unsubscribed / this.delivered) * 100 : 0
  }

  // Performance assessment
  isPerformingWell() {
    return this.getOpenRate() > 20 && this.getClickRate() > 2 && this.getBounceRate() < 5
  }

  needsOptimization() {
    return this.getOpenRate() < 15 || this.getClickRate() < 1 || this.getBounceRate() > 10
  }
}

// ========================================
// AGGREGATE ROOT
// ========================================

/**
 * Email Sequence Aggregate Root
 */
export class EmailSequence extends AggregateRoot {
  constructor(id, {
    name,
    userEmail,
    businessInfo,
    settings,
    emails = [],
    status = 'draft',
    metadata = {}
  }) {
    super(id)
    
    this._name = name
    this._userEmail = userEmail instanceof EmailAddress ? userEmail : new EmailAddress(userEmail)
    this._businessInfo = businessInfo instanceof BusinessInfo ? businessInfo : new BusinessInfo(businessInfo)
    this._settings = settings instanceof SequenceSettings ? settings : new SequenceSettings(settings)
    this._emails = new Map()
    this._status = status
    this._metadata = {
      createdBy: 'ai-assistant',
      generationModel: 'gpt-4',
      estimatedValue: 0,
      tags: [],
      ...metadata
    }
    
    // Initialize emails
    emails.forEach(email => this.addEmail(email))
    
    this.validate()
    this.enforceInvariants()
  }

  // Getters
  get name() { return this._name }
  get userEmail() { return this._userEmail }
  get businessInfo() { return this._businessInfo }
  get settings() { return this._settings }
  get emails() { return Array.from(this._emails.values()) }
  get status() { return this._status }
  get metadata() { return this._metadata }

  // Business methods
  addEmail(email) {
    if (!(email instanceof Email)) {
      throw new DomainValidationError('Must be Email instance')
    }
    
    if (this._emails.has(email.sequencePosition)) {
      throw new InvariantViolationError('Email position already exists')
    }
    
    this._emails.set(email.sequencePosition, email)
    this.markAsUpdated()
    this.addDomainEvent(new EmailAddedToSequenceEvent(this.id, email.id, email.sequencePosition))
  }

  removeEmail(position) {
    if (!this._emails.has(position)) {
      throw new InvariantViolationError('Email position does not exist')
    }
    
    const email = this._emails.get(position)
    this._emails.delete(position)
    this.markAsUpdated()
    this.addDomainEvent(new EmailRemovedFromSequenceEvent(this.id, email.id, position))
  }

  updateEmail(position, newContent) {
    if (!this._emails.has(position)) {
      throw new InvariantViolationError('Email position does not exist')
    }
    
    const email = this._emails.get(position)
    email.updateContent(newContent)
    this.markAsUpdated()
  }

  publish() {
    if (this._status === 'published') {
      throw new InvariantViolationError('Sequence already published')
    }
    
    this.enforceInvariants()
    this._status = 'published'
    this.markAsUpdated()
    this.addDomainEvent(new SequencePublishedEvent(this.id, this._userEmail.value))
  }

  archive() {
    this._status = 'archived'
    this.markAsUpdated()
    this.addDomainEvent(new SequenceArchivedEvent(this.id))
  }

  duplicate() {
    const duplicateId = IdGenerator.uuid()
    const duplicateName = `${this._name} (Copy)`
    
    const duplicateEmails = this.emails.map(email => 
      new Email(IdGenerator.uuid(), {
        sequencePosition: email.sequencePosition,
        content: email.content,
        scheduledDays: email.scheduledDays,
        isActive: email.isActive
      })
    )
    
    const duplicate = new EmailSequence(duplicateId, {
      name: duplicateName,
      userEmail: this._userEmail,
      businessInfo: this._businessInfo,
      settings: this._settings,
      emails: duplicateEmails,
      status: 'draft',
      metadata: { ...this._metadata, originalSequenceId: this.id }
    })
    
    this.addDomainEvent(new SequenceDuplicatedEvent(this.id, duplicateId))
    return duplicate
  }

  // Query methods
  getEmailByPosition(position) {
    return this._emails.get(position)
  }

  getEmailCount() {
    return this._emails.size
  }

  getActiveEmails() {
    return this.emails.filter(email => email.isActive)
  }

  getTotalEstimatedDuration() {
    if (this._emails.size === 0) return 0
    
    const lastEmail = Math.max(...this._emails.keys())
    const lastEmailScheduled = this._emails.get(lastEmail)?.scheduledDays || 0
    return lastEmailScheduled
  }

  // Performance analytics
  getOverallPerformance() {
    const emails = this.getActiveEmails()
    if (emails.length === 0) return null
    
    const totalSent = emails.reduce((sum, email) => sum + email.performance.sent, 0)
    const totalOpened = emails.reduce((sum, email) => sum + email.performance.opened, 0)
    const totalClicked = emails.reduce((sum, email) => sum + email.performance.clicked, 0)
    
    return {
      averageOpenRate: totalSent > 0 ? (totalOpened / totalSent) * 100 : 0,
      averageClickRate: totalSent > 0 ? (totalClicked / totalSent) * 100 : 0,
      totalEngagement: totalOpened + totalClicked,
      emailCount: emails.length
    }
  }

  // Validation
  _validateEntity() {
    const errors = []
    
    if (!this._name || this._name.trim().length < 3) {
      errors.push('Sequence name must be at least 3 characters')
    }
    
    if (!(this._userEmail instanceof EmailAddress)) {
      errors.push('User email must be valid EmailAddress')
    }
    
    if (!(this._businessInfo instanceof BusinessInfo)) {
      errors.push('Business info must be valid BusinessInfo')
    }
    
    if (!(this._settings instanceof SequenceSettings)) {
      errors.push('Settings must be valid SequenceSettings')
    }
    
    const validStatuses = ['draft', 'published', 'archived', 'paused']
    if (!validStatuses.includes(this._status)) {
      errors.push('Invalid status')
    }
    
    return errors
  }

  // Business invariants
  _checkInvariants() {
    const violations = []
    
    // Must have at least 3 emails when published
    if (this._status === 'published' && this._emails.size < 3) {
      violations.push('Published sequence must have at least 3 emails')
    }
    
    // Email positions must be sequential
    const positions = Array.from(this._emails.keys()).sort((a, b) => a - b)
    for (let i = 0; i < positions.length; i++) {
      if (positions[i] !== i + 1) {
        violations.push('Email positions must be sequential starting from 1')
        break
      }
    }
    
    // Settings email count must match actual emails
    if (this._emails.size !== this._settings.emailCount) {
      violations.push('Email count must match settings')
    }
    
    // All emails must have valid scheduled days
    const daysBetween = this._settings.getDaysBetweenEmails()
    let expectedDays = 0
    
    for (const [position, email] of this._emails) {
      if (position === 1 && email.scheduledDays !== 0) {
        violations.push('First email must be scheduled for day 0')
      } else if (position > 1 && email.scheduledDays !== expectedDays) {
        violations.push(`Email ${position} scheduled days don't match frequency`)
      }
      expectedDays += daysBetween
    }
    
    return violations
  }
}

// ========================================
// DOMAIN EVENTS
// ========================================

export class EmailSequenceCreatedEvent extends DomainEvent {
  constructor(sequenceId, userEmail, businessInfo) {
    super(sequenceId, { userEmail, businessInfo: businessInfo.props })
  }
}

export class EmailAddedToSequenceEvent extends DomainEvent {
  constructor(sequenceId, emailId, position) {
    super(sequenceId, { emailId, position })
  }
}

export class EmailRemovedFromSequenceEvent extends DomainEvent {
  constructor(sequenceId, emailId, position) {
    super(sequenceId, { emailId, position })
  }
}

export class EmailContentUpdatedEvent extends DomainEvent {
  constructor(emailId, content) {
    super(emailId, { content: content.props })
  }
}

export class EmailActivatedEvent extends DomainEvent {
  constructor(emailId) {
    super(emailId, {})
  }
}

export class EmailDeactivatedEvent extends DomainEvent {
  constructor(emailId) {
    super(emailId, {})
  }
}

export class SequencePublishedEvent extends DomainEvent {
  constructor(sequenceId, userEmail) {
    super(sequenceId, { userEmail, publishedAt: new Date() })
  }
}

export class SequenceArchivedEvent extends DomainEvent {
  constructor(sequenceId) {
    super(sequenceId, { archivedAt: new Date() })
  }
}

export class SequenceDuplicatedEvent extends DomainEvent {
  constructor(originalSequenceId, duplicateSequenceId) {
    super(originalSequenceId, { duplicateSequenceId, duplicatedAt: new Date() })
  }
}