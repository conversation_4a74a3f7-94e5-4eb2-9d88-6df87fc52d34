/**
 * 📧 Email Sequence Domain Services
 * Complex business logic and domain operations
 */

import {
  DomainService,
  DomainValidationError,
  InvariantViolationError,
  Specification,
  IdGenerator
} from '../../domain-core.js'

import {
  EmailSequence,
  Email,
  EmailContent,
  BusinessInfo,
  SequenceSettings,
  EmailAddress,
  EmailSequenceCreatedEvent
} from './entities.js'

import { logger } from '../../../backend/utils/logger.js'

// ========================================
// DOMAIN SERVICES
// ========================================

/**
 * Email Sequence Generation Service
 * Handles complex AI-powered sequence creation
 */
export class EmailSequenceGenerationService extends DomainService {
  constructor(aiService, contentOptimizer) {
    super()
    this.aiService = aiService
    this.contentOptimizer = contentOptimizer
  }

  async generateSequence(userEmail, businessInfo, settings) {
    // Validate inputs
    await this._validateInput({ userEmail, businessInfo, settings })

    try {
      logger.info('Starting email sequence generation', {
        userEmail: userEmail.value,
        businessName: businessInfo.businessName,
        sequenceType: settings.sequenceType
      })

      // Generate sequence name
      const sequenceName = this._generateSequenceName(businessInfo, settings)
      
      // Create the aggregate root
      const sequenceId = IdGenerator.uuid()
      const sequence = new EmailSequence(sequenceId, {
        name: sequenceName,
        userEmail,
        businessInfo,
        settings,
        status: 'draft',
        metadata: {
          generatedAt: new Date(),
          generationVersion: '2.0',
          aiModel: 'gpt-4'
        }
      })

      // Generate individual emails
      const emails = await this._generateEmailContent(businessInfo, settings)
      
      // Add emails to sequence
      emails.forEach(email => sequence.addEmail(email))

      // Apply domain event
      sequence.addDomainEvent(new EmailSequenceCreatedEvent(
        sequenceId, 
        userEmail.value, 
        businessInfo
      ))

      logger.info('Email sequence generated successfully', {
        sequenceId,
        emailCount: emails.length,
        totalDuration: sequence.getTotalEstimatedDuration()
      })

      return sequence

    } catch (error) {
      logger.error('Email sequence generation failed', error)
      throw error
    }
  }

  async _validateInput({ userEmail, businessInfo, settings }) {
    const errors = []

    if (!(userEmail instanceof EmailAddress)) {
      errors.push('Invalid email address')
    }

    if (!(businessInfo instanceof BusinessInfo)) {
      errors.push('Invalid business information')
    }

    if (!(settings instanceof SequenceSettings)) {
      errors.push('Invalid sequence settings')
    }

    if (!businessInfo.isComplete()) {
      errors.push('Business information is incomplete')
    }

    if (errors.length > 0) {
      throw new DomainValidationError('Invalid sequence generation input', errors)
    }
  }

  _generateSequenceName(businessInfo, settings) {
    const typeNames = {
      'welcome': 'Welcome Series',
      'nurture': 'Nurture Campaign', 
      'sales': 'Sales Sequence',
      'onboarding': 'Onboarding Flow',
      'reactivation': 'Re-engagement Campaign'
    }

    const typeName = typeNames[settings.sequenceType] || 'Email Sequence'
    return `${businessInfo.businessName} - ${typeName}`
  }

  async _generateEmailContent(businessInfo, settings) {
    const emails = []
    const basePrompt = this._buildBasePrompt(businessInfo, settings)

    for (let position = 1; position <= settings.emailCount; position++) {
      const emailPrompt = this._buildEmailPrompt(basePrompt, position, settings)
      
      try {
        const aiResponse = await this.aiService.generateContent(emailPrompt)
        const content = this._parseAIResponse(aiResponse, position)
        
        // Optimize content
        const optimizedContent = await this.contentOptimizer.optimize(content, {
          businessInfo,
          settings,
          position
        })

        const email = new Email(IdGenerator.uuid(), {
          sequencePosition: position,
          content: optimizedContent,
          scheduledDays: this._calculateScheduledDays(position, settings),
          isActive: true
        })

        emails.push(email)

        logger.debug(`Generated email ${position}/${settings.emailCount}`, {
          subject: optimizedContent.subject,
          wordCount: optimizedContent.metadata.wordCount
        })

      } catch (error) {
        logger.error(`Failed to generate email ${position}`, error)
        throw new Error(`Email generation failed at position ${position}: ${error.message}`)
      }
    }

    return emails
  }

  _buildBasePrompt(businessInfo, settings) {
    return {
      businessName: businessInfo.businessName,
      industry: businessInfo.industry,
      targetAudience: businessInfo.targetAudience,
      goals: businessInfo.goals,
      tone: businessInfo.tone,
      sequenceType: settings.sequenceType,
      emailCount: settings.emailCount,
      frequency: settings.frequency,
      personalizeLevel: settings.personalizeLevel,
      includeCallToAction: settings.includeCallToAction,
      ctaType: settings.ctaType
    }
  }

  _buildEmailPrompt(basePrompt, position, settings) {
    const emailContext = this._getEmailContext(position, settings)
    
    return {
      ...basePrompt,
      emailPosition: position,
      totalEmails: settings.emailCount,
      emailPurpose: emailContext.purpose,
      emailGoal: emailContext.goal,
      scheduledDays: this._calculateScheduledDays(position, settings),
      isWelcomeEmail: position === 1,
      isFollowUpEmail: position > 1,
      isFinalEmail: position === settings.emailCount
    }
  }

  _getEmailContext(position, settings) {
    const contexts = {
      'welcome': {
        1: { purpose: 'Welcome and introduce', goal: 'Build rapport and set expectations' },
        2: { purpose: 'Provide value', goal: 'Share helpful resources' },
        3: { purpose: 'Build trust', goal: 'Share testimonials and case studies' },
        4: { purpose: 'Soft pitch', goal: 'Introduce products/services gently' },
        5: { purpose: 'Call to action', goal: 'Encourage next step' }
      },
      'nurture': {
        1: { purpose: 'Value delivery', goal: 'Establish expertise' },
        2: { purpose: 'Education', goal: 'Teach something valuable' },
        3: { purpose: 'Problem solving', goal: 'Address common pain points' },
        4: { purpose: 'Social proof', goal: 'Build credibility' },
        5: { purpose: 'Gentle offer', goal: 'Present solution' }
      },
      'sales': {
        1: { purpose: 'Problem identification', goal: 'Highlight pain points' },
        2: { purpose: 'Solution introduction', goal: 'Present your solution' },
        3: { purpose: 'Benefit explanation', goal: 'Detailed value proposition' },
        4: { purpose: 'Objection handling', goal: 'Address common concerns' },
        5: { purpose: 'Strong CTA', goal: 'Drive conversion' }
      }
    }

    const typeContexts = contexts[settings.sequenceType] || contexts['nurture']
    return typeContexts[position] || { 
      purpose: 'Follow up', 
      goal: 'Continue relationship building' 
    }
  }

  _calculateScheduledDays(position, settings) {
    if (position === 1) return 0 // First email sent immediately
    
    const daysBetween = settings.getDaysBetweenEmails()
    return (position - 1) * daysBetween
  }

  _parseAIResponse(aiResponse, position) {
    // Parse AI response and create EmailContent
    // This would integrate with your actual AI service response format
    
    const lines = aiResponse.split('\n').filter(line => line.trim())
    const subjectLine = lines.find(line => line.toLowerCase().includes('subject:')) || `Email ${position} Subject`
    const subject = subjectLine.replace(/subject:\s*/i, '').trim()
    
    const bodyStartIndex = lines.findIndex(line => 
      line.toLowerCase().includes('body:') || 
      line.toLowerCase().includes('content:')
    )
    
    const body = bodyStartIndex >= 0 
      ? lines.slice(bodyStartIndex + 1).join('\n\n')
      : aiResponse

    return new EmailContent({
      subject,
      body: body.trim(),
      metadata: {
        generatedBy: 'ai',
        position
      }
    })
  }
}

/**
 * Email Content Optimization Service
 */
export class EmailContentOptimizationService extends DomainService {
  constructor(performanceAnalyzer, personalizationEngine) {
    super()
    this.performanceAnalyzer = performanceAnalyzer
    this.personalizationEngine = personalizationEngine
  }

  async optimize(content, context) {
    try {
      logger.debug('Optimizing email content', {
        position: context.position,
        originalWordCount: content.metadata.wordCount
      })

      // Apply various optimizations
      let optimizedContent = content

      // Subject line optimization
      optimizedContent = await this._optimizeSubjectLine(optimizedContent, context)

      // Body content optimization
      optimizedContent = await this._optimizeBodyContent(optimizedContent, context)

      // Personalization
      optimizedContent = await this._addPersonalization(optimizedContent, context)

      // CTA optimization
      if (context.settings.includeCallToAction) {
        optimizedContent = await this._optimizeCTA(optimizedContent, context)
      }

      // Performance validation
      await this._validateOptimizedContent(optimizedContent, context)

      logger.debug('Email content optimized', {
        position: context.position,
        finalWordCount: optimizedContent.metadata.wordCount,
        readabilityScore: optimizedContent.getReadabilityScore()
      })

      return optimizedContent

    } catch (error) {
      logger.error('Email content optimization failed', error)
      throw error
    }
  }

  async _optimizeSubjectLine(content, context) {
    const { settings, businessInfo, position } = context
    
    let subject = content.subject

    // Add personalization tokens based on level
    if (settings.personalizeLevel === 'high') {
      if (!subject.includes('{{')) {
        subject = `{{firstName}}, ${subject}`
      }
    }

    // Optimize length (30-50 characters is optimal)
    if (subject.length > 50) {
      subject = subject.substring(0, 47) + '...'
    }

    // Add urgency for sales sequences
    if (settings.sequenceType === 'sales' && position >= 3) {
      if (!subject.toLowerCase().includes('limited') && 
          !subject.toLowerCase().includes('urgent') &&
          !subject.toLowerCase().includes('today')) {
        subject = `${subject} - Limited Time`
      }
    }

    return content.clone({ subject })
  }

  async _optimizeBodyContent(content, context) {
    let body = content.body

    // Ensure proper paragraph structure
    body = this._optimizeParagraphStructure(body)

    // Add industry-specific terminology
    body = this._addIndustryContext(body, context.businessInfo.industry)

    // Optimize reading level
    body = this._optimizeReadingLevel(body, context.settings.personalizeLevel)

    return content.clone({ body })
  }

  _optimizeParagraphStructure(body) {
    // Ensure paragraphs are not too long (max 3 sentences)
    const sentences = body.split(/[.!?]+/).filter(s => s.trim())
    const paragraphs = []
    let currentParagraph = []

    sentences.forEach(sentence => {
      currentParagraph.push(sentence.trim())
      if (currentParagraph.length >= 3) {
        paragraphs.push(currentParagraph.join('. ') + '.')
        currentParagraph = []
      }
    })

    if (currentParagraph.length > 0) {
      paragraphs.push(currentParagraph.join('. ') + '.')
    }

    return paragraphs.join('\n\n')
  }

  _addIndustryContext(body, industry) {
    const industryTerms = {
      'technology': ['innovation', 'digital transformation', 'efficiency'],
      'healthcare': ['patient care', 'health outcomes', 'wellness'],
      'finance': ['financial growth', 'investment', 'returns'],
      'retail': ['customer experience', 'satisfaction', 'value'],
      'education': ['learning outcomes', 'student success', 'knowledge']
    }

    const terms = industryTerms[industry.toLowerCase()] || []
    // This is a simplified implementation - in practice, you'd use NLP
    return body
  }

  _optimizeReadingLevel(body, personalizeLevel) {
    // Adjust complexity based on personalization level
    if (personalizeLevel === 'low') {
      // Use simpler language
      body = body
        .replace(/utilize/g, 'use')
        .replace(/demonstrate/g, 'show')
        .replace(/facilitate/g, 'help')
    }

    return body
  }

  async _addPersonalization(content, context) {
    const { settings, businessInfo } = context

    if (settings.personalizeLevel === 'low') {
      return content
    }

    let body = content.body

    // Add basic personalization tokens
    if (settings.personalizeLevel === 'medium') {
      body = body.replace(/\byou\b/g, '{{firstName}}')
      body = body.replace(/your business/g, '{{companyName}}')
    }

    // Add advanced personalization
    if (settings.personalizeLevel === 'high') {
      body = `Hi {{firstName}},\n\n${body}`
      body = body.replace(/our industry/g, `the ${businessInfo.industry} industry`)
      body = body.replace(/your industry/g, `{{industry}}`)
    }

    return content.clone({ body })
  }

  async _optimizeCTA(content, context) {
    const { settings } = context

    if (!settings.includeCallToAction || settings.ctaType === 'none') {
      return content
    }

    let body = content.body

    // Ensure CTA is present and properly formatted
    if (!content.hasCallToAction()) {
      const cta = this._generateCTA(settings.ctaType, context)
      body = `${body}\n\n${cta}`
    }

    return content.clone({ body })
  }

  _generateCTA(ctaType, context) {
    const ctas = {
      'soft': [
        "Feel free to reply if you have any questions.",
        "I'd love to hear your thoughts on this.",
        "What's your experience with this?"
      ],
      'medium': [
        "Click here to learn more about how we can help.",
        "Schedule a quick 15-minute call to discuss your needs.",
        "Download our free guide to get started."
      ],
      'strong': [
        "Book your free consultation now - limited spots available!",
        "Start your free trial today and see results immediately.",
        "Get started now - this offer expires soon!"
      ]
    }

    const ctaOptions = ctas[ctaType] || ctas['soft']
    return ctaOptions[Math.floor(Math.random() * ctaOptions.length)]
  }

  async _validateOptimizedContent(content, context) {
    const errors = []

    // Check reading time
    if (content.metadata.estimatedReadTime > 3) {
      errors.push('Email too long - should be under 3 minutes reading time')
    }

    // Check readability
    if (content.getReadabilityScore() === 'difficult') {
      errors.push('Content may be too complex for target audience')
    }

    // Check personalization consistency
    if (context.settings.personalizeLevel === 'high' && !content.metadata.hasPersonalization) {
      errors.push('High personalization level but no personalization tokens found')
    }

    if (errors.length > 0) {
      logger.warn('Content optimization validation warnings', { errors })
      // Don't throw - these are warnings, not hard failures
    }
  }
}

/**
 * Sequence Performance Analysis Service
 */
export class SequencePerformanceAnalysisService extends DomainService {
  constructor(analyticsRepository) {
    super()
    this.analyticsRepository = analyticsRepository
  }

  async analyzeSequencePerformance(sequence) {
    try {
      const performance = sequence.getOverallPerformance()
      if (!performance) {
        return this._createEmptyAnalysis(sequence)
      }

      const analysis = {
        sequenceId: sequence.id,
        analyzedAt: new Date(),
        overall: performance,
        emailAnalysis: await this._analyzeIndividualEmails(sequence),
        recommendations: await this._generateRecommendations(sequence, performance),
        benchmarkComparison: await this._compareToBenchmarks(sequence, performance),
        trends: await this._analyzeTrends(sequence)
      }

      logger.info('Sequence performance analysis completed', {
        sequenceId: sequence.id,
        overallOpenRate: performance.averageOpenRate,
        recommendationCount: analysis.recommendations.length
      })

      return analysis

    } catch (error) {
      logger.error('Sequence performance analysis failed', error)
      throw error
    }
  }

  async _analyzeIndividualEmails(sequence) {
    return sequence.getActiveEmails().map(email => ({
      emailId: email.id,
      position: email.sequencePosition,
      performance: {
        openRate: email.performance.getOpenRate(),
        clickRate: email.performance.getClickRate(),
        bounceRate: email.performance.getBounceRate(),
        unsubscribeRate: email.performance.getUnsubscribeRate()
      },
      isPerformingWell: email.performance.isPerformingWell(),
      needsOptimization: email.performance.needsOptimization(),
      subject: email.content.subject,
      wordCount: email.content.metadata.wordCount,
      readabilityScore: email.content.getReadabilityScore()
    }))
  }

  async _generateRecommendations(sequence, performance) {
    const recommendations = []

    // Low open rate recommendations
    if (performance.averageOpenRate < 20) {
      recommendations.push({
        type: 'optimization',
        priority: 'high',
        category: 'subject_lines',
        title: 'Improve Subject Lines',
        description: 'Your open rates are below industry average',
        actions: [
          'A/B test different subject line approaches',
          'Add personalization to subject lines',
          'Try shorter, more intriguing subjects',
          'Test sending times and days'
        ]
      })
    }

    // Low click rate recommendations
    if (performance.averageClickRate < 2) {
      recommendations.push({
        type: 'optimization',
        priority: 'high',
        category: 'content',
        title: 'Enhance Call-to-Actions',
        description: 'Your click rates suggest weak CTAs',
        actions: [
          'Make CTAs more prominent and compelling',
          'Use action-oriented language',
          'Test different CTA placements',
          'Improve value proposition clarity'
        ]
      })
    }

    // Sequence-specific recommendations
    if (sequence.settings.sequenceType === 'sales' && performance.averageClickRate < 5) {
      recommendations.push({
        type: 'strategy',
        priority: 'medium',
        category: 'sequence_flow',
        title: 'Optimize Sales Funnel',
        description: 'Sales sequences should have higher engagement',
        actions: [
          'Add more value before asking for sale',
          'Include customer testimonials',
          'Address common objections',
          'Create urgency in later emails'
        ]
      })
    }

    return recommendations
  }

  async _compareToBenchmarks(sequence, performance) {
    // Industry benchmarks (would come from database in real implementation)
    const benchmarks = {
      'technology': { openRate: 22, clickRate: 3.5 },
      'healthcare': { openRate: 25, clickRate: 4.2 },
      'finance': { openRate: 20, clickRate: 2.8 },
      'retail': { openRate: 18, clickRate: 2.5 },
      'education': { openRate: 28, clickRate: 5.1 }
    }

    const industry = sequence.businessInfo.industry.toLowerCase()
    const benchmark = benchmarks[industry] || benchmarks['technology']

    return {
      industry,
      benchmark,
      comparison: {
        openRate: {
          yours: performance.averageOpenRate,
          benchmark: benchmark.openRate,
          difference: performance.averageOpenRate - benchmark.openRate,
          status: performance.averageOpenRate >= benchmark.openRate ? 'above' : 'below'
        },
        clickRate: {
          yours: performance.averageClickRate,
          benchmark: benchmark.clickRate,
          difference: performance.averageClickRate - benchmark.clickRate,
          status: performance.averageClickRate >= benchmark.clickRate ? 'above' : 'below'
        }
      }
    }
  }

  async _analyzeTrends(sequence) {
    // This would analyze historical performance data
    return {
      openRateTrend: 'stable',
      clickRateTrend: 'improving',
      engagementTrend: 'stable',
      period: '30 days'
    }
  }

  _createEmptyAnalysis(sequence) {
    return {
      sequenceId: sequence.id,
      analyzedAt: new Date(),
      overall: null,
      emailAnalysis: [],
      recommendations: [{
        type: 'info',
        priority: 'low',
        category: 'data',
        title: 'No Performance Data',
        description: 'Start sending this sequence to collect performance data',
        actions: ['Publish and activate the sequence']
      }],
      benchmarkComparison: null,
      trends: null
    }
  }
}

// ========================================
// SPECIFICATIONS
// ========================================

/**
 * Specification for sequences ready to publish
 */
export class SequenceReadyToPublishSpecification extends Specification {
  isSatisfiedBy(sequence) {
    if (!(sequence instanceof EmailSequence)) return false
    
    // Must have minimum number of emails
    if (sequence.getEmailCount() < 3) return false
    
    // All emails must be active
    const activeEmails = sequence.getActiveEmails()
    if (activeEmails.length !== sequence.getEmailCount()) return false
    
    // All emails must have content
    const hasContent = activeEmails.every(email => 
      email.content && 
      email.content.subject && 
      email.content.body &&
      email.content.subject.trim().length >= 5 &&
      email.content.body.trim().length >= 50
    )
    
    if (!hasContent) return false
    
    // Business info must be complete
    if (!sequence.businessInfo.isComplete()) return false
    
    return true
  }
}

/**
 * Specification for high-performing sequences
 */
export class HighPerformingSequenceSpecification extends Specification {
  constructor(minOpenRate = 25, minClickRate = 3) {
    super()
    this.minOpenRate = minOpenRate
    this.minClickRate = minClickRate
  }

  isSatisfiedBy(sequence) {
    if (!(sequence instanceof EmailSequence)) return false
    
    const performance = sequence.getOverallPerformance()
    if (!performance) return false
    
    return performance.averageOpenRate >= this.minOpenRate &&
           performance.averageClickRate >= this.minClickRate
  }
}

/**
 * Specification for sequences needing optimization
 */
export class SequenceNeedsOptimizationSpecification extends Specification {
  constructor(maxOpenRate = 15, maxClickRate = 1) {
    super()
    this.maxOpenRate = maxOpenRate
    this.maxClickRate = maxClickRate
  }

  isSatisfiedBy(sequence) {
    if (!(sequence instanceof EmailSequence)) return false
    
    const performance = sequence.getOverallPerformance()
    if (!performance) return false
    
    return performance.averageOpenRate <= this.maxOpenRate ||
           performance.averageClickRate <= this.maxClickRate
  }
}