/**
 * 🔗 Abstraction Layers for External Dependencies
 * Provides vendor-agnostic interfaces for easy technology swapping
 */

import { logger } from '../backend/utils/logger.js'
import { EventEmitter } from 'events'

// ========================================
// AI SERVICE ABSTRACTION LAYER
// ========================================

/**
 * AI Service Provider Interface
 */
export class AIServiceProvider {
  async generateContent(prompt, options = {}) {
    throw new Error('generateContent must be implemented by provider')
  }

  async analyzeContent(content, analysisType = 'general') {
    throw new Error('analyzeContent must be implemented by provider')
  }

  async getUsageStats() {
    throw new Error('getUsageStats must be implemented by provider')
  }

  async checkHealth() {
    throw new Error('checkHealth must be implemented by provider')
  }
}

/**
 * OpenAI Provider Implementation
 */
export class OpenAIProvider extends AIServiceProvider {
  constructor(apiKey, config = {}) {
    super()
    this.apiKey = apiKey
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1'
    this.model = config.model || 'gpt-4'
    this.maxTokens = config.maxTokens || 2000
    this.temperature = config.temperature || 0.7
    this.requestTimeout = config.timeout || 30000
  }

  async generateContent(prompt, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: options.model || this.model,
          messages: [{ role: 'user', content: prompt }],
          max_tokens: options.maxTokens || this.maxTokens,
          temperature: options.temperature || this.temperature
        }),
        signal: AbortSignal.timeout(this.requestTimeout)
      })

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      return {
        content: data.choices[0]?.message?.content || '',
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        model: data.model,
        finishReason: data.choices[0]?.finish_reason
      }

    } catch (error) {
      logger.error('OpenAI generation error', error)
      throw new AIServiceError('OpenAI', 'generation', error)
    }
  }

  async analyzeContent(content, analysisType = 'general') {
    const analysisPrompts = {
      sentiment: `Analyze the sentiment of this content and respond with only: positive, negative, or neutral\n\nContent: ${content}`,
      readability: `Rate the readability of this content from 1-10 and explain why\n\nContent: ${content}`,
      general: `Analyze this content for quality, clarity, and effectiveness\n\nContent: ${content}`
    }

    const prompt = analysisPrompts[analysisType] || analysisPrompts.general
    
    try {
      const result = await this.generateContent(prompt, { maxTokens: 500 })
      return {
        type: analysisType,
        analysis: result.content,
        confidence: 0.8 // OpenAI doesn't provide confidence scores
      }
    } catch (error) {
      logger.error('OpenAI analysis error', error)
      throw new AIServiceError('OpenAI', 'analysis', error)
    }
  }

  async getUsageStats() {
    // OpenAI doesn't provide usage stats via API
    // This would need to be tracked internally
    return {
      provider: 'OpenAI',
      requestsToday: 0,
      tokensUsedToday: 0,
      costToday: 0
    }
  }

  async checkHealth() {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: { 'Authorization': `Bearer ${this.apiKey}` },
        signal: AbortSignal.timeout(5000)
      })

      return {
        healthy: response.ok,
        responseTime: Date.now(),
        status: response.status
      }
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      }
    }
  }
}

/**
 * Anthropic Provider Implementation
 */
export class AnthropicProvider extends AIServiceProvider {
  constructor(apiKey, config = {}) {
    super()
    this.apiKey = apiKey
    this.baseUrl = config.baseUrl || 'https://api.anthropic.com/v1'
    this.model = config.model || 'claude-3-sonnet-20240229'
    this.maxTokens = config.maxTokens || 2000
    this.requestTimeout = config.timeout || 30000
  }

  async generateContent(prompt, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: options.model || this.model,
          max_tokens: options.maxTokens || this.maxTokens,
          messages: [{ role: 'user', content: prompt }]
        }),
        signal: AbortSignal.timeout(this.requestTimeout)
      })

      if (!response.ok) {
        throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      return {
        content: data.content[0]?.text || '',
        usage: {
          promptTokens: data.usage?.input_tokens || 0,
          completionTokens: data.usage?.output_tokens || 0,
          totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
        },
        model: data.model,
        finishReason: data.stop_reason
      }

    } catch (error) {
      logger.error('Anthropic generation error', error)
      throw new AIServiceError('Anthropic', 'generation', error)
    }
  }

  async analyzeContent(content, analysisType = 'general') {
    // Similar implementation to OpenAI but with Anthropic-specific formatting
    const result = await this.generateContent(`Analyze this content: ${content}`)
    return {
      type: analysisType,
      analysis: result.content,
      confidence: 0.85
    }
  }

  async checkHealth() {
    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: this.model,
          max_tokens: 10,
          messages: [{ role: 'user', content: 'test' }]
        }),
        signal: AbortSignal.timeout(5000)
      })

      return {
        healthy: response.ok,
        responseTime: Date.now(),
        status: response.status
      }
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      }
    }
  }
}

/**
 * AI Service Manager - Handles multiple providers with failover
 */
export class AIServiceManager extends EventEmitter {
  constructor() {
    super()
    this.providers = new Map()
    this.primaryProvider = null
    this.fallbackProviders = []
    this.healthChecks = new Map()
    this.circuitBreakers = new Map()
    this.loadBalancer = new LoadBalancer()
  }

  // Register AI provider
  registerProvider(name, provider, config = {}) {
    this.providers.set(name, provider)
    
    // Setup circuit breaker
    this.circuitBreakers.set(name, new CircuitBreaker({
      failureThreshold: config.failureThreshold || 5,
      resetTimeout: config.resetTimeout || 60000
    }))

    // Setup health monitoring
    if (config.healthCheckInterval) {
      this._setupHealthCheck(name, provider, config.healthCheckInterval)
    }

    logger.info(`AI provider registered: ${name}`, config)
  }

  // Set primary provider
  setPrimaryProvider(name) {
    if (!this.providers.has(name)) {
      throw new Error(`Provider not found: ${name}`)
    }
    this.primaryProvider = name
    logger.info(`Primary AI provider set: ${name}`)
  }

  // Add fallback provider
  addFallbackProvider(name) {
    if (!this.providers.has(name)) {
      throw new Error(`Provider not found: ${name}`)
    }
    this.fallbackProviders.push(name)
    logger.info(`Fallback AI provider added: ${name}`)
  }

  // Generate content with automatic failover
  async generateContent(prompt, options = {}) {
    const providers = [this.primaryProvider, ...this.fallbackProviders].filter(Boolean)
    
    for (const providerName of providers) {
      const provider = this.providers.get(providerName)
      const circuitBreaker = this.circuitBreakers.get(providerName)

      if (circuitBreaker.isOpen()) {
        logger.warn(`Circuit breaker open for provider: ${providerName}`)
        continue
      }

      try {
        const result = await circuitBreaker.execute(() => 
          provider.generateContent(prompt, { ...options, provider: providerName })
        )

        this.emit('contentGenerated', { provider: providerName, success: true })
        return result

      } catch (error) {
        logger.error(`AI generation failed with provider: ${providerName}`, error)
        this.emit('contentGenerated', { provider: providerName, success: false, error })
        
        // Continue to next provider
        continue
      }
    }

    throw new Error('All AI providers failed')
  }

  // Get provider status
  getProviderStatus() {
    const status = {}
    
    for (const [name, provider] of this.providers) {
      const circuitBreaker = this.circuitBreakers.get(name)
      const health = this.healthChecks.get(name)
      
      status[name] = {
        available: !circuitBreaker.isOpen(),
        healthy: health?.healthy || false,
        lastHealthCheck: health?.lastCheck,
        circuitBreakerState: circuitBreaker.getState(),
        isPrimary: name === this.primaryProvider,
        isFallback: this.fallbackProviders.includes(name)
      }
    }
    
    return status
  }

  // Setup health monitoring
  _setupHealthCheck(name, provider, interval) {
    const checkHealth = async () => {
      try {
        const health = await provider.checkHealth()
        this.healthChecks.set(name, {
          healthy: health.healthy,
          lastCheck: new Date(),
          details: health
        })

        if (!health.healthy) {
          this.emit('providerUnhealthy', { provider: name, health })
        }

      } catch (error) {
        this.healthChecks.set(name, {
          healthy: false,
          lastCheck: new Date(),
          error: error.message
        })
        this.emit('providerUnhealthy', { provider: name, error })
      }
    }

    // Initial check
    checkHealth()
    
    // Periodic checks
    setInterval(checkHealth, interval)
  }
}

/**
 * Circuit Breaker Pattern Implementation
 */
class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5
    this.resetTimeout = options.resetTimeout || 60000
    this.state = 'CLOSED' // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0
    this.lastFailureTime = null
  }

  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN'
        this.failureCount = 0
      } else {
        throw new Error('Circuit breaker is OPEN')
      }
    }

    try {
      const result = await operation()
      
      if (this.state === 'HALF_OPEN') {
        this.state = 'CLOSED'
        this.failureCount = 0
      }
      
      return result

    } catch (error) {
      this.failureCount++
      this.lastFailureTime = Date.now()

      if (this.failureCount >= this.failureThreshold) {
        this.state = 'OPEN'
      }

      throw error
    }
  }

  isOpen() {
    return this.state === 'OPEN'
  }

  getState() {
    return this.state
  }
}

/**
 * Load Balancer for Provider Selection
 */
class LoadBalancer {
  constructor(strategy = 'round_robin') {
    this.strategy = strategy
    this.roundRobinIndex = 0
    this.requestCounts = new Map()
  }

  selectProvider(providers, weights = {}) {
    const availableProviders = providers.filter(p => p !== null)
    
    if (availableProviders.length === 0) {
      throw new Error('No providers available')
    }

    switch (this.strategy) {
      case 'round_robin':
        return this._roundRobin(availableProviders)
      case 'weighted':
        return this._weighted(availableProviders, weights)
      case 'least_connections':
        return this._leastConnections(availableProviders)
      default:
        return availableProviders[0]
    }
  }

  _roundRobin(providers) {
    const provider = providers[this.roundRobinIndex % providers.length]
    this.roundRobinIndex++
    return provider
  }

  _weighted(providers, weights) {
    const totalWeight = providers.reduce((sum, p) => sum + (weights[p] || 1), 0)
    let random = Math.random() * totalWeight
    
    for (const provider of providers) {
      random -= weights[provider] || 1
      if (random <= 0) {
        return provider
      }
    }
    
    return providers[0]
  }

  _leastConnections(providers) {
    return providers.reduce((least, current) => {
      const leastCount = this.requestCounts.get(least) || 0
      const currentCount = this.requestCounts.get(current) || 0
      return currentCount < leastCount ? current : least
    })
  }
}

// ========================================
// EMAIL SERVICE ABSTRACTION LAYER
// ========================================

/**
 * Email Service Provider Interface
 */
export class EmailServiceProvider {
  async sendEmail(emailData) {
    throw new Error('sendEmail must be implemented by provider')
  }

  async sendBulkEmails(emailsData) {
    throw new Error('sendBulkEmails must be implemented by provider')
  }

  async getDeliveryStatus(messageId) {
    throw new Error('getDeliveryStatus must be implemented by provider')
  }

  async getStatistics(timeRange = '24h') {
    throw new Error('getStatistics must be implemented by provider')
  }
}

/**
 * SendGrid Provider Implementation
 */
export class SendGridProvider extends EmailServiceProvider {
  constructor(apiKey, config = {}) {
    super()
    this.apiKey = apiKey
    this.baseUrl = 'https://api.sendgrid.com/v3'
    this.defaultFrom = config.defaultFrom
  }

  async sendEmail(emailData) {
    try {
      const response = await fetch(`${this.baseUrl}/mail/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: emailData.to }],
            subject: emailData.subject
          }],
          from: { email: emailData.from || this.defaultFrom },
          content: [{
            type: emailData.contentType || 'text/html',
            value: emailData.content
          }]
        })
      })

      const messageId = response.headers.get('x-message-id')
      
      return {
        messageId,
        status: response.ok ? 'sent' : 'failed',
        provider: 'SendGrid'
      }

    } catch (error) {
      logger.error('SendGrid send error', error)
      throw new EmailServiceError('SendGrid', 'send', error)
    }
  }

  async getStatistics(timeRange = '24h') {
    try {
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date(Date.now() - this._parseTimeRange(timeRange))
        .toISOString().split('T')[0]

      const response = await fetch(
        `${this.baseUrl}/stats?start_date=${startDate}&end_date=${endDate}`,
        {
          headers: { 'Authorization': `Bearer ${this.apiKey}` }
        }
      )

      const data = await response.json()
      
      return {
        provider: 'SendGrid',
        sent: data.reduce((sum, stat) => sum + (stat.stats[0]?.metrics?.requests || 0), 0),
        delivered: data.reduce((sum, stat) => sum + (stat.stats[0]?.metrics?.delivered || 0), 0),
        bounced: data.reduce((sum, stat) => sum + (stat.stats[0]?.metrics?.bounces || 0), 0),
        opens: data.reduce((sum, stat) => sum + (stat.stats[0]?.metrics?.unique_opens || 0), 0)
      }

    } catch (error) {
      logger.error('SendGrid stats error', error)
      return { provider: 'SendGrid', error: error.message }
    }
  }

  _parseTimeRange(timeRange) {
    const units = { 'h': 3600000, 'd': 86400000, 'w': 604800000 }
    const match = timeRange.match(/^(\d+)([hdw])$/)
    return match ? parseInt(match[1]) * units[match[2]] : 86400000
  }
}

/**
 * SMTP Provider Implementation
 */
export class SMTPProvider extends EmailServiceProvider {
  constructor(config) {
    super()
    this.config = config
    this.transporter = this._createTransporter()
  }

  async sendEmail(emailData) {
    try {
      const info = await this.transporter.sendMail({
        from: emailData.from || this.config.defaultFrom,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.content
      })

      return {
        messageId: info.messageId,
        status: 'sent',
        provider: 'SMTP'
      }

    } catch (error) {
      logger.error('SMTP send error', error)
      throw new EmailServiceError('SMTP', 'send', error)
    }
  }

  _createTransporter() {
    // This would use nodemailer or similar
    // Implementation depends on specific SMTP configuration
    return {
      sendMail: async (mailOptions) => ({
        messageId: `smtp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })
    }
  }
}

// ========================================
// PAYMENT SERVICE ABSTRACTION LAYER
// ========================================

/**
 * Payment Service Provider Interface
 */
export class PaymentServiceProvider {
  async createCustomer(customerData) {
    throw new Error('createCustomer must be implemented by provider')
  }

  async createSubscription(subscriptionData) {
    throw new Error('createSubscription must be implemented by provider')
  }

  async processPayment(paymentData) {
    throw new Error('processPayment must be implemented by provider')
  }

  async getPaymentStatus(paymentId) {
    throw new Error('getPaymentStatus must be implemented by provider')
  }

  async handleWebhook(payload, signature) {
    throw new Error('handleWebhook must be implemented by provider')
  }
}

/**
 * Stripe Provider Implementation
 */
export class StripeProvider extends PaymentServiceProvider {
  constructor(secretKey, config = {}) {
    super()
    this.secretKey = secretKey
    this.baseUrl = 'https://api.stripe.com/v1'
    this.webhookSecret = config.webhookSecret
  }

  async createCustomer(customerData) {
    try {
      const response = await this._stripeRequest('customers', 'POST', {
        email: customerData.email,
        name: customerData.name,
        metadata: customerData.metadata || {}
      })

      return {
        customerId: response.id,
        provider: 'Stripe'
      }

    } catch (error) {
      logger.error('Stripe create customer error', error)
      throw new PaymentServiceError('Stripe', 'createCustomer', error)
    }
  }

  async createSubscription(subscriptionData) {
    try {
      const response = await this._stripeRequest('subscriptions', 'POST', {
        customer: subscriptionData.customerId,
        items: [{ price: subscriptionData.priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent']
      })

      return {
        subscriptionId: response.id,
        clientSecret: response.latest_invoice.payment_intent.client_secret,
        provider: 'Stripe'
      }

    } catch (error) {
      logger.error('Stripe create subscription error', error)
      throw new PaymentServiceError('Stripe', 'createSubscription', error)
    }
  }

  async _stripeRequest(endpoint, method, data = null) {
    const url = `${this.baseUrl}/${endpoint}`
    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${this.secretKey}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }

    if (data && method !== 'GET') {
      options.body = new URLSearchParams(data).toString()
    }

    const response = await fetch(url, options)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Stripe API error: ${error.error.message}`)
    }

    return await response.json()
  }
}

// ========================================
// ERROR CLASSES
// ========================================

class AIServiceError extends Error {
  constructor(provider, operation, originalError) {
    super(`AI Service error (${provider}): ${operation} failed`)
    this.name = 'AIServiceError'
    this.provider = provider
    this.operation = operation
    this.originalError = originalError
  }
}

class EmailServiceError extends Error {
  constructor(provider, operation, originalError) {
    super(`Email Service error (${provider}): ${operation} failed`)
    this.name = 'EmailServiceError'
    this.provider = provider
    this.operation = operation
    this.originalError = originalError
  }
}

class PaymentServiceError extends Error {
  constructor(provider, operation, originalError) {
    super(`Payment Service error (${provider}): ${operation} failed`)
    this.name = 'PaymentServiceError'
    this.provider = provider
    this.operation = operation
    this.originalError = originalError
  }
}

// ========================================
// SERVICE REGISTRY
// ========================================

/**
 * Service Registry - Central management of all abstracted services
 */
export class ServiceRegistry extends EventEmitter {
  constructor() {
    super()
    this.services = new Map()
    this.healthMonitor = new HealthMonitor()
  }

  // Register service with abstraction layer
  registerService(type, name, provider, config = {}) {
    if (!this.services.has(type)) {
      this.services.set(type, new Map())
    }

    this.services.get(type).set(name, {
      provider,
      config,
      registeredAt: new Date(),
      active: config.active !== false
    })

    // Setup health monitoring
    if (config.healthCheck) {
      this.healthMonitor.addService(type, name, provider, config.healthCheck)
    }

    logger.info(`Service registered: ${type}/${name}`, config)
    this.emit('serviceRegistered', { type, name, config })
  }

  // Get service instance
  getService(type, name = 'default') {
    const serviceType = this.services.get(type)
    if (!serviceType) {
      throw new Error(`Service type not found: ${type}`)
    }

    const service = serviceType.get(name)
    if (!service) {
      throw new Error(`Service not found: ${type}/${name}`)
    }

    if (!service.active) {
      throw new Error(`Service is inactive: ${type}/${name}`)
    }

    return service.provider
  }

  // Get all services of a type
  getServicesOfType(type) {
    const services = this.services.get(type)
    return services ? Array.from(services.entries()) : []
  }

  // Activate/deactivate service
  setServiceActive(type, name, active) {
    const service = this.services.get(type)?.get(name)
    if (service) {
      service.active = active
      this.emit('serviceStatusChanged', { type, name, active })
    }
  }

  // Get registry status
  getRegistryStatus() {
    const status = {}
    
    for (const [type, services] of this.services) {
      status[type] = {}
      for (const [name, service] of services) {
        status[type][name] = {
          active: service.active,
          registeredAt: service.registeredAt,
          health: this.healthMonitor.getServiceHealth(type, name)
        }
      }
    }
    
    return status
  }
}

/**
 * Health Monitor for Services
 */
class HealthMonitor {
  constructor() {
    this.services = new Map()
    this.healthStatus = new Map()
    this.checkInterval = 30000 // 30 seconds
  }

  addService(type, name, provider, healthConfig) {
    const key = `${type}/${name}`
    
    this.services.set(key, {
      provider,
      config: healthConfig,
      lastCheck: null
    })

    this._startHealthCheck(key)
  }

  getServiceHealth(type, name) {
    const key = `${type}/${name}`
    return this.healthStatus.get(key) || { status: 'unknown' }
  }

  async _startHealthCheck(serviceKey) {
    const checkHealth = async () => {
      const service = this.services.get(serviceKey)
      if (!service) return

      try {
        const health = await service.provider.checkHealth()
        
        this.healthStatus.set(serviceKey, {
          status: health.healthy ? 'healthy' : 'unhealthy',
          lastCheck: new Date(),
          details: health
        })

      } catch (error) {
        this.healthStatus.set(serviceKey, {
          status: 'error',
          lastCheck: new Date(),
          error: error.message
        })
      }
    }

    // Initial check
    await checkHealth()
    
    // Periodic checks
    setInterval(checkHealth, this.checkInterval)
  }
}

// ========================================
// EXPORTS
// ========================================

export {
  AIServiceManager,
  OpenAIProvider,
  AnthropicProvider,
  SendGridProvider,
  SMTPProvider,
  StripeProvider,
  ServiceRegistry
}

// Create global service registry instance
export const serviceRegistry = new ServiceRegistry()

logger.info('🔗 Abstraction layers for external dependencies initialized')