/**
 * 🚀 Technology Migration Strategy Framework
 * Handles gradual technology evolution while maintaining backward compatibility
 */

import { logger } from '../backend/utils/logger.js'
import { EventEmitter } from 'events'

// ========================================
// MIGRATION FRAMEWORK CORE
// ========================================

/**
 * Migration Manager - Orchestrates technology migrations
 */
export class MigrationManager extends EventEmitter {
  constructor() {
    super()
    this.migrations = new Map()
    this.activeMigrations = new Set()
    this.migrationHistory = []
    this.rollbackStrategies = new Map()
    this.healthChecks = new Map()
  }

  // Register a migration strategy
  registerMigration(name, migrationConfig) {
    const migration = new Migration(name, migrationConfig)
    this.migrations.set(name, migration)
    this.rollbackStrategies.set(name, migrationConfig.rollback)
    this.healthChecks.set(name, migrationConfig.healthCheck)
    
    logger.info(`Migration strategy registered: ${name}`, {
      type: migrationConfig.type,
      criticality: migrationConfig.criticality
    })
  }

  // Execute migration with safety controls
  async executeMigration(name, options = {}) {
    const migration = this.migrations.get(name)
    if (!migration) {
      throw new Error(`Migration not found: ${name}`)
    }

    if (this.activeMigrations.has(name)) {
      throw new Error(`Migration already in progress: ${name}`)
    }

    try {
      this.activeMigrations.add(name)
      this.emit('migrationStarted', { name, options })

      logger.info(`Starting migration: ${name}`, options)

      // Pre-migration health check
      await this._preHealthCheck(name)

      // Execute migration phases
      const result = await migration.execute(options)

      // Post-migration validation
      await this._postHealthCheck(name)

      // Record successful migration
      this.migrationHistory.push({
        name,
        timestamp: new Date(),
        status: 'completed',
        result,
        options
      })

      this.emit('migrationCompleted', { name, result })
      logger.info(`Migration completed successfully: ${name}`, result)

      return result

    } catch (error) {
      logger.error(`Migration failed: ${name}`, error)
      
      // Attempt automatic rollback
      if (options.autoRollback !== false) {
        await this._attemptRollback(name, error)
      }

      this.migrationHistory.push({
        name,
        timestamp: new Date(),
        status: 'failed',
        error: error.message,
        options
      })

      this.emit('migrationFailed', { name, error })
      throw error

    } finally {
      this.activeMigrations.delete(name)
    }
  }

  // Rollback migration
  async rollbackMigration(name, reason = 'Manual rollback') {
    const rollbackStrategy = this.rollbackStrategies.get(name)
    if (!rollbackStrategy) {
      throw new Error(`No rollback strategy found for: ${name}`)
    }

    logger.info(`Starting rollback: ${name}`, { reason })

    try {
      const result = await rollbackStrategy.execute()
      
      this.migrationHistory.push({
        name,
        timestamp: new Date(),
        status: 'rolled_back',
        reason,
        result
      })

      this.emit('migrationRolledBack', { name, reason, result })
      return result

    } catch (error) {
      logger.error(`Rollback failed: ${name}`, error)
      this.emit('rollbackFailed', { name, error })
      throw error
    }
  }

  // Pre-migration health check
  async _preHealthCheck(name) {
    const healthCheck = this.healthChecks.get(name)
    if (healthCheck && healthCheck.pre) {
      const result = await healthCheck.pre()
      if (!result.healthy) {
        throw new Error(`Pre-migration health check failed: ${result.reason}`)
      }
    }
  }

  // Post-migration validation
  async _postHealthCheck(name) {
    const healthCheck = this.healthChecks.get(name)
    if (healthCheck && healthCheck.post) {
      const result = await healthCheck.post()
      if (!result.healthy) {
        throw new Error(`Post-migration health check failed: ${result.reason}`)
      }
    }
  }

  // Automatic rollback on failure
  async _attemptRollback(name, originalError) {
    try {
      logger.warn(`Attempting automatic rollback for failed migration: ${name}`)
      await this.rollbackMigration(name, `Auto-rollback due to: ${originalError.message}`)
    } catch (rollbackError) {
      logger.error(`Automatic rollback failed: ${name}`, rollbackError)
      // Emit critical alert - manual intervention required
      this.emit('criticalMigrationFailure', { name, originalError, rollbackError })
    }
  }

  // Get migration status
  getMigrationStatus() {
    return {
      activeMigrations: Array.from(this.activeMigrations),
      registeredMigrations: Array.from(this.migrations.keys()),
      history: this.migrationHistory.slice(-20), // Last 20 migrations
      lastMigration: this.migrationHistory[this.migrationHistory.length - 1]
    }
  }
}

/**
 * Individual Migration Implementation
 */
class Migration {
  constructor(name, config) {
    this.name = name
    this.type = config.type
    this.criticality = config.criticality || 'medium'
    this.phases = config.phases || []
    this.prerequisites = config.prerequisites || []
    this.estimatedDuration = config.estimatedDuration
    this.rollbackWindow = config.rollbackWindow || '24h'
  }

  async execute(options = {}) {
    // Check prerequisites
    await this._checkPrerequisites()

    const results = []
    let currentPhase = 0

    try {
      for (const phase of this.phases) {
        logger.info(`Executing migration phase: ${this.name} - ${phase.name}`)
        
        const phaseResult = await this._executePhase(phase, options)
        results.push({
          phase: phase.name,
          status: 'completed',
          result: phaseResult,
          timestamp: new Date()
        })
        
        currentPhase++

        // Wait between phases if specified
        if (phase.waitAfter) {
          await this._wait(phase.waitAfter)
        }
      }

      return {
        migration: this.name,
        phases: results,
        status: 'completed',
        duration: this._calculateDuration(results)
      }

    } catch (error) {
      // Mark failed phase
      results.push({
        phase: this.phases[currentPhase]?.name || 'unknown',
        status: 'failed',
        error: error.message,
        timestamp: new Date()
      })

      throw new MigrationError(this.name, currentPhase, error, results)
    }
  }

  async _checkPrerequisites() {
    for (const prerequisite of this.prerequisites) {
      const result = await prerequisite.check()
      if (!result.satisfied) {
        throw new Error(`Prerequisite not satisfied: ${prerequisite.name} - ${result.reason}`)
      }
    }
  }

  async _executePhase(phase, options) {
    const startTime = Date.now()
    
    try {
      const result = await phase.execute(options)
      
      // Verify phase completion
      if (phase.verify) {
        const verification = await phase.verify(result)
        if (!verification.success) {
          throw new Error(`Phase verification failed: ${verification.reason}`)
        }
      }

      return {
        ...result,
        duration: Date.now() - startTime,
        verified: !!phase.verify
      }

    } catch (error) {
      // Attempt phase rollback if available
      if (phase.rollback) {
        try {
          await phase.rollback()
          logger.info(`Phase rollback completed: ${phase.name}`)
        } catch (rollbackError) {
          logger.error(`Phase rollback failed: ${phase.name}`, rollbackError)
        }
      }
      throw error
    }
  }

  _wait(duration) {
    const ms = this._parseDuration(duration)
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  _parseDuration(duration) {
    if (typeof duration === 'number') return duration
    
    const units = {
      's': 1000,
      'm': 60 * 1000,
      'h': 60 * 60 * 1000
    }
    
    const match = duration.match(/^(\d+)([smh])$/)
    if (match) {
      const [, value, unit] = match
      return parseInt(value) * units[unit]
    }
    
    return 0
  }

  _calculateDuration(results) {
    if (results.length === 0) return 0
    
    const start = new Date(results[0].timestamp)
    const end = new Date(results[results.length - 1].timestamp)
    return end.getTime() - start.getTime()
  }
}

/**
 * Migration Error with context
 */
class MigrationError extends Error {
  constructor(migrationName, failedPhase, originalError, phaseResults) {
    super(`Migration failed: ${migrationName} at phase ${failedPhase}`)
    this.name = 'MigrationError'
    this.migrationName = migrationName
    this.failedPhase = failedPhase
    this.originalError = originalError
    this.phaseResults = phaseResults
  }
}

// ========================================
// BACKWARD COMPATIBILITY FRAMEWORK
// ========================================

/**
 * Compatibility Layer Manager
 */
export class CompatibilityManager {
  constructor() {
    this.adapters = new Map()
    this.deprecationSchedule = new Map()
    this.supportMatrix = new Map()
    this.usageTracking = new Map()
  }

  // Register compatibility adapter
  registerAdapter(name, config) {
    const adapter = new CompatibilityAdapter(name, config)
    this.adapters.set(name, adapter)
    
    // Track support timeline
    if (config.deprecationDate) {
      this.deprecationSchedule.set(name, {
        deprecationDate: new Date(config.deprecationDate),
        removalDate: new Date(config.removalDate),
        migrationPath: config.migrationPath
      })
    }

    logger.info(`Compatibility adapter registered: ${name}`, {
      version: config.version,
      deprecationDate: config.deprecationDate
    })
  }

  // Handle legacy request
  async handleLegacyRequest(adapterId, request, context = {}) {
    const adapter = this.adapters.get(adapterId)
    if (!adapter) {
      throw new Error(`Compatibility adapter not found: ${adapterId}`)
    }

    // Track usage for deprecation planning
    this._trackUsage(adapterId, context)

    // Check if deprecated
    const deprecation = this.deprecationSchedule.get(adapterId)
    if (deprecation && this._isDeprecated(deprecation)) {
      logger.warn(`Using deprecated adapter: ${adapterId}`, {
        deprecationDate: deprecation.deprecationDate,
        removalDate: deprecation.removalDate,
        migrationPath: deprecation.migrationPath
      })
    }

    return await adapter.handle(request, context)
  }

  // Get compatibility status
  getCompatibilityStatus() {
    const status = {
      adapters: [],
      deprecations: [],
      usageStats: {},
      supportMatrix: Object.fromEntries(this.supportMatrix)
    }

    for (const [name, adapter] of this.adapters) {
      const deprecation = this.deprecationSchedule.get(name)
      const usage = this.usageTracking.get(name) || { count: 0, lastUsed: null }

      status.adapters.push({
        name,
        version: adapter.version,
        active: adapter.active,
        usage: usage.count,
        lastUsed: usage.lastUsed
      })

      if (deprecation) {
        status.deprecations.push({
          name,
          deprecationDate: deprecation.deprecationDate,
          removalDate: deprecation.removalDate,
          daysUntilRemoval: this._daysUntilRemoval(deprecation.removalDate),
          migrationPath: deprecation.migrationPath
        })
      }
    }

    return status
  }

  // Track adapter usage
  _trackUsage(adapterId, context) {
    const current = this.usageTracking.get(adapterId) || { count: 0, lastUsed: null }
    this.usageTracking.set(adapterId, {
      count: current.count + 1,
      lastUsed: new Date(),
      lastContext: context
    })
  }

  // Check if deprecated
  _isDeprecated(deprecation) {
    return new Date() >= deprecation.deprecationDate
  }

  // Calculate days until removal
  _daysUntilRemoval(removalDate) {
    const now = new Date()
    const removal = new Date(removalDate)
    const diffTime = removal.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }
}

/**
 * Individual Compatibility Adapter
 */
class CompatibilityAdapter {
  constructor(name, config) {
    this.name = name
    this.version = config.version
    this.targetVersion = config.targetVersion
    this.transformer = config.transformer
    this.validator = config.validator
    this.active = config.active !== false
  }

  async handle(request, context) {
    if (!this.active) {
      throw new Error(`Compatibility adapter is disabled: ${this.name}`)
    }

    try {
      // Validate legacy request format
      if (this.validator) {
        const validation = await this.validator(request)
        if (!validation.valid) {
          throw new Error(`Legacy request validation failed: ${validation.error}`)
        }
      }

      // Transform to current format
      const transformedRequest = await this.transformer.toLegacy 
        ? request // Already in legacy format
        : await this.transformer.fromLegacy(request)

      // Process with current system
      const result = await this._processWithCurrentSystem(transformedRequest, context)

      // Transform response back to legacy format if needed
      const legacyResponse = this.transformer.toLegacy 
        ? await this.transformer.toLegacy(result)
        : result

      return legacyResponse

    } catch (error) {
      logger.error(`Compatibility adapter error: ${this.name}`, error)
      throw new CompatibilityError(this.name, error)
    }
  }

  async _processWithCurrentSystem(request, context) {
    // This would delegate to the current system implementation
    // Implementation depends on specific system being adapted
    return { adapted: true, request, context }
  }
}

/**
 * Compatibility Error
 */
class CompatibilityError extends Error {
  constructor(adapterName, originalError) {
    super(`Compatibility adapter failed: ${adapterName}`)
    this.name = 'CompatibilityError'
    this.adapterName = adapterName
    this.originalError = originalError
  }
}

// ========================================
// TECHNOLOGY EVOLUTION PATTERNS
// ========================================

/**
 * Technology Evolution Roadmap
 */
export class TechnologyRoadmap {
  constructor() {
    this.technologies = new Map()
    this.evolutionPaths = new Map()
    this.evaluationCriteria = new Map()
    this.adoptionTimelines = new Map()
  }

  // Register technology for evaluation
  registerTechnology(name, config) {
    this.technologies.set(name, {
      name,
      category: config.category,
      maturity: config.maturity,
      adoptionCost: config.adoptionCost,
      benefits: config.benefits,
      risks: config.risks,
      dependencies: config.dependencies || [],
      alternatives: config.alternatives || []
    })

    logger.info(`Technology registered for evaluation: ${name}`, {
      category: config.category,
      maturity: config.maturity
    })
  }

  // Define evolution path
  defineEvolutionPath(name, path) {
    this.evolutionPaths.set(name, {
      name,
      fromTechnology: path.from,
      toTechnology: path.to,
      phases: path.phases,
      estimatedDuration: path.estimatedDuration,
      prerequisites: path.prerequisites || [],
      risks: path.risks || [],
      rollbackStrategy: path.rollbackStrategy
    })
  }

  // Evaluate technology adoption
  async evaluateTechnology(name, currentContext = {}) {
    const technology = this.technologies.get(name)
    if (!technology) {
      throw new Error(`Technology not found: ${name}`)
    }

    const evaluation = {
      technology: name,
      timestamp: new Date(),
      context: currentContext,
      scores: {},
      recommendation: null,
      reasoning: []
    }

    // Evaluate based on criteria
    for (const [criteriaName, criteria] of this.evaluationCriteria) {
      const score = await criteria.evaluate(technology, currentContext)
      evaluation.scores[criteriaName] = score
    }

    // Calculate overall recommendation
    evaluation.recommendation = this._calculateRecommendation(evaluation.scores)
    evaluation.reasoning = this._generateReasoning(technology, evaluation.scores)

    return evaluation
  }

  // Set evaluation criteria
  setEvaluationCriteria(name, criteria) {
    this.evaluationCriteria.set(name, criteria)
  }

  // Generate technology adoption timeline
  generateAdoptionTimeline(technologyName, targetDate) {
    const evolutionPath = this.evolutionPaths.get(technologyName)
    if (!evolutionPath) {
      throw new Error(`No evolution path defined for: ${technologyName}`)
    }

    const timeline = {
      technology: technologyName,
      targetDate: new Date(targetDate),
      phases: [],
      totalDuration: 0,
      criticalPath: [],
      dependencies: []
    }

    let currentDate = new Date()
    
    for (const phase of evolutionPath.phases) {
      const phaseDuration = this._parseDuration(phase.duration)
      const phaseEndDate = new Date(currentDate.getTime() + phaseDuration)

      timeline.phases.push({
        name: phase.name,
        startDate: new Date(currentDate),
        endDate: phaseEndDate,
        duration: phaseDuration,
        prerequisites: phase.prerequisites || [],
        deliverables: phase.deliverables || [],
        risks: phase.risks || []
      })

      currentDate = phaseEndDate
      timeline.totalDuration += phaseDuration
    }

    this.adoptionTimelines.set(technologyName, timeline)
    return timeline
  }

  _calculateRecommendation(scores) {
    const weights = {
      technical: 0.3,
      business: 0.25,
      risk: 0.2,
      strategic: 0.15,
      operational: 0.1
    }

    let weightedScore = 0
    let totalWeight = 0

    for (const [criteria, score] of Object.entries(scores)) {
      const weight = weights[criteria] || 0.1
      weightedScore += score * weight
      totalWeight += weight
    }

    const finalScore = weightedScore / totalWeight

    if (finalScore >= 8) return 'strongly_recommended'
    if (finalScore >= 6) return 'recommended'
    if (finalScore >= 4) return 'conditional'
    if (finalScore >= 2) return 'not_recommended'
    return 'strongly_discouraged'
  }

  _generateReasoning(technology, scores) {
    const reasoning = []

    // High-scoring criteria
    const highScores = Object.entries(scores).filter(([, score]) => score >= 7)
    if (highScores.length > 0) {
      reasoning.push(`Strong points: ${highScores.map(([criteria]) => criteria).join(', ')}`)
    }

    // Low-scoring criteria
    const lowScores = Object.entries(scores).filter(([, score]) => score <= 3)
    if (lowScores.length > 0) {
      reasoning.push(`Concerns: ${lowScores.map(([criteria]) => criteria).join(', ')}`)
    }

    // Maturity consideration
    if (technology.maturity === 'experimental') {
      reasoning.push('Consider maturity risk for production use')
    }

    // Cost consideration
    if (technology.adoptionCost === 'high') {
      reasoning.push('High adoption cost requires careful ROI analysis')
    }

    return reasoning
  }

  _parseDuration(duration) {
    // Same implementation as in Migration class
    if (typeof duration === 'number') return duration
    
    const units = {
      'd': 24 * 60 * 60 * 1000,
      'w': 7 * 24 * 60 * 60 * 1000,
      'm': 30 * 24 * 60 * 60 * 1000
    }
    
    const match = duration.match(/^(\d+)([dwm])$/)
    if (match) {
      const [, value, unit] = match
      return parseInt(value) * units[unit]
    }
    
    return 0
  }
}

// ========================================
// EXPORTS AND INITIALIZATION
// ========================================

export const migrationManager = new MigrationManager()
export const compatibilityManager = new CompatibilityManager()
export const technologyRoadmap = new TechnologyRoadmap()

// Initialize default evaluation criteria
technologyRoadmap.setEvaluationCriteria('technical', {
  async evaluate(technology, context) {
    // Technical merit evaluation
    let score = 5 // Base score
    
    if (technology.maturity === 'stable') score += 2
    if (technology.maturity === 'experimental') score -= 2
    
    if (technology.dependencies.length === 0) score += 1
    if (technology.dependencies.length > 5) score -= 1
    
    return Math.max(0, Math.min(10, score))
  }
})

technologyRoadmap.setEvaluationCriteria('business', {
  async evaluate(technology, context) {
    // Business value evaluation
    let score = 5
    
    if (technology.benefits.includes('cost_reduction')) score += 2
    if (technology.benefits.includes('performance_improvement')) score += 2
    if (technology.benefits.includes('developer_productivity')) score += 1
    
    if (technology.adoptionCost === 'low') score += 1
    if (technology.adoptionCost === 'high') score -= 2
    
    return Math.max(0, Math.min(10, score))
  }
})

logger.info('🚀 Future-proofing migration strategy framework initialized')