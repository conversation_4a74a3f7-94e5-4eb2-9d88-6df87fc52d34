/**
 * 🔄 API Versioning Strategy & Deprecation Management
 * Handles seamless API evolution while maintaining backward compatibility
 */

import { logger } from '../backend/utils/logger.js'
import { EventEmitter } from 'events'

// ========================================
// API VERSION MANAGEMENT CORE
// ========================================

/**
 * API Version Manager - Central control for API evolution
 */
export class APIVersionManager extends EventEmitter {
  constructor() {
    super()
    this.versions = new Map()
    this.deprecationSchedule = new Map()
    this.transformers = new Map()
    this.usageTracker = new UsageTracker()
    this.compatibilityMatrix = new Map()
    this.migrationGuides = new Map()
  }

  // Register API version
  registerVersion(version, config) {
    const versionInfo = new APIVersion(version, config)
    this.versions.set(version, versionInfo)

    // Setup deprecation schedule if specified
    if (config.deprecationDate) {
      this.scheduleDeprecation(version, config.deprecationDate, config.removalDate)
    }

    // Register transformers for version compatibility
    if (config.transformers) {
      this.registerTransformers(version, config.transformers)
    }

    logger.info(`API version registered: ${version}`, {
      status: config.status,
      deprecationDate: config.deprecationDate,
      supportLevel: config.supportLevel
    })

    this.emit('versionRegistered', { version, config })
  }

  // Get version information
  getVersion(version) {
    return this.versions.get(version)
  }

  // Get latest version
  getLatestVersion() {
    const activeVersions = Array.from(this.versions.values())
      .filter(v => v.status === 'active' || v.status === 'stable')
      .sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))

    return activeVersions[0]?.version
  }

  // Get supported versions
  getSupportedVersions() {
    return Array.from(this.versions.values())
      .filter(v => v.status !== 'removed')
      .map(v => ({
        version: v.version,
        status: v.status,
        supportLevel: v.supportLevel,
        deprecationDate: this.deprecationSchedule.get(v.version)?.deprecationDate
      }))
  }

  // Schedule version deprecation
  scheduleDeprecation(version, deprecationDate, removalDate) {
    this.deprecationSchedule.set(version, {
      version,
      deprecationDate: new Date(deprecationDate),
      removalDate: new Date(removalDate),
      scheduledAt: new Date()
    })

    logger.info(`Deprecation scheduled for version ${version}`, {
      deprecationDate,
      removalDate
    })
  }

  // Register version transformers
  registerTransformers(version, transformers) {
    this.transformers.set(version, transformers)
  }

  // Check if version is deprecated
  isDeprecated(version) {
    const schedule = this.deprecationSchedule.get(version)
    return schedule && new Date() >= schedule.deprecationDate
  }

  // Check if version should be removed
  shouldRemove(version) {
    const schedule = this.deprecationSchedule.get(version)
    return schedule && new Date() >= schedule.removalDate
  }

  // Get deprecation status
  getDeprecationStatus(version) {
    const schedule = this.deprecationSchedule.get(version)
    if (!schedule) {
      return { deprecated: false }
    }

    const now = new Date()
    const deprecated = now >= schedule.deprecationDate
    const daysUntilRemoval = schedule.removalDate 
      ? Math.ceil((schedule.removalDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      : null

    return {
      deprecated,
      deprecationDate: schedule.deprecationDate,
      removalDate: schedule.removalDate,
      daysUntilRemoval,
      willBeRemoved: daysUntilRemoval !== null && daysUntilRemoval <= 0
    }
  }

  // Track API usage
  trackUsage(version, endpoint, userId, metadata = {}) {
    this.usageTracker.recordUsage(version, endpoint, userId, metadata)
  }

  // Get usage statistics
  getUsageStats(version, timeRange = '30d') {
    return this.usageTracker.getStats(version, timeRange)
  }

  // Create migration guide
  createMigrationGuide(fromVersion, toVersion, guide) {
    const key = `${fromVersion}->${toVersion}`
    this.migrationGuides.set(key, guide)

    logger.info(`Migration guide created: ${fromVersion} -> ${toVersion}`)
  }

  // Get migration guide
  getMigrationGuide(fromVersion, toVersion) {
    const key = `${fromVersion}->${toVersion}`
    return this.migrationGuides.get(key)
  }

  // Update version status
  updateVersionStatus(version, newStatus) {
    const versionInfo = this.versions.get(version)
    if (versionInfo) {
      const oldStatus = versionInfo.status
      versionInfo.status = newStatus
      versionInfo.statusUpdatedAt = new Date()

      logger.info(`Version status updated: ${version}`, { from: oldStatus, to: newStatus })
      this.emit('versionStatusUpdated', { version, oldStatus, newStatus })
    }
  }

  // Generate compatibility report
  generateCompatibilityReport() {
    const report = {
      versions: [],
      deprecations: [],
      migrations: [],
      usageStats: {},
      recommendations: []
    }

    // Version information
    for (const [version, versionInfo] of this.versions) {
      const deprecationStatus = this.getDeprecationStatus(version)
      const usage = this.usageTracker.getStats(version, '30d')

      report.versions.push({
        version,
        status: versionInfo.status,
        supportLevel: versionInfo.supportLevel,
        releaseDate: versionInfo.releaseDate,
        deprecated: deprecationStatus.deprecated,
        daysUntilRemoval: deprecationStatus.daysUntilRemoval,
        monthlyUsage: usage.totalRequests
      })

      if (deprecationStatus.deprecated) {
        report.deprecations.push({
          version,
          deprecationDate: deprecationStatus.deprecationDate,
          removalDate: deprecationStatus.removalDate,
          activeUsers: usage.uniqueUsers,
          recommendations: this._generateDeprecationRecommendations(version, usage)
        })
      }
    }

    // Migration paths
    for (const [key, guide] of this.migrationGuides) {
      const [from, to] = key.split('->')
      report.migrations.push({
        from,
        to,
        complexity: guide.complexity,
        estimatedEffort: guide.estimatedEffort,
        breakingChanges: guide.breakingChanges?.length || 0
      })
    }

    return report
  }

  _generateDeprecationRecommendations(version, usage) {
    const recommendations = []

    if (usage.uniqueUsers > 100) {
      recommendations.push('High usage detected - consider extended deprecation timeline')
    }

    if (usage.totalRequests > 10000) {
      recommendations.push('Heavy API usage - implement gradual migration strategy')
    }

    const latestVersion = this.getLatestVersion()
    if (latestVersion) {
      recommendations.push(`Migrate to version ${latestVersion}`)
    }

    return recommendations
  }
}

/**
 * Individual API Version
 */
class APIVersion {
  constructor(version, config) {
    this.version = version
    this.status = config.status || 'draft' // draft, beta, stable, deprecated, removed
    this.supportLevel = config.supportLevel || 'full' // full, security_only, none
    this.releaseDate = new Date(config.releaseDate || Date.now())
    this.statusUpdatedAt = new Date()
    this.features = config.features || []
    this.breakingChanges = config.breakingChanges || []
    this.documentation = config.documentation
    this.changelogUrl = config.changelogUrl
  }

  isActive() {
    return ['beta', 'stable'].includes(this.status)
  }

  isSupported() {
    return this.status !== 'removed'
  }

  serialize() {
    return {
      version: this.version,
      status: this.status,
      supportLevel: this.supportLevel,
      releaseDate: this.releaseDate,
      statusUpdatedAt: this.statusUpdatedAt,
      features: this.features,
      breakingChanges: this.breakingChanges,
      documentation: this.documentation,
      changelogUrl: this.changelogUrl
    }
  }
}

// ========================================
// REQUEST ROUTER WITH VERSION HANDLING
// ========================================

/**
 * Versioned API Router
 */
export class VersionedAPIRouter {
  constructor(versionManager) {
    this.versionManager = versionManager
    this.routes = new Map()
    this.middleware = new Map()
    this.defaultVersion = null
  }

  // Set default version for unversioned requests
  setDefaultVersion(version) {
    this.defaultVersion = version
  }

  // Register versioned route
  registerRoute(version, method, path, handler, options = {}) {
    const key = `${version}:${method}:${path}`
    
    this.routes.set(key, {
      version,
      method: method.toUpperCase(),
      path,
      handler,
      options,
      registeredAt: new Date()
    })

    logger.debug(`Versioned route registered: ${key}`)
  }

  // Register version-specific middleware
  registerMiddleware(version, middleware) {
    if (!this.middleware.has(version)) {
      this.middleware.set(version, [])
    }
    this.middleware.get(version).push(middleware)
  }

  // Route request to appropriate version handler
  async routeRequest(req, res, next) {
    try {
      // Extract version from request
      const requestedVersion = this._extractVersion(req)
      const version = requestedVersion || this.defaultVersion

      if (!version) {
        return res.status(400).json({
          error: 'API version required',
          supportedVersions: this.versionManager.getSupportedVersions()
        })
      }

      // Validate version
      const versionInfo = this.versionManager.getVersion(version)
      if (!versionInfo || !versionInfo.isSupported()) {
        return res.status(400).json({
          error: `Unsupported API version: ${version}`,
          supportedVersions: this.versionManager.getSupportedVersions()
        })
      }

      // Check deprecation status
      const deprecationStatus = this.versionManager.getDeprecationStatus(version)
      if (deprecationStatus.deprecated) {
        res.set('X-API-Deprecated', 'true')
        res.set('X-API-Deprecation-Date', deprecationStatus.deprecationDate.toISOString())
        
        if (deprecationStatus.removalDate) {
          res.set('X-API-Removal-Date', deprecationStatus.removalDate.toISOString())
        }

        // Add deprecation warning to response
        res.set('Warning', `299 - "API version ${version} is deprecated"`)
      }

      // Track usage
      this.versionManager.trackUsage(version, req.path, req.user?.id, {
        userAgent: req.get('User-Agent'),
        ip: req.ip
      })

      // Find route handler
      const routeKey = `${version}:${req.method}:${req.path}`
      let route = this.routes.get(routeKey)

      // Try with path parameters if exact match not found
      if (!route) {
        route = this._findParameterizedRoute(version, req.method, req.path)
      }

      if (!route) {
        // Try to transform from newer version if transformer exists
        const transformedResult = await this._tryVersionTransform(version, req, res)
        if (transformedResult) {
          return transformedResult
        }

        return res.status(404).json({
          error: `Endpoint not found in API version ${version}`,
          path: req.path,
          method: req.method
        })
      }

      // Apply version-specific middleware
      const versionMiddleware = this.middleware.get(version) || []
      
      for (const middleware of versionMiddleware) {
        await new Promise((resolve, reject) => {
          middleware(req, res, (err) => {
            if (err) reject(err)
            else resolve()
          })
        })
      }

      // Add version info to request
      req.apiVersion = version
      req.versionInfo = versionInfo.serialize()

      // Execute route handler
      await route.handler(req, res, next)

    } catch (error) {
      logger.error('Versioned routing error', error)
      next(error)
    }
  }

  // Extract version from request
  _extractVersion(req) {
    // Try header first
    let version = req.get('X-API-Version') || req.get('API-Version')
    
    // Try query parameter
    if (!version) {
      version = req.query.version || req.query.v
    }
    
    // Try URL path prefix
    if (!version) {
      const pathMatch = req.path.match(/^\/v(\d+(?:\.\d+)?)\//)
      if (pathMatch) {
        version = pathMatch[1]
        // Remove version from path for downstream processing
        req.path = req.path.replace(pathMatch[0], '/')
        req.url = req.url.replace(pathMatch[0], '/')
      }
    }

    // Try Accept header
    if (!version) {
      const accept = req.get('Accept')
      if (accept) {
        const versionMatch = accept.match(/application\/vnd\.sequenceai\.v(\d+(?:\.\d+)?)\+json/)
        if (versionMatch) {
          version = versionMatch[1]
        }
      }
    }

    return version
  }

  // Find route with parameters
  _findParameterizedRoute(version, method, path) {
    const pathSegments = path.split('/')
    
    for (const [routeKey, route] of this.routes) {
      if (!routeKey.startsWith(`${version}:${method}:`)) continue
      
      const routePath = routeKey.split(':').slice(2).join(':')
      const routeSegments = routePath.split('/')
      
      if (pathSegments.length !== routeSegments.length) continue
      
      // Check if segments match (allowing for parameters)
      const matches = routeSegments.every((segment, index) => {
        return segment.startsWith(':') || segment === pathSegments[index]
      })
      
      if (matches) {
        // Extract parameters
        const params = {}
        routeSegments.forEach((segment, index) => {
          if (segment.startsWith(':')) {
            const paramName = segment.substring(1)
            params[paramName] = pathSegments[index]
          }
        })
        
        return { ...route, params }
      }
    }
    
    return null
  }

  // Try to transform request from newer version
  async _tryVersionTransform(targetVersion, req, res) {
    const transformers = this.versionManager.transformers.get(targetVersion)
    if (!transformers) return null

    // Try to find a transformer that can handle this request
    for (const transformer of transformers) {
      if (transformer.canTransform(req)) {
        try {
          const transformedResponse = await transformer.transform(req, res)
          return transformedResponse
        } catch (error) {
          logger.error('Version transformation error', error)
        }
      }
    }

    return null
  }
}

// ========================================
// VERSION TRANSFORMERS
// ========================================

/**
 * Base Version Transformer
 */
export class VersionTransformer {
  constructor(fromVersion, toVersion) {
    this.fromVersion = fromVersion
    this.toVersion = toVersion
    this.transformations = new Map()
  }

  // Register field transformation
  registerTransformation(field, transformer) {
    this.transformations.set(field, transformer)
  }

  // Check if this transformer can handle the request
  canTransform(req) {
    // Override in specific implementations
    return false
  }

  // Transform request/response between versions
  async transform(req, res) {
    throw new Error('transform method must be implemented')
  }

  // Transform data object
  transformData(data, direction = 'forward') {
    if (!data || typeof data !== 'object') return data

    const transformed = { ...data }

    for (const [field, transformer] of this.transformations) {
      if (field in transformed) {
        try {
          if (direction === 'forward') {
            transformed[field] = transformer.forward(transformed[field])
          } else {
            transformed[field] = transformer.backward(transformed[field])
          }
        } catch (error) {
          logger.warn(`Transformation error for field ${field}`, error)
        }
      }
    }

    return transformed
  }
}

/**
 * Field-level transformers
 */
export const FieldTransformers = {
  // Rename field
  rename: (oldName, newName) => ({
    forward: (value) => value,
    backward: (value) => value,
    transformObject: (obj, direction) => {
      if (direction === 'forward') {
        obj[newName] = obj[oldName]
        delete obj[oldName]
      } else {
        obj[oldName] = obj[newName]
        delete obj[newName]
      }
      return obj
    }
  }),

  // Change data type
  typeChange: (fromType, toType, converter) => ({
    forward: (value) => converter.toNew(value),
    backward: (value) => converter.toOld(value)
  }),

  // Add default value for new field
  addDefault: (defaultValue) => ({
    forward: (value) => value,
    backward: (value) => value,
    addToObject: (obj) => {
      if (!obj.hasOwnProperty(this.field)) {
        obj[this.field] = defaultValue
      }
      return obj
    }
  }),

  // Remove deprecated field
  remove: () => ({
    forward: (value) => undefined,
    backward: (value) => value
  }),

  // Nest field under new structure
  nest: (parentField) => ({
    forward: (value) => value,
    backward: (value) => value,
    transformObject: (obj, direction) => {
      if (direction === 'forward') {
        if (!obj[parentField]) obj[parentField] = {}
        obj[parentField][this.field] = obj[this.field]
        delete obj[this.field]
      } else {
        obj[this.field] = obj[parentField]?.[this.field]
        if (obj[parentField]) delete obj[parentField][this.field]
      }
      return obj
    }
  })
}

// ========================================
// USAGE TRACKING
// ========================================

/**
 * API Usage Tracker
 */
class UsageTracker {
  constructor() {
    this.requests = []
    this.maxRecords = 100000 // Keep last 100k requests
    this.aggregatedStats = new Map()
  }

  // Record API usage
  recordUsage(version, endpoint, userId, metadata = {}) {
    const record = {
      version,
      endpoint,
      userId,
      timestamp: new Date(),
      metadata
    }

    this.requests.push(record)

    // Keep only recent records
    if (this.requests.length > this.maxRecords) {
      this.requests = this.requests.slice(-this.maxRecords)
    }

    // Update aggregated stats
    this._updateAggregatedStats(record)
  }

  // Get usage statistics
  getStats(version, timeRange = '30d') {
    const cutoffTime = this._getTimeRangeCutoff(timeRange)
    const relevantRequests = this.requests.filter(req => 
      req.version === version && req.timestamp >= cutoffTime
    )

    const uniqueUsers = new Set(relevantRequests.map(req => req.userId).filter(Boolean))
    const endpointStats = new Map()

    for (const request of relevantRequests) {
      const current = endpointStats.get(request.endpoint) || { count: 0, users: new Set() }
      current.count++
      if (request.userId) current.users.add(request.userId)
      endpointStats.set(request.endpoint, current)
    }

    const topEndpoints = Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        requests: stats.count,
        uniqueUsers: stats.users.size
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10)

    return {
      version,
      timeRange,
      totalRequests: relevantRequests.length,
      uniqueUsers: uniqueUsers.size,
      requestsPerDay: this._calculateDailyAverage(relevantRequests, timeRange),
      topEndpoints
    }
  }

  // Get all version stats
  getAllStats(timeRange = '30d') {
    const versions = new Set(this.requests.map(req => req.version))
    const stats = {}

    for (const version of versions) {
      stats[version] = this.getStats(version, timeRange)
    }

    return stats
  }

  _updateAggregatedStats(record) {
    const dayKey = record.timestamp.toISOString().split('T')[0]
    const statKey = `${record.version}:${dayKey}`
    
    const current = this.aggregatedStats.get(statKey) || {
      version: record.version,
      date: dayKey,
      requests: 0,
      users: new Set()
    }
    
    current.requests++
    if (record.userId) current.users.add(record.userId)
    
    this.aggregatedStats.set(statKey, current)
  }

  _getTimeRangeCutoff(timeRange) {
    const now = new Date()
    const match = timeRange.match(/^(\d+)([hdw])$/)
    
    if (!match) return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days default
    
    const value = parseInt(match[1])
    const unit = match[2]
    
    const units = {
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000,
      'w': 7 * 24 * 60 * 60 * 1000
    }
    
    return new Date(now.getTime() - value * units[unit])
  }

  _calculateDailyAverage(requests, timeRange) {
    if (requests.length === 0) return 0
    
    const days = this._getTimeRangeDays(timeRange)
    return requests.length / days
  }

  _getTimeRangeDays(timeRange) {
    const match = timeRange.match(/^(\d+)([hdw])$/)
    if (!match) return 30
    
    const value = parseInt(match[1])
    const unit = match[2]
    
    switch (unit) {
      case 'h': return value / 24
      case 'd': return value
      case 'w': return value * 7
      default: return 30
    }
  }
}

// ========================================
// DEPRECATION MANAGER
// ========================================

/**
 * Deprecation Notification Manager
 */
export class DeprecationManager extends EventEmitter {
  constructor(versionManager, notificationService) {
    super()
    this.versionManager = versionManager
    this.notificationService = notificationService
    this.notificationSchedule = new Map()
    this.sentNotifications = new Set()
  }

  // Schedule deprecation notifications
  scheduleNotifications(version, schedule) {
    this.notificationSchedule.set(version, schedule)
    
    // Schedule automated notifications
    for (const notification of schedule) {
      this._scheduleNotification(version, notification)
    }
  }

  // Send immediate deprecation notice
  async sendDeprecationNotice(version, targetAudience = 'all') {
    const versionInfo = this.versionManager.getVersion(version)
    if (!versionInfo) return

    const deprecationStatus = this.versionManager.getDeprecationStatus(version)
    const usageStats = this.versionManager.getUsageStats(version, '30d')

    const notice = {
      version,
      deprecationDate: deprecationStatus.deprecationDate,
      removalDate: deprecationStatus.removalDate,
      daysUntilRemoval: deprecationStatus.daysUntilRemoval,
      affectedUsers: usageStats.uniqueUsers,
      migrationGuide: this._getMigrationInstructions(version),
      supportContact: '<EMAIL>'
    }

    // Send notifications based on target audience
    if (targetAudience === 'all' || targetAudience === 'email') {
      await this._sendEmailNotifications(version, notice)
    }

    if (targetAudience === 'all' || targetAudience === 'webhook') {
      await this._sendWebhookNotifications(version, notice)
    }

    if (targetAudience === 'all' || targetAudience === 'dashboard') {
      await this._sendDashboardNotifications(version, notice)
    }

    this.emit('deprecationNoticeSent', { version, targetAudience, notice })
  }

  // Get migration instructions
  _getMigrationInstructions(version) {
    const latestVersion = this.versionManager.getLatestVersion()
    const migrationGuide = this.versionManager.getMigrationGuide(version, latestVersion)

    return {
      targetVersion: latestVersion,
      migrationGuide: migrationGuide?.url || `https://docs.sequenceai.app/api/migration/${version}-to-${latestVersion}`,
      breakingChanges: migrationGuide?.breakingChanges || [],
      estimatedEffort: migrationGuide?.estimatedEffort || 'medium',
      supportAvailable: true
    }
  }

  _scheduleNotification(version, notification) {
    const notificationDate = new Date(notification.date)
    const now = new Date()

    if (notificationDate <= now) {
      // Send immediately if date has passed
      this.sendDeprecationNotice(version, notification.audience)
    } else {
      // Schedule for future
      const delay = notificationDate.getTime() - now.getTime()
      setTimeout(() => {
        this.sendDeprecationNotice(version, notification.audience)
      }, delay)
    }
  }

  async _sendEmailNotifications(version, notice) {
    // Implementation would depend on email service
    logger.info(`Email deprecation notice sent for version ${version}`, notice)
  }

  async _sendWebhookNotifications(version, notice) {
    // Implementation would send to registered webhooks
    logger.info(`Webhook deprecation notice sent for version ${version}`, notice)
  }

  async _sendDashboardNotifications(version, notice) {
    // Implementation would update dashboard notifications
    logger.info(`Dashboard deprecation notice sent for version ${version}`, notice)
  }
}

// ========================================
// EXPORTS
// ========================================

export {
  APIVersionManager,
  VersionedAPIRouter,
  VersionTransformer,
  FieldTransformers,
  DeprecationManager
}

// Initialize global version manager
export const apiVersionManager = new APIVersionManager()

logger.info('🔄 API versioning strategy and deprecation management initialized')