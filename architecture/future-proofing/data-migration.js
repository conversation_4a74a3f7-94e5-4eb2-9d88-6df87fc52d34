/**
 * 🗃️ Data Migration & Schema Evolution Framework
 * Handles safe database schema changes and data transformations
 */

import { logger } from '../backend/utils/logger.js'
import { EventEmitter } from 'events'

// ========================================
// DATA MIGRATION CORE FRAMEWORK
// ========================================

/**
 * Data Migration Manager - Orchestrates database schema evolution
 */
export class DataMigrationManager extends EventEmitter {
  constructor(database) {
    super()
    this.database = database
    this.migrations = new Map()
    this.migrationHistory = []
    this.schemaVersions = new Map()
    this.rollbackStrategies = new Map()
    this.validationRules = new Map()
    this.backupManager = new BackupManager(database)
    this.lockManager = new MigrationLockManager()
  }

  // Register a data migration
  registerMigration(name, migration) {
    if (this.migrations.has(name)) {
      throw new Error(`Migration already exists: ${name}`)
    }

    const migrationInstance = new DataMigration(name, migration)
    this.migrations.set(name, migrationInstance)

    // Register rollback strategy
    if (migration.rollback) {
      this.rollbackStrategies.set(name, migration.rollback)
    }

    // Register validation rules
    if (migration.validation) {
      this.validationRules.set(name, migration.validation)
    }

    logger.info(`Data migration registered: ${name}`, {
      version: migration.version,
      type: migration.type,
      criticality: migration.criticality
    })
  }

  // Execute migration with safety checks
  async executeMigration(name, options = {}) {
    const migration = this.migrations.get(name)
    if (!migration) {
      throw new Error(`Migration not found: ${name}`)
    }

    // Acquire migration lock
    const lockId = await this.lockManager.acquireLock(name)
    
    try {
      logger.info(`Starting data migration: ${name}`)
      this.emit('migrationStarted', { name, options })

      // Pre-migration checks
      await this._preMigrationChecks(migration, options)

      // Create backup if required
      let backupId = null
      if (migration.requiresBackup || options.forceBackup) {
        backupId = await this.backupManager.createBackup(migration.collections)
        logger.info(`Backup created: ${backupId}`)
      }

      // Execute migration steps
      const result = await this._executeMigrationSteps(migration, options)

      // Post-migration validation
      await this._postMigrationValidation(migration, result)

      // Record successful migration
      this._recordMigrationHistory(name, 'completed', result, backupId)

      logger.info(`Data migration completed: ${name}`, result)
      this.emit('migrationCompleted', { name, result })

      return result

    } catch (error) {
      logger.error(`Data migration failed: ${name}`, error)

      // Attempt automatic rollback
      if (options.autoRollback !== false) {
        try {
          await this.rollbackMigration(name, `Auto-rollback: ${error.message}`)
        } catch (rollbackError) {
          logger.error(`Rollback failed for migration: ${name}`, rollbackError)
          this.emit('criticalMigrationFailure', { name, error, rollbackError })
        }
      }

      this._recordMigrationHistory(name, 'failed', { error: error.message })
      this.emit('migrationFailed', { name, error })

      throw error

    } finally {
      await this.lockManager.releaseLock(lockId)
    }
  }

  // Rollback migration
  async rollbackMigration(name, reason = 'Manual rollback') {
    const rollbackStrategy = this.rollbackStrategies.get(name)
    if (!rollbackStrategy) {
      throw new Error(`No rollback strategy found for migration: ${name}`)
    }

    logger.info(`Starting migration rollback: ${name}`, { reason })

    try {
      const result = await rollbackStrategy.execute()
      
      this._recordMigrationHistory(name, 'rolled_back', { reason, result })
      this.emit('migrationRolledBack', { name, reason, result })

      return result

    } catch (error) {
      logger.error(`Migration rollback failed: ${name}`, error)
      this.emit('rollbackFailed', { name, error })
      throw error
    }
  }

  // Get migration status
  getMigrationStatus() {
    return {
      registeredMigrations: Array.from(this.migrations.keys()),
      completedMigrations: this.migrationHistory
        .filter(m => m.status === 'completed')
        .map(m => m.name),
      failedMigrations: this.migrationHistory
        .filter(m => m.status === 'failed')
        .map(m => ({ name: m.name, error: m.result.error })),
      pendingMigrations: this._getPendingMigrations(),
      lastMigration: this.migrationHistory[this.migrationHistory.length - 1]
    }
  }

  // Check if migration has been applied
  isMigrationApplied(name) {
    return this.migrationHistory.some(m => 
      m.name === name && m.status === 'completed'
    )
  }

  // Pre-migration safety checks
  async _preMigrationChecks(migration, options) {
    // Check database connection
    if (!await this.database.isConnected()) {
      throw new Error('Database connection not available')
    }

    // Check disk space
    const diskSpace = await this.database.getDiskSpace()
    if (diskSpace.available < migration.estimatedDiskUsage) {
      throw new Error('Insufficient disk space for migration')
    }

    // Check if migration is already applied
    if (this.isMigrationApplied(migration.name) && !options.force) {
      throw new Error(`Migration already applied: ${migration.name}`)
    }

    // Run custom pre-checks
    if (migration.preChecks) {
      for (const check of migration.preChecks) {
        const result = await check.execute()
        if (!result.passed) {
          throw new Error(`Pre-check failed: ${check.name} - ${result.reason}`)
        }
      }
    }
  }

  // Execute migration steps with monitoring
  async _executeMigrationSteps(migration, options) {
    const results = []
    let stepIndex = 0

    for (const step of migration.steps) {
      logger.info(`Executing migration step: ${step.name}`)
      
      const stepStart = Date.now()
      const stepResult = await this._executeStep(step, migration, options)
      const stepDuration = Date.now() - stepStart

      results.push({
        step: step.name,
        duration: stepDuration,
        result: stepResult,
        timestamp: new Date()
      })

      // Progress tracking
      const progress = ((stepIndex + 1) / migration.steps.length) * 100
      this.emit('migrationProgress', { 
        migration: migration.name, 
        step: step.name, 
        progress 
      })

      stepIndex++
    }

    return {
      migration: migration.name,
      steps: results,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0),
      status: 'completed'
    }
  }

  // Execute individual migration step
  async _executeStep(step, migration, options) {
    try {
      // Check step prerequisites
      if (step.prerequisites) {
        for (const prereq of step.prerequisites) {
          const satisfied = await prereq.check()
          if (!satisfied) {
            throw new Error(`Prerequisite not satisfied: ${prereq.name}`)
          }
        }
      }

      // Execute step with timeout
      const timeout = step.timeout || 300000 // 5 minutes default
      const result = await Promise.race([
        step.execute({ database: this.database, options }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Step timeout')), timeout)
        )
      ])

      // Validate step result
      if (step.validate) {
        const validation = await step.validate(result)
        if (!validation.valid) {
          throw new Error(`Step validation failed: ${validation.reason}`)
        }
      }

      return result

    } catch (error) {
      // Attempt step rollback
      if (step.rollback) {
        try {
          await step.rollback()
          logger.info(`Step rollback completed: ${step.name}`)
        } catch (rollbackError) {
          logger.error(`Step rollback failed: ${step.name}`, rollbackError)
        }
      }

      throw new MigrationStepError(step.name, error)
    }
  }

  // Post-migration validation
  async _postMigrationValidation(migration, result) {
    const validationRules = this.validationRules.get(migration.name)
    if (!validationRules) return

    for (const rule of validationRules) {
      const validation = await rule.validate(result)
      if (!validation.valid) {
        throw new Error(`Post-migration validation failed: ${rule.name} - ${validation.reason}`)
      }
    }
  }

  // Record migration in history
  _recordMigrationHistory(name, status, result, backupId = null) {
    this.migrationHistory.push({
      name,
      status,
      result,
      backupId,
      timestamp: new Date(),
      duration: result?.totalDuration || 0
    })

    // Keep history manageable (last 1000 entries)
    if (this.migrationHistory.length > 1000) {
      this.migrationHistory = this.migrationHistory.slice(-1000)
    }
  }

  // Get pending migrations
  _getPendingMigrations() {
    const completed = new Set(
      this.migrationHistory
        .filter(m => m.status === 'completed')
        .map(m => m.name)
    )

    return Array.from(this.migrations.keys())
      .filter(name => !completed.has(name))
  }
}

/**
 * Individual Data Migration
 */
class DataMigration {
  constructor(name, config) {
    this.name = name
    this.version = config.version
    this.type = config.type || 'schema' // schema, data, index, cleanup
    this.criticality = config.criticality || 'medium' // low, medium, high, critical
    this.description = config.description || ''
    this.collections = config.collections || []
    this.estimatedDuration = config.estimatedDuration || 60000 // 1 minute default
    this.estimatedDiskUsage = config.estimatedDiskUsage || 0
    this.requiresBackup = config.requiresBackup !== false
    this.canRunOnline = config.canRunOnline === true
    this.steps = config.steps || []
    this.preChecks = config.preChecks || []
    this.dependencies = config.dependencies || []
    this.createdAt = new Date()
  }

  // Validate migration configuration
  validate() {
    if (!this.name || !this.version || !this.steps.length) {
      throw new Error('Invalid migration configuration')
    }

    // Validate steps
    for (const step of this.steps) {
      if (!step.name || !step.execute) {
        throw new Error(`Invalid migration step: ${step.name || 'unnamed'}`)
      }
    }

    return true
  }

  // Get migration metadata
  getMetadata() {
    return {
      name: this.name,
      version: this.version,
      type: this.type,
      criticality: this.criticality,
      description: this.description,
      collections: this.collections,
      estimatedDuration: this.estimatedDuration,
      estimatedDiskUsage: this.estimatedDiskUsage,
      requiresBackup: this.requiresBackup,
      canRunOnline: this.canRunOnline,
      stepsCount: this.steps.length,
      dependenciesCount: this.dependencies.length,
      createdAt: this.createdAt
    }
  }
}

// ========================================
// SCHEMA EVOLUTION PATTERNS
// ========================================

/**
 * Schema Evolution Manager
 */
export class SchemaEvolutionManager {
  constructor(database) {
    this.database = database
    this.schemaVersions = new Map()
    this.evolutionStrategies = new Map()
  }

  // Register schema version
  registerSchemaVersion(collection, version, schema) {
    const key = `${collection}:${version}`
    this.schemaVersions.set(key, {
      collection,
      version,
      schema,
      registeredAt: new Date()
    })

    logger.info(`Schema version registered: ${collection} v${version}`)
  }

  // Create field addition migration
  createAddFieldMigration(collection, fieldName, fieldConfig) {
    return {
      name: `add_${fieldName}_to_${collection}`,
      version: this._getNextVersion(collection),
      type: 'schema',
      collections: [collection],
      steps: [{
        name: `add_field_${fieldName}`,
        execute: async ({ database }) => {
          const result = await database.collection(collection).updateMany(
            { [fieldName]: { $exists: false } },
            { $set: { [fieldName]: fieldConfig.defaultValue } }
          )
          return { modifiedCount: result.modifiedCount }
        },
        validate: async (result) => ({
          valid: result.modifiedCount >= 0,
          reason: result.modifiedCount >= 0 ? null : 'No documents updated'
        })
      }],
      rollback: {
        execute: async () => {
          await this.database.collection(collection).updateMany(
            {},
            { $unset: { [fieldName]: "" } }
          )
        }
      }
    }
  }

  // Create field removal migration
  createRemoveFieldMigration(collection, fieldName) {
    return {
      name: `remove_${fieldName}_from_${collection}`,
      version: this._getNextVersion(collection),
      type: 'schema',
      collections: [collection],
      requiresBackup: true,
      steps: [{
        name: `remove_field_${fieldName}`,
        execute: async ({ database }) => {
          // First, backup field values
          const docs = await database.collection(collection)
            .find({ [fieldName]: { $exists: true } })
            .project({ _id: 1, [fieldName]: 1 })
            .toArray()

          // Remove field
          const result = await database.collection(collection).updateMany(
            { [fieldName]: { $exists: true } },
            { $unset: { [fieldName]: "" } }
          )

          return { 
            modifiedCount: result.modifiedCount,
            backupData: docs
          }
        },
        validate: async (result) => ({
          valid: result.modifiedCount >= 0,
          reason: null
        })
      }],
      rollback: {
        execute: async () => {
          // Restore from backup data stored in migration result
          // Implementation would restore field values
        }
      }
    }
  }

  // Create field rename migration
  createRenameFieldMigration(collection, oldFieldName, newFieldName) {
    return {
      name: `rename_${oldFieldName}_to_${newFieldName}_in_${collection}`,
      version: this._getNextVersion(collection),
      type: 'schema',
      collections: [collection],
      steps: [{
        name: `rename_field_${oldFieldName}`,
        execute: async ({ database }) => {
          const result = await database.collection(collection).updateMany(
            { [oldFieldName]: { $exists: true } },
            { $rename: { [oldFieldName]: newFieldName } }
          )
          return { modifiedCount: result.modifiedCount }
        },
        rollback: async () => {
          await this.database.collection(collection).updateMany(
            { [newFieldName]: { $exists: true } },
            { $rename: { [newFieldName]: oldFieldName } }
          )
        }
      }]
    }
  }

  // Create data type change migration
  createTypeChangeMigration(collection, fieldName, fromType, toType, converter) {
    return {
      name: `change_${fieldName}_type_${fromType}_to_${toType}_in_${collection}`,
      version: this._getNextVersion(collection),
      type: 'data',
      collections: [collection],
      requiresBackup: true,
      steps: [{
        name: `convert_${fieldName}_type`,
        execute: async ({ database }) => {
          const cursor = database.collection(collection).find({ 
            [fieldName]: { $exists: true, $type: fromType }
          })

          let modifiedCount = 0
          
          for await (const doc of cursor) {
            try {
              const oldValue = doc[fieldName]
              const newValue = converter(oldValue)
              
              await database.collection(collection).updateOne(
                { _id: doc._id },
                { $set: { [fieldName]: newValue } }
              )
              
              modifiedCount++
            } catch (error) {
              logger.warn(`Type conversion failed for document ${doc._id}`, error)
            }
          }

          return { modifiedCount }
        }
      }]
    }
  }

  // Create index migration
  createIndexMigration(collection, indexSpec, options = {}) {
    const indexName = options.name || Object.keys(indexSpec).join('_')
    
    return {
      name: `create_index_${indexName}_on_${collection}`,
      version: this._getNextVersion(collection),
      type: 'index',
      collections: [collection],
      canRunOnline: true,
      steps: [{
        name: `create_index_${indexName}`,
        execute: async ({ database }) => {
          const result = await database.collection(collection).createIndex(
            indexSpec,
            { background: true, ...options }
          )
          return { indexName: result }
        },
        rollback: async () => {
          await this.database.collection(collection).dropIndex(indexName)
        }
      }]
    }
  }

  _getNextVersion(collection) {
    const versions = Array.from(this.schemaVersions.keys())
      .filter(key => key.startsWith(`${collection}:`))
      .map(key => parseFloat(key.split(':')[1]))
      .sort((a, b) => b - a)

    const latestVersion = versions[0] || 0
    return (latestVersion + 0.1).toFixed(1)
  }
}

// ========================================
// BACKUP MANAGEMENT
// ========================================

/**
 * Backup Manager for Data Migration Safety
 */
class BackupManager {
  constructor(database) {
    this.database = database
    this.backups = new Map()
    this.backupRetention = 30 * 24 * 60 * 60 * 1000 // 30 days
  }

  // Create backup before migration
  async createBackup(collections, options = {}) {
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      const backupData = {
        id: backupId,
        collections: [],
        createdAt: new Date(),
        size: 0
      }

      for (const collectionName of collections) {
        const collection = this.database.collection(collectionName)
        const documents = await collection.find({}).toArray()
        
        backupData.collections.push({
          name: collectionName,
          documentCount: documents.length,
          documents: options.includeData ? documents : null,
          indexes: await collection.indexes()
        })

        backupData.size += JSON.stringify(documents).length
      }

      // Store backup (in production, this would go to external storage)
      this.backups.set(backupId, backupData)

      logger.info(`Backup created: ${backupId}`, {
        collections: collections.length,
        size: `${Math.round(backupData.size / 1024)}KB`
      })

      // Schedule cleanup
      setTimeout(() => this._cleanupBackup(backupId), this.backupRetention)

      return backupId

    } catch (error) {
      logger.error(`Backup creation failed: ${backupId}`, error)
      throw new BackupError('create', error)
    }
  }

  // Restore from backup
  async restoreBackup(backupId, options = {}) {
    const backup = this.backups.get(backupId)
    if (!backup) {
      throw new Error(`Backup not found: ${backupId}`)
    }

    try {
      for (const collectionBackup of backup.collections) {
        const collection = this.database.collection(collectionBackup.name)

        if (options.dropBeforeRestore) {
          await collection.drop()
        }

        if (collectionBackup.documents) {
          if (collectionBackup.documents.length > 0) {
            await collection.insertMany(collectionBackup.documents)
          }
        }

        // Restore indexes
        for (const indexSpec of collectionBackup.indexes) {
          if (indexSpec.name !== '_id_') { // Skip default _id index
            try {
              await collection.createIndex(indexSpec.key, indexSpec)
            } catch (error) {
              logger.warn(`Index restoration failed: ${indexSpec.name}`, error)
            }
          }
        }
      }

      logger.info(`Backup restored: ${backupId}`)

    } catch (error) {
      logger.error(`Backup restoration failed: ${backupId}`, error)
      throw new BackupError('restore', error)
    }
  }

  // Get backup information
  getBackupInfo(backupId) {
    const backup = this.backups.get(backupId)
    if (!backup) return null

    return {
      id: backup.id,
      createdAt: backup.createdAt,
      collections: backup.collections.map(c => ({
        name: c.name,
        documentCount: c.documentCount
      })),
      size: backup.size,
      age: Date.now() - backup.createdAt.getTime()
    }
  }

  // List all backups
  listBackups() {
    return Array.from(this.backups.keys()).map(id => this.getBackupInfo(id))
  }

  // Clean up old backup
  _cleanupBackup(backupId) {
    if (this.backups.delete(backupId)) {
      logger.info(`Backup cleaned up: ${backupId}`)
    }
  }
}

// ========================================
// MIGRATION LOCK MANAGER
// ========================================

/**
 * Migration Lock Manager - Prevents concurrent migrations
 */
class MigrationLockManager {
  constructor() {
    this.locks = new Map()
    this.lockTimeout = 60 * 60 * 1000 // 1 hour
  }

  // Acquire migration lock
  async acquireLock(migrationName) {
    const lockId = `lock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    if (this.locks.has(migrationName)) {
      const existingLock = this.locks.get(migrationName)
      if (Date.now() - existingLock.acquiredAt < this.lockTimeout) {
        throw new Error(`Migration is already running: ${migrationName}`)
      } else {
        // Clean up expired lock
        this.locks.delete(migrationName)
      }
    }

    this.locks.set(migrationName, {
      lockId,
      migrationName,
      acquiredAt: Date.now()
    })

    logger.debug(`Migration lock acquired: ${migrationName} (${lockId})`)
    return lockId
  }

  // Release migration lock
  async releaseLock(lockId) {
    for (const [migrationName, lock] of this.locks) {
      if (lock.lockId === lockId) {
        this.locks.delete(migrationName)
        logger.debug(`Migration lock released: ${migrationName} (${lockId})`)
        return
      }
    }
  }

  // Check if migration is locked
  isLocked(migrationName) {
    const lock = this.locks.get(migrationName)
    if (!lock) return false

    // Check if lock has expired
    if (Date.now() - lock.acquiredAt >= this.lockTimeout) {
      this.locks.delete(migrationName)
      return false
    }

    return true
  }
}

// ========================================
// ERROR CLASSES
// ========================================

class MigrationStepError extends Error {
  constructor(stepName, originalError) {
    super(`Migration step failed: ${stepName}`)
    this.name = 'MigrationStepError'
    this.stepName = stepName
    this.originalError = originalError
  }
}

class BackupError extends Error {
  constructor(operation, originalError) {
    super(`Backup ${operation} failed`)
    this.name = 'BackupError'
    this.operation = operation
    this.originalError = originalError
  }
}

// ========================================
// COMMON MIGRATION PATTERNS
// ========================================

/**
 * Pre-built Migration Patterns
 */
export const MigrationPatterns = {
  // Add field with default value
  addField: (collection, fieldName, defaultValue, validation = null) => ({
    name: `add_${fieldName}_to_${collection}`,
    type: 'schema',
    collections: [collection],
    steps: [{
      name: `add_field_${fieldName}`,
      execute: async ({ database }) => {
        const result = await database.collection(collection).updateMany(
          { [fieldName]: { $exists: false } },
          { $set: { [fieldName]: defaultValue } }
        )
        return { modifiedCount: result.modifiedCount }
      },
      validate: validation || (async (result) => ({
        valid: result.modifiedCount >= 0,
        reason: null
      }))
    }]
  }),

  // Remove field safely
  removeField: (collection, fieldName) => ({
    name: `remove_${fieldName}_from_${collection}`,
    type: 'schema',
    collections: [collection],
    requiresBackup: true,
    steps: [{
      name: `remove_field_${fieldName}`,
      execute: async ({ database }) => {
        const result = await database.collection(collection).updateMany(
          {},
          { $unset: { [fieldName]: "" } }
        )
        return { modifiedCount: result.modifiedCount }
      }
    }]
  }),

  // Create index with online build
  createIndex: (collection, indexSpec, options = {}) => ({
    name: `create_index_on_${collection}`,
    type: 'index',
    collections: [collection],
    canRunOnline: true,
    steps: [{
      name: 'create_index',
      execute: async ({ database }) => {
        const result = await database.collection(collection).createIndex(
          indexSpec,
          { background: true, ...options }
        )
        return { indexName: result }
      }
    }]
  }),

  // Data cleanup migration
  cleanupData: (collection, condition, description) => ({
    name: `cleanup_${collection}_${description}`,
    type: 'cleanup',
    collections: [collection],
    requiresBackup: true,
    steps: [{
      name: 'cleanup_data',
      execute: async ({ database }) => {
        const result = await database.collection(collection).deleteMany(condition)
        return { deletedCount: result.deletedCount }
      }
    }]
  })
}

// ========================================
// EXPORTS
// ========================================

export {
  DataMigrationManager,
  SchemaEvolutionManager,
  MigrationPatterns
}

logger.info('🗃️ Data migration and schema evolution framework initialized')