/**
 * 🎚️ Feature Flags & Configuration Management System
 * Enables gradual rollouts, A/B testing, and safe feature deployment
 */

import { logger } from '../backend/utils/logger.js'
import { EventEmitter } from 'events'
import { createHash } from 'crypto'

// ========================================
// FEATURE FLAGS CORE SYSTEM
// ========================================

/**
 * Feature Flag Manager - Central control for feature rollouts
 */
export class FeatureFlagManager extends EventEmitter {
  constructor(storage, analytics) {
    super()
    this.storage = storage
    this.analytics = analytics
    this.flags = new Map()
    this.rules = new Map()
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5 minutes
    this.evaluationLog = []
    this.maxLogSize = 1000
  }

  // Register a feature flag
  async registerFlag(name, config) {
    const flag = new FeatureFlag(name, config)
    this.flags.set(name, flag)

    // Persist to storage
    await this.storage.saveFlag(name, flag.serialize())

    // Set up evaluation rules
    if (config.rules) {
      this.rules.set(name, config.rules)
    }

    logger.info(`Feature flag registered: ${name}`, {
      type: config.type,
      defaultValue: config.defaultValue,
      rolloutPercentage: config.rolloutPercentage
    })

    this.emit('flagRegistered', { name, config })
  }

  // Evaluate feature flag for user/context
  async evaluateFlag(flagName, context = {}) {
    const cacheKey = this._getCacheKey(flagName, context)
    
    // Check cache first
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value
    }

    const flag = this.flags.get(flagName)
    if (!flag) {
      logger.warn(`Feature flag not found: ${flagName}`)
      return null
    }

    try {
      // Evaluate flag with rules and context
      const evaluation = await this._evaluateWithRules(flag, context)
      
      // Cache result
      this.cache.set(cacheKey, {
        value: evaluation,
        timestamp: Date.now()
      })

      // Log evaluation for analytics
      this._logEvaluation(flagName, context, evaluation)

      // Track analytics
      if (this.analytics) {
        await this.analytics.trackFlagEvaluation(flagName, evaluation.enabled, context)
      }

      return evaluation

    } catch (error) {
      logger.error(`Feature flag evaluation error: ${flagName}`, error)
      
      // Return safe default
      return {
        enabled: flag.defaultValue,
        value: flag.defaultValue,
        reason: 'error_fallback',
        error: error.message
      }
    }
  }

  // Check if feature is enabled (simple boolean check)
  async isEnabled(flagName, context = {}) {
    const evaluation = await this.evaluateFlag(flagName, context)
    return evaluation?.enabled || false
  }

  // Get feature value (for complex feature flags)
  async getValue(flagName, context = {}) {
    const evaluation = await this.evaluateFlag(flagName, context)
    return evaluation?.value
  }

  // Update flag configuration
  async updateFlag(flagName, updates) {
    const flag = this.flags.get(flagName)
    if (!flag) {
      throw new Error(`Feature flag not found: ${flagName}`)
    }

    const oldConfig = flag.serialize()
    flag.update(updates)

    // Persist changes
    await this.storage.saveFlag(flagName, flag.serialize())

    // Clear cache for this flag
    this._clearFlagCache(flagName)

    logger.info(`Feature flag updated: ${flagName}`, {
      oldConfig: oldConfig,
      newConfig: flag.serialize()
    })

    this.emit('flagUpdated', { flagName, oldConfig, newConfig: flag.serialize() })
  }

  // Gradual rollout - increase percentage over time
  async graduralRollout(flagName, targetPercentage, durationMs, steps = 10) {
    const flag = this.flags.get(flagName)
    if (!flag) {
      throw new Error(`Feature flag not found: ${flagName}`)
    }

    const currentPercentage = flag.rolloutPercentage || 0
    const increment = (targetPercentage - currentPercentage) / steps
    const stepDuration = durationMs / steps

    logger.info(`Starting gradual rollout: ${flagName}`, {
      from: currentPercentage,
      to: targetPercentage,
      steps,
      stepDuration: `${stepDuration}ms`
    })

    for (let i = 0; i < steps; i++) {
      const newPercentage = Math.min(targetPercentage, currentPercentage + (increment * (i + 1)))
      
      await this.updateFlag(flagName, { rolloutPercentage: newPercentage })
      
      logger.info(`Rollout step ${i + 1}/${steps}: ${flagName} = ${newPercentage}%`)
      this.emit('rolloutStep', { flagName, step: i + 1, percentage: newPercentage })

      // Wait before next step (except for last step)
      if (i < steps - 1) {
        await new Promise(resolve => setTimeout(resolve, stepDuration))
      }
    }

    logger.info(`Gradual rollout completed: ${flagName} = ${targetPercentage}%`)
    this.emit('rolloutCompleted', { flagName, finalPercentage: targetPercentage })
  }

  // Emergency flag disable
  async emergencyDisable(flagName, reason = 'Emergency disable') {
    await this.updateFlag(flagName, { 
      enabled: false,
      reason: reason,
      disabledAt: new Date()
    })

    logger.warn(`Emergency disable: ${flagName}`, { reason })
    this.emit('emergencyDisable', { flagName, reason })
  }

  // Get all flags status
  async getAllFlags() {
    const flags = {}
    for (const [name, flag] of this.flags) {
      flags[name] = {
        ...flag.serialize(),
        cacheSize: this._getFlagCacheSize(name)
      }
    }
    return flags
  }

  // Get flag analytics
  async getFlagAnalytics(flagName, timeRange = '24h') {
    if (!this.analytics) {
      return null
    }

    return await this.analytics.getFlagAnalytics(flagName, timeRange)
  }

  // Evaluate with rules engine
  async _evaluateWithRules(flag, context) {
    // Start with base evaluation
    let evaluation = {
      enabled: flag.enabled,
      value: flag.defaultValue,
      reason: 'default'
    }

    // Apply flag-specific logic
    if (!flag.enabled) {
      evaluation.reason = 'flag_disabled'
      evaluation.enabled = false
      return evaluation
    }

    // Apply rollout percentage
    if (flag.rolloutPercentage !== undefined) {
      const userHash = this._getUserHash(context.userId || context.sessionId || 'anonymous', flag.name)
      const userPercentile = userHash % 100

      if (userPercentile >= flag.rolloutPercentage) {
        evaluation.enabled = false
        evaluation.reason = 'rollout_percentage'
        return evaluation
      }
    }

    // Apply targeting rules
    const rules = this.rules.get(flag.name)
    if (rules) {
      const ruleResult = await this._evaluateRules(rules, context)
      if (ruleResult.matched) {
        evaluation = {
          ...evaluation,
          ...ruleResult.result,
          reason: `rule_${ruleResult.ruleName}`
        }
      }
    }

    // Apply A/B testing variants
    if (flag.variants && flag.variants.length > 0) {
      const variant = this._selectVariant(flag.variants, context)
      evaluation.value = variant.value
      evaluation.variant = variant.name
      evaluation.reason = 'variant_selection'
    }

    return evaluation
  }

  // Evaluate targeting rules
  async _evaluateRules(rules, context) {
    for (const rule of rules) {
      try {
        const matched = await this._evaluateRule(rule, context)
        if (matched) {
          return {
            matched: true,
            ruleName: rule.name,
            result: rule.result
          }
        }
      } catch (error) {
        logger.warn(`Rule evaluation error: ${rule.name}`, error)
      }
    }

    return { matched: false }
  }

  // Evaluate individual rule
  async _evaluateRule(rule, context) {
    for (const condition of rule.conditions) {
      const result = await this._evaluateCondition(condition, context)
      
      // AND logic - all conditions must pass
      if (!result) {
        return false
      }
    }

    return true
  }

  // Evaluate individual condition
  async _evaluateCondition(condition, context) {
    const { attribute, operator, value } = condition
    const contextValue = this._getContextValue(context, attribute)

    switch (operator) {
      case 'equals':
        return contextValue === value
      case 'not_equals':
        return contextValue !== value
      case 'contains':
        return Array.isArray(contextValue) ? contextValue.includes(value) : 
               String(contextValue).includes(String(value))
      case 'not_contains':
        return Array.isArray(contextValue) ? !contextValue.includes(value) : 
               !String(contextValue).includes(String(value))
      case 'greater_than':
        return Number(contextValue) > Number(value)
      case 'less_than':
        return Number(contextValue) < Number(value)
      case 'regex':
        return new RegExp(value).test(String(contextValue))
      case 'in':
        return Array.isArray(value) ? value.includes(contextValue) : false
      case 'not_in':
        return Array.isArray(value) ? !value.includes(contextValue) : true
      default:
        logger.warn(`Unknown condition operator: ${operator}`)
        return false
    }
  }

  // Select A/B testing variant
  _selectVariant(variants, context) {
    const userId = context.userId || context.sessionId || 'anonymous'
    const hash = this._getUserHash(userId, 'variant_selection')
    const totalWeight = variants.reduce((sum, v) => sum + (v.weight || 1), 0)
    
    let currentWeight = 0
    const target = hash % totalWeight

    for (const variant of variants) {
      currentWeight += variant.weight || 1
      if (target < currentWeight) {
        return variant
      }
    }

    // Fallback to first variant
    return variants[0]
  }

  // Generate consistent user hash
  _getUserHash(userId, seed) {
    const hash = createHash('md5').update(`${userId}:${seed}`).digest('hex')
    return parseInt(hash.substring(0, 8), 16)
  }

  // Get value from context using dot notation
  _getContextValue(context, path) {
    return path.split('.').reduce((obj, key) => obj?.[key], context)
  }

  // Generate cache key
  _getCacheKey(flagName, context) {
    const contextHash = createHash('md5')
      .update(JSON.stringify(context))
      .digest('hex')
      .substring(0, 8)
    return `${flagName}:${contextHash}`
  }

  // Clear cache for specific flag
  _clearFlagCache(flagName) {
    for (const [key] of this.cache) {
      if (key.startsWith(`${flagName}:`)) {
        this.cache.delete(key)
      }
    }
  }

  // Get cache size for specific flag
  _getFlagCacheSize(flagName) {
    let count = 0
    for (const [key] of this.cache) {
      if (key.startsWith(`${flagName}:`)) {
        count++
      }
    }
    return count
  }

  // Log evaluation for debugging
  _logEvaluation(flagName, context, evaluation) {
    this.evaluationLog.push({
      timestamp: new Date(),
      flagName,
      context: {
        userId: context.userId,
        plan: context.plan,
        environment: context.environment
      },
      result: evaluation
    })

    // Keep log size manageable
    if (this.evaluationLog.length > this.maxLogSize) {
      this.evaluationLog = this.evaluationLog.slice(-this.maxLogSize)
    }
  }
}

/**
 * Individual Feature Flag
 */
class FeatureFlag {
  constructor(name, config) {
    this.name = name
    this.type = config.type || 'boolean' // boolean, string, number, json
    this.enabled = config.enabled !== false
    this.defaultValue = config.defaultValue
    this.rolloutPercentage = config.rolloutPercentage || 0
    this.variants = config.variants || []
    this.description = config.description || ''
    this.tags = config.tags || []
    this.createdAt = new Date()
    this.updatedAt = new Date()
  }

  update(changes) {
    const allowedFields = [
      'enabled', 'defaultValue', 'rolloutPercentage', 
      'variants', 'description', 'tags'
    ]

    for (const [key, value] of Object.entries(changes)) {
      if (allowedFields.includes(key)) {
        this[key] = value
      }
    }

    this.updatedAt = new Date()
  }

  serialize() {
    return {
      name: this.name,
      type: this.type,
      enabled: this.enabled,
      defaultValue: this.defaultValue,
      rolloutPercentage: this.rolloutPercentage,
      variants: this.variants,
      description: this.description,
      tags: this.tags,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }
}

// ========================================
// CONFIGURATION MANAGEMENT SYSTEM
// ========================================

/**
 * Dynamic Configuration Manager
 */
export class ConfigurationManager extends EventEmitter {
  constructor(storage) {
    super()
    this.storage = storage
    this.configs = new Map()
    this.cache = new Map()
    this.watchers = new Map()
    this.updateQueue = []
    this.isProcessingQueue = false
  }

  // Register configuration schema
  registerConfig(name, schema) {
    const config = new DynamicConfig(name, schema)
    this.configs.set(name, config)

    logger.info(`Configuration registered: ${name}`, {
      type: schema.type,
      hot_reload: schema.hotReload
    })
  }

  // Get configuration value
  async getValue(configName, key, defaultValue = null) {
    const config = this.configs.get(configName)
    if (!config) {
      logger.warn(`Configuration not found: ${configName}`)
      return defaultValue
    }

    // Check cache first
    const cacheKey = `${configName}.${key}`
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      // Load from storage
      const value = await this.storage.getConfigValue(configName, key)
      
      // Validate against schema
      const validatedValue = config.validateValue(key, value)
      
      // Cache if cacheable
      if (config.schema.cache !== false) {
        this.cache.set(cacheKey, validatedValue)
      }

      return validatedValue !== undefined ? validatedValue : defaultValue

    } catch (error) {
      logger.error(`Configuration value error: ${configName}.${key}`, error)
      return defaultValue
    }
  }

  // Set configuration value
  async setValue(configName, key, value, options = {}) {
    const config = this.configs.get(configName)
    if (!config) {
      throw new Error(`Configuration not found: ${configName}`)
    }

    // Validate value
    const validatedValue = config.validateValue(key, value)
    
    // Queue update for batch processing
    this.updateQueue.push({
      configName,
      key,
      value: validatedValue,
      options,
      timestamp: new Date()
    })

    // Process queue
    if (!this.isProcessingQueue) {
      this._processUpdateQueue()
    }

    // Immediate cache update for hot-reload configs
    if (config.schema.hotReload) {
      const cacheKey = `${configName}.${key}`
      this.cache.set(cacheKey, validatedValue)
      
      // Notify watchers immediately
      this._notifyWatchers(configName, key, validatedValue)
    }
  }

  // Watch configuration changes
  watchConfig(configName, key, callback) {
    const watchKey = `${configName}.${key}`
    
    if (!this.watchers.has(watchKey)) {
      this.watchers.set(watchKey, new Set())
    }
    
    this.watchers.get(watchKey).add(callback)

    // Return unwatch function
    return () => {
      const callbacks = this.watchers.get(watchKey)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.watchers.delete(watchKey)
        }
      }
    }
  }

  // Get all configurations
  async getAllConfigs() {
    const result = {}
    
    for (const [name, config] of this.configs) {
      result[name] = await this.storage.getAllConfigValues(name)
    }
    
    return result
  }

  // Reload configuration from storage
  async reloadConfig(configName) {
    // Clear cache for this config
    for (const [key] of this.cache) {
      if (key.startsWith(`${configName}.`)) {
        this.cache.delete(key)
      }
    }

    // Reload and notify watchers
    const config = this.configs.get(configName)
    if (config) {
      const allValues = await this.storage.getAllConfigValues(configName)
      
      for (const [key, value] of Object.entries(allValues)) {
        this._notifyWatchers(configName, key, value)
      }
    }

    this.emit('configReloaded', { configName })
  }

  // Process update queue in batches
  async _processUpdateQueue() {
    if (this.isProcessingQueue || this.updateQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      const batch = this.updateQueue.splice(0, 100) // Process in batches of 100
      
      // Group by config for efficient storage updates
      const groupedUpdates = new Map()
      
      for (const update of batch) {
        if (!groupedUpdates.has(update.configName)) {
          groupedUpdates.set(update.configName, [])
        }
        groupedUpdates.get(update.configName).push(update)
      }

      // Apply updates
      for (const [configName, updates] of groupedUpdates) {
        await this.storage.batchUpdateConfig(configName, updates)
        
        // Notify watchers for non-hot-reload configs
        const config = this.configs.get(configName)
        if (!config.schema.hotReload) {
          for (const update of updates) {
            this._notifyWatchers(configName, update.key, update.value)
          }
        }
      }

      logger.debug(`Processed ${batch.length} configuration updates`)

    } catch (error) {
      logger.error('Configuration update batch failed', error)
    } finally {
      this.isProcessingQueue = false
      
      // Process remaining queue if any
      if (this.updateQueue.length > 0) {
        setTimeout(() => this._processUpdateQueue(), 100)
      }
    }
  }

  // Notify configuration watchers
  _notifyWatchers(configName, key, value) {
    const watchKey = `${configName}.${key}`
    const callbacks = this.watchers.get(watchKey)
    
    if (callbacks) {
      for (const callback of callbacks) {
        try {
          callback(value, key, configName)
        } catch (error) {
          logger.error('Configuration watcher error', error)
        }
      }
    }

    this.emit('configChanged', { configName, key, value })
  }
}

/**
 * Dynamic Configuration Definition
 */
class DynamicConfig {
  constructor(name, schema) {
    this.name = name
    this.schema = schema
    this.validators = new Map()
    
    // Setup validators for each field
    if (schema.fields) {
      for (const [fieldName, fieldSchema] of Object.entries(schema.fields)) {
        this.validators.set(fieldName, this._createValidator(fieldSchema))
      }
    }
  }

  validateValue(key, value) {
    const validator = this.validators.get(key)
    
    if (!validator) {
      // No specific validation, return as-is
      return value
    }

    return validator(value)
  }

  _createValidator(fieldSchema) {
    return (value) => {
      // Type validation
      if (fieldSchema.type && typeof value !== fieldSchema.type) {
        // Try to coerce common types
        if (fieldSchema.type === 'number' && !isNaN(Number(value))) {
          value = Number(value)
        } else if (fieldSchema.type === 'boolean') {
          value = Boolean(value)
        } else if (fieldSchema.type === 'string') {
          value = String(value)
        } else {
          throw new Error(`Expected ${fieldSchema.type}, got ${typeof value}`)
        }
      }

      // Range validation for numbers
      if (fieldSchema.type === 'number') {
        if (fieldSchema.min !== undefined && value < fieldSchema.min) {
          throw new Error(`Value ${value} is below minimum ${fieldSchema.min}`)
        }
        if (fieldSchema.max !== undefined && value > fieldSchema.max) {
          throw new Error(`Value ${value} is above maximum ${fieldSchema.max}`)
        }
      }

      // Length validation for strings
      if (fieldSchema.type === 'string') {
        if (fieldSchema.minLength !== undefined && value.length < fieldSchema.minLength) {
          throw new Error(`String length ${value.length} is below minimum ${fieldSchema.minLength}`)
        }
        if (fieldSchema.maxLength !== undefined && value.length > fieldSchema.maxLength) {
          throw new Error(`String length ${value.length} is above maximum ${fieldSchema.maxLength}`)
        }
      }

      // Enum validation
      if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
        throw new Error(`Value ${value} is not in allowed values: ${fieldSchema.enum.join(', ')}`)
      }

      // Pattern validation
      if (fieldSchema.pattern && !new RegExp(fieldSchema.pattern).test(value)) {
        throw new Error(`Value ${value} does not match pattern ${fieldSchema.pattern}`)
      }

      return value
    }
  }
}

// ========================================
// STORAGE ADAPTERS
// ========================================

/**
 * MongoDB Storage Adapter for Feature Flags and Configurations
 */
export class MongoStorageAdapter {
  constructor(db) {
    this.db = db
    this.flagsCollection = db.collection('feature_flags')
    this.configsCollection = db.collection('configurations')
  }

  async saveFlag(name, flagData) {
    await this.flagsCollection.replaceOne(
      { name },
      { ...flagData, updatedAt: new Date() },
      { upsert: true }
    )
  }

  async getFlag(name) {
    return await this.flagsCollection.findOne({ name })
  }

  async getAllFlags() {
    return await this.flagsCollection.find({}).toArray()
  }

  async getConfigValue(configName, key) {
    const config = await this.configsCollection.findOne(
      { name: configName },
      { projection: { [`values.${key}`]: 1 } }
    )
    return config?.values?.[key]
  }

  async getAllConfigValues(configName) {
    const config = await this.configsCollection.findOne({ name: configName })
    return config?.values || {}
  }

  async batchUpdateConfig(configName, updates) {
    const updateDoc = {}
    
    for (const update of updates) {
      updateDoc[`values.${update.key}`] = update.value
    }

    await this.configsCollection.updateOne(
      { name: configName },
      { 
        $set: { 
          ...updateDoc,
          updatedAt: new Date()
        }
      },
      { upsert: true }
    )
  }
}

/**
 * Redis Storage Adapter (for caching and fast access)
 */
export class RedisStorageAdapter {
  constructor(redisClient) {
    this.redis = redisClient
    this.flagPrefix = 'ff:'
    this.configPrefix = 'config:'
  }

  async saveFlag(name, flagData) {
    await this.redis.setex(
      `${this.flagPrefix}${name}`,
      3600, // 1 hour TTL
      JSON.stringify(flagData)
    )
  }

  async getFlag(name) {
    const data = await this.redis.get(`${this.flagPrefix}${name}`)
    return data ? JSON.parse(data) : null
  }

  async getConfigValue(configName, key) {
    return await this.redis.hget(`${this.configPrefix}${configName}`, key)
  }

  async batchUpdateConfig(configName, updates) {
    const pipeline = this.redis.pipeline()
    
    for (const update of updates) {
      pipeline.hset(`${this.configPrefix}${configName}`, update.key, JSON.stringify(update.value))
    }
    
    await pipeline.exec()
  }
}

// ========================================
// ANALYTICS AND MONITORING
// ========================================

/**
 * Feature Flag Analytics
 */
export class FeatureFlagAnalytics {
  constructor(storage) {
    this.storage = storage
    this.metrics = new Map()
  }

  async trackFlagEvaluation(flagName, enabled, context) {
    const metric = {
      timestamp: new Date(),
      flagName,
      enabled,
      userId: context.userId,
      plan: context.plan,
      environment: context.environment || 'production'
    }

    // Store in time-series format
    await this.storage.insertMetric('flag_evaluations', metric)

    // Update real-time counters
    const key = `${flagName}:${enabled ? 'enabled' : 'disabled'}`
    const current = this.metrics.get(key) || 0
    this.metrics.set(key, current + 1)
  }

  async getFlagAnalytics(flagName, timeRange = '24h') {
    const endTime = new Date()
    const startTime = new Date(endTime.getTime() - this._parseTimeRange(timeRange))

    const metrics = await this.storage.queryMetrics('flag_evaluations', {
      flagName,
      timestamp: { $gte: startTime, $lte: endTime }
    })

    return this._processMetrics(metrics)
  }

  _processMetrics(metrics) {
    const analysis = {
      totalEvaluations: metrics.length,
      enabledCount: 0,
      disabledCount: 0,
      uniqueUsers: new Set(),
      byPlan: {},
      timeline: []
    }

    for (const metric of metrics) {
      if (metric.enabled) {
        analysis.enabledCount++
      } else {
        analysis.disabledCount++
      }

      if (metric.userId) {
        analysis.uniqueUsers.add(metric.userId)
      }

      if (metric.plan) {
        analysis.byPlan[metric.plan] = (analysis.byPlan[metric.plan] || 0) + 1
      }
    }

    analysis.uniqueUsers = analysis.uniqueUsers.size
    analysis.enablementRate = analysis.totalEvaluations > 0 
      ? (analysis.enabledCount / analysis.totalEvaluations) * 100 
      : 0

    return analysis
  }

  _parseTimeRange(timeRange) {
    const units = {
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000,
      'w': 7 * 24 * 60 * 60 * 1000
    }

    const match = timeRange.match(/^(\d+)([hdw])$/)
    if (match) {
      const [, value, unit] = match
      return parseInt(value) * units[unit]
    }

    return 24 * 60 * 60 * 1000 // Default to 24 hours
  }
}

// ========================================
// EXPORTS AND INITIALIZATION
// ========================================

export { FeatureFlagManager, ConfigurationManager, MongoStorageAdapter, RedisStorageAdapter, FeatureFlagAnalytics }

logger.info('🎚️ Feature flags and configuration management system initialized')