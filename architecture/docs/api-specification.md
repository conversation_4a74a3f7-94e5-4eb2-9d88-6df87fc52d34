# 📋 NeuroColony API Specification

## Overview

NeuroColony provides a comprehensive RESTful API for managing email sequences, user accounts, billing, and analytics. The API follows DDD (Domain-Driven Design) principles with CQRS (Command Query Responsibility Segregation) patterns.

**Base URL**: `https://api.sequenceai.app`  
**Version**: v1  
**Protocol**: HTTPS only  
**Authentication**: JWT Bearer tokens  

## 🔐 Authentication

All API endpoints require authentication unless otherwise specified.

### Authentication Header
```http
Authorization: Bearer <jwt_token>
```

### JWT Token Structure
```json
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "plan": "pro|business|free",
  "iat": **********,
  "exp": **********
}
```

### Public Endpoints
- `POST /auth/login`
- `POST /auth/register`
- `GET /health`
- `GET /pricing`

---

## 📧 Email Sequences API

### Create Email Sequence

**Command**: `CreateEmailSequenceCommand`

```http
POST /api/sequences/generate
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "name": "Product Launch Sequence",
  "businessInfo": {
    "name": "TechCorp Inc",
    "industry": "Software",
    "targetAudience": "B2B SaaS companies",
    "productService": "Project management tool",
    "tone": "professional",
    "goals": ["nurture leads", "increase conversions"]
  },
  "settings": {
    "sequenceLength": 5,
    "emailFrequency": "every_2_days",
    "includeSubjects": true,
    "includePreheaders": true,
    "emailLength": "medium",
    "callToAction": "Sign up for free trial",
    "personalizationLevel": "high"
  }
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": {
    "sequenceId": "seq_123456789",
    "name": "Product Launch Sequence",
    "status": "generated",
    "emails": [
      {
        "id": "email_001",
        "subject": "Welcome to the future of project management",
        "preheader": "Discover how TechCorp streamlines your workflow",
        "content": "Hi there,\n\nWelcome to TechCorp...",
        "callToAction": {
          "text": "Start Your Free Trial",
          "url": "https://techcorp.com/signup"
        },
        "dayInSequence": 1,
        "estimatedSendTime": "immediately"
      }
    ],
    "metadata": {
      "generatedAt": "2024-01-15T10:30:00Z",
      "estimatedReadTime": "2 minutes",
      "sequenceLength": 5,
      "totalWords": 1250
    }
  },
  "usage": {
    "sequencesUsed": 15,
    "sequencesLimit": 75,
    "usagePercentage": 20,
    "resetDate": "2024-02-01T00:00:00Z"
  }
}
```

### Get Email Sequence

**Query**: `GetEmailSequenceByIdQuery`

```http
GET /api/sequences/{sequenceId}
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": "seq_123456789",
    "name": "Product Launch Sequence",
    "status": "published",
    "userEmail": "<EMAIL>",
    "businessInfo": {
      "name": "TechCorp Inc",
      "industry": "Software"
    },
    "emails": [...],
    "performance": {
      "totalSent": 1250,
      "totalOpens": 375,
      "totalClicks": 89,
      "openRate": 30.0,
      "clickRate": 7.12,
      "conversionRate": 2.8
    },
    "createdAt": "2024-01-15T10:30:00Z",
    "lastModified": "2024-01-15T14:22:00Z"
  }
}
```

### List Email Sequences

**Query**: `GetUserEmailSequencesQuery`

```http
GET /api/sequences
Authorization: Bearer <token>
```

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (`draft`, `published`, `archived`)
- `search` (optional): Search by name or keywords
- `sortBy` (optional): Sort field (`created`, `modified`, `name`, `performance`)
- `sortOrder` (optional): Sort direction (`asc`, `desc`)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "sequences": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "filters": {
      "status": "all",
      "search": "",
      "appliedFilters": 0
    }
  }
}
```

### Update Email Sequence

**Command**: `UpdateEmailSequenceCommand`

```http
PUT /api/sequences/{sequenceId}
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "name": "Updated Sequence Name",
  "emails": [
    {
      "id": "email_001",
      "subject": "Updated subject line",
      "content": "Updated email content...",
      "callToAction": {
        "text": "New CTA Text",
        "url": "https://example.com/new-url"
      }
    }
  ]
}
```

### Delete Email Sequence

**Command**: `ArchiveEmailSequenceCommand`

```http
DELETE /api/sequences/{sequenceId}
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Email sequence archived successfully",
  "data": {
    "sequenceId": "seq_123456789",
    "archivedAt": "2024-01-15T16:45:00Z"
  }
}
```

### Optimize Email Sequence

**Command**: `OptimizeEmailSequenceCommand`

```http
POST /api/sequences/{sequenceId}/optimize
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "optimizationType": "subject_lines|content|timing|cta",
  "optimizationGoal": "open_rate|click_rate|conversion_rate",
  "testingParameters": {
    "sampleSize": 1000,
    "testDuration": "7_days",
    "confidenceLevel": 95
  }
}
```

---

## 👤 User Management API

### Get User Profile

```http
GET /api/user/profile
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": "user_123456",
    "email": "<EMAIL>",
    "name": "John Doe",
    "plan": "pro",
    "subscription": {
      "status": "active",
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-02-01T00:00:00Z",
      "cancelAtPeriodEnd": false
    },
    "usage": {
      "currentPeriod": {
        "sequencesGenerated": 15,
        "overageSequences": 0,
        "overageCharges": 0,
        "startDate": "2024-01-01T00:00:00Z",
        "endDate": "2024-02-01T00:00:00Z"
      },
      "limits": {
        "sequencesPerMonth": 75,
        "overageAllowed": true
      }
    },
    "preferences": {
      "emailNotifications": true,
      "weeklyReports": true,
      "marketingEmails": false
    },
    "createdAt": "2023-12-01T00:00:00Z"
  }
}
```

### Update User Profile

```http
PUT /api/user/profile
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "name": "John Smith",
  "preferences": {
    "emailNotifications": false,
    "weeklyReports": true,
    "marketingEmails": false
  }
}
```

---

## 📊 Usage Tracking API

### Get Usage Statistics

```http
GET /api/usage/stats
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "currentPeriod": {
      "sequencesGenerated": 15,
      "sequencesLimit": 75,
      "overageSequences": 0,
      "overageCharges": 0,
      "usagePercentage": 20,
      "status": "normal",
      "daysRemaining": 16,
      "estimatedMonthlyUsage": 28
    },
    "limits": {
      "plan": "pro",
      "sequencesPerMonth": 75,
      "overageRate": 3.00,
      "overageEnabled": false
    },
    "period": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-02-01T00:00:00Z",
      "daysInPeriod": 31,
      "daysElapsed": 15
    }
  }
}
```

### Get Usage History

```http
GET /api/usage/history
Authorization: Bearer <token>
```

**Query Parameters**:
- `months` (optional): Number of months to include (default: 6, max: 24)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "period": "2023-12",
        "sequencesGenerated": 42,
        "overageSequences": 0,
        "overageCharges": 0,
        "plan": "pro"
      },
      {
        "period": "2024-01",
        "sequencesGenerated": 15,
        "overageSequences": 0,
        "overageCharges": 0,
        "plan": "pro"
      }
    ],
    "trends": {
      "averageMonthlyUsage": 28.5,
      "usageGrowth": 12.5,
      "peakUsageMonth": "2023-11",
      "totalSequencesGenerated": 256
    },
    "projections": {
      "estimatedCurrentMonth": 28,
      "likelyOverage": false,
      "recommendedPlan": "pro"
    }
  }
}
```

### Check Generation Permission

```http
GET /api/usage/check-generation
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "canGenerate": true,
    "reason": null,
    "usage": {
      "current": 15,
      "limit": 75,
      "remaining": 60
    },
    "warnings": []
  }
}
```

**Response when at limit** (200 OK):
```json
{
  "success": true,
  "data": {
    "canGenerate": false,
    "reason": "Monthly limit reached",
    "usage": {
      "current": 75,
      "limit": 75,
      "remaining": 0
    },
    "options": [
      {
        "type": "overage",
        "available": true,
        "rate": 3.00,
        "description": "Continue generating at $3 per sequence"
      },
      {
        "type": "upgrade",
        "available": true,
        "plan": "business",
        "description": "Upgrade to Business plan for 200 sequences/month"
      }
    ]
  }
}
```

### Enable Overage Billing

```http
POST /api/usage/overage-consent
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "consentGiven": true,
  "acknowledgment": "I understand I will be charged $3 for each additional sequence generated beyond my monthly limit."
}
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Overage billing enabled successfully",
  "data": {
    "overageEnabled": true,
    "rate": 3.00,
    "effectiveDate": "2024-01-15T16:30:00Z"
  }
}
```

---

## 💳 Billing & Payments API

### Get Billing Information

```http
GET /api/billing/info
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "sub_123456789",
      "status": "active",
      "plan": "pro",
      "amount": 2900,
      "currency": "usd",
      "interval": "month",
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-02-01T00:00:00Z",
      "cancelAtPeriodEnd": false
    },
    "paymentMethod": {
      "type": "card",
      "last4": "4242",
      "brand": "visa",
      "expMonth": 12,
      "expYear": 2025
    },
    "upcomingInvoice": {
      "amount": 2900,
      "date": "2024-02-01T00:00:00Z",
      "description": "Pro Plan - February 2024"
    },
    "usageCharges": {
      "currentPeriod": 0,
      "overageSequences": 0,
      "estimatedTotal": 2900
    }
  }
}
```

### Get Invoice History

```http
GET /api/billing/invoices
Authorization: Bearer <token>
```

**Query Parameters**:
- `limit` (optional): Number of invoices (default: 12)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "invoices": [
      {
        "id": "in_123456789",
        "number": "INV-2024-0001",
        "status": "paid",
        "amount": 2900,
        "currency": "usd",
        "date": "2024-01-01T00:00:00Z",
        "paidAt": "2024-01-01T00:15:00Z",
        "items": [
          {
            "description": "Pro Plan - January 2024",
            "amount": 2900,
            "quantity": 1
          }
        ],
        "downloadUrl": "https://api.sequenceai.app/billing/invoices/in_123456789/pdf"
      }
    ],
    "hasMore": false
  }
}
```

### Update Payment Method

```http
POST /api/billing/payment-method
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "paymentMethodId": "pm_123456789"
}
```

### Cancel Subscription

```http
POST /api/billing/cancel
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "cancelAtPeriodEnd": true,
  "reason": "switching_providers",
  "feedback": "Found a more suitable solution for our needs"
}
```

---

## 📈 Analytics API

### Get Sequence Performance

```http
GET /api/analytics/sequence/{sequenceId}/performance
Authorization: Bearer <token>
```

**Query Parameters**:
- `dateRange` (optional): `7d`, `30d`, `90d`, `1y` (default: `30d`)
- `metrics` (optional): Comma-separated list of metrics

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalSent": 1250,
      "totalOpens": 375,
      "totalClicks": 89,
      "totalConversions": 35,
      "openRate": 30.0,
      "clickRate": 7.12,
      "conversionRate": 2.8,
      "revenue": 3500.00
    },
    "byEmail": [
      {
        "emailId": "email_001",
        "subject": "Welcome to the future of project management",
        "dayInSequence": 1,
        "sent": 250,
        "opens": 95,
        "clicks": 23,
        "conversions": 8,
        "openRate": 38.0,
        "clickRate": 9.2,
        "conversionRate": 3.2
      }
    ],
    "timeline": [
      {
        "date": "2024-01-15",
        "sent": 50,
        "opens": 15,
        "clicks": 4,
        "conversions": 1
      }
    ]
  }
}
```

### Get Dashboard Analytics

```http
GET /api/analytics/dashboard
Authorization: Bearer <token>
```

**Query Parameters**:
- `dateRange` (optional): `7d`, `30d`, `90d`, `1y` (default: `30d`)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalSequences": 45,
      "activeSequences": 12,
      "totalEmailsSent": 15750,
      "averageOpenRate": 28.5,
      "averageClickRate": 6.8,
      "totalRevenue": 45750.00
    },
    "topPerforming": {
      "sequences": [...],
      "emails": [...]
    },
    "trends": {
      "sequenceCreation": [...],
      "engagement": [...],
      "revenue": [...]
    },
    "benchmarks": {
      "industryAverageOpenRate": 25.2,
      "industryAverageClickRate": 5.4,
      "yourPerformanceVsIndustry": {
        "openRate": "+13.1%",
        "clickRate": "+25.9%"
      }
    }
  }
}
```

---

## 🔍 Search API

### Search Sequences

```http
GET /api/search/sequences
Authorization: Bearer <token>
```

**Query Parameters**:
- `q` (required): Search query
- `filters` (optional): JSON object with filters
- `page` (optional): Page number
- `limit` (optional): Results per page

**Request Example**:
```http
GET /api/search/sequences?q=product%20launch&filters={"status":"published","industry":"software"}&page=1&limit=10
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "seq_123456789",
        "name": "Product Launch Sequence",
        "snippet": "...launch your product with confidence...",
        "relevanceScore": 0.95,
        "matchedFields": ["name", "content"],
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 8,
      "pages": 1
    },
    "facets": {
      "status": {
        "published": 5,
        "draft": 2,
        "archived": 1
      },
      "industry": {
        "software": 4,
        "ecommerce": 2,
        "healthcare": 2
      }
    }
  }
}
```

---

## ⚠️ Error Handling

### Error Response Format

All API errors follow this consistent format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "businessInfo.name",
      "issue": "Business name is required"
    },
    "requestId": "req_123456789",
    "timestamp": "2024-01-15T16:30:00Z"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid JWT token |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource doesn't exist |
| `RATE_LIMIT_EXCEEDED` | 429 | API rate limit exceeded |
| `USAGE_LIMIT_EXCEEDED` | 403 | Monthly sequence limit reached |
| `PAYMENT_REQUIRED` | 402 | Payment method required for overage |
| `INTERNAL_ERROR` | 500 | Unexpected server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

### Domain-Specific Errors

```json
{
  "success": false,
  "error": {
    "code": "DOMAIN_VALIDATION_ERROR",
    "message": "Email sequence validation failed",
    "details": {
      "violations": [
        {
          "rule": "SequenceLengthSpecification",
          "message": "Sequence must contain between 3 and 10 emails"
        }
      ]
    }
  }
}
```

---

## 📋 Rate Limiting

### Rate Limit Headers

All API responses include rate limiting headers:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640998800
X-RateLimit-Window: 3600
```

### Rate Limits by Plan

| Plan | Requests/Hour | Burst Requests |
|------|---------------|----------------|
| Free | 100 | 20 |
| Pro | 1,000 | 50 |
| Business | 5,000 | 100 |

### Rate Limit Error Response

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "API rate limit exceeded",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "resetAt": "2024-01-15T17:00:00Z",
      "retryAfter": 3600
    }
  }
}
```

---

## 🔄 Webhooks

### Webhook Events

NeuroColony sends webhooks for various events:

| Event | Description |
|-------|-------------|
| `sequence.generated` | New email sequence created |
| `sequence.published` | Sequence published and active |
| `sequence.performance.updated` | Performance metrics updated |
| `usage.warning` | Usage approaching limit (80%) |
| `usage.limit_reached` | Monthly limit reached |
| `billing.invoice.paid` | Invoice payment successful |
| `billing.payment.failed` | Payment attempt failed |
| `subscription.updated` | Subscription plan changed |

### Webhook Payload Example

```json
{
  "id": "evt_123456789",
  "event": "sequence.generated",
  "createdAt": "2024-01-15T16:30:00Z",
  "data": {
    "sequenceId": "seq_123456789",
    "userId": "user_123456",
    "name": "Product Launch Sequence",
    "status": "generated",
    "emailCount": 5
  },
  "metadata": {
    "source": "api",
    "userAgent": "NeuroColony Web App/1.0"
  }
}
```

### Webhook Security

Webhooks are signed with HMAC-SHA256:

```http
X-Signature-256: sha256=5d41402abc4b2a76b9719d911017c592
X-Timestamp: 1640998800
```

---

## 📚 SDKs & Libraries

### Official SDKs

- **Node.js**: `npm install @sequenceai/node-sdk`
- **Python**: `pip install sequenceai`
- **PHP**: `composer require sequenceai/php-sdk`
- **Ruby**: `gem install sequenceai`

### SDK Example (Node.js)

```javascript
import NeuroColony from '@sequenceai/node-sdk'

const client = new NeuroColony({
  apiKey: process.env.SEQUENCEAI_API_KEY,
  environment: 'production' // or 'sandbox'
})

// Generate a sequence
const sequence = await client.sequences.create({
  name: 'Welcome Series',
  businessInfo: {
    name: 'My Company',
    industry: 'Software'
  },
  settings: {
    sequenceLength: 5,
    emailFrequency: 'every_2_days'
  }
})

console.log(sequence.data.sequenceId)
```

---

## 🧪 Testing & Sandbox

### Sandbox Environment

**Base URL**: `https://api-sandbox.sequenceai.app`

The sandbox environment provides:
- Full API functionality without charges
- Test data and sequences
- Webhook testing endpoints
- Performance simulation

### Test API Keys

Sandbox API keys start with `sk_test_` and can be found in your dashboard.

### Mock Responses

The sandbox supports mock responses for testing error scenarios:

```http
POST /api/sequences/generate
X-Mock-Response: rate_limit_exceeded
```

---

## 📖 OpenAPI Specification

The complete OpenAPI 3.0 specification is available at:
- **Production**: `https://api.sequenceai.app/openapi.json`
- **Sandbox**: `https://api-sandbox.sequenceai.app/openapi.json`

### Interactive Documentation

Explore the API interactively:
- **Swagger UI**: `https://docs.sequenceai.app/api`
- **Postman Collection**: [Download Collection](https://api.sequenceai.app/postman-collection.json)

---

## 🆘 Support

### API Support Channels

- **Documentation**: https://docs.sequenceai.app
- **Email**: <EMAIL>
- **Discord**: https://discord.gg/sequenceai
- **Status Page**: https://status.sequenceai.app

### Request Support

When contacting support, please include:
- Request ID (from error response)
- Timestamp of the issue
- Complete request/response details
- Expected vs actual behavior

---

*API Specification v1.0 - Last updated: January 15, 2024*