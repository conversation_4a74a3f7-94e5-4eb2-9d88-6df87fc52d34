# 🛠️ NeuroColony Developer Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Architecture Overview](#architecture-overview)
3. [Domain-Driven Design (DDD)](#domain-driven-design-ddd)
4. [Development Environment](#development-environment)
5. [Code Organization](#code-organization)
6. [Testing Strategy](#testing-strategy)
7. [API Development](#api-development)
8. [Database Patterns](#database-patterns)
9. [Performance Guidelines](#performance-guidelines)
10. [Security Best Practices](#security-best-practices)
11. [Debugging & Monitoring](#debugging--monitoring)
12. [Deployment Guide](#deployment-guide)

---

## 🚀 Getting Started

### Prerequisites

```bash
# Required tools
node >= 18.0.0
npm >= 9.0.0
docker >= 20.10.0
docker-compose >= 2.0.0
mongodb >= 7.0
redis >= 7.0
```

### Quick Setup

```bash
# Clone repository
git clone https://github.com/your-org/sequenceai.git
cd sequenceai

# Install dependencies
npm install
cd backend && npm install
cd ../frontend && npm install
cd ../architecture/testing && npm install

# Environment setup
cp .env.example .env
# Edit .env with your configuration

# Start development environment
docker-compose up -d
npm run dev
```

### Project Structure

```
sequenceai/
├── architecture/                    # DDD Architecture & Patterns
│   ├── ddd/                        # Domain-Driven Design implementation
│   │   ├── domain-core.js          # Base DDD classes
│   │   ├── domains/                # Domain modules
│   │   └── application/            # CQRS commands & queries
│   ├── testing/                    # Testing framework
│   ├── dev-tools/                  # Development utilities
│   ├── ci-cd/                      # CI/CD pipeline configuration
│   └── docs/                       # Architecture documentation
├── backend/                        # Node.js API server
│   ├── controllers/                # Express route handlers
│   ├── models/                     # Mongoose data models
│   ├── services/                   # Business logic services
│   ├── middleware/                 # Express middleware
│   ├── routes/                     # API route definitions
│   └── utils/                      # Utility functions
├── frontend/                       # React application
│   ├── src/
│   │   ├── components/             # Reusable UI components
│   │   ├── pages/                  # Route-based page components
│   │   ├── hooks/                  # Custom React hooks
│   │   ├── context/                # React context providers
│   │   └── utils/                  # Frontend utilities
├── docker-compose.yml              # Development environment
├── Dockerfile.test                 # Testing container
└── package.json                    # Root package configuration
```

---

## 🏗️ Architecture Overview

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React SPA] --> B[API Client]
    end
    
    subgraph "API Layer"
        C[Express.js] --> D[Authentication]
        C --> E[Rate Limiting]
        C --> F[Validation]
    end
    
    subgraph "Application Layer"
        G[Command Handlers] --> H[Domain Services]
        I[Query Handlers] --> J[Read Models]
    end
    
    subgraph "Domain Layer"
        K[Aggregates] --> L[Entities]
        K --> M[Value Objects]
        N[Domain Events] --> O[Event Handlers]
    end
    
    subgraph "Infrastructure Layer"
        P[MongoDB] --> Q[Repositories]
        R[Redis] --> S[Cache]
        T[External APIs] --> U[AI Services]
    end
    
    B --> C
    F --> G
    F --> I
    H --> K
    Q --> K
    S --> I
    U --> H
```

### Core Principles

1. **Domain-Driven Design (DDD)**: Business logic isolated in domain layer
2. **CQRS (Command Query Responsibility Segregation)**: Separate read/write models
3. **Event Sourcing**: Domain events for audit trail and integration
4. **Clean Architecture**: Dependency inversion and separation of concerns
5. **Microservices Ready**: Modular design for future decomposition

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| Frontend | React 18 + Vite | Modern SPA with hot reload |
| API | Express.js + Node.js | RESTful API server |
| Database | MongoDB + Mongoose | Document database with ODM |
| Cache | Redis | Session storage and caching |
| Authentication | JWT + bcrypt | Stateless authentication |
| Testing | Mocha + Chai + Sinon | Comprehensive testing framework |
| DevOps | Docker + GitHub Actions | Containerization and CI/CD |

---

## 🎯 Domain-Driven Design (DDD)

### Domain Model Structure

```javascript
// Domain Entity Example
export class EmailSequence extends AggregateRoot {
  constructor(id, { name, userEmail, businessInfo, settings }) {
    super(id)
    this.name = name
    this.userEmail = EmailAddress.create(userEmail)
    this.businessInfo = BusinessInfo.create(businessInfo)
    this.settings = SequenceSettings.create(settings)
    this.emails = []
    this.status = SequenceStatus.DRAFT
    
    // Domain invariants
    this._validateSequenceConstraints()
    
    // Raise domain event
    this.raiseDomainEvent(new EmailSequenceCreated(this.id, this.name))
  }
  
  // Business methods
  addEmail(emailData) {
    const email = Email.create(emailData)
    
    // Business rules
    if (this.emails.length >= this.settings.maxEmails) {
      throw new DomainValidationError('Maximum email limit reached')
    }
    
    this.emails.push(email)
    this.raiseDomainEvent(new EmailAddedToSequence(this.id, email.id))
  }
  
  publish() {
    // Business invariants
    if (this.emails.length === 0) {
      throw new DomainValidationError('Cannot publish empty sequence')
    }
    
    this.status = SequenceStatus.PUBLISHED
    this.publishedAt = new Date()
    this.raiseDomainEvent(new EmailSequencePublished(this.id))
  }
}
```

### Value Objects

```javascript
// Email Address Value Object
export class EmailAddress extends ValueObject {
  constructor(value) {
    super()
    this._validate(value)
    this.value = value.toLowerCase().trim()
  }
  
  static create(value) {
    return new EmailAddress(value)
  }
  
  _validate(value) {
    if (!value || typeof value !== 'string') {
      throw new DomainValidationError('Email address is required')
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) {
      throw new DomainValidationError('Invalid email address format')
    }
  }
  
  equals(other) {
    return other instanceof EmailAddress && other.value === this.value
  }
}
```

### Repository Pattern

```javascript
// Repository Interface
export class EmailSequenceRepository {
  async save(emailSequence) {
    throw new Error('Method must be implemented')
  }
  
  async findById(id) {
    throw new Error('Method must be implemented')
  }
  
  async findByUserEmail(userEmail) {
    throw new Error('Method must be implemented')
  }
}

// MongoDB Implementation
export class MongoEmailSequenceRepository extends EmailSequenceRepository {
  constructor(db) {
    super()
    this.collection = db.collection('emailSequences')
  }
  
  async save(emailSequence) {
    const document = this._toDocument(emailSequence)
    const result = await this.collection.replaceOne(
      { _id: emailSequence.id },
      document,
      { upsert: true }
    )
    
    // Handle domain events
    await this._publishDomainEvents(emailSequence)
    
    return result
  }
  
  async findById(id) {
    const document = await this.collection.findOne({ _id: id })
    return document ? this._toDomain(document) : null
  }
}
```

### CQRS Commands and Queries

```javascript
// Command Definition
export class CreateEmailSequenceCommand {
  constructor({ userEmail, name, businessInfo, settings }) {
    this.userEmail = userEmail
    this.name = name
    this.businessInfo = businessInfo
    this.settings = settings
    this.timestamp = new Date()
  }
}

// Command Handler
export class CreateEmailSequenceCommandHandler {
  constructor(repository, domainService, eventBus) {
    this.repository = repository
    this.domainService = domainService
    this.eventBus = eventBus
  }
  
  async handle(command) {
    // Validate command
    await this._validateCommand(command)
    
    // Create domain object
    const sequence = await this.domainService.createSequence(
      command.userEmail,
      command.name,
      command.businessInfo,
      command.settings
    )
    
    // Persist
    await this.repository.save(sequence)
    
    // Return result
    return {
      sequenceId: sequence.id,
      status: 'created'
    }
  }
}

// Query Definition
export class GetEmailSequenceByIdQuery {
  constructor(sequenceId, userEmail) {
    this.sequenceId = sequenceId
    this.userEmail = userEmail
  }
}

// Query Handler
export class GetEmailSequenceByIdQueryHandler {
  constructor(repository) {
    this.repository = repository
  }
  
  async handle(query) {
    const sequence = await this.repository.findById(query.sequenceId)
    
    if (!sequence) {
      throw new ResourceNotFoundError('Email sequence not found')
    }
    
    // Authorization check
    if (sequence.userEmail.value !== query.userEmail) {
      throw new UnauthorizedError('Access denied')
    }
    
    return this._toReadModel(sequence)
  }
}
```

---

## 💻 Development Environment

### Docker Development Setup

```yaml
# docker-compose.yml
version: '3.8'
services:
  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_DATABASE: sequenceai_dev
    volumes:
      - mongodb_data:/data/db
      
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
      
  backend:
    build: 
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      MONGODB_URI: mongodb://mongodb:27017/sequenceai_dev
      REDIS_URL: redis://redis:6379
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules
    depends_on:
      - mongodb
      - redis
      
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      VITE_API_URL: http://localhost:3001
    volumes:
      - ./frontend:/app/frontend
      - /app/frontend/node_modules
```

### Environment Variables

```bash
# .env.development
NODE_ENV=development
PORT=3001

# Database
MONGODB_URI=mongodb://localhost:27017/sequenceai_dev
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-dev-jwt-secret
JWT_EXPIRES_IN=7d

# External APIs
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Email
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=

# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Performance
CACHE_TTL=3600
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000
```

### Development Scripts

```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && nodemon server.js",
    "dev:frontend": "cd frontend && npm run dev",
    "dev:full": "docker-compose up",
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "cd architecture/testing && npm run test:unit",
    "test:integration": "cd architecture/testing && npm run test:integration",
    "test:e2e": "cd architecture/testing && npm run test:e2e",
    "test:watch": "cd architecture/testing && npm run test:watch",
    "lint": "eslint . --ext .js,.jsx --fix",
    "format": "prettier --write .",
    "typecheck": "cd backend && npx jsdoc -t unused -r . || true",
    "db:migrate": "cd backend && node scripts/migrate.js",
    "db:seed": "cd backend && node scripts/seed.js",
    "build": "npm run build:frontend && npm run build:backend",
    "build:frontend": "cd frontend && npm run build",
    "build:backend": "cd backend && npm run build"
  }
}
```

---

## 📂 Code Organization

### Backend Structure

```
backend/
├── controllers/           # HTTP request handlers
│   ├── authController.js
│   ├── sequenceController.js
│   ├── usageController.js
│   └── analyticsController.js
├── middleware/           # Express middleware
│   ├── auth.js          # JWT authentication
│   ├── validation.js    # Request validation
│   ├── rateLimit.js     # Rate limiting
│   └── errorHandler.js  # Global error handling
├── models/              # Mongoose schemas
│   ├── User.js
│   ├── EmailSequence.js
│   └── UsageRecord.js
├── routes/              # Route definitions
│   ├── auth.js
│   ├── sequences.js
│   ├── usage.js
│   └── analytics.js
├── services/            # Business logic
│   ├── authService.js
│   ├── sequenceService.js
│   ├── usageService.js
│   └── aiService.js
├── utils/               # Utilities
│   ├── logger.js
│   ├── email.js
│   ├── cache.js
│   └── helpers.js
└── server.js           # Application entry point
```

### Frontend Structure

```
frontend/src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   │   ├── Button.jsx
│   │   ├── Modal.jsx
│   │   └── LoadingSpinner.jsx
│   ├── forms/          # Form components
│   │   ├── SequenceForm.jsx
│   │   └── BusinessInfoForm.jsx
│   ├── usage/          # Usage-related components
│   │   ├── UsageDashboard.jsx
│   │   ├── UsageIndicator.jsx
│   │   └── UsageNotification.jsx
│   └── layout/         # Layout components
│       ├── Navbar.jsx
│       ├── Sidebar.jsx
│       └── Footer.jsx
├── pages/              # Route components
│   ├── Dashboard.jsx
│   ├── GeneratorPage.jsx
│   ├── SequencesPage.jsx
│   └── SettingsPage.jsx
├── hooks/              # Custom React hooks
│   ├── useAuth.js
│   ├── useUsage.js
│   ├── useSequences.js
│   └── useApi.js
├── context/            # React context
│   ├── AuthContext.jsx
│   ├── UsageContext.jsx
│   └── NotificationContext.jsx
├── utils/              # Utilities
│   ├── api.js         # API client
│   ├── auth.js        # Auth helpers
│   ├── formatting.js  # Data formatting
│   └── constants.js   # App constants
└── App.jsx            # Root component
```

### Naming Conventions

```javascript
// Files and directories: kebab-case
email-sequence-service.js
usage-dashboard.jsx

// Classes: PascalCase
class EmailSequenceService {}
class UsageDashboard extends React.Component {}

// Functions and variables: camelCase
const getUserSequences = () => {}
const sequenceData = {}

// Constants: SCREAMING_SNAKE_CASE
const MAX_SEQUENCE_LENGTH = 10
const API_BASE_URL = 'https://api.sequenceai.app'

// Database fields: snake_case
{
  user_email: '<EMAIL>',
  created_at: new Date(),
  usage_limit: 75
}

// API endpoints: kebab-case
/api/email-sequences
/api/usage-statistics
/api/user-profile
```

---

## 🧪 Testing Strategy

### Testing Pyramid

```mermaid
graph TB
    A[E2E Tests<br/>10%] --> B[Integration Tests<br/>30%]
    B --> C[Unit Tests<br/>60%]
    
    style A fill:#ff9999
    style B fill:#ffcc99
    style C fill:#99ff99
```

### Unit Testing

```javascript
// architecture/testing/unit/domain/email-sequence.test.js
import { expect } from 'chai'
import { EmailSequence } from '../../../ddd/domains/email-sequence/entities.js'
import { EmailAddress } from '../../../ddd/domains/email-sequence/entities.js'
import { DomainValidationError } from '../../../ddd/domain-core.js'

describe('EmailSequence Domain Entity', () => {
  describe('Creation', () => {
    it('should create valid email sequence', () => {
      const sequence = new EmailSequence('seq_123', {
        name: 'Test Sequence',
        userEmail: '<EMAIL>',
        businessInfo: {
          name: 'Test Company',
          industry: 'Software'
        },
        settings: {
          sequenceLength: 5,
          emailFrequency: 'every_2_days'
        }
      })
      
      expect(sequence.name).to.equal('Test Sequence')
      expect(sequence.userEmail.value).to.equal('<EMAIL>')
      expect(sequence.emails).to.have.length(0)
      expect(sequence.status).to.equal('draft')
    })
    
    it('should validate required fields', () => {
      expect(() => {
        new EmailSequence('seq_123', {
          // Missing required fields
        })
      }).to.throw(DomainValidationError)
    })
  })
  
  describe('Business Logic', () => {
    let sequence
    
    beforeEach(() => {
      sequence = new EmailSequence('seq_123', {
        name: 'Test Sequence',
        userEmail: '<EMAIL>',
        businessInfo: { name: 'Test Company', industry: 'Software' },
        settings: { sequenceLength: 3, emailFrequency: 'every_2_days' }
      })
    })
    
    it('should add email to sequence', () => {
      sequence.addEmail({
        subject: 'Welcome Email',
        content: 'Welcome to our service...',
        dayInSequence: 1
      })
      
      expect(sequence.emails).to.have.length(1)
      expect(sequence.emails[0].subject).to.equal('Welcome Email')
    })
    
    it('should enforce email limit', () => {
      // Add maximum emails
      for (let i = 0; i < 3; i++) {
        sequence.addEmail({
          subject: `Email ${i + 1}`,
          content: 'Content...',
          dayInSequence: i + 1
        })
      }
      
      // Adding one more should throw error
      expect(() => {
        sequence.addEmail({
          subject: 'Extra Email',
          content: 'Content...',
          dayInSequence: 4
        })
      }).to.throw(DomainValidationError, 'Maximum email limit reached')
    })
    
    it('should not publish empty sequence', () => {
      expect(() => {
        sequence.publish()
      }).to.throw(DomainValidationError, 'Cannot publish empty sequence')
    })
  })
})
```

### Integration Testing

```javascript
// architecture/testing/integration/api/sequences.test.js
import { expect } from 'chai'
import supertest from 'supertest'
import { app } from '../../../backend/server.js'
import { connectTestDatabase, clearTestDatabase } from '../helpers/database.js'
import { createTestUser, generateJwtToken } from '../helpers/auth.js'

describe('Email Sequences API', () => {
  let request
  let testUser
  let authToken
  
  before(async () => {
    await connectTestDatabase()
    request = supertest(app)
  })
  
  beforeEach(async () => {
    await clearTestDatabase()
    testUser = await createTestUser({
      email: '<EMAIL>',
      plan: 'pro'
    })
    authToken = generateJwtToken(testUser)
  })
  
  describe('POST /api/sequences/generate', () => {
    it('should create email sequence successfully', async () => {
      const sequenceData = {
        name: 'Product Launch Sequence',
        businessInfo: {
          name: 'TechCorp Inc',
          industry: 'Software',
          targetAudience: 'B2B SaaS companies'
        },
        settings: {
          sequenceLength: 5,
          emailFrequency: 'every_2_days'
        }
      }
      
      const response = await request
        .post('/api/sequences/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sequenceData)
        .expect(201)
      
      expect(response.body.success).to.be.true
      expect(response.body.data).to.have.property('sequenceId')
      expect(response.body.data.name).to.equal('Product Launch Sequence')
      expect(response.body.data.emails).to.have.length(5)
      expect(response.body.usage.sequencesUsed).to.equal(1)
    })
    
    it('should enforce usage limits', async () => {
      // Update user to be at limit
      await testUser.updateOne({
        'usage.currentPeriod.sequencesGenerated': 75 // Pro plan limit
      })
      
      const response = await request
        .post('/api/sequences/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Sequence',
          businessInfo: { name: 'Test', industry: 'Software' },
          settings: { sequenceLength: 3 }
        })
        .expect(403)
      
      expect(response.body.success).to.be.false
      expect(response.body.error.code).to.equal('USAGE_LIMIT_EXCEEDED')
    })
    
    it('should validate required fields', async () => {
      const response = await request
        .post('/api/sequences/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
        })
        .expect(400)
      
      expect(response.body.success).to.be.false
      expect(response.body.error.code).to.equal('VALIDATION_ERROR')
    })
  })
  
  describe('GET /api/sequences', () => {
    it('should list user sequences with pagination', async () => {
      // Create test sequences
      await createTestSequences(testUser, 5)
      
      const response = await request
        .get('/api/sequences?page=1&limit=3')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
      
      expect(response.body.success).to.be.true
      expect(response.body.data.sequences).to.have.length(3)
      expect(response.body.data.pagination.total).to.equal(5)
      expect(response.body.data.pagination.hasNext).to.be.true
    })
  })
})
```

### E2E Testing

```javascript
// architecture/testing/e2e/sequence-generation.test.js
import { expect } from 'chai'
import { By, until } from 'selenium-webdriver'
import { createDriver, loginUser, navigateToGenerator } from '../helpers/e2e.js'

describe('Email Sequence Generation E2E', () => {
  let driver
  
  before(async () => {
    driver = await createDriver()
  })
  
  after(async () => {
    await driver.quit()
  })
  
  it('should generate complete email sequence', async () => {
    // Login
    await loginUser(driver, '<EMAIL>', 'password')
    
    // Navigate to generator
    await navigateToGenerator(driver)
    
    // Fill out business information
    await driver.findElement(By.id('business-name')).sendKeys('TechCorp Inc')
    await driver.findElement(By.id('industry')).sendKeys('Software')
    await driver.findElement(By.id('target-audience')).sendKeys('B2B SaaS companies')
    
    // Configure sequence settings
    await driver.findElement(By.id('sequence-length')).clear()
    await driver.findElement(By.id('sequence-length')).sendKeys('5')
    
    await driver.findElement(By.css('[data-testid="frequency-every-2-days"]')).click()
    
    // Submit generation
    await driver.findElement(By.css('[data-testid="generate-sequence"]')).click()
    
    // Wait for generation to complete
    await driver.wait(until.elementLocated(By.css('[data-testid="sequence-result"]')), 30000)
    
    // Verify results
    const sequenceName = await driver.findElement(By.css('[data-testid="sequence-name"]')).getText()
    expect(sequenceName).to.include('TechCorp Inc')
    
    const emailCards = await driver.findElements(By.css('[data-testid="email-card"]'))
    expect(emailCards).to.have.length(5)
    
    // Verify usage indicator updated
    const usageIndicator = await driver.findElement(By.css('[data-testid="usage-indicator"]')).getText()
    expect(usageIndicator).to.include('1 of 75')
  })
  
  it('should handle usage limit gracefully', async () => {
    // Setup user at limit via API
    await setupUserAtLimit('<EMAIL>')
    
    await loginUser(driver, '<EMAIL>', 'password')
    await navigateToGenerator(driver)
    
    // Try to generate sequence
    await driver.findElement(By.css('[data-testid="generate-sequence"]')).click()
    
    // Should show limit reached modal
    await driver.wait(until.elementLocated(By.css('[data-testid="limit-reached-modal"]')), 5000)
    
    const modalText = await driver.findElement(By.css('[data-testid="modal-content"]')).getText()
    expect(modalText).to.include('Monthly limit reached')
    
    // Should offer upgrade option
    const upgradeButton = await driver.findElement(By.css('[data-testid="upgrade-button"]'))
    expect(await upgradeButton.isDisplayed()).to.be.true
  })
})
```

### Test Configuration

```javascript
// architecture/testing/test-config.js
export const testConfig = {
  // Database
  testDbUrl: process.env.TEST_MONGODB_URI || 'mongodb://localhost:27018/sequenceai_test',
  
  // Timeouts
  defaultTimeout: 30000,
  e2eTimeout: 60000,
  
  // Coverage thresholds
  coverage: {
    statements: 80,
    branches: 75,
    functions: 80,
    lines: 80
  },
  
  // Test data
  testUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    plan: 'pro'
  },
  
  // Browser settings for E2E
  browser: {
    headless: process.env.CI === 'true',
    viewport: { width: 1280, height: 720 },
    timeout: 30000
  }
}
```

---

## 🔧 API Development

### Request/Response Patterns

```javascript
// Standard API response format
export class ApiResponse {
  static success(data, metadata = {}) {
    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata
      }
    }
  }
  
  static error(error, requestId = null) {
    return {
      success: false,
      error: {
        code: error.code || 'INTERNAL_ERROR',
        message: error.message,
        details: error.details || null,
        requestId,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Usage in controllers
export const createSequence = async (req, res, next) => {
  try {
    const command = new CreateEmailSequenceCommand({
      userEmail: req.user.email,
      ...req.body
    })
    
    const result = await commandBus.send(command)
    
    res.status(201).json(ApiResponse.success(result, {
      requestId: req.requestId,
      usage: await usageService.getUserUsage(req.user.email)
    }))
  } catch (error) {
    next(error)
  }
}
```

### Input Validation

```javascript
// Validation middleware using Joi
import Joi from 'joi'

export const validateCreateSequence = (req, res, next) => {
  const schema = Joi.object({
    name: Joi.string().min(1).max(100).required(),
    businessInfo: Joi.object({
      name: Joi.string().min(1).max(100).required(),
      industry: Joi.string().valid(
        'Software', 'E-commerce', 'Healthcare', 'Finance', 'Education', 'Other'
      ).required(),
      targetAudience: Joi.string().min(1).max(500).required(),
      productService: Joi.string().max(500),
      tone: Joi.string().valid('professional', 'casual', 'friendly', 'authoritative').default('professional'),
      goals: Joi.array().items(Joi.string()).min(1).max(5)
    }).required(),
    settings: Joi.object({
      sequenceLength: Joi.number().integer().min(3).max(10).required(),
      emailFrequency: Joi.string().valid('daily', 'every_2_days', 'every_3_days', 'weekly').required(),
      includeSubjects: Joi.boolean().default(true),
      includePreheaders: Joi.boolean().default(true),
      emailLength: Joi.string().valid('short', 'medium', 'long').default('medium'),
      callToAction: Joi.string().max(100),
      personalizationLevel: Joi.string().valid('low', 'medium', 'high').default('medium')
    }).required()
  })
  
  const { error, value } = schema.validate(req.body)
  
  if (error) {
    return res.status(400).json(ApiResponse.error({
      code: 'VALIDATION_ERROR',
      message: 'Invalid request parameters',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    }, req.requestId))
  }
  
  req.body = value
  next()
}
```

### Error Handling

```javascript
// Global error handler middleware
export const errorHandler = (error, req, res, next) => {
  // Log error
  logger.error('API Error', {
    error: error.message,
    stack: error.stack,
    requestId: req.requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  })
  
  // Determine response based on error type
  let statusCode = 500
  let errorResponse = {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred'
  }
  
  if (error instanceof ValidationError) {
    statusCode = 400
    errorResponse = {
      code: 'VALIDATION_ERROR',
      message: error.message,
      details: error.details
    }
  } else if (error instanceof UnauthorizedError) {
    statusCode = 401
    errorResponse = {
      code: 'AUTHENTICATION_REQUIRED',
      message: 'Authentication required'
    }
  } else if (error instanceof ForbiddenError) {
    statusCode = 403
    errorResponse = {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: error.message
    }
  } else if (error instanceof NotFoundError) {
    statusCode = 404
    errorResponse = {
      code: 'RESOURCE_NOT_FOUND',
      message: error.message
    }
  } else if (error instanceof UsageLimitError) {
    statusCode = 403
    errorResponse = {
      code: 'USAGE_LIMIT_EXCEEDED',
      message: error.message,
      details: error.limitInfo
    }
  } else if (error instanceof RateLimitError) {
    statusCode = 429
    errorResponse = {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'API rate limit exceeded',
      details: {
        retryAfter: error.retryAfter
      }
    }
  }
  
  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    errorResponse.message = 'Internal server error'
    delete errorResponse.details
  }
  
  res.status(statusCode).json(ApiResponse.error(errorResponse, req.requestId))
}
```

### Rate Limiting

```javascript
// Rate limiting middleware
import rateLimit from 'express-rate-limit'
import RedisStore from 'rate-limit-redis'
import Redis from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

export const createRateLimit = (options = {}) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes
    max: options.max || 100, // requests per window
    standardHeaders: true,
    legacyHeaders: false,
    store: new RedisStore({
      client: redis,
      prefix: 'rl:',
    }),
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user ? `user:${req.user.id}` : `ip:${req.ip}`
    },
    handler: (req, res) => {
      res.status(429).json(ApiResponse.error({
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests, please try again later',
        details: {
          retryAfter: Math.round(options.windowMs / 1000)
        }
      }, req.requestId))
    }
  })
}

// Apply rate limits by plan
export const planBasedRateLimit = (req, res, next) => {
  const limits = {
    free: { windowMs: 60 * 60 * 1000, max: 100 },    // 100/hour
    pro: { windowMs: 60 * 60 * 1000, max: 1000 },    // 1000/hour
    business: { windowMs: 60 * 60 * 1000, max: 5000 } // 5000/hour
  }
  
  const userPlan = req.user?.plan || 'free'
  const limit = limits[userPlan]
  
  return createRateLimit(limit)(req, res, next)
}
```

---

## 🗃️ Database Patterns

### MongoDB Schema Design

```javascript
// User Schema with Usage Tracking
import mongoose from 'mongoose'

const UsagePeriodSchema = new mongoose.Schema({
  sequencesGenerated: { type: Number, default: 0 },
  overageSequences: { type: Number, default: 0 },
  overageCharges: { type: Number, default: 0 },
  startDate: { type: Date, default: Date.now },
  endDate: { type: Date },
  createdAt: { type: Date, default: Date.now }
}, { _id: false })

const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },
  passwordHash: { type: String, required: true },
  name: { type: String, required: true },
  plan: {
    type: String,
    enum: ['free', 'pro', 'business'],
    default: 'free',
    index: true
  },
  subscription: {
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    status: {
      type: String,
      enum: ['active', 'canceled', 'past_due', 'incomplete'],
      default: 'active'
    },
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: { type: Boolean, default: false }
  },
  usage: {
    currentPeriod: UsagePeriodSchema,
    notifications: {
      usage80Sent: { type: Boolean, default: false },
      usage95Sent: { type: Boolean, default: false },
      overageConsentGiven: { type: Boolean, default: false }
    },
    history: [UsagePeriodSchema]
  },
  preferences: {
    emailNotifications: { type: Boolean, default: true },
    weeklyReports: { type: Boolean, default: true },
    marketingEmails: { type: Boolean, default: false }
  },
  metadata: {
    lastLoginAt: Date,
    loginCount: { type: Number, default: 0 },
    source: String,
    ipAddress: String,
    userAgent: String
  }
}, {
  timestamps: true,
  collection: 'users'
})

// Indexes for performance
UserSchema.index({ email: 1 })
UserSchema.index({ plan: 1 })
UserSchema.index({ 'subscription.stripeCustomerId': 1 })
UserSchema.index({ 'usage.currentPeriod.startDate': 1 })
UserSchema.index({ createdAt: -1 })

// Virtual for usage percentage
UserSchema.virtual('usage.percentage').get(function() {
  const limits = { free: 5, pro: 75, business: 200 }
  const limit = limits[this.plan] || 5
  return Math.round((this.usage.currentPeriod.sequencesGenerated / limit) * 100)
})

export const User = mongoose.model('User', UserSchema)
```

### Email Sequence Schema

```javascript
// Email Sequence Schema
const EmailSchema = new mongoose.Schema({
  id: { type: String, required: true },
  subject: { type: String, required: true },
  preheader: String,
  content: { type: String, required: true },
  callToAction: {
    text: String,
    url: String
  },
  dayInSequence: { type: Number, required: true },
  estimatedSendTime: String,
  performance: {
    sent: { type: Number, default: 0 },
    opens: { type: Number, default: 0 },
    clicks: { type: Number, default: 0 },
    conversions: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 }
  }
}, { _id: false })

const EmailSequenceSchema = new mongoose.Schema({
  _id: { type: String, required: true },
  name: { type: String, required: true },
  userEmail: { type: String, required: true, index: true },
  businessInfo: {
    name: { type: String, required: true },
    industry: { type: String, required: true },
    targetAudience: String,
    productService: String,
    tone: String,
    goals: [String]
  },
  settings: {
    sequenceLength: { type: Number, required: true },
    emailFrequency: { type: String, required: true },
    includeSubjects: { type: Boolean, default: true },
    includePreheaders: { type: Boolean, default: true },
    emailLength: String,
    callToAction: String,
    personalizationLevel: String
  },
  emails: [EmailSchema],
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
    index: true
  },
  metadata: {
    generatedAt: { type: Date, default: Date.now },
    publishedAt: Date,
    archivedAt: Date,
    estimatedReadTime: String,
    totalWords: Number,
    aiModel: String,
    generationTime: Number
  },
  billing: {
    wasOverage: { type: Boolean, default: false },
    overageCharge: { type: Number, default: 0 },
    billedAt: Date
  },
  version: { type: Number, default: 1 } // For optimistic locking
}, {
  timestamps: true,
  collection: 'emailSequences'
})

// Indexes
EmailSequenceSchema.index({ userEmail: 1, status: 1 })
EmailSequenceSchema.index({ userEmail: 1, createdAt: -1 })
EmailSequenceSchema.index({ 'businessInfo.industry': 1 })
EmailSequenceSchema.index({ status: 1, createdAt: -1 })

// Virtual for overall performance
EmailSequenceSchema.virtual('overallPerformance').get(function() {
  const totals = this.emails.reduce((acc, email) => ({
    sent: acc.sent + email.performance.sent,
    opens: acc.opens + email.performance.opens,
    clicks: acc.clicks + email.performance.clicks,
    conversions: acc.conversions + email.performance.conversions,
    revenue: acc.revenue + email.performance.revenue
  }), { sent: 0, opens: 0, clicks: 0, conversions: 0, revenue: 0 })
  
  return {
    ...totals,
    openRate: totals.sent > 0 ? (totals.opens / totals.sent) * 100 : 0,
    clickRate: totals.sent > 0 ? (totals.clicks / totals.sent) * 100 : 0,
    conversionRate: totals.sent > 0 ? (totals.conversions / totals.sent) * 100 : 0
  }
})

export const EmailSequence = mongoose.model('EmailSequence', EmailSequenceSchema)
```

### Repository Implementation

```javascript
// MongoDB Repository Implementation
export class MongoEmailSequenceRepository extends EmailSequenceRepository {
  constructor() {
    super()
    this.model = EmailSequence
  }
  
  async save(sequence) {
    try {
      const document = this._toDocument(sequence)
      
      // Optimistic locking
      const filter = { _id: sequence.id }
      if (sequence.version > 1) {
        filter.version = sequence.version - 1
      }
      
      const result = await this.model.replaceOne(
        filter,
        { ...document, version: sequence.version },
        { upsert: true }
      )
      
      if (result.matchedCount === 0 && sequence.version > 1) {
        throw new ConcurrencyError('Sequence was modified by another process')
      }
      
      return result
    } catch (error) {
      if (error.code === 11000) {
        throw new DuplicateResourceError('Sequence already exists')
      }
      throw error
    }
  }
  
  async findById(id) {
    const document = await this.model.findById(id).lean()
    return document ? this._toDomain(document) : null
  }
  
  async findByUserEmail(userEmail, filters = {}) {
    const query = { userEmail }
    
    if (filters.status) {
      query.status = filters.status
    }
    
    if (filters.search) {
      query.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { 'businessInfo.name': { $regex: filters.search, $options: 'i' } }
      ]
    }
    
    const sort = {}
    if (filters.sortBy) {
      sort[filters.sortBy] = filters.sortOrder === 'asc' ? 1 : -1
    } else {
      sort.createdAt = -1
    }
    
    const skip = ((filters.page || 1) - 1) * (filters.limit || 20)
    const limit = Math.min(filters.limit || 20, 100)
    
    const [documents, total] = await Promise.all([
      this.model.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      this.model.countDocuments(query)
    ])
    
    return {
      sequences: documents.map(doc => this._toDomain(doc)),
      pagination: {
        page: filters.page || 1,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: skip + limit < total,
        hasPrev: (filters.page || 1) > 1
      }
    }
  }
  
  async getPerformanceStats(userEmail, dateRange = '30d') {
    const startDate = this._getStartDate(dateRange)
    
    const pipeline = [
      {
        $match: {
          userEmail,
          status: 'published',
          'metadata.publishedAt': { $gte: startDate }
        }
      },
      {
        $unwind: '$emails'
      },
      {
        $group: {
          _id: null,
          totalSent: { $sum: '$emails.performance.sent' },
          totalOpens: { $sum: '$emails.performance.opens' },
          totalClicks: { $sum: '$emails.performance.clicks' },
          totalConversions: { $sum: '$emails.performance.conversions' },
          totalRevenue: { $sum: '$emails.performance.revenue' },
          sequenceCount: { $addToSet: '$_id' }
        }
      },
      {
        $project: {
          totalSent: 1,
          totalOpens: 1,
          totalClicks: 1,
          totalConversions: 1,
          totalRevenue: 1,
          sequenceCount: { $size: '$sequenceCount' },
          openRate: {
            $cond: [
              { $gt: ['$totalSent', 0] },
              { $multiply: [{ $divide: ['$totalOpens', '$totalSent'] }, 100] },
              0
            ]
          },
          clickRate: {
            $cond: [
              { $gt: ['$totalSent', 0] },
              { $multiply: [{ $divide: ['$totalClicks', '$totalSent'] }, 100] },
              0
            ]
          },
          conversionRate: {
            $cond: [
              { $gt: ['$totalSent', 0] },
              { $multiply: [{ $divide: ['$totalConversions', '$totalSent'] }, 100] },
              0
            ]
          }
        }
      }
    ]
    
    const [result] = await this.model.aggregate(pipeline)
    
    return result || {
      totalSent: 0,
      totalOpens: 0,
      totalClicks: 0,
      totalConversions: 0,
      totalRevenue: 0,
      sequenceCount: 0,
      openRate: 0,
      clickRate: 0,
      conversionRate: 0
    }
  }
}
```

---

## ⚡ Performance Guidelines

### Caching Strategy

```javascript
// Redis caching service
export class CacheService {
  constructor(redisClient) {
    this.redis = redisClient
    this.defaultTTL = 3600 // 1 hour
  }
  
  async get(key, defaultValue = null) {
    try {
      const value = await this.redis.get(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      logger.error('Cache get error', { key, error: error.message })
      return defaultValue
    }
  }
  
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serialized = JSON.stringify(value)
      await this.redis.setex(key, ttl, serialized)
    } catch (error) {
      logger.error('Cache set error', { key, error: error.message })
    }
  }
  
  async del(key) {
    try {
      await this.redis.del(key)
    } catch (error) {
      logger.error('Cache delete error', { key, error: error.message })
    }
  }
  
  // Cache with fallback pattern
  async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
    let value = await this.get(key)
    
    if (value === null) {
      value = await fetchFunction()
      if (value !== null) {
        await this.set(key, value, ttl)
      }
    }
    
    return value
  }
  
  // Cache invalidation patterns
  generateKey(prefix, ...parts) {
    return [prefix, ...parts].join(':')
  }
  
  async invalidatePattern(pattern) {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length > 0) {
        await this.redis.del(...keys)
      }
    } catch (error) {
      logger.error('Cache pattern invalidation error', { pattern, error: error.message })
    }
  }
}

// Usage in services
export class SequenceService {
  constructor(repository, cacheService) {
    this.repository = repository
    this.cache = cacheService
  }
  
  async getUserSequences(userEmail, filters = {}) {
    const cacheKey = this.cache.generateKey(
      'user:sequences',
      userEmail,
      JSON.stringify(filters)
    )
    
    return await this.cache.getOrSet(
      cacheKey,
      () => this.repository.findByUserEmail(userEmail, filters),
      1800 // 30 minutes
    )
  }
  
  async getSequenceById(id, userEmail) {
    const cacheKey = this.cache.generateKey('sequence', id)
    
    const sequence = await this.cache.getOrSet(
      cacheKey,
      () => this.repository.findById(id),
      3600 // 1 hour
    )
    
    // Authorization check (not cached)
    if (sequence && sequence.userEmail !== userEmail) {
      throw new UnauthorizedError('Access denied')
    }
    
    return sequence
  }
  
  async updateSequence(id, updateData) {
    const sequence = await this.repository.findById(id)
    // ... update logic
    
    const result = await this.repository.save(sequence)
    
    // Invalidate caches
    await this.cache.del(this.cache.generateKey('sequence', id))
    await this.cache.invalidatePattern(`user:sequences:${sequence.userEmail}:*`)
    
    return result
  }
}
```

### Database Optimization

```javascript
// Database connection optimization
const mongoOptions = {
  // Connection pool settings
  maxPoolSize: 10,
  minPoolSize: 2,
  maxIdleTimeMS: 30000,
  
  // Performance settings
  bufferMaxEntries: 0,
  bufferCommands: false,
  
  // Reliability settings
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 10000,
  
  // Compression
  compressors: ['zlib'],
  zlibCompressionLevel: 6
}

// Query optimization techniques
export class OptimizedQueries {
  // Use lean() for read-only operations
  async getSequencesList(userEmail, page = 1, limit = 20) {
    return await EmailSequence
      .find({ userEmail, status: { $ne: 'archived' } })
      .select('_id name status createdAt metadata.generatedAt')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
  }
  
  // Use aggregation for complex queries
  async getUserDashboardStats(userEmail) {
    return await EmailSequence.aggregate([
      { $match: { userEmail, status: 'published' } },
      {
        $group: {
          _id: null,
          totalSequences: { $sum: 1 },
          totalEmails: { $sum: { $size: '$emails' } },
          avgPerformance: {
            $avg: {
              $divide: [
                { $sum: '$emails.performance.opens' },
                { $sum: '$emails.performance.sent' }
              ]
            }
          }
        }
      }
    ])
  }
  
  // Use indexes effectively
  async searchSequences(userEmail, searchTerm, filters = {}) {
    const query = {
      userEmail,
      $text: { $search: searchTerm } // Requires text index
    }
    
    if (filters.industry) {
      query['businessInfo.industry'] = filters.industry
    }
    
    if (filters.dateRange) {
      query.createdAt = {
        $gte: new Date(Date.now() - filters.dateRange * 24 * 60 * 60 * 1000)
      }
    }
    
    return await EmailSequence
      .find(query, { score: { $meta: 'textScore' } })
      .sort({ score: { $meta: 'textScore' } })
      .lean()
  }
}
```

### API Response Optimization

```javascript
// Response compression and optimization
export const optimizeResponse = (req, res, next) => {
  // Enable compression
  if (!req.headers['accept-encoding']) {
    req.headers['accept-encoding'] = 'gzip, deflate'
  }
  
  // Cache headers for static content
  if (req.url.includes('/static/') || req.url.includes('/assets/')) {
    res.set('Cache-Control', 'public, max-age=31536000') // 1 year
  }
  
  // API response caching headers
  if (req.method === 'GET' && req.url.startsWith('/api/')) {
    res.set('Cache-Control', 'private, max-age=300') // 5 minutes
  }
  
  next()
}

// Pagination optimization
export const paginateResults = async (model, query, options = {}) => {
  const page = Math.max(1, parseInt(options.page) || 1)
  const limit = Math.min(100, parseInt(options.limit) || 20)
  const skip = (page - 1) * limit
  
  // Use countDocuments with same query
  const [results, total] = await Promise.all([
    model.find(query)
      .sort(options.sort || { createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    model.countDocuments(query)
  ])
  
  return {
    data: results,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
      hasNext: skip + limit < total,
      hasPrev: page > 1
    }
  }
}

// Response streaming for large datasets
export const streamLargeResponse = (req, res, dataStream) => {
  res.setHeader('Content-Type', 'application/json')
  res.setHeader('Transfer-Encoding', 'chunked')
  
  res.write('{"success":true,"data":[')
  
  let first = true
  dataStream.on('data', (chunk) => {
    if (!first) res.write(',')
    res.write(JSON.stringify(chunk))
    first = false
  })
  
  dataStream.on('end', () => {
    res.write(']}')
    res.end()
  })
  
  dataStream.on('error', (error) => {
    res.write(`],"error":${JSON.stringify({ message: error.message })}}`)
    res.end()
  })
}
```

---

## 🔒 Security Best Practices

### Authentication & Authorization

```javascript
// JWT authentication middleware
export const authenticateJWT = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json(ApiResponse.error({
        code: 'AUTHENTICATION_REQUIRED',
        message: 'Bearer token required'
      }))
    }
    
    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    
    // Verify token hasn't been revoked
    const isRevoked = await redis.get(`revoked:${decoded.jti}`)
    if (isRevoked) {
      return res.status(401).json(ApiResponse.error({
        code: 'TOKEN_REVOKED',
        message: 'Token has been revoked'
      }))
    }
    
    // Load current user data
    const user = await User.findById(decoded.sub)
    if (!user) {
      return res.status(401).json(ApiResponse.error({
        code: 'USER_NOT_FOUND',
        message: 'User no longer exists'
      }))
    }
    
    req.user = user
    req.token = decoded
    next()
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json(ApiResponse.error({
        code: 'TOKEN_EXPIRED',
        message: 'Token has expired'
      }))
    }
    
    return res.status(401).json(ApiResponse.error({
      code: 'INVALID_TOKEN',
      message: 'Invalid token'
    }))
  }
}

// Authorization middleware
export const requirePlan = (requiredPlans) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(ApiResponse.error({
        code: 'AUTHENTICATION_REQUIRED',
        message: 'Authentication required'
      }))
    }
    
    const userPlan = req.user.plan
    const allowed = Array.isArray(requiredPlans) 
      ? requiredPlans.includes(userPlan)
      : requiredPlans === userPlan
    
    if (!allowed) {
      return res.status(403).json(ApiResponse.error({
        code: 'INSUFFICIENT_PERMISSIONS',
        message: `This feature requires a ${requiredPlans} plan`,
        details: {
          currentPlan: userPlan,
          requiredPlan: requiredPlans
        }
      }))
    }
    
    next()
  }
}
```

### Input Sanitization

```javascript
// Input sanitization middleware
import DOMPurify from 'isomorphic-dompurify'
import validator from 'validator'

export const sanitizeInput = (req, res, next) => {
  // Sanitize string inputs
  const sanitizeObject = (obj) => {
    if (typeof obj === 'string') {
      // Remove potential XSS
      return DOMPurify.sanitize(obj, { ALLOWED_TAGS: [] })
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject)
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {}
      for (const [key, value] of Object.entries(obj)) {
        // Sanitize key names
        const cleanKey = key.replace(/[^a-zA-Z0-9_]/g, '')
        sanitized[cleanKey] = sanitizeObject(value)
      }
      return sanitized
    }
    
    return obj
  }
  
  req.body = sanitizeObject(req.body)
  req.query = sanitizeObject(req.query)
  
  next()
}

// Specific input validators
export const validators = {
  email: (value) => {
    if (!validator.isEmail(value)) {
      throw new ValidationError('Invalid email format')
    }
    return validator.normalizeEmail(value)
  },
  
  url: (value) => {
    if (!validator.isURL(value, { protocols: ['http', 'https'] })) {
      throw new ValidationError('Invalid URL format')
    }
    return value
  },
  
  mongoId: (value) => {
    if (!validator.isMongoId(value)) {
      throw new ValidationError('Invalid ID format')
    }
    return value
  },
  
  businessName: (value) => {
    // Remove dangerous characters
    const cleaned = value.replace(/[<>\"\'%;()&+]/g, '')
    if (cleaned.length < 1 || cleaned.length > 100) {
      throw new ValidationError('Business name must be 1-100 characters')
    }
    return cleaned
  }
}
```

### API Security Headers

```javascript
// Security headers middleware
export const securityHeaders = (req, res, next) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY')
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff')
  
  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block')
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // Content Security Policy
  res.setHeader('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://api.stripe.com",
    "frame-ancestors 'none'"
  ].join('; '))
  
  // HSTS for production
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  }
  
  next()
}

// CORS configuration
export const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000']
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}
```

### Password Security

```javascript
// Password hashing and validation
import bcrypt from 'bcrypt'
import zxcvbn from 'zxcvbn'

export class PasswordService {
  static async hash(password) {
    const saltRounds = 12
    return await bcrypt.hash(password, saltRounds)
  }
  
  static async verify(password, hash) {
    return await bcrypt.compare(password, hash)
  }
  
  static validateStrength(password) {
    const result = zxcvbn(password)
    
    if (result.score < 3) {
      throw new ValidationError('Password is too weak', {
        score: result.score,
        feedback: result.feedback.suggestions
      })
    }
    
    // Additional requirements
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters')
    }
    
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      throw new ValidationError('Password must contain uppercase, lowercase, and number')
    }
    
    return true
  }
}

// Account lockout protection
export class AccountSecurityService {
  constructor(redisClient) {
    this.redis = redisClient
    this.maxAttempts = 5
    this.lockoutDuration = 15 * 60 // 15 minutes
  }
  
  async recordFailedAttempt(email) {
    const key = `failed_attempts:${email}`
    const attempts = await this.redis.incr(key)
    
    if (attempts === 1) {
      await this.redis.expire(key, 60 * 60) // Reset after 1 hour
    }
    
    if (attempts >= this.maxAttempts) {
      await this.redis.setex(`locked:${email}`, this.lockoutDuration, '1')
      throw new SecurityError('Account temporarily locked due to failed login attempts')
    }
    
    return attempts
  }
  
  async clearFailedAttempts(email) {
    await this.redis.del(`failed_attempts:${email}`)
  }
  
  async isAccountLocked(email) {
    return await this.redis.exists(`locked:${email}`)
  }
}
```

---

## 🐛 Debugging & Monitoring

### Logging Strategy

```javascript
// Structured logging with Winston
import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
)

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'sequenceai-api',
    version: process.env.APP_VERSION || '1.0.0'
  },
  transports: [
    // Console logging
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    
    // File logging with rotation
    new DailyRotateFile({
      filename: 'logs/app-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info'
    }),
    
    // Error file logging
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error'
    })
  ]
})

// Request logging middleware
export const requestLogger = (req, res, next) => {
  const start = Date.now()
  const requestId = req.requestId || Math.random().toString(36).substr(2, 9)
  
  req.requestId = requestId
  
  logger.info('Request started', {
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id
  })
  
  const originalSend = res.send
  res.send = function(data) {
    const duration = Date.now() - start
    
    logger.info('Request completed', {
      requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      contentLength: data ? data.length : 0
    })
    
    return originalSend.call(this, data)
  }
  
  next()
}
```

### Application Monitoring

```javascript
// Health check endpoint
export const healthCheck = async (req, res) => {
  const checks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    version: process.env.APP_VERSION || '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    services: {}
  }
  
  try {
    // Database health
    const dbStart = Date.now()
    await mongoose.connection.db.admin().ping()
    checks.services.database = {
      status: 'healthy',
      responseTime: Date.now() - dbStart
    }
  } catch (error) {
    checks.services.database = {
      status: 'unhealthy',
      error: error.message
    }
    checks.status = 'degraded'
  }
  
  try {
    // Redis health
    const redisStart = Date.now()
    await redis.ping()
    checks.services.redis = {
      status: 'healthy',
      responseTime: Date.now() - redisStart
    }
  } catch (error) {
    checks.services.redis = {
      status: 'unhealthy',
      error: error.message
    }
    checks.status = 'degraded'
  }
  
  try {
    // External API health (optional)
    const apiStart = Date.now()
    await fetch('https://api.openai.com/v1/models', {
      headers: { 'Authorization': `Bearer ${process.env.OPENAI_API_KEY}` }
    })
    checks.services.openai = {
      status: 'healthy',
      responseTime: Date.now() - apiStart
    }
  } catch (error) {
    checks.services.openai = {
      status: 'unhealthy',
      error: error.message
    }
  }
  
  const httpStatus = checks.status === 'healthy' ? 200 : 503
  res.status(httpStatus).json(checks)
}

// Performance metrics
export class MetricsCollector {
  constructor() {
    this.metrics = new Map()
    this.startTime = Date.now()
  }
  
  increment(name, value = 1, tags = {}) {
    const key = this._createKey(name, tags)
    const current = this.metrics.get(key) || { count: 0, tags }
    current.count += value
    this.metrics.set(key, current)
  }
  
  timing(name, duration, tags = {}) {
    const key = this._createKey(`${name}.timing`, tags)
    const current = this.metrics.get(key) || { 
      count: 0, 
      total: 0, 
      min: Infinity, 
      max: 0,
      tags 
    }
    
    current.count++
    current.total += duration
    current.min = Math.min(current.min, duration)
    current.max = Math.max(current.max, duration)
    current.avg = current.total / current.count
    
    this.metrics.set(key, current)
  }
  
  gauge(name, value, tags = {}) {
    const key = this._createKey(name, tags)
    this.metrics.set(key, { value, tags, timestamp: Date.now() })
  }
  
  getMetrics() {
    const result = {}
    for (const [key, value] of this.metrics) {
      result[key] = value
    }
    return result
  }
  
  _createKey(name, tags) {
    const tagString = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}:${v}`)
      .join(',')
    return tagString ? `${name}{${tagString}}` : name
  }
}

// Metrics middleware
export const metricsMiddleware = (metrics) => {
  return (req, res, next) => {
    const start = Date.now()
    
    metrics.increment('http_requests_total', 1, {
      method: req.method,
      endpoint: req.route?.path || req.path
    })
    
    const originalSend = res.send
    res.send = function(data) {
      const duration = Date.now() - start
      
      metrics.timing('http_request_duration', duration, {
        method: req.method,
        status_code: res.statusCode,
        endpoint: req.route?.path || req.path
      })
      
      metrics.increment('http_responses_total', 1, {
        status_code: res.statusCode,
        method: req.method
      })
      
      return originalSend.call(this, data)
    }
    
    next()
  }
}
```

### Error Tracking

```javascript
// Error tracking service
export class ErrorTrackingService {
  constructor() {
    this.errors = []
    this.maxErrors = 1000
  }
  
  trackError(error, context = {}) {
    const errorInfo = {
      id: this._generateId(),
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      type: error.constructor.name,
      context: {
        ...context,
        userId: context.req?.user?.id,
        requestId: context.req?.requestId,
        userAgent: context.req?.get('User-Agent'),
        ip: context.req?.ip
      }
    }
    
    this.errors.unshift(errorInfo)
    
    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors)
    }
    
    // Log error
    logger.error('Error tracked', errorInfo)
    
    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this._sendToExternalService(errorInfo)
    }
    
    return errorInfo.id
  }
  
  getErrors(filters = {}) {
    let filtered = this.errors
    
    if (filters.since) {
      const since = new Date(filters.since)
      filtered = filtered.filter(err => new Date(err.timestamp) >= since)
    }
    
    if (filters.type) {
      filtered = filtered.filter(err => err.type === filters.type)
    }
    
    if (filters.userId) {
      filtered = filtered.filter(err => err.context.userId === filters.userId)
    }
    
    return filtered.slice(0, filters.limit || 100)
  }
  
  getErrorStats() {
    const now = Date.now()
    const hour = 60 * 60 * 1000
    const day = 24 * hour
    
    const recent = this.errors.filter(err => 
      now - new Date(err.timestamp).getTime() < hour
    )
    
    const today = this.errors.filter(err => 
      now - new Date(err.timestamp).getTime() < day
    )
    
    const byType = {}
    this.errors.forEach(err => {
      byType[err.type] = (byType[err.type] || 0) + 1
    })
    
    return {
      total: this.errors.length,
      lastHour: recent.length,
      last24Hours: today.length,
      byType,
      topErrors: Object.entries(byType)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
    }
  }
  
  _generateId() {
    return Math.random().toString(36).substr(2, 9)
  }
  
  async _sendToExternalService(errorInfo) {
    // Integration with Sentry, Bugsnag, etc.
    try {
      // Example: Send to webhook
      if (process.env.ERROR_WEBHOOK_URL) {
        await fetch(process.env.ERROR_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(errorInfo)
        })
      }
    } catch (err) {
      logger.error('Failed to send error to external service', err)
    }
  }
}
```

---

## 🚀 Deployment Guide

### Production Environment Setup

```dockerfile
# Dockerfile for production
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS build

# Install dev dependencies for build
RUN npm ci

# Copy source code
COPY . .

# Build frontend
RUN cd frontend && npm run build

# Build backend (if needed)
RUN cd backend && npm run build || true

# Production stage
FROM node:18-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=build --chown=nextjs:nodejs /app/backend/dist ./backend
COPY --from=build --chown=nextjs:nodejs /app/frontend/dist ./frontend
COPY --from=build --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nextjs:nodejs /app/package.json ./

# Create logs directory
RUN mkdir -p logs && chown -R nextjs:nodejs logs

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "backend/server.js"]
```

### Docker Compose for Production

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=${MONGODB_URI}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - app_logs:/app/logs
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=sequenceai
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - app_logs:/var/log/app
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongodb_data:
  redis_data:
  app_logs:
```

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sequenceai-app
  labels:
    app: sequenceai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sequenceai
  template:
    metadata:
      labels:
        app: sequenceai
    spec:
      containers:
      - name: app
        image: sequenceai:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: sequenceai-secrets
              key: mongodb-uri
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: sequenceai-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: sequenceai-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: sequenceai-service
spec:
  selector:
    app: sequenceai
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3001
  type: LoadBalancer
```

### Environment Configuration

```bash
# .env.production
NODE_ENV=production
PORT=3001

# Database
MONGODB_URI=*********************************************************************
REDIS_URL=redis://:password@redis:6379

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=7d

# External APIs
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Email
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>

# Stripe
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Monitoring
LOG_LEVEL=info
ERROR_WEBHOOK_URL=https://hooks.slack.com/services/...

# Security
ALLOWED_ORIGINS=https://sequenceai.app,https://www.sequenceai.app
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# Performance
CACHE_TTL=3600
DB_POOL_SIZE=10
```

### Monitoring & Observability

```javascript
// Production monitoring setup
import { createPrometheusMetrics } from './metrics/prometheus.js'
import { setupErrorReporting } from './monitoring/error-reporting.js'
import { configureAPM } from './monitoring/apm.js'

// Initialize monitoring
if (process.env.NODE_ENV === 'production') {
  // APM (Application Performance Monitoring)
  configureAPM({
    serviceName: 'sequenceai-api',
    serverUrl: process.env.APM_SERVER_URL,
    secretToken: process.env.APM_SECRET_TOKEN
  })
  
  // Error reporting (Sentry, etc.)
  setupErrorReporting({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    release: process.env.APP_VERSION
  })
  
  // Prometheus metrics
  const metrics = createPrometheusMetrics()
  app.use('/metrics', metrics.middleware)
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully')
  
  // Stop accepting new connections
  server.close(() => {
    logger.info('HTTP server closed')
  })
  
  // Close database connections
  await mongoose.connection.close()
  await redis.disconnect()
  
  // Exit process
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully')
  process.exit(0)
})

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise })
  process.exit(1)
})
```

---

## 📚 Additional Resources

### Useful Commands

```bash
# Development
npm run dev              # Start development environment
npm run test             # Run all tests
npm run test:watch       # Run tests in watch mode
npm run lint             # Run linting
npm run format           # Format code with Prettier

# Docker
docker-compose up -d     # Start services in background
docker-compose logs -f   # Follow logs
docker-compose down -v   # Stop and remove volumes

# Database
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with test data
npm run db:backup        # Backup database
npm run db:restore       # Restore database

# Production
npm run build            # Build for production
npm run start            # Start production server
npm run deploy           # Deploy to production
```

### Recommended Tools

| Category | Tool | Purpose |
|----------|------|---------|
| Code Editor | VS Code | Development environment |
| API Testing | Postman/Insomnia | API endpoint testing |
| Database | MongoDB Compass | Database management |
| Redis | RedisInsight | Redis monitoring |
| Git | GitHub Desktop | Git GUI |
| Docker | Docker Desktop | Container management |
| Monitoring | Grafana | Metrics visualization |
| Logging | Kibana | Log analysis |

### Learning Resources

- **Domain-Driven Design**: "Domain-Driven Design" by Eric Evans
- **Clean Architecture**: "Clean Architecture" by Robert Martin
- **Node.js Best Practices**: [Node.js Best Practices Repository](https://github.com/goldbergyoni/nodebestpractices)
- **API Design**: REST API Design Best Practices
- **Testing**: "Test-Driven Development" by Kent Beck
- **MongoDB**: MongoDB University Courses
- **React**: Official React Documentation

---

*Developer Guide v1.0 - Last updated: January 15, 2024*