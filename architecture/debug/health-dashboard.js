/**
 * 📊 Real-Time Health Dashboard
 * Visual monitoring interface with live metrics and alerts
 */

import { systemMonitor } from './system-monitor.js'
import { errorFilter } from './error-filter.js'
import { logger } from '../../backend/utils/logger.js'
import { eventBus } from '../modern/event-system.js'

class HealthDashboard {
  constructor() {
    this.dashboardData = {}
    this.updateInterval = null
    this.isActive = false
    this.subscribers = new Set()
  }

  // Initialize dashboard
  async initialize() {
    try {
      // Start system monitor if not already running
      if (!systemMonitor.isRunning) {
        await systemMonitor.initialize()
      }

      // Start error filter if not already running
      if (!errorFilter.initialized) {
        await errorFilter.initialize()
      }

      // Subscribe to system events
      this.subscribeToEvents()

      // Start dashboard updates
      this.startDashboardUpdates()

      this.isActive = true
      logger.info('📊 Health Dashboard initialized')

      return true

    } catch (error) {
      logger.error('Health Dashboard initialization failed:', error)
      return false
    }
  }

  // Subscribe to system events
  subscribeToEvents() {
    eventBus.subscribe('metrics.collected', (metrics) => {
      this.updateDashboardMetrics(metrics)
    })

    eventBus.subscribe('alert.triggered', (alert) => {
      this.addAlert(alert)
    })

    eventBus.subscribe('autohealing.success', (event) => {
      this.addSystemEvent('auto_healing_success', event)
    })

    eventBus.subscribe('autohealing.failed', (event) => {
      this.addSystemEvent('auto_healing_failed', event)
    })

    logger.info('🔔 Dashboard subscribed to system events')
  }

  // Start dashboard data updates
  startDashboardUpdates() {
    const updateDashboard = () => {
      if (!this.isActive) return

      try {
        this.dashboardData = this.generateDashboardData()
        this.notifySubscribers()
      } catch (error) {
        logger.error('Dashboard update failed:', error)
      }
    }

    // Update every 10 seconds
    this.updateInterval = setInterval(updateDashboard, 10000)
    
    // Initial update
    updateDashboard()
  }

  // Generate comprehensive dashboard data
  generateDashboardData() {
    const timestamp = new Date().toISOString()
    
    return {
      timestamp,
      system: this.getSystemOverview(),
      services: this.getServiceStatus(),
      metrics: this.getMetricsSummary(),
      alerts: this.getRecentAlerts(),
      performance: this.getPerformanceMetrics(),
      errorFiltering: this.getErrorFilteringStatus(),
      autoHealing: this.getAutoHealingStatus(),
      recommendations: this.getSystemRecommendations()
    }
  }

  // Get system overview
  getSystemOverview() {
    const dashboardData = systemMonitor.getDashboardData()
    
    return {
      status: dashboardData.systemStatus,
      uptime: dashboardData.uptime,
      uptimeFormatted: this.formatUptime(dashboardData.uptime),
      lastUpdate: dashboardData.timestamp,
      healthScore: this.calculateHealthScore(dashboardData.healthChecks),
      totalAlerts: dashboardData.alerts.length,
      criticalAlerts: dashboardData.alerts.filter(a => a.severity === 'critical').length
    }
  }

  // Get service status
  getServiceStatus() {
    const dashboardData = systemMonitor.getDashboardData()
    const services = {}

    for (const [serviceName, metrics] of Object.entries(dashboardData.healthChecks)) {
      services[serviceName] = {
        status: metrics.status,
        consecutiveFailures: metrics.consecutiveFailures,
        averageResponseTime: Math.round(metrics.averageResponseTime || 0),
        lastCheck: metrics.lastResult ? 'success' : 'error',
        healthIndicator: this.getHealthIndicator(metrics)
      }
    }

    return services
  }

  // Get metrics summary
  getMetricsSummary() {
    const dashboardData = systemMonitor.getDashboardData()
    
    return {
      totalHealthChecks: Object.keys(dashboardData.healthChecks).length,
      healthyServices: Object.values(dashboardData.healthChecks)
        .filter(service => service.status === 'healthy').length,
      averageResponseTime: this.calculateAverageResponseTime(dashboardData.healthChecks),
      systemLoad: this.getSystemLoad(),
      memoryUsage: this.getMemoryUsage()
    }
  }

  // Get recent alerts
  getRecentAlerts() {
    const dashboardData = systemMonitor.getDashboardData()
    
    return dashboardData.alerts.map(alert => ({
      id: alert.id,
      source: alert.source,
      type: alert.type,
      severity: alert.severity,
      timestamp: alert.timestamp,
      timeAgo: this.getTimeAgo(alert.timestamp),
      message: this.formatAlertMessage(alert),
      acknowledged: alert.acknowledged
    }))
  }

  // Get performance metrics
  getPerformanceMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        external: Math.round(memUsage.external / 1024 / 1024) // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        total: cpuUsage.user + cpuUsage.system
      },
      uptime: {
        process: Math.round(process.uptime()),
        system: Math.round(process.uptime()) // Simplified for demo
      },
      nodeVersion: process.version
    }
  }

  // Get error filtering status
  getErrorFilteringStatus() {
    const config = errorFilter.getFilterConfiguration?.() || {}
    const stats = errorFilter.generateStatisticsReport?.() || {}
    
    return {
      enabled: config.initialized || false,
      totalRules: config.totalRules || 0,
      errorsProcessed: stats.totalErrors || 0,
      errorsSuppressed: stats.suppressedErrors || 0,
      suppressionRate: stats.totalErrors > 0 
        ? Math.round((stats.suppressedErrors / stats.totalErrors) * 100) 
        : 0,
      topErrorTypes: stats.topErrors?.slice(0, 5) || []
    }
  }

  // Get auto-healing status
  getAutoHealingStatus() {
    return {
      enabled: true,
      totalActions: systemMonitor.autoHealingActions?.size || 0,
      recentExecutions: [], // Would track recent executions
      successRate: 85, // Simulated for demo
      lastExecution: null // Would track last execution
    }
  }

  // Get system recommendations
  getSystemRecommendations() {
    const recommendations = []
    const dashboardData = systemMonitor.getDashboardData()
    
    // Check for issues and generate recommendations
    if (dashboardData.alerts.length > 5) {
      recommendations.push({
        priority: 'medium',
        title: 'High Alert Volume',
        description: 'Consider reviewing alert thresholds',
        action: 'Review monitoring configuration'
      })
    }

    const unhealthyServices = Object.values(dashboardData.healthChecks)
      .filter(service => service.status !== 'healthy')
    
    if (unhealthyServices.length > 0) {
      recommendations.push({
        priority: 'high',
        title: 'Service Health Issues',
        description: `${unhealthyServices.length} service(s) reporting issues`,
        action: 'Investigate service health'
      })
    }

    const performance = this.getPerformanceMetrics()
    if (performance.memory.percentage > 80) {
      recommendations.push({
        priority: 'medium',
        title: 'High Memory Usage',
        description: `Memory usage at ${performance.memory.percentage}%`,
        action: 'Monitor memory consumption'
      })
    }

    return recommendations
  }

  // Helper methods
  calculateHealthScore(healthChecks) {
    const total = Object.keys(healthChecks).length
    const healthy = Object.values(healthChecks)
      .filter(service => service.status === 'healthy').length
    
    return total > 0 ? Math.round((healthy / total) * 100) : 100
  }

  getHealthIndicator(metrics) {
    if (metrics.status === 'healthy' && metrics.consecutiveFailures === 0) {
      return { status: 'excellent', color: 'green', icon: '✅' }
    } else if (metrics.status === 'healthy' && metrics.consecutiveFailures > 0) {
      return { status: 'recovering', color: 'yellow', icon: '⚠️' }
    } else if (metrics.consecutiveFailures < 3) {
      return { status: 'degraded', color: 'orange', icon: '🔶' }
    } else {
      return { status: 'critical', color: 'red', icon: '❌' }
    }
  }

  calculateAverageResponseTime(healthChecks) {
    const responseTimes = Object.values(healthChecks)
      .map(service => service.averageResponseTime || 0)
      .filter(time => time > 0)
    
    if (responseTimes.length === 0) return 0
    
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
    return Math.round(average)
  }

  getSystemLoad() {
    // Simplified system load calculation
    const memUsage = process.memoryUsage()
    const memPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100
    
    if (memPercentage < 50) return 'low'
    if (memPercentage < 80) return 'medium'
    return 'high'
  }

  getMemoryUsage() {
    const memUsage = process.memoryUsage()
    return Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
  }

  formatUptime(uptimeMs) {
    const seconds = Math.floor(uptimeMs / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  }

  getTimeAgo(timestamp) {
    const now = new Date()
    const alertTime = new Date(timestamp)
    const diffMs = now - alertTime
    
    const diffMinutes = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    if (diffMinutes > 0) return `${diffMinutes}m ago`
    return 'Just now'
  }

  formatAlertMessage(alert) {
    const { source, type, details } = alert
    
    switch (type) {
      case 'health_check_failure':
        return `${source} health check failed (${details.consecutiveFailures} consecutive failures)`
      case 'critical_threshold':
        return `${details.metric} exceeded critical threshold: ${details.value} > ${details.threshold}`
      case 'warning_threshold':
        return `${details.metric} exceeded warning threshold: ${details.value} > ${details.threshold}`
      default:
        return `${type} detected on ${source}`
    }
  }

  // Update methods
  updateDashboardMetrics(metrics) {
    if (this.dashboardData.metrics) {
      this.dashboardData.metrics = { ...this.dashboardData.metrics, ...metrics }
      this.dashboardData.timestamp = new Date().toISOString()
    }
  }

  addAlert(alert) {
    if (!this.dashboardData.alerts) {
      this.dashboardData.alerts = []
    }
    
    this.dashboardData.alerts.unshift({
      ...alert,
      timeAgo: this.getTimeAgo(alert.timestamp)
    })
    
    // Keep only recent alerts
    this.dashboardData.alerts = this.dashboardData.alerts.slice(0, 10)
  }

  addSystemEvent(type, event) {
    if (!this.dashboardData.systemEvents) {
      this.dashboardData.systemEvents = []
    }
    
    this.dashboardData.systemEvents.unshift({
      type,
      timestamp: event.timestamp,
      data: event
    })
    
    // Keep only recent events
    this.dashboardData.systemEvents = this.dashboardData.systemEvents.slice(0, 20)
  }

  // Subscription management
  subscribe(callback) {
    this.subscribers.add(callback)
    
    // Immediately send current data to new subscriber
    if (this.dashboardData.timestamp) {
      callback(this.dashboardData)
    }
    
    return () => this.subscribers.delete(callback)
  }

  notifySubscribers() {
    for (const callback of this.subscribers) {
      try {
        callback(this.dashboardData)
      } catch (error) {
        logger.error('Dashboard subscriber notification failed:', error)
      }
    }
  }

  // API methods for web interface
  getDashboardData() {
    return this.dashboardData
  }

  // Console dashboard for CLI
  displayConsoleDashboard() {
    if (!this.dashboardData.timestamp) {
      console.log('📊 Dashboard not yet initialized...')
      return
    }

    console.clear()
    console.log('📊 SEQUENCEAI SYSTEM HEALTH DASHBOARD')
    console.log('=====================================')
    console.log(`🕐 Last Update: ${this.dashboardData.timestamp}`)
    console.log(`📈 System Status: ${this.dashboardData.system?.status || 'Unknown'}`)
    console.log(`⏱️  Uptime: ${this.dashboardData.system?.uptimeFormatted || 'Unknown'}`)
    console.log(`💯 Health Score: ${this.dashboardData.system?.healthScore || 0}%`)
    console.log('')

    console.log('🔧 SERVICE STATUS:')
    for (const [name, service] of Object.entries(this.dashboardData.services || {})) {
      const indicator = service.healthIndicator
      console.log(`${indicator.icon} ${name}: ${service.status} (${service.averageResponseTime}ms avg)`)
    }
    console.log('')

    console.log('📊 PERFORMANCE:')
    const perf = this.dashboardData.performance || {}
    console.log(`💾 Memory: ${perf.memory?.used || 0}MB / ${perf.memory?.total || 0}MB (${perf.memory?.percentage || 0}%)`)
    console.log(`🏃 Process Uptime: ${perf.uptime?.process || 0}s`)
    console.log(`📦 Node Version: ${perf.nodeVersion || 'Unknown'}`)
    console.log('')

    if (this.dashboardData.alerts?.length > 0) {
      console.log('🚨 RECENT ALERTS:')
      this.dashboardData.alerts.slice(0, 5).forEach(alert => {
        console.log(`${this.getSeverityIcon(alert.severity)} ${alert.source}: ${alert.message} (${alert.timeAgo})`)
      })
      console.log('')
    }

    console.log('🔍 ERROR FILTERING:')
    const errorStats = this.dashboardData.errorFiltering || {}
    console.log(`📈 Processed: ${errorStats.errorsProcessed || 0} errors`)
    console.log(`🔇 Suppressed: ${errorStats.errorsSuppressed || 0} (${errorStats.suppressionRate || 0}%)`)
    console.log('')

    if (this.dashboardData.recommendations?.length > 0) {
      console.log('💡 RECOMMENDATIONS:')
      this.dashboardData.recommendations.forEach(rec => {
        console.log(`${this.getPriorityIcon(rec.priority)} ${rec.title}: ${rec.description}`)
      })
    }

    console.log('\n📊 Dashboard auto-refreshes every 10 seconds')
  }

  getSeverityIcon(severity) {
    switch (severity) {
      case 'critical': return '🔴'
      case 'warning': return '🟡'
      case 'error': return '🟠'
      default: return 'ℹ️'
    }
  }

  getPriorityIcon(priority) {
    switch (priority) {
      case 'high': return '🔴'
      case 'medium': return '🟡'
      case 'low': return '🟢'
      default: return 'ℹ️'
    }
  }

  // Start console dashboard (for CLI mode)
  startConsoleDashboard() {
    this.displayConsoleDashboard()
    
    setInterval(() => {
      this.displayConsoleDashboard()
    }, 10000) // Update every 10 seconds
  }

  // Stop dashboard
  stop() {
    this.isActive = false
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }
    
    logger.info('📊 Health Dashboard stopped')
  }
}

// Create singleton instance
const healthDashboard = new HealthDashboard()

export { HealthDashboard, healthDashboard }