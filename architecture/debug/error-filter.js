/**
 * 🔍 Intelligent Error Filtering System
 * Advanced error classification and noise reduction
 */

import { logger } from '../../backend/utils/logger.js'

class IntelligentErrorFilter {
  constructor() {
    this.filterRules = new Map()
    this.suppressedErrors = new Map()
    this.errorStatistics = new Map()
    this.initialized = false
  }

  // Initialize error filtering system
  async initialize() {
    try {
      this.setupDefaultFilters()
      this.startStatisticsCollection()
      this.initialized = true
      
      logger.info('🔍 Intelligent Error Filter initialized')
      return true
      
    } catch (error) {
      logger.error('Error filter initialization failed:', error)
      return false
    }
  }

  // Setup default filtering rules
  setupDefaultFilters() {
    // MongoDB Container Noise Filter
    this.addFilterRule('mongodb_container_auth', {
      pattern: /Authentication failed.*mongodb/i,
      stackPattern: /\/app\/node_modules\/mongodb/,
      action: 'suppress',
      category: 'infrastructure_noise',
      reason: 'Docker container attempting connection to wrong MongoDB port',
      confidence: 0.95,
      systemImpact: 'none',
      suppressCount: 0,
      maxSuppressCount: 1000 // Log every 1000th occurrence
    })

    // Docker Daemon Connection Filter
    this.addFilterRule('docker_daemon_unavailable', {
      pattern: /Cannot connect to the Docker daemon/i,
      action: 'downgrade',
      newLevel: 'debug',
      category: 'infrastructure_optional',
      reason: 'Docker not required for core functionality - host services available',
      confidence: 1.0,
      systemImpact: 'minimal'
    })

    // OpenAI Demo Mode Filter  
    this.addFilterRule('openai_demo_mode', {
      pattern: /OpenAI.*demo.*mode/i,
      action: 'categorize',
      newLevel: 'info',
      category: 'configuration_notice',
      reason: 'Expected behavior in demo mode - not an error',
      confidence: 1.0,
      systemImpact: 'functional_limitation'
    })

    // High Frequency Error Suppression
    this.addFilterRule('high_frequency_suppression', {
      pattern: /.*/,
      action: 'rate_limit',
      threshold: 10, // Max 10 identical errors per minute
      window: 60000, // 1 minute window
      category: 'rate_limiting',
      reason: 'Preventing log spam from repeated errors'
    })

    logger.info('🛡️ Default error filters configured')
  }

  // Add custom filter rule
  addFilterRule(name, rule) {
    this.filterRules.set(name, {
      ...rule,
      createdAt: new Date().toISOString(),
      timesApplied: 0,
      lastApplied: null
    })
  }

  // Process error through filtering system
  processError(errorLog) {
    if (!this.initialized) {
      return errorLog // Pass through if not initialized
    }

    const originalError = { ...errorLog }
    let processedError = { ...errorLog }
    const appliedFilters = []

    // Apply each filter rule
    for (const [ruleName, rule] of this.filterRules) {
      const filterResult = this.applyFilterRule(processedError, rule, ruleName)
      
      if (filterResult.applied) {
        appliedFilters.push(ruleName)
        processedError = filterResult.modifiedError
        
        // Update rule statistics
        rule.timesApplied++
        rule.lastApplied = new Date().toISOString()
        
        // If error was suppressed, break the chain
        if (filterResult.action === 'suppress') {
          break
        }
      }
    }

    // Update error statistics
    this.updateErrorStatistics(originalError, processedError, appliedFilters)

    return processedError
  }

  // Apply individual filter rule
  applyFilterRule(errorLog, rule, ruleName) {
    const result = {
      applied: false,
      action: null,
      modifiedError: { ...errorLog }
    }

    try {
      // Check if rule pattern matches
      const messageMatch = rule.pattern ? rule.pattern.test(errorLog.message || '') : false
      const stackMatch = rule.stackPattern ? rule.stackPattern.test(errorLog.stack || '') : false
      
      if (!messageMatch && !stackMatch && rule.pattern) {
        return result // No match
      }

      // Apply rate limiting if specified
      if (rule.action === 'rate_limit') {
        if (this.isRateLimited(errorLog, rule)) {
          result.applied = true
          result.action = 'suppress'
          result.modifiedError = null // Suppress the error
          return result
        }
      }

      // Apply the filter action
      switch (rule.action) {
        case 'suppress':
          if (this.shouldSuppressError(errorLog, rule)) {
            result.applied = true
            result.action = 'suppress'
            result.modifiedError = null
            
            // Log suppression notification periodically
            rule.suppressCount = (rule.suppressCount || 0) + 1
            if (rule.suppressCount % (rule.maxSuppressCount || 100) === 0) {
              logger.info(`🔇 Suppressed ${rule.suppressCount} instances of: ${rule.category}`)
            }
          }
          break

        case 'downgrade':
          result.applied = true
          result.action = 'downgrade'
          result.modifiedError = {
            ...errorLog,
            level: rule.newLevel || 'debug',
            originalLevel: errorLog.level,
            filteredBy: ruleName,
            filterReason: rule.reason
          }
          break

        case 'categorize':
          result.applied = true
          result.action = 'categorize'
          result.modifiedError = {
            ...errorLog,
            level: rule.newLevel || errorLog.level,
            errorCategory: rule.category,
            filteredBy: ruleName,
            filterReason: rule.reason,
            systemImpact: rule.systemImpact
          }
          break

        case 'enhance':
          result.applied = true
          result.action = 'enhance'
          result.modifiedError = {
            ...errorLog,
            enhancedMessage: this.enhanceErrorMessage(errorLog, rule),
            troubleshootingSteps: rule.troubleshootingSteps || [],
            relatedDocumentation: rule.relatedDocumentation || [],
            filteredBy: ruleName
          }
          break
      }

    } catch (filterError) {
      logger.error(`Filter rule application failed for ${ruleName}:`, filterError)
    }

    return result
  }

  // Check if error should be suppressed
  shouldSuppressError(errorLog, rule) {
    // Always suppress if explicitly configured
    if (rule.action === 'suppress') {
      // Check for additional conditions
      if (rule.timeWindow) {
        return this.isWithinTimeWindow(errorLog, rule.timeWindow)
      }
      
      if (rule.frequency) {
        return this.exceedsFrequencyThreshold(errorLog, rule.frequency)
      }
      
      return true
    }
    
    return false
  }

  // Rate limiting implementation
  isRateLimited(errorLog, rule) {
    const errorKey = this.getErrorKey(errorLog)
    const now = Date.now()
    const window = rule.window || 60000 // Default 1 minute
    
    if (!this.errorStatistics.has(errorKey)) {
      this.errorStatistics.set(errorKey, {
        count: 0,
        firstSeen: now,
        lastSeen: now,
        windowStart: now
      })
    }
    
    const stats = this.errorStatistics.get(errorKey)
    
    // Reset window if expired
    if (now - stats.windowStart > window) {
      stats.count = 0
      stats.windowStart = now
    }
    
    stats.count++
    stats.lastSeen = now
    
    return stats.count > (rule.threshold || 10)
  }

  // Generate unique key for error
  getErrorKey(errorLog) {
    const message = errorLog.message || ''
    const code = errorLog.code || ''
    return `${message.substring(0, 100)}_${code}`.replace(/\s+/g, '_')
  }

  // Enhance error message with additional context
  enhanceErrorMessage(errorLog, rule) {
    let enhanced = errorLog.message || 'Unknown error'
    
    // Add context based on error type
    if (rule.contextProvider) {
      enhanced += `\n\n🔍 Context: ${rule.contextProvider(errorLog)}`
    }
    
    // Add suggestions
    if (rule.suggestions) {
      enhanced += `\n\n💡 Suggestions:\n${rule.suggestions.map(s => `• ${s}`).join('\n')}`
    }
    
    return enhanced
  }

  // Update error statistics
  updateErrorStatistics(originalError, processedError, appliedFilters) {
    const timestamp = new Date().toISOString()
    const errorKey = this.getErrorKey(originalError)
    
    if (!this.errorStatistics.has(errorKey)) {
      this.errorStatistics.set(errorKey, {
        count: 0,
        firstSeen: timestamp,
        lastSeen: timestamp,
        appliedFilters: new Set(),
        suppressed: 0,
        processed: 0
      })
    }
    
    const stats = this.errorStatistics.get(errorKey)
    stats.count++
    stats.lastSeen = timestamp
    stats.processed++
    
    // Track applied filters
    appliedFilters.forEach(filter => stats.appliedFilters.add(filter))
    
    // Track suppressions
    if (!processedError) {
      stats.suppressed++
    }
  }

  // Start statistics collection
  startStatisticsCollection() {
    setInterval(() => {
      this.generateStatisticsReport()
    }, 300000) // Every 5 minutes
  }

  // Generate statistics report
  generateStatisticsReport() {
    const now = new Date().toISOString()
    const stats = {
      timestamp: now,
      totalErrors: 0,
      suppressedErrors: 0,
      processedErrors: 0,
      filterEfficiency: {},
      topErrors: []
    }

    // Aggregate statistics
    for (const [errorKey, errorStats] of this.errorStatistics) {
      stats.totalErrors += errorStats.count
      stats.suppressedErrors += errorStats.suppressed
      stats.processedErrors += errorStats.processed
      
      stats.topErrors.push({
        errorKey: errorKey.substring(0, 100),
        count: errorStats.count,
        suppressed: errorStats.suppressed,
        appliedFilters: Array.from(errorStats.appliedFilters)
      })
    }

    // Sort top errors by frequency
    stats.topErrors.sort((a, b) => b.count - a.count)
    stats.topErrors = stats.topErrors.slice(0, 10)

    // Calculate filter efficiency
    for (const [ruleName, rule] of this.filterRules) {
      stats.filterEfficiency[ruleName] = {
        timesApplied: rule.timesApplied,
        lastApplied: rule.lastApplied,
        effectiveness: rule.timesApplied > 0 ? (rule.timesApplied / stats.totalErrors) : 0
      }
    }

    // Log summary if significant activity
    if (stats.totalErrors > 0) {
      logger.info(`📊 Error Filter Statistics: ${stats.suppressedErrors}/${stats.totalErrors} errors suppressed (${((stats.suppressedErrors/stats.totalErrors)*100).toFixed(1)}%)`)
    }

    return stats
  }

  // Get filter configuration
  getFilterConfiguration() {
    const config = {
      initialized: this.initialized,
      totalRules: this.filterRules.size,
      rules: {}
    }
    
    for (const [name, rule] of this.filterRules) {
      config.rules[name] = {
        action: rule.action,
        category: rule.category,
        confidence: rule.confidence,
        timesApplied: rule.timesApplied,
        lastApplied: rule.lastApplied
      }
    }
    
    return config
  }

  // Clear statistics
  clearStatistics() {
    this.errorStatistics.clear()
    logger.info('📊 Error filter statistics cleared')
  }

  // Export filtered error for logging
  shouldLogError(processedError) {
    return processedError !== null
  }
}

// Create singleton instance
const errorFilter = new IntelligentErrorFilter()

export { IntelligentErrorFilter, errorFilter }