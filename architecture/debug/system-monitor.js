/**
 * 📊 Advanced System Monitoring & Health Dashboard
 * Real-time monitoring with predictive alerts and auto-healing
 */

import { logger } from '../../backend/utils/logger.js'
import { errorFilter } from './error-filter.js'
import { eventBus } from '../modern/event-system.js'
import redis from 'redis'
import mongoose from 'mongoose'

class AdvancedSystemMonitor {
  constructor() {
    this.metrics = new Map()
    this.healthChecks = new Map()
    this.alerts = new Map()
    this.thresholds = new Map()
    this.autoHealingActions = new Map()
    this.monitoringInterval = null
    this.isRunning = false
    this.startTime = Date.now()
  }

  // Initialize monitoring system
  async initialize() {
    try {
      // Setup default health checks
      this.setupDefaultHealthChecks()
      
      // Setup default thresholds
      this.setupDefaultThresholds()
      
      // Setup auto-healing actions
      this.setupAutoHealingActions()
      
      // Start monitoring
      await this.startMonitoring()
      
      logger.info('📊 Advanced System Monitor initialized')
      return true
      
    } catch (error) {
      logger.error('System monitor initialization failed:', error)
      return false
    }
  }

  // Setup default health checks
  setupDefaultHealthChecks() {
    // MongoDB Health Check
    this.addHealthCheck('mongodb', {
      name: 'MongoDB Connection',
      check: this.checkMongoDB,
      interval: 30000, // 30 seconds
      timeout: 5000,
      retries: 3,
      critical: true,
      expectedResponse: 'connected'
    })

    // Redis Health Check
    this.addHealthCheck('redis', {
      name: 'Redis Connection',
      check: this.checkRedis,
      interval: 30000,
      timeout: 5000,
      retries: 3,
      critical: true,
      expectedResponse: 'PONG'
    })

    // API Endpoint Health Check
    this.addHealthCheck('api', {
      name: 'Backend API',
      check: this.checkAPI,
      interval: 15000,
      timeout: 3000,
      retries: 2,
      critical: true,
      expectedStatus: 200
    })

    // System Resources Health Check
    this.addHealthCheck('system_resources', {
      name: 'System Resources',
      check: this.checkSystemResources,
      interval: 60000, // 1 minute
      timeout: 2000,
      retries: 1,
      critical: false
    })

    // Error Rate Health Check
    this.addHealthCheck('error_rate', {
      name: 'Error Rate Monitor',
      check: this.checkErrorRate,
      interval: 60000,
      timeout: 1000,
      retries: 1,
      critical: false
    })

    logger.info('🏥 Default health checks configured')
  }

  // Setup monitoring thresholds
  setupDefaultThresholds() {
    this.thresholds.set('error_rate', {
      warning: 0.05,  // 5% error rate
      critical: 0.15, // 15% error rate
      window: 300000  // 5 minute window
    })

    this.thresholds.set('response_time', {
      warning: 2000,  // 2 seconds
      critical: 5000, // 5 seconds
      window: 60000   // 1 minute window
    })

    this.thresholds.set('memory_usage', {
      warning: 0.8,   // 80% memory usage
      critical: 0.95, // 95% memory usage
      window: 120000  // 2 minute window
    })

    this.thresholds.set('cpu_usage', {
      warning: 0.8,   // 80% CPU usage
      critical: 0.95, // 95% CPU usage
      window: 180000  // 3 minute window
    })

    this.thresholds.set('disk_usage', {
      warning: 0.85,  // 85% disk usage
      critical: 0.95, // 95% disk usage
      window: 600000  // 10 minute window
    })

    logger.info('⚠️ Monitoring thresholds configured')
  }

  // Setup auto-healing actions
  setupAutoHealingActions() {
    this.autoHealingActions.set('mongodb_connection_failure', {
      condition: (metrics) => metrics.mongodb?.status === 'unhealthy',
      action: this.healMongoDBConnection,
      cooldown: 300000, // 5 minutes
      maxAttempts: 3,
      description: 'Attempt to reconnect to MongoDB'
    })

    this.autoHealingActions.set('redis_connection_failure', {
      condition: (metrics) => metrics.redis?.status === 'unhealthy',
      action: this.healRedisConnection,
      cooldown: 180000, // 3 minutes
      maxAttempts: 3,
      description: 'Attempt to reconnect to Redis'
    })

    this.autoHealingActions.set('high_error_rate', {
      condition: (metrics) => metrics.error_rate?.current > 0.1,
      action: this.healHighErrorRate,
      cooldown: 600000, // 10 minutes
      maxAttempts: 2,
      description: 'Investigate and mitigate high error rate'
    })

    this.autoHealingActions.set('memory_pressure', {
      condition: (metrics) => metrics.memory_usage?.current > 0.9,
      action: this.healMemoryPressure,
      cooldown: 900000, // 15 minutes
      maxAttempts: 1,
      description: 'Attempt to reduce memory usage'
    })

    logger.info('🔧 Auto-healing actions configured')
  }

  // Add custom health check
  addHealthCheck(name, config) {
    this.healthChecks.set(name, {
      ...config,
      lastRun: null,
      lastResult: null,
      consecutiveFailures: 0,
      totalRuns: 0,
      totalFailures: 0,
      averageResponseTime: 0,
      isRunning: false
    })
  }

  // Start monitoring system
  async startMonitoring() {
    if (this.isRunning) {
      logger.warn('Monitoring already running')
      return
    }

    this.isRunning = true
    
    // Start health check loops
    for (const [name, healthCheck] of this.healthChecks) {
      this.startHealthCheckLoop(name, healthCheck)
    }

    // Start metrics collection
    this.startMetricsCollection()

    // Start auto-healing engine
    this.startAutoHealingEngine()

    logger.info('📊 System monitoring started')
    
    // Emit monitoring started event
    eventBus.publish('monitoring.started', {
      timestamp: new Date().toISOString(),
      healthChecks: this.healthChecks.size,
      autoHealingActions: this.autoHealingActions.size
    })
  }

  // Start individual health check loop
  startHealthCheckLoop(name, healthCheck) {
    const runHealthCheck = async () => {
      if (!this.isRunning || healthCheck.isRunning) return

      try {
        healthCheck.isRunning = true
        healthCheck.totalRuns++
        
        const startTime = Date.now()
        const result = await Promise.race([
          healthCheck.check.call(this),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), healthCheck.timeout)
          )
        ])
        
        const responseTime = Date.now() - startTime
        
        // Update metrics
        healthCheck.lastRun = new Date().toISOString()
        healthCheck.lastResult = result
        healthCheck.averageResponseTime = 
          (healthCheck.averageResponseTime * (healthCheck.totalRuns - 1) + responseTime) / healthCheck.totalRuns
        
        // Check if result meets expectations
        const isHealthy = this.evaluateHealthCheckResult(result, healthCheck)
        
        if (isHealthy) {
          healthCheck.consecutiveFailures = 0
          this.recordHealthMetric(name, 'healthy', responseTime, result)
        } else {
          healthCheck.consecutiveFailures++
          healthCheck.totalFailures++
          this.recordHealthMetric(name, 'unhealthy', responseTime, result)
          
          // Trigger alerts if critical and multiple failures
          if (healthCheck.critical && healthCheck.consecutiveFailures >= (healthCheck.retries || 1)) {
            this.triggerAlert(name, 'health_check_failure', {
              consecutiveFailures: healthCheck.consecutiveFailures,
              lastResult: result,
              responseTime
            })
          }
        }

      } catch (error) {
        healthCheck.consecutiveFailures++
        healthCheck.totalFailures++
        
        this.recordHealthMetric(name, 'error', null, { error: error.message })
        
        if (healthCheck.critical) {
          this.triggerAlert(name, 'health_check_error', {
            error: error.message,
            consecutiveFailures: healthCheck.consecutiveFailures
          })
        }
        
        logger.error(`Health check failed for ${name}:`, error)
        
      } finally {
        healthCheck.isRunning = false
      }
    }

    // Run immediately, then set interval
    runHealthCheck()
    const interval = setInterval(runHealthCheck, healthCheck.interval)
    
    // Store interval for cleanup
    healthCheck.intervalId = interval
  }

  // Record health metric
  recordHealthMetric(checkName, status, responseTime, result) {
    const timestamp = Date.now()
    
    if (!this.metrics.has(checkName)) {
      this.metrics.set(checkName, [])
    }
    
    const metrics = this.metrics.get(checkName)
    metrics.push({
      timestamp,
      status,
      responseTime,
      result,
      date: new Date().toISOString()
    })
    
    // Keep only recent metrics (last 1000 entries)
    if (metrics.length > 1000) {
      metrics.splice(0, metrics.length - 1000)
    }
  }

  // Evaluate health check result
  evaluateHealthCheckResult(result, healthCheck) {
    if (healthCheck.expectedResponse) {
      return result === healthCheck.expectedResponse
    }
    
    if (healthCheck.expectedStatus && result.status) {
      return result.status === healthCheck.expectedStatus
    }
    
    // Default: any non-error result is healthy
    return result && !result.error
  }

  // Health check implementations
  async checkMongoDB() {
    try {
      if (mongoose.connection.readyState === 1) {
        // Connection is open, perform a simple operation
        await mongoose.connection.db.admin().ping()
        return 'connected'
      } else {
        return { error: 'Not connected', readyState: mongoose.connection.readyState }
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  async checkRedis() {
    try {
      const client = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6380'
      })
      
      await client.connect()
      const response = await client.ping()
      await client.disconnect()
      
      return response
    } catch (error) {
      return { error: error.message }
    }
  }

  async checkAPI() {
    try {
      const response = await fetch('http://localhost:5000/health', {
        timeout: 3000
      })
      
      return {
        status: response.status,
        statusText: response.statusText,
        healthy: response.ok
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  async checkSystemResources() {
    try {
      const uptime = process.uptime()
      const memUsage = process.memoryUsage()
      const cpuUsage = process.cpuUsage()
      
      return {
        uptime,
        memory: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
          usage: memUsage.heapUsed / memUsage.heapTotal
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        nodeVersion: process.version
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  async checkErrorRate() {
    try {
      // Get error statistics from error filter
      const errorStats = errorFilter.generateStatisticsReport?.() || {
        totalErrors: 0,
        processedErrors: 0
      }
      
      const errorRate = errorStats.processedErrors > 0 
        ? errorStats.totalErrors / errorStats.processedErrors 
        : 0
      
      return {
        errorRate,
        totalErrors: errorStats.totalErrors,
        processedErrors: errorStats.processedErrors,
        threshold: this.thresholds.get('error_rate')?.warning || 0.05
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  // Start metrics collection
  startMetricsCollection() {
    const collectMetrics = () => {
      const timestamp = Date.now()
      const aggregatedMetrics = {}
      
      // Aggregate health check metrics
      for (const [name, healthCheck] of this.healthChecks) {
        const metrics = this.metrics.get(name) || []
        const recentMetrics = metrics.filter(m => timestamp - m.timestamp < 300000) // Last 5 minutes
        
        aggregatedMetrics[name] = {
          status: healthCheck.lastResult && !healthCheck.lastResult.error ? 'healthy' : 'unhealthy',
          consecutiveFailures: healthCheck.consecutiveFailures,
          averageResponseTime: healthCheck.averageResponseTime,
          successRate: healthCheck.totalRuns > 0 ? 
            ((healthCheck.totalRuns - healthCheck.totalFailures) / healthCheck.totalRuns) : 1,
          recentMetrics: recentMetrics.length
        }
      }
      
      // System-wide metrics
      aggregatedMetrics.system = {
        uptime: Date.now() - this.startTime,
        healthChecks: this.healthChecks.size,
        alerts: this.alerts.size,
        timestamp: new Date().toISOString()
      }
      
      // Check thresholds and trigger auto-healing
      this.checkThresholds(aggregatedMetrics)
      
      // Emit metrics event
      eventBus.publish('metrics.collected', aggregatedMetrics)
      
    }
    
    // Collect metrics every minute
    setInterval(collectMetrics, 60000)
  }

  // Check thresholds and trigger alerts
  checkThresholds(metrics) {
    for (const [thresholdName, threshold] of this.thresholds) {
      const metric = this.getMetricValue(metrics, thresholdName)
      
      if (metric !== null) {
        if (metric >= threshold.critical) {
          this.triggerAlert(thresholdName, 'critical_threshold', {
            value: metric,
            threshold: threshold.critical,
            metric: thresholdName
          })
        } else if (metric >= threshold.warning) {
          this.triggerAlert(thresholdName, 'warning_threshold', {
            value: metric,
            threshold: threshold.warning,
            metric: thresholdName
          })
        }
      }
    }
  }

  // Get metric value for threshold checking
  getMetricValue(metrics, thresholdName) {
    switch (thresholdName) {
      case 'error_rate':
        return metrics.error_rate?.errorRate || 0
      case 'response_time':
        return Math.max(
          metrics.mongodb?.averageResponseTime || 0,
          metrics.redis?.averageResponseTime || 0,
          metrics.api?.averageResponseTime || 0
        )
      default:
        return null
    }
  }

  // Trigger alert
  triggerAlert(source, type, details) {
    const alertId = `${source}_${type}_${Date.now()}`
    const alert = {
      id: alertId,
      source,
      type,
      details,
      timestamp: new Date().toISOString(),
      severity: this.getAlertSeverity(type),
      acknowledged: false
    }
    
    this.alerts.set(alertId, alert)
    
    logger.warn(`🚨 Alert triggered: ${type} for ${source}`, details)
    
    // Emit alert event
    eventBus.publish('alert.triggered', alert)
    
    // Clean up old alerts
    this.cleanupOldAlerts()
  }

  // Get alert severity
  getAlertSeverity(type) {
    if (type.includes('critical')) return 'critical'
    if (type.includes('warning')) return 'warning'
    if (type.includes('error')) return 'error'
    return 'info'
  }

  // Start auto-healing engine
  startAutoHealingEngine() {
    const runAutoHealing = () => {
      if (!this.isRunning) return
      
      const currentMetrics = this.getCurrentMetrics()
      
      for (const [actionName, action] of this.autoHealingActions) {
        try {
          if (action.condition(currentMetrics)) {
            this.executeAutoHealingAction(actionName, action, currentMetrics)
          }
        } catch (error) {
          logger.error(`Auto-healing condition check failed for ${actionName}:`, error)
        }
      }
    }
    
    // Run auto-healing checks every 2 minutes
    setInterval(runAutoHealing, 120000)
  }

  // Execute auto-healing action
  async executeAutoHealingAction(actionName, action, metrics) {
    const now = Date.now()
    const lastExecution = action.lastExecution || 0
    const attempts = action.attempts || 0
    
    // Check cooldown and max attempts
    if (now - lastExecution < action.cooldown || attempts >= action.maxAttempts) {
      return
    }
    
    try {
      logger.info(`🔧 Executing auto-healing action: ${actionName}`)
      
      action.lastExecution = now
      action.attempts = attempts + 1
      
      const result = await action.action.call(this, metrics)
      
      if (result && result.success) {
        logger.info(`✅ Auto-healing successful: ${actionName}`)
        action.attempts = 0 // Reset attempts on success
        
        eventBus.publish('autohealing.success', {
          action: actionName,
          result,
          timestamp: new Date().toISOString()
        })
      } else {
        logger.warn(`⚠️ Auto-healing partially successful: ${actionName}`, result)
      }
      
    } catch (error) {
      logger.error(`❌ Auto-healing failed for ${actionName}:`, error)
      
      eventBus.publish('autohealing.failed', {
        action: actionName,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    }
  }

  // Auto-healing action implementations
  async healMongoDBConnection() {
    try {
      // Attempt to reconnect to MongoDB
      if (mongoose.connection.readyState !== 1) {
        await mongoose.connect(process.env.MONGODB_URI)
        return { success: true, action: 'reconnected' }
      }
      return { success: true, action: 'already_connected' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  async healRedisConnection() {
    try {
      // Test Redis connection
      const client = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6380'
      })
      
      await client.connect()
      await client.ping()
      await client.disconnect()
      
      return { success: true, action: 'connection_verified' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  async healHighErrorRate() {
    try {
      // Analyze recent errors and apply additional filtering
      const errorStats = errorFilter.generateStatisticsReport?.()
      
      if (errorStats && errorStats.topErrors) {
        logger.info('🔍 Analyzing high error rate patterns for additional filtering')
        
        // Could implement adaptive filtering here
        return { success: true, action: 'error_analysis_completed' }
      }
      
      return { success: false, error: 'No error statistics available' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  async healMemoryPressure() {
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
        return { success: true, action: 'garbage_collection_forced' }
      }
      
      return { success: false, error: 'Garbage collection not available' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // Get current aggregated metrics
  getCurrentMetrics() {
    const metrics = {}
    
    for (const [name, healthCheck] of this.healthChecks) {
      metrics[name] = {
        status: healthCheck.lastResult && !healthCheck.lastResult.error ? 'healthy' : 'unhealthy',
        consecutiveFailures: healthCheck.consecutiveFailures,
        averageResponseTime: healthCheck.averageResponseTime,
        lastResult: healthCheck.lastResult
      }
    }
    
    return metrics
  }

  // Get system dashboard data
  getDashboardData() {
    const currentMetrics = this.getCurrentMetrics()
    const recentAlerts = Array.from(this.alerts.values())
      .filter(alert => Date.now() - new Date(alert.timestamp).getTime() < 3600000) // Last hour
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    
    return {
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      healthChecks: currentMetrics,
      alerts: recentAlerts.slice(0, 10), // Latest 10 alerts
      systemStatus: this.calculateOverallStatus(currentMetrics),
      autoHealing: {
        enabled: true,
        actions: this.autoHealingActions.size,
        recentActions: [] // Could track recent auto-healing actions
      }
    }
  }

  // Calculate overall system status
  calculateOverallStatus(metrics) {
    const criticalServices = ['mongodb', 'redis', 'api']
    const criticalHealthy = criticalServices.filter(service => 
      metrics[service] && metrics[service].status === 'healthy'
    ).length
    
    const healthPercentage = (criticalHealthy / criticalServices.length) * 100
    
    if (healthPercentage === 100) return 'healthy'
    if (healthPercentage >= 66) return 'degraded'
    return 'unhealthy'
  }

  // Cleanup old alerts
  cleanupOldAlerts() {
    const cutoff = Date.now() - 86400000 // 24 hours
    
    for (const [alertId, alert] of this.alerts) {
      if (new Date(alert.timestamp).getTime() < cutoff) {
        this.alerts.delete(alertId)
      }
    }
  }

  // Stop monitoring
  async stop() {
    this.isRunning = false
    
    // Clear all intervals
    for (const [name, healthCheck] of this.healthChecks) {
      if (healthCheck.intervalId) {
        clearInterval(healthCheck.intervalId)
      }
    }
    
    logger.info('📊 System monitoring stopped')
  }
}

// Create singleton instance
const systemMonitor = new AdvancedSystemMonitor()

export { AdvancedSystemMonitor, systemMonitor }