/**
 * 🛠️ Development Tools & Debugging Infrastructure
 * Advanced debugging, profiling, and development utilities
 */

import { logger } from '../backend/utils/logger.js'
import { performance } from 'perf_hooks'
import { createHash } from 'crypto'

// ========================================
// DEBUGGING UTILITIES
// ========================================

/**
 * Advanced Debugger with Context Tracking
 */
export class AdvancedDebugger {
  constructor() {
    this.contexts = new Map()
    this.breakpoints = new Set()
    this.watchExpressions = new Map()
    this.callStack = []
    this.enabled = process.env.NODE_ENV === 'development'
  }

  // Context management
  createContext(id, metadata = {}) {
    const context = {
      id,
      metadata,
      createdAt: new Date(),
      variables: new Map(),
      history: [],
      performance: {
        startTime: performance.now(),
        checkpoints: []
      }
    }
    
    this.contexts.set(id, context)
    
    if (this.enabled) {
      logger.debug(`🐛 Debug context created: ${id}`, metadata)
    }
    
    return context
  }

  // Variable watching
  watch(contextId, variable, value) {
    const context = this.contexts.get(contextId)
    if (!context) return

    const previous = context.variables.get(variable)
    context.variables.set(variable, value)
    
    // Track changes
    if (previous !== undefined && previous !== value) {
      context.history.push({
        timestamp: new Date(),
        variable,
        previousValue: previous,
        newValue: value,
        stackTrace: new Error().stack
      })

      if (this.enabled) {
        logger.debug(`👁️ Variable changed in ${contextId}: ${variable}`, {
          previous,
          current: value
        })
      }
    }

    // Check watch expressions
    this.checkWatchExpressions(contextId, variable, value)
  }

  // Performance checkpoints
  checkpoint(contextId, name) {
    const context = this.contexts.get(contextId)
    if (!context) return

    const checkpoint = {
      name,
      timestamp: performance.now(),
      duration: performance.now() - context.performance.startTime,
      memoryUsage: process.memoryUsage()
    }

    context.performance.checkpoints.push(checkpoint)

    if (this.enabled) {
      logger.debug(`⏱️ Performance checkpoint in ${contextId}: ${name}`, {
        duration: `${checkpoint.duration.toFixed(2)}ms`,
        memory: `${Math.round(checkpoint.memoryUsage.heapUsed / 1024 / 1024)}MB`
      })
    }

    return checkpoint
  }

  // Conditional breakpoints
  breakpoint(contextId, condition, message = '') {
    if (!this.enabled) return

    const shouldBreak = typeof condition === 'function' ? condition() : condition
    
    if (shouldBreak) {
      const context = this.contexts.get(contextId)
      const breakpointInfo = {
        contextId,
        message,
        timestamp: new Date(),
        variables: context ? Object.fromEntries(context.variables) : {},
        stackTrace: new Error().stack
      }

      logger.warn(`🔴 Breakpoint hit in ${contextId}: ${message}`, breakpointInfo)
      
      // In development, you could integrate with debugger
      if (global.debugger) {
        debugger
      }
    }
  }

  // Function call tracing
  trace(contextId, functionName, args = [], returnValue = null) {
    if (!this.enabled) return

    const traceInfo = {
      function: functionName,
      args: this.sanitizeArgs(args),
      returnValue: this.sanitizeValue(returnValue),
      timestamp: new Date(),
      duration: null
    }

    this.callStack.push(traceInfo)

    logger.debug(`📞 Function called in ${contextId}: ${functionName}`, traceInfo)

    // Return a function to mark completion
    return (actualReturnValue) => {
      const completed = this.callStack.pop()
      if (completed) {
        completed.returnValue = this.sanitizeValue(actualReturnValue)
        completed.duration = performance.now() - completed.timestamp
        
        logger.debug(`✅ Function completed in ${contextId}: ${functionName}`, {
          duration: `${completed.duration.toFixed(2)}ms`,
          returnValue: completed.returnValue
        })
      }
    }
  }

  // Watch expressions
  addWatchExpression(name, expression) {
    this.watchExpressions.set(name, expression)
  }

  checkWatchExpressions(contextId, variable, value) {
    for (const [name, expression] of this.watchExpressions) {
      try {
        const result = expression(variable, value)
        if (result) {
          logger.warn(`👁️ Watch expression triggered: ${name}`, {
            contextId,
            variable,
            value,
            expression: name
          })
        }
      } catch (error) {
        logger.error(`❌ Watch expression error: ${name}`, error)
      }
    }
  }

  // Memory leak detection
  detectMemoryLeaks(contextId) {
    const context = this.contexts.get(contextId)
    if (!context) return

    const memUsage = process.memoryUsage()
    const heapUsed = memUsage.heapUsed / 1024 / 1024 // MB

    // Check for memory growth
    const checkpoints = context.performance.checkpoints
    if (checkpoints.length > 5) {
      const recentCheckpoints = checkpoints.slice(-5)
      const memoryGrowth = recentCheckpoints.map(cp => cp.memoryUsage.heapUsed / 1024 / 1024)
      const avgGrowth = memoryGrowth.reduce((sum, mem, i) => {
        if (i === 0) return 0
        return sum + (mem - memoryGrowth[i - 1])
      }, 0) / (memoryGrowth.length - 1)

      if (avgGrowth > 10) { // Growing by more than 10MB per checkpoint
        logger.warn(`🚨 Potential memory leak detected in ${contextId}`, {
          averageGrowth: `${avgGrowth.toFixed(2)}MB`,
          currentUsage: `${heapUsed.toFixed(2)}MB`
        })
      }
    }
  }

  // Context cleanup
  cleanup(contextId) {
    const context = this.contexts.get(contextId)
    if (!context) return

    const summary = {
      duration: performance.now() - context.performance.startTime,
      checkpoints: context.performance.checkpoints.length,
      variableChanges: context.history.length,
      finalMemory: process.memoryUsage().heapUsed / 1024 / 1024
    }

    this.contexts.delete(contextId)

    if (this.enabled) {
      logger.debug(`🧹 Debug context cleaned up: ${contextId}`, summary)
    }

    return summary
  }

  // Utility methods
  sanitizeArgs(args) {
    return args.map(arg => this.sanitizeValue(arg))
  }

  sanitizeValue(value) {
    if (value === null || value === undefined) return value
    if (typeof value === 'function') return '[Function]'
    if (value instanceof Error) return { name: value.name, message: value.message }
    if (typeof value === 'object') {
      try {
        return JSON.parse(JSON.stringify(value))
      } catch {
        return '[Complex Object]'
      }
    }
    return value
  }

  // Get debug report
  getReport(contextId) {
    const context = this.contexts.get(contextId)
    if (!context) return null

    return {
      id: contextId,
      metadata: context.metadata,
      uptime: performance.now() - context.performance.startTime,
      variables: Object.fromEntries(context.variables),
      history: context.history,
      checkpoints: context.performance.checkpoints,
      memoryUsage: process.memoryUsage()
    }
  }
}

// ========================================
// PERFORMANCE PROFILER
// ========================================

/**
 * Advanced Performance Profiler
 */
export class PerformanceProfiler {
  constructor() {
    this.profiles = new Map()
    this.enabled = process.env.NODE_ENV === 'development' || process.env.ENABLE_PROFILING === 'true'
  }

  // Start profiling session
  startProfile(name, metadata = {}) {
    if (!this.enabled) return

    const profile = {
      name,
      metadata,
      startTime: performance.now(),
      startMemory: process.memoryUsage(),
      operations: [],
      milestones: [],
      endTime: null,
      endMemory: null
    }

    this.profiles.set(name, profile)
    
    logger.info(`📊 Performance profiling started: ${name}`, metadata)
    
    return profile
  }

  // Record operation
  recordOperation(profileName, operationName, duration, metadata = {}) {
    const profile = this.profiles.get(profileName)
    if (!profile) return

    const operation = {
      name: operationName,
      duration,
      timestamp: performance.now(),
      metadata,
      memoryUsage: process.memoryUsage()
    }

    profile.operations.push(operation)

    if (this.enabled) {
      logger.debug(`⚡ Operation recorded: ${operationName}`, {
        duration: `${duration.toFixed(2)}ms`,
        profile: profileName
      })
    }
  }

  // Add milestone
  milestone(profileName, name, description = '') {
    const profile = this.profiles.get(profileName)
    if (!profile) return

    const milestone = {
      name,
      description,
      timestamp: performance.now(),
      relativeTime: performance.now() - profile.startTime,
      memoryUsage: process.memoryUsage()
    }

    profile.milestones.push(milestone)

    if (this.enabled) {
      logger.debug(`🎯 Milestone reached: ${name}`, {
        relativeTime: `${milestone.relativeTime.toFixed(2)}ms`,
        profile: profileName
      })
    }
  }

  // End profiling session
  endProfile(profileName) {
    const profile = this.profiles.get(profileName)
    if (!profile) return null

    profile.endTime = performance.now()
    profile.endMemory = process.memoryUsage()

    const totalDuration = profile.endTime - profile.startTime
    const memoryDelta = profile.endMemory.heapUsed - profile.startMemory.heapUsed

    const summary = {
      name: profileName,
      totalDuration: totalDuration,
      memoryDelta: memoryDelta,
      operationsCount: profile.operations.length,
      averageOperationTime: profile.operations.length > 0 
        ? profile.operations.reduce((sum, op) => sum + op.duration, 0) / profile.operations.length 
        : 0,
      slowestOperation: profile.operations.length > 0 
        ? profile.operations.reduce((max, op) => op.duration > max.duration ? op : max)
        : null,
      milestones: profile.milestones.length
    }

    if (this.enabled) {
      logger.info(`📊 Performance profiling completed: ${profileName}`, {
        duration: `${totalDuration.toFixed(2)}ms`,
        memoryDelta: `${Math.round(memoryDelta / 1024)}KB`,
        operations: summary.operationsCount
      })
    }

    return summary
  }

  // Get detailed profile analysis
  analyzeProfile(profileName) {
    const profile = this.profiles.get(profileName)
    if (!profile) return null

    const analysis = {
      overview: {
        name: profileName,
        duration: profile.endTime ? profile.endTime - profile.startTime : performance.now() - profile.startTime,
        memoryUsage: profile.endMemory || process.memoryUsage(),
        operationsCount: profile.operations.length
      },
      performance: {
        averageOperationTime: this.calculateAverage(profile.operations.map(op => op.duration)),
        medianOperationTime: this.calculateMedian(profile.operations.map(op => op.duration)),
        slowestOperations: profile.operations
          .sort((a, b) => b.duration - a.duration)
          .slice(0, 5),
        operationsByType: this.groupOperationsByType(profile.operations)
      },
      memory: {
        peakUsage: this.findPeakMemoryUsage(profile.operations),
        memoryGrowth: this.calculateMemoryGrowth(profile.operations),
        gcEvents: this.detectGCEvents(profile.operations)
      },
      timeline: {
        milestones: profile.milestones,
        operationDistribution: this.createTimelineDistribution(profile.operations, profile.startTime)
      }
    }

    return analysis
  }

  // Statistical calculations
  calculateAverage(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0
  }

  calculateMedian(values) {
    if (values.length === 0) return 0
    const sorted = [...values].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid]
  }

  groupOperationsByType(operations) {
    const groups = {}
    operations.forEach(op => {
      const type = op.name.split(':')[0] || 'other'
      if (!groups[type]) {
        groups[type] = { count: 0, totalDuration: 0, operations: [] }
      }
      groups[type].count++
      groups[type].totalDuration += op.duration
      groups[type].operations.push(op)
    })
    return groups
  }

  findPeakMemoryUsage(operations) {
    return operations.reduce((peak, op) => {
      return op.memoryUsage.heapUsed > peak ? op.memoryUsage.heapUsed : peak
    }, 0)
  }

  calculateMemoryGrowth(operations) {
    if (operations.length < 2) return 0
    const first = operations[0].memoryUsage.heapUsed
    const last = operations[operations.length - 1].memoryUsage.heapUsed
    return last - first
  }

  detectGCEvents(operations) {
    const gcEvents = []
    for (let i = 1; i < operations.length; i++) {
      const prev = operations[i - 1].memoryUsage.heapUsed
      const curr = operations[i].memoryUsage.heapUsed
      if (prev - curr > 10 * 1024 * 1024) { // 10MB drop indicates GC
        gcEvents.push({
          timestamp: operations[i].timestamp,
          memoryFreed: prev - curr,
          operation: operations[i].name
        })
      }
    }
    return gcEvents
  }

  createTimelineDistribution(operations, startTime) {
    const buckets = 10
    const totalDuration = performance.now() - startTime
    const bucketSize = totalDuration / buckets
    const distribution = new Array(buckets).fill(0)

    operations.forEach(op => {
      const bucketIndex = Math.min(
        Math.floor((op.timestamp - startTime) / bucketSize),
        buckets - 1
      )
      distribution[bucketIndex]++
    })

    return distribution
  }

  // Export profile data
  exportProfile(profileName, format = 'json') {
    const profile = this.profiles.get(profileName)
    if (!profile) return null

    const analysis = this.analyzeProfile(profileName)

    switch (format) {
      case 'json':
        return JSON.stringify(analysis, null, 2)
      case 'csv':
        return this.convertToCSV(profile.operations)
      case 'flamegraph':
        return this.generateFlameGraphData(profile.operations)
      default:
        return analysis
    }
  }

  convertToCSV(operations) {
    const headers = 'Name,Duration,Timestamp,MemoryUsed\n'
    const rows = operations.map(op => 
      `${op.name},${op.duration},${op.timestamp},${op.memoryUsage.heapUsed}`
    ).join('\n')
    return headers + rows
  }

  generateFlameGraphData(operations) {
    // Simplified flame graph data structure
    const stacks = operations.map(op => ({
      name: op.name,
      value: op.duration,
      children: []
    }))
    return { name: 'root', children: stacks }
  }
}

// ========================================
// DEVELOPMENT SERVER UTILITIES
// ========================================

/**
 * Development Server Enhancement
 */
export class DevServerUtils {
  constructor() {
    this.watchers = new Map()
    this.reloadTriggers = new Set()
    this.hotReloadEnabled = process.env.HOT_RELOAD !== 'false'
  }

  // File watching with smart reloading
  watchFiles(patterns, callback) {
    const chokidar = require('chokidar')
    
    const watcher = chokidar.watch(patterns, {
      ignored: /node_modules|\.git|coverage|dist/,
      persistent: true,
      ignoreInitial: true
    })

    const watcherId = createHash('md5').update(patterns.join(',')).digest('hex')
    
    watcher.on('change', (path) => {
      logger.info(`📝 File changed: ${path}`)
      
      if (this.shouldTriggerReload(path)) {
        callback('change', path)
      }
    })

    watcher.on('add', (path) => {
      logger.info(`➕ File added: ${path}`)
      callback('add', path)
    })

    watcher.on('unlink', (path) => {
      logger.info(`➖ File removed: ${path}`)
      callback('remove', path)
    })

    this.watchers.set(watcherId, watcher)
    return watcherId
  }

  shouldTriggerReload(path) {
    // Intelligent reload logic
    const extensions = ['.js', '.mjs', '.ts', '.json', '.env']
    const isRelevant = extensions.some(ext => path.endsWith(ext))
    
    if (!isRelevant) return false

    // Debounce rapid changes
    const now = Date.now()
    const lastTrigger = this.reloadTriggers.has(path) 
      ? this.reloadTriggers.get(path) 
      : 0
    
    if (now - lastTrigger < 1000) return false // 1 second debounce
    
    this.reloadTriggers.set(path, now)
    return true
  }

  // API endpoint introspection
  analyzeAPIEndpoints(app) {
    const endpoints = []
    
    // Extract routes from Express app
    app._router.stack.forEach(layer => {
      if (layer.route) {
        const route = layer.route
        Object.keys(route.methods).forEach(method => {
          endpoints.push({
            method: method.toUpperCase(),
            path: route.path,
            handlers: route.stack.length,
            middleware: layer.regexp.source
          })
        })
      }
    })

    logger.info(`🔍 API Endpoints discovered: ${endpoints.length}`)
    return endpoints
  }

  // Request/Response logging middleware
  createRequestLogger() {
    return (req, res, next) => {
      const start = performance.now()
      const requestId = createHash('md5')
        .update(`${Date.now()}-${Math.random()}`)
        .digest('hex')
        .substring(0, 8)

      req.requestId = requestId

      logger.info(`📨 ${req.method} ${req.path}`, {
        requestId,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        query: req.query,
        body: req.method === 'POST' ? this.sanitizeRequestBody(req.body) : undefined
      })

      const originalSend = res.send
      res.send = function(data) {
        const duration = performance.now() - start
        
        logger.info(`📤 ${req.method} ${req.path} - ${res.statusCode}`, {
          requestId,
          duration: `${duration.toFixed(2)}ms`,
          responseSize: data ? data.length : 0
        })

        return originalSend.call(this, data)
      }

      next()
    }
  }

  sanitizeRequestBody(body) {
    if (!body) return body
    
    const sensitive = ['password', 'token', 'secret', 'key']
    const sanitized = { ...body }
    
    Object.keys(sanitized).forEach(key => {
      if (sensitive.some(s => key.toLowerCase().includes(s))) {
        sanitized[key] = '[REDACTED]'
      }
    })
    
    return sanitized
  }

  // Database query logging
  createQueryLogger() {
    return {
      beforeQuery: (query) => {
        const queryId = createHash('md5').update(query).digest('hex').substring(0, 8)
        logger.debug(`💾 Query started: ${queryId}`, { query })
        return { queryId, startTime: performance.now() }
      },
      
      afterQuery: (context, result) => {
        const duration = performance.now() - context.startTime
        logger.debug(`💾 Query completed: ${context.queryId}`, {
          duration: `${duration.toFixed(2)}ms`,
          resultCount: Array.isArray(result) ? result.length : 1
        })
      },
      
      onError: (context, error) => {
        const duration = performance.now() - context.startTime
        logger.error(`💾 Query failed: ${context.queryId}`, {
          duration: `${duration.toFixed(2)}ms`,
          error: error.message
        })
      }
    }
  }

  // Environment health checker
  checkEnvironment() {
    const checks = {
      nodeVersion: process.version,
      memoryUsage: process.memoryUsage(),
      platform: process.platform,
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      pid: process.pid
    }

    // Check dependencies
    try {
      const packageJson = require('../../package.json')
      checks.appVersion = packageJson.version
      checks.dependencies = Object.keys(packageJson.dependencies).length
    } catch (error) {
      checks.appVersion = 'unknown'
    }

    // Check database connections
    checks.database = {
      mongodb: this.checkMongoConnection(),
      redis: this.checkRedisConnection()
    }

    logger.info('🏥 Environment health check', checks)
    return checks
  }

  async checkMongoConnection() {
    try {
      const mongoose = require('mongoose')
      if (mongoose.connection.readyState === 1) {
        return { status: 'connected', state: 'ready' }
      } else {
        return { status: 'disconnected', state: mongoose.connection.readyState }
      }
    } catch (error) {
      return { status: 'error', message: error.message }
    }
  }

  async checkRedisConnection() {
    try {
      // This would require your Redis client instance
      return { status: 'connected', state: 'ready' }
    } catch (error) {
      return { status: 'error', message: error.message }
    }
  }

  // Cleanup
  cleanup() {
    this.watchers.forEach((watcher, id) => {
      watcher.close()
      logger.info(`🧹 File watcher closed: ${id}`)
    })
    this.watchers.clear()
    this.reloadTriggers.clear()
  }
}

// ========================================
// EXPORTS & INITIALIZATION
// ========================================

export const debugger = new AdvancedDebugger()
export const profiler = new PerformanceProfiler()
export const devUtils = new DevServerUtils()

// Global debug helpers for development
if (process.env.NODE_ENV === 'development') {
  global.debug = debugger
  global.profile = profiler
  global.devUtils = devUtils
}

logger.info('🛠️ Development tools initialized')