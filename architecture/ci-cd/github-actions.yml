# GitHub Actions CI/CD Pipeline for NeuroColony
name: 🚀 NeuroColony CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: sequenceai

jobs:
  # ========================================
  # CODE QUALITY & SECURITY CHECKS
  # ========================================
  quality-checks:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 📦 Install dependencies
      run: |
        npm ci
        cd backend && npm ci
        cd ../frontend && npm ci
        cd ../architecture/testing && npm ci
        
    - name: 🔎 Lint code
      run: |
        npm run lint
        cd backend && npm run lint
        cd ../frontend && npm run lint
        
    - name: 🎨 Check code formatting
      run: |
        npm run format:check
        cd backend && npm run format:check
        cd ../frontend && npm run format:check
        
    - name: 🔒 Security audit
      run: |
        npm audit --audit-level=moderate
        cd backend && npm audit --audit-level=moderate
        cd ../frontend && npm audit --audit-level=moderate
        
    - name: 📋 Type checking
      run: |
        cd backend && npm run typecheck
        cd ../frontend && npm run typecheck
        
    - name: 📊 Upload lint results
      uses: github/super-linter@v4
      if: always()
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_JAVASCRIPT_ES: true
        VALIDATE_CSS: true
        VALIDATE_HTML: true

  # ========================================
  # UNIT TESTS
  # ========================================
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    
    strategy:
      matrix:
        node-version: ['18', '20']
        
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: 📦 Install dependencies
      run: |
        npm ci
        cd architecture/testing && npm ci
        
    - name: 🧪 Run unit tests
      run: |
        cd architecture/testing
        npm run test:unit
        
    - name: 📊 Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./architecture/testing/coverage/lcov.info
        flags: unit-tests
        name: unit-tests-${{ matrix.node-version }}

  # ========================================
  # INTEGRATION TESTS
  # ========================================
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      mongodb:
        image: mongo:7
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongosh
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 📦 Install dependencies
      run: |
        npm ci
        cd architecture/testing && npm ci
        
    - name: ⚙️ Setup test environment
      run: |
        cp .env.example .env.test
        echo "MONGODB_URI=mongodb://localhost:27017/sequenceai_test" >> .env.test
        echo "REDIS_URL=redis://localhost:6379" >> .env.test
        
    - name: 🔗 Run integration tests
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/sequenceai_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-jwt-secret
      run: |
        cd architecture/testing
        npm run test:integration
        
    - name: 📊 Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: architecture/testing/test-reports/

  # ========================================
  # E2E TESTS
  # ========================================
  e2e-tests:
    name: 🌐 E2E Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 🐳 Build and run test environment
      run: |
        cd architecture/testing
        docker-compose -f docker-compose.test.yml up --build -d
        
    - name: ⏳ Wait for services
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:3001/health; do sleep 2; done'
        
    - name: 🌐 Run E2E tests
      run: |
        cd architecture/testing
        npm run test:e2e
        
    - name: 📊 Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: architecture/testing/test-reports/
        
    - name: 🧹 Cleanup test environment
      if: always()
      run: |
        cd architecture/testing
        docker-compose -f docker-compose.test.yml down -v

  # ========================================
  # PERFORMANCE TESTS
  # ========================================
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: e2e-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 🐳 Start performance test environment
      run: |
        cd architecture/testing
        docker-compose -f docker-compose.test.yml up --build -d app-test mongodb-test redis-test
        
    - name: ⚡ Run performance tests
      env:
        PERF_TESTS: true
      run: |
        cd architecture/testing
        npm run test:performance
        
    - name: 📈 Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results
        path: architecture/testing/test-reports/performance-summary.json

  # ========================================
  # SECURITY SCANNING
  # ========================================
  security-scan:
    name: 🔒 Security Scanning
    runs-on: ubuntu-latest
    needs: quality-checks
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔍 Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: 📋 Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: 🔐 Run CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: javascript
        
    - name: 🔍 Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # ========================================
  # BUILD & PACKAGE
  # ========================================
  build:
    name: 🏗️ Build & Package
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🏗️ Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 🔑 Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 📋 Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: 🏗️ Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # ========================================
  # DEPLOYMENT TO STAGING
  # ========================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/develop' || github.event_name == 'pull_request'
    environment: staging
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: ⚙️ Setup kubectl
      uses: azure/setup-kubectl@v3
      
    - name: 🔑 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        
    - name: 🚀 Deploy to staging
      env:
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        # Update Kubernetes deployment
        kubectl set image deployment/sequenceai-staging \
          app=${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        kubectl rollout status deployment/sequenceai-staging
        
    - name: 🧪 Run smoke tests
      run: |
        cd architecture/testing
        npm run test:smoke
        
    - name: 💬 Post deployment status
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 Deployed to staging successfully! \n\n' +
                  'Staging URL: https://staging.sequenceai.app \n' +
                  'Image: ${{ needs.build.outputs.image-tag }}'
          })

  # ========================================
  # DEPLOYMENT TO PRODUCTION
  # ========================================
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, performance-tests, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: ⚙️ Setup kubectl
      uses: azure/setup-kubectl@v3
      
    - name: 🔑 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        
    - name: 🌟 Deploy to production
      env:
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        # Blue-green deployment
        kubectl patch deployment sequenceai-production \
          -p '{"spec":{"template":{"spec":{"containers":[{"name":"app","image":"${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:${{ github.sha }}"}]}}}}'
        kubectl rollout status deployment/sequenceai-production
        
    - name: 🧪 Run production smoke tests
      run: |
        cd architecture/testing
        PRODUCTION_URL=https://sequenceai.app npm run test:smoke
        
    - name: 📊 Update deployment metrics
      run: |
        curl -X POST "${{ secrets.MONITORING_WEBHOOK }}" \
          -H "Content-Type: application/json" \
          -d '{
            "deployment": "production",
            "version": "${{ github.sha }}",
            "status": "success",
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
          }'

  # ========================================
  # CLEANUP
  # ========================================
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: 🗑️ Clean up old images
      run: |
        # Clean up Docker images older than 7 days
        docker image prune -a --filter "until=168h" -f || true
        
    - name: 📋 Generate deployment report
      run: |
        echo "## Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "- **Status**: ✅ Success" >> $GITHUB_STEP_SUMMARY