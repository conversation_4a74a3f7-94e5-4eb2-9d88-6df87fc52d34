#!/bin/bash

# NeuroColony Production Deployment Script
# Usage: ./deploy.sh [platform] [domain]
# Platforms: digitalocean, railway, docker, vercel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PLATFORM=${1:-"digitalocean"}
DOMAIN=${2:-""}
PROJECT_NAME="neurocolony"

echo -e "${BLUE}🚀 NeuroColony Deployment Script${NC}"
echo -e "${BLUE}Platform: ${PLATFORM}${NC}"
echo -e "${BLUE}Domain: ${DOMAIN}${NC}"
echo ""

# Validate prerequisites
validate_prerequisites() {
    echo -e "${YELLOW}📋 Validating prerequisites...${NC}"
    
    # Check if .env.production exists
    if [ ! -f ".env.production" ]; then
        echo -e "${RED}❌ .env.production not found${NC}"
        echo -e "${YELLOW}💡 Creating from template...${NC}"
        cp .env.production.template .env.production
        echo -e "${RED}⚠️  Please edit .env.production with your actual values${NC}"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js not found${NC}"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm not found${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Prerequisites validated${NC}"
}

# Install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    
    # Backend dependencies
    cd backend
    npm install --production
    cd ..
    
    # Frontend dependencies
    cd frontend
    npm install
    cd ..
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Build frontend
build_frontend() {
    echo -e "${YELLOW}🏗️  Building frontend...${NC}"
    
    cd frontend
    
    # Set environment variables for build
    if [ -n "$DOMAIN" ]; then
        export VITE_API_URL="https://${DOMAIN}/api"
    fi
    
    # Build
    npm run build
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Frontend built successfully${NC}"
    else
        echo -e "${RED}❌ Frontend build failed${NC}"
        exit 1
    fi
    
    cd ..
}

# Test backend
test_backend() {
    echo -e "${YELLOW}🧪 Testing backend...${NC}"
    
    cd backend
    
    # Run tests if they exist
    if [ -f "package.json" ] && grep -q "test" package.json; then
        npm test
    else
        echo -e "${YELLOW}⚠️  No tests found, skipping...${NC}"
    fi
    
    cd ..
}

# Deploy to DigitalOcean App Platform
deploy_digitalocean() {
    echo -e "${YELLOW}🌊 Deploying to DigitalOcean App Platform...${NC}"
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        echo -e "${RED}❌ doctl CLI not found${NC}"
        echo -e "${YELLOW}💡 Install with: snap install doctl${NC}"
        exit 1
    fi
    
    # Update domain in app.yaml
    if [ -n "$DOMAIN" ]; then
        sed -i "s/yourdomain.com/${DOMAIN}/g" .do/app.yaml
    fi
    
    # Deploy
    echo -e "${YELLOW}🚀 Creating DigitalOcean app...${NC}"
    doctl apps create --spec .do/app.yaml
    
    echo -e "${GREEN}✅ DigitalOcean deployment initiated${NC}"
    echo -e "${YELLOW}💡 Check status with: doctl apps list${NC}"
}

# Deploy to Railway
deploy_railway() {
    echo -e "${YELLOW}🚂 Deploying to Railway...${NC}"
    
    # Check if railway CLI is installed
    if ! command -v railway &> /dev/null; then
        echo -e "${RED}❌ Railway CLI not found${NC}"
        echo -e "${YELLOW}💡 Install with: npm install -g @railway/cli${NC}"
        exit 1
    fi
    
    # Login check
    if ! railway status &> /dev/null; then
        echo -e "${YELLOW}🔑 Please login to Railway first${NC}"
        railway login
    fi
    
    # Deploy
    echo -e "${YELLOW}🚀 Deploying to Railway...${NC}"
    railway up
    
    echo -e "${GREEN}✅ Railway deployment initiated${NC}"
}

# Deploy with Docker
deploy_docker() {
    echo -e "${YELLOW}🐳 Deploying with Docker...${NC}"
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker not found${NC}"
        exit 1
    fi
    
    # Check if docker-compose is installed
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose not found${NC}"
        exit 1
    fi
    
    # Stop existing containers
    echo -e "${YELLOW}🛑 Stopping existing containers...${NC}"
    docker-compose -f docker-compose.prod.yml down
    
    # Build and start
    echo -e "${YELLOW}🏗️  Building and starting containers...${NC}"
    docker-compose -f docker-compose.prod.yml up -d --build
    
    echo -e "${GREEN}✅ Docker deployment complete${NC}"
    echo -e "${YELLOW}💡 Check status with: docker-compose -f docker-compose.prod.yml ps${NC}"
}

# Deploy to Vercel (frontend only)
deploy_vercel() {
    echo -e "${YELLOW}⚡ Deploying frontend to Vercel...${NC}"
    
    # Check if vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        echo -e "${RED}❌ Vercel CLI not found${NC}"
        echo -e "${YELLOW}💡 Install with: npm install -g vercel${NC}"
        exit 1
    fi
    
    cd frontend
    
    # Deploy
    echo -e "${YELLOW}🚀 Deploying to Vercel...${NC}"
    vercel --prod
    
    cd ..
    
    echo -e "${GREEN}✅ Vercel deployment complete${NC}"
    echo -e "${YELLOW}⚠️  Note: Backend needs separate deployment${NC}"
}

# Post-deployment checks
post_deployment_checks() {
    echo -e "${YELLOW}🔍 Running post-deployment checks...${NC}"
    
    if [ -n "$DOMAIN" ]; then
        echo -e "${YELLOW}🌐 Checking domain accessibility...${NC}"
        
        # Wait a bit for deployment
        sleep 10
        
        # Check if domain is accessible
        if curl -s -o /dev/null -w "%{http_code}" "https://${DOMAIN}" | grep -q "200\|301\|302"; then
            echo -e "${GREEN}✅ Domain is accessible${NC}"
        else
            echo -e "${YELLOW}⚠️  Domain not yet accessible (may take a few minutes)${NC}"
        fi
    fi
}

# Main deployment flow
main() {
    echo -e "${BLUE}🎯 Starting deployment process...${NC}"
    
    validate_prerequisites
    install_dependencies
    build_frontend
    test_backend
    
    case $PLATFORM in
        "digitalocean")
            deploy_digitalocean
            ;;
        "railway")
            deploy_railway
            ;;
        "docker")
            deploy_docker
            ;;
        "vercel")
            deploy_vercel
            ;;
        *)
            echo -e "${RED}❌ Unknown platform: $PLATFORM${NC}"
            echo -e "${YELLOW}💡 Supported platforms: digitalocean, railway, docker, vercel${NC}"
            exit 1
            ;;
    esac
    
    post_deployment_checks
    
    echo ""
    echo -e "${GREEN}🎉 Deployment complete!${NC}"
    echo -e "${BLUE}📋 Next steps:${NC}"
    echo -e "${YELLOW}  1. Configure your domain DNS settings${NC}"
    echo -e "${YELLOW}  2. Set up SSL certificates${NC}"
    echo -e "${YELLOW}  3. Configure production API keys${NC}"
    echo -e "${YELLOW}  4. Test all functionality${NC}"
    echo -e "${YELLOW}  5. Monitor deployment logs${NC}"
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo -e "${BLUE}NeuroColony Deployment Script${NC}"
    echo ""
    echo -e "${YELLOW}Usage:${NC}"
    echo "  ./deploy.sh [platform] [domain]"
    echo ""
    echo -e "${YELLOW}Platforms:${NC}"
    echo "  digitalocean  - DigitalOcean App Platform (recommended)"
    echo "  railway       - Railway.app"
    echo "  docker        - Local Docker deployment"
    echo "  vercel        - Vercel (frontend only)"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  ./deploy.sh digitalocean neurocolony.com"
    echo "  ./deploy.sh railway"
    echo "  ./deploy.sh docker"
    echo ""
    exit 0
fi

# Run main function
main