#!/bin/bash

# NeuroColony Connection Fix Script
# This script diagnoses and fixes connection issues

echo "🧠 NeuroColony Connection Fix Script"
echo "===================================="
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to test URL
test_url() {
    local url=$1
    local name=$2
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|201"; then
        echo -e "${GREEN}✅ $name is accessible at $url${NC}"
        return 0
    else
        echo -e "${RED}❌ $name is not accessible at $url${NC}"
        return 1
    fi
}

echo -e "${BLUE}1️⃣  Checking current services...${NC}"
echo ""

# Check if services are running
BACKEND_5000=false
BACKEND_5002=false
FRONTEND_3000=false
FRONTEND_3004=false

if lsof -i :5000 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Something is running on port 5000${NC}"
    if test_url "http://localhost:5000/api/test" "Backend API (5000)"; then
        BACKEND_5000=true
    fi
else
    echo -e "${YELLOW}⚠️  Nothing on port 5000${NC}"
fi

if lsof -i :5002 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Something is running on port 5002${NC}"
    if test_url "http://localhost:5002/api/test" "Backend API (5002)"; then
        BACKEND_5002=true
    fi
else
    echo -e "${YELLOW}⚠️  Nothing on port 5002${NC}"
fi

if lsof -i :3000 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Something is running on port 3000${NC}"
    FRONTEND_3000=true
else
    echo -e "${YELLOW}⚠️  Nothing on port 3000${NC}"
fi

if lsof -i :3004 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Something is running on port 3004${NC}"
    FRONTEND_3004=true
else
    echo -e "${YELLOW}⚠️  Nothing on port 3004${NC}"
fi

echo ""
echo -e "${BLUE}2️⃣  Checking MongoDB...${NC}"
echo ""

# Test MongoDB connection
if pgrep -x mongod > /dev/null; then
    echo -e "${GREEN}✅ MongoDB process is running${NC}"
    
    # Test actual connection
    if mongosh --eval "db.adminCommand('ping')" --quiet 2>/dev/null | grep -q "1"; then
        echo -e "${GREEN}✅ MongoDB is accepting connections${NC}"
    else
        echo -e "${RED}❌ MongoDB is running but not accepting connections${NC}"
        echo -e "${YELLOW}💡 Try: sudo systemctl restart mongod${NC}"
    fi
else
    echo -e "${RED}❌ MongoDB is not running${NC}"
    echo -e "${YELLOW}💡 Starting MongoDB...${NC}"
    
    # Try to start MongoDB
    if command -v mongod >/dev/null 2>&1; then
        sudo systemctl start mongod 2>/dev/null || \
        mongod --fork --logpath /tmp/mongodb.log 2>/dev/null || \
        echo -e "${RED}❌ Failed to start MongoDB automatically${NC}"
    fi
fi

echo ""
echo -e "${BLUE}3️⃣  Checking configuration...${NC}"
echo ""

# Check backend .env
if [ -f "backend/.env" ]; then
    echo -e "${GREEN}✅ Backend .env file exists${NC}"
    
    # Check PORT configuration
    PORT_CONFIG=$(grep "^PORT=" backend/.env | cut -d'=' -f2)
    echo -e "   📍 Configured PORT: ${YELLOW}${PORT_CONFIG}${NC}"
    
    # Check MongoDB URI
    MONGO_URI=$(grep "^MONGODB_URI=" backend/.env | cut -d'=' -f2)
    if [[ $MONGO_URI == *"localhost"* ]]; then
        echo -e "   ${YELLOW}⚠️  MongoDB URI uses 'localhost' - may cause issues${NC}"
        echo -e "   ${BLUE}💡 Consider changing to 127.0.0.1${NC}"
    fi
else
    echo -e "${RED}❌ Backend .env file missing${NC}"
    echo -e "${YELLOW}💡 Creating from example...${NC}"
    cp backend/.env.example backend/.env
fi

echo ""
echo -e "${BLUE}4️⃣  Diagnosis Summary:${NC}"
echo ""

if $BACKEND_5000 || $BACKEND_5002; then
    echo -e "${GREEN}✅ Backend is running${NC}"
    if $BACKEND_5000; then
        echo -e "   📍 Backend URL: http://localhost:5000"
    fi
    if $BACKEND_5002; then
        echo -e "   📍 Backend URL: http://localhost:5002"
    fi
else
    echo -e "${RED}❌ Backend is not running${NC}"
fi

if $FRONTEND_3000 || $FRONTEND_3004; then
    echo -e "${GREEN}✅ Frontend is running${NC}"
    if $FRONTEND_3000; then
        echo -e "   📍 Frontend URL: http://localhost:3000"
    fi
    if $FRONTEND_3004; then
        echo -e "   📍 Frontend URL: http://localhost:3004"
    fi
else
    echo -e "${RED}❌ Frontend is not running${NC}"
fi

echo ""
echo -e "${BLUE}5️⃣  Recommended Actions:${NC}"
echo ""

if ! $BACKEND_5000 && ! $BACKEND_5002; then
    echo -e "${YELLOW}To start the backend:${NC}"
    echo -e "   cd backend"
    echo -e "   export PORT=5002"
    echo -e "   npm start"
    echo ""
fi

if ! $FRONTEND_3000 && ! $FRONTEND_3004; then
    echo -e "${YELLOW}To start the frontend:${NC}"
    echo -e "   cd frontend"
    echo -e "   npm run dev"
    echo ""
fi

echo -e "${BLUE}Or run the fixed startup script:${NC}"
echo -e "   ${GREEN}./start-neurocolony-fixed.sh${NC}"
echo ""

# Test health endpoint if backend is running
if $BACKEND_5000 || $BACKEND_5002; then
    echo -e "${BLUE}6️⃣  Testing system health...${NC}"
    echo ""
    
    if $BACKEND_5000; then
        HEALTH_URL="http://localhost:5000/api/test/health/system"
    else
        HEALTH_URL="http://localhost:5002/api/test/health/system"
    fi
    
    echo -e "${YELLOW}Fetching health status from $HEALTH_URL...${NC}"
    curl -s "$HEALTH_URL" | python3 -m json.tool 2>/dev/null || echo -e "${RED}Failed to get health status${NC}"
fi

echo ""
echo -e "${GREEN}✅ Diagnosis complete!${NC}"