import localAiService from './backend/services/localAiService.js'
import { logger } from './backend/utils/logger.js'

async function testLocalAI() {
  try {
    console.log('🧪 Testing Local AI Service...')
    
    const businessInfo = {
      industry: 'E-commerce',
      productService: 'Email Marketing Automation Tool',
      targetAudience: 'Small Business Owners',
      pricePoint: '$29/month',
      mainBenefit: 'Increase sales by 200%',
      painPoint: 'Manual email marketing is time-consuming'
    }
    
    const settings = {
      sequenceLength: 3,
      tone: 'Professional',
      primaryGoal: 'Generate Sales',
      includeCTA: true,
      includePersonalization: true
    }
    
    console.log('📧 Generating 3-email sequence...')
    
    const result = await localAiService.generateEmailSequence(businessInfo, settings)
    
    console.log('✅ Generation successful!')
    console.log('\n📊 AI Analysis:')
    console.log(`Overall Score: ${result.aiAnalysis.overallScore}`)
    console.log(`Generated By: ${result.aiAnalysis.generatedBy}`)
    console.log(`Predicted Conversion Rate: ${result.aiAnalysis.predictedConversionRate}%`)
    
    console.log('\n📧 Generated Emails:')
    result.emails.forEach((email, index) => {
      console.log(`\n--- Email ${index + 1} (Day ${email.dayDelay}) ---`)
      console.log(`Subject: ${email.subject}`)
      console.log(`Score: ${email.conversionScore}`)
      console.log(`Triggers: ${email.psychologyTriggers.join(', ')}`)
      console.log(`Body Preview: ${email.body.substring(0, 200)}...`)
    })
    
    console.log('\n🎉 Local AI Test Complete!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.log('\n🔄 This is expected if Ollama is not running or models are not available.')
    console.log('✅ The system will fall back to demo mode in production.')
  }
}

testLocalAI()