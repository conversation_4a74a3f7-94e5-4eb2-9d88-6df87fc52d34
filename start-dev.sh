#!/bin/bash

# SequenceAI Development Startup Script
echo "🚀 Starting SequenceAI Development Environment..."

# Kill any existing processes
echo "🧹 Cleaning up existing processes..."
pkill -f "node.*server" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
sleep 2

# Navigate to project directory
cd /home/<USER>/SequenceAI

# Start backend in background
echo "📡 Starting backend server..."
cd backend
node server-minimal-working.js &
BACKEND_PID=$!
echo "Backend PID: $BACKEND_PID"

# Wait for backend to start
sleep 3

# Test backend
echo "🧪 Testing backend connection..."
curl -s http://localhost:5001/health || echo "❌ Backend not responding"

# Start frontend in background
echo "🎨 Starting frontend server..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!
echo "Frontend PID: $FRONTEND_PID"

# Wait for frontend to start
sleep 5

echo ""
echo "✅ SequenceAI Development Environment Started!"
echo "📡 Backend: http://localhost:5001"
echo "🎨 Frontend: http://localhost:3000"
echo "📊 API Test: http://localhost:5001/api/test"
echo ""
echo "To stop servers:"
echo "kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "🎯 Ready for testing conversion optimization features!"

# Keep script running to monitor
wait