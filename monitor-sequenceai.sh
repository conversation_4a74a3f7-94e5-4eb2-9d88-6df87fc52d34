#!/bin/bash

# SequenceAI System Monitor
# Comprehensive health monitoring and auto-restart capabilities

set -euo pipefail

# Configuration
BACKEND_PORT=${BACKEND_PORT:-5000}
FRONTEND_PORT=${FRONTEND_PORT:-3001}
MONGO_PORT=${MONGO_PORT:-27018}
REDIS_PORT=${REDIS_PORT:-6380}
NGINX_PORT=${NGINX_PORT:-8080}
LOG_FILE="/home/<USER>/convertflow/monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# Check if port is listening
check_port() {
    local port=$1
    local service=$2
    
    if netstat -tuln | grep -q ":$port "; then
        log "${GREEN}✅ $service (port $port) is running${NC}"
        return 0
    else
        log "${RED}❌ $service (port $port) is not running${NC}"
        return 1
    fi
}

# Check HTTP endpoint
check_http() {
    local url=$1
    local service=$2
    
    if curl -sf "$url" >/dev/null 2>&1; then
        log "${GREEN}✅ $service HTTP check passed${NC}"
        return 0
    else
        log "${RED}❌ $service HTTP check failed${NC}"
        return 1
    fi
}

# Check system health endpoint
check_system_health() {
    local health_url="http://localhost:$BACKEND_PORT/api/test/health/system"
    
    if command -v jq >/dev/null 2>&1; then
        local response=$(curl -sf "$health_url" 2>/dev/null)
        local status=$(echo "$response" | jq -r '.status // "error"')
        
        case "$status" in
            "healthy")
                log "${GREEN}✅ System health check: HEALTHY${NC}"
                return 0
                ;;
            "warning")
                log "${YELLOW}⚠️ System health check: WARNING${NC}"
                echo "$response" | jq '.checks' | tee -a "$LOG_FILE"
                return 0
                ;;
            *)
                log "${RED}❌ System health check: UNHEALTHY${NC}"
                echo "$response" | jq '.checks // .error' | tee -a "$LOG_FILE"
                return 1
                ;;
        esac
    else
        check_http "$health_url" "System Health"
    fi
}

# Main monitoring function
monitor_services() {
    log "${BLUE}🔍 Starting SequenceAI system monitor...${NC}"
    
    local all_good=true
    
    # Check core services
    check_port "$MONGO_PORT" "MongoDB" || all_good=false
    check_port "$REDIS_PORT" "Redis" || all_good=false
    check_port "$BACKEND_PORT" "Backend API" || all_good=false
    check_port "$FRONTEND_PORT" "Frontend" || all_good=false
    check_port "$NGINX_PORT" "Nginx" || all_good=false
    
    # Check HTTP endpoints
    check_http "http://localhost:$BACKEND_PORT/api/health" "Backend Health" || all_good=false
    check_http "http://localhost:$FRONTEND_PORT" "Frontend" || all_good=false
    check_http "http://localhost:$NGINX_PORT" "Nginx Proxy" || all_good=false
    
    # Comprehensive system health
    check_system_health || all_good=false
    
    if [ "$all_good" = true ]; then
        log "${GREEN}🎉 All SequenceAI services are healthy!${NC}"
        return 0
    else
        log "${RED}🚨 Some SequenceAI services have issues!${NC}"
        return 1
    fi
}

# Resource monitoring
monitor_resources() {
    log "${BLUE}📊 Resource usage:${NC}"
    
    # Memory usage
    local mem_usage=$(free -h | awk '/^Mem:/{print $3"/"$2}')
    log "💾 Memory: $mem_usage"
    
    # Disk usage
    local disk_usage=$(df -h / | awk 'NR==2{print $5}')
    log "💿 Disk: $disk_usage used"
    
    # CPU load
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}')
    log "⚡ CPU load:$cpu_load"
    
    # Node.js processes
    local node_count=$(pgrep -f "node" | wc -l)
    log "🔧 Node.js processes: $node_count"
}

# Auto-restart function
auto_restart() {
    log "${YELLOW}🔄 Attempting auto-restart of failed services...${NC}"
    
    # Try to restart backend if it's down
    if ! check_port "$BACKEND_PORT" "Backend API" >/dev/null 2>&1; then
        log "${YELLOW}🔄 Restarting backend service...${NC}"
        cd /home/<USER>/convertflow/backend
        nohup npm start >/dev/null 2>&1 &
        sleep 5
    fi
    
    # Check if restart was successful
    if monitor_services >/dev/null 2>&1; then
        log "${GREEN}✅ Auto-restart successful!${NC}"
        return 0
    else
        log "${RED}❌ Auto-restart failed!${NC}"
        return 1
    fi
}

# Usage information
usage() {
    echo "SequenceAI System Monitor"
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  monitor    Monitor all services (default)"
    echo "  restart    Attempt auto-restart of failed services"
    echo "  resources  Show resource usage"
    echo "  watch      Continuous monitoring (every 30 seconds)"
    echo "  help       Show this help message"
}

# Main execution
case "${1:-monitor}" in
    "monitor")
        monitor_services
        monitor_resources
        ;;
    "restart")
        auto_restart
        ;;
    "resources")
        monitor_resources
        ;;
    "watch")
        log "${BLUE}🔍 Starting continuous monitoring (Ctrl+C to stop)...${NC}"
        while true; do
            monitor_services
            echo ""
            sleep 30
        done
        ;;
    "help")
        usage
        ;;
    *)
        echo "Unknown option: $1"
        usage
        exit 1
        ;;
esac