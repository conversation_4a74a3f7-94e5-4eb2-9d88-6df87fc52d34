#!/bin/bash

echo "🧪 Testing NeuroColony Stripe Webhook Integration"
echo "==============================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo ""
echo "Testing the 5 webhook events NeuroColony handles..."
echo ""

# Test each event that NeuroColony processes
events=(
    "customer.subscription.created"
    "customer.subscription.updated" 
    "customer.subscription.deleted"
    "invoice.payment_succeeded"
    "invoice.payment_failed"
)

for event in "${events[@]}"; do
    echo -e "${YELLOW}Testing: $event${NC}"
    
    if stripe trigger "$event" &> /dev/null; then
        echo -e "${GREEN}✅ Successfully triggered $event${NC}"
    else
        echo -e "${RED}❌ Failed to trigger $event${NC}"
    fi
    
    sleep 1
done

echo ""
echo -e "${YELLOW}Check your NeuroColony backend console for webhook events!${NC}"
echo ""
echo "You should see messages like:"
echo "  📨 Webhook received: customer.subscription.created"
echo "  📨 Webhook received: invoice.payment_succeeded"
echo ""
echo "If you don't see these messages, check:"
echo "  1. Backend is running on port 5000"
echo "  2. Stripe webhook forwarding is active"
echo "  3. Webhook secret is correctly configured"