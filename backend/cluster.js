import cluster from 'cluster'
import os from 'os'
import { logger } from './utils/logger.js'

class ClusterManager {
  constructor() {
    this.numWorkers = process.env.CLUSTER_WORKERS ? 
      parseInt(process.env.CLUSTER_WORKERS) : 
      Math.min(os.cpus().length, 4) // Max 4 workers for development
    
    this.workerStats = new Map()
    this.setupCluster()
  }

  setupCluster() {
    if (cluster.isPrimary) {
      logger.info(`🏭 Starting cluster with ${this.numWorkers} workers`)
      
      // Fork workers
      for (let i = 0; i < this.numWorkers; i++) {
        this.forkWorker(i)
      }

      // Handle worker events
      cluster.on('online', (worker) => {
        logger.info(`👷 Worker ${worker.process.pid} is online`)
        this.workerStats.set(worker.id, {
          pid: worker.process.pid,
          startTime: Date.now(),
          restarts: 0,
          status: 'online'
        })
      })

      cluster.on('exit', (worker, code, signal) => {
        const stats = this.workerStats.get(worker.id)
        const uptime = stats ? Date.now() - stats.startTime : 0
        
        logger.error(`💀 Worker ${worker.process.pid} died (${signal || code}) after ${Math.round(uptime/1000)}s`)
        
        // Update stats
        if (stats) {
          stats.restarts++
          stats.status = 'dead'
        }

        // Restart worker if not intentional shutdown
        if (!worker.exitedAfterDisconnect) {
          logger.info('🔄 Restarting worker...')
          this.forkWorker(worker.id)
        }
      })

      cluster.on('disconnect', (worker) => {
        logger.warn(`🔌 Worker ${worker.process.pid} disconnected`)
        const stats = this.workerStats.get(worker.id)
        if (stats) {
          stats.status = 'disconnected'
        }
      })

      // Graceful shutdown handling
      process.on('SIGTERM', () => {
        logger.info('🛑 Received SIGTERM, shutting down cluster gracefully...')
        this.gracefulShutdown()
      })

      process.on('SIGINT', () => {
        logger.info('🛑 Received SIGINT, shutting down cluster gracefully...')
        this.gracefulShutdown()
      })

      // Health monitoring
      this.startHealthMonitoring()

    } else {
      // Worker process
      this.startWorker()
    }
  }

  forkWorker(id) {
    const worker = cluster.fork({
      WORKER_ID: id,
      WORKER_START_TIME: Date.now()
    })
    
    // Set up worker communication
    worker.on('message', (message) => {
      this.handleWorkerMessage(worker, message)
    })
    
    return worker
  }

  startWorker() {
    const workerId = process.env.WORKER_ID || cluster.worker.id
    const startTime = process.env.WORKER_START_TIME || Date.now()
    
    logger.info(`🔧 Worker ${process.pid} starting (ID: ${workerId})`)
    
    // Import and start the main application
    import('./server.js').then(() => {
      logger.info(`✅ Worker ${process.pid} ready`)
      
      // Send ready message to master
      if (process.send) {
        process.send({
          type: 'worker_ready',
          pid: process.pid,
          workerId,
          startTime
        })
      }
    }).catch((error) => {
      logger.error(`❌ Worker ${process.pid} failed to start:`, error)
      process.exit(1)
    })

    // Handle worker shutdown
    process.on('SIGTERM', () => {
      logger.info(`🛑 Worker ${process.pid} received SIGTERM, shutting down...`)
      this.shutdownWorker()
    })

    process.on('SIGINT', () => {
      logger.info(`🛑 Worker ${process.pid} received SIGINT, shutting down...`)
      this.shutdownWorker()
    })

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error(`💥 Uncaught exception in worker ${process.pid}:`, error)
      this.shutdownWorker(1)
    })

    process.on('unhandledRejection', (reason, promise) => {
      logger.error(`💥 Unhandled rejection in worker ${process.pid}:`, reason)
      this.shutdownWorker(1)
    })
  }

  handleWorkerMessage(worker, message) {
    switch (message.type) {
      case 'worker_ready':
        logger.info(`✅ Worker ${message.pid} reports ready`)
        break
      
      case 'health_check':
        // Respond to health check
        worker.send({
          type: 'health_response',
          status: 'healthy',
          pid: worker.process.pid,
          timestamp: Date.now()
        })
        break
      
      case 'performance_stats':
        // Update worker performance stats
        const stats = this.workerStats.get(worker.id)
        if (stats) {
          stats.performance = message.data
        }
        break
    }
  }

  startHealthMonitoring() {
    // Monitor worker health every 30 seconds
    setInterval(() => {
      this.checkWorkerHealth()
    }, 30000)

    // Log cluster stats every 5 minutes
    setInterval(() => {
      this.logClusterStats()
    }, 5 * 60 * 1000)
  }

  checkWorkerHealth() {
    const workers = Object.values(cluster.workers)
    const healthyWorkers = workers.filter(worker => worker && !worker.isDead())
    
    if (healthyWorkers.length < this.numWorkers) {
      logger.warn(`⚠️  Only ${healthyWorkers.length}/${this.numWorkers} workers are healthy`)
    }

    // Send health check to all workers
    workers.forEach(worker => {
      if (worker && !worker.isDead()) {
        worker.send({ type: 'health_check' })
      }
    })
  }

  logClusterStats() {
    const workers = Object.values(cluster.workers)
    const totalWorkers = workers.length
    const healthyWorkers = workers.filter(worker => worker && !worker.isDead()).length
    const deadWorkers = totalWorkers - healthyWorkers

    const uptime = process.uptime()
    const memoryUsage = process.memoryUsage()

    logger.info('📊 Cluster status report', {
      totalWorkers,
      healthyWorkers,
      deadWorkers,
      uptime: `${Math.round(uptime)}s`,
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`
      }
    })

    // Log individual worker stats
    this.workerStats.forEach((stats, workerId) => {
      const worker = cluster.workers[workerId]
      if (worker && stats) {
        const workerUptime = Date.now() - stats.startTime
        logger.debug(`Worker ${workerId} stats`, {
          pid: stats.pid,
          status: stats.status,
          uptime: `${Math.round(workerUptime / 1000)}s`,
          restarts: stats.restarts
        })
      }
    })
  }

  async shutdownWorker(exitCode = 0) {
    try {
      // Give the worker time to finish current requests
      logger.info(`🔄 Worker ${process.pid} beginning graceful shutdown...`)
      
      // Close server gracefully (this would be implemented in server.js)
      if (global.server) {
        await new Promise((resolve) => {
          global.server.close(() => {
            logger.info(`✅ Worker ${process.pid} server closed`)
            resolve()
          })
        })
      }

      // Close database connections (this would be implemented in server.js)
      if (global.closeConnections) {
        await global.closeConnections()
      }

      logger.info(`✅ Worker ${process.pid} shutdown complete`)
      process.exit(exitCode)
      
    } catch (error) {
      logger.error(`❌ Error during worker shutdown:`, error)
      process.exit(1)
    }
  }

  gracefulShutdown() {
    logger.info('🔄 Starting graceful cluster shutdown...')
    
    const workers = Object.values(cluster.workers)
    let shutdownTimeout

    // Set a timeout for forceful shutdown
    shutdownTimeout = setTimeout(() => {
      logger.error('⏰ Shutdown timeout reached, force killing workers')
      workers.forEach(worker => {
        if (worker && !worker.isDead()) {
          worker.kill('SIGKILL')
        }
      })
      process.exit(1)
    }, 30000) // 30 second timeout

    // Gracefully disconnect all workers
    let remainingWorkers = workers.length
    
    workers.forEach(worker => {
      if (worker && !worker.isDead()) {
        worker.disconnect()
        
        worker.on('disconnect', () => {
          remainingWorkers--
          logger.info(`Worker ${worker.process.pid} disconnected, ${remainingWorkers} remaining`)
          
          if (remainingWorkers === 0) {
            clearTimeout(shutdownTimeout)
            logger.info('✅ All workers disconnected, cluster shutdown complete')
            process.exit(0)
          }
        })
      } else {
        remainingWorkers--
      }
    })

    // If no workers to shutdown
    if (remainingWorkers === 0) {
      clearTimeout(shutdownTimeout)
      logger.info('✅ No workers to shutdown, cluster shutdown complete')
      process.exit(0)
    }
  }

  // Get cluster statistics
  getStats() {
    const workers = Object.values(cluster.workers)
    
    return {
      cluster: {
        totalWorkers: workers.length,
        healthyWorkers: workers.filter(worker => worker && !worker.isDead()).length,
        isPrimary: cluster.isPrimary,
        numCPUs: os.cpus().length
      },
      workers: Array.from(this.workerStats.entries()).map(([id, stats]) => ({
        id,
        pid: stats.pid,
        status: stats.status,
        uptime: `${Math.round((Date.now() - stats.startTime) / 1000)}s`,
        restarts: stats.restarts
      }))
    }
  }

  // Health check for cluster
  healthCheck() {
    const workers = Object.values(cluster.workers)
    const healthyWorkers = workers.filter(worker => worker && !worker.isDead()).length
    const totalWorkers = workers.length

    let status = 'healthy'
    if (healthyWorkers === 0) {
      status = 'unhealthy'
    } else if (healthyWorkers < totalWorkers * 0.5) {
      status = 'degraded'
    }

    return {
      status,
      healthyWorkers,
      totalWorkers,
      isPrimary: cluster.isPrimary
    }
  }
}

// Only initialize cluster manager if clustering is enabled
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_CLUSTER === 'true') {
  new ClusterManager()
} else {
  // Single process mode for development
  logger.info('🔧 Starting in single process mode (development)')
  import('./server.js').catch((error) => {
    logger.error('❌ Failed to start server:', error)
    process.exit(1)
  })
}

export default ClusterManager