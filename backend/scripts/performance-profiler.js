#!/usr/bin/env node

/**
 * NeuroColony Performance Profiler
 * 
 * Advanced performance profiling tool for billion-user scale applications.
 * Provides detailed insights into CPU, memory, database, and business metrics.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import { performance } from 'perf_hooks';
import chalk from 'chalk';
import inquirer from 'inquirer';

class PerformanceProfiler {
  constructor() {
    this.profileSessions = new Map();
    this.metrics = {
      cpu: [],
      memory: [],
      database: [],
      api: [],
      business: []
    };
    this.benchmarks = this.setupBenchmarks();
  }

  setupBenchmarks() {
    return {
      // Performance targets for billion-user scale
      api: {
        responseTime: {
          p50: 100,    // 50th percentile < 100ms
          p95: 200,    // 95th percentile < 200ms
          p99: 500     // 99th percentile < 500ms
        },
        throughput: {
          target: 10000, // 10k requests/second minimum
          peak: 50000    // 50k requests/second peak
        },
        errorRate: {
          warning: 0.1,  // 0.1% warning threshold
          critical: 1.0  // 1.0% critical threshold
        }
      },
      database: {
        queryTime: {
          simple: 10,     // Simple queries < 10ms
          complex: 100,   // Complex queries < 100ms
          analytical: 1000 // Analytical queries < 1s
        },
        connections: {
          max: 1000,      // Maximum connections
          optimal: 100    // Optimal connection count
        }
      },
      memory: {
        heap: {
          warning: 1024,  // 1GB warning
          critical: 2048  // 2GB critical
        },
        leakRate: {
          warning: 10,    // 10MB/hour growth
          critical: 100   // 100MB/hour growth
        }
      },
      business: {
        aiGeneration: {
          latency: 2000,     // < 2s for AI generation
          cost: 0.10,        // < $0.10 per generation
          qualityScore: 80   // > 80 quality score
        },
        userExperience: {
          conversionRate: 5.0,  // > 5% conversion rate
          churnRate: 2.0,       // < 2% monthly churn
          nps: 50               // > 50 NPS score
        }
      }
    };
  }

  async startProfileSession(config = {}) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      startTime: Date.now(),
      config: {
        duration: config.duration || 300000, // 5 minutes default
        interval: config.interval || 5000,   // 5 seconds default
        includeMetrics: config.metrics || ['all'],
        environment: config.environment || 'development',
        loadLevel: config.loadLevel || 'normal'
      },
      metrics: {
        cpu: [],
        memory: [],
        database: [],
        api: [],
        business: []
      },
      status: 'running'
    };

    this.profileSessions.set(sessionId, session);

    console.log(chalk.blue.bold(`🔍 Starting performance profiling session: ${sessionId}`));
    console.log(chalk.gray(`Duration: ${session.config.duration / 1000}s, Interval: ${session.config.interval / 1000}s`));

    // Start monitoring
    await this.startMonitoring(session);

    return sessionId;
  }

  async startMonitoring(session) {
    const { config } = session;
    let elapsed = 0;

    const monitoringInterval = setInterval(async () => {
      try {
        // Collect metrics
        if (config.includeMetrics.includes('all') || config.includeMetrics.includes('cpu')) {
          await this.collectCPUMetrics(session);
        }

        if (config.includeMetrics.includes('all') || config.includeMetrics.includes('memory')) {
          await this.collectMemoryMetrics(session);
        }

        if (config.includeMetrics.includes('all') || config.includeMetrics.includes('database')) {
          await this.collectDatabaseMetrics(session);
        }

        if (config.includeMetrics.includes('all') || config.includeMetrics.includes('api')) {
          await this.collectAPIMetrics(session);
        }

        if (config.includeMetrics.includes('all') || config.includeMetrics.includes('business')) {
          await this.collectBusinessMetrics(session);
        }

        elapsed += config.interval;

        // Progress indicator
        const progress = Math.round((elapsed / config.duration) * 100);
        console.log(chalk.blue(`📊 Profiling progress: ${progress}%`));

        // Check if session should end
        if (elapsed >= config.duration) {
          clearInterval(monitoringInterval);
          await this.endProfileSession(session.id);
        }

      } catch (error) {
        console.error(chalk.red(`Error during monitoring: ${error.message}`));
      }
    }, config.interval);

    // Store interval reference for cleanup
    session.monitoringInterval = monitoringInterval;
  }

  async collectCPUMetrics(session) {
    try {
      // Get CPU usage from system
      const cpuUsage = process.cpuUsage();
      const currentTime = Date.now();
      
      // Calculate CPU percentage
      const totalUsage = cpuUsage.user + cpuUsage.system;
      const elapsedTime = currentTime - session.startTime;
      const cpuPercent = (totalUsage / (elapsedTime * 1000)) * 100;

      // Get load average (Unix systems)
      let loadAverage = [0, 0, 0];
      try {
        const loadOutput = execSync('uptime', { encoding: 'utf8' });
        const loadMatch = loadOutput.match(/load average: ([0-9.]+), ([0-9.]+), ([0-9.]+)/);
        if (loadMatch) {
          loadAverage = [
            parseFloat(loadMatch[1]),
            parseFloat(loadMatch[2]),
            parseFloat(loadMatch[3])
          ];
        }
      } catch (error) {
        // Windows or other systems - use process CPU usage
      }

      const cpuMetric = {
        timestamp: currentTime,
        usage: cpuPercent,
        user: cpuUsage.user,
        system: cpuUsage.system,
        loadAverage: loadAverage,
        processes: await this.getProcessCount()
      };

      session.metrics.cpu.push(cpuMetric);

    } catch (error) {
      console.error(chalk.red(`CPU metrics collection failed: ${error.message}`));
    }
  }

  async collectMemoryMetrics(session) {
    try {
      const memUsage = process.memoryUsage();
      const currentTime = Date.now();

      // Get system memory info
      let systemMemory = { total: 0, free: 0, used: 0 };
      try {
        if (process.platform === 'linux') {
          const meminfo = await fs.readFile('/proc/meminfo', 'utf8');
          const totalMatch = meminfo.match(/MemTotal:\s+(\d+)\s+kB/);
          const freeMatch = meminfo.match(/MemFree:\s+(\d+)\s+kB/);
          const availableMatch = meminfo.match(/MemAvailable:\s+(\d+)\s+kB/);

          if (totalMatch && freeMatch) {
            systemMemory.total = parseInt(totalMatch[1]) * 1024;
            systemMemory.free = parseInt(freeMatch[1]) * 1024;
            systemMemory.available = availableMatch ? parseInt(availableMatch[1]) * 1024 : systemMemory.free;
            systemMemory.used = systemMemory.total - systemMemory.free;
          }
        }
      } catch (error) {
        // Fallback for non-Linux systems
      }

      const memoryMetric = {
        timestamp: currentTime,
        process: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
          arrayBuffers: memUsage.arrayBuffers
        },
        system: systemMemory,
        gc: await this.getGCStats()
      };

      session.metrics.memory.push(memoryMetric);

      // Check for memory leaks
      await this.checkMemoryLeaks(session);

    } catch (error) {
      console.error(chalk.red(`Memory metrics collection failed: ${error.message}`));
    }
  }

  async collectDatabaseMetrics(session) {
    try {
      const currentTime = Date.now();

      // Simulate database metrics collection
      // In a real implementation, you would connect to your actual database
      const dbMetric = {
        timestamp: currentTime,
        connections: {
          active: Math.floor(Math.random() * 50) + 10,
          idle: Math.floor(Math.random() * 20) + 5,
          total: 100
        },
        queries: {
          total: Math.floor(Math.random() * 1000) + 500,
          slow: Math.floor(Math.random() * 10),
          failed: Math.floor(Math.random() * 5)
        },
        performance: {
          avgQueryTime: Math.random() * 50 + 10,
          p95QueryTime: Math.random() * 200 + 50,
          cacheHitRate: Math.random() * 20 + 80
        },
        storage: {
          size: Math.random() * 1000 + 5000, // MB
          indexSize: Math.random() * 200 + 100, // MB
          freeSpace: Math.random() * 500 + 1000 // MB
        }
      };

      session.metrics.database.push(dbMetric);

    } catch (error) {
      console.error(chalk.red(`Database metrics collection failed: ${error.message}`));
    }
  }

  async collectAPIMetrics(session) {
    try {
      const currentTime = Date.now();

      // In a real implementation, you would collect these from your monitoring system
      const apiMetric = {
        timestamp: currentTime,
        requests: {
          total: Math.floor(Math.random() * 1000) + 500,
          successful: Math.floor(Math.random() * 950) + 470,
          errors: Math.floor(Math.random() * 50) + 10,
          rps: Math.floor(Math.random() * 100) + 50
        },
        responseTime: {
          avg: Math.random() * 100 + 50,
          p50: Math.random() * 80 + 40,
          p95: Math.random() * 200 + 100,
          p99: Math.random() * 500 + 200
        },
        endpoints: [
          {
            path: '/api/sequences/generate',
            requests: Math.floor(Math.random() * 200) + 100,
            avgResponseTime: Math.random() * 2000 + 500,
            errorRate: Math.random() * 2
          },
          {
            path: '/api/auth/login',
            requests: Math.floor(Math.random() * 100) + 50,
            avgResponseTime: Math.random() * 300 + 100,
            errorRate: Math.random() * 1
          }
        ]
      };

      session.metrics.api.push(apiMetric);

    } catch (error) {
      console.error(chalk.red(`API metrics collection failed: ${error.message}`));
    }
  }

  async collectBusinessMetrics(session) {
    try {
      const currentTime = Date.now();

      // Business metrics would typically come from your analytics system
      const businessMetric = {
        timestamp: currentTime,
        users: {
          active: Math.floor(Math.random() * 1000) + 500,
          new: Math.floor(Math.random() * 50) + 10,
          returning: Math.floor(Math.random() * 800) + 400
        },
        aiGeneration: {
          total: Math.floor(Math.random() * 200) + 100,
          successful: Math.floor(Math.random() * 190) + 95,
          avgLatency: Math.random() * 3000 + 1000,
          avgCost: Math.random() * 0.10 + 0.02,
          avgQualityScore: Math.random() * 20 + 75
        },
        revenue: {
          hourly: Math.random() * 1000 + 500,
          conversions: Math.floor(Math.random() * 20) + 5,
          churn: Math.random() * 2 + 0.5
        },
        performance: {
          pageLoadTime: Math.random() * 2000 + 500,
          bounceRate: Math.random() * 20 + 10,
          userSatisfaction: Math.random() * 30 + 70
        }
      };

      session.metrics.business.push(businessMetric);

    } catch (error) {
      console.error(chalk.red(`Business metrics collection failed: ${error.message}`));
    }
  }

  async endProfileSession(sessionId) {
    const session = this.profileSessions.get(sessionId);
    if (!session) {
      throw new Error(`Profile session not found: ${sessionId}`);
    }

    session.status = 'completed';
    session.endTime = Date.now();

    // Clear monitoring interval
    if (session.monitoringInterval) {
      clearInterval(session.monitoringInterval);
    }

    console.log(chalk.green.bold(`✅ Profiling session completed: ${sessionId}`));

    // Generate analysis report
    const analysis = await this.analyzeProfileData(session);
    
    // Save report
    await this.saveProfileReport(session, analysis);

    // Display summary
    await this.displayAnalysisSummary(analysis);

    return analysis;
  }

  async analyzeProfileData(session) {
    const analysis = {
      sessionId: session.id,
      duration: session.endTime - session.startTime,
      summary: {},
      performance: {},
      recommendations: [],
      alerts: [],
      scores: {}
    };

    // Analyze CPU performance
    if (session.metrics.cpu.length > 0) {
      analysis.performance.cpu = this.analyzeCPUMetrics(session.metrics.cpu);
    }

    // Analyze memory performance
    if (session.metrics.memory.length > 0) {
      analysis.performance.memory = this.analyzeMemoryMetrics(session.metrics.memory);
    }

    // Analyze database performance
    if (session.metrics.database.length > 0) {
      analysis.performance.database = this.analyzeDatabaseMetrics(session.metrics.database);
    }

    // Analyze API performance
    if (session.metrics.api.length > 0) {
      analysis.performance.api = this.analyzeAPIMetrics(session.metrics.api);
    }

    // Analyze business metrics
    if (session.metrics.business.length > 0) {
      analysis.performance.business = this.analyzeBusinessMetrics(session.metrics.business);
    }

    // Generate overall performance score
    analysis.scores.overall = this.calculateOverallScore(analysis.performance);

    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(analysis.performance);

    // Generate alerts
    analysis.alerts = this.generateAlerts(analysis.performance);

    return analysis;
  }

  analyzeCPUMetrics(cpuMetrics) {
    const usage = cpuMetrics.map(m => m.usage);
    const loadAverages = cpuMetrics.map(m => m.loadAverage[0]);

    return {
      avgUsage: this.calculateAverage(usage),
      maxUsage: Math.max(...usage),
      p95Usage: this.calculatePercentile(usage, 95),
      avgLoadAverage: this.calculateAverage(loadAverages),
      maxLoadAverage: Math.max(...loadAverages),
      trend: this.calculateTrend(usage),
      score: this.calculateCPUScore(usage)
    };
  }

  analyzeMemoryMetrics(memoryMetrics) {
    const heapUsed = memoryMetrics.map(m => m.process.heapUsed);
    const rss = memoryMetrics.map(m => m.process.rss);

    return {
      avgHeapUsed: this.calculateAverage(heapUsed),
      maxHeapUsed: Math.max(...heapUsed),
      avgRSS: this.calculateAverage(rss),
      maxRSS: Math.max(...rss),
      memoryGrowth: this.calculateMemoryGrowth(memoryMetrics),
      leakDetected: this.detectMemoryLeak(memoryMetrics),
      score: this.calculateMemoryScore(memoryMetrics)
    };
  }

  analyzeDatabaseMetrics(dbMetrics) {
    const queryTimes = dbMetrics.map(m => m.performance.avgQueryTime);
    const slowQueries = dbMetrics.map(m => m.queries.slow);
    const cacheHitRates = dbMetrics.map(m => m.performance.cacheHitRate);

    return {
      avgQueryTime: this.calculateAverage(queryTimes),
      p95QueryTime: this.calculatePercentile(queryTimes, 95),
      avgSlowQueries: this.calculateAverage(slowQueries),
      avgCacheHitRate: this.calculateAverage(cacheHitRates),
      connectionUtilization: this.calculateConnectionUtilization(dbMetrics),
      score: this.calculateDatabaseScore(dbMetrics)
    };
  }

  analyzeAPIMetrics(apiMetrics) {
    const responseTimes = apiMetrics.map(m => m.responseTime.avg);
    const p95Times = apiMetrics.map(m => m.responseTime.p95);
    const errorRates = apiMetrics.map(m => (m.requests.errors / m.requests.total) * 100);
    const rps = apiMetrics.map(m => m.requests.rps);

    return {
      avgResponseTime: this.calculateAverage(responseTimes),
      p95ResponseTime: this.calculateAverage(p95Times),
      avgErrorRate: this.calculateAverage(errorRates),
      avgRPS: this.calculateAverage(rps),
      maxRPS: Math.max(...rps),
      throughputTrend: this.calculateTrend(rps),
      score: this.calculateAPIScore(apiMetrics)
    };
  }

  analyzeBusinessMetrics(businessMetrics) {
    const aiLatencies = businessMetrics.map(m => m.aiGeneration.avgLatency);
    const aiCosts = businessMetrics.map(m => m.aiGeneration.avgCost);
    const qualityScores = businessMetrics.map(m => m.aiGeneration.avgQualityScore);
    const revenues = businessMetrics.map(m => m.revenue.hourly);

    return {
      avgAILatency: this.calculateAverage(aiLatencies),
      avgAICost: this.calculateAverage(aiCosts),
      avgQualityScore: this.calculateAverage(qualityScores),
      avgHourlyRevenue: this.calculateAverage(revenues),
      revenueTrend: this.calculateTrend(revenues),
      score: this.calculateBusinessScore(businessMetrics)
    };
  }

  calculateOverallScore(performance) {
    const scores = [];
    
    if (performance.cpu) scores.push(performance.cpu.score);
    if (performance.memory) scores.push(performance.memory.score);
    if (performance.database) scores.push(performance.database.score);
    if (performance.api) scores.push(performance.api.score);
    if (performance.business) scores.push(performance.business.score);

    return scores.length > 0 ? this.calculateAverage(scores) : 0;
  }

  generateRecommendations(performance) {
    const recommendations = [];

    // CPU recommendations
    if (performance.cpu && performance.cpu.avgUsage > 70) {
      recommendations.push({
        category: 'cpu',
        priority: 'high',
        title: 'High CPU Usage Detected',
        description: `Average CPU usage is ${performance.cpu.avgUsage.toFixed(1)}%. Consider optimizing algorithms or scaling horizontally.`,
        actions: [
          'Profile CPU-intensive functions',
          'Implement caching for expensive operations',
          'Consider horizontal scaling',
          'Review algorithm complexity'
        ]
      });
    }

    // Memory recommendations
    if (performance.memory && performance.memory.leakDetected) {
      recommendations.push({
        category: 'memory',
        priority: 'critical',
        title: 'Memory Leak Detected',
        description: 'Memory usage is continuously growing, indicating a potential memory leak.',
        actions: [
          'Review event listeners and timers',
          'Check for circular references',
          'Implement proper cleanup in lifecycle methods',
          'Use memory profiling tools'
        ]
      });
    }

    // Database recommendations
    if (performance.database && performance.database.avgQueryTime > 100) {
      recommendations.push({
        category: 'database',
        priority: 'medium',
        title: 'Slow Database Queries',
        description: `Average query time is ${performance.database.avgQueryTime.toFixed(1)}ms.`,
        actions: [
          'Add database indexes',
          'Optimize slow queries',
          'Implement query caching',
          'Consider database sharding'
        ]
      });
    }

    // API recommendations
    if (performance.api && performance.api.avgResponseTime > 200) {
      recommendations.push({
        category: 'api',
        priority: 'medium',
        title: 'High API Response Times',
        description: `Average response time is ${performance.api.avgResponseTime.toFixed(1)}ms.`,
        actions: [
          'Implement response caching',
          'Optimize database queries',
          'Add CDN for static assets',
          'Consider API rate limiting'
        ]
      });
    }

    return recommendations;
  }

  generateAlerts(performance) {
    const alerts = [];

    // Check against benchmarks
    if (performance.api && performance.api.p95ResponseTime > this.benchmarks.api.responseTime.p95) {
      alerts.push({
        type: 'performance',
        severity: 'warning',
        message: `API P95 response time (${performance.api.p95ResponseTime.toFixed(1)}ms) exceeds target (${this.benchmarks.api.responseTime.p95}ms)`
      });
    }

    if (performance.memory && performance.memory.leakDetected) {
      alerts.push({
        type: 'memory',
        severity: 'critical',
        message: 'Memory leak detected - memory usage growing continuously'
      });
    }

    if (performance.cpu && performance.cpu.maxUsage > 90) {
      alerts.push({
        type: 'cpu',
        severity: 'critical',
        message: `CPU usage peaked at ${performance.cpu.maxUsage.toFixed(1)}%`
      });
    }

    return alerts;
  }

  async saveProfileReport(session, analysis) {
    const reportDir = path.join(process.cwd(), 'performance-reports');
    await fs.mkdir(reportDir, { recursive: true });

    const reportFile = path.join(reportDir, `profile-${session.id}.json`);
    const report = {
      session: {
        id: session.id,
        startTime: session.startTime,
        endTime: session.endTime,
        duration: session.endTime - session.startTime,
        config: session.config
      },
      metrics: session.metrics,
      analysis: analysis,
      generatedAt: Date.now()
    };

    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));

    // Also generate HTML report
    const htmlReport = await this.generateHTMLReport(report);
    const htmlFile = path.join(reportDir, `profile-${session.id}.html`);
    await fs.writeFile(htmlFile, htmlReport);

    console.log(chalk.green(`📄 Performance report saved: ${reportFile}`));
    console.log(chalk.green(`🌐 HTML report saved: ${htmlFile}`));
  }

  async displayAnalysisSummary(analysis) {
    console.log(chalk.blue.bold('\n📊 Performance Analysis Summary\n'));

    // Overall score
    const scoreColor = analysis.scores.overall >= 80 ? 'green' : 
                     analysis.scores.overall >= 60 ? 'yellow' : 'red';
    console.log(chalk[scoreColor](`Overall Performance Score: ${analysis.scores.overall.toFixed(1)}/100`));

    // Performance breakdown
    if (analysis.performance.cpu) {
      console.log(chalk.blue('\n🖥️  CPU Performance:'));
      console.log(`  Average Usage: ${analysis.performance.cpu.avgUsage.toFixed(1)}%`);
      console.log(`  Peak Usage: ${analysis.performance.cpu.maxUsage.toFixed(1)}%`);
      console.log(`  Load Average: ${analysis.performance.cpu.avgLoadAverage.toFixed(2)}`);
    }

    if (analysis.performance.memory) {
      console.log(chalk.blue('\n🧠 Memory Performance:'));
      console.log(`  Average Heap: ${(analysis.performance.memory.avgHeapUsed / 1024 / 1024).toFixed(1)} MB`);
      console.log(`  Peak Heap: ${(analysis.performance.memory.maxHeapUsed / 1024 / 1024).toFixed(1)} MB`);
      console.log(`  Memory Leak: ${analysis.performance.memory.leakDetected ? '⚠️  Detected' : '✅ None'}`);
    }

    if (analysis.performance.api) {
      console.log(chalk.blue('\n🌐 API Performance:'));
      console.log(`  Average Response Time: ${analysis.performance.api.avgResponseTime.toFixed(1)}ms`);
      console.log(`  P95 Response Time: ${analysis.performance.api.p95ResponseTime.toFixed(1)}ms`);
      console.log(`  Average RPS: ${analysis.performance.api.avgRPS.toFixed(0)}`);
      console.log(`  Error Rate: ${analysis.performance.api.avgErrorRate.toFixed(2)}%`);
    }

    // Alerts
    if (analysis.alerts.length > 0) {
      console.log(chalk.red.bold('\n⚠️  Alerts:'));
      analysis.alerts.forEach(alert => {
        const color = alert.severity === 'critical' ? 'red' : 'yellow';
        console.log(chalk[color](`  ${alert.severity.toUpperCase()}: ${alert.message}`));
      });
    }

    // Top recommendations
    if (analysis.recommendations.length > 0) {
      console.log(chalk.blue.bold('\n💡 Top Recommendations:'));
      analysis.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(chalk.blue(`${index + 1}. ${rec.title}`));
        console.log(chalk.gray(`   ${rec.description}`));
      });
    }
  }

  // Utility methods
  calculateAverage(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }

  calculatePercentile(values, percentile) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);
    
    return ((secondAvg - firstAvg) / firstAvg) * 100;
  }

  calculateCPUScore(usage) {
    const avgUsage = this.calculateAverage(usage);
    const maxUsage = Math.max(...usage);
    
    let score = 100;
    if (avgUsage > 70) score -= 30;
    if (maxUsage > 90) score -= 40;
    
    return Math.max(0, score);
  }

  calculateMemoryScore(memoryMetrics) {
    const heapGrowth = this.calculateMemoryGrowth(memoryMetrics);
    const leakDetected = this.detectMemoryLeak(memoryMetrics);
    
    let score = 100;
    if (heapGrowth > 10) score -= 20; // 10MB/hour growth
    if (leakDetected) score -= 50;
    
    return Math.max(0, score);
  }

  calculateDatabaseScore(dbMetrics) {
    const avgQueryTime = this.calculateAverage(dbMetrics.map(m => m.performance.avgQueryTime));
    const avgSlowQueries = this.calculateAverage(dbMetrics.map(m => m.queries.slow));
    
    let score = 100;
    if (avgQueryTime > 50) score -= 20;
    if (avgQueryTime > 100) score -= 30;
    if (avgSlowQueries > 5) score -= 25;
    
    return Math.max(0, score);
  }

  calculateAPIScore(apiMetrics) {
    const avgResponseTime = this.calculateAverage(apiMetrics.map(m => m.responseTime.avg));
    const avgErrorRate = this.calculateAverage(apiMetrics.map(m => (m.requests.errors / m.requests.total) * 100));
    
    let score = 100;
    if (avgResponseTime > 200) score -= 25;
    if (avgResponseTime > 500) score -= 35;
    if (avgErrorRate > 1) score -= 30;
    
    return Math.max(0, score);
  }

  calculateBusinessScore(businessMetrics) {
    const avgAILatency = this.calculateAverage(businessMetrics.map(m => m.aiGeneration.avgLatency));
    const avgQualityScore = this.calculateAverage(businessMetrics.map(m => m.aiGeneration.avgQualityScore));
    
    let score = 100;
    if (avgAILatency > 3000) score -= 30;
    if (avgQualityScore < 70) score -= 40;
    
    return Math.max(0, score);
  }

  calculateMemoryGrowth(memoryMetrics) {
    if (memoryMetrics.length < 2) return 0;
    
    const startHeap = memoryMetrics[0].process.heapUsed;
    const endHeap = memoryMetrics[memoryMetrics.length - 1].process.heapUsed;
    const duration = memoryMetrics[memoryMetrics.length - 1].timestamp - memoryMetrics[0].timestamp;
    
    // Growth rate in MB per hour
    return ((endHeap - startHeap) / 1024 / 1024) / (duration / 3600000);
  }

  detectMemoryLeak(memoryMetrics) {
    if (memoryMetrics.length < 5) return false;
    
    const heapValues = memoryMetrics.map(m => m.process.heapUsed);
    const trend = this.calculateTrend(heapValues);
    
    // Consider it a leak if memory is growing by more than 5% consistently
    return trend > 5;
  }

  calculateConnectionUtilization(dbMetrics) {
    const avgActive = this.calculateAverage(dbMetrics.map(m => m.connections.active));
    const avgTotal = this.calculateAverage(dbMetrics.map(m => m.connections.total));
    
    return (avgActive / avgTotal) * 100;
  }

  async getProcessCount() {
    try {
      const output = execSync('ps aux | wc -l', { encoding: 'utf8' });
      return parseInt(output.trim()) - 1; // Subtract header line
    } catch (error) {
      return 0;
    }
  }

  async getGCStats() {
    // This would require additional setup to collect GC stats
    // For now, return mock data
    return {
      collections: Math.floor(Math.random() * 10),
      duration: Math.random() * 10,
      reclaimedMemory: Math.random() * 1024 * 1024
    };
  }

  async checkMemoryLeaks(session) {
    // Implementation for memory leak detection
    // This would involve comparing memory usage over time
  }

  generateSessionId() {
    return `prof_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async generateHTMLReport(report) {
    // Generate a comprehensive HTML report
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Report - ${report.session.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 4px; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert.critical { background: #ffebee; border-left: 4px solid #f44336; }
        .alert.warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .recommendation { margin: 10px 0; padding: 15px; background: #e3f2fd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Performance Analysis Report</h1>
        <p><strong>Session ID:</strong> ${report.session.id}</p>
        <p><strong>Duration:</strong> ${Math.round(report.session.duration / 1000)}s</p>
        <p><strong>Generated:</strong> ${new Date(report.generatedAt).toLocaleString()}</p>
    </div>

    <div class="section">
        <h2>Overall Score</h2>
        <div class="metric">
            <strong>Performance Score:</strong> ${report.analysis.scores.overall.toFixed(1)}/100
        </div>
    </div>

    ${report.analysis.alerts.length > 0 ? `
    <div class="section">
        <h2>Alerts</h2>
        ${report.analysis.alerts.map(alert => `
            <div class="alert ${alert.severity}">
                <strong>${alert.severity.toUpperCase()}:</strong> ${alert.message}
            </div>
        `).join('')}
    </div>
    ` : ''}

    ${report.analysis.recommendations.length > 0 ? `
    <div class="section">
        <h2>Recommendations</h2>
        ${report.analysis.recommendations.map(rec => `
            <div class="recommendation">
                <h3>${rec.title}</h3>
                <p>${rec.description}</p>
                <ul>
                    ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                </ul>
            </div>
        `).join('')}
    </div>
    ` : ''}

    <div class="section">
        <h2>Detailed Metrics</h2>
        <pre>${JSON.stringify(report.analysis.performance, null, 2)}</pre>
    </div>
</body>
</html>
    `;
  }

  // CLI interface
  async runCLI() {
    const questions = [
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          'Start new profiling session',
          'View existing reports',
          'Run performance benchmark',
          'Exit'
        ]
      }
    ];

    const { action } = await inquirer.prompt(questions);

    switch (action) {
      case 'Start new profiling session':
        await this.runProfileSession();
        break;
      case 'View existing reports':
        await this.viewReports();
        break;
      case 'Run performance benchmark':
        await this.runBenchmark();
        break;
      case 'Exit':
        console.log(chalk.blue('👋 Goodbye!'));
        process.exit(0);
        break;
    }
  }

  async runProfileSession() {
    const questions = [
      {
        type: 'input',
        name: 'duration',
        message: 'Profiling duration (seconds):',
        default: '300',
        validate: (input) => {
          const num = parseInt(input);
          return num > 0 && num <= 3600 ? true : 'Duration must be between 1 and 3600 seconds';
        }
      },
      {
        type: 'checkbox',
        name: 'metrics',
        message: 'Select metrics to collect:',
        choices: [
          { name: 'CPU Usage', value: 'cpu', checked: true },
          { name: 'Memory Usage', value: 'memory', checked: true },
          { name: 'Database Performance', value: 'database', checked: true },
          { name: 'API Performance', value: 'api', checked: true },
          { name: 'Business Metrics', value: 'business', checked: true }
        ]
      }
    ];

    const answers = await inquirer.prompt(questions);
    
    const config = {
      duration: parseInt(answers.duration) * 1000,
      metrics: answers.metrics,
      interval: 5000
    };

    await this.startProfileSession(config);
  }

  async viewReports() {
    try {
      const reportDir = path.join(process.cwd(), 'performance-reports');
      const files = await fs.readdir(reportDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));

      if (jsonFiles.length === 0) {
        console.log(chalk.yellow('No performance reports found.'));
        return;
      }

      const { selectedFile } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedFile',
          message: 'Select a report to view:',
          choices: jsonFiles.map(f => ({
            name: f.replace('.json', ''),
            value: f
          }))
        }
      ]);

      const reportPath = path.join(reportDir, selectedFile);
      const report = JSON.parse(await fs.readFile(reportPath, 'utf8'));

      console.log(chalk.blue.bold('\n📊 Performance Report Summary\n'));
      console.log(`Session ID: ${report.session.id}`);
      console.log(`Duration: ${Math.round(report.session.duration / 1000)}s`);
      console.log(`Overall Score: ${report.analysis.scores.overall.toFixed(1)}/100`);

      if (report.analysis.alerts.length > 0) {
        console.log(chalk.red.bold('\nAlerts:'));
        report.analysis.alerts.forEach(alert => {
          console.log(chalk.red(`- ${alert.message}`));
        });
      }

      if (report.analysis.recommendations.length > 0) {
        console.log(chalk.blue.bold('\nTop Recommendations:'));
        report.analysis.recommendations.slice(0, 3).forEach((rec, index) => {
          console.log(chalk.blue(`${index + 1}. ${rec.title}`));
        });
      }

    } catch (error) {
      console.error(chalk.red(`Error viewing reports: ${error.message}`));
    }
  }

  async runBenchmark() {
    console.log(chalk.blue('🏃‍♂️ Running performance benchmarks...'));
    
    // This would run specific benchmark tests
    console.log(chalk.green('✅ Benchmarks completed (feature in development)'));
  }
}

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const profiler = new PerformanceProfiler();
  profiler.runCLI().catch(console.error);
}

export { PerformanceProfiler };