import mongoose from 'mongoose'
import dotenv from 'dotenv'
import CompleteUser from '../models/CompleteUser.js'
import { logger } from '../utils/logger.js'

dotenv.config()

/**
 * Database Initialization Script
 * Sets up initial data, indexes, and performs migrations
 */
class DatabaseInitializer {
  constructor() {
    this.logger = logger
  }

  async initialize() {
    try {
      console.log('🔄 Starting database initialization...')
      
      // Connect to MongoDB
      await this.connectToDatabase()
      
      // Create indexes
      await this.createIndexes()
      
      // Set up initial data
      await this.setupInitialData()
      
      // Perform migrations
      await this.performMigrations()
      
      // Validate data integrity
      await this.validateDataIntegrity()
      
      console.log('✅ Database initialization completed successfully!')
      this.logger.info('Database initialization completed')
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      this.logger.error('Database initialization failed:', error)
      throw error
    }
  }

  async connectToDatabase() {
    try {
      const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/sequenceai'
      
      await mongoose.connect(uri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true
      })
      
      console.log('✅ Connected to MongoDB')
      this.logger.info('Connected to MongoDB for initialization')
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error)
      throw error
    }
  }

  async createIndexes() {
    try {
      console.log('🔄 Creating database indexes...')
      
      // User indexes
      await CompleteUser.collection.createIndex({ email: 1 }, { unique: true })
      await CompleteUser.collection.createIndex({ 'subscription.stripeCustomerId': 1 })
      await CompleteUser.collection.createIndex({ 'subscription.plan': 1 })
      await CompleteUser.collection.createIndex({ createdAt: -1 })
      await CompleteUser.collection.createIndex({ 'behavior.lastLogin': -1 })
      await CompleteUser.collection.createIndex({ 'behavior.engagementScore': -1 })
      await CompleteUser.collection.createIndex({ 'usage.currentPeriod.sequencesGenerated': 1 })
      await CompleteUser.collection.createIndex({ 'onboarding.completed': 1 })
      await CompleteUser.collection.createIndex({ 
        'subscription.plan': 1, 
        'behavior.engagementScore': -1 
      })
      
      // Text search indexes
      await CompleteUser.collection.createIndex({
        firstName: 'text',
        lastName: 'text',
        company: 'text',
        'onboarding.industry': 'text'
      })
      
      console.log('✅ Database indexes created')
      this.logger.info('Database indexes created successfully')
    } catch (error) {
      console.error('❌ Index creation failed:', error)
      this.logger.error('Index creation failed:', error)
      throw error
    }
  }

  async setupInitialData() {
    try {
      console.log('🔄 Setting up initial data...')
      
      // Check if admin user exists
      const adminExists = await CompleteUser.findOne({ 
        email: process.env.ADMIN_EMAIL || '<EMAIL>' 
      })
      
      if (!adminExists) {
        // Create admin user
        const adminUser = new CompleteUser({
          email: process.env.ADMIN_EMAIL || '<EMAIL>',
          password: process.env.ADMIN_PASSWORD || 'ChangeMe123!',
          firstName: 'Admin',
          lastName: 'User',
          company: 'NeuroColony',
          isEmailVerified: true,
          subscription: {
            plan: 'enterprise',
            status: 'active',
            price: 0
          },
          team: {
            role: 'owner'
          },
          preferences: {
            theme: 'dark',
            notifications: {
              email: true,
              browser: true,
              marketing: false,
              updates: true
            }
          },
          onboarding: {
            completed: true,
            completedAt: new Date(),
            stepsCompleted: [1, 2, 3, 4, 5]
          }
        })
        
        await adminUser.save()
        console.log('✅ Admin user created')
        this.logger.info('Admin user created')
      }
      
      // Create demo users for testing
      await this.createDemoUsers()
      
      console.log('✅ Initial data setup completed')
    } catch (error) {
      console.error('❌ Initial data setup failed:', error)
      this.logger.error('Initial data setup failed:', error)
      throw error
    }
  }

  async createDemoUsers() {
    try {
      const demoUsers = [
        {
          email: '<EMAIL>',
          plan: 'free',
          firstName: 'Free',
          lastName: 'User',
          company: 'Startup Inc',
          industry: 'SaaS'
        },
        {
          email: '<EMAIL>',
          plan: 'pro',
          firstName: 'Pro',
          lastName: 'User',
          company: 'Growth Corp',
          industry: 'E-commerce'
        },
        {
          email: '<EMAIL>',
          plan: 'business',
          firstName: 'Business',
          lastName: 'User',
          company: 'Enterprise Ltd',
          industry: 'Consulting'
        }
      ]

      for (const userData of demoUsers) {
        const existingUser = await CompleteUser.findOne({ email: userData.email })
        
        if (!existingUser) {
          const demoUser = new CompleteUser({
            email: userData.email,
            password: 'Demo123!',
            firstName: userData.firstName,
            lastName: userData.lastName,
            company: userData.company,
            isEmailVerified: true,
            subscription: {
              plan: userData.plan,
              status: 'active',
              price: userData.plan === 'pro' ? 29 : userData.plan === 'business' ? 79 : 0
            },
            onboarding: {
              completed: true,
              completedAt: new Date(),
              industry: userData.industry,
              businessType: 'B2B',
              goals: ['Increase sales', 'Nurture leads'],
              stepsCompleted: [1, 2]
            },
            usage: {
              currentPeriod: {
                sequencesGenerated: Math.floor(Math.random() * 5),
                emailsSent: Math.floor(Math.random() * 100),
                startDate: new Date()
              }
            },
            behavior: {
              lastLogin: new Date(),
              loginCount: Math.floor(Math.random() * 20) + 1,
              engagementScore: Math.floor(Math.random() * 40) + 30
            }
          })
          
          await demoUser.save()
          console.log(`✅ Demo user created: ${userData.email}`)
        }
      }
    } catch (error) {
      console.error('❌ Demo users creation failed:', error)
      throw error
    }
  }

  async performMigrations() {
    try {
      console.log('🔄 Performing data migrations...')
      
      // Migration 1: Ensure all users have required fields
      await this.ensureRequiredFields()
      
      // Migration 2: Update engagement scores
      await this.updateEngagementScores()
      
      // Migration 3: Fix subscription data
      await this.fixSubscriptionData()
      
      console.log('✅ Data migrations completed')
      this.logger.info('Data migrations completed')
    } catch (error) {
      console.error('❌ Data migrations failed:', error)
      this.logger.error('Data migrations failed:', error)
      throw error
    }
  }

  async ensureRequiredFields() {
    try {
      // Add missing onboarding fields
      await CompleteUser.updateMany(
        { 'onboarding.completed': { $exists: false } },
        {
          $set: {
            'onboarding.completed': false,
            'onboarding.currentStep': 1,
            'onboarding.stepsCompleted': []
          }
        }
      )

      // Add missing behavior fields
      await CompleteUser.updateMany(
        { 'behavior.engagementScore': { $exists: false } },
        {
          $set: {
            'behavior.engagementScore': 0,
            'behavior.upgradeReadiness': 0,
            'behavior.churnRisk': 0
          }
        }
      )

      // Add missing usage fields
      await CompleteUser.updateMany(
        { 'usage.currentPeriod': { $exists: false } },
        {
          $set: {
            'usage.currentPeriod': {
              sequencesGenerated: 0,
              emailsSent: 0,
              startDate: new Date()
            },
            'usage.totalLifetime': {
              sequencesGenerated: 0,
              emailsSent: 0,
              features: {}
            }
          }
        }
      )

      console.log('✅ Required fields migration completed')
    } catch (error) {
      console.error('❌ Required fields migration failed:', error)
      throw error
    }
  }

  async updateEngagementScores() {
    try {
      const users = await CompleteUser.find({})
      
      for (const user of users) {
        // Recalculate engagement score
        user.calculateEngagementScore()
        await user.save()
      }
      
      console.log(`✅ Updated engagement scores for ${users.length} users`)
    } catch (error) {
      console.error('❌ Engagement score update failed:', error)
      throw error
    }
  }

  async fixSubscriptionData() {
    try {
      // Ensure all users have proper subscription data
      await CompleteUser.updateMany(
        { 'subscription.plan': { $exists: false } },
        {
          $set: {
            'subscription.plan': 'free',
            'subscription.status': 'active',
            'subscription.price': 0
          }
        }
      )

      // Set proper pricing for existing plans
      await CompleteUser.updateMany(
        { 'subscription.plan': 'pro', 'subscription.price': { $ne: 29 } },
        { $set: { 'subscription.price': 29 } }
      )

      await CompleteUser.updateMany(
        { 'subscription.plan': 'business', 'subscription.price': { $ne: 79 } },
        { $set: { 'subscription.price': 79 } }
      )

      await CompleteUser.updateMany(
        { 'subscription.plan': 'enterprise', 'subscription.price': { $ne: 199 } },
        { $set: { 'subscription.price': 199 } }
      )

      console.log('✅ Subscription data migration completed')
    } catch (error) {
      console.error('❌ Subscription data migration failed:', error)
      throw error
    }
  }

  async validateDataIntegrity() {
    try {
      console.log('🔄 Validating data integrity...')
      
      // Check for duplicate emails
      const duplicateEmails = await CompleteUser.aggregate([
        { $group: { _id: '$email', count: { $sum: 1 } } },
        { $match: { count: { $gt: 1 } } }
      ])
      
      if (duplicateEmails.length > 0) {
        console.warn('⚠️ Duplicate emails found:', duplicateEmails)
        this.logger.warn('Duplicate emails found:', duplicateEmails)
      }
      
      // Check for invalid subscription plans
      const invalidPlans = await CompleteUser.find({
        'subscription.plan': { $nin: ['free', 'pro', 'business', 'enterprise'] }
      })
      
      if (invalidPlans.length > 0) {
        console.warn('⚠️ Invalid subscription plans found:', invalidPlans.length)
        this.logger.warn(`Invalid subscription plans found: ${invalidPlans.length}`)
        
        // Fix invalid plans
        await CompleteUser.updateMany(
          { 'subscription.plan': { $nin: ['free', 'pro', 'business', 'enterprise'] } },
          { $set: { 'subscription.plan': 'free' } }
        )
      }
      
      // Validate usage data
      const invalidUsage = await CompleteUser.find({
        'usage.currentPeriod.sequencesGenerated': { $lt: 0 }
      })
      
      if (invalidUsage.length > 0) {
        console.warn('⚠️ Invalid usage data found:', invalidUsage.length)
        this.logger.warn(`Invalid usage data found: ${invalidUsage.length}`)
        
        // Fix negative usage
        await CompleteUser.updateMany(
          { 'usage.currentPeriod.sequencesGenerated': { $lt: 0 } },
          { $set: { 'usage.currentPeriod.sequencesGenerated': 0 } }
        )
      }
      
      console.log('✅ Data integrity validation completed')
      this.logger.info('Data integrity validation completed')
    } catch (error) {
      console.error('❌ Data integrity validation failed:', error)
      this.logger.error('Data integrity validation failed:', error)
      throw error
    }
  }

  async generateStats() {
    try {
      const stats = {}
      
      // User statistics
      stats.totalUsers = await CompleteUser.countDocuments()
      stats.usersByPlan = await CompleteUser.aggregate([
        { $group: { _id: '$subscription.plan', count: { $sum: 1 } } }
      ])
      
      // Onboarding statistics
      stats.onboardingCompleted = await CompleteUser.countDocuments({
        'onboarding.completed': true
      })
      stats.onboardingRate = stats.totalUsers > 0 ? 
        Math.round((stats.onboardingCompleted / stats.totalUsers) * 100) : 0
      
      // Engagement statistics
      const engagementStats = await CompleteUser.aggregate([
        {
          $group: {
            _id: null,
            avgEngagement: { $avg: '$behavior.engagementScore' },
            highEngagement: {
              $sum: { $cond: [{ $gte: ['$behavior.engagementScore', 80] }, 1, 0] }
            }
          }
        }
      ])
      
      if (engagementStats.length > 0) {
        stats.averageEngagement = Math.round(engagementStats[0].avgEngagement)
        stats.highEngagementUsers = engagementStats[0].highEngagement
      }
      
      // Usage statistics
      const usageStats = await CompleteUser.aggregate([
        {
          $group: {
            _id: null,
            totalSequences: { $sum: '$usage.totalLifetime.sequencesGenerated' },
            totalEmails: { $sum: '$usage.totalLifetime.emailsSent' },
            activeUsers: {
              $sum: { $cond: [{ $gt: ['$usage.currentPeriod.sequencesGenerated', 0] }, 1, 0] }
            }
          }
        }
      ])
      
      if (usageStats.length > 0) {
        stats.totalSequencesGenerated = usageStats[0].totalSequences
        stats.totalEmailsSent = usageStats[0].totalEmails
        stats.activeUsers = usageStats[0].activeUsers
      }
      
      return stats
    } catch (error) {
      console.error('❌ Stats generation failed:', error)
      return {}
    }
  }

  async cleanup() {
    try {
      console.log('🔄 Performing cleanup...')
      
      // Remove temporary data
      // Add cleanup logic here if needed
      
      console.log('✅ Cleanup completed')
    } catch (error) {
      console.error('❌ Cleanup failed:', error)
    } finally {
      await mongoose.disconnect()
      console.log('🔌 Database connection closed')
    }
  }
}

// Main execution
async function main() {
  const initializer = new DatabaseInitializer()
  
  try {
    await initializer.initialize()
    
    // Generate and display stats
    const stats = await initializer.generateStats()
    console.log('\n📊 Database Statistics:')
    console.log(JSON.stringify(stats, null, 2))
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  } finally {
    await initializer.cleanup()
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default DatabaseInitializer