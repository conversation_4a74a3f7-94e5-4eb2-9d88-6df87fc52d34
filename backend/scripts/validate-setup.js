#!/usr/bin/env node

/**
 * NeuroColony Setup Validation Script
 * 
 * Validates that the world-class developer experience setup is working correctly
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import chalk from 'chalk';

class SetupValidator {
  constructor() {
    this.validationResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  async validateSetup() {
    console.log(chalk.blue.bold('🔍 Validating NeuroColony Developer Experience Setup\n'));

    // Core validation checks
    await this.validateNodeEnvironment();
    await this.validateProjectStructure();
    await this.validateDependencies();
    await this.validateScripts();
    await this.validateDocumentation();
    await this.validateTestingFramework();
    await this.validateLinting();
    await this.validateDockerSetup();

    // Display results
    this.displayResults();
    
    return this.validationResults.failed === 0;
  }

  async validateNodeEnvironment() {
    this.addTestGroup('Node.js Environment');

    // Check Node.js version
    const nodeVersion = process.version;
    const requiredVersion = '18.0.0';
    
    if (this.isVersionCompatible(nodeVersion, requiredVersion)) {
      this.addSuccess(`Node.js version: ${nodeVersion} ✓`);
    } else {
      this.addError(`Node.js version ${requiredVersion}+ required, found ${nodeVersion}`);
    }

    // Check npm version
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      this.addSuccess(`npm version: ${npmVersion} ✓`);
    } catch (error) {
      this.addError('npm not found or not working');
    }

    // Check important environment variables
    const requiredEnvVars = ['NODE_ENV'];
    const optionalEnvVars = ['MONGODB_URI', 'REDIS_URL', 'OPENAI_API_KEY'];

    requiredEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        this.addSuccess(`Environment variable ${envVar} set ✓`);
      } else {
        this.addWarning(`Environment variable ${envVar} not set (will use defaults)`);
      }
    });

    optionalEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        this.addSuccess(`Optional environment variable ${envVar} set ✓`);
      } else {
        this.addInfo(`Optional environment variable ${envVar} not set (expected for setup)`);
      }
    });
  }

  async validateProjectStructure() {
    this.addTestGroup('Project Structure');

    const requiredDirectories = [
      'src',
      'tests',
      'docs',
      'scripts',
      'logs'
    ];

    const recommendedDirectories = [
      'src/core/domain/entities',
      'src/core/usecases',
      'src/infrastructure',
      'tests/unit',
      'tests/integration',
      'docs/api',
      'docs/adr'
    ];

    // Check required directories
    for (const dir of requiredDirectories) {
      try {
        await fs.access(dir);
        this.addSuccess(`Directory exists: ${dir} ✓`);
      } catch (error) {
        this.addError(`Required directory missing: ${dir}`);
      }
    }

    // Check recommended directories
    for (const dir of recommendedDirectories) {
      try {
        await fs.access(dir);
        this.addSuccess(`Clean architecture directory: ${dir} ✓`);
      } catch (error) {
        this.addWarning(`Recommended directory missing: ${dir} (run npm run setup:dev)`);
      }
    }

    // Check important files
    const requiredFiles = [
      'package.json',
      'README.md',
      '.gitignore'
    ];

    const recommendedFiles = [
      '.eslintrc.js',
      '.prettierrc',
      'vitest.config.js',
      'Dockerfile',
      'docker-compose.yml'
    ];

    for (const file of requiredFiles) {
      try {
        await fs.access(file);
        this.addSuccess(`Required file exists: ${file} ✓`);
      } catch (error) {
        this.addError(`Required file missing: ${file}`);
      }
    }

    for (const file of recommendedFiles) {
      try {
        await fs.access(file);
        this.addSuccess(`Configuration file exists: ${file} ✓`);
      } catch (error) {
        this.addWarning(`Configuration file missing: ${file} (run npm run setup:dev)`);
      }
    }
  }

  async validateDependencies() {
    this.addTestGroup('Dependencies');

    try {
      // Check if package.json exists and is valid
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      this.addSuccess('package.json is valid JSON ✓');

      // Check essential dependencies
      const essentialDeps = [
        'express',
        'mongoose',
        'redis',
        'winston',
        'helmet',
        'cors'
      ];

      const devDeps = [
        'vitest',
        'eslint',
        'prettier',
        'nodemon'
      ];

      essentialDeps.forEach(dep => {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          this.addSuccess(`Essential dependency installed: ${dep} ✓`);
        } else {
          this.addError(`Essential dependency missing: ${dep}`);
        }
      });

      devDeps.forEach(dep => {
        if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
          this.addSuccess(`Dev dependency installed: ${dep} ✓`);
        } else {
          this.addWarning(`Dev dependency missing: ${dep} (recommended for DX)`);
        }
      });

      // Check if node_modules exists
      try {
        await fs.access('node_modules');
        this.addSuccess('node_modules directory exists ✓');
      } catch (error) {
        this.addError('node_modules missing - run npm install');
      }

    } catch (error) {
      this.addError(`Failed to validate dependencies: ${error.message}`);
    }
  }

  async validateScripts() {
    this.addTestGroup('NPM Scripts');

    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      const scripts = packageJson.scripts || {};

      const essentialScripts = [
        'start',
        'dev',
        'test',
        'lint',
        'build'
      ];

      const worldClassScripts = [
        'test:unit',
        'test:integration',
        'test:coverage',
        'docs:generate',
        'profile',
        'setup:dev',
        'docker:build',
        'health:check'
      ];

      essentialScripts.forEach(script => {
        if (scripts[script]) {
          this.addSuccess(`Essential script exists: ${script} ✓`);
        } else {
          this.addError(`Essential script missing: ${script}`);
        }
      });

      worldClassScripts.forEach(script => {
        if (scripts[script]) {
          this.addSuccess(`World-class DX script exists: ${script} ✓`);
        } else {
          this.addWarning(`World-class DX script missing: ${script} (enhances developer experience)`);
        }
      });

    } catch (error) {
      this.addError(`Failed to validate scripts: ${error.message}`);
    }
  }

  async validateDocumentation() {
    this.addTestGroup('Documentation');

    const documentationFiles = [
      'README.md',
      'DEVELOPER_EXPERIENCE_BLUEPRINT.md',
      'docs/api/README.md',
      'docs/adr',
      'docs/runbooks'
    ];

    for (const file of documentationFiles) {
      try {
        const stats = await fs.stat(file);
        if (stats.isDirectory()) {
          this.addSuccess(`Documentation directory exists: ${file} ✓`);
        } else {
          this.addSuccess(`Documentation file exists: ${file} ✓`);
        }
      } catch (error) {
        this.addWarning(`Documentation missing: ${file} (enhances team onboarding)`);
      }
    }

    // Check if docs can be generated
    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts['docs:generate']) {
        this.addSuccess('Documentation generation script available ✓');
      }
    } catch (error) {
      this.addWarning('Documentation generation not set up');
    }
  }

  async validateTestingFramework() {
    this.addTestGroup('Testing Framework');

    // Check test configuration files
    const testConfigFiles = [
      'vitest.config.js',
      'tests/helpers/testSetup.js'
    ];

    for (const file of testConfigFiles) {
      try {
        await fs.access(file);
        this.addSuccess(`Test configuration exists: ${file} ✓`);
      } catch (error) {
        this.addWarning(`Test configuration missing: ${file} (run npm run setup:dev)`);
      }
    }

    // Check test directories
    const testDirectories = [
      'tests/unit',
      'tests/integration',
      'tests/e2e'
    ];

    for (const dir of testDirectories) {
      try {
        await fs.access(dir);
        this.addSuccess(`Test directory exists: ${dir} ✓`);
      } catch (error) {
        this.addWarning(`Test directory missing: ${dir} (part of test pyramid)`);
      }
    }

    // Try to run a simple test
    try {
      execSync('npm run test -- --version', { stdio: 'ignore' });
      this.addSuccess('Test runner (Vitest) is working ✓');
    } catch (error) {
      this.addWarning('Test runner not working (install dependencies)');
    }
  }

  async validateLinting() {
    this.addTestGroup('Code Quality & Linting');

    const lintingFiles = [
      '.eslintrc.js',
      '.prettierrc',
      '.gitignore'
    ];

    for (const file of lintingFiles) {
      try {
        await fs.access(file);
        this.addSuccess(`Code quality file exists: ${file} ✓`);
      } catch (error) {
        this.addWarning(`Code quality file missing: ${file} (run npm run setup:dev)`);
      }
    }

    // Check if linting works
    try {
      execSync('npm run lint -- --version', { stdio: 'ignore' });
      this.addSuccess('ESLint is working ✓');
    } catch (error) {
      this.addWarning('ESLint not working (install dependencies)');
    }

    // Check if formatting works
    try {
      execSync('npx prettier --version', { stdio: 'ignore' });
      this.addSuccess('Prettier is working ✓');
    } catch (error) {
      this.addWarning('Prettier not working (install dependencies)');
    }
  }

  async validateDockerSetup() {
    this.addTestGroup('Docker & Containerization');

    const dockerFiles = [
      'Dockerfile',
      'docker-compose.yml',
      'docker-compose.dev.yml',
      '.dockerignore'
    ];

    for (const file of dockerFiles) {
      try {
        await fs.access(file);
        this.addSuccess(`Docker file exists: ${file} ✓`);
      } catch (error) {
        this.addWarning(`Docker file missing: ${file} (improves development consistency)`);
      }
    }

    // Check if Docker is available
    try {
      execSync('docker --version', { stdio: 'ignore' });
      this.addSuccess('Docker is available ✓');
    } catch (error) {
      this.addInfo('Docker not available (optional but recommended)');
    }

    // Check if docker-compose is available
    try {
      execSync('docker-compose --version', { stdio: 'ignore' });
      this.addSuccess('Docker Compose is available ✓');
    } catch (error) {
      this.addInfo('Docker Compose not available (optional but recommended)');
    }
  }

  // Helper methods
  addTestGroup(name) {
    this.validationResults.details.push({
      type: 'group',
      message: name
    });
  }

  addSuccess(message) {
    this.validationResults.passed++;
    this.validationResults.details.push({
      type: 'success',
      message
    });
  }

  addError(message) {
    this.validationResults.failed++;
    this.validationResults.details.push({
      type: 'error',
      message
    });
  }

  addWarning(message) {
    this.validationResults.warnings++;
    this.validationResults.details.push({
      type: 'warning',
      message
    });
  }

  addInfo(message) {
    this.validationResults.details.push({
      type: 'info',
      message
    });
  }

  isVersionCompatible(current, required) {
    const parseVersion = (version) => version.replace('v', '').split('.').map(Number);
    const currentParts = parseVersion(current);
    const requiredParts = parseVersion(required);

    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const requiredPart = requiredParts[i] || 0;

      if (currentPart > requiredPart) return true;
      if (currentPart < requiredPart) return false;
    }

    return true;
  }

  displayResults() {
    console.log('\n' + '='.repeat(60));
    console.log(chalk.blue.bold('📋 VALIDATION RESULTS'));
    console.log('='.repeat(60));

    let currentGroup = '';

    this.validationResults.details.forEach(item => {
      switch (item.type) {
        case 'group':
          currentGroup = item.message;
          console.log(chalk.blue.bold(`\n📁 ${item.message}`));
          break;
        case 'success':
          console.log(chalk.green(`  ✅ ${item.message}`));
          break;
        case 'error':
          console.log(chalk.red(`  ❌ ${item.message}`));
          break;
        case 'warning':
          console.log(chalk.yellow(`  ⚠️  ${item.message}`));
          break;
        case 'info':
          console.log(chalk.gray(`  ℹ️  ${item.message}`));
          break;
      }
    });

    console.log('\n' + '='.repeat(60));
    console.log(chalk.blue.bold('📊 SUMMARY'));
    console.log('='.repeat(60));

    console.log(chalk.green(`✅ Passed: ${this.validationResults.passed}`));
    console.log(chalk.red(`❌ Failed: ${this.validationResults.failed}`));
    console.log(chalk.yellow(`⚠️  Warnings: ${this.validationResults.warnings}`));

    const totalChecks = this.validationResults.passed + this.validationResults.failed + this.validationResults.warnings;
    const successRate = ((this.validationResults.passed / totalChecks) * 100).toFixed(1);

    console.log(chalk.blue(`📈 Success Rate: ${successRate}%`));

    if (this.validationResults.failed === 0) {
      console.log(chalk.green.bold('\n🎉 VALIDATION PASSED!'));
      console.log(chalk.green('Your NeuroColony developer experience setup is ready for billion-user scale development!'));
    } else {
      console.log(chalk.red.bold('\n❌ VALIDATION FAILED'));
      console.log(chalk.yellow('Please address the errors above before proceeding.'));
    }

    if (this.validationResults.warnings > 0) {
      console.log(chalk.yellow('\n💡 Consider addressing the warnings to enhance your developer experience.'));
      console.log(chalk.gray('Run "npm run setup:dev" to automatically set up missing components.'));
    }

    console.log('\n' + '='.repeat(60));
    console.log(chalk.blue.bold('🚀 NEXT STEPS'));
    console.log('='.repeat(60));

    if (this.validationResults.failed > 0) {
      console.log(chalk.yellow('1. Fix the validation errors listed above'));
      console.log(chalk.yellow('2. Run "npm install" if dependencies are missing'));
      console.log(chalk.yellow('3. Run "npm run setup:dev" for automated setup'));
      console.log(chalk.yellow('4. Re-run this validation: "npm run validate:setup"'));
    } else {
      console.log(chalk.green('1. Start development: "npm run dev"'));
      console.log(chalk.green('2. Run tests: "npm test"'));
      console.log(chalk.green('3. Generate docs: "npm run docs:generate"'));
      console.log(chalk.green('4. Profile performance: "npm run profile"'));
      console.log(chalk.green('5. Check the Developer Experience Blueprint: DEVELOPER_EXPERIENCE_BLUEPRINT.md'));
    }

    console.log('');
  }
}

// Run validation if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new SetupValidator();
  validator.validateSetup()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(chalk.red('Validation failed with error:'), error);
      process.exit(1);
    });
}

export { SetupValidator };