#!/usr/bin/env node

/**
 * NeuroColony Developer Environment Setup Script
 * 
 * This script sets up a complete world-class development environment
 * with all the tools and configurations needed for billion-user scale development.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import chalk from 'chalk';
import inquirer from 'inquirer';

class DevEnvironmentSetup {
  constructor() {
    this.config = {
      projectRoot: process.cwd(),
      setupSteps: [
        'validateEnvironment',
        'setupCleanArchitecture',
        'setupTestingFramework',
        'setupCICD',
        'setupDocumentation',
        'setupMonitoring',
        'setupDeveloperTools',
        'validateSetup'
      ]
    };
  }

  async run() {
    console.log(chalk.blue.bold('🚀 NeuroColony Developer Experience Setup'));
    console.log(chalk.gray('Setting up world-class development environment...\n'));

    try {
      // Get user preferences
      const preferences = await this.getUserPreferences();
      
      // Run setup steps
      for (const step of this.config.setupSteps) {
        await this.executeStep(step, preferences);
      }

      console.log(chalk.green.bold('\n✅ Developer environment setup complete!'));
      console.log(chalk.yellow('🎉 You\'re ready to build at billion-user scale!\n'));
      
      await this.displayNextSteps();

    } catch (error) {
      console.error(chalk.red.bold('❌ Setup failed:'), error.message);
      process.exit(1);
    }
  }

  async getUserPreferences() {
    const questions = [
      {
        type: 'checkbox',
        name: 'features',
        message: 'Select features to set up:',
        choices: [
          { name: 'Clean Architecture (Domain-driven design)', value: 'cleanArch', checked: true },
          { name: 'Advanced Testing (Unit, Integration, Contract, Chaos)', value: 'testing', checked: true },
          { name: 'CI/CD Pipeline (GitOps, Canary, Feature Flags)', value: 'cicd', checked: true },
          { name: 'Auto-generated Documentation', value: 'docs', checked: true },
          { name: 'Advanced Monitoring & Debugging', value: 'monitoring', checked: true },
          { name: 'Developer Dashboard', value: 'dashboard', checked: true },
          { name: 'Performance Profiling', value: 'profiling', checked: true },
          { name: 'Security Scanning', value: 'security', checked: true }
        ]
      },
      {
        type: 'list',
        name: 'environment',
        message: 'Primary development environment:',
        choices: ['local', 'docker', 'kubernetes'],
        default: 'docker'
      },
      {
        type: 'list',
        name: 'cicdProvider',
        message: 'CI/CD Provider:',
        choices: ['github-actions', 'gitlab-ci', 'jenkins', 'azure-devops'],
        default: 'github-actions'
      },
      {
        type: 'confirm',
        name: 'setupSampleData',
        message: 'Set up sample data for development?',
        default: true
      }
    ];

    return await inquirer.prompt(questions);
  }

  async executeStep(stepName, preferences) {
    const stepMethod = this[stepName];
    if (!stepMethod) {
      throw new Error(`Unknown setup step: ${stepName}`);
    }

    console.log(chalk.blue(`📋 ${this.getStepDescription(stepName)}`));
    
    try {
      await stepMethod.call(this, preferences);
      console.log(chalk.green(`✅ ${stepName} completed\n`));
    } catch (error) {
      console.error(chalk.red(`❌ ${stepName} failed:`), error.message);
      throw error;
    }
  }

  getStepDescription(stepName) {
    const descriptions = {
      validateEnvironment: 'Validating system requirements and dependencies',
      setupCleanArchitecture: 'Setting up hexagonal architecture structure',
      setupTestingFramework: 'Configuring comprehensive testing framework',
      setupCICD: 'Setting up CI/CD pipeline and deployment automation',
      setupDocumentation: 'Configuring auto-generated documentation system',
      setupMonitoring: 'Setting up advanced monitoring and observability',
      setupDeveloperTools: 'Installing and configuring developer tools',
      validateSetup: 'Validating complete setup and running health checks'
    };
    return descriptions[stepName] || stepName;
  }

  async validateEnvironment(preferences) {
    // Check Node.js version
    const nodeVersion = process.version;
    const minNodeVersion = '18.0.0';
    
    if (!this.isVersionCompatible(nodeVersion, minNodeVersion)) {
      throw new Error(`Node.js ${minNodeVersion} or higher required. Current: ${nodeVersion}`);
    }

    // Check required tools
    const requiredTools = ['git', 'docker', 'npm'];
    const optionalTools = ['kubectl', 'helm', 'terraform'];

    for (const tool of requiredTools) {
      try {
        execSync(`${tool} --version`, { stdio: 'ignore' });
      } catch (error) {
        throw new Error(`Required tool not found: ${tool}`);
      }
    }

    // Check optional tools
    const availableOptionalTools = [];
    for (const tool of optionalTools) {
      try {
        execSync(`${tool} --version`, { stdio: 'ignore' });
        availableOptionalTools.push(tool);
      } catch (error) {
        console.log(chalk.yellow(`⚠️  Optional tool not found: ${tool}`));
      }
    }

    console.log(chalk.green(`✓ Node.js ${nodeVersion}`));
    console.log(chalk.green(`✓ Required tools: ${requiredTools.join(', ')}`));
    if (availableOptionalTools.length > 0) {
      console.log(chalk.green(`✓ Optional tools: ${availableOptionalTools.join(', ')}`));
    }
  }

  async setupCleanArchitecture(preferences) {
    if (!preferences.features.includes('cleanArch')) return;

    const directories = [
      'src/core/domain/entities',
      'src/core/domain/events',
      'src/core/domain/services',
      'src/core/usecases',
      'src/core/ports/repositories',
      'src/core/ports/services',
      'src/infrastructure/repositories',
      'src/infrastructure/services',
      'src/infrastructure/adapters',
      'src/infrastructure/config',
      'src/infrastructure/di',
      'src/presentation/routes',
      'src/presentation/middleware',
      'src/presentation/controllers'
    ];

    // Create directory structure
    for (const dir of directories) {
      await fs.mkdir(path.join(this.config.projectRoot, dir), { recursive: true });
    }

    // Create base entity class
    await this.createFile('src/core/domain/entities/BaseEntity.js', this.getBaseEntityTemplate());
    
    // Create repository interface
    await this.createFile('src/core/ports/repositories/IRepository.js', this.getRepositoryInterfaceTemplate());
    
    // Create DI container
    await this.createFile('src/infrastructure/di/Container.js', this.getDIContainerTemplate());
    
    // Create sample entity
    await this.createFile('src/core/domain/entities/EmailSequence.js', this.getEmailSequenceEntityTemplate());

    console.log(chalk.green('✓ Clean architecture structure created'));
  }

  async setupTestingFramework(preferences) {
    if (!preferences.features.includes('testing')) return;

    // Install testing dependencies
    const testDependencies = [
      'vitest',
      'supertest',
      '@pact-foundation/pact',
      'fast-check',
      'testcontainers',
      '@testing-library/jest-dom',
      'msw'
    ];

    console.log(chalk.blue('Installing testing dependencies...'));
    execSync(`npm install --save-dev ${testDependencies.join(' ')}`, { stdio: 'inherit' });

    // Create test directories
    const testDirectories = [
      'tests/unit/core/entities',
      'tests/unit/core/usecases',
      'tests/integration/api',
      'tests/integration/database',
      'tests/contract',
      'tests/e2e',
      'tests/performance',
      'tests/chaos',
      'tests/helpers',
      'tests/fixtures'
    ];

    for (const dir of testDirectories) {
      await fs.mkdir(path.join(this.config.projectRoot, dir), { recursive: true });
    }

    // Create test configuration
    await this.createFile('vitest.config.js', this.getVitestConfigTemplate());
    await this.createFile('tests/helpers/testSetup.js', this.getTestSetupTemplate());
    await this.createFile('tests/unit/sample.test.js', this.getSampleUnitTestTemplate());

    // Update package.json scripts
    await this.updatePackageJsonScripts({
      'test': 'vitest',
      'test:unit': 'vitest run tests/unit',
      'test:integration': 'vitest run tests/integration',
      'test:e2e': 'vitest run tests/e2e',
      'test:contract': 'npm run pact:test && npm run pact:publish',
      'test:chaos': 'node tests/chaos/run-chaos-tests.js',
      'test:coverage': 'vitest run --coverage',
      'pact:test': 'vitest run tests/contract',
      'pact:publish': 'pact-broker publish pacts --broker-base-url=$PACT_BROKER_URL'
    });

    console.log(chalk.green('✓ Testing framework configured'));
  }

  async setupCICD(preferences) {
    if (!preferences.features.includes('cicd')) return;

    const cicdTemplates = {
      'github-actions': '.github/workflows/ci-cd.yml',
      'gitlab-ci': '.gitlab-ci.yml',
      'jenkins': 'Jenkinsfile',
      'azure-devops': 'azure-pipelines.yml'
    };

    const configFile = cicdTemplates[preferences.cicdProvider];
    if (configFile) {
      await fs.mkdir(path.dirname(path.join(this.config.projectRoot, configFile)), { recursive: true });
      await this.createFile(configFile, this.getCICDTemplate(preferences.cicdProvider));
    }

    // Create deployment configurations
    await fs.mkdir('deployment/helm/sequenceai', { recursive: true });
    await this.createFile('deployment/helm/sequenceai/Chart.yaml', this.getHelmChartTemplate());
    await this.createFile('deployment/helm/sequenceai/values.yaml', this.getHelmValuesTemplate());

    // Create Docker configuration
    await this.createFile('Dockerfile.dev', this.getDevDockerfileTemplate());
    await this.createFile('docker-compose.dev.yml', this.getDevDockerComposeTemplate());

    console.log(chalk.green('✓ CI/CD pipeline configured'));
  }

  async setupDocumentation(preferences) {
    if (!preferences.features.includes('docs')) return;

    // Install documentation dependencies
    const docDependencies = [
      'swagger-jsdoc',
      'swagger-ui-express',
      '@redocly/openapi-cli',
      'typedoc',
      '@microsoft/api-extractor'
    ];

    execSync(`npm install --save-dev ${docDependencies.join(' ')}`, { stdio: 'inherit' });

    // Create documentation directories
    const docDirectories = [
      'docs/api',
      'docs/architecture',
      'docs/runbooks',
      'docs/adr',
      'docs/guides'
    ];

    for (const dir of docDirectories) {
      await fs.mkdir(path.join(this.config.projectRoot, dir), { recursive: true });
    }

    // Create documentation generation script
    await this.createFile('scripts/generate-docs.js', this.getDocGenerationScript());

    // Create sample ADR
    await this.createFile('docs/adr/001-clean-architecture.md', this.getSampleADRTemplate());

    // Update package.json scripts
    await this.updatePackageJsonScripts({
      'docs:generate': 'node scripts/generate-docs.js',
      'docs:serve': 'npx swagger-ui-serve docs/api/openapi.json',
      'docs:build': 'npm run docs:generate && typedoc'
    });

    console.log(chalk.green('✓ Documentation system configured'));
  }

  async setupMonitoring(preferences) {
    if (!preferences.features.includes('monitoring')) return;

    // Install monitoring dependencies
    const monitoringDependencies = [
      '@opentelemetry/sdk-node',
      '@opentelemetry/auto-instrumentations-node',
      '@opentelemetry/semantic-conventions',
      'prom-client',
      'sentry',
      'winston',
      'winston-daily-rotate-file'
    ];

    execSync(`npm install ${monitoringDependencies.join(' ')}`, { stdio: 'inherit' });

    // Create monitoring directories
    await fs.mkdir('src/infrastructure/monitoring', { recursive: true });

    // Create monitoring configuration
    await this.createFile('src/infrastructure/monitoring/telemetry.js', this.getTelemetryConfigTemplate());
    await this.createFile('src/infrastructure/monitoring/metrics.js', this.getMetricsConfigTemplate());
    await this.createFile('src/infrastructure/monitoring/logger.js', this.getLoggerConfigTemplate());

    console.log(chalk.green('✓ Monitoring and observability configured'));
  }

  async setupDeveloperTools(preferences) {
    // Install development dependencies
    const devDependencies = [
      'eslint',
      '@typescript-eslint/parser',
      '@typescript-eslint/eslint-plugin',
      'prettier',
      'husky',
      'lint-staged',
      'nodemon',
      'concurrently'
    ];

    execSync(`npm install --save-dev ${devDependencies.join(' ')}`, { stdio: 'inherit' });

    // Create configuration files
    await this.createFile('.eslintrc.js', this.getESLintConfigTemplate());
    await this.createFile('.prettierrc', this.getPrettierConfigTemplate());
    await this.createFile('.gitignore', this.getGitignoreTemplate());

    // Setup Git hooks
    execSync('npx husky install', { stdio: 'inherit' });
    execSync('npx husky add .husky/pre-commit "npx lint-staged"', { stdio: 'inherit' });
    execSync('npx husky add .husky/commit-msg "npx commitizen --hook"', { stdio: 'inherit' });

    // Create lint-staged configuration
    await this.updatePackageJson({
      'lint-staged': {
        '*.{js,jsx,ts,tsx}': ['eslint --fix', 'prettier --write'],
        '*.{json,md}': ['prettier --write']
      }
    });

    console.log(chalk.green('✓ Developer tools configured'));
  }

  async validateSetup(preferences) {
    console.log(chalk.blue('Running validation checks...'));

    // Check if all files were created
    const requiredFiles = [
      'src/core/domain/entities/BaseEntity.js',
      'src/infrastructure/di/Container.js',
      'vitest.config.js',
      '.eslintrc.js',
      '.prettierrc'
    ];

    for (const file of requiredFiles) {
      try {
        await fs.access(path.join(this.config.projectRoot, file));
        console.log(chalk.green(`✓ ${file}`));
      } catch (error) {
        console.log(chalk.red(`✗ ${file} missing`));
      }
    }

    // Run test to ensure everything works
    try {
      console.log(chalk.blue('Running test suite...'));
      execSync('npm test -- --run', { stdio: 'inherit' });
      console.log(chalk.green('✓ Test suite passes'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Some tests failed, but setup is complete'));
    }

    // Check linting
    try {
      console.log(chalk.blue('Running linter...'));
      execSync('npm run lint', { stdio: 'inherit' });
      console.log(chalk.green('✓ Linting passes'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Linting issues found, run npm run lint:fix'));
    }
  }

  async displayNextSteps() {
    console.log(chalk.blue.bold('🎯 Next Steps:\n'));
    
    const nextSteps = [
      '1. Review the generated clean architecture in src/',
      '2. Run "npm test" to verify your testing setup',
      '3. Check out docs/adr/ for architecture decisions',
      '4. Configure your environment variables in .env',
      '5. Start development server with "npm run dev"',
      '6. Access API documentation at /docs when server is running',
      '7. Review CI/CD pipeline configuration',
      '8. Set up monitoring dashboards and alerts'
    ];

    nextSteps.forEach(step => {
      console.log(chalk.gray(step));
    });

    console.log(chalk.blue.bold('\n📚 Useful Commands:\n'));
    
    const commands = [
      'npm test              # Run all tests',
      'npm run test:unit     # Run unit tests only',
      'npm run docs:generate # Generate API documentation',
      'npm run lint          # Check code quality',
      'npm run dev           # Start development server',
      'npm run build         # Build for production'
    ];

    commands.forEach(command => {
      console.log(chalk.gray(command));
    });
  }

  // Helper methods
  isVersionCompatible(current, required) {
    const parseVersion = (version) => version.replace('v', '').split('.').map(Number);
    const currentParts = parseVersion(current);
    const requiredParts = parseVersion(required);

    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const requiredPart = requiredParts[i] || 0;

      if (currentPart > requiredPart) return true;
      if (currentPart < requiredPart) return false;
    }

    return true;
  }

  async createFile(filePath, content) {
    const fullPath = path.join(this.config.projectRoot, filePath);
    await fs.mkdir(path.dirname(fullPath), { recursive: true });
    await fs.writeFile(fullPath, content);
  }

  async updatePackageJsonScripts(scripts) {
    const packageJsonPath = path.join(this.config.projectRoot, 'package.json');
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    packageJson.scripts = {
      ...packageJson.scripts,
      ...scripts
    };

    await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
  }

  async updatePackageJson(updates) {
    const packageJsonPath = path.join(this.config.projectRoot, 'package.json');
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    Object.assign(packageJson, updates);

    await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
  }

  // Template methods
  getBaseEntityTemplate() {
    return `export class BaseEntity {
  constructor(id) {
    this.id = id;
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  updateTimestamp() {
    this.updatedAt = new Date();
  }

  equals(other) {
    return other instanceof this.constructor && this.id === other.id;
  }

  toJSON() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
`;
  }

  getRepositoryInterfaceTemplate() {
    return `export class IRepository {
  async save(entity) {
    throw new Error('Method must be implemented');
  }

  async findById(id) {
    throw new Error('Method must be implemented');
  }

  async findAll(criteria = {}) {
    throw new Error('Method must be implemented');
  }

  async delete(id) {
    throw new Error('Method must be implemented');
  }

  async exists(id) {
    throw new Error('Method must be implemented');
  }
}
`;
  }

  getDIContainerTemplate() {
    return `export class DIContainer {
  constructor() {
    this.bindings = new Map();
    this.instances = new Map();
  }

  bind(name, factory) {
    this.bindings.set(name, factory);
    return this;
  }

  singleton(name, factory) {
    return this.bind(name, () => {
      if (!this.instances.has(name)) {
        this.instances.set(name, factory());
      }
      return this.instances.get(name);
    });
  }

  resolve(name) {
    const factory = this.bindings.get(name);
    if (!factory) {
      throw new Error(\`No binding found for: \${name}\`);
    }
    return factory();
  }

  resolveAll(names) {
    return names.map(name => this.resolve(name));
  }
}
`;
  }

  getEmailSequenceEntityTemplate() {
    return `import { BaseEntity } from './BaseEntity.js';

export class EmailSequence extends BaseEntity {
  constructor(id, businessInfo, emails, metadata = {}) {
    super(id);
    this.businessInfo = businessInfo;
    this.emails = emails;
    this.metadata = metadata;
    this.validate();
  }

  validate() {
    if (!this.businessInfo || !this.emails || this.emails.length === 0) {
      throw new Error('EmailSequence requires business info and at least one email');
    }

    this.emails.forEach((email, index) => {
      if (!email.subject || !email.content) {
        throw new Error(\`Email at index \${index} must have subject and content\`);
      }
    });
  }

  addEmail(email) {
    if (!email.subject || !email.content) {
      throw new Error('Email must have subject and content');
    }
    
    this.emails.push(email);
    this.updateTimestamp();
  }

  updateMetadata(metadata) {
    this.metadata = { ...this.metadata, ...metadata };
    this.updateTimestamp();
  }

  toJSON() {
    return {
      ...super.toJSON(),
      businessInfo: this.businessInfo,
      emails: this.emails,
      metadata: this.metadata
    };
  }
}
`;
  }

  getVitestConfigTemplate() {
    return `import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/helpers/testSetup.js'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        'coverage/',
        '**/*.d.ts',
        '**/*.config.js'
      ]
    },
    testTimeout: 10000,
    hookTimeout: 10000
  }
});
`;
  }

  getTestSetupTemplate() {
    return `import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// Global test setup
beforeAll(async () => {
  // Setup test database, external services, etc.
  console.log('🧪 Setting up test environment...');
});

afterAll(async () => {
  // Cleanup test environment
  console.log('🧹 Cleaning up test environment...');
});

beforeEach(async () => {
  // Reset state before each test
});

afterEach(async () => {
  // Cleanup after each test
});

// Global test utilities
global.testHelpers = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    subscription: { type: 'pro' }
  }),
  
  createMockBusinessInfo: () => ({
    name: 'Test Business',
    industry: 'technology',
    description: 'A test business for unit testing'
  }),

  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};
`;
  }

  getSampleUnitTestTemplate() {
    return `import { describe, it, expect } from 'vitest';
import { EmailSequence } from '../../src/core/domain/entities/EmailSequence.js';

describe('EmailSequence Entity', () => {
  const validBusinessInfo = testHelpers.createMockBusinessInfo();
  const validEmails = [
    { subject: 'Welcome', content: 'Welcome to our service!' },
    { subject: 'Follow up', content: 'How are you finding our service?' }
  ];

  describe('constructor', () => {
    it('should create a valid email sequence', () => {
      const sequence = new EmailSequence('test-id', validBusinessInfo, validEmails);
      
      expect(sequence.id).toBe('test-id');
      expect(sequence.businessInfo).toEqual(validBusinessInfo);
      expect(sequence.emails).toHaveLength(2);
      expect(sequence.createdAt).toBeInstanceOf(Date);
    });

    it('should throw error for invalid data', () => {
      expect(() => {
        new EmailSequence('test-id', null, validEmails);
      }).toThrow('EmailSequence requires business info and at least one email');
    });
  });

  describe('addEmail', () => {
    it('should add email successfully', () => {
      const sequence = new EmailSequence('test-id', validBusinessInfo, validEmails);
      const newEmail = { subject: 'New Email', content: 'New content' };
      
      sequence.addEmail(newEmail);
      
      expect(sequence.emails).toHaveLength(3);
      expect(sequence.emails[2]).toEqual(newEmail);
    });

    it('should throw error for invalid email', () => {
      const sequence = new EmailSequence('test-id', validBusinessInfo, validEmails);
      
      expect(() => {
        sequence.addEmail({ subject: 'No content' });
      }).toThrow('Email must have subject and content');
    });
  });
});
`;
  }

  getCICDTemplate(provider) {
    if (provider === 'github-actions') {
      return `name: NeuroColony CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: sequenceai/backend

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: test
          MONGO_INITDB_ROOT_PASSWORD: test
        ports:
          - 27017:27017
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: \${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit

      - name: Run integration tests
        run: npm run test:integration
        env:
          MONGODB_URI: ****************************************
          REDIS_URL: redis://localhost:6379

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          token: \${{ secrets.CODECOV_TOKEN }}

  build:
    name: Build & Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: \${{ env.REGISTRY }}
          username: \${{ github.actor }}
          password: \${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}:\${{ github.sha }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your deployment commands here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your deployment commands here
`;
    }
    
    return '# CI/CD configuration for other providers coming soon...';
  }

  getHelmChartTemplate() {
    return `apiVersion: v2
name: sequenceai
description: NeuroColony Backend API Helm Chart
type: application
version: 1.0.0
appVersion: "1.0.0"
`;
  }

  getHelmValuesTemplate() {
    return `replicaCount: 3

image:
  repository: sequenceai/backend
  pullPolicy: IfNotPresent
  tag: ""

service:
  type: ClusterIP
  port: 80
  targetPort: 5000

ingress:
  enabled: true
  className: ""
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: api.sequenceai.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: sequenceai-tls
      hosts:
        - api.sequenceai.com

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

env:
  NODE_ENV: production
  PORT: 5000

mongodb:
  enabled: true
  architecture: replicaset
  replicaCount: 3

redis:
  enabled: true
  architecture: replication
  master:
    persistence:
      enabled: true
`;
  }

  getDevDockerfileTemplate() {
    return `FROM node:18-alpine

WORKDIR /app

# Install dependencies first for better caching
COPY package*.json ./
RUN npm ci --only=development

# Copy source code
COPY . .

# Expose port
EXPOSE 5000

# Development command
CMD ["npm", "run", "dev"]
`;
  }

  getDevDockerComposeTemplate() {
    return `version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/sequenceai_dev
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  mongodb_data:
  redis_data:
  grafana_data:
`;
  }

  getDocGenerationScript() {
    return `import swaggerJsdoc from 'swagger-jsdoc';
import fs from 'fs/promises';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'NeuroColony API',
      version: '1.0.0',
      description: 'AI-powered email sequence generation platform'
    }
  },
  apis: ['./src/presentation/routes/*.js']
};

async function generateDocs() {
  try {
    const specs = swaggerJsdoc(options);
    
    await fs.writeFile(
      './docs/api/openapi.json',
      JSON.stringify(specs, null, 2)
    );
    
    console.log('✅ API documentation generated successfully');
  } catch (error) {
    console.error('❌ Failed to generate documentation:', error);
    process.exit(1);
  }
}

generateDocs();
`;
  }

  getSampleADRTemplate() {
    return `# ADR-001: Adopt Clean Architecture Pattern

## Status
Accepted

## Context
NeuroColony is scaling to support billion users and 100+ developers. Our current architecture lacks clear separation of concerns, making it difficult to:
- Test business logic in isolation
- Swap external dependencies
- Maintain code quality at scale
- Onboard new developers quickly

## Decision
We will adopt the Clean Architecture (Hexagonal Architecture) pattern.

### Benefits:
- **Testability**: Business logic can be tested without external dependencies
- **Flexibility**: Easy to swap implementations
- **Maintainability**: Clear separation of concerns
- **Team Productivity**: Developers can work on different layers independently

### Trade-offs:
- **Initial Complexity**: More files and abstractions
- **Learning Curve**: Team needs to understand the pattern

## Consequences
- Improved testability and maintainability
- Better separation of concerns
- Easier to onboard new developers
- Reduced coupling between layers

---

**Author**: Engineering Team  
**Date**: ${new Date().toISOString().split('T')[0]}
`;
  }

  getTelemetryConfigTemplate() {
    return `import { NodeSDK } from '@opentelemetry/sdk-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';

export function initializeTelemetry() {
  const sdk = new NodeSDK({
    resource: new Resource({
      [SemanticResourceAttributes.SERVICE_NAME]: 'sequenceai-api',
      [SemanticResourceAttributes.SERVICE_VERSION]: process.env.APP_VERSION || '1.0.0',
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV || 'development'
    }),
    instrumentations: [getNodeAutoInstrumentations()]
  });

  sdk.start();
  console.log('🔍 Telemetry initialized successfully');
}
`;
  }

  getMetricsConfigTemplate() {
    return `import client from 'prom-client';

// Create a Registry to register the metrics
const register = new client.Registry();

// Add default metrics
client.collectDefaultMetrics({ register });

// Custom metrics
export const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

export const httpRequestsTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

export const aiGenerationsTotal = new client.Counter({
  name: 'ai_generations_total',
  help: 'Total number of AI generations',
  labelNames: ['model', 'status']
});

export const aiGenerationDuration = new client.Histogram({
  name: 'ai_generation_duration_seconds',
  help: 'Duration of AI generations in seconds',
  labelNames: ['model'],
  buckets: [1, 2, 5, 10, 30]
});

// Register custom metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestsTotal);
register.registerMetric(aiGenerationsTotal);
register.registerMetric(aiGenerationDuration);

export { register };
`;
  }

  getLoggerConfigTemplate() {
    return `import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'sequenceai-api',
    version: process.env.APP_VERSION,
    environment: process.env.NODE_ENV
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new DailyRotateFile({
      filename: 'logs/application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d'
    }),
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: '20m',
      maxFiles: '30d'
    })
  ]
});
`;
  }

  getESLintConfigTemplate() {
    return `module.exports = {
  env: {
    es2022: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'template-curly-spacing': 'error',
    'arrow-spacing': 'error',
    'comma-dangle': ['error', 'never'],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always']
  },
};
`;
  }

  getPrettierConfigTemplate() {
    return `{
  "semi": true,
  "trailingComma": "none",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
`;
  }

  getGitignoreTemplate() {
    return `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# macOS
.DS_Store

# Windows
Thumbs.db

# VS Code
.vscode/

# JetBrains IDEs
.idea/

# Temporary files
tmp/
temp/

# Build artifacts
build/
dist/

# Database files
*.db
*.sqlite

# Test artifacts
test-results/
test-artifacts/

# Docker
.dockerignore

# Monitoring
prometheus/
grafana/

# Documentation build
docs/build/
`;
  }
}

// Run the setup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new DevEnvironmentSetup();
  setup.run().catch(console.error);
}

export { DevEnvironmentSetup };