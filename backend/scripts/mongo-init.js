// MongoDB initialization script for NeuroColony development
db = db.getSiblingDB('sequenceai');

// Create application user
db.createUser({
  user: 'sequenceai',
  pwd: 'sequenceai123',
  roles: [
    {
      role: 'readWrite',
      db: 'sequenceai'
    }
  ]
});

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.emailsequences.createIndex({ "userId": 1 });
db.emailsequences.createIndex({ "createdAt": -1 });
db.emailsequences.createIndex({ "industry": 1 });

// Insert initial demo data
db.emailsequences.insertMany([
  {
    title: "Welcome Series Demo",
    industry: "SaaS",
    sequences: [
      {
        subject: "Welcome to NeuroColony! 🚀",
        content: "Welcome to the future of AI-powered email marketing...",
        day: 1
      },
      {
        subject: "Your first AI-generated sequence",
        content: "Let's walk through creating your first sequence...",
        day: 3
      }
    ],
    createdAt: new Date(),
    isDemo: true
  }
]);

print("✅ MongoDB initialized for NeuroColony development");