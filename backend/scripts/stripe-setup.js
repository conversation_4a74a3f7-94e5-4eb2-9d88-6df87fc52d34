#!/usr/bin/env node

/**
 * Stripe Setup Script for NeuroColony
 * Creates products and prices in Stripe
 * Run: node scripts/stripe-setup.js
 */

import Stripe from 'stripe'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import fs from 'fs/promises'

// Load environment variables
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
dotenv.config({ path: join(__dirname, '../.env') })

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

// Product definitions
const PRODUCTS = {
  neurocolony: {
    name: 'NeuroColony Platform',
    description: 'AI-powered email marketing and agent automation platform',
    metadata: {
      platform: 'neurocolony',
      type: 'subscription'
    }
  }
}

// Price definitions
const PRICES = {
  starter: {
    productKey: 'neurocolony',
    unit_amount: 2900, // $29.00
    currency: 'usd',
    recurring: { interval: 'month' },
    nickname: 'Starter Colony',
    metadata: {
      plan: 'starter',
      sequences: '50',
      agents: '5',
      compute_hours: '50'
    }
  },
  professional: {
    productKey: 'neurocolony',
    unit_amount: 8900, // $89.00
    currency: 'usd',
    recurring: { interval: 'month' },
    nickname: 'Professional Colony',
    metadata: {
      plan: 'professional',
      sequences: '200',
      agents: '25',
      compute_hours: '200',
      premium_ai: 'true'
    }
  },
  enterprise: {
    productKey: 'neurocolony',
    unit_amount: 28900, // $289.00
    currency: 'usd',
    recurring: { interval: 'month' },
    nickname: 'Enterprise Colony',
    metadata: {
      plan: 'enterprise',
      sequences: 'unlimited',
      agents: 'unlimited',
      compute_hours: 'unlimited',
      white_label: 'true',
      dedicated_support: 'true'
    }
  }
}

// Usage-based pricing for overages
const USAGE_PRICES = {
  sequence_overage: {
    productKey: 'neurocolony',
    currency: 'usd',
    recurring: { 
      interval: 'month',
      usage_type: 'metered'
    },
    unit_amount: 200, // $2.00 per sequence
    nickname: 'Sequence Overage',
    metadata: {
      type: 'overage',
      resource: 'sequence'
    }
  },
  agent_execution_overage: {
    productKey: 'neurocolony',
    currency: 'usd',
    recurring: { 
      interval: 'month',
      usage_type: 'metered'
    },
    unit_amount: 300, // $3.00 per agent execution
    nickname: 'Agent Execution Overage',
    metadata: {
      type: 'overage',
      resource: 'agent_execution'
    }
  }
}

async function createOrUpdateProduct(productKey, productData) {
  try {
    // Search for existing product
    const existingProducts = await stripe.products.search({
      query: `metadata['platform']:'neurocolony' AND active:'true'`
    })
    
    let product = existingProducts.data.find(p => 
      p.metadata.platform === 'neurocolony' && p.name === productData.name
    )
    
    if (product) {
      console.log(`✓ Product already exists: ${product.name} (${product.id})`)
    } else {
      // Create new product
      product = await stripe.products.create(productData)
      console.log(`✓ Created product: ${product.name} (${product.id})`)
    }
    
    return product
  } catch (error) {
    console.error(`✗ Error with product ${productKey}:`, error.message)
    throw error
  }
}

async function createPrice(product, priceKey, priceData) {
  try {
    // Check if price already exists
    const existingPrices = await stripe.prices.list({
      product: product.id,
      active: true,
      limit: 100
    })
    
    const existingPrice = existingPrices.data.find(p => 
      p.nickname === priceData.nickname &&
      p.unit_amount === priceData.unit_amount
    )
    
    if (existingPrice) {
      console.log(`  ✓ Price already exists: ${existingPrice.nickname} (${existingPrice.id})`)
      return existingPrice
    }
    
    // Create new price
    const price = await stripe.prices.create({
      product: product.id,
      ...priceData
    })
    
    console.log(`  ✓ Created price: ${price.nickname} (${price.id})`)
    return price
  } catch (error) {
    console.error(`  ✗ Error creating price ${priceKey}:`, error.message)
    throw error
  }
}

async function setupStripe() {
  console.log('🚀 Starting Stripe setup for NeuroColony...\n')
  
  // Verify API key
  try {
    const account = await stripe.accounts.retrieve()
    console.log(`✓ Connected to Stripe account: ${account.settings.dashboard.display_name || account.id}`)
    console.log(`  Mode: ${account.id.startsWith('acct_') ? 'LIVE' : 'TEST'}\n`)
  } catch (error) {
    console.error('✗ Failed to connect to Stripe:', error.message)
    console.error('  Please check your STRIPE_SECRET_KEY in .env')
    process.exit(1)
  }
  
  const products = {}
  const prices = {}
  
  // Create products
  console.log('📦 Creating products...')
  for (const [key, data] of Object.entries(PRODUCTS)) {
    products[key] = await createOrUpdateProduct(key, data)
  }
  
  // Create subscription prices
  console.log('\n💰 Creating subscription prices...')
  for (const [key, data] of Object.entries(PRICES)) {
    const { productKey, ...priceData } = data
    prices[key] = await createPrice(products[productKey], key, priceData)
  }
  
  // Create usage-based prices
  console.log('\n📊 Creating usage-based prices...')
  for (const [key, data] of Object.entries(USAGE_PRICES)) {
    const { productKey, ...priceData } = data
    prices[key] = await createPrice(products[productKey], key, priceData)
  }
  
  // Generate environment variables
  console.log('\n📝 Environment variables to add to your .env file:\n')
  console.log('# Stripe Price IDs (generated by stripe-setup.js)')
  console.log(`STRIPE_STARTER_PRICE_ID=${prices.starter.id}`)
  console.log(`STRIPE_PROFESSIONAL_PRICE_ID=${prices.professional.id}`)
  console.log(`STRIPE_ENTERPRISE_PRICE_ID=${prices.enterprise.id}`)
  console.log(`STRIPE_SEQUENCE_OVERAGE_PRICE_ID=${prices.sequence_overage.id}`)
  console.log(`STRIPE_AGENT_OVERAGE_PRICE_ID=${prices.agent_execution_overage.id}`)
  
  // Update .env.example
  try {
    const envExamplePath = join(__dirname, '../.env.example')
    let envContent = await fs.readFile(envExamplePath, 'utf-8')
    
    // Add price IDs if not present
    if (!envContent.includes('STRIPE_STARTER_PRICE_ID')) {
      envContent += `\n# Stripe Price IDs\nSTRIPE_STARTER_PRICE_ID=${prices.starter.id}\nSTRIPE_PROFESSIONAL_PRICE_ID=${prices.professional.id}\nSTRIPE_ENTERPRISE_PRICE_ID=${prices.enterprise.id}\nSTRIPE_SEQUENCE_OVERAGE_PRICE_ID=${prices.sequence_overage.id}\nSTRIPE_AGENT_OVERAGE_PRICE_ID=${prices.agent_execution_overage.id}\n`
      await fs.writeFile(envExamplePath, envContent)
      console.log('\n✓ Updated .env.example with price IDs')
    }
  } catch (error) {
    console.log('\n⚠️  Could not update .env.example:', error.message)
  }
  
  console.log('\n✅ Stripe setup complete!')
  console.log('\n📌 Next steps:')
  console.log('1. Copy the price IDs above to your .env file')
  console.log('2. Set up webhook endpoint at: https://your-domain.com/api/payments/webhook')
  console.log('3. Copy the webhook signing secret to STRIPE_WEBHOOK_SECRET in .env')
  console.log('\n🔗 Stripe Dashboard: https://dashboard.stripe.com/products')
}

// Run setup
setupStripe().catch(console.error)