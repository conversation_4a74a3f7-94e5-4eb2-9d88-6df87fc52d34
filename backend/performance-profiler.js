import axios from 'axios';
import { performance } from 'perf_hooks';

console.log('=== COMPREHENSIVE PERFORMANCE BOTTLENECK ANALYSIS ===\n');

// Test function for AI generation performance
async function testAIPerformance() {
  console.log('🧠 AI GENERATION PERFORMANCE ANALYSIS:');
  
  const tests = [
    {
      name: 'Quick Generation (100 chars)',
      prompt: 'Write a brief product description for productivity software in 100 characters.',
      expected: '< 2000ms'
    },
    {
      name: 'Medium Generation (500 chars)', 
      prompt: 'Create a compelling email subject line and preview for a productivity app targeting entrepreneurs. Include psychological triggers.',
      expected: '< 4000ms'
    },
    {
      name: 'Complex Generation (Email Sequence)',
      prompt: 'Create 3 email marketing sequence for productivity software targeting entrepreneurs: Email 1 (welcome), Email 2 (value), Email 3 (conversion). Include subject and body for each.',
      expected: '< 8000ms'
    }
  ];
  
  for (const test of tests) {
    try {
      const start = performance.now();
      
      const response = await axios.post('http://localhost:11434/api/generate', {
        model: 'llama3.2:3b',
        prompt: test.prompt,
        stream: false,
        options: {
          temperature: 0.7,
          num_predict: test.name.includes('Complex') ? 800 : 
                       test.name.includes('Medium') ? 200 : 100
        }
      }, {
        timeout: 15000
      });
      
      const end = performance.now();
      const duration = end - start;
      const tokensGenerated = response.data.response.split(' ').length;
      const tokensPerSecond = (tokensGenerated / (duration / 1000)).toFixed(1);
      
      console.log(`✅ ${test.name}: ${duration.toFixed(0)}ms (${test.expected})`);  
      console.log(`   📊 Tokens: ${tokensGenerated}, Speed: ${tokensPerSecond} t/s`);
      console.log(`   📝 Response length: ${response.data.response.length} chars`);
      
      // Performance rating
      const expectedMs = parseInt(test.expected.replace(/[^0-9]/g, ''));
      const performance_rating = duration < expectedMs ? '🟢 EXCELLENT' : 
                                duration < expectedMs * 1.5 ? '🟡 ACCEPTABLE' : '🔴 NEEDS OPTIMIZATION';
      console.log(`   ${performance_rating}`);
      console.log();
      
    } catch (error) {
      console.log(`❌ ${test.name} FAILED: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.log('   🔧 Solution: Start Ollama service');
      } else if (error.code === 'TIMEOUT') {
        console.log('   🔧 Solution: Increase timeout or reduce prompt complexity');
      }
      console.log();
    }
  }
}

// Test database performance with realistic queries
async function testDatabasePerformance() {
  console.log('💾 DATABASE PERFORMANCE ANALYSIS:');
  
  try {
    const { default: mongoose } = await import('mongoose');
    
    // Connection test
    const connectStart = performance.now();
    await mongoose.connect('mongodb://127.0.0.1:27018/sequenceai', {
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 10000
    });
    const connectEnd = performance.now();
    console.log(`✅ Database Connection: ${(connectEnd - connectStart).toFixed(2)}ms`);
    
    const db = mongoose.connection.db;
    
    // Test operations that the app would actually perform
    const operations = [
      {
        name: 'User Lookup',
        operation: () => db.collection('users').findOne({ email: '<EMAIL>' }),
        expected: '< 50ms'
      },
      {
        name: 'Sequence Insert',
        operation: () => db.collection('sequences').insertOne({
          userId: 'test123',
          businessInfo: { industry: 'tech' },
          emails: [],
          createdAt: new Date()
        }),
        expected: '< 100ms'
      },
      {
        name: 'Recent Sequences Query',
        operation: () => db.collection('sequences').find({}).limit(10).toArray(),
        expected: '< 200ms'
      }
    ];
    
    for (const op of operations) {
      try {
        const start = performance.now();
        const result = await op.operation();
        const end = performance.now();
        const duration = end - start;
        
        const expectedMs = parseInt(op.expected.replace(/[^0-9]/g, ''));
        const rating = duration < expectedMs ? '🟢 FAST' : 
                      duration < expectedMs * 2 ? '🟡 ACCEPTABLE' : '🔴 SLOW';
        
        console.log(`✅ ${op.name}: ${duration.toFixed(2)}ms (${op.expected}) ${rating}`);
      } catch (err) {
        console.log(`❌ ${op.name} FAILED: ${err.message}`);
      }
    }
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.log(`❌ Database tests failed: ${error.message}`);
    if (error.message.includes('ECONNREFUSED')) {
      console.log('   🔧 Solution: Ensure MongoDB is running on port 27018');
    }
  }
  console.log();
}

// Test cache performance 
async function testCachePerformance() {
  console.log('🚀 CACHE PERFORMANCE ANALYSIS:');
  
  try {
    const { LRUCache } = await import('lru-cache');
    const cache = new LRUCache({ max: 1000 });
    
    // Simulate typical caching patterns
    const tests = [
      {
        name: 'Cache Write Performance',
        operation: () => {
          for (let i = 0; i < 1000; i++) {
            cache.set(`ai_result_${i}`, { 
              emails: Array(5).fill({ subject: 'test', body: 'test content' }),
              metadata: { generated: Date.now() }
            });
          }
        },
        expected: '< 5ms'
      },
      {
        name: 'Cache Read Performance', 
        operation: () => {
          for (let i = 0; i < 1000; i++) {
            cache.get(`ai_result_${i}`);
          }
        },
        expected: '< 2ms'
      },
      {
        name: 'Cache Hit Rate Test',
        operation: () => {
          let hits = 0;
          for (let i = 0; i < 100; i++) {
            if (cache.get(`ai_result_${i}`)) hits++;
          }
          return hits;
        },
        expected: '> 90% hit rate'
      }
    ];
    
    for (const test of tests) {
      const start = performance.now();
      const result = test.operation();
      const end = performance.now();
      const duration = end - start;
      
      if (test.name.includes('Hit Rate')) {
        console.log(`✅ ${test.name}: ${result}% hit rate (Expected: ${test.expected})`);
      } else {
        const expectedMs = parseInt(test.expected.replace(/[^0-9]/g, ''));
        const rating = duration < expectedMs ? '🟢 FAST' : '🟡 ACCEPTABLE';
        console.log(`✅ ${test.name}: ${duration.toFixed(2)}ms (${test.expected}) ${rating}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Cache tests failed: ${error.message}`);
  }
  console.log();
}

// System resource analysis
async function analyzeSystemResources() {
  console.log('📊 SYSTEM RESOURCE ANALYSIS:');
  
  const usage = process.memoryUsage();
  const { cpus, totalmem, freemem, loadavg } = await import('os');
  
  console.log(`💾 Memory Usage:`);
  console.log(`   RSS: ${(usage.rss / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Heap Used: ${(usage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Heap Total: ${(usage.heapTotal / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   External: ${(usage.external / 1024 / 1024).toFixed(2)} MB`);
  
  console.log(`\n🖥️  System Resources:`);
  console.log(`   CPU Cores: ${cpus().length}`);
  console.log(`   Total RAM: ${(totalmem() / 1024 / 1024 / 1024).toFixed(2)} GB`);
  console.log(`   Free RAM: ${(freemem() / 1024 / 1024 / 1024).toFixed(2)} GB`);
  console.log(`   Load Average: ${loadavg().map(x => x.toFixed(2)).join(', ')}`);
  
  // Memory pressure analysis
  const memoryPressure = ((totalmem() - freemem()) / totalmem()) * 100;
  const memoryRating = memoryPressure < 70 ? '🟢 HEALTHY' : 
                      memoryPressure < 85 ? '🟡 MODERATE' : '🔴 HIGH PRESSURE';
  console.log(`   Memory Pressure: ${memoryPressure.toFixed(1)}% ${memoryRating}`);
  console.log();
}

// Run all performance tests
async function runComprehensiveAnalysis() {
  const totalStart = performance.now();
  
  await analyzeSystemResources();
  await testCachePerformance();
  await testDatabasePerformance();
  await testAIPerformance();
  
  const totalEnd = performance.now();
  
  console.log('=== PERFORMANCE SUMMARY ===');
  console.log(`⏱️  Total Analysis Time: ${(totalEnd - totalStart).toFixed(2)}ms`);
  
  // Final recommendations
  console.log('\n🎯 OPTIMIZATION RECOMMENDATIONS:');
  console.log('1. 🧠 AI Performance: Consider model quantization for faster inference');
  console.log('2. 💾 Database: Implement connection pooling and query indexing');
  console.log('3. 🚀 Cache: Current cache performance is excellent - maintain current strategy');
  console.log('4. 📊 Monitoring: Add real-time performance metrics dashboard');
  console.log('5. ⚡ Scaling: Consider horizontal scaling for AI workloads > 100 req/min');
}

runComprehensiveAnalysis().catch(console.error);