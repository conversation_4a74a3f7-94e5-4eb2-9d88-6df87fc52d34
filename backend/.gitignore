# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# macOS
.DS_Store

# Windows
Thumbs.db

# VS Code
.vscode/

# JetBrains IDEs
.idea/

# Temporary files
tmp/
temp/

# Build artifacts
build/
dist/

# Database files
*.db
*.sqlite

# Test artifacts
test-results/
test-artifacts/
performance-reports/

# Docker
.dockerignore

# Monitoring
prometheus/
grafana/

# Documentation build
docs/build/

# Kubernetes secrets
secrets/
*.key
*.crt

# Performance profiling
*.prof
*.trace
flamegraph.svg

# Backup files
*.backup
*.bak

# IDE specific
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db