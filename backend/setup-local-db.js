#!/usr/bin/env node

import { existsSync, mkdirSync } from 'fs'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

console.log('🚀 NeuroColony Local Database Setup')
console.log('=====================================')

// Create data directory
const dataDir = './data'
if (!existsSync(dataDir)) {
  mkdirSync(dataDir, { recursive: true })
  console.log('✅ Created data directory')
}

// Check if MongoDB is available
async function checkMongoDB() {
  try {
    await execAsync('mongod --version')
    console.log('✅ MongoDB found locally')
    return true
  } catch (error) {
    console.log('⚠️ MongoDB not found locally')
    return false
  }
}

// Check if Docker is available
async function checkDocker() {
  try {
    await execAsync('docker --version')
    console.log('✅ Docker found')
    return true
  } catch (error) {
    console.log('⚠️ Docker not available')
    return false
  }
}

// Start MongoDB with Docker
async function startMongoDocker() {
  try {
    console.log('🐳 Starting MongoDB container...')
    await execAsync('docker run --name neurocolony-mongo -d -p 27017:27017 -e MONGO_INITDB_DATABASE=neurocolony mongo:7.0')
    console.log('✅ MongoDB container started on port 27017')
    return true
  } catch (error) {
    if (error.message.includes('already in use')) {
      console.log('✅ MongoDB container already running')
      return true
    }
    console.log('❌ Failed to start MongoDB container:', error.message)
    return false
  }
}

// Main setup function
async function setupDatabase() {
  const hasLocalMongo = await checkMongoDB()
  
  if (hasLocalMongo) {
    console.log('🎯 Using local MongoDB installation')
    console.log('📝 Connection string: mongodb://127.0.0.1:27017/neurocolony')
    return
  }

  const hasDocker = await checkDocker()
  
  if (hasDocker) {
    const started = await startMongoDocker()
    if (started) {
      console.log('🎯 Using Docker MongoDB container')
      console.log('📝 Connection string: mongodb://127.0.0.1:27017/neurocolony')
      return
    }
  }

  console.log('⚠️ No MongoDB available - using in-memory fallback')
  console.log('📝 Note: Data will not persist between restarts')
  console.log('')
  console.log('🔧 To set up persistent storage:')
  console.log('   1. Install MongoDB: https://docs.mongodb.com/manual/installation/')
  console.log('   2. Or install Docker: https://docs.docker.com/get-docker/')
  console.log('   3. Or use MongoDB Atlas: https://www.mongodb.com/atlas')
}

// Test database connection
async function testConnection() {
  try {
    const mongoose = await import('mongoose')
    await mongoose.connect('mongodb://127.0.0.1:27017/neurocolony', {
      serverSelectionTimeoutMS: 3000
    })
    console.log('✅ Database connection successful')
    await mongoose.disconnect()
  } catch (error) {
    console.log('⚠️ Database connection failed - will use fallback mode')
  }
}

// Run setup
setupDatabase()
  .then(() => testConnection())
  .then(() => {
    console.log('')
    console.log('🚀 Database setup complete!')
    console.log('💡 Run: npm run dev (to start the backend)')
  })
  .catch(console.error)