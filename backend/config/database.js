/**
 * Unified Database Configuration
 * Handles MongoDB connection with proper error handling and connection pooling
 */

const mongoose = require('mongoose');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'database' },
  transports: [
    new winston.transports.File({ filename: 'logs/database.log' }),
    new winston.transports.Console()
  ]
});

const connectDB = async () => {
  try {
    // Connection URI with fallback
    const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27020/neurocolony';
    
    // MongoDB connection options for production
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4,
      retryWrites: true,
      w: 'majority'
    };
    
    // Set Mongoose options
    mongoose.set('strictQuery', false);
    
    // Event listeners for connection monitoring
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB connection established successfully');
    });
    
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
    });
    
    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB connection disconnected');
    });
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, options);
    
    // Create indexes for better performance
    if (process.env.NODE_ENV === 'production') {
      await createIndexes();
    }
    
    return mongoose.connection;
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    throw error;
  }
};

// Create database indexes for optimization
const createIndexes = async () => {
  try {
    const collections = mongoose.connection.collections;
    
    // User indexes
    if (collections.users) {
      await collections.users.createIndex({ email: 1 }, { unique: true });
      await collections.users.createIndex({ createdAt: -1 });
      await collections.users.createIndex({ 'subscription.status': 1 });
    }
    
    // Sequence indexes
    if (collections.emailsequences) {
      await collections.emailsequences.createIndex({ userId: 1, createdAt: -1 });
      await collections.emailsequences.createIndex({ status: 1 });
      await collections.emailsequences.createIndex({ createdAt: -1 });
    }
    
    // Agent indexes
    if (collections.agents) {
      await collections.agents.createIndex({ category: 1, isActive: 1 });
      await collections.agents.createIndex({ createdAt: -1 });
    }
    
    // Workflow indexes
    if (collections.workflows) {
      await collections.workflows.createIndex({ userId: 1, status: 1 });
      await collections.workflows.createIndex({ createdAt: -1 });
    }
    
    logger.info('Database indexes created successfully');
  } catch (error) {
    logger.error('Error creating indexes:', error);
  }
};

module.exports = connectDB;