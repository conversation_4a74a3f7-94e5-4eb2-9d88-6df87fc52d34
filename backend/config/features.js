/**
 * Feature Flags Configuration
 * Centralized feature management for gradual rollout and A/B testing
 */

const featureFlags = {
  // Core features
  authentication: true,
  emailSequences: true,
  aiGeneration: true,
  
  // Advanced features
  aiEngine: process.env.ENABLE_AI_ENGINE === 'true' || true,
  agents: process.env.ENABLE_AGENTS === 'true' || true,
  workflows: process.env.ENABLE_WORKFLOWS === 'true' || true,
  colonyFeatures: process.env.ENABLE_COLONY === 'true' || false,
  
  // Infrastructure features
  caching: process.env.ENABLE_REDIS === 'true' || true,
  metrics: process.env.ENABLE_METRICS === 'true' || true,
  analytics: process.env.ENABLE_ANALYTICS === 'true' || true,
  
  // Admin features
  admin: process.env.ENABLE_ADMIN === 'true' || true,
  monitoring: process.env.ENABLE_MONITORING === 'true' || true,
  
  // Billing features
  stripeBilling: process.env.ENABLE_STRIPE === 'true' || true,
  usageBasedBilling: process.env.ENABLE_USAGE_BILLING === 'true' || true,
  
  // Performance features
  clustering: process.env.ENABLE_CLUSTERING === 'true' || false,
  advancedCaching: process.env.ENABLE_ADVANCED_CACHE === 'true' || false,
  
  // Security features
  advancedSecurity: process.env.ENABLE_ADVANCED_SECURITY === 'true' || true,
  rateLimit: process.env.ENABLE_RATE_LIMIT === 'true' || true,
  
  // UI/UX features
  darkMode: true,
  advancedDashboard: true,
  betaFeatures: process.env.ENABLE_BETA === 'true' || false
};

// Feature dependencies
const validateFeatures = () => {
  // Agents require AI engine
  if (featureFlags.agents && !featureFlags.aiEngine) {
    console.warn('Agents feature requires AI engine. Enabling AI engine.');
    featureFlags.aiEngine = true;
  }
  
  // Workflows require agents
  if (featureFlags.workflows && !featureFlags.agents) {
    console.warn('Workflows feature requires agents. Enabling agents.');
    featureFlags.agents = true;
  }
  
  // Analytics require metrics
  if (featureFlags.analytics && !featureFlags.metrics) {
    console.warn('Analytics feature requires metrics. Enabling metrics.');
    featureFlags.metrics = true;
  }
  
  // Advanced caching requires basic caching
  if (featureFlags.advancedCaching && !featureFlags.caching) {
    console.warn('Advanced caching requires basic caching. Enabling caching.');
    featureFlags.caching = true;
  }
};

// Validate on load
validateFeatures();

// Feature flag utilities
const isFeatureEnabled = (feature) => {
  return featureFlags[feature] === true;
};

const enableFeature = (feature) => {
  featureFlags[feature] = true;
  validateFeatures();
};

const disableFeature = (feature) => {
  featureFlags[feature] = false;
};

const getEnabledFeatures = () => {
  return Object.entries(featureFlags)
    .filter(([_, enabled]) => enabled)
    .map(([feature]) => feature);
};

module.exports = {
  featureFlags,
  isFeatureEnabled,
  enableFeature,
  disableFeature,
  getEnabledFeatures
};