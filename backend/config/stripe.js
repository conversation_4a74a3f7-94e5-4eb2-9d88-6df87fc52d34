import Stripe from 'stripe';

// Initialize Stripe with API key from environment
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
  typescript: false,
});

// Subscription price IDs (to be created in Stripe Dashboard)
export const PRICE_IDS = {
  pro: process.env.STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
  business: process.env.STRIPE_BUSINESS_PRICE_ID || 'price_business_monthly',
  enterprise: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly'
};

// Webhook endpoint secret for signature verification
export const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

export { stripe };