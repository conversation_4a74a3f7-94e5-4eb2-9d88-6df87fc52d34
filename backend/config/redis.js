/**
 * Redis Configuration
 * Handles Redis connection for caching, sessions, and job queues
 */

const Redis = require('ioredis');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'redis' },
  transports: [
    new winston.transports.File({ filename: 'logs/redis.log' }),
    new winston.transports.Console()
  ]
});

let redisClient = null;
let redisPubClient = null;
let redisSubClient = null;

const configureRedis = async () => {
  try {
    const redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6382,
      password: process.env.REDIS_PASSWORD,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
          return true;
        }
        return false;
      },
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      enableOfflineQueue: true
    };
    
    // Main Redis client for general operations
    redisClient = new Redis(redisConfig);
    
    // Publisher client for pub/sub
    redisPubClient = new Redis(redisConfig);
    
    // Subscriber client for pub/sub
    redisSubClient = new Redis(redisConfig);
    
    // Event handlers
    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });
    
    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });
    
    redisClient.on('error', (err) => {
      logger.error('Redis client error:', err);
    });
    
    redisClient.on('close', () => {
      logger.warn('Redis connection closed');
    });
    
    // Test connection
    await redisClient.ping();
    
    // Store clients globally for access across the application
    global.redisClient = redisClient;
    global.redisPubClient = redisPubClient;
    global.redisSubClient = redisSubClient;
    
    logger.info('Redis configuration completed successfully');
    
    return { redisClient, redisPubClient, redisSubClient };
  } catch (error) {
    logger.error('Failed to configure Redis:', error);
    throw error;
  }
};

// Cache utilities
const cache = {
  async get(key) {
    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  },
  
  async set(key, value, ttl = 3600) {
    try {
      const serialized = JSON.stringify(value);
      if (ttl) {
        await redisClient.setex(key, ttl, serialized);
      } else {
        await redisClient.set(key, serialized);
      }
      return true;
    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  },
  
  async del(key) {
    try {
      await redisClient.del(key);
      return true;
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  },
  
  async flush() {
    try {
      await redisClient.flushdb();
      logger.info('Cache flushed');
      return true;
    } catch (error) {
      logger.error('Cache flush error:', error);
      return false;
    }
  },
  
  async keys(pattern) {
    try {
      return await redisClient.keys(pattern);
    } catch (error) {
      logger.error(`Cache keys error for pattern ${pattern}:`, error);
      return [];
    }
  }
};

// Session store utilities
const sessionStore = {
  async get(sessionId) {
    const key = `session:${sessionId}`;
    return await cache.get(key);
  },
  
  async set(sessionId, data, ttl = 86400) { // 24 hours default
    const key = `session:${sessionId}`;
    return await cache.set(key, data, ttl);
  },
  
  async destroy(sessionId) {
    const key = `session:${sessionId}`;
    return await cache.del(key);
  },
  
  async touch(sessionId, ttl = 86400) {
    const key = `session:${sessionId}`;
    const data = await cache.get(key);
    if (data) {
      return await cache.set(key, data, ttl);
    }
    return false;
  }
};

// Rate limiting utilities
const rateLimiter = {
  async checkLimit(identifier, limit = 100, window = 3600) {
    const key = `rate:${identifier}`;
    const current = await redisClient.incr(key);
    
    if (current === 1) {
      await redisClient.expire(key, window);
    }
    
    return {
      allowed: current <= limit,
      current,
      limit,
      remaining: Math.max(0, limit - current),
      reset: await redisClient.ttl(key)
    };
  },
  
  async resetLimit(identifier) {
    const key = `rate:${identifier}`;
    return await cache.del(key);
  }
};

module.exports = {
  configureRedis,
  cache,
  sessionStore,
  rateLimiter,
  getRedisClient: () => redisClient,
  getPubClient: () => redisPubClient,
  getSubClient: () => redisSubClient
};