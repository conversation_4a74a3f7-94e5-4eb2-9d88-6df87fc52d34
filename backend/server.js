import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import mongoose from 'mongoose'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Import routes
import authRoutes from './routes/auth.js'
import sequenceRoutes from './routes/sequences.js'
import enhancedSequenceRoutes from './routes/enhanced-sequences.js'
import paymentRoutes from './routes/payments-simple.js'
import paypalPaymentRoutes from './routes/paypal-payments.js'
import userRoutes from './routes/users.js'
import testRoutes from './routes/test.js'
import usageRoutes from './routes/usage.js'
import performanceRoutes from './routes/performance.js'
import aiAdvancedRoutes from './routes/ai-advanced.js'
import socialIntegrationRoutes from './routes/social-integration.js'
import abTestingRoutes from './routes/ab-testing.js'
import revenueRoutes from './routes/revenue.js'
import onboardingRoutes from './routes/onboarding.js'
import whiteLabelRoutes from './routes/white-label.js'
import agentsRoutes from './routes/agents.js'
import workflowsRoutes from './routes/workflows.js'
import integrationsRoutes from './routes/integrations.js'
import teamsRoutes from './routes/teams.js'
import enterpriseRoutes from './routes/enterprise.js'
import neuralIntelligenceRoutes from './routes/neuralIntelligence.js'
import colonyRoutes from './routes/colony.js'
import sequencesCrudRoutes from './routes/sequences-crud.js'
import aiStreamingRoutes from './routes/ai-streaming.js'
import exportRoutes from './routes/export.js'

// Import middleware
import { errorHandler } from './middleware/errorHandler.js'
import { logger } from './utils/logger.js'
// import { ConfigValidator } from './utils/configValidator.js'
// import { queryMonitor } from './middleware/queryMonitor.js'
// import { sessionManager } from './middleware/sessionManager.js'
// import { distributedRateLimiter } from './middleware/distributedRateLimit.js'
// import { eventHandlers } from './services/eventHandlers.js'

dotenv.config()

// Validate configuration on startup (temporarily disabled for development)
// const validator = new ConfigValidator()
// const autoFixes = validator.autoFix()

// if (autoFixes.length > 0) {
//   logger.info('🔧 Auto-fixed configuration issues:', autoFixes)
// }

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 5000

// Security middleware
app.use(helmet())

// Enhanced compression middleware
app.use(compression({
  filter: (req, res) => {
    // Don't compress responses with this request header
    if (req.headers['x-no-compression']) {
      return false
    }
    // Fallback to standard filter function
    return compression.filter(req, res)
  },
  level: 6, // Optimal balance between compression and speed
  threshold: 1024, // Only compress responses > 1kb
  chunkSize: 1024, // Process in 1kb chunks for better memory usage
  windowBits: 15, // Maximum compression window
  memLevel: 8, // Memory usage level (1-9, 8 is good balance)
  // Enable brotli compression for modern browsers
  brotliOptions: {
    level: 4, // Brotli compression level (0-11, 4 is good balance)
    chunkSize: 1024
  }
}))

// Distributed rate limiting with Redis (temporarily disabled for development)
// const rateLimiters = distributedRateLimiter.getAllLimiters()

// Apply simple rate limiting for development
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
})
app.use('/api/', limiter)

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))

// Session management with Redis (temporarily disabled for development)
// const sessionMiddleware = sessionManager.createSessionMiddleware()
// app.use(sessionMiddleware)

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Connect to MongoDB with optimized settings
console.log('🔄 Attempting MongoDB connection...')
console.log('MongoDB URI:', process.env.MONGODB_URI)

// Performance optimized connection settings
const mongooseOptions = {
  // Connection pool optimization
  maxPoolSize: 10, // Maximum number of connections
  minPoolSize: 2,  // Minimum number of connections to maintain
  maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
  serverSelectionTimeoutMS: 5000, // How long to try selecting a server
  socketTimeoutMS: 45000, // How long to wait for a response
  
  // Performance settings
  bufferCommands: false, // Disable mongoose buffering
  
  // Retry settings
  retryWrites: true,
  retryReads: true,
  
  // Compression
  compressors: ['zlib'],
  zlibCompressionLevel: 6
}

// Enhanced connection with graceful degradation and retry logic
async function connectWithRetry(retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      // Try with IPv4 localhost fix
      const uri = process.env.MONGODB_URI.replace('localhost', '127.0.0.1')
      await mongoose.connect(uri, mongooseOptions)
      console.log('✅ Connected to MongoDB successfully with optimized settings!')
      logger.info('Connected to MongoDB')
      global.dbConnected = true
      return true
    } catch (error) {
      console.log(`⚠️ MongoDB connection attempt ${i + 1}/${retries} failed:`, error.message)
      
      if (i === retries - 1) {
        // Last attempt failed - graceful degradation
        console.log('🔄 Starting in degraded mode - database operations will use fallback')
        logger.warn('MongoDB unavailable, running in degraded mode')
        global.dbConnected = false
        return false
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
    }
  }
}

connectWithRetry().then(async (connected) => {
  if (connected) {
    // Initialize colony intelligence after database connection
    try {
      const colonyIntelligenceService = (await import('./services/colonyIntelligenceService.js')).default
      await colonyIntelligenceService.initializeColony()
    } catch (error) {
      logger.error('Failed to initialize colony after DB connection:', error)
    }
  }
})

// Routes
console.log('🔄 Loading routes...')
try {
  app.use('/api/auth', authRoutes)
  console.log('✅ Auth routes loaded')
  app.use('/api/sequences', sequenceRoutes)
  app.use('/api/sequences', enhancedSequenceRoutes)
  console.log('✅ Sequence routes loaded')
  app.use('/api/payments', paymentRoutes)
  console.log('✅ Payment routes loaded')
  app.use('/api/paypal', paypalPaymentRoutes)
  console.log('✅ PayPal payment routes loaded')
  app.use('/api/users', userRoutes)
  console.log('✅ User routes loaded')
  app.use('/api/usage', usageRoutes)
  console.log('✅ Usage routes loaded')
  app.use('/api/ai-advanced', aiAdvancedRoutes)
  console.log('✅ Advanced AI routes loaded')
  app.use('/api/social', socialIntegrationRoutes)
  console.log('✅ Social integration routes loaded')
  app.use('/api/ab-testing', abTestingRoutes)
  console.log('✅ A/B testing routes loaded')
  app.use('/api/revenue', revenueRoutes)
  console.log('✅ Revenue optimization routes loaded')
  app.use('/api/onboarding', onboardingRoutes)
  console.log('✅ User onboarding routes loaded')
  app.use('/api/white-label', whiteLabelRoutes)
  console.log('✅ White-label routes loaded')
  app.use('/api/agents', agentsRoutes)
  console.log('✅ Agent platform routes loaded')
  app.use('/api/workflows', workflowsRoutes)
  console.log('✅ Workflow engine routes loaded')
  app.use('/api/integrations', integrationsRoutes)
  console.log('✅ Platform integration routes loaded')
  app.use('/api/teams', teamsRoutes)
  console.log('✅ Team collaboration routes loaded')
  app.use('/api/enterprise', enterpriseRoutes)
  console.log('✅ Enterprise features routes loaded')
  app.use('/api/intelligence', neuralIntelligenceRoutes)
  console.log('✅ Neural Intelligence routes loaded')
  app.use('/api/colony', colonyRoutes)
  console.log('✅ Colony Intelligence routes loaded')
  app.use('/api/sequences/v2', sequencesCrudRoutes)
  console.log('✅ Sequences CRUD routes loaded')
  app.use('/api/ai', aiStreamingRoutes)
  console.log('✅ AI streaming routes loaded')
  app.use('/api/export', exportRoutes)
  console.log('✅ Export routes loaded')
  app.use('/api/test', testRoutes)
  console.log('✅ Test routes loaded')
  app.use('/api/performance', performanceRoutes)
  console.log('✅ Performance monitoring routes loaded')
  
  // Add health route alias for compatibility
  app.get('/health', (req, res) => {
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: global.dbConnected ? 'connected' : 'degraded',
      uptime: process.uptime()
    })
  })
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: global.dbConnected ? 'connected' : 'degraded',
      uptime: process.uptime()
    })
  })
  console.log('✅ Health route aliases added')
} catch (error) {
  console.error('❌ Route loading failed:', error)
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// Stripe webhook endpoint (must be before other middleware)
app.use('/api/webhooks/stripe', express.raw({ type: 'application/json' }), paymentRoutes)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  })
})

// Error handling middleware
app.use(errorHandler)

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`)
  console.log(`Environment: ${process.env.NODE_ENV}`)
  logger.info(`Server running on port ${PORT}`)
  logger.info(`Environment: ${process.env.NODE_ENV}`)
})

export default app