{"name": "neurocolony-backend", "version": "2.0.0", "description": "NeuroColony Backend API - Minimal Working Version", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "echo 'Tests will be added after dependency resolution'", "deps:check": "npm outdated", "deps:audit": "npm audit", "deps:update": "npm update"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.0", "redis": "^4.6.0", "axios": "^1.6.0", "dotenv": "^16.3.0", "winston": "^3.11.0", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "nodemailer": "^6.9.0", "stripe": "^14.7.0", "openai": "^4.20.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["neurocolony", "backend", "api"], "author": "NeuroColony Engineering Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}