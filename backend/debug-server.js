import express from 'express'
import dotenv from 'dotenv'

console.log('🚀 Starting debug server...')

dotenv.config()

const app = express()
const PORT = process.env.PORT || 5000

console.log('📋 Environment variables:')
console.log('PORT:', PORT)
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('MONGODB_URI:', process.env.MONGODB_URI)

app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Debug server is running' })
})

app.listen(PORT, () => {
  console.log(`✅ Debug server running on port ${PORT}`)
})