import { logger } from '../utils/logger.js'

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      aiRequests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0,
        timeouts: 0
      },
      apiRequests: {
        total: 0,
        slowRequests: 0, // > 5 seconds
        errorRequests: 0
      }
    }
    
    this.thresholds = {
      aiResponseTime: 10000, // 10 seconds
      apiResponseTime: 5000,  // 5 seconds
      errorRate: 0.05        // 5% error rate
    }
  }

  trackAIRequest(startTime, endTime, success, error = null) {
    const responseTime = endTime - startTime
    
    this.metrics.aiRequests.total++
    
    if (success) {
      this.metrics.aiRequests.successful++
    } else {
      this.metrics.aiRequests.failed++
      
      if (error && error.message.includes('timeout')) {
        this.metrics.aiRequests.timeouts++
      }
    }
    
    // Calculate rolling average
    const currentAvg = this.metrics.aiRequests.averageResponseTime
    const totalRequests = this.metrics.aiRequests.total
    this.metrics.aiRequests.averageResponseTime = 
      ((currentAvg * (totalRequests - 1)) + responseTime) / totalRequests
    
    // Alert on performance issues
    if (responseTime > this.thresholds.aiResponseTime) {
      logger.warn('🐌 Slow AI request detected', {
        responseTime: `${responseTime}ms`,
        threshold: `${this.thresholds.aiResponseTime}ms`,
        success,
        error: error?.message
      })
    }
    
    return {
      responseTime,
      average: this.metrics.aiRequests.averageResponseTime,
      successRate: this.metrics.aiRequests.successful / this.metrics.aiRequests.total
    }
  }

  trackAPIRequest(req, res, responseTime) {
    this.metrics.apiRequests.total++
    
    if (responseTime > this.thresholds.apiResponseTime) {
      this.metrics.apiRequests.slowRequests++
      
      logger.warn('🐌 Slow API request', {
        method: req.method,
        url: req.url,
        responseTime: `${responseTime}ms`,
        userAgent: req.get('User-Agent')
      })
    }
    
    if (res.statusCode >= 400) {
      this.metrics.apiRequests.errorRequests++
    }
  }

  getHealthStatus() {
    const aiSuccessRate = this.metrics.aiRequests.total > 0 ? 
      this.metrics.aiRequests.successful / this.metrics.aiRequests.total : 1
    
    const apiErrorRate = this.metrics.apiRequests.total > 0 ? 
      this.metrics.apiRequests.errorRequests / this.metrics.apiRequests.total : 0
    
    const isHealthy = 
      aiSuccessRate >= (1 - this.thresholds.errorRate) &&
      apiErrorRate <= this.thresholds.errorRate &&
      this.metrics.aiRequests.averageResponseTime <= this.thresholds.aiResponseTime
    
    return {
      status: isHealthy ? 'healthy' : 'degraded',
      metrics: this.metrics,
      thresholds: this.thresholds,
      aiSuccessRate: (aiSuccessRate * 100).toFixed(1) + '%',
      apiErrorRate: (apiErrorRate * 100).toFixed(1) + '%',
      avgAIResponseTime: Math.round(this.metrics.aiRequests.averageResponseTime) + 'ms'
    }
  }

  middleware() {
    return (req, res, next) => {
      const startTime = Date.now()
      
      res.on('finish', () => {
        const responseTime = Date.now() - startTime
        this.trackAPIRequest(req, res, responseTime)
      })
      
      next()
    }
  }

  async healthCheck() {
    const health = this.getHealthStatus()
    
    logger.info('🔍 System health check', {
      status: health.status,
      aiRequests: health.metrics.aiRequests.total,
      aiSuccessRate: health.aiSuccessRate,
      avgResponseTime: health.avgAIResponseTime
    })
    
    return health
  }
}

export default new PerformanceMonitor()