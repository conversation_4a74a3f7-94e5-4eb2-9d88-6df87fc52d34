import { logger } from '../utils/logger.js'

export const errorHandler = (err, req, res, next) => {
  // Log error with request context
  logger.error('Error occurred:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  })

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(val => ({
      field: val.path,
      message: val.message,
      value: val.value
    }))
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    })
  }

  // Mongoose cast error (invalid ObjectId)
  if (err.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: `Invalid ${err.path}: ${err.value}`
    })
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue || err.keyPattern || {})[0] || 'field'
    return res.status(400).json({
      success: false,
      message: `${field} already exists`,
      field
    })
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    })
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    })
  }

  // Rate limit errors
  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      success: false,
      message: 'Request body too large'
    })
  }

  // Stripe errors
  if (err.type && err.type.includes('Stripe')) {
    return res.status(400).json({
      success: false,
      message: 'Payment processing error',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    })
  }

  // OpenAI API errors
  if (err.response && err.response.status) {
    if (err.response.status === 429) {
      return res.status(429).json({
        success: false,
        message: 'AI service temporarily unavailable. Please try again later.'
      })
    }
    if (err.response.status === 401) {
      logger.error('OpenAI API key invalid or expired')
      return res.status(500).json({
        success: false,
        message: 'AI service configuration error'
      })
    }
  }

  // Default server error
  const statusCode = err.statusCode || 500
  const message = statusCode === 500 && process.env.NODE_ENV === 'production' 
    ? 'Internal Server Error' 
    : err.message || 'Internal Server Error'

  res.status(statusCode).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack
    })
  })
}