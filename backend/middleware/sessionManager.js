import session from 'express-session'
// import ConnectRedis from 'connect-redis'
// import { createClient } from 'redis'
import { logger } from '../utils/logger.js'

class SessionManager {
  constructor() {
    this.redisClient = null
    this.sessionMiddleware = null
    this.isRedisAvailable = false
    this.setupRedisClient()
  }

  async setupRedisClient() {
    // Temporarily disable Red<PERSON> for Phase 3 testing
    this.isRedisAvailable = false
    logger.info('📝 Session store using memory (Redis disabled for testing)')
  }

  createSessionMiddleware() {
    let store
    let sessionConfig = {
      secret: process.env.SESSION_SECRET || 'convertflow-session-secret-change-in-production',
      resave: false,
      saveUninitialized: false,
      rolling: true, // Extend session on activity
      cookie: {
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        httpOnly: true, // Prevent XSS
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'strict' // CSRF protection
      },
      name: 'convertflow.session.id' // Custom session name
    }

    // Always use memory store for Phase 3 testing
    logger.info('✅ Session middleware configured with memory store', {
      redisAvailable: false,
      note: 'Using memory store for Phase 3 testing'
    })

    this.sessionMiddleware = session(sessionConfig)
    return this.sessionMiddleware
  }

  // Get session statistics
  async getSessionStats() {
    if (!this.isRedisAvailable || !this.redisClient) {
      return {
        type: 'memory',
        redisAvailable: false,
        sessions: 'unknown'
      }
    }

    try {
      // Count active sessions in Redis
      const sessionKeys = await this.redisClient.keys('cf:sess:*')
      const info = await this.redisClient.info('memory')
      
      // Parse memory info
      const memoryLines = info.split('\r\n')
      const usedMemory = memoryLines.find(line => line.startsWith('used_memory_human:'))
        ?.split(':')[1] || 'unknown'

      return {
        type: 'redis',
        redisAvailable: true,
        activeSessions: sessionKeys.length,
        memoryUsage: usedMemory,
        prefix: 'cf:sess:'
      }
    } catch (error) {
      logger.error('Failed to get session stats:', error)
      return {
        type: 'redis',
        redisAvailable: false,
        error: error.message
      }
    }
  }

  // Clean up expired sessions manually (Redis handles this automatically)
  async cleanupSessions() {
    if (!this.isRedisAvailable || !this.redisClient) {
      logger.info('Session cleanup skipped - Redis not available')
      return { cleaned: 0 }
    }

    try {
      // Get all session keys
      const sessionKeys = await this.redisClient.keys('cf:sess:*')
      let cleanedCount = 0

      for (const key of sessionKeys) {
        try {
          const ttl = await this.redisClient.ttl(key)
          if (ttl === -1) {
            // Key exists but has no expiration, set TTL
            await this.redisClient.expire(key, 24 * 60 * 60) // 24 hours
            cleanedCount++
          }
        } catch (error) {
          logger.warn(`Failed to check TTL for session ${key}:`, error.message)
        }
      }

      logger.info(`Session cleanup completed: ${cleanedCount} sessions updated`)
      return { cleaned: cleanedCount, total: sessionKeys.length }
    } catch (error) {
      logger.error('Session cleanup failed:', error)
      return { error: error.message }
    }
  }

  // Health check for session store
  healthCheck() {
    if (this.isRedisAvailable && this.redisClient) {
      return {
        status: 'healthy',
        type: 'redis',
        redisConnected: true
      }
    } else {
      return {
        status: 'degraded',
        type: 'memory',
        redisConnected: false,
        warning: 'Using memory store - not suitable for clustering'
      }
    }
  }

  // Graceful shutdown
  async shutdown() {
    if (this.redisClient) {
      logger.info('🔄 Closing Redis session store connection...')
      try {
        await this.redisClient.quit()
        logger.info('✅ Redis session store connection closed')
      } catch (error) {
        logger.error('❌ Error closing Redis session connection:', error)
      }
    }
  }
}

// Export singleton instance
export const sessionManager = new SessionManager()
export default sessionManager