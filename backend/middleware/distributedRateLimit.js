import rateLimit from 'express-rate-limit'
import RedisStore from 'rate-limit-redis'
import { createClient } from 'redis'
import { logger } from '../utils/logger.js'

class DistributedRateLimiter {
  constructor() {
    this.redisClient = null
    this.isRedisAvailable = false
    this.limiters = new Map()
    this.setupRedisClient()
  }

  async setupRedisClient() {
    try {
      // Create Redis client for rate limiting
      this.redisClient = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        retry_strategy: (options) => {
          if (options.total_retry_time > 1000 * 60 * 60) {
            return new Error('Retry time exhausted')
          }
          if (options.attempt > 10) {
            return undefined
          }
          return Math.min(options.attempt * 100, 3000)
        },
        connect_timeout: 60000,
        lazyConnect: true
      })

      // Redis event handlers
      this.redisClient.on('ready', () => {
        this.isRedisAvailable = true
        logger.info('✅ Redis rate limiter ready')
      })

      this.redisClient.on('error', (err) => {
        this.isRedisAvailable = false
        logger.error('❌ Redis rate limiter error:', err.message)
      })

      // Connect to Redis
      await this.redisClient.connect()
      await this.redisClient.ping()
      
      logger.info('🚦 Distributed rate limiting initialized')

    } catch (error) {
      this.isRedisAvailable = false
      logger.warn('⚠️  Redis not available for rate limiting, using memory store:', error.message)
    }
  }

  // Create different rate limiters for different endpoints
  createAPILimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:api:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // 100 requests per 15 minutes per IP
      message: {
        error: 'Too many API requests',
        message: 'Please slow down your requests',
        retryAfter: 15 * 60 // seconds
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => {
        // Use user ID if authenticated, otherwise IP
        return req.user?.id || req.ip
      },
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/health' || req.path === '/api/health'
      },
      handler: (req, res, next, options) => {
        logger.warn('🚦 API rate limit reached', {
          ip: req.ip,
          userId: req.user?.id,
          path: req.path,
          userAgent: req.get('User-Agent')
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('api', limiter)
    return limiter
  }

  createAILimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:ai:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 10, // 10 AI requests per minute per user
      message: {
        error: 'AI generation rate limit exceeded',
        message: 'Please wait before generating more sequences',
        retryAfter: 60
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => {
        // Always use user ID for AI requests
        return req.user?.id || req.ip
      },
      handler: (req, res, next, options) => {
        logger.warn('🤖 AI rate limit reached', {
          userId: req.user?.id,
          ip: req.ip,
          plan: req.user?.subscription?.type
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('ai', limiter)
    return limiter
  }

  createAuthLimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:auth:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 login attempts per 15 minutes per IP
      message: {
        error: 'Too many authentication attempts',
        message: 'Please wait before trying again',
        retryAfter: 15 * 60
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => req.ip,
      skipSuccessfulRequests: true, // Don't count successful logins
      handler: (req, res, next, options) => {
        logger.warn('🔐 Auth rate limit reached', {
          ip: req.ip,
          email: req.body?.email,
          userAgent: req.get('User-Agent')
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('auth', limiter)
    return limiter
  }

  createPaymentLimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:payment:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 payment attempts per hour per user
      message: {
        error: 'Payment rate limit exceeded',
        message: 'Please contact support if you need assistance',
        retryAfter: 60 * 60
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => req.user?.id || req.ip,
      handler: (req, res, next, options) => {
        logger.warn('💳 Payment rate limit reached', {
          userId: req.user?.id,
          ip: req.ip,
          amount: req.body?.amount
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('payment', limiter)
    return limiter
  }

  // Dynamic rate limiter based on user plan
  createPlanBasedLimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:plan:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: (req) => {
        // Different limits based on user plan
        const plan = req.user?.subscription?.type || 'free'
        const limits = {
          free: 5,      // 5 requests per minute
          pro: 20,      // 20 requests per minute
          business: 50  // 50 requests per minute
        }
        return limits[plan] || limits.free
      },
      message: (req) => {
        const plan = req.user?.subscription?.type || 'free'
        return {
          error: 'Plan rate limit exceeded',
          message: `${plan} plan limits reached. Consider upgrading for higher limits.`,
          currentPlan: plan,
          retryAfter: 60
        }
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => `${req.user?.id || req.ip}:${req.user?.subscription?.type || 'free'}`,
      handler: (req, res, next, options) => {
        logger.warn('📊 Plan rate limit reached', {
          userId: req.user?.id,
          plan: req.user?.subscription?.type,
          limit: options.max
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('plan', limiter)
    return limiter
  }

  // Burst protection for high-frequency requests
  createBurstLimiter() {
    const store = this.isRedisAvailable ? new RedisStore({
      sendCommand: (...args) => this.redisClient.sendCommand(args),
      prefix: 'cf:rl:burst:'
    }) : undefined

    const limiter = rateLimit({
      windowMs: 1000, // 1 second
      max: 5, // 5 requests per second
      message: {
        error: 'Request burst limit exceeded',
        message: 'Please slow down your requests',
        retryAfter: 1
      },
      standardHeaders: true,
      legacyHeaders: false,
      store,
      keyGenerator: (req) => req.user?.id || req.ip,
      handler: (req, res, next, options) => {
        logger.warn('💥 Burst rate limit reached', {
          userId: req.user?.id,
          ip: req.ip,
          path: req.path
        })
        res.status(options.statusCode).json(options.message)
      }
    })

    this.limiters.set('burst', limiter)
    return limiter
  }

  // Get all configured limiters
  getAllLimiters() {
    return {
      api: this.createAPILimiter(),
      ai: this.createAILimiter(),
      auth: this.createAuthLimiter(),
      payment: this.createPaymentLimiter(),
      plan: this.createPlanBasedLimiter(),
      burst: this.createBurstLimiter()
    }
  }

  // Get rate limit statistics
  async getStats() {
    if (!this.isRedisAvailable || !this.redisClient) {
      return {
        type: 'memory',
        redisAvailable: false,
        limiters: Array.from(this.limiters.keys())
      }
    }

    try {
      // Get rate limit keys from Redis
      const apiKeys = await this.redisClient.keys('cf:rl:api:*')
      const aiKeys = await this.redisClient.keys('cf:rl:ai:*')
      const authKeys = await this.redisClient.keys('cf:rl:auth:*')
      const paymentKeys = await this.redisClient.keys('cf:rl:payment:*')

      return {
        type: 'redis',
        redisAvailable: true,
        limiters: Array.from(this.limiters.keys()),
        activeKeys: {
          api: apiKeys.length,
          ai: aiKeys.length,
          auth: authKeys.length,
          payment: paymentKeys.length
        }
      }
    } catch (error) {
      logger.error('Failed to get rate limit stats:', error)
      return {
        type: 'redis',
        redisAvailable: false,
        error: error.message
      }
    }
  }

  // Health check for rate limiting
  healthCheck() {
    return {
      status: this.isRedisAvailable ? 'healthy' : 'degraded',
      redisAvailable: this.isRedisAvailable,
      limiterCount: this.limiters.size,
      type: this.isRedisAvailable ? 'distributed' : 'memory'
    }
  }

  // Clear rate limits for a specific key (admin function)
  async clearLimits(prefix, key) {
    if (!this.isRedisAvailable || !this.redisClient) {
      logger.warn('Cannot clear rate limits - Redis not available')
      return false
    }

    try {
      const fullKey = `cf:rl:${prefix}:${key}`
      await this.redisClient.del(fullKey)
      logger.info(`Rate limits cleared for ${fullKey}`)
      return true
    } catch (error) {
      logger.error('Failed to clear rate limits:', error)
      return false
    }
  }

  // Graceful shutdown
  async shutdown() {
    if (this.redisClient) {
      logger.info('🔄 Closing Redis rate limiter connection...')
      try {
        await this.redisClient.quit()
        logger.info('✅ Redis rate limiter connection closed')
      } catch (error) {
        logger.error('❌ Error closing Redis rate limiter connection:', error)
      }
    }
  }
}

// Export singleton instance
export const distributedRateLimiter = new DistributedRateLimiter()
export default distributedRateLimiter