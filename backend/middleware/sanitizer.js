import DOMPurify from 'isomorphic-dompurify'

// Sanitize HTML input to prevent XSS attacks
export const sanitizeInput = (req, res, next) => {
  const sanitizeObj = (obj) => {
    if (typeof obj === 'string') {
      return DOMPurify.sanitize(obj, { ALLOWED_TAGS: [] }) // Strip all HTML tags
    } else if (Array.isArray(obj)) {
      return obj.map(sanitizeObj)
    } else if (obj && typeof obj === 'object') {
      const sanitized = {}
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObj(value)
      }
      return sanitized
    }
    return obj
  }

  if (req.body) {
    req.body = sanitizeObj(req.body)
  }
  
  if (req.query) {
    req.query = sanitizeObj(req.query)
  }
  
  if (req.params) {
    req.params = sanitizeObj(req.params)
  }

  next()
}

// Validate MongoDB ObjectId format
export const validateObjectId = (req, res, next) => {
  const { id } = req.params
  if (id && !/^[0-9a-fA-F]{24}$/.test(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format'
    })
  }
  next()
}

// Prevent NoSQL injection by sanitizing query operators
export const sanitizeQuery = (req, res, next) => {
  const sanitizeQueryObj = (obj) => {
    if (typeof obj === 'object' && obj !== null) {
      const sanitized = {}
      for (const [key, value] of Object.entries(obj)) {
        // Remove MongoDB operators from user input
        if (key.startsWith('$')) {
          continue
        }
        sanitized[key] = typeof value === 'object' ? sanitizeQueryObj(value) : value
      }
      return sanitized
    }
    return obj
  }

  if (req.query) {
    req.query = sanitizeQueryObj(req.query)
  }

  next()
}