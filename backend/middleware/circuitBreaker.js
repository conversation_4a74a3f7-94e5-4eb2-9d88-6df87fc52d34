import CircuitBreaker from 'opossum'
import { logger } from '../utils/logger.js'

class AIServiceCircuitBreaker {
  constructor() {
    this.breakers = new Map()
    this.setupDefaultBreaker()
    
    logger.info('⚡ Circuit breaker system initialized')
  }

  setupDefaultBreaker() {
    // Main AI service circuit breaker
    const aiOptions = {
      timeout: 30000, // 30 seconds
      errorThresholdPercentage: 50, // Open circuit if 50% of requests fail
      resetTimeout: 30000, // Try to close circuit after 30 seconds
      rollingCountTimeout: 60000, // 1 minute rolling window
      rollingCountBuckets: 10, // 10 buckets in the rolling window
      name: 'AI Service',
      group: 'ai-generation'
    }

    const aiBreaker = new CircuitBreaker(this.aiServiceWrapper.bind(this), aiOptions)
    
    // Set up fallback for AI service
    aiBreaker.fallback(() => {
      logger.warn('🔄 AI Circuit breaker fallback triggered')
      return this.generateFallbackResponse()
    })

    // Set up event listeners for monitoring
    this.setupEventListeners(aiBreaker, 'ai-service')
    
    this.breakers.set('ai-service', aiBreaker)

    // Database circuit breaker
    const dbOptions = {
      timeout: 10000, // 10 seconds
      errorThresholdPercentage: 70, // More lenient for database
      resetTimeout: 15000, // Faster recovery
      name: 'Database',
      group: 'database'
    }

    const dbBreaker = new CircuitBreaker(this.databaseWrapper.bind(this), dbOptions)
    dbBreaker.fallback(() => {
      logger.warn('🔄 Database circuit breaker fallback triggered')
      throw new Error('Database temporarily unavailable - please try again')
    })

    this.setupEventListeners(dbBreaker, 'database')
    this.breakers.set('database', dbBreaker)
  }

  setupEventListeners(breaker, serviceName) {
    breaker.on('open', () => {
      logger.error(`🚨 Circuit breaker OPENED for ${serviceName}`, {
        service: serviceName,
        state: 'OPEN'
      })
    })

    breaker.on('halfOpen', () => {
      logger.warn(`🔄 Circuit breaker HALF-OPEN for ${serviceName}`, {
        service: serviceName,
        state: 'HALF_OPEN'
      })
    })

    breaker.on('close', () => {
      logger.info(`✅ Circuit breaker CLOSED for ${serviceName}`, {
        service: serviceName,
        state: 'CLOSED'
      })
    })

    breaker.on('failure', (error) => {
      logger.warn(`❌ Circuit breaker recorded failure for ${serviceName}`, {
        service: serviceName,
        error: error.message
      })
    })

    breaker.on('success', () => {
      logger.debug(`✅ Circuit breaker recorded success for ${serviceName}`, {
        service: serviceName
      })
    })

    breaker.on('timeout', () => {
      logger.warn(`⏰ Circuit breaker timeout for ${serviceName}`, {
        service: serviceName
      })
    })

    breaker.on('reject', () => {
      logger.warn(`🚫 Circuit breaker rejected request for ${serviceName}`, {
        service: serviceName,
        reason: 'Circuit is open'
      })
    })

    breaker.on('fallback', (result) => {
      logger.info(`🔄 Circuit breaker fallback executed for ${serviceName}`, {
        service: serviceName,
        fallbackResult: typeof result
      })
    })
  }

  // Wrapper for AI service calls
  async aiServiceWrapper(aiService, businessInfo, settings) {
    const startTime = Date.now()
    
    try {
      const result = await aiService.generateEmailSequence(businessInfo, settings)
      const duration = Date.now() - startTime
      
      logger.debug('⚡ AI service call succeeded through circuit breaker', {
        duration: `${duration}ms`,
        quality: result.aiAnalysis?.overallScore
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      logger.error('❌ AI service call failed through circuit breaker', {
        duration: `${duration}ms`,
        error: error.message
      })
      
      throw error
    }
  }

  // Wrapper for database operations
  async databaseWrapper(operation) {
    const startTime = Date.now()
    
    try {
      const result = await operation()
      const duration = Date.now() - startTime
      
      logger.debug('⚡ Database operation succeeded through circuit breaker', {
        duration: `${duration}ms`
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      logger.error('❌ Database operation failed through circuit breaker', {
        duration: `${duration}ms`,
        error: error.message
      })
      
      throw error
    }
  }

  // Generate fallback response when AI service is unavailable
  generateFallbackResponse() {
    return {
      emails: [
        {
          emailNumber: 1,
          subject: "Welcome! Let's get started",
          body: "Thank you for your interest! We're excited to help you achieve your goals. Stay tuned for more valuable content.",
          dayDelay: 0,
          personalizedElements: ["Welcome message", "Interest acknowledgment"]
        },
        {
          emailNumber: 2,
          subject: "Quick question for you",
          body: "We'd love to learn more about your specific needs. What's your biggest challenge right now?",
          dayDelay: 2,
          personalizedElements: ["Engagement question", "Personalized inquiry"]
        },
        {
          emailNumber: 3,
          subject: "Here's how we can help",
          body: "Based on what we know about your industry, here are some ways we can provide value. Let's connect!",
          dayDelay: 5,
          personalizedElements: ["Industry reference", "Value proposition"]
        }
      ],
      aiAnalysis: {
        overallScore: 75,
        strengths: [
          "Fallback system active",
          "Maintains user experience during outages",
          "Generic but professional content"
        ],
        improvements: [
          "AI service will resume normal operation automatically",
          "Contact support if issues persist"
        ],
        predictedConversionRate: 2.5,
        generatedBy: 'Circuit Breaker Fallback System'
      }
    }
  }

  // Execute AI service call through circuit breaker
  async executeAICall(aiService, businessInfo, settings) {
    const breaker = this.breakers.get('ai-service')
    return await breaker.fire(aiService, businessInfo, settings)
  }

  // Execute database operation through circuit breaker
  async executeDatabaseCall(operation) {
    const breaker = this.breakers.get('database')
    return await breaker.fire(operation)
  }

  // Get circuit breaker statistics
  getStats() {
    const stats = {}
    
    for (const [name, breaker] of this.breakers.entries()) {
      const breakerStats = breaker.stats
      
      stats[name] = {
        state: breaker.opened ? 'OPEN' : (breaker.halfOpen ? 'HALF_OPEN' : 'CLOSED'),
        requests: breakerStats.fires,
        successes: breakerStats.successes,
        failures: breakerStats.failures,
        timeouts: breakerStats.timeouts,
        rejections: breakerStats.rejects,
        fallbacks: breakerStats.fallbacks,
        successRate: breakerStats.fires > 0 ? 
          ((breakerStats.successes / breakerStats.fires) * 100).toFixed(1) + '%' : '0%',
        errorRate: breakerStats.fires > 0 ? 
          ((breakerStats.failures / breakerStats.fires) * 100).toFixed(1) + '%' : '0%'
      }
    }
    
    return stats
  }

  // Health check for circuit breakers
  healthCheck() {
    const openBreakers = []
    const degradedBreakers = []
    
    for (const [name, breaker] of this.breakers.entries()) {
      if (breaker.opened) {
        openBreakers.push(name)
      } else if (breaker.halfOpen) {
        degradedBreakers.push(name)
      }
    }
    
    let status = 'healthy'
    if (openBreakers.length > 0) {
      status = 'unhealthy'
    } else if (degradedBreakers.length > 0) {
      status = 'degraded'
    }
    
    return {
      status,
      openBreakers,
      degradedBreakers,
      totalBreakers: this.breakers.size,
      details: this.getStats()
    }
  }

  // Force open/close breakers for testing
  forceOpen(serviceName) {
    const breaker = this.breakers.get(serviceName)
    if (breaker) {
      breaker.open()
      logger.warn(`🔧 Force opened circuit breaker for ${serviceName}`)
    }
  }

  forceClose(serviceName) {
    const breaker = this.breakers.get(serviceName)
    if (breaker) {
      breaker.close()
      logger.info(`🔧 Force closed circuit breaker for ${serviceName}`)
    }
  }

  // Get breaker by name
  getBreaker(name) {
    return this.breakers.get(name)
  }
}

// Export singleton instance
export const circuitBreaker = new AIServiceCircuitBreaker()
export default circuitBreaker