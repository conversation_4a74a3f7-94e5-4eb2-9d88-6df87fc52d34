import { logger } from '../utils/logger.js'
import mongoose from 'mongoose'

class QueryMonitor {
  constructor() {
    this.queryStats = {
      totalQueries: 0,
      slowQueries: 0,
      averageTime: 0,
      slowQueryThreshold: 100, // ms
      queries: []
    }
    
    this.setupMonitoring()
    
    // Log stats every 5 minutes
    setInterval(() => {
      this.logStats()
    }, 5 * 60 * 1000)
    
    logger.info('📊 Query monitoring initialized', {
      slowQueryThreshold: `${this.queryStats.slowQueryThreshold}ms`
    })
  }

  setupMonitoring() {
    // Enable mongoose debugging with custom handler
    mongoose.set('debug', (collectionName, method, query, doc, options) => {
      const startTime = Date.now()
      
      // Create a promise to track completion
      const trackQuery = (query, method, collection) => {
        return new Promise((resolve) => {
          // Use setImmediate to capture the query completion
          setImmediate(() => {
            const endTime = Date.now()
            const duration = endTime - startTime
            
            this.recordQuery({
              collection: collectionName,
              method,
              query: this.sanitizeQuery(query),
              duration,
              timestamp: new Date().toISOString()
            })
            
            resolve()
          })
        })
      }
      
      trackQuery(query, method, collectionName)
    })
    
    // Hook into mongoose queries for more detailed monitoring
    const originalExec = mongoose.Query.prototype.exec
    mongoose.Query.prototype.exec = function() {
      const startTime = Date.now()
      const collection = this.model.collection.name
      const method = this.op
      const query = this.getQuery()
      
      return originalExec.call(this).then((result) => {
        const duration = Date.now() - startTime
        
        queryMonitor.recordQuery({
          collection,
          method,
          query: queryMonitor.sanitizeQuery(query),
          duration,
          timestamp: new Date().toISOString(),
          resultSize: Array.isArray(result) ? result.length : (result ? 1 : 0)
        })
        
        return result
      }).catch((error) => {
        const duration = Date.now() - startTime
        
        queryMonitor.recordQuery({
          collection,
          method,
          query: queryMonitor.sanitizeQuery(query),
          duration,
          timestamp: new Date().toISOString(),
          error: error.message
        })
        
        throw error
      })
    }
  }

  recordQuery(queryInfo) {
    this.queryStats.totalQueries++
    
    // Track slow queries
    if (queryInfo.duration > this.queryStats.slowQueryThreshold) {
      this.queryStats.slowQueries++
      
      logger.warn('🐌 Slow query detected', {
        collection: queryInfo.collection,
        method: queryInfo.method,
        duration: `${queryInfo.duration}ms`,
        query: queryInfo.query
      })
    }
    
    // Update average time
    this.queryStats.averageTime = (
      (this.queryStats.averageTime * (this.queryStats.totalQueries - 1) + queryInfo.duration) / 
      this.queryStats.totalQueries
    )
    
    // Keep last 100 queries for analysis
    this.queryStats.queries.push(queryInfo)
    if (this.queryStats.queries.length > 100) {
      this.queryStats.queries.shift()
    }
    
    // Log individual query performance
    if (queryInfo.duration > 50) { // Log queries over 50ms
      logger.debug('📊 Query performance', {
        collection: queryInfo.collection,
        method: queryInfo.method,
        duration: `${queryInfo.duration}ms`,
        resultSize: queryInfo.resultSize
      })
    }
  }

  sanitizeQuery(query) {
    // Remove sensitive data from query logging
    const sanitized = JSON.parse(JSON.stringify(query))
    
    // Remove password fields
    if (sanitized.password) delete sanitized.password
    if (sanitized.$set && sanitized.$set.password) delete sanitized.$set.password
    
    // Truncate long strings
    const truncateStrings = (obj) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string' && obj[key].length > 100) {
          obj[key] = obj[key].substring(0, 100) + '...'
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          truncateStrings(obj[key])
        }
      }
    }
    
    truncateStrings(sanitized)
    return sanitized
  }

  // Analyze query patterns for optimization opportunities
  analyzeQueries() {
    const analysis = {
      totalQueries: this.queryStats.totalQueries,
      slowQueries: this.queryStats.slowQueries,
      slowQueryPercentage: (this.queryStats.slowQueries / this.queryStats.totalQueries * 100).toFixed(1),
      averageTime: Math.round(this.queryStats.averageTime),
      collectionStats: {},
      methodStats: {},
      recommendations: []
    }
    
    // Analyze by collection
    this.queryStats.queries.forEach(query => {
      if (!analysis.collectionStats[query.collection]) {
        analysis.collectionStats[query.collection] = {
          count: 0,
          totalTime: 0,
          slowCount: 0
        }
      }
      
      const stat = analysis.collectionStats[query.collection]
      stat.count++
      stat.totalTime += query.duration
      if (query.duration > this.queryStats.slowQueryThreshold) {
        stat.slowCount++
      }
    })
    
    // Calculate averages and identify issues
    for (const collection in analysis.collectionStats) {
      const stat = analysis.collectionStats[collection]
      stat.averageTime = Math.round(stat.totalTime / stat.count)
      stat.slowPercentage = (stat.slowCount / stat.count * 100).toFixed(1)
      
      // Generate recommendations
      if (stat.slowPercentage > 20) {
        analysis.recommendations.push(`Collection '${collection}' has ${stat.slowPercentage}% slow queries - consider adding indexes`)
      }
      
      if (stat.averageTime > 200) {
        analysis.recommendations.push(`Collection '${collection}' average query time is ${stat.averageTime}ms - optimize queries`)
      }
    }
    
    return analysis
  }

  // Get real-time performance stats
  getStats() {
    return {
      performance: {
        totalQueries: this.queryStats.totalQueries,
        slowQueries: this.queryStats.slowQueries,
        slowQueryPercentage: this.queryStats.totalQueries > 0 ? 
          (this.queryStats.slowQueries / this.queryStats.totalQueries * 100).toFixed(1) + '%' : '0%',
        averageQueryTime: `${Math.round(this.queryStats.averageTime)}ms`,
        slowQueryThreshold: `${this.queryStats.slowQueryThreshold}ms`
      },
      recentQueries: this.queryStats.queries.slice(-10).map(q => ({
        collection: q.collection,
        method: q.method,
        duration: `${q.duration}ms`,
        timestamp: q.timestamp
      }))
    }
  }

  // Log comprehensive stats
  logStats() {
    const analysis = this.analyzeQueries()
    
    logger.info('📊 Query performance report', {
      summary: {
        totalQueries: analysis.totalQueries,
        slowQueryPercentage: analysis.slowQueryPercentage + '%',
        averageTime: analysis.averageTime + 'ms'
      },
      topCollections: Object.entries(analysis.collectionStats)
        .sort(([,a], [,b]) => b.count - a.count)
        .slice(0, 5)
        .map(([name, stats]) => ({
          collection: name,
          queries: stats.count,
          avgTime: stats.averageTime + 'ms',
          slowPercentage: stats.slowPercentage + '%'
        }))
    })
    
    if (analysis.recommendations.length > 0) {
      logger.warn('🎯 Query optimization recommendations', {
        recommendations: analysis.recommendations
      })
    }
  }

  // Health check for query performance
  healthCheck() {
    const slowQueryPercentage = this.queryStats.totalQueries > 0 ? 
      (this.queryStats.slowQueries / this.queryStats.totalQueries * 100) : 0
    
    const avgTime = this.queryStats.averageTime
    
    let status = 'healthy'
    if (slowQueryPercentage > 30 || avgTime > 300) {
      status = 'unhealthy'
    } else if (slowQueryPercentage > 15 || avgTime > 150) {
      status = 'degraded'
    }
    
    return {
      status,
      metrics: {
        slowQueryPercentage: slowQueryPercentage.toFixed(1) + '%',
        averageTime: Math.round(avgTime) + 'ms',
        totalQueries: this.queryStats.totalQueries
      }
    }
  }
}

// Export singleton instance
export const queryMonitor = new QueryMonitor()
export default queryMonitor