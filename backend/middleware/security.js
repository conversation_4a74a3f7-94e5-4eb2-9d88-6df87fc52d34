import crypto from 'crypto'
import { RateLimiterRedis } from 'rate-limiter-flexible'
import DOMPurify from 'isomorphic-dompurify'
import { z } from 'zod'

// Enhanced Security Middleware
export class SecurityManager {
  constructor(redisClient) {
    this.redisClient = redisClient
    this.setupRateLimiters()
  }

  setupRateLimiters() {
    // Login rate limiter
    this.loginLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'login_fail',
      points: 5, // 5 attempts
      duration: 900, // per 15 minutes
      blockDuration: 900, // block for 15 minutes
    })

    // Registration rate limiter
    this.registerLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'register_ip',
      points: 3, // 3 registrations
      duration: 3600, // per hour
      blockDuration: 3600,
    })

    // API rate limiter
    this.apiLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'api_call',
      points: 100, // 100 requests
      duration: 900, // per 15 minutes
      blockDuration: 60, // block for 1 minute
    })
  }

  // Secure input validation and sanitization
  validateAndSanitize(schema) {
    return (req, res, next) => {
      try {
        // Parse and validate with Zod
        const result = schema.safeParse(req.body)
        
        if (!result.success) {
          return res.status(400).json({
            success: false,
            message: 'Invalid input data',
            errors: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          })
        }

        // Sanitize strings to prevent XSS
        req.validatedBody = this.deepSanitize(result.data)
        next()
      } catch (error) {
        res.status(500).json({
          success: false,
          message: 'Validation error'
        })
      }
    }
  }

  deepSanitize(obj) {
    if (typeof obj === 'string') {
      return DOMPurify.sanitize(obj)
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitize(item))
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {}
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.deepSanitize(value)
      }
      return sanitized
    }
    
    return obj
  }

  // Rate limiting middleware
  rateLimitMiddleware(limiterType = 'api') {
    return async (req, res, next) => {
      try {
        const limiter = this[`${limiterType}Limiter`]
        const key = req.ip
        
        await limiter.consume(key)
        next()
      } catch (rateLimiterRes) {
        const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1
        res.set('Retry-After', String(secs))
        
        // Log security event
        this.logSecurityEvent('RATE_LIMIT_EXCEEDED', req, {
          limiterType,
          resetTime: new Date(Date.now() + rateLimiterRes.msBeforeNext)
        })
        
        res.status(429).json({
          success: false,
          message: `Too many requests. Try again in ${secs} seconds.`,
          retryAfter: secs
        })
      }
    }
  }

  // Security headers middleware
  securityHeaders() {
    return (req, res, next) => {
      // Set security headers
      res.setHeader('X-Content-Type-Options', 'nosniff')
      res.setHeader('X-Frame-Options', 'DENY')
      res.setHeader('X-XSS-Protection', '1; mode=block')
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
      res.setHeader('Content-Security-Policy', 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline' fonts.googleapis.com; " +
        "font-src 'self' fonts.gstatic.com; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self'"
      )
      
      next()
    }
  }

  // Enhanced authentication with refresh tokens
  enhancedAuth() {
    return async (req, res, next) => {
      try {
        const token = req.header('Authorization')?.replace('Bearer ', '')
        
        if (!token) {
          return res.status(401).json({
            success: false,
            message: 'Access denied. No token provided.'
          })
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET)
        
        // Check if token is blacklisted (for logout)
        const isBlacklisted = await this.redisClient.get(`blacklist:${token}`)
        if (isBlacklisted) {
          return res.status(401).json({
            success: false,
            message: 'Token has been revoked'
          })
        }

        // Get user from database
        const user = await User.findById(decoded.id).select('-password')
        if (!user || !user.isActive) {
          return res.status(401).json({
            success: false,
            message: 'User not found or inactive'
          })
        }

        req.user = user
        req.token = token
        next()
      } catch (error) {
        this.logSecurityEvent('INVALID_TOKEN', req, { error: error.message })
        
        res.status(401).json({
          success: false,
          message: 'Invalid token'
        })
      }
    }
  }

  // Log security events
  logSecurityEvent(event, req, details = {}) {
    const securityLog = {
      timestamp: new Date(),
      event,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method,
      userId: req.user?.id,
      ...details
    }

    console.warn(`🔒 SECURITY EVENT: ${event}`, securityLog)
    
    // In production, send to security monitoring service
    // this.sendToSecurityService(securityLog)
  }

  // Generate CSRF token
  generateCSRFToken() {
    return crypto.randomBytes(32).toString('hex')
  }

  // Validate CSRF token
  validateCSRF() {
    return (req, res, next) => {
      if (req.method === 'GET') return next()
      
      const token = req.header('X-CSRF-Token') || req.body._csrf
      const sessionToken = req.session?.csrfToken
      
      if (!token || !sessionToken || token !== sessionToken) {
        this.logSecurityEvent('CSRF_VALIDATION_FAILED', req)
        return res.status(403).json({
          success: false,
          message: 'CSRF token validation failed'
        })
      }
      
      next()
    }
  }
}

// Validation schemas
export const authSchemas = {
  register: z.object({
    name: z.string().min(2).max(50).trim(),
    email: z.string().email().toLowerCase(),
    password: z.string().min(8).max(128)
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      )
  }),
  
  login: z.object({
    email: z.string().email().toLowerCase(),
    password: z.string().min(1)
  }),
  
  emailSequence: z.object({
    title: z.string().min(1).max(100).trim(),
    businessInfo: z.object({
      industry: z.string().min(1).max(100),
      productService: z.string().min(1).max(500),
      targetAudience: z.string().min(1).max(500)
    }),
    generationSettings: z.object({
      sequenceLength: z.number().int().min(3).max(14),
      tone: z.enum(['professional', 'casual', 'friendly', 'urgent', 'helpful'])
    })
  })
}

export default SecurityManager