/**
 * Advanced Text Processor - Phase 2 Algorithmic Enhancement
 * Features: Trie-based template matching, String interning, Unicode optimization, Smart parsing
 * Performance Target: O(log n) template lookup, 80% memory reduction, sub-millisecond processing
 */

import { logger } from '../utils/logger.js'

class AdvancedTextProcessor {
  constructor() {
    // Trie structure for ultra-fast template matching
    this.templateTrie = new TemplateTrie()
    this.phraseIndexTrie = new Trie()
    
    // Advanced string interning pools
    this.globalStringPool = new AdvancedStringPool(20000)
    this.templateStringPool = new AdvancedStringPool(5000)
    this.commonPhrasePool = new AdvancedStringPool(10000)
    
    // Unicode optimization tables
    this.unicodeWidthTable = new Map()
    this.emojiTable = new Map()
    this.breakPointTable = new Map()
    
    // Template parsing cache with intelligent invalidation
    this.parseCache = new Map()
    this.parseCacheLimit = 1000
    
    // Performance optimization structures
    this.wordFrequency = new Map()
    this.phraseFrequency = new Map()
    this.templateUsageStats = new Map()
    
    // Background processing queues
    this.optimizationQueue = []
    this.preprocessingQueue = []
    
    // Performance metrics
    this.metrics = {
      templateMatches: 0,
      cacheHits: 0,
      cacheMisses: 0,
      stringInternings: 0,
      memoryOptimizations: 0,
      avgProcessingTime: 0,
      totalProcessed: 0
    }
    
    // Initialize with common patterns
    this.initializeCommonPatterns()
    this.startBackgroundOptimization()
  }

  /**
   * Ultra-fast template matching using Trie structure
   */
  findBestTemplate(businessInfo, settings) {
    const startTime = performance.now()
    
    // Create search key from business info
    const searchKey = this.createTemplateSearchKey(businessInfo, settings)
    
    // Trie-based template lookup O(log n)
    const matches = this.templateTrie.search(searchKey)
    
    if (matches.length > 0) {
      this.metrics.templateMatches++
      
      // Score and rank matches
      const scoredMatches = this.scoreTemplateMatches(matches, businessInfo, settings)
      const bestMatch = scoredMatches[0]
      
      // Update usage statistics
      this.updateTemplateUsage(bestMatch.template.id)
      
      this.updateProcessingTime(startTime)
      return bestMatch.template
    }
    
    this.updateProcessingTime(startTime)
    return this.getFallbackTemplate(businessInfo, settings)
  }

  /**
   * Advanced string interning with context awareness
   */
  internString(str, context = 'global') {
    if (typeof str !== 'string' || str.length === 0) return str
    
    const pool = this.getStringPool(context)
    const interned = pool.intern(str)
    
    if (interned === str && pool.hasEntry(str)) {
      this.metrics.stringInternings++
    }
    
    return interned
  }

  /**
   * Get appropriate string pool based on context
   */
  getStringPool(context) {
    switch (context) {
      case 'template': return this.templateStringPool
      case 'phrase': return this.commonPhrasePool
      default: return this.globalStringPool
    }
  }

  /**
   * Optimized text wrapping with intelligent break points
   */
  wrapTextIntelligently(text, maxWidth = 80, options = {}) {
    const startTime = performance.now()
    
    if (!text || typeof text !== 'string') return ''
    
    // Check cache first
    const cacheKey = `wrap:${text.length}:${maxWidth}:${JSON.stringify(options)}`
    if (this.parseCache.has(cacheKey)) {
      this.metrics.cacheHits++
      this.updateProcessingTime(startTime)
      return this.parseCache.get(cacheKey)
    }
    this.metrics.cacheMisses++
    
    const result = this.performIntelligentWrapping(text, maxWidth, options)
    
    // Cache result if under limit
    if (this.parseCache.size < this.parseCacheLimit) {
      this.parseCache.set(cacheKey, result)
    }
    
    this.updateProcessingTime(startTime)
    return result
  }

  /**
   * Perform intelligent text wrapping with advanced algorithms
   */
  performIntelligentWrapping(text, maxWidth, options) {
    const lines = []
    let currentLine = ''
    let currentWidth = 0
    
    // Tokenize text with smart break point detection
    const tokens = this.tokenizeWithBreakPoints(text)
    
    for (const token of tokens) {
      const tokenWidth = this.getDisplayWidth(token)
      
      // Check if token fits on current line
      if (currentWidth + tokenWidth <= maxWidth || currentLine === '') {
        currentLine += token
        currentWidth += tokenWidth
      } else {
        // Line break needed
        if (currentLine) {
          lines.push(currentLine.trim())
        }
        
        // Handle oversized tokens
        if (tokenWidth > maxWidth) {
          const brokenToken = this.breakOversizedToken(token, maxWidth)
          lines.push(...brokenToken.slice(0, -1))
          currentLine = brokenToken[brokenToken.length - 1] || ''
          currentWidth = this.getDisplayWidth(currentLine)
        } else {
          currentLine = token
          currentWidth = tokenWidth
        }
      }
    }
    
    if (currentLine) {
      lines.push(currentLine.trim())
    }
    
    return lines.join('\n')
  }

  /**
   * Smart tokenization with break point detection
   */
  tokenizeWithBreakPoints(text) {
    const tokens = []
    let currentToken = ''
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      const nextChar = text[i + 1]
      
      currentToken += char
      
      // Detect natural break points
      if (this.isBreakPoint(char, nextChar, i, text)) {
        if (currentToken.trim()) {
          tokens.push(currentToken)
        }
        currentToken = ''
      }
    }
    
    if (currentToken.trim()) {
      tokens.push(currentToken)
    }
    
    return tokens
  }

  /**
   * Determine if current position is a good break point
   */
  isBreakPoint(char, nextChar, position, text) {
    // Whitespace boundaries
    if (/\s/.test(char)) return true
    
    // Punctuation boundaries  
    if (/[.,;:!?]/.test(char) && /\s/.test(nextChar)) return true
    
    // URL/Path boundaries
    if ((char === '/' || char === '\\') && nextChar && !/\s/.test(nextChar)) return true
    
    // Camel case boundaries
    if (/[a-z]/.test(char) && /[A-Z]/.test(nextChar)) return true
    
    // Special character boundaries
    if (/[-_]/.test(char)) return true
    
    return false
  }

  /**
   * Get display width considering Unicode characters
   */
  getDisplayWidth(text) {
    if (!text) return 0
    
    let width = 0
    for (const char of text) {
      width += this.getCharacterWidth(char)
    }
    return width
  }

  /**
   * Get character width with Unicode support
   */
  getCharacterWidth(char) {
    const code = char.codePointAt(0)
    
    // Check cache first
    if (this.unicodeWidthTable.has(code)) {
      return this.unicodeWidthTable.get(code)
    }
    
    let width = 1
    
    // Wide characters (CJK, etc.)
    if (code >= 0x1100 && (
      code <= 0x115F ||  // Hangul Jamo
      code === 0x2329 || code === 0x232A ||
      (code >= 0x2E80 && code <= 0x3247) ||  // CJK
      (code >= 0x3250 && code <= 0x4DBF) ||
      (code >= 0x4E00 && code <= 0x9FFF) ||  // CJK Unified
      (code >= 0xA960 && code <= 0xA97F) ||
      (code >= 0xAC00 && code <= 0xD7A3) ||  // Hangul Syllables
      (code >= 0xF900 && code <= 0xFAFF) ||  // CJK Compatibility
      (code >= 0xFE30 && code <= 0xFE6F) ||
      (code >= 0xFF00 && code <= 0xFF60) ||  // Fullwidth Forms
      (code >= 0xFFE0 && code <= 0xFFE6) ||
      (code >= 0x20000 && code <= 0x2FFFD) ||
      (code >= 0x30000 && code <= 0x3FFFD)
    )) {
      width = 2
    }
    
    // Zero-width characters
    if (code >= 0x300 && code <= 0x36F) width = 0  // Combining marks
    if (code >= 0x1AB0 && code <= 0x1AFF) width = 0 // Combining Diacritical Marks Extended
    if (code >= 0x1DC0 && code <= 0x1DFF) width = 0 // Combining Diacritical Marks Supplement
    if (code >= 0x20D0 && code <= 0x20FF) width = 0 // Combining Diacritical Marks for Symbols
    if (code >= 0xFE20 && code <= 0xFE2F) width = 0 // Combining Half Marks
    
    // Cache result
    this.unicodeWidthTable.set(code, width)
    return width
  }

  /**
   * Break oversized tokens intelligently
   */
  breakOversizedToken(token, maxWidth) {
    const chunks = []
    let currentChunk = ''
    let currentWidth = 0
    
    for (const char of token) {
      const charWidth = this.getCharacterWidth(char)
      
      if (currentWidth + charWidth > maxWidth && currentChunk) {
        chunks.push(currentChunk)
        currentChunk = char
        currentWidth = charWidth
      } else {
        currentChunk += char
        currentWidth += charWidth
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk)
    }
    
    return chunks
  }

  /**
   * Create optimized template search key
   */
  createTemplateSearchKey(businessInfo, settings) {
    // Intern common strings to reduce memory usage
    const industry = this.internString(businessInfo.industry?.toLowerCase() || '', 'template')
    const tone = this.internString(settings.tone || '', 'template')
    const goal = this.internString(settings.primaryGoal || '', 'template')
    
    return {
      industry,
      tone,
      goal,
      sequenceLength: settings.sequenceLength || 5,
      productType: this.categorizeProduct(businessInfo.productService)
    }
  }

  /**
   * Score template matches using advanced algorithms
   */
  scoreTemplateMatches(matches, businessInfo, settings) {
    return matches.map(match => {
      let score = 0
      
      // Industry match scoring
      if (match.metadata.industry === businessInfo.industry?.toLowerCase()) {
        score += 30
      } else if (this.isRelatedIndustry(match.metadata.industry, businessInfo.industry)) {
        score += 15
      }
      
      // Tone compatibility
      if (match.metadata.tone === settings.tone) {
        score += 20
      }
      
      // Goal alignment  
      if (match.metadata.goal === settings.primaryGoal) {
        score += 25
      }
      
      // Sequence length preference
      const lengthDiff = Math.abs(match.metadata.sequenceLength - settings.sequenceLength)
      score += Math.max(0, 15 - lengthDiff * 2)
      
      // Usage frequency bonus
      const usageCount = this.templateUsageStats.get(match.template.id) || 0
      score += Math.min(usageCount * 0.5, 10)
      
      return { template: match.template, score, match }
    }).sort((a, b) => b.score - a.score)
  }

  /**
   * Initialize common patterns and templates
   */
  initializeCommonPatterns() {
    // Load common industry patterns
    const industries = [
      'technology', 'healthcare', 'finance', 'e-commerce', 'education',
      'consulting', 'real-estate', 'automotive', 'food', 'retail'
    ]
    
    industries.forEach(industry => {
      this.templateTrie.addPattern(industry, { type: 'industry', value: industry })
    })
    
    // Load common phrases
    const phrases = [
      'limited time offer', 'free trial', 'money back guarantee',
      'call to action', 'social proof', 'urgency', 'scarcity'
    ]
    
    phrases.forEach(phrase => {
      this.phraseIndexTrie.insert(phrase)
      this.internString(phrase, 'phrase')
    })
    
    // Precompute common Unicode characters
    for (let i = 32; i < 127; i++) {
      this.getCharacterWidth(String.fromCodePoint(i))
    }
  }

  /**
   * Background optimization processes
   */
  startBackgroundOptimization() {
    // Cleanup and optimize every 5 minutes
    setInterval(() => this.performBackgroundOptimization(), 300000)
    
    // Update frequency tables every minute
    setInterval(() => this.updateFrequencyTables(), 60000)
  }

  /**
   * Perform background optimization
   */
  performBackgroundOptimization() {
    // Optimize string pools
    this.optimizeStringPools()
    
    // Clean parse cache
    this.cleanParseCache()
    
    // Update Unicode tables
    this.optimizeUnicodeTables()
    
    this.metrics.memoryOptimizations++
  }

  /**
   * Optimize string pools by removing unused entries
   */
  optimizeStringPools() {
    [this.globalStringPool, this.templateStringPool, this.commonPhrasePool]
      .forEach(pool => pool.optimize())
  }

  /**
   * Clean parse cache using LRU strategy
   */
  cleanParseCache() {
    if (this.parseCache.size > this.parseCacheLimit * 1.2) {
      // Remove oldest 20% of entries
      const entries = Array.from(this.parseCache.entries())
      const toRemove = entries.slice(0, Math.floor(entries.length * 0.2))
      
      toRemove.forEach(([key]) => {
        this.parseCache.delete(key)
      })
    }
  }

  /**
   * Get comprehensive performance metrics
   */
  getPerformanceMetrics() {
    return {
      processing: {
        avgProcessingTime: this.metrics.avgProcessingTime.toFixed(3) + 'ms',
        totalProcessed: this.metrics.totalProcessed,
        templateMatches: this.metrics.templateMatches,
        cacheHitRate: Math.round((this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0) * 10000) / 100 + '%'
      },
      memory: {
        stringInternings: this.metrics.stringInternings,
        globalPoolSize: this.globalStringPool.size,
        templatePoolSize: this.templateStringPool.size,
        phrasePoolSize: this.commonPhrasePool.size,
        parseCacheSize: this.parseCache.size,
        unicodeTableSize: this.unicodeWidthTable.size,
        estimatedMemoryUsage: this.estimateMemoryUsage()
      },
      optimization: {
        memoryOptimizations: this.metrics.memoryOptimizations,
        templateTrieSize: this.templateTrie.size(),
        phraseTrieSize: this.phraseIndexTrie.size(),
        optimizationQueueSize: this.optimizationQueue.length
      }
    }
  }

  /**
   * Update processing time metrics
   */
  updateProcessingTime(startTime) {
    const processingTime = performance.now() - startTime
    this.metrics.totalProcessed++
    this.metrics.avgProcessingTime = (this.metrics.avgProcessingTime * (this.metrics.totalProcessed - 1) + processingTime) / this.metrics.totalProcessed
  }

  /**
   * Estimate total memory usage
   */
  estimateMemoryUsage() {
    const stringPoolMemory = this.globalStringPool.estimateMemory() + 
                           this.templateStringPool.estimateMemory() + 
                           this.commonPhrasePool.estimateMemory()
    const cacheMemory = this.parseCache.size * 200 // Rough estimate
    const unicodeMemory = this.unicodeWidthTable.size * 8
    const trieMemory = this.templateTrie.estimateMemory() + this.phraseIndexTrie.estimateMemory()
    
    return Math.round((stringPoolMemory + cacheMemory + unicodeMemory + trieMemory) / 1024) + 'KB'
  }

  // Placeholder methods
  categorizeProduct(productService) { return 'general' }
  isRelatedIndustry(industry1, industry2) { return false }
  getFallbackTemplate(businessInfo, settings) { return null }
  updateTemplateUsage(templateId) { /* Implementation */ }
  updateFrequencyTables() { /* Implementation */ }
  optimizeUnicodeTables() { /* Implementation */ }
}

/**
 * Trie structure for fast template matching
 */
class TemplateTrie {
  constructor() {
    this.root = { children: new Map(), templates: [], isEnd: false }
    this.templateCount = 0
  }

  addTemplate(template, searchKeys) {
    this.templateCount++
    
    searchKeys.forEach(key => {
      let node = this.root
      const keyString = this.serializeSearchKey(key)
      
      for (const char of keyString) {
        if (!node.children.has(char)) {
          node.children.set(char, { children: new Map(), templates: [], isEnd: false })
        }
        node = node.children.get(char)
      }
      
      node.isEnd = true
      node.templates.push({ template, metadata: key })
    })
  }

  search(searchKey) {
    const keyString = this.serializeSearchKey(searchKey)
    let node = this.root
    
    for (const char of keyString) {
      if (!node.children.has(char)) {
        return []
      }
      node = node.children.get(char)
    }
    
    return node.isEnd ? node.templates : []
  }

  addPattern(pattern, metadata) {
    let node = this.root
    
    for (const char of pattern) {
      if (!node.children.has(char)) {
        node.children.set(char, { children: new Map(), templates: [], isEnd: false })
      }
      node = node.children.get(char)
    }
    
    node.isEnd = true
    node.templates.push({ pattern, metadata })
  }

  serializeSearchKey(key) {
    return `${key.industry}:${key.tone}:${key.goal}:${key.sequenceLength}:${key.productType}`
  }

  size() {
    return this.templateCount
  }

  estimateMemory() {
    return this.templateCount * 150 // Rough estimate
  }
}

/**
 * Standard Trie for phrase indexing
 */
class Trie {
  constructor() {
    this.root = { children: new Map(), isEnd: false, data: null }
    this.wordCount = 0
  }

  insert(word) {
    let node = this.root
    
    for (const char of word.toLowerCase()) {
      if (!node.children.has(char)) {
        node.children.set(char, { children: new Map(), isEnd: false, data: null })
      }
      node = node.children.get(char)
    }
    
    if (!node.isEnd) {
      this.wordCount++
    }
    
    node.isEnd = true
    node.data = word
  }

  search(word) {
    let node = this.root
    
    for (const char of word.toLowerCase()) {
      if (!node.children.has(char)) {
        return false
      }
      node = node.children.get(char)
    }
    
    return node.isEnd
  }

  size() {
    return this.wordCount
  }

  estimateMemory() {
    return this.wordCount * 50 // Rough estimate
  }
}

/**
 * Advanced String Pool with optimization features
 */
class AdvancedStringPool {
  constructor(maxSize) {
    this.pool = new Map()
    this.usage = new Map()
    this.maxSize = maxSize
    this.hits = 0
    this.misses = 0
  }

  intern(str) {
    if (typeof str !== 'string') return str
    
    if (this.pool.has(str)) {
      this.hits++
      this.usage.set(str, (this.usage.get(str) || 0) + 1)
      return this.pool.get(str)
    }
    
    if (this.pool.size >= this.maxSize) {
      this.evictLeastUsed()
    }
    
    this.pool.set(str, str)
    this.usage.set(str, 1)
    this.misses++
    return str
  }

  hasEntry(str) {
    return this.pool.has(str)
  }

  evictLeastUsed() {
    let minUsage = Infinity
    let leastUsedKey = null
    
    for (const [key, usage] of this.usage.entries()) {
      if (usage < minUsage) {
        minUsage = usage
        leastUsedKey = key
      }
    }
    
    if (leastUsedKey) {
      this.pool.delete(leastUsedKey)
      this.usage.delete(leastUsedKey)
    }
  }

  optimize() {
    // Remove entries with very low usage
    const threshold = Math.max(1, Math.floor(this.maxSize * 0.1))
    const toRemove = []
    
    for (const [key, usage] of this.usage.entries()) {
      if (usage < threshold) {
        toRemove.push(key)
      }
    }
    
    toRemove.forEach(key => {
      this.pool.delete(key)
      this.usage.delete(key)
    })
  }

  get size() {
    return this.pool.size
  }

  estimateMemory() {
    let totalSize = 0
    for (const str of this.pool.keys()) {
      totalSize += str.length * 2 // UTF-16
    }
    return totalSize
  }

  getStats() {
    return {
      size: this.pool.size,
      maxSize: this.maxSize,
      hits: this.hits,
      misses: this.misses,
      hitRate: this.hits / (this.hits + this.misses) || 0
    }
  }
}

export default new AdvancedTextProcessor()