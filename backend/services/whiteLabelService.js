import CompleteUser from '../models/CompleteUser.js'
import { logger } from '../utils/logger.js'

class WhiteLabelService {
  constructor() {
    this.defaultBranding = {
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af',
      accentColor: '#3b82f6',
      logoUrl: '/images/sequenceai-logo.png',
      faviconUrl: '/images/favicon.ico',
      companyName: 'NeuroColony',
      supportEmail: '<EMAIL>',
      customDomain: null,
      customCss: '',
      footerText: 'Powered by NeuroColony',
      hidePoweredBy: false
    }
  }

  // Check if user has white-label access
  async hasWhiteLabelAccess(userId) {
    try {
      const user = await CompleteUser.findById(userId)
      if (!user) return false

      const allowedPlans = ['business', 'enterprise']
      return allowedPlans.includes(user.subscription.plan)
    } catch (error) {
      logger.error('White-label access check error:', error)
      return false
    }
  }

  // Get user's branding configuration
  async getBrandingConfig(userId) {
    try {
      const user = await CompleteUser.findById(userId)
      if (!user) throw new Error('User not found')

      // Check access
      if (!await this.hasWhiteLabelAccess(userId)) {
        return {
          ...this.defaultBranding,
          isPremium: false,
          upgradeRequired: true
        }
      }

      // Get custom branding or defaults
      const branding = user.branding || this.defaultBranding

      return {
        ...this.defaultBranding,
        ...branding,
        isPremium: true,
        upgradeRequired: false
      }
    } catch (error) {
      logger.error('Get branding config error:', error)
      throw error
    }
  }

  // Update user's branding configuration
  async updateBrandingConfig(userId, brandingData) {
    try {
      const user = await CompleteUser.findById(userId)
      if (!user) throw new Error('User not found')

      // Check access
      if (!await this.hasWhiteLabelAccess(userId)) {
        throw new Error('White-label features require Business or Enterprise plan')
      }

      // Validate branding data
      const validatedBranding = this.validateBrandingData(brandingData)
      
      // Update user branding
      user.branding = {
        ...user.branding,
        ...validatedBranding,
        updatedAt: new Date()
      }

      await user.save()

      logger.info('Branding config updated:', { userId, branding: validatedBranding })

      return user.branding
    } catch (error) {
      logger.error('Update branding config error:', error)
      throw error
    }
  }

  // Validate branding data
  validateBrandingData(data) {
    const validated = {}

    // Color validation (hex colors)
    if (data.primaryColor && /^#[0-9A-F]{6}$/i.test(data.primaryColor)) {
      validated.primaryColor = data.primaryColor
    }
    if (data.secondaryColor && /^#[0-9A-F]{6}$/i.test(data.secondaryColor)) {
      validated.secondaryColor = data.secondaryColor
    }
    if (data.accentColor && /^#[0-9A-F]{6}$/i.test(data.accentColor)) {
      validated.accentColor = data.accentColor
    }

    // URL validation
    if (data.logoUrl && this.isValidUrl(data.logoUrl)) {
      validated.logoUrl = data.logoUrl
    }
    if (data.faviconUrl && this.isValidUrl(data.faviconUrl)) {
      validated.faviconUrl = data.faviconUrl
    }

    // Text fields
    if (data.companyName && data.companyName.length <= 100) {
      validated.companyName = data.companyName.trim()
    }
    if (data.supportEmail && this.isValidEmail(data.supportEmail)) {
      validated.supportEmail = data.supportEmail.trim()
    }
    if (data.footerText && data.footerText.length <= 200) {
      validated.footerText = data.footerText.trim()
    }

    // Custom domain (requires verification)
    if (data.customDomain && this.isValidDomain(data.customDomain)) {
      validated.customDomain = data.customDomain.toLowerCase()
      validated.domainVerified = false // Requires verification
    }

    // Custom CSS (sanitized)
    if (data.customCss) {
      validated.customCss = this.sanitizeCSS(data.customCss)
    }

    // Boolean fields
    if (typeof data.hidePoweredBy === 'boolean') {
      validated.hidePoweredBy = data.hidePoweredBy
    }

    return validated
  }

  // Generate custom CSS theme
  async generateCustomTheme(userId) {
    try {
      const branding = await this.getBrandingConfig(userId)
      
      const customCSS = `
        :root {
          --primary-color: ${branding.primaryColor};
          --secondary-color: ${branding.secondaryColor};
          --accent-color: ${branding.accentColor};
        }

        .brand-primary {
          color: var(--primary-color);
        }
        
        .bg-brand-primary {
          background-color: var(--primary-color);
        }
        
        .border-brand-primary {
          border-color: var(--primary-color);
        }
        
        .brand-secondary {
          color: var(--secondary-color);
        }
        
        .bg-brand-secondary {
          background-color: var(--secondary-color);
        }
        
        .brand-accent {
          color: var(--accent-color);
        }
        
        .bg-brand-accent {
          background-color: var(--accent-color);
        }

        /* Button styling */
        .btn-brand-primary {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
          color: white;
        }
        
        .btn-brand-primary:hover {
          background-color: var(--secondary-color);
          border-color: var(--secondary-color);
        }

        /* Navigation styling */
        .navbar-brand {
          color: var(--primary-color) !important;
        }

        /* Card styling */
        .card-header-brand {
          background-color: var(--primary-color);
          color: white;
        }

        /* Link styling */
        .link-brand {
          color: var(--primary-color);
        }
        
        .link-brand:hover {
          color: var(--secondary-color);
        }

        /* Custom CSS from user */
        ${branding.customCss || ''}
      `

      return customCSS
    } catch (error) {
      logger.error('Generate custom theme error:', error)
      return ''
    }
  }

  // Get white-label email template
  async getWhiteLabelEmailTemplate(userId, templateType = 'default') {
    try {
      const branding = await this.getBrandingConfig(userId)
      
      const templates = {
        default: {
          headerHtml: `
            <div style="background-color: ${branding.primaryColor}; padding: 20px; text-align: center;">
              ${branding.logoUrl ? 
                `<img src="${branding.logoUrl}" alt="${branding.companyName}" style="max-height: 60px;">` :
                `<h1 style="color: white; margin: 0; font-size: 24px;">${branding.companyName}</h1>`
              }
            </div>
          `,
          footerHtml: `
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666;">
              <p style="margin: 0;">${branding.footerText}</p>
              ${!branding.hidePoweredBy ? 
                '<p style="margin: 5px 0 0 0;">Powered by NeuroColony</p>' : ''
              }
              <p style="margin: 5px 0 0 0;">
                Contact: <a href="mailto:${branding.supportEmail}">${branding.supportEmail}</a>
              </p>
            </div>
          `,
          primaryColor: branding.primaryColor,
          secondaryColor: branding.secondaryColor,
          accentColor: branding.accentColor
        },
        
        notification: {
          headerHtml: `
            <div style="background: linear-gradient(135deg, ${branding.primaryColor}, ${branding.secondaryColor}); padding: 15px; text-align: center;">
              <h2 style="color: white; margin: 0; font-size: 18px;">${branding.companyName}</h2>
            </div>
          `,
          footerHtml: `
            <div style="border-top: 1px solid #eee; padding: 15px; text-align: center; font-size: 11px; color: #999;">
              <p style="margin: 0;">This email was sent by ${branding.companyName}</p>
              ${!branding.hidePoweredBy ? 
                '<p style="margin: 5px 0 0 0;">Powered by NeuroColony</p>' : ''
              }
            </div>
          `
        }
      }

      return templates[templateType] || templates.default
    } catch (error) {
      logger.error('Get email template error:', error)
      return null
    }
  }

  // Setup custom subdomain
  async setupCustomSubdomain(userId, subdomain) {
    try {
      const user = await CompleteUser.findById(userId)
      if (!user) throw new Error('User not found')

      // Check access
      if (!await this.hasWhiteLabelAccess(userId)) {
        throw new Error('Custom subdomains require Business or Enterprise plan')
      }

      // Validate subdomain
      if (!this.isValidSubdomain(subdomain)) {
        throw new Error('Invalid subdomain format')
      }

      // Check if subdomain is available
      const existingUser = await CompleteUser.findOne({
        'branding.customSubdomain': subdomain
      })

      if (existingUser && existingUser._id.toString() !== userId) {
        throw new Error('Subdomain already taken')
      }

      // Update user with custom subdomain
      user.branding = user.branding || {}
      user.branding.customSubdomain = subdomain
      user.branding.subdomainStatus = 'pending'
      user.branding.subdomainSetupDate = new Date()

      await user.save()

      // In production, trigger DNS setup process
      await this.triggerSubdomainSetup(userId, subdomain)

      logger.info('Custom subdomain setup initiated:', { userId, subdomain })

      return {
        subdomain,
        status: 'pending',
        fullUrl: `https://${subdomain}.sequenceai.app`,
        setupTime: 'Usually ready within 5 minutes'
      }
    } catch (error) {
      logger.error('Setup custom subdomain error:', error)
      throw error
    }
  }

  // Get white-label dashboard configuration
  async getDashboardConfig(userId) {
    try {
      const branding = await this.getBrandingConfig(userId)
      
      return {
        title: `${branding.companyName} Email Marketing`,
        logoUrl: branding.logoUrl,
        primaryColor: branding.primaryColor,
        secondaryColor: branding.secondaryColor,
        navigation: {
          hideNeuroColonyBranding: branding.hidePoweredBy,
          customFooter: branding.footerText,
          supportEmail: branding.supportEmail
        },
        features: {
          customDomain: branding.customDomain,
          customSubdomain: branding.customSubdomain,
          whiteLabeled: true
        }
      }
    } catch (error) {
      logger.error('Get dashboard config error:', error)
      throw error
    }
  }

  // Generate white-label export options
  async getExportOptions(userId) {
    try {
      const hasAccess = await this.hasWhiteLabelAccess(userId)
      const branding = await this.getBrandingConfig(userId)

      if (!hasAccess) {
        return {
          hasAccess: false,
          upgradeRequired: true,
          message: 'White-label exports require Business or Enterprise plan'
        }
      }

      return {
        hasAccess: true,
        options: {
          removeNeuroColonyBranding: true,
          customBranding: true,
          customHeaders: branding.logoUrl ? true : false,
          customFooters: true,
          brandedPDFs: true,
          brandedCSVs: true,
          customEmailSignatures: true,
          clientReady: true
        },
        branding
      }
    } catch (error) {
      logger.error('Get export options error:', error)
      throw error
    }
  }

  // Helper methods
  isValidUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  isValidDomain(domain) {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
    return domainRegex.test(domain)
  }

  isValidSubdomain(subdomain) {
    const subdomainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$/
    return subdomainRegex.test(subdomain) && 
           subdomain.length >= 3 && 
           subdomain.length <= 63 &&
           !subdomain.includes('sequenceai') // Reserve brand terms
  }

  sanitizeCSS(css) {
    // Basic CSS sanitization - remove potentially dangerous content
    const dangerousPatterns = [
      /javascript:/gi,
      /expression\s*\(/gi,
      /behavior\s*:/gi,
      /binding\s*:/gi,
      /@import/gi,
      /url\s*\(\s*javascript:/gi
    ]

    let sanitized = css
    dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '')
    })

    return sanitized.substring(0, 10000) // Limit length
  }

  async triggerSubdomainSetup(userId, subdomain) {
    // In production, this would trigger:
    // 1. DNS record creation
    // 2. SSL certificate generation
    // 3. Load balancer configuration
    // 4. Health checks
    
    logger.info('Subdomain setup triggered:', { userId, subdomain })
    
    // Simulate setup delay
    setTimeout(async () => {
      try {
        const user = await CompleteUser.findById(userId)
        if (user && user.branding) {
          user.branding.subdomainStatus = 'active'
          await user.save()
          logger.info('Subdomain setup completed:', { userId, subdomain })
        }
      } catch (error) {
        logger.error('Subdomain setup completion error:', error)
      }
    }, 30000) // 30 seconds simulation
  }

  // Get white-label pricing
  getWhiteLabelPricing() {
    return {
      businessPlan: {
        price: 79,
        interval: 'month',
        features: [
          'Remove "Powered by NeuroColony" branding',
          'Custom colors and logo',
          'Custom email templates',
          'Branded exports (PDF, CSV)',
          'Custom footer text'
        ]
      },
      enterprisePlan: {
        price: 199,
        interval: 'month',
        features: [
          'All Business plan features',
          'Custom subdomain (your-brand.sequenceai.app)',
          'Custom domain support',
          'Advanced CSS customization',
          'White-label API documentation',
          'Dedicated support'
        ]
      },
      customDomainSetup: {
        oneTimeFee: 49,
        description: 'One-time setup fee for custom domain configuration'
      }
    }
  }
}

export default new WhiteLabelService()