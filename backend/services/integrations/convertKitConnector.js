import axios from 'axios'
import { logger } from '../../utils/logger.js'

class ConvertKitConnector {
  constructor() {
    this.baseUrl = 'https://api.convertkit.com/v3'
    this.name = 'convertkit'
    this.displayName = 'ConvertKit'
    this.description = 'Connect your ConvertKit account to sync email sequences and subscribers'
    this.features = [
      'Sync email sequences',
      'Import/export subscribers',
      'Manage tags and segments',
      'Create forms and landing pages',
      'Track subscriber engagement'
    ]
  }

  /**
   * Initialize connection with API secret
   */
  async connect(credentials) {
    try {
      const { apiSecret } = credentials
      
      // Test connection and get account info
      const response = await this.makeRequest('GET', '/account', {
        api_secret: apiSecret
      })

      return {
        connected: true,
        accountInfo: {
          name: response.name,
          email: response.primary_email_address,
          planType: response.plan_type,
          subscriberCount: response.subscriber_count
        },
        metadata: {
          apiVersion: 'v3'
        }
      }

    } catch (error) {
      logger.error('ConvertKit connection error:', error)
      throw new Error(`Failed to connect to ConvertKit: ${error.message}`)
    }
  }

  /**
   * Get all sequences (courses in ConvertKit)
   */
  async getSequences(apiSecret) {
    try {
      const response = await this.makeRequest('GET', '/sequences', {
        api_secret: apiSecret
      })

      return response.courses.map(course => ({
        id: course.id,
        name: course.name,
        subscriberCount: course.subscriber_count || 0,
        createdAt: course.created_at
      }))

    } catch (error) {
      logger.error('Error fetching ConvertKit sequences:', error)
      throw error
    }
  }

  /**
   * Create a sequence from email sequence
   */
  async createSequence(apiSecret, sequence) {
    try {
      // Create the sequence (course)
      const course = await this.makeRequest('POST', '/sequences', {
        api_secret: apiSecret,
        name: sequence.name,
        description: sequence.description || ''
      })

      // Add emails to the sequence
      for (let i = 0; i < sequence.emails.length; i++) {
        const email = sequence.emails[i]
        
        await this.makeRequest('POST', `/sequences/${course.course.id}/emails`, {
          api_secret: apiSecret,
          email: {
            subject: email.subject,
            content: this.generateEmailContent(email),
            published: true,
            send_after: email.delay || i, // Days after subscription
            thumbnail_url: null,
            description: email.preview || ''
          }
        })
      }

      return {
        sequenceId: course.course.id,
        name: course.course.name,
        emailCount: sequence.emails.length
      }

    } catch (error) {
      logger.error('Error creating ConvertKit sequence:', error)
      throw error
    }
  }

  /**
   * Get all forms
   */
  async getForms(apiSecret) {
    try {
      const response = await this.makeRequest('GET', '/forms', {
        api_secret: apiSecret
      })

      return response.forms.map(form => ({
        id: form.id,
        name: form.name,
        type: form.type,
        subscriberCount: form.subscriber_count || 0,
        conversionRate: form.conversion_percentage || 0,
        visits: form.visits || 0
      }))

    } catch (error) {
      logger.error('Error fetching ConvertKit forms:', error)
      throw error
    }
  }

  /**
   * Get all tags
   */
  async getTags(apiSecret) {
    try {
      const response = await this.makeRequest('GET', '/tags', {
        api_secret: apiSecret
      })

      return response.tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        subscriberCount: tag.subscriber_count || 0,
        createdAt: tag.created_at
      }))

    } catch (error) {
      logger.error('Error fetching ConvertKit tags:', error)
      throw error
    }
  }

  /**
   * Create or update subscriber
   */
  async upsertSubscriber(apiSecret, subscriber) {
    try {
      const response = await this.makeRequest('POST', '/subscribers', {
        api_secret: apiSecret,
        email: subscriber.email,
        first_name: subscriber.firstName || '',
        fields: subscriber.customFields || {},
        tags: subscriber.tags || []
      })

      return {
        subscriberId: response.subscriber.id,
        email: response.subscriber.email_address,
        state: response.subscriber.state,
        createdAt: response.subscriber.created_at
      }

    } catch (error) {
      logger.error('Error creating ConvertKit subscriber:', error)
      throw error
    }
  }

  /**
   * Add subscriber to sequence
   */
  async addSubscriberToSequence(apiSecret, email, sequenceId) {
    try {
      const response = await this.makeRequest('POST', `/sequences/${sequenceId}/subscribe`, {
        api_secret: apiSecret,
        email: email
      })

      return {
        success: true,
        subscriber: response.subscriber
      }

    } catch (error) {
      logger.error('Error adding subscriber to sequence:', error)
      throw error
    }
  }

  /**
   * Tag a subscriber
   */
  async tagSubscriber(apiSecret, email, tagId) {
    try {
      const response = await this.makeRequest('POST', `/tags/${tagId}/subscribe`, {
        api_secret: apiSecret,
        email: email
      })

      return {
        success: true,
        subscription: response.subscription
      }

    } catch (error) {
      logger.error('Error tagging subscriber:', error)
      throw error
    }
  }

  /**
   * Get subscriber by email
   */
  async getSubscriber(apiSecret, email) {
    try {
      const response = await this.makeRequest('GET', '/subscribers', {
        api_secret: apiSecret,
        email_address: email
      })

      if (response.total_subscribers === 0) {
        return null
      }

      const subscriber = response.subscribers[0]
      return {
        id: subscriber.id,
        email: subscriber.email_address,
        firstName: subscriber.first_name,
        state: subscriber.state,
        tags: subscriber.tags || [],
        customFields: subscriber.fields || {},
        createdAt: subscriber.created_at
      }

    } catch (error) {
      logger.error('Error fetching ConvertKit subscriber:', error)
      throw error
    }
  }

  /**
   * Create a broadcast (one-time email)
   */
  async createBroadcast(apiSecret, email, recipientFilter = {}) {
    try {
      const response = await this.makeRequest('POST', '/broadcasts', {
        api_secret: apiSecret,
        subject: email.subject,
        content: this.generateEmailContent(email),
        description: email.preview || '',
        published: true,
        send_at: email.scheduledAt || null,
        // Recipient filters
        email_address: recipientFilter.email || null,
        tag_ids: recipientFilter.tagIds || null,
        segment_ids: recipientFilter.segmentIds || null
      })

      return {
        broadcastId: response.broadcast.id,
        subject: response.broadcast.subject,
        status: response.broadcast.published ? 'scheduled' : 'draft',
        scheduledAt: response.broadcast.send_at
      }

    } catch (error) {
      logger.error('Error creating ConvertKit broadcast:', error)
      throw error
    }
  }

  /**
   * Get broadcast statistics
   */
  async getBroadcastStats(apiSecret, broadcastId) {
    try {
      const response = await this.makeRequest('GET', `/broadcasts/${broadcastId}/stats`, {
        api_secret: apiSecret
      })

      return {
        recipients: response.broadcast.total_recipients,
        opens: response.broadcast.open_tracking_count,
        clicks: response.broadcast.link_tracking_count,
        unsubscribes: response.broadcast.unsubscribes,
        bounces: response.broadcast.bounces,
        complaints: response.broadcast.complaints
      }

    } catch (error) {
      logger.error('Error fetching broadcast stats:', error)
      throw error
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(event) {
    try {
      logger.info(`Processing ConvertKit webhook: ${event.name}`)

      switch (event.name) {
        case 'subscriber.subscriber_activate':
          return {
            type: 'subscriber.confirmed',
            data: {
              subscriberId: event.subscriber.id,
              email: event.subscriber.email_address,
              confirmedAt: event.occurred_at
            }
          }

        case 'subscriber.subscriber_unsubscribe':
          return {
            type: 'subscriber.unsubscribed',
            data: {
              subscriberId: event.subscriber.id,
              email: event.subscriber.email_address,
              unsubscribedAt: event.occurred_at
            }
          }

        case 'subscriber.tag_add':
          return {
            type: 'subscriber.tagged',
            data: {
              subscriberId: event.subscriber.id,
              email: event.subscriber.email_address,
              tagId: event.tag.id,
              tagName: event.tag.name
            }
          }

        case 'purchase.purchase_create':
          return {
            type: 'purchase.created',
            data: {
              purchaseId: event.purchase.id,
              subscriberId: event.purchase.subscriber.id,
              amount: event.purchase.amount,
              currency: event.purchase.currency,
              productName: event.purchase.product.name
            }
          }

        default:
          return {
            type: `convertkit.${event.name}`,
            data: event
          }
      }

    } catch (error) {
      logger.error('Error handling ConvertKit webhook:', error)
      throw error
    }
  }

  /**
   * Make API request
   */
  async makeRequest(method, endpoint, params = {}) {
    const url = `${this.baseUrl}${endpoint}`

    try {
      const response = await axios({
        method,
        url,
        params: method === 'GET' ? params : {},
        data: method !== 'GET' ? params : {},
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response.data

    } catch (error) {
      if (error.response) {
        const errorMessage = error.response.data.error || error.response.data.message || 'Unknown error'
        throw new Error(`ConvertKit API error: ${errorMessage}`)
      }
      throw error
    }
  }

  /**
   * Generate email content HTML
   */
  generateEmailContent(email) {
    const ctaHtml = email.cta ? 
      `<table border="0" cellpadding="0" cellspacing="0" style="margin: 30px 0;">
        <tr>
          <td align="center" bgcolor="#FF6B6B" style="border-radius: 4px;">
            <a href="{{ subscriber.cta_url }}" target="_blank" style="display: inline-block; padding: 16px 32px; font-family: Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 4px;">${email.cta}</a>
          </td>
        </tr>
      </table>` : ''

    return `
<div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
  ${email.body.split('\n').map(p => `<p style="margin: 16px 0;">${p}</p>`).join('')}
  ${ctaHtml}
  <p style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
    You received this email because you're subscribed to {{ subscriber.first_name | default: "our list" }}.
    <a href="{{ unsubscribe_url }}" style="color: #666;">Unsubscribe</a>
  </p>
</div>`
  }

  /**
   * Validate webhook signature (ConvertKit doesn't use signatures by default)
   */
  validateWebhookSignature(rawBody, signature, secret) {
    // ConvertKit doesn't provide webhook signatures by default
    // You can implement IP whitelisting or custom validation here
    return true
  }
}

export default new ConvertKitConnector()