import axios from 'axios'
import crypto from 'crypto'
import { logger } from '../../utils/logger.js'

class MailchimpConnector {
  constructor() {
    this.baseUrl = 'https://{dc}.api.mailchimp.com/3.0'
    this.name = 'mailchimp'
    this.displayName = 'Mailchimp'
    this.description = 'Connect your Mailchimp account to sync email sequences and subscribers'
    this.features = [
      'Sync email sequences as campaigns',
      'Import/export subscribers',
      'Track email performance',
      'Manage lists and segments',
      'Automation workflows'
    ]
  }

  /**
   * Initialize connection with API key
   */
  async connect(credentials) {
    try {
      const { apiKey } = credentials
      
      // Extract datacenter from API key
      const dc = apiKey.split('-')[1]
      if (!dc) {
        throw new Error('Invalid API key format')
      }

      // Test connection
      const response = await this.makeRequest('GET', '/ping', null, apiKey)
      
      if (response.health_status !== 'Everything\'s Chimpy!') {
        throw new Error('Failed to connect to Mailchimp')
      }

      // Get account info
      const accountInfo = await this.makeRequest('GET', '/', null, apiKey)

      return {
        connected: true,
        accountInfo: {
          accountId: accountInfo.account_id,
          accountName: accountInfo.account_name,
          email: accountInfo.email,
          totalSubscribers: accountInfo.total_subscribers
        },
        metadata: {
          datacenter: dc,
          apiVersion: '3.0'
        }
      }

    } catch (error) {
      logger.error('Mailchimp connection error:', error)
      throw new Error(`Failed to connect to Mailchimp: ${error.message}`)
    }
  }

  /**
   * Get all lists
   */
  async getLists(apiKey) {
    try {
      const response = await this.makeRequest('GET', '/lists', {
        count: 100,
        fields: 'lists.id,lists.name,lists.stats'
      }, apiKey)

      return response.lists.map(list => ({
        id: list.id,
        name: list.name,
        memberCount: list.stats.member_count,
        campaignCount: list.stats.campaign_count,
        openRate: list.stats.open_rate,
        clickRate: list.stats.click_rate
      }))

    } catch (error) {
      logger.error('Error fetching Mailchimp lists:', error)
      throw error
    }
  }

  /**
   * Create a campaign from email sequence
   */
  async createCampaign(apiKey, sequence, listId, settings = {}) {
    try {
      // Create campaign
      const campaign = await this.makeRequest('POST', '/campaigns', {
        type: 'regular',
        recipients: {
          list_id: listId,
          segment_opts: settings.segmentOpts
        },
        settings: {
          subject_line: sequence.emails[0].subject,
          preview_text: sequence.emails[0].preview || '',
          title: sequence.name,
          from_name: settings.fromName || 'Your Company',
          reply_to: settings.replyTo || '<EMAIL>',
          authenticate: true,
          auto_footer: false,
          inline_css: true
        },
        tracking: {
          opens: true,
          html_clicks: true,
          text_clicks: true,
          goal_tracking: false,
          ecomm360: false
        }
      }, apiKey)

      // Set campaign content
      await this.makeRequest('PUT', `/campaigns/${campaign.id}/content`, {
        html: this.generateHtmlContent(sequence.emails[0]),
        plain_text: sequence.emails[0].body
      }, apiKey)

      return {
        campaignId: campaign.id,
        webId: campaign.web_id,
        status: campaign.status,
        createTime: campaign.create_time
      }

    } catch (error) {
      logger.error('Error creating Mailchimp campaign:', error)
      throw error
    }
  }

  /**
   * Create automation workflow from email sequence
   */
  async createAutomation(apiKey, sequence, listId, settings = {}) {
    try {
      // Create automation workflow
      const automation = await this.makeRequest('POST', '/automations', {
        recipients: {
          list_id: listId
        },
        settings: {
          title: sequence.name,
          from_name: settings.fromName || 'Your Company',
          reply_to: settings.replyTo || '<EMAIL>'
        },
        trigger_settings: {
          workflow_type: settings.triggerType || 'signup',
          send_immediately: false,
          trigger_on_import: true,
          runtime: {
            days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
            hours: {
              send_at: '10:00'
            }
          }
        }
      }, apiKey)

      // Add emails to automation
      for (let i = 0; i < sequence.emails.length; i++) {
        const email = sequence.emails[i]
        
        await this.makeRequest('POST', `/automations/${automation.id}/emails`, {
          settings: {
            subject_line: email.subject,
            preview_text: email.preview || '',
            title: email.title || `Email ${i + 1}`,
            from_name: settings.fromName || 'Your Company',
            reply_to: settings.replyTo || '<EMAIL>'
          },
          delay: {
            amount: email.delay || i,
            type: 'day',
            direction: 'after',
            action: i === 0 ? 'signup' : 'previous_campaign_sent'
          },
          content: {
            html: this.generateHtmlContent(email),
            plain_text: email.body
          }
        }, apiKey)
      }

      return {
        automationId: automation.id,
        status: automation.status,
        emailCount: sequence.emails.length,
        createTime: automation.create_time
      }

    } catch (error) {
      logger.error('Error creating Mailchimp automation:', error)
      throw error
    }
  }

  /**
   * Sync subscribers
   */
  async syncSubscribers(apiKey, listId, subscribers) {
    try {
      const operations = subscribers.map(subscriber => ({
        method: 'PUT',
        path: `/lists/${listId}/members/${this.getSubscriberHash(subscriber.email)}`,
        body: JSON.stringify({
          email_address: subscriber.email,
          status: subscriber.status || 'subscribed',
          merge_fields: {
            FNAME: subscriber.firstName || '',
            LNAME: subscriber.lastName || ''
          },
          tags: subscriber.tags || []
        })
      }))

      // Batch operation
      const response = await this.makeRequest('POST', '/batches', {
        operations
      }, apiKey)

      return {
        batchId: response.id,
        status: response.status,
        totalOperations: response.total_operations
      }

    } catch (error) {
      logger.error('Error syncing subscribers to Mailchimp:', error)
      throw error
    }
  }

  /**
   * Get campaign reports
   */
  async getCampaignReports(apiKey, campaignId) {
    try {
      const report = await this.makeRequest('GET', `/reports/${campaignId}`, null, apiKey)

      return {
        sends: report.emails_sent,
        opens: {
          count: report.opens.opens_total,
          rate: report.opens.open_rate,
          uniqueOpens: report.opens.unique_opens
        },
        clicks: {
          count: report.clicks.clicks_total,
          rate: report.clicks.click_rate,
          uniqueClicks: report.clicks.unique_clicks
        },
        bounces: {
          hard: report.bounces.hard_bounces,
          soft: report.bounces.soft_bounces,
          total: report.bounces.hard_bounces + report.bounces.soft_bounces
        },
        unsubscribes: report.unsubscribed
      }

    } catch (error) {
      logger.error('Error fetching campaign reports:', error)
      throw error
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(event, data) {
    try {
      logger.info(`Processing Mailchimp webhook: ${event}`)

      switch (event) {
        case 'subscribe':
          return {
            type: 'subscriber.added',
            data: {
              email: data.email,
              listId: data.list_id,
              mergeFields: data.merges
            }
          }

        case 'unsubscribe':
          return {
            type: 'subscriber.removed',
            data: {
              email: data.email,
              listId: data.list_id,
              reason: data.reason
            }
          }

        case 'campaign':
          return {
            type: 'campaign.sent',
            data: {
              campaignId: data.id,
              subject: data.subject,
              sentTo: data.emails_sent
            }
          }

        default:
          return {
            type: `mailchimp.${event}`,
            data
          }
      }

    } catch (error) {
      logger.error('Error handling Mailchimp webhook:', error)
      throw error
    }
  }

  /**
   * Make API request
   */
  async makeRequest(method, endpoint, data, apiKey) {
    const dc = apiKey.split('-')[1]
    const url = this.baseUrl.replace('{dc}', dc) + endpoint

    try {
      const response = await axios({
        method,
        url,
        data,
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      return response.data

    } catch (error) {
      if (error.response) {
        const errorMessage = error.response.data.detail || error.response.data.error || 'Unknown error'
        throw new Error(`Mailchimp API error: ${errorMessage}`)
      }
      throw error
    }
  }

  /**
   * Generate HTML content from email
   */
  generateHtmlContent(email) {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${email.subject}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .cta { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
    .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    ${email.body.split('\n').map(p => `<p>${p}</p>`).join('')}
    ${email.cta ? `<a href="{CTA_URL}" class="cta">${email.cta}</a>` : ''}
    <div class="footer">
      <p>You received this email because you subscribed to our list.</p>
      <p><a href="*|UNSUB|*">Unsubscribe</a> | <a href="*|UPDATE_PROFILE|*">Update preferences</a></p>
    </div>
  </div>
</body>
</html>`
  }

  /**
   * Get subscriber hash (MD5 of lowercase email)
   */
  getSubscriberHash(email) {
    return crypto.createHash('md5').update(email.toLowerCase()).digest('hex')
  }

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(rawBody, signature, secret) {
    const hash = crypto
      .createHmac('sha256', secret)
      .update(rawBody)
      .digest('hex')
    
    return hash === signature
  }
}

export default new MailchimpConnector()