import ColonyAgent from '../models/ColonyAgent.js'
import { logger } from '../utils/logger.js'

/**
 * NeuroColony Agent Blueprints
 * Pre-built neural network blueprints for marketing automation colonies
 * Superior to n8n with AI-powered intelligence and swarm optimization
 */

class ColonyBlueprints {
  constructor() {
    this.blueprints = new Map()
    this.initializeBlueprints()
  }

  /**
   * Initialize all colony agent blueprints
   */
  initializeBlueprints() {
    // Communication Colony Agents
    this.addBlueprint('email_sequence_generator', this.createEmailSequenceGenerator())
    this.addBlueprint('subject_line_optimizer', this.createSubjectLineOptimizer())
    this.addBlueprint('email_personalization_agent', this.createEmailPersonalizationAgent())

    // Evolution Colony Agents
    this.addBlueprint('social_media_scheduler', this.createSocialMediaScheduler())

    // Intelligence Colony Agents
    this.addBlueprint('performance_analyzer', this.createPerformanceAnalyzer())

    // Automation Colony Agents
    this.addBlueprint('lead_magnet_creator', this.createLeadMagnetCreator())

    // Communication Colony Agents (Advanced)
    this.addBlueprint('social_engagement_monitor', this.createSocialEngagementMonitor())

    // Intelligence Colony Agents (Advanced)
    this.addBlueprint('lead_scoring_agent', this.createLeadScoringAgent())

    // Intelligence Colony Agents (Analytics)
    this.addBlueprint('attribution_analyzer', this.createAttributionAnalyzer())

    // Automation Colony Agents (E-commerce)
    this.addBlueprint('abandoned_cart_recovery', this.createAbandonedCartRecovery())

    logger.info(`🧬 Initialized ${this.blueprints.size} colony agent blueprints`)
  }

  addBlueprint(key, blueprint) {
    this.blueprints.set(key, blueprint)
  }

  getBlueprint(key) {
    return this.blueprints.get(key)
  }

  getAllBlueprints() {
    return Array.from(this.blueprints.entries()).map(([key, blueprint]) => ({
      key,
      ...blueprint
    }))
  }

  /**
   * Create Email Sequence Generator Agent Blueprint
   */
  createEmailSequenceGenerator() {
    return {
      designation: 'Email Sequence Generator Colony',
      purpose: 'Orchestrates multi-channel communication sequences with neural network optimization',
      colonyType: 'communication',
      neuralComplexity: 8,
      swarmSize: 5,
      synapticStrength: 92,
      evolutionStage: 'advanced',
      capabilities: [
        {
          name: 'sequence_orchestration',
          description: 'Coordinates multi-email sequences with timing and personalization',
          inputSchema: {
            type: 'object',
            properties: {
              businessInfo: { type: 'object' },
              sequenceType: { type: 'string' },
              emailCount: { type: 'number' },
              targetAudience: { type: 'string' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              sequence: { type: 'array' },
              performance: { type: 'object' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'manual', config: {} },
          { type: 'webhook', config: { endpoint: '/trigger/email-sequence' } },
          { type: 'schedule', config: { cron: '0 9 * * *' } }
        ],
        actions: [
          { type: 'generate_sequence', config: { aiProvider: 'multi-ai' } },
          { type: 'coordinate_workers', config: {} },
          { type: 'schedule_delivery', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are an Email Marketing Queen agent. Orchestrate high-converting email sequences by coordinating with worker agents for content creation, personalization, and optimization.',
        marketingContext: {
          objectives: ['engagement', 'conversion', 'retention'],
          kpis: ['open_rate', 'click_rate', 'conversion_rate']
        }
      }
    }
  }

  /**
   * Create Subject Line Optimizer Agent Blueprint
   */
  createSubjectLineOptimizer() {
    return {
      designation: 'Subject Line Optimizer Neural Network',
      purpose: 'Generates and evolves communication headers for maximum engagement',
      colonyType: 'evolution',
      neuralComplexity: 6,
      swarmSize: 3,
      synapticStrength: 88,
      evolutionStage: 'advanced',
      capabilities: [
        {
          name: 'subject_generation',
          description: 'Creates multiple subject line variations using psychology and AI',
          inputSchema: {
            type: 'object',
            properties: {
              emailContent: { type: 'string' },
              targetAudience: { type: 'string' },
              tone: { type: 'string' },
              industry: { type: 'string' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              subjects: { type: 'array' },
              recommendations: { type: 'object' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'agent_signal', config: { signal: 'subject_needed' } }
        ],
        actions: [
          { type: 'generate_subjects', config: { count: 5 } },
          { type: 'analyze_psychology', config: {} },
          { type: 'recommend_best', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Subject Line Optimization specialist. Create compelling, psychology-driven email subject lines that maximize open rates while avoiding spam filters.',
        marketingContext: {
          objectives: ['open_rate_optimization'],
          techniques: ['curiosity', 'urgency', 'personalization', 'benefit_driven']
        }
      }
    }
  }

  /**
   * Create Email Personalization Agent (Worker)
   */
  createEmailPersonalizationAgent() {
    return {
      name: 'Email Personalization Worker',
      description: 'Personalizes email content based on user data and behavior',
      agentType: 'worker',
      category: 'email_marketing',
      capabilities: [
        {
          name: 'content_personalization',
          description: 'Customizes email content for individual recipients',
          inputSchema: {
            type: 'object',
            properties: {
              template: { type: 'string' },
              userData: { type: 'object' },
              behaviorData: { type: 'object' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              personalizedContent: { type: 'string' },
              personalizationScore: { type: 'number' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'agent_signal', config: { signal: 'personalization_needed' } }
        ],
        actions: [
          { type: 'analyze_user_data', config: {} },
          { type: 'personalize_content', config: {} },
          { type: 'validate_personalization', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are an Email Personalization specialist. Create highly personalized email content that resonates with individual recipients based on their data and behavior.',
        marketingContext: {
          objectives: ['engagement', 'relevance', 'conversion'],
          personalizationTypes: ['name', 'location', 'behavior', 'preferences', 'purchase_history']
        }
      }
    }
  }

  /**
   * Create Performance Analyzer Agent (Scout)
   */
  createPerformanceAnalyzer() {
    return {
      name: 'Performance Analyzer Scout',
      description: 'Monitors and analyzes email campaign performance across all metrics',
      agentType: 'scout',
      category: 'analytics_reporting',
      capabilities: [
        {
          name: 'performance_monitoring',
          description: 'Tracks email metrics and identifies optimization opportunities',
          inputSchema: {
            type: 'object',
            properties: {
              campaignId: { type: 'string' },
              timeframe: { type: 'string' },
              metrics: { type: 'array' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              performance: { type: 'object' },
              insights: { type: 'array' },
              recommendations: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'schedule', config: { interval: '1h' } },
          { type: 'condition', config: { metric: 'open_rate', threshold: 0.15 } }
        ],
        actions: [
          { type: 'collect_metrics', config: {} },
          { type: 'analyze_performance', config: {} },
          { type: 'generate_insights', config: {} },
          { type: 'alert_queen', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Performance Analysis scout. Monitor email campaign metrics, identify trends, and provide actionable insights to optimize marketing performance.',
        marketingContext: {
          objectives: ['performance_optimization', 'insight_generation'],
          metrics: ['open_rate', 'click_rate', 'conversion_rate', 'unsubscribe_rate', 'spam_rate']
        }
      }
    }
  }

  /**
   * Create Lead Magnet Creator Agent (Worker)
   */
  createLeadMagnetCreator() {
    return {
      name: 'Lead Magnet Creator Worker',
      description: 'Creates compelling lead magnets and opt-in incentives',
      agentType: 'worker',
      category: 'lead_generation',
      capabilities: [
        {
          name: 'lead_magnet_generation',
          description: 'Creates various types of lead magnets based on audience and industry',
          inputSchema: {
            type: 'object',
            properties: {
              industry: { type: 'string' },
              targetAudience: { type: 'string' },
              painPoints: { type: 'array' },
              format: { type: 'string' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              leadMagnet: { type: 'object' },
              landingPageCopy: { type: 'string' },
              emailSequence: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'manual', config: {} },
          { type: 'agent_signal', config: { signal: 'lead_magnet_needed' } }
        ],
        actions: [
          { type: 'research_audience', config: {} },
          { type: 'create_lead_magnet', config: {} },
          { type: 'generate_landing_copy', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Lead Magnet Creation specialist. Design irresistible lead magnets that solve specific problems and capture high-quality leads.',
        marketingContext: {
          objectives: ['lead_generation', 'value_delivery'],
          formats: ['ebook', 'checklist', 'template', 'webinar', 'course', 'toolkit']
        }
      }
    }
  }

  /**
   * Create Social Media Scheduler Agent (Worker)
   */
  createSocialMediaScheduler() {
    return {
      name: 'Social Media Scheduler Worker',
      description: 'Schedules and publishes content across multiple social media platforms',
      agentType: 'worker',
      category: 'social_media',
      capabilities: [
        {
          name: 'multi_platform_posting',
          description: 'Posts content to Facebook, LinkedIn, Twitter, Instagram simultaneously',
          inputSchema: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              platforms: { type: 'array' },
              scheduleTime: { type: 'string' },
              hashtags: { type: 'array' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              postIds: { type: 'object' },
              scheduledTime: { type: 'string' },
              platforms: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'schedule', config: { cron: '0 9,12,15,18 * * *' } },
          { type: 'agent_signal', config: { signal: 'content_ready' } }
        ],
        actions: [
          { type: 'schedule_posts', config: {} },
          { type: 'optimize_timing', config: {} },
          { type: 'track_engagement', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Social Media Scheduling specialist. Optimize posting times, adapt content for each platform, and maximize engagement across all social channels.',
        marketingContext: {
          objectives: ['engagement', 'reach', 'brand_awareness'],
          platforms: ['facebook', 'linkedin', 'twitter', 'instagram']
        }
      }
    }
  }

  /**
   * Create Social Engagement Monitor Agent (Scout)
   */
  createSocialEngagementMonitor() {
    return {
      name: 'Social Engagement Monitor Scout',
      description: 'Monitors social media engagement and identifies trending conversations',
      agentType: 'scout',
      category: 'social_media',
      capabilities: [
        {
          name: 'engagement_tracking',
          description: 'Tracks likes, comments, shares, and mentions across platforms',
          inputSchema: {
            type: 'object',
            properties: {
              platforms: { type: 'array' },
              keywords: { type: 'array' },
              timeframe: { type: 'string' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              engagementMetrics: { type: 'object' },
              trendingTopics: { type: 'array' },
              influencerMentions: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'schedule', config: { interval: '30m' } },
          { type: 'condition', config: { metric: 'mention_spike', threshold: 10 } }
        ],
        actions: [
          { type: 'monitor_mentions', config: {} },
          { type: 'analyze_sentiment', config: {} },
          { type: 'identify_trends', config: {} },
          { type: 'alert_team', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Social Media Intelligence scout. Monitor conversations, identify trends, and provide actionable insights for social media strategy.',
        marketingContext: {
          objectives: ['brand_monitoring', 'trend_identification', 'crisis_prevention'],
          metrics: ['engagement_rate', 'sentiment_score', 'reach', 'mentions']
        }
      }
    }
  }

  /**
   * Create Lead Scoring Agent (Worker)
   */
  createLeadScoringAgent() {
    return {
      name: 'Lead Scoring Agent Worker',
      description: 'Automatically scores and qualifies leads based on behavior and demographics',
      agentType: 'worker',
      category: 'lead_generation',
      capabilities: [
        {
          name: 'behavioral_scoring',
          description: 'Scores leads based on website behavior, email engagement, and interactions',
          inputSchema: {
            type: 'object',
            properties: {
              leadData: { type: 'object' },
              behaviorData: { type: 'object' },
              demographicData: { type: 'object' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              leadScore: { type: 'number' },
              qualification: { type: 'string' },
              nextActions: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'webhook', config: { endpoint: '/lead-update' } },
          { type: 'schedule', config: { interval: '1h' } }
        ],
        actions: [
          { type: 'calculate_score', config: {} },
          { type: 'update_crm', config: {} },
          { type: 'trigger_nurture', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are a Lead Scoring specialist. Analyze lead behavior, demographics, and engagement to assign accurate scores and recommend next actions.',
        marketingContext: {
          objectives: ['lead_qualification', 'sales_efficiency', 'conversion_optimization'],
          scoringFactors: ['email_engagement', 'website_behavior', 'demographic_fit', 'company_size']
        }
      }
    }
  }

  /**
   * Create Attribution Analyzer Agent (Scout)
   */
  createAttributionAnalyzer() {
    return {
      name: 'Attribution Analyzer Scout',
      description: 'Analyzes marketing attribution across all channels and touchpoints',
      agentType: 'scout',
      category: 'analytics_reporting',
      capabilities: [
        {
          name: 'multi_touch_attribution',
          description: 'Tracks customer journey across multiple touchpoints and channels',
          inputSchema: {
            type: 'object',
            properties: {
              customerId: { type: 'string' },
              timeframe: { type: 'string' },
              channels: { type: 'array' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              attributionModel: { type: 'object' },
              channelContribution: { type: 'object' },
              conversionPath: { type: 'array' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'schedule', config: { cron: '0 6 * * *' } },
          { type: 'condition', config: { metric: 'conversion', threshold: 1 } }
        ],
        actions: [
          { type: 'analyze_touchpoints', config: {} },
          { type: 'calculate_attribution', config: {} },
          { type: 'generate_insights', config: {} }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are an Attribution Analysis specialist. Track customer journeys, analyze touchpoint effectiveness, and provide insights for marketing optimization.',
        marketingContext: {
          objectives: ['attribution_accuracy', 'channel_optimization', 'budget_allocation'],
          models: ['first_touch', 'last_touch', 'linear', 'time_decay', 'position_based']
        }
      }
    }
  }

  /**
   * Create Abandoned Cart Recovery Agent (Worker)
   */
  createAbandonedCartRecovery() {
    return {
      name: 'Abandoned Cart Recovery Worker',
      description: 'Automatically recovers abandoned carts with personalized email sequences',
      agentType: 'worker',
      category: 'email_marketing',
      capabilities: [
        {
          name: 'cart_recovery_sequence',
          description: 'Sends personalized recovery emails with product recommendations',
          inputSchema: {
            type: 'object',
            properties: {
              cartData: { type: 'object' },
              customerData: { type: 'object' },
              abandonmentTime: { type: 'string' }
            }
          },
          outputSchema: {
            type: 'object',
            properties: {
              emailsSent: { type: 'number' },
              recoveryRate: { type: 'number' },
              revenue: { type: 'number' }
            }
          }
        }
      ],
      executionConfig: {
        triggers: [
          { type: 'webhook', config: { endpoint: '/cart-abandoned' } },
          { type: 'schedule', config: { interval: '1h' } }
        ],
        actions: [
          { type: 'send_recovery_email', config: { delay: '1h' } },
          { type: 'send_discount_offer', config: { delay: '24h' } },
          { type: 'send_final_reminder', config: { delay: '72h' } }
        ]
      },
      aiConfig: {
        provider: 'multi-ai',
        systemPrompt: 'You are an E-commerce Recovery specialist. Create compelling abandoned cart emails that bring customers back to complete their purchase.',
        marketingContext: {
          objectives: ['cart_recovery', 'revenue_optimization', 'customer_retention'],
          tactics: ['urgency', 'social_proof', 'discounts', 'product_recommendations']
        }
      }
    }
  }

  /**
   * Create agent from template for a specific user
   */
  async createAgentFromTemplate(templateKey, userId, customizations = {}) {
    try {
      const template = this.getTemplate(templateKey)
      if (!template) {
        throw new Error(`Template ${templateKey} not found`)
      }

      const agentData = {
        ...template,
        ...customizations,
        owner: userId,
        status: 'active',
        marketplace: {
          isPublic: false,
          price: 0
        }
      }

      const agent = new ColonyAgent(agentData)
      await agent.save()

      logger.info(`✅ Created agent from template: ${templateKey} for user: ${userId}`)
      return agent
    } catch (error) {
      logger.error(`Failed to create agent from template ${templateKey}:`, error)
      throw error
    }
  }

  /**
   * Create a complete marketing colony from templates
   */
  async createMarketingColony(userId, colonyConfig = {}) {
    try {
      const {
        industry = 'general',
        targetAudience = 'small_business',
        objectives = ['lead_generation', 'email_marketing']
      } = colonyConfig

      // Create Queen agent
      const queenAgent = await this.createAgentFromTemplate('email_sequence_generator', userId, {
        aiConfig: {
          ...this.getTemplate('email_sequence_generator').aiConfig,
          marketingContext: {
            industry,
            targetAudience,
            objectives
          }
        }
      })

      // Create Worker agents
      const workerAgents = []
      const workerTemplates = ['subject_line_optimizer', 'email_personalization_agent', 'lead_magnet_creator']
      
      for (const templateKey of workerTemplates) {
        const worker = await this.createAgentFromTemplate(templateKey, userId, {
          parentQueen: queenAgent._id,
          aiConfig: {
            ...this.getTemplate(templateKey).aiConfig,
            marketingContext: {
              industry,
              targetAudience,
              objectives
            }
          }
        })
        workerAgents.push(worker)
      }

      // Create Scout agents
      const scoutAgents = []
      const scoutTemplates = ['performance_analyzer']
      
      for (const templateKey of scoutTemplates) {
        const scout = await this.createAgentFromTemplate(templateKey, userId, {
          parentQueen: queenAgent._id,
          aiConfig: {
            ...this.getTemplate(templateKey).aiConfig,
            marketingContext: {
              industry,
              targetAudience,
              objectives
            }
          }
        })
        scoutAgents.push(scout)
      }

      // Update Queen with subordinate agents
      queenAgent.subordinateAgents = [...workerAgents.map(w => w._id), ...scoutAgents.map(s => s._id)]
      await queenAgent.save()

      logger.info(`🏰 Created marketing colony for user ${userId} with ${workerAgents.length} workers and ${scoutAgents.length} scouts`)

      return {
        queen: queenAgent,
        workers: workerAgents,
        scouts: scoutAgents,
        colonyId: `colony_${queenAgent._id}`
      }
    } catch (error) {
      logger.error('Failed to create marketing colony:', error)
      throw error
    }
  }
}

export default new ColonyBlueprints()
