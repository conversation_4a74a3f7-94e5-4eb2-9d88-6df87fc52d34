import crypto from 'crypto'
import { promisify } from 'util'

/**
 * NeuroColony Encryption Service
 * Military-grade encryption for sensitive data with proper AES-256-GCM implementation
 * Supports key rotation, secure key derivation, and authenticated encryption
 */

class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm'
    this.keyDerivationIterations = 100000
    this.saltLength = 32
    this.ivLength = 16
    this.tagLength = 16
    
    // Initialize master key from environment or generate if not exists
    this.initializeMasterKey()
  }

  /**
   * Initialize master key with proper security
   */
  initializeMasterKey() {
    const envKey = process.env.ENCRYPTION_MASTER_KEY
    
    if (!envKey) {
      console.error('⚠️  WARNING: ENCRYPTION_MASTER_KEY not set in environment!')
      console.error('⚠️  Generating temporary key - THIS IS NOT SECURE FOR PRODUCTION!')
      
      // Generate a secure random key for development
      const tempKey = crypto.randomBytes(32).toString('base64')
      console.error(`⚠️  Set ENCRYPTION_MASTER_KEY="${tempKey}" in your .env file`)
      
      this.masterKey = crypto.createHash('sha256')
        .update('INSECURE-DEV-KEY-REPLACE-IMMEDIATELY')
        .digest()
    } else {
      // Derive master key from environment variable
      this.masterKey = crypto.createHash('sha256')
        .update(envKey)
        .digest()
    }
  }

  /**
   * Derive encryption key from master key and context
   * @param {string} context - Context for key derivation (e.g., 'integration', 'api-key')
   * @param {Buffer} salt - Salt for key derivation
   * @returns {Promise<Buffer>} Derived key
   */
  async deriveKey(context, salt) {
    const pbkdf2 = promisify(crypto.pbkdf2)
    const info = Buffer.from(`neurocolony-${context}`, 'utf8')
    
    return pbkdf2(
      Buffer.concat([this.masterKey, info]),
      salt,
      this.keyDerivationIterations,
      32,
      'sha256'
    )
  }

  /**
   * Encrypt data using AES-256-GCM with authenticated encryption
   * @param {string} plaintext - Data to encrypt
   * @param {string} context - Encryption context (e.g., 'integration-credentials')
   * @param {object} aad - Additional authenticated data
   * @returns {Promise<object>} Encrypted data with metadata
   */
  async encrypt(plaintext, context = 'general', aad = {}) {
    try {
      // Generate random salt and IV
      const salt = crypto.randomBytes(this.saltLength)
      const iv = crypto.randomBytes(this.ivLength)
      
      // Derive context-specific key
      const key = await this.deriveKey(context, salt)
      
      // Create cipher
      const cipher = crypto.createCipheriv(this.algorithm, key, iv)
      
      // Set additional authenticated data
      const aadBuffer = Buffer.from(JSON.stringify({
        ...aad,
        context,
        timestamp: Date.now(),
        version: 1
      }), 'utf8')
      cipher.setAAD(aadBuffer)
      
      // Encrypt data
      const encrypted = Buffer.concat([
        cipher.update(plaintext, 'utf8'),
        cipher.final()
      ])
      
      // Get authentication tag
      const authTag = cipher.getAuthTag()
      
      // Combine all components
      const combined = Buffer.concat([
        Buffer.from([1]), // Version byte
        salt,
        iv,
        authTag,
        encrypted
      ])
      
      return {
        encrypted: combined.toString('base64'),
        context,
        version: 1
      }
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`)
    }
  }

  /**
   * Decrypt data encrypted with encrypt()
   * @param {string} encryptedData - Base64 encoded encrypted data
   * @param {string} context - Encryption context
   * @param {object} aad - Additional authenticated data (must match encryption)
   * @returns {Promise<string>} Decrypted plaintext
   */
  async decrypt(encryptedData, context = 'general', aad = {}) {
    try {
      // Decode from base64
      const combined = Buffer.from(encryptedData, 'base64')
      
      // Extract components
      const version = combined[0]
      if (version !== 1) {
        throw new Error(`Unsupported encryption version: ${version}`)
      }
      
      let offset = 1
      const salt = combined.slice(offset, offset + this.saltLength)
      offset += this.saltLength
      
      const iv = combined.slice(offset, offset + this.ivLength)
      offset += this.ivLength
      
      const authTag = combined.slice(offset, offset + this.tagLength)
      offset += this.tagLength
      
      const encrypted = combined.slice(offset)
      
      // Derive key
      const key = await this.deriveKey(context, salt)
      
      // Create decipher
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv)
      decipher.setAuthTag(authTag)
      
      // Set additional authenticated data
      const aadBuffer = Buffer.from(JSON.stringify({
        ...aad,
        context,
        timestamp: Date.now(),
        version: 1
      }), 'utf8')
      decipher.setAAD(aadBuffer)
      
      // Decrypt data
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ])
      
      return decrypted.toString('utf8')
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`)
    }
  }

  /**
   * Encrypt multiple fields in an object
   * @param {object} data - Object with fields to encrypt
   * @param {string[]} fields - Fields to encrypt
   * @param {string} context - Encryption context
   * @returns {Promise<object>} Object with encrypted fields
   */
  async encryptFields(data, fields, context) {
    const encrypted = { ...data }
    
    for (const field of fields) {
      if (data[field] && typeof data[field] === 'string') {
        const result = await this.encrypt(data[field], context, {
          field,
          objectId: data._id?.toString()
        })
        encrypted[field] = result.encrypted
      }
    }
    
    return encrypted
  }

  /**
   * Decrypt multiple fields in an object
   * @param {object} data - Object with encrypted fields
   * @param {string[]} fields - Fields to decrypt
   * @param {string} context - Encryption context
   * @returns {Promise<object>} Object with decrypted fields
   */
  async decryptFields(data, fields, context) {
    const decrypted = { ...data }
    
    for (const field of fields) {
      if (data[field] && typeof data[field] === 'string') {
        try {
          decrypted[field] = await this.decrypt(data[field], context, {
            field,
            objectId: data._id?.toString()
          })
        } catch (error) {
          // Log error but don't fail entire operation
          console.error(`Failed to decrypt field ${field}:`, error.message)
          decrypted[field] = null
        }
      }
    }
    
    return decrypted
  }

  /**
   * Generate a secure random token
   * @param {number} length - Token length in bytes
   * @returns {string} Hex encoded token
   */
  generateToken(length = 32) {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * Hash sensitive data with salt
   * @param {string} data - Data to hash
   * @param {string} salt - Optional salt (generated if not provided)
   * @returns {object} Hash and salt
   */
  hashWithSalt(data, salt = null) {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex')
    const hash = crypto.createHash('sha256')
      .update(data + actualSalt)
      .digest('hex')
    
    return { hash, salt: actualSalt }
  }

  /**
   * Verify hashed data
   * @param {string} data - Data to verify
   * @param {string} hash - Expected hash
   * @param {string} salt - Salt used for hashing
   * @returns {boolean} Verification result
   */
  verifyHash(data, hash, salt) {
    const computed = crypto.createHash('sha256')
      .update(data + salt)
      .digest('hex')
    
    return crypto.timingSafeEqual(
      Buffer.from(hash),
      Buffer.from(computed)
    )
  }

  /**
   * Rotate encryption keys (for key rotation)
   * @param {string} oldEncrypted - Data encrypted with old key
   * @param {string} oldContext - Old encryption context
   * @param {string} newContext - New encryption context
   * @returns {Promise<object>} Re-encrypted data
   */
  async rotateEncryption(oldEncrypted, oldContext, newContext) {
    // Decrypt with old context
    const plaintext = await this.decrypt(oldEncrypted, oldContext)
    
    // Re-encrypt with new context
    return this.encrypt(plaintext, newContext)
  }
}

// Export singleton instance
export default new EncryptionService()