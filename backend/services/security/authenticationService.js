import jwt from 'jsonwebtoken'
import speakeasy from 'speakeasy'
import QRCode from 'qrcode'
import crypto from 'crypto'
import argon2 from 'argon2'

/**
 * NeuroColony Authentication Service
 * Enterprise-grade authentication with JWT refresh tokens, 2FA, and biometric support
 */

class AuthenticationService {
  constructor() {
    this.accessTokenExpiry = '15m' // 15 minutes
    this.refreshTokenExpiry = '7d' // 7 days
    this.sessionTimeout = 30 * 60 * 1000 // 30 minutes of inactivity
    
    // Security settings
    this.maxLoginAttempts = 5
    this.lockoutDuration = 30 * 60 * 1000 // 30 minutes
    this.passwordHistory = 5 // Remember last 5 passwords
    
    // Token families for refresh token rotation
    this.tokenFamilies = new Map()
    
    // Active sessions tracking
    this.activeSessions = new Map()
    
    // Failed login attempts tracking
    this.failedAttempts = new Map()
  }

  /**
   * Generate access and refresh tokens
   * @param {object} user - User object
   * @param {object} options - Token options
   * @returns {object} Token pair and metadata
   */
  async generateTokenPair(user, options = {}) {
    const tokenFamily = crypto.randomBytes(16).toString('hex')
    const sessionId = crypto.randomBytes(16).toString('hex')
    
    // Access token payload
    const accessPayload = {
      userId: user._id,
      email: user.email,
      role: user.role || 'user',
      permissions: user.permissions || [],
      sessionId,
      type: 'access'
    }
    
    // Refresh token payload
    const refreshPayload = {
      userId: user._id,
      tokenFamily,
      sessionId,
      type: 'refresh',
      version: 1
    }
    
    // Generate tokens
    const accessToken = jwt.sign(
      accessPayload,
      process.env.JWT_ACCESS_SECRET || process.env.JWT_SECRET,
      { 
        expiresIn: this.accessTokenExpiry,
        issuer: 'neurocolony',
        audience: 'neurocolony-api',
        jwtid: crypto.randomBytes(16).toString('hex')
      }
    )
    
    const refreshToken = jwt.sign(
      refreshPayload,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      { 
        expiresIn: this.refreshTokenExpiry,
        issuer: 'neurocolony',
        audience: 'neurocolony-api',
        jwtid: crypto.randomBytes(16).toString('hex')
      }
    )
    
    // Store token family
    this.tokenFamilies.set(tokenFamily, {
      userId: user._id,
      version: 1,
      created: Date.now(),
      lastUsed: Date.now(),
      revoked: false
    })
    
    // Create session
    this.createSession(sessionId, {
      userId: user._id,
      userAgent: options.userAgent,
      ipAddress: options.ipAddress,
      tokenFamily
    })
    
    return {
      accessToken,
      refreshToken,
      tokenFamily,
      sessionId,
      expiresIn: 900, // 15 minutes in seconds
      tokenType: 'Bearer'
    }
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @param {object} options - Refresh options
   * @returns {object} New token pair
   */
  async refreshAccessToken(refreshToken, options = {}) {
    try {
      // Verify refresh token
      const decoded = jwt.verify(
        refreshToken,
        process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
        {
          issuer: 'neurocolony',
          audience: 'neurocolony-api'
        }
      )
      
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type')
      }
      
      // Check token family
      const family = this.tokenFamilies.get(decoded.tokenFamily)
      if (!family || family.revoked) {
        // Possible token reuse - revoke entire family
        await this.revokeTokenFamily(decoded.tokenFamily)
        throw new Error('Token family revoked - possible security breach')
      }
      
      // Check version (detect replay attacks)
      if (decoded.version !== family.version) {
        await this.revokeTokenFamily(decoded.tokenFamily)
        throw new Error('Token version mismatch - possible replay attack')
      }
      
      // Update token family version
      family.version++
      family.lastUsed = Date.now()
      
      // Get user data
      const user = await this.getUserById(decoded.userId)
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive')
      }
      
      // Generate new token pair
      return this.generateTokenPair(user, {
        ...options,
        tokenFamily: decoded.tokenFamily,
        sessionId: decoded.sessionId
      })
    } catch (error) {
      // Log security event
      await this.logSecurityEvent({
        type: 'refresh_token_error',
        error: error.message,
        ipAddress: options.ipAddress
      })
      
      throw new Error('Invalid refresh token')
    }
  }

  /**
   * Verify access token
   * @param {string} token - Access token
   * @returns {object} Decoded token
   */
  async verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(
        token,
        process.env.JWT_ACCESS_SECRET || process.env.JWT_SECRET,
        {
          issuer: 'neurocolony',
          audience: 'neurocolony-api'
        }
      )
      
      if (decoded.type !== 'access') {
        throw new Error('Invalid token type')
      }
      
      // Check if session is active
      const session = this.activeSessions.get(decoded.sessionId)
      if (!session || session.revoked) {
        throw new Error('Session not found or revoked')
      }
      
      // Update session activity
      session.lastActivity = Date.now()
      
      return decoded
    } catch (error) {
      throw new Error('Invalid access token')
    }
  }

  /**
   * Setup 2FA for user
   * @param {object} user - User object
   * @returns {object} 2FA setup data
   */
  async setup2FA(user) {
    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `NeuroColony (${user.email})`,
      issuer: 'NeuroColony',
      length: 32
    })
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url)
    
    // Generate backup codes
    const backupCodes = Array.from({ length: 10 }, () => 
      crypto.randomBytes(4).toString('hex').toUpperCase()
    )
    
    return {
      secret: secret.base32,
      qrCode: qrCodeUrl,
      backupCodes,
      manualEntry: secret.base32.match(/.{1,4}/g).join(' ')
    }
  }

  /**
   * Verify 2FA token
   * @param {string} token - TOTP token
   * @param {string} secret - User's 2FA secret
   * @returns {boolean} Verification result
   */
  verify2FA(token, secret) {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2 // Allow 2 time steps for clock drift
    })
  }

  /**
   * Verify biometric authentication
   * @param {object} biometricData - Biometric authentication data
   * @param {object} user - User object
   * @returns {boolean} Verification result
   */
  async verifyBiometric(biometricData, user) {
    // Implement WebAuthn/FIDO2 verification
    // This is a placeholder for actual biometric verification
    
    if (!user.biometricCredentials) {
      throw new Error('Biometric authentication not set up')
    }
    
    // Verify biometric credential
    // In production, use webauthn library
    const isValid = await this.verifyWebAuthnAssertion(
      biometricData,
      user.biometricCredentials
    )
    
    return isValid
  }

  /**
   * Check password strength
   * @param {string} password - Password to check
   * @returns {object} Strength analysis
   */
  checkPasswordStrength(password) {
    const analysis = {
      score: 0,
      feedback: [],
      isStrong: false
    }
    
    // Length check
    if (password.length >= 12) {
      analysis.score += 2
    } else if (password.length >= 8) {
      analysis.score += 1
    } else {
      analysis.feedback.push('Password should be at least 8 characters')
    }
    
    // Complexity checks
    const hasLower = /[a-z]/.test(password)
    const hasUpper = /[A-Z]/.test(password)
    const hasNumber = /\d/.test(password)
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    
    if (hasLower) analysis.score++
    if (hasUpper) analysis.score++
    if (hasNumber) analysis.score++
    if (hasSpecial) analysis.score++
    
    // Feedback
    if (!hasLower || !hasUpper) {
      analysis.feedback.push('Include both uppercase and lowercase letters')
    }
    if (!hasNumber) {
      analysis.feedback.push('Include at least one number')
    }
    if (!hasSpecial) {
      analysis.feedback.push('Include at least one special character')
    }
    
    // Common patterns check
    const commonPatterns = [
      /^[a-zA-Z]+\d+$/, // Letters followed by numbers
      /^\d+[a-zA-Z]+$/, // Numbers followed by letters
      /^(.)\1+$/, // Repeated characters
      /^(012|123|234|345|456|567|678|789|890)+/, // Sequential numbers
      /^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)+/i // Sequential letters
    ]
    
    if (commonPatterns.some(pattern => pattern.test(password))) {
      analysis.score--
      analysis.feedback.push('Avoid common patterns')
    }
    
    // Dictionary words check (simplified)
    const commonWords = ['password', 'admin', 'user', 'login', 'welcome']
    if (commonWords.some(word => password.toLowerCase().includes(word))) {
      analysis.score--
      analysis.feedback.push('Avoid dictionary words')
    }
    
    analysis.isStrong = analysis.score >= 5
    return analysis
  }

  /**
   * Track failed login attempt
   * @param {string} identifier - User email or ID
   * @param {string} ipAddress - Request IP
   */
  async trackFailedLogin(identifier, ipAddress) {
    const key = `${identifier}:${ipAddress}`
    const attempts = this.failedAttempts.get(key) || {
      count: 0,
      firstAttempt: Date.now(),
      lastAttempt: Date.now()
    }
    
    attempts.count++
    attempts.lastAttempt = Date.now()
    
    this.failedAttempts.set(key, attempts)
    
    // Check if should lock account
    if (attempts.count >= this.maxLoginAttempts) {
      await this.lockAccount(identifier, ipAddress)
    }
    
    // Log security event
    await this.logSecurityEvent({
      type: 'failed_login',
      identifier,
      ipAddress,
      attemptCount: attempts.count
    })
  }

  /**
   * Check if account is locked
   * @param {string} identifier - User email or ID
   * @param {string} ipAddress - Request IP
   * @returns {object} Lock status
   */
  isAccountLocked(identifier, ipAddress) {
    const key = `${identifier}:${ipAddress}`
    const attempts = this.failedAttempts.get(key)
    
    if (!attempts || attempts.count < this.maxLoginAttempts) {
      return { locked: false }
    }
    
    const lockExpiry = attempts.lastAttempt + this.lockoutDuration
    const isLocked = Date.now() < lockExpiry
    
    if (!isLocked) {
      // Clear attempts after lockout expires
      this.failedAttempts.delete(key)
    }
    
    return {
      locked: isLocked,
      remainingTime: isLocked ? lockExpiry - Date.now() : 0,
      attempts: attempts.count
    }
  }

  /**
   * Clear failed login attempts
   * @param {string} identifier - User email or ID
   * @param {string} ipAddress - Request IP
   */
  clearFailedAttempts(identifier, ipAddress) {
    const key = `${identifier}:${ipAddress}`
    this.failedAttempts.delete(key)
  }

  /**
   * Create user session
   * @param {string} sessionId - Session ID
   * @param {object} sessionData - Session data
   */
  createSession(sessionId, sessionData) {
    this.activeSessions.set(sessionId, {
      ...sessionData,
      created: Date.now(),
      lastActivity: Date.now(),
      revoked: false
    })
  }

  /**
   * Revoke session
   * @param {string} sessionId - Session ID
   */
  async revokeSession(sessionId) {
    const session = this.activeSessions.get(sessionId)
    if (session) {
      session.revoked = true
      
      // Log security event
      await this.logSecurityEvent({
        type: 'session_revoked',
        sessionId,
        userId: session.userId
      })
    }
  }

  /**
   * Revoke all user sessions
   * @param {string} userId - User ID
   */
  async revokeAllUserSessions(userId) {
    let revokedCount = 0
    
    for (const [sessionId, session] of this.activeSessions) {
      if (session.userId === userId) {
        session.revoked = true
        revokedCount++
      }
    }
    
    // Log security event
    await this.logSecurityEvent({
      type: 'all_sessions_revoked',
      userId,
      sessionCount: revokedCount
    })
  }

  /**
   * Revoke token family
   * @param {string} tokenFamily - Token family ID
   */
  async revokeTokenFamily(tokenFamily) {
    const family = this.tokenFamilies.get(tokenFamily)
    if (family) {
      family.revoked = true
      
      // Revoke all sessions in this family
      for (const [sessionId, session] of this.activeSessions) {
        if (session.tokenFamily === tokenFamily) {
          session.revoked = true
        }
      }
      
      // Log security event
      await this.logSecurityEvent({
        type: 'token_family_revoked',
        tokenFamily,
        userId: family.userId,
        reason: 'Security breach detected'
      })
    }
  }

  /**
   * Clean up expired sessions and tokens
   */
  cleanupExpiredSessions() {
    const now = Date.now()
    
    // Clean up inactive sessions
    for (const [sessionId, session] of this.activeSessions) {
      if (now - session.lastActivity > this.sessionTimeout || session.revoked) {
        this.activeSessions.delete(sessionId)
      }
    }
    
    // Clean up old token families
    for (const [familyId, family] of this.tokenFamilies) {
      if (now - family.lastUsed > 7 * 24 * 60 * 60 * 1000 || family.revoked) {
        this.tokenFamilies.delete(familyId)
      }
    }
    
    // Clean up old failed attempts
    for (const [key, attempts] of this.failedAttempts) {
      if (now - attempts.lastAttempt > this.lockoutDuration * 2) {
        this.failedAttempts.delete(key)
      }
    }
  }

  /**
   * Get active sessions for user
   * @param {string} userId - User ID
   * @returns {array} Active sessions
   */
  getUserSessions(userId) {
    const sessions = []
    
    for (const [sessionId, session] of this.activeSessions) {
      if (session.userId === userId && !session.revoked) {
        sessions.push({
          sessionId,
          created: session.created,
          lastActivity: session.lastActivity,
          userAgent: session.userAgent,
          ipAddress: session.ipAddress
        })
      }
    }
    
    return sessions
  }

  /**
   * Placeholder for WebAuthn verification
   */
  async verifyWebAuthnAssertion(assertion, credentials) {
    // In production, implement actual WebAuthn verification
    return true
  }

  /**
   * Placeholder for user lookup
   */
  async getUserById(userId) {
    // In production, fetch from database
    return { _id: userId, email: '<EMAIL>', isActive: true }
  }

  /**
   * Placeholder for account locking
   */
  async lockAccount(identifier, ipAddress) {
    // In production, update user record in database
    console.log(`Account locked: ${identifier} from ${ipAddress}`)
  }

  /**
   * Log security events
   */
  async logSecurityEvent(event) {
    // In production, send to SIEM or security logging service
    console.log('[SECURITY_AUTH]', {
      ...event,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    setInterval(() => this.cleanupExpiredSessions(), 60000) // Every minute
  }
}

// Export singleton instance
const authService = new AuthenticationService()
authService.startCleanupInterval()

export default authService