import { EventEmitter } from 'events'
import { logger } from '../utils/logger.js'

/**
 * NeuroColony Team Collaboration Service
 * Manages team workspaces, permissions, and collaborative workflows
 */

class TeamCollaborationService extends EventEmitter {
  constructor() {
    super()
    this.teams = new Map()
    this.workspaces = new Map()
    this.collaborations = new Map()
    this.permissions = new Map()
    
    this.initializeDefaultRoles()
    logger.info('👥 Team Collaboration Service initialized')
  }

  initializeDefaultRoles() {
    this.defaultRoles = {
      'owner': {
        name: 'Owner',
        permissions: ['all'],
        description: 'Full access to all features and settings',
        color: '#8B5CF6'
      },
      'admin': {
        name: 'Admin', 
        permissions: [
          'manage_team',
          'manage_workflows',
          'manage_agents',
          'manage_integrations',
          'view_analytics',
          'execute_workflows',
          'create_workflows',
          'edit_workflows',
          'delete_workflows'
        ],
        description: 'Manage team and workflows',
        color: '#3B82F6'
      },
      'editor': {
        name: 'Editor',
        permissions: [
          'view_analytics',
          'execute_workflows', 
          'create_workflows',
          'edit_workflows',
          'manage_agents'
        ],
        description: 'Create and edit workflows',
        color: '#10B981'
      },
      'viewer': {
        name: 'Viewer',
        permissions: [
          'view_analytics',
          'execute_workflows'
        ],
        description: 'View and execute workflows only',
        color: '#6B7280'
      }
    }
  }

  /**
   * Create a new team workspace
   */
  async createTeam(ownerId, teamData) {
    try {
      const teamId = `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const team = {
        id: teamId,
        name: teamData.name,
        description: teamData.description || '',
        ownerId,
        createdAt: new Date(),
        settings: {
          allowGuestCollaboration: teamData.allowGuests || false,
          requireApproval: teamData.requireApproval || true,
          defaultRole: 'viewer',
          workflowSharing: 'team', // 'team', 'organization', 'public'
          ...teamData.settings
        },
        members: [
          {
            userId: ownerId,
            role: 'owner',
            joinedAt: new Date(),
            status: 'active',
            permissions: ['all']
          }
        ],
        stats: {
          memberCount: 1,
          workflowCount: 0,
          agentCount: 0,
          executionCount: 0
        }
      }

      this.teams.set(teamId, team)
      this.emit('teamCreated', { teamId, team, ownerId })
      
      logger.info(`Team created: ${team.name} (${teamId}) by user ${ownerId}`)
      
      return {
        success: true,
        teamId,
        team: this.sanitizeTeamData(team)
      }
    } catch (error) {
      logger.error('Create team error:', error)
      throw error
    }
  }

  /**
   * Invite user to team
   */
  async inviteToTeam(teamId, inviterId, inviteData) {
    try {
      const team = this.teams.get(teamId)
      if (!team) {
        throw new Error('Team not found')
      }

      // Check if inviter has permission
      if (!this.hasPermission(teamId, inviterId, 'manage_team')) {
        throw new Error('Insufficient permissions to invite users')
      }

      const inviteId = `invite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const invitation = {
        id: inviteId,
        teamId,
        inviterId,
        email: inviteData.email,
        role: inviteData.role || 'viewer',
        message: inviteData.message || '',
        status: 'pending',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        permissions: this.defaultRoles[inviteData.role]?.permissions || ['view_analytics', 'execute_workflows']
      }

      // Store invitation (in production, this would be in database)
      if (!this.collaborations.has(teamId)) {
        this.collaborations.set(teamId, { invitations: [], requests: [] })
      }
      this.collaborations.get(teamId).invitations.push(invitation)

      this.emit('userInvited', { teamId, invitation, inviter: inviterId })
      
      logger.info(`User invited to team ${teamId}: ${inviteData.email} as ${inviteData.role}`)

      return {
        success: true,
        inviteId,
        invitation: {
          id: inviteId,
          email: inviteData.email,
          role: inviteData.role,
          status: 'pending',
          expiresAt: invitation.expiresAt
        }
      }
    } catch (error) {
      logger.error('Invite to team error:', error)
      throw error
    }
  }

  /**
   * Accept team invitation
   */
  async acceptInvitation(inviteId, userId) {
    try {
      let foundInvitation = null
      let teamId = null

      // Find invitation across all teams
      for (const [tId, collaboration] of this.collaborations.entries()) {
        const invitation = collaboration.invitations.find(inv => inv.id === inviteId)
        if (invitation) {
          foundInvitation = invitation
          teamId = tId
          break
        }
      }

      if (!foundInvitation) {
        throw new Error('Invitation not found or expired')
      }

      if (foundInvitation.status !== 'pending') {
        throw new Error('Invitation already processed')
      }

      if (foundInvitation.expiresAt < new Date()) {
        throw new Error('Invitation has expired')
      }

      const team = this.teams.get(teamId)
      if (!team) {
        throw new Error('Team no longer exists')
      }

      // Add user to team
      const newMember = {
        userId,
        email: foundInvitation.email,
        role: foundInvitation.role,
        joinedAt: new Date(),
        status: 'active',
        permissions: foundInvitation.permissions,
        invitedBy: foundInvitation.inviterId
      }

      team.members.push(newMember)
      team.stats.memberCount = team.members.length

      // Mark invitation as accepted
      foundInvitation.status = 'accepted'
      foundInvitation.acceptedAt = new Date()
      foundInvitation.acceptedBy = userId

      this.emit('invitationAccepted', { teamId, userId, member: newMember })
      
      logger.info(`User ${userId} joined team ${teamId} as ${foundInvitation.role}`)

      return {
        success: true,
        teamId,
        member: newMember,
        team: this.sanitizeTeamData(team)
      }
    } catch (error) {
      logger.error('Accept invitation error:', error)
      throw error
    }
  }

  /**
   * Get teams for user
   */
  getUserTeams(userId) {
    const userTeams = []
    
    for (const [teamId, team] of this.teams.entries()) {
      const member = team.members.find(m => m.userId === userId && m.status === 'active')
      if (member) {
        userTeams.push({
          ...this.sanitizeTeamData(team),
          userRole: member.role,
          userPermissions: member.permissions,
          joinedAt: member.joinedAt
        })
      }
    }

    return userTeams.sort((a, b) => new Date(b.joinedAt) - new Date(a.joinedAt))
  }

  /**
   * Get team details
   */
  getTeam(teamId, userId) {
    const team = this.teams.get(teamId)
    if (!team) {
      throw new Error('Team not found')
    }

    const member = team.members.find(m => m.userId === userId && m.status === 'active')
    if (!member) {
      throw new Error('Access denied - not a team member')
    }

    return {
      ...this.sanitizeTeamData(team),
      userRole: member.role,
      userPermissions: member.permissions,
      canManageTeam: this.hasPermission(teamId, userId, 'manage_team')
    }
  }

  /**
   * Share workflow with team
   */
  async shareWorkflow(workflowId, teamId, userId, shareOptions = {}) {
    try {
      const team = this.teams.get(teamId)
      if (!team) {
        throw new Error('Team not found')
      }

      if (!this.hasPermission(teamId, userId, 'create_workflows')) {
        throw new Error('Insufficient permissions to share workflows')
      }

      const sharedWorkflow = {
        id: `shared_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        workflowId,
        teamId,
        sharedBy: userId,
        sharedAt: new Date(),
        permissions: {
          canView: shareOptions.canView !== false,
          canEdit: shareOptions.canEdit || false,
          canExecute: shareOptions.canExecute !== false,
          canShare: shareOptions.canShare || false
        },
        settings: {
          allowComments: shareOptions.allowComments !== false,
          allowVersioning: shareOptions.allowVersioning !== false,
          requireApproval: shareOptions.requireApproval || false
        }
      }

      // Store shared workflow (in production, this would be in database)
      if (!this.workspaces.has(teamId)) {
        this.workspaces.set(teamId, { workflows: [], agents: [], integrations: [] })
      }
      this.workspaces.get(teamId).workflows.push(sharedWorkflow)

      team.stats.workflowCount = this.workspaces.get(teamId).workflows.length

      this.emit('workflowShared', { teamId, workflowId, sharedWorkflow, userId })
      
      logger.info(`Workflow ${workflowId} shared with team ${teamId} by user ${userId}`)

      return {
        success: true,
        sharedWorkflowId: sharedWorkflow.id,
        permissions: sharedWorkflow.permissions
      }
    } catch (error) {
      logger.error('Share workflow error:', error)
      throw error
    }
  }

  /**
   * Get team workflows
   */
  getTeamWorkflows(teamId, userId) {
    if (!this.hasPermission(teamId, userId, 'view_analytics')) {
      throw new Error('Insufficient permissions to view team workflows')
    }

    const workspace = this.workspaces.get(teamId)
    if (!workspace) {
      return []
    }

    return workspace.workflows.map(workflow => ({
      id: workflow.id,
      workflowId: workflow.workflowId,
      sharedBy: workflow.sharedBy,
      sharedAt: workflow.sharedAt,
      permissions: workflow.permissions,
      canAccess: this.canAccessWorkflow(teamId, userId, workflow)
    }))
  }

  /**
   * Create team comment on workflow
   */
  async addWorkflowComment(workflowId, teamId, userId, commentData) {
    try {
      if (!this.hasPermission(teamId, userId, 'view_analytics')) {
        throw new Error('Insufficient permissions to comment')
      }

      const commentId = `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const comment = {
        id: commentId,
        workflowId,
        teamId,
        userId,
        content: commentData.content,
        type: commentData.type || 'general', // 'general', 'suggestion', 'issue'
        createdAt: new Date(),
        updatedAt: new Date(),
        replies: [],
        reactions: {},
        mentions: commentData.mentions || []
      }

      // Store comment (in production, this would be in database)
      // For now, emit event for real-time updates
      this.emit('workflowComment', { teamId, workflowId, comment, userId })
      
      logger.info(`Comment added to workflow ${workflowId} in team ${teamId} by user ${userId}`)

      return {
        success: true,
        commentId,
        comment
      }
    } catch (error) {
      logger.error('Add workflow comment error:', error)
      throw error
    }
  }

  /**
   * Check if user has specific permission in team
   */
  hasPermission(teamId, userId, permission) {
    const team = this.teams.get(teamId)
    if (!team) return false

    const member = team.members.find(m => m.userId === userId && m.status === 'active')
    if (!member) return false

    // Owner has all permissions
    if (member.role === 'owner' || member.permissions.includes('all')) {
      return true
    }

    return member.permissions.includes(permission)
  }

  /**
   * Check if user can access specific workflow
   */
  canAccessWorkflow(teamId, userId, workflow) {
    if (!this.hasPermission(teamId, userId, 'view_analytics')) {
      return false
    }

    // Workflow owner always has access
    if (workflow.sharedBy === userId) {
      return true
    }

    return workflow.permissions.canView
  }

  /**
   * Get team collaboration analytics
   */
  getTeamAnalytics(teamId, userId) {
    if (!this.hasPermission(teamId, userId, 'view_analytics')) {
      throw new Error('Insufficient permissions to view analytics')
    }

    const team = this.teams.get(teamId)
    const workspace = this.workspaces.get(teamId)
    const collaboration = this.collaborations.get(teamId)

    return {
      team: {
        memberCount: team.stats.memberCount,
        workflowCount: team.stats.workflowCount,
        agentCount: team.stats.agentCount,
        executionCount: team.stats.executionCount
      },
      activity: {
        pendingInvitations: collaboration?.invitations.filter(inv => inv.status === 'pending').length || 0,
        sharedWorkflows: workspace?.workflows.length || 0,
        activeCollaborations: team.members.filter(m => m.status === 'active').length
      },
      permissions: {
        canInvite: this.hasPermission(teamId, userId, 'manage_team'),
        canManageWorkflows: this.hasPermission(teamId, userId, 'manage_workflows'),
        canManageAgents: this.hasPermission(teamId, userId, 'manage_agents')
      }
    }
  }

  /**
   * Remove sensitive data from team object
   */
  sanitizeTeamData(team) {
    return {
      id: team.id,
      name: team.name,
      description: team.description,
      createdAt: team.createdAt,
      settings: {
        allowGuestCollaboration: team.settings.allowGuestCollaboration,
        requireApproval: team.settings.requireApproval,
        workflowSharing: team.settings.workflowSharing
      },
      members: team.members.map(member => ({
        userId: member.userId,
        email: member.email,
        role: member.role,
        joinedAt: member.joinedAt,
        status: member.status
      })),
      stats: team.stats
    }
  }

  /**
   * Get available roles for team
   */
  getAvailableRoles() {
    return Object.entries(this.defaultRoles).map(([key, role]) => ({
      id: key,
      ...role
    }))
  }
}

// Export singleton instance
export default new TeamCollaborationService()