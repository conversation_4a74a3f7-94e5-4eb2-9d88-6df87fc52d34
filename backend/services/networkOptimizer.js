/**
 * Advanced Network Performance Optimizer - Phase 3
 * Features: HTTP/2, Compression, Keep-Alive, Request Batching, Response Streaming
 * Target: 60%+ compression ratio, <50ms network latency, multiplexed connections
 */

import http2 from 'http2'
import zlib from 'zlib'
import crypto from 'crypto'
import { promisify } from 'util'
import { Transform } from 'stream'
import { logger } from '../utils/logger.js'

const {
  HTTP2_HEADER_STATUS,
  HTTP2_HEADER_CONTENT_TYPE,
  HTTP2_HEADER_CONTENT_ENCODING,
  HTTP2_HEADER_CONTENT_LENGTH,
  HTTP2_HEADER_CACHE_CONTROL,
  HTTP2_HEADER_ACCEPT_ENCODING
} = http2.constants

class NetworkOptimizer {
  constructor() {
    this.compressionCache = new Map()
    this.requestBatches = new Map()
    this.connectionPools = new Map()
    
    this.stats = {
      totalRequests: 0,
      compressedRequests: 0,
      compressionRatio: 0,
      avgLatency: 0,
      http2Requests: 0,
      batchedRequests: 0,
      streamedResponses: 0,
      cacheHits: 0
    }
    
    this.compressionOptions = {
      brotli: {
        level: 4,
        windowBits: 22,
        quality: 4,
        chunkSize: 1024
      },
      gzip: {
        level: 6,
        chunkSize: 1024,
        windowBits: 15,
        memLevel: 8
      },
      deflate: {
        level: 6,
        chunkSize: 1024,
        windowBits: 15,
        memLevel: 8
      }
    }
    
    this.initializeOptimizations()
  }

  /**
   * Initialize all network optimizations
   */
  initializeOptimizations() {
    this.setupCompressionEngine()
    this.setupRequestBatching()
    this.setupConnectionOptimization()
    this.setupResponseStreaming()
    
    logger.info('✅ Network optimizer initialized')
  }

  /**
   * Advanced compression engine with multiple algorithms
   */
  setupCompressionEngine() {
    // Promisify compression functions for async/await
    this.compress = {
      brotli: {
        compress: promisify(zlib.brotliCompress),
        decompress: promisify(zlib.brotliDecompress)
      },
      gzip: {
        compress: promisify(zlib.gzip),
        decompress: promisify(zlib.gunzip)
      },
      deflate: {
        compress: promisify(zlib.deflate),
        decompress: promisify(zlib.inflate)
      }
    }
    
    logger.info('✅ Compression engine initialized')
  }

  /**
   * Smart compression middleware with algorithm selection
   */
  createCompressionMiddleware() {
    return async (req, res, next) => {
      const startTime = Date.now()
      
      // Skip compression for certain content types
      const skipCompression = [
        'image/',
        'video/',
        'audio/',
        'application/octet-stream'
      ]
      
      const originalSend = res.send
      const originalJson = res.json
      
      // Override res.send to add compression
      res.send = async function(data) {
        try {
          const contentType = res.getHeader('content-type') || ''
          const shouldCompress = !skipCompression.some(type => 
            contentType.startsWith(type)
          ) && data && data.length > 1024 // Only compress >1KB
          
          if (shouldCompress) {
            const compressed = await this.compressResponse(req, data)
            if (compressed) {
              res.setHeader('content-encoding', compressed.encoding)
              res.setHeader('content-length', compressed.data.length)
              this.stats.compressedRequests++
              
              // Update compression ratio
              const ratio = 1 - (compressed.data.length / data.length)
              this.stats.compressionRatio = 
                (this.stats.compressionRatio * (this.stats.compressedRequests - 1) + ratio) / 
                this.stats.compressedRequests
              
              data = compressed.data
            }
          }
          
          this.stats.totalRequests++
          this.stats.avgLatency = 
            (this.stats.avgLatency * (this.stats.totalRequests - 1) + (Date.now() - startTime)) / 
            this.stats.totalRequests
          
          return originalSend.call(res, data)
        } catch (error) {
          logger.error('Compression error:', error)
          return originalSend.call(res, data)
        }
      }.bind(this)
      
      // Override res.json to add compression
      res.json = async function(obj) {
        try {
          const data = JSON.stringify(obj)
          const compressed = await this.compressResponse(req, data)
          
          if (compressed) {
            res.setHeader('content-type', 'application/json')
            res.setHeader('content-encoding', compressed.encoding)
            res.setHeader('content-length', compressed.data.length)
            this.stats.compressedRequests++
            
            const ratio = 1 - (compressed.data.length / data.length)
            this.stats.compressionRatio = 
              (this.stats.compressionRatio * (this.stats.compressedRequests - 1) + ratio) / 
              this.stats.compressedRequests
            
            return originalSend.call(res, compressed.data)
          }
          
          return originalJson.call(res, obj)
        } catch (error) {
          logger.error('JSON compression error:', error)
          return originalJson.call(res, obj)
        }
      }.bind(this)
      
      next()
    }
  }

  /**
   * Intelligent compression algorithm selection
   */
  async compressResponse(req, data) {
    try {
      const acceptEncoding = req.headers['accept-encoding'] || ''
      const dataSize = Buffer.byteLength(data)
      
      // Skip compression for small data
      if (dataSize < 1024) return null
      
      // Check compression cache
      const cacheKey = this.generateCompressionCacheKey(data)
      if (this.compressionCache.has(cacheKey)) {
        this.stats.cacheHits++
        return this.compressionCache.get(cacheKey)
      }
      
      let algorithm = 'gzip' // Default
      let options = this.compressionOptions.gzip
      
      // Select best compression algorithm
      if (acceptEncoding.includes('br') && dataSize > 10240) {
        // Brotli for larger files (>10KB) and when supported
        algorithm = 'brotli'
        options = this.compressionOptions.brotli
      } else if (acceptEncoding.includes('gzip')) {
        algorithm = 'gzip'
        options = this.compressionOptions.gzip
      } else if (acceptEncoding.includes('deflate')) {
        algorithm = 'deflate'
        options = this.compressionOptions.deflate
      } else {
        return null // No supported compression
      }
      
      // Compress data
      const compressedData = await this.compress[algorithm].compress(
        Buffer.from(data), 
        options
      )
      
      const result = {
        data: compressedData,
        encoding: algorithm === 'brotli' ? 'br' : algorithm,
        originalSize: dataSize,
        compressedSize: compressedData.length,
        ratio: 1 - (compressedData.length / dataSize)
      }
      
      // Cache result if compression ratio is good (>10% reduction)
      if (result.ratio > 0.1) {
        this.compressionCache.set(cacheKey, result)
        
        // Limit cache size
        if (this.compressionCache.size > 1000) {
          const firstKey = this.compressionCache.keys().next().value
          this.compressionCache.delete(firstKey)
        }
      }
      
      return result
      
    } catch (error) {
      logger.error('Compression error:', error)
      return null
    }
  }

  /**
   * Generate cache key for compression
   */
  generateCompressionCacheKey(data) {
    // Use hash of data for cache key
    return crypto.createHash('sha256')
      .update(data)
      .digest('hex')
      .substring(0, 16)
  }

  /**
   * Simple hash function fallback
   */
  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(16).substring(0, 16)
  }

  /**
   * Request batching system for API optimization
   */
  setupRequestBatching() {
    this.batchProcessor = {
      queues: new Map(),
      timers: new Map(),
      maxBatchSize: 10,
      batchTimeout: 10 // 10ms
    }
    
    logger.info('✅ Request batching system initialized')
  }

  /**
   * Create request batching middleware
   */
  createBatchingMiddleware() {
    return (req, res, next) => {
      // Only batch specific API endpoints
      const batchableEndpoints = [
        '/api/sequences/batch',
        '/api/users/batch',
        '/api/analytics/batch'
      ]
      
      const isBatchable = batchableEndpoints.some(endpoint => 
        req.path.startsWith(endpoint)
      )
      
      if (!isBatchable) {
        return next()
      }
      
      // Add request to batch
      this.addToBatch(req, res, next)
    }
  }

  /**
   * Add request to batch processing queue
   */
  addToBatch(req, res, next) {
    const batchKey = `${req.method}:${req.path}`
    
    if (!this.batchProcessor.queues.has(batchKey)) {
      this.batchProcessor.queues.set(batchKey, [])
    }
    
    const queue = this.batchProcessor.queues.get(batchKey)
    queue.push({ req, res, next, timestamp: Date.now() })
    
    // Process batch if it reaches max size
    if (queue.length >= this.batchProcessor.maxBatchSize) {
      this.processBatch(batchKey)
    } else {
      // Set timer for batch processing
      if (!this.batchProcessor.timers.has(batchKey)) {
        const timer = setTimeout(() => {
          this.processBatch(batchKey)
        }, this.batchProcessor.batchTimeout)
        
        this.batchProcessor.timers.set(batchKey, timer)
      }
    }
  }

  /**
   * Process batched requests
   */
  async processBatch(batchKey) {
    const queue = this.batchProcessor.queues.get(batchKey) || []
    if (queue.length === 0) return
    
    // Clear timer
    const timer = this.batchProcessor.timers.get(batchKey)
    if (timer) {
      clearTimeout(timer)
      this.batchProcessor.timers.delete(batchKey)
    }
    
    // Process requests in parallel
    const requests = queue.splice(0)
    this.stats.batchedRequests += requests.length
    
    try {
      await Promise.all(requests.map(({ req, res, next }) => {
        return new Promise(resolve => {
          res.on('finish', resolve)
          next()
        })
      }))
      
      logger.info(`📦 Processed batch of ${requests.length} requests for ${batchKey}`)
      
    } catch (error) {
      logger.error('Batch processing error:', error)
    }
  }

  /**
   * HTTP/2 optimization setup
   */
  setupHTTP2() {
    const http2Options = {
      settings: {
        headerTableSize: 4096,
        enablePush: true,
        maxConcurrentStreams: 100,
        initialWindowSize: 65535,
        maxFrameSize: 16384,
        maxHeaderListSize: 8192
      },
      
      // Performance optimizations
      allowHTTP1: true,
      origins: ['https://convertflow.app'],
      
      // Security settings
      secureProtocol: 'TLSv1_3_method',
      ciphers: [
        'ECDHE-RSA-AES128-GCM-SHA256',
        'ECDHE-RSA-AES256-GCM-SHA384',
        'ECDHE-RSA-AES128-SHA256',
        'ECDHE-RSA-AES256-SHA384'
      ].join(':')
    }
    
    return http2Options
  }

  /**
   * Create HTTP/2 middleware
   */
  createHTTP2Middleware() {
    return (req, res, next) => {
      // Track HTTP/2 usage
      if (req.httpVersion === '2.0') {
        this.stats.http2Requests++
        
        // HTTP/2 Server Push optimization
        if (res.createPushResponse) {
          this.optimizeServerPush(req, res)
        }
      }
      
      next()
    }
  }

  /**
   * Optimize HTTP/2 Server Push
   */
  optimizeServerPush(req, res) {
    // Push critical resources based on request path
    const pushResources = {
      '/dashboard': [
        '/api/user/profile',
        '/api/sequences/recent',
        '/api/analytics/summary'
      ],
      '/sequences': [
        '/api/sequences/list',
        '/api/templates/popular'
      ]
    }
    
    const resources = pushResources[req.path]
    if (!resources) return
    
    resources.forEach(resource => {
      try {
        res.createPushResponse({
          [HTTP2_HEADER_STATUS]: 200,
          [HTTP2_HEADER_CONTENT_TYPE]: 'application/json'
        }, (err, pushStream) => {
          if (err) {
            logger.error('Server push error:', err)
            return
          }
          
          // Push resource data
          this.getPushResourceData(resource)
            .then(data => {
              pushStream.end(JSON.stringify(data))
            })
            .catch(error => {
              logger.error('Push resource error:', error)
              pushStream.end('{}')
            })
        })
      } catch (error) {
        logger.error('Create push response error:', error)
      }
    })
  }

  /**
   * Get data for pushed resources
   */
  async getPushResourceData(resource) {
    // This would integrate with your API endpoints
    // Placeholder implementation
    return { pushed: true, resource, timestamp: Date.now() }
  }

  /**
   * Connection optimization setup
   */
  setupConnectionOptimization() {
    this.connectionOptions = {
      keepAlive: true,
      keepAliveTimeout: 65000,
      headersTimeout: 60000,
      requestTimeout: 120000,
      
      // TCP optimization
      tcpNoDelay: true,
      tcpKeepAlive: true,
      allowHalfOpen: false,
      
      // Connection pooling
      maxSockets: 256,
      maxFreeSockets: 256,
      timeout: 120000,
      freeSocketTimeout: 15000
    }
    
    logger.info('✅ Connection optimization configured')
  }

  /**
   * Response streaming optimization
   */
  setupResponseStreaming() {
    this.streamingOptions = {
      highWaterMark: 16 * 1024, // 16KB chunks
      objectMode: false,
      backpressure: true
    }
    
    logger.info('✅ Response streaming configured')
  }

  /**
   * Create streaming response middleware
   */
  createStreamingMiddleware() {
    return (req, res, next) => {
      // Add streaming capabilities to response
      res.streamJSON = (data) => {
        this.streamJSONResponse(res, data)
      }
      
      res.streamArray = (array) => {
        this.streamArrayResponse(res, array)
      }
      
      next()
    }
  }

  /**
   * Stream JSON response in chunks
   */
  streamJSONResponse(res, data) {
    this.stats.streamedResponses++
    
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Transfer-Encoding', 'chunked')
    
    const jsonString = JSON.stringify(data)
    const chunkSize = 8192 // 8KB chunks
    
    res.write('{"streaming":true,"data":')
    
    for (let i = 0; i < jsonString.length; i += chunkSize) {
      const chunk = jsonString.slice(i, i + chunkSize)
      res.write(chunk)
    }
    
    res.end('}')
  }

  /**
   * Stream array response
   */
  streamArrayResponse(res, array) {
    this.stats.streamedResponses++
    
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Transfer-Encoding', 'chunked')
    
    res.write('[')
    
    array.forEach((item, index) => {
      if (index > 0) res.write(',')
      res.write(JSON.stringify(item))
    })
    
    res.end(']')
  }

  /**
   * Create comprehensive network optimization middleware
   */
  createOptimizedMiddleware() {
    return [
      this.createCompressionMiddleware(),
      this.createHTTP2Middleware(),
      this.createBatchingMiddleware(),
      this.createStreamingMiddleware()
    ]
  }

  /**
   * Network performance monitoring
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.logPerformanceMetrics()
    }, 60000) // Every minute
    
    logger.info('✅ Network performance monitoring started')
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics() {
    const compressionRate = this.stats.totalRequests > 0 
      ? (this.stats.compressedRequests / this.stats.totalRequests) * 100 
      : 0
    
    const http2Rate = this.stats.totalRequests > 0
      ? (this.stats.http2Requests / this.stats.totalRequests) * 100
      : 0
    
    logger.info('📊 Network Performance Metrics:', {
      totalRequests: this.stats.totalRequests,
      compressionRate: `${compressionRate.toFixed(1)}%`,
      avgCompressionRatio: `${(this.stats.compressionRatio * 100).toFixed(1)}%`,
      avgLatency: `${this.stats.avgLatency.toFixed(2)}ms`,
      http2Usage: `${http2Rate.toFixed(1)}%`,
      batchedRequests: this.stats.batchedRequests,
      streamedResponses: this.stats.streamedResponses,
      cacheHits: this.stats.cacheHits
    })
  }

  /**
   * Get network performance statistics
   */
  getPerformanceStats() {
    const compressionRate = this.stats.totalRequests > 0 
      ? (this.stats.compressedRequests / this.stats.totalRequests) * 100 
      : 0
    
    const http2Rate = this.stats.totalRequests > 0
      ? (this.stats.http2Requests / this.stats.totalRequests) * 100
      : 0
    
    return {
      requests: {
        total: this.stats.totalRequests,
        compressed: this.stats.compressedRequests,
        compressionRate: Math.round(compressionRate * 100) / 100,
        http2: this.stats.http2Requests,
        http2Rate: Math.round(http2Rate * 100) / 100,
        batched: this.stats.batchedRequests,
        streamed: this.stats.streamedResponses
      },
      
      performance: {
        avgLatency: Math.round(this.stats.avgLatency * 100) / 100,
        compressionRatio: Math.round(this.stats.compressionRatio * 10000) / 100,
        cacheHits: this.stats.cacheHits
      },
      
      optimization: {
        compressionCacheSize: this.compressionCache.size,
        batchQueueSize: this.batchProcessor.queues.size,
        activeTimers: this.batchProcessor.timers.size
      }
    }
  }

  /**
   * Clear network caches
   */
  clearCaches() {
    this.compressionCache.clear()
    
    // Clear batch queues
    for (const [key, timer] of this.batchProcessor.timers) {
      clearTimeout(timer)
    }
    this.batchProcessor.queues.clear()
    this.batchProcessor.timers.clear()
    
    logger.info('🗑️ Network caches cleared')
  }

  /**
   * Shutdown network optimizer
   */
  async shutdown() {
    this.clearCaches()
    logger.info('✅ Network optimizer shutdown complete')
  }
}

// Singleton instance
const networkOptimizer = new NetworkOptimizer()

export default networkOptimizer