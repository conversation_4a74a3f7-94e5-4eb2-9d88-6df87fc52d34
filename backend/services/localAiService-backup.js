import { exec } from 'child_process'
import { promisify } from 'util'
import { logger } from '../utils/logger.js'

const execAsync = promisify(exec)

class LocalAIService {
  constructor() {
    this.model = 'llama3.2:3b' // Using the local 3B model for speed
    this.isAvailable = false
    this.checkAvailability()
  }

  async checkAvailability() {
    try {
      const { stdout } = await execAsync('ollama list')
      this.isAvailable = stdout.includes('llama3.2:3b')
      if (this.isAvailable) {
        logger.info('🧠 Local AI (Ollama) is available and ready')
      } else {
        logger.warn('⚠️  Local AI model not found, falling back to demo mode')
      }
    } catch (error) {
      logger.warn('⚠️  Ollama not available:', error.message)
      this.isAvailable = false
    }
  }

  async generateWithOllama(prompt, systemPrompt = '') {
    try {
      const fullPrompt = systemPrompt ? `${systemPrompt}\n\nUser: ${prompt}` : prompt
      
      // Escape quotes for shell command
      const escapedPrompt = fullPrompt.replace(/"/g, '\\"').replace(/\$/g, '\\$')
      
      const command = `ollama run ${this.model} "${escapedPrompt}"`
      const { stdout, stderr } = await execAsync(command, { 
        timeout: 60000, // 60 second timeout
        maxBuffer: 1024 * 1024 // 1MB buffer
      })

      if (stderr && !stderr.includes('pulling')) {
        logger.warn('Ollama stderr:', stderr)
      }

      return stdout.trim()
    } catch (error) {
      logger.error('Ollama generation error:', error.message)
      throw error
    }
  }

  async generateEmailSequence(businessInfo, settings) {
    if (!this.isAvailable) {
      throw new Error('Local AI not available')
    }

    try {
      const systemPrompt = `You are an expert email marketing copywriter with 15+ years of experience. You understand psychology, persuasion, and conversion optimization. Create high-converting email sequences using proven frameworks.`

      const prompt = this.buildSequencePrompt(businessInfo, settings)
      
      logger.info(`🧠 Generating ${settings.sequenceLength}-email sequence with local AI...`)
      
      const response = await this.generateWithOllama(prompt, systemPrompt)
      
      return this.parseGeneratedSequence(response, settings, businessInfo)
      
    } catch (error) {
      logger.error('Local AI sequence generation error:', error)
      throw error
    }
  }

  buildSequencePrompt(businessInfo, settings) {
    return `Create a ${settings.sequenceLength}-email marketing sequence for this business:

BUSINESS DETAILS:
- Industry: ${businessInfo.industry}
- Product/Service: ${businessInfo.productService}
- Target Audience: ${businessInfo.targetAudience}
- Price Point: ${businessInfo.pricePoint}
- Main Benefit: ${businessInfo.mainBenefit || 'Improved results and success'}
- Pain Point: ${businessInfo.painPoint || 'Common industry challenges'}

REQUIREMENTS:
- Tone: ${settings.tone}
- Goal: ${settings.primaryGoal}
- Include CTAs: ${settings.includeCTA ? 'Yes' : 'No'}
- Personalization: ${settings.includePersonalization ? 'Yes' : 'No'}

EMAIL STRUCTURE:
Email 1: Welcome & Value Introduction
Email 2: Problem Agitation & Empathy  
Email 3: Solution Introduction & Social Proof
Email 4: Objection Handling & Value Stack
Email 5: Urgency & Scarcity
Email 6: Final Call & Last Chance
Email 7: Break-up & Door Still Open

For each email, provide:
1. Subject line (under 60 characters)
2. Email body (150-400 words)
3. Day delay (0, 1, 2, etc.)
4. Psychology triggers used
5. 3 subject line variations

Make each email compelling with clear CTAs and proven psychology principles like scarcity, social proof, urgency, and reciprocity.

Please format as a simple structure I can parse, with clear separators between emails.`
  }

  parseGeneratedSequence(content, settings, businessInfo) {
    try {
      // Parse the AI response into email structure
      const emails = []
      
      // Split content into potential email sections
      const sections = content.split(/Email \d+:/i)
      
      for (let i = 1; i < Math.min(sections.length, settings.sequenceLength + 1); i++) {
        const section = sections[i]
        
        // Extract subject line
        const subjectMatch = section.match(/Subject:?\s*([^\n]+)/i)
        const subject = subjectMatch ? subjectMatch[1].trim().replace(/["""]/g, '') : 
          this.getDefaultSubject(i, businessInfo)

        // Extract body (everything after subject, clean it up)
        let body = section
          .replace(/Subject:?\s*[^\n]+/i, '')
          .replace(/Day delay:?\s*\d+/i, '')
          .replace(/Psychology triggers?:?[^\n]*/i, '')
          .replace(/Subject line variations?:?[^\n]*/i, '')
          .trim()

        // If body is too short, use a default
        if (body.length < 50) {
          body = this.getDefaultEmailBody(i, businessInfo, settings)
        }

        // Clean up the body
        body = body
          .replace(/^\s*[-•]\s*/gm, '') // Remove bullet points at start of lines
          .replace(/\n\s*\n\s*\n/g, '\n\n') // Reduce multiple newlines
          .trim()

        emails.push({
          dayDelay: i - 1,
          subject: subject.substring(0, 100), // Limit subject length
          body: body,
          psychologyTriggers: this.identifyTriggers(subject + ' ' + body),
          conversionScore: this.calculateConversionScore(body),
          subjectLineVariations: this.generateSubjectVariations(subject, businessInfo)
        })
      }

      // Ensure we have the right number of emails
      while (emails.length < settings.sequenceLength) {
        const emailNum = emails.length + 1
        emails.push({
          dayDelay: emails.length,
          subject: this.getDefaultSubject(emailNum, businessInfo),
          body: this.getDefaultEmailBody(emailNum, businessInfo, settings),
          psychologyTriggers: ['urgency', 'social_proof'],
          conversionScore: 75,
          subjectLineVariations: this.generateSubjectVariations(
            this.getDefaultSubject(emailNum, businessInfo), 
            businessInfo
          )
        })
      }

      return {
        emails: emails.slice(0, settings.sequenceLength),
        aiAnalysis: {
          overallScore: 85,
          strengths: [
            'AI-generated with local intelligence',
            'Industry-specific content',
            'Psychological triggers integrated',
            'Progressive email sequence structure'
          ],
          improvements: [
            'Review and customize for brand voice',
            'Add specific company details',
            'Test subject line variations'
          ],
          predictedConversionRate: 4.0,
          generatedBy: 'Local AI (Ollama)'
        }
      }
      
    } catch (error) {
      logger.error('Failed to parse local AI response:', error)
      throw error
    }
  }

  getDefaultSubject(emailNum, businessInfo) {
    const subjects = [
      `Welcome to ${businessInfo.industry}! Here's what happens next...`,
      `The #1 mistake in ${businessInfo.industry} (and how to fix it)`,
      `How one client increased results by 200% with ${businessInfo.productService}`,
      `Your ${businessInfo.industry} questions answered`,
      `Limited time: Special offer for ${businessInfo.targetAudience}`,
      `Last chance: Don't miss this opportunity`,
      `We'll miss you (but the door is still open)`
    ]
    return subjects[emailNum - 1] || subjects[0]
  }

  getDefaultEmailBody(emailNum, businessInfo, settings) {
    const defaultBodies = [
      `Welcome to our community! I'm excited to help you succeed with ${businessInfo.productService}. Over the next few days, you'll discover proven strategies that work specifically for ${businessInfo.targetAudience} in the ${businessInfo.industry} space.`,
      
      `The biggest mistake I see in ${businessInfo.industry}? Focusing on features instead of benefits. Your customers don't care about what your ${businessInfo.productService} does - they care about what it does FOR THEM.`,
      
      `Let me share a success story. One of our clients in ${businessInfo.industry} was struggling with the same challenges you face. After implementing our ${businessInfo.productService}, they saw a 200% improvement in just 30 days.`,
      
      `You might be wondering if ${businessInfo.productService} is right for your ${businessInfo.industry} business. The answer depends on whether you're ready to take action and implement proven strategies.`,
      
      `This is a limited-time opportunity for ${businessInfo.targetAudience}. Our ${businessInfo.productService} has helped hundreds of businesses in ${businessInfo.industry} achieve remarkable results. At ${businessInfo.pricePoint}, it's an investment that pays for itself.`,
      
      `This is your final chance to join our exclusive program. After midnight tonight, this offer expires. Don't let this opportunity slip away - your future self will thank you for taking action today.`,
      
      `I noticed you haven't taken advantage of our ${businessInfo.productService} offer yet. That's okay - not everyone is ready for dramatic change. If you change your mind, I'm here to help. Just reply to this email.`
    ]
    
    return defaultBodies[emailNum - 1] || defaultBodies[0]
  }

  identifyTriggers(content) {
    const triggers = []
    const contentLower = content.toLowerCase()
    
    if (/\b(limited|deadline|expires|hurry|act now|don't wait|last chance)\b/.test(contentLower)) {
      triggers.push('urgency')
    }
    if (/\b(only|few left|limited spots|exclusive|special)\b/.test(contentLower)) {
      triggers.push('scarcity')
    }
    if (/\b(customers|clients|testimonial|review|success|results)\b/.test(contentLower)) {
      triggers.push('social_proof')
    }
    if (/\b(expert|proven|research|study|guarantee)\b/.test(contentLower)) {
      triggers.push('authority')
    }
    if (/\b(free|bonus|gift|value)\b/.test(contentLower)) {
      triggers.push('reciprocity')
    }
    
    return triggers.length > 0 ? triggers : ['engagement']
  }

  calculateConversionScore(emailContent) {
    let score = 60 // Base score for local AI
    
    // Check for psychological triggers
    const triggers = {
      urgency: /\b(limited|deadline|expires|hurry|act now|don't wait)\b/i,
      scarcity: /\b(only|last|few left|limited spots|exclusive)\b/i,
      social_proof: /\b(customers|clients|testimonial|review|success)\b/i,
      authority: /\b(expert|proven|research|study|guarantee)\b/i
    }
    
    Object.values(triggers).forEach(regex => {
      if (regex.test(emailContent)) score += 5
    })
    
    // Check for clear CTA
    if (/\b(click|buy|get|start|download|sign up|learn more)\b/i.test(emailContent)) {
      score += 10
    }
    
    // Check length (optimal range)
    const wordCount = emailContent.split(' ').length
    if (wordCount >= 100 && wordCount <= 500) {
      score += 5
    }
    
    return Math.min(score, 95) // Cap at 95 for local AI
  }

  generateSubjectVariations(originalSubject, businessInfo) {
    return [
      originalSubject,
      originalSubject.replace(/[!?]/, ''),
      originalSubject + ' (Limited Time)',
      `${businessInfo.industry}: ` + originalSubject.replace(/^[^:]*:\s*/, ''),
      originalSubject.replace(/\b(your|you)\b/gi, 'my')
    ]
  }

  async generateSubjectLineVariations(originalSubject, businessInfo) {
    if (!this.isAvailable) {
      return this.generateSubjectVariations(originalSubject, businessInfo)
    }

    try {
      const prompt = `Generate 3 alternative email subject lines for: "${originalSubject}"

Business: ${businessInfo.industry} - ${businessInfo.productService}
Target: ${businessInfo.targetAudience}

Requirements:
- Keep under 60 characters
- Use different psychology triggers
- Maintain the core message
- Avoid spam words

Provide only the 3 alternatives, one per line:`

      const response = await this.generateWithOllama(prompt)
      
      const variations = response
        .split('\n')
        .map(line => line.replace(/^\d+\.?\s*/, '').trim())
        .filter(line => line.length > 0 && line.length < 100)
        .slice(0, 3)

      return [originalSubject, ...variations]
      
    } catch (error) {
      logger.error('Subject line generation error:', error)
      return this.generateSubjectVariations(originalSubject, businessInfo)
    }
  }
}

export default new LocalAIService()