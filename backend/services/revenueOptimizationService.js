import CompleteUser from '../models/CompleteUser.js'
import { logger } from '../utils/logger.js'

class RevenueOptimizationService {
  constructor() {
    this.conversionTriggers = [
      'usage_limit_80',
      'usage_limit_100',
      'feature_gate',
      'success_moment',
      'social_proof',
      'time_based',
      'value_demonstration'
    ]
    
    this.pricingMatrix = {
      free: { price: 0, sequences: 5, value: 'Basic email sequences' },
      pro: { price: 29, sequences: 75, value: 'AI analytics + A/B testing' },
      business: { price: 79, sequences: 500, value: 'Team collaboration + white-label' },
      enterprise: { price: 199, sequences: -1, value: 'Custom integrations + dedicated support' }
    }
  }

  // Analyze user behavior and recommend conversion strategy
  async analyzeConversionOpportunity(userId) {
    try {
      const user = await CompleteUser.findById(userId)
      if (!user) return null

      const analysis = {
        userId,
        currentTier: user.subscription.plan,
        engagementScore: user.behavior.engagementScore,
        usagePattern: this.analyzeUsagePattern(user),
        conversionProbability: 0,
        recommendedStrategy: null,
        expectedLTV: 0,
        timeToConvert: null,
        triggers: []
      }

      // Calculate conversion probability based on multiple factors
      analysis.conversionProbability = this.calculateConversionProbability(user)
      
      // Determine best conversion strategy
      analysis.recommendedStrategy = this.selectOptimalStrategy(user, analysis)
      
      // Calculate expected LTV
      analysis.expectedLTV = this.calculateExpectedLTV(user, analysis)
      
      // Identify active triggers
      analysis.triggers = this.identifyActiveTriggers(user)

      return analysis
    } catch (error) {
      logger.error('Conversion analysis error:', error)
      return null
    }
  }

  analyzeUsagePattern(user) {
    const usage = user.usage.currentPeriod
    const limits = user.planLimits
    
    return {
      sequenceUsage: limits.sequences > 0 ? usage.sequencesGenerated / limits.sequences : 0,
      emailUsage: limits.emails > 0 ? usage.emailsSent / limits.emails : 0,
      featureAdoption: Object.keys(user.behavior.featureUsage).length,
      powerUser: usage.sequencesGenerated > 0 && user.behavior.loginCount > 5,
      frequency: this.calculateUsageFrequency(user),
      trend: this.calculateUsageTrend(user)
    }
  }

  calculateConversionProbability(user) {
    let probability = 0.1 // Base probability

    // Engagement score weight (40%)
    const engagementFactor = user.behavior.engagementScore / 100
    probability += engagementFactor * 0.4

    // Usage proximity to limits (30%)
    const usage = user.usage.currentPeriod
    const limits = user.planLimits
    
    if (limits.sequences > 0) {
      const sequenceUsage = usage.sequencesGenerated / limits.sequences
      if (sequenceUsage >= 0.8) probability += 0.25
      else if (sequenceUsage >= 0.6) probability += 0.15
      else if (sequenceUsage >= 0.4) probability += 0.05
    }

    // Feature adoption (20%)
    const featuresUsed = Object.keys(user.behavior.featureUsage).length
    if (featuresUsed >= 5) probability += 0.15
    else if (featuresUsed >= 3) probability += 0.10
    else if (featuresUsed >= 2) probability += 0.05

    // Time since signup (10%)
    const daysSinceSignup = Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
    if (daysSinceSignup >= 7 && daysSinceSignup <= 30) probability += 0.08
    else if (daysSinceSignup > 30) probability += 0.05

    return Math.min(probability, 0.95) // Cap at 95%
  }

  selectOptimalStrategy(user, analysis) {
    const { currentTier, conversionProbability, usagePattern } = analysis

    // High probability users - direct approach
    if (conversionProbability >= 0.7) {
      return {
        type: 'direct_upgrade',
        message: 'Ready for Pro features',
        incentive: null,
        timing: 'immediate',
        channel: 'in_app_modal'
      }
    }

    // Medium probability - value demonstration
    if (conversionProbability >= 0.5) {
      return {
        type: 'value_demonstration',
        message: 'See what Pro can do for you',
        incentive: 'free_trial_7_days',
        timing: 'after_success_action',
        channel: 'email_sequence'
      }
    }

    // Usage-based triggers
    if (usagePattern.sequenceUsage >= 0.8) {
      return {
        type: 'usage_limit',
        message: 'You\'re almost at your limit',
        incentive: '20_percent_off_first_month',
        timing: 'immediate',
        channel: 'usage_notification'
      }
    }

    // Feature gating for power users
    if (usagePattern.powerUser && usagePattern.featureAdoption >= 3) {
      return {
        type: 'feature_gate',
        message: 'Unlock advanced AI analytics',
        incentive: 'feature_preview',
        timing: 'when_attempting_pro_feature',
        channel: 'feature_gate_modal'
      }
    }

    // Default nurturing strategy
    return {
      type: 'nurture',
      message: 'Discover more powerful features',
      incentive: 'educational_content',
      timing: 'weekly',
      channel: 'email_course'
    }
  }

  calculateExpectedLTV(user, analysis) {
    const { currentTier, conversionProbability } = analysis
    
    // Base LTV calculations (12-month periods)
    const baseLTV = {
      free: 0,
      pro: 29 * 12, // $348
      business: 79 * 12, // $948
      enterprise: 199 * 12 // $2,388
    }

    // Calculate expected upgrade path
    let expectedLTV = 0

    if (currentTier === 'free') {
      // Probability of converting to Pro
      const proConversion = conversionProbability
      expectedLTV += proConversion * baseLTV.pro

      // Probability of eventually upgrading to Business (15% of Pro users)
      const businessConversion = proConversion * 0.15
      expectedLTV += businessConversion * (baseLTV.business - baseLTV.pro)

      // Factor in retention and expansion
      expectedLTV *= this.calculateRetentionMultiplier(user)
    }

    return {
      immediate: Math.round(expectedLTV * 0.7), // First year value
      annual: Math.round(expectedLTV),
      lifetime: Math.round(expectedLTV * 2.4) // Average customer lifetime
    }
  }

  identifyActiveTriggers(user) {
    const triggers = []
    const usage = user.usage.currentPeriod
    const limits = user.planLimits

    // Usage-based triggers
    if (limits.sequences > 0) {
      const sequenceUsage = usage.sequencesGenerated / limits.sequences
      if (sequenceUsage >= 1.0) {
        triggers.push({
          type: 'usage_limit_100',
          urgency: 'high',
          message: 'You\'ve reached your sequence limit',
          action: 'upgrade_now'
        })
      } else if (sequenceUsage >= 0.8) {
        triggers.push({
          type: 'usage_limit_80',
          urgency: 'medium',
          message: 'You\'re close to your sequence limit',
          action: 'consider_upgrade'
        })
      }
    }

    // Feature-based triggers
    if (user.behavior.featureUsage.get('ai_analytics') > 0 && user.subscription.plan === 'free') {
      triggers.push({
        type: 'feature_gate',
        urgency: 'medium',
        message: 'Unlock advanced AI analytics',
        action: 'upgrade_for_analytics'
      })
    }

    // Success moment triggers
    if (usage.sequencesGenerated >= 3 && user.analytics.performance.avgOpenRate > 0.25) {
      triggers.push({
        type: 'success_moment',
        urgency: 'low',
        message: 'Great results! Scale with Pro features',
        action: 'celebrate_and_upgrade'
      })
    }

    // Time-based triggers
    const daysSinceSignup = Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
    if (daysSinceSignup === 7) {
      triggers.push({
        type: 'time_based',
        urgency: 'low',
        message: 'Week 1 complete - ready for more?',
        action: 'week_1_upgrade_offer'
      })
    }

    return triggers
  }

  calculateUsageFrequency(user) {
    const loginCount = user.behavior.loginCount
    const daysSinceSignup = Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
    
    return daysSinceSignup > 0 ? loginCount / daysSinceSignup : 0
  }

  calculateUsageTrend(user) {
    // Simple trend analysis based on recent activity
    const recentLogins = user.behavior.loginCount
    const sequencesCreated = user.usage.currentPeriod.sequencesGenerated
    
    if (recentLogins >= 5 && sequencesCreated >= 3) return 'increasing'
    if (recentLogins >= 2 && sequencesCreated >= 1) return 'stable'
    return 'decreasing'
  }

  calculateRetentionMultiplier(user) {
    // Factors that affect retention and LTV
    let multiplier = 1.0

    // Engagement score bonus
    if (user.behavior.engagementScore >= 80) multiplier += 0.3
    else if (user.behavior.engagementScore >= 60) multiplier += 0.2

    // Feature adoption bonus
    const featuresUsed = Object.keys(user.behavior.featureUsage).length
    if (featuresUsed >= 5) multiplier += 0.2
    else if (featuresUsed >= 3) multiplier += 0.1

    // Early value achievement
    if (user.usage.currentPeriod.sequencesGenerated >= 3) multiplier += 0.15

    return Math.min(multiplier, 2.0) // Cap at 2x
  }

  // Find users ready for conversion campaigns
  async findConversionOpportunities(limit = 100) {
    try {
      const opportunities = await CompleteUser.aggregate([
        {
          $match: {
            'subscription.plan': 'free',
            'behavior.engagementScore': { $gte: 40 },
            isActive: true
          }
        },
        {
          $addFields: {
            usagePercentage: {
              $divide: ['$usage.currentPeriod.sequencesGenerated', 5]
            }
          }
        },
        {
          $match: {
            usagePercentage: { $gte: 0.6 }
          }
        },
        {
          $sort: {
            'behavior.engagementScore': -1,
            usagePercentage: -1
          }
        },
        {
          $limit: limit
        }
      ])

      // Analyze each opportunity
      const analyzedOpportunities = []
      for (const user of opportunities) {
        const analysis = await this.analyzeConversionOpportunity(user._id)
        if (analysis && analysis.conversionProbability >= 0.5) {
          analyzedOpportunities.push(analysis)
        }
      }

      return analyzedOpportunities.sort((a, b) => b.conversionProbability - a.conversionProbability)
    } catch (error) {
      logger.error('Conversion opportunities search error:', error)
      return []
    }
  }

  // Generate personalized upgrade offer
  async generateUpgradeOffer(userId) {
    const analysis = await this.analyzeConversionOpportunity(userId)
    if (!analysis) return null

    const strategy = analysis.recommendedStrategy
    const user = await CompleteUser.findById(userId)

    return {
      userId,
      targetPlan: this.getRecommendedPlan(analysis),
      headline: this.generateHeadline(strategy, user),
      benefits: this.generateBenefits(user, analysis),
      incentive: this.generateIncentive(strategy, analysis),
      urgency: this.generateUrgency(strategy, user),
      socialProof: this.generateSocialProof(user),
      cta: this.generateCTA(strategy),
      expiresAt: this.calculateOfferExpiry(strategy),
      conversionProbability: analysis.conversionProbability
    }
  }

  getRecommendedPlan(analysis) {
    if (analysis.conversionProbability >= 0.8) return 'business'
    return 'pro'
  }

  generateHeadline(strategy, user) {
    const headlines = {
      direct_upgrade: `Ready to supercharge your email marketing, ${user.firstName}?`,
      value_demonstration: `See how Pro features can 3x your results`,
      usage_limit: `You're hitting your limits - time to scale up!`,
      feature_gate: `Unlock the full power of AI email intelligence`,
      nurture: `Take your email marketing to the next level`
    }

    return headlines[strategy.type] || headlines.nurture
  }

  generateBenefits(user, analysis) {
    const benefits = [
      '🧠 Advanced AI analytics and insights',
      '📊 A/B testing with statistical significance',
      '🌍 Multi-language email generation',
      '📱 Social media content creation',
      '⚡ Priority support and faster processing'
    ]

    // Personalize based on user behavior
    if (user.behavior.featureUsage.get('translation') > 0) {
      benefits.unshift('🌍 Unlimited translations in 50+ languages')
    }

    if (user.behavior.featureUsage.get('analytics') > 0) {
      benefits.unshift('📈 Advanced performance analytics and predictions')
    }

    return benefits.slice(0, 4) // Return top 4 benefits
  }

  generateIncentive(strategy, analysis) {
    if (analysis.conversionProbability >= 0.8) {
      return null // No incentive needed for high-probability users
    }

    const incentives = {
      '20_percent_off_first_month': {
        type: 'discount',
        value: 0.2,
        duration: 1,
        code: 'SCALE20'
      },
      'free_trial_7_days': {
        type: 'trial',
        duration: 7,
        features: 'all_pro_features'
      },
      'feature_preview': {
        type: 'preview',
        duration: 3,
        features: 'analytics_only'
      }
    }

    return incentives[strategy.incentive] || null
  }

  generateUrgency(strategy, user) {
    if (strategy.type === 'usage_limit') {
      return 'Your account will be limited until you upgrade'
    }

    const daysSinceSignup = Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
    if (daysSinceSignup <= 14) {
      return 'New user bonus expires in 48 hours'
    }

    return null
  }

  generateSocialProof(user) {
    // Industry-specific social proof
    const proofs = {
      'saas': 'Join 2,000+ SaaS companies using NeuroColony Pro',
      'ecommerce': 'E-commerce brands see 34% higher conversion rates',
      'agency': 'Agencies scale 3x faster with Pro features',
      'default': 'Trusted by 10,000+ marketers worldwide'
    }

    const industry = user.onboarding.industry?.toLowerCase() || 'default'
    return proofs[industry] || proofs.default
  }

  generateCTA(strategy) {
    const ctas = {
      direct_upgrade: 'Upgrade to Pro Now',
      value_demonstration: 'Start Free Trial',
      usage_limit: 'Remove Limits Now',
      feature_gate: 'Unlock Pro Features',
      nurture: 'Learn More'
    }

    return ctas[strategy.type] || ctas.nurture
  }

  calculateOfferExpiry(strategy) {
    const hours = {
      direct_upgrade: 24,
      value_demonstration: 72,
      usage_limit: 48,
      feature_gate: 168, // 1 week
      nurture: null
    }

    const expiryHours = hours[strategy.type]
    return expiryHours ? new Date(Date.now() + expiryHours * 60 * 60 * 1000) : null
  }
}

export default new RevenueOptimizationService()