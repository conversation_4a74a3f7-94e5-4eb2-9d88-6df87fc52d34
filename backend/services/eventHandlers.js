import { eventBus } from './eventBus.js'
import { logger } from '../utils/logger.js'
import usageService from './usageService.js'
import { cache } from '../utils/cache.js'

class EventHandlers {
  constructor() {
    this.setupEventHandlers()
    logger.info('🎯 Event handlers initialized')
  }

  setupEventHandlers() {
    // User lifecycle events
    this.setupUserHandlers()
    
    // Sequence generation events
    this.setupSequenceHandlers()
    
    // Usage and billing events
    this.setupUsageHandlers()
    
    // Payment events
    this.setupPaymentHandlers()
    
    // AI service events
    this.setupAIHandlers()
    
    // System monitoring events
    this.setupSystemHandlers()
  }

  setupUserHandlers() {
    // When user registers, set up their usage tracking
    eventBus.onUserRegistered(async (userData) => {
      try {
        logger.info('🎉 Processing new user registration', {
          userId: userData.userId,
          email: userData.email,
          plan: userData.plan
        })

        // Initialize usage tracking
        await usageService.initializeUserUsage(userData.userId)
        
        // Send welcome email (if email service is configured)
        eventBus.emit('email:send', {
          type: 'welcome',
          userId: userData.userId,
          email: userData.email,
          plan: userData.plan
        })

        // Track analytics
        eventBus.emit('analytics:track', {
          event: 'user_registered',
          userId: userData.userId,
          properties: {
            plan: userData.plan,
            email: userData.email
          }
        })

      } catch (error) {
        logger.error('❌ Error processing user registration:', error)
        eventBus.emitSystemAlert('error', {
          type: 'user_registration_handler_failed',
          userId: userData.userId,
          error: error.message,
          severity: 'medium'
        })
      }
    })

    // When user upgrades, update usage limits
    eventBus.on('user:upgraded', async (upgradeData) => {
      try {
        logger.info('⬆️  Processing user plan upgrade', {
          userId: upgradeData.userId,
          oldPlan: upgradeData.oldPlan,
          newPlan: upgradeData.newPlan
        })

        // Update usage limits
        await usageService.updateUserPlan(upgradeData.userId, upgradeData.newPlan)
        
        // Clear cache entries related to this user
        cache.delete(`user_limits_${upgradeData.userId}`)
        
        // Send upgrade confirmation email
        eventBus.emit('email:send', {
          type: 'plan_upgraded',
          userId: upgradeData.userId,
          email: upgradeData.email,
          oldPlan: upgradeData.oldPlan,
          newPlan: upgradeData.newPlan
        })

      } catch (error) {
        logger.error('❌ Error processing user upgrade:', error)
      }
    })
  }

  setupSequenceHandlers() {
    // When sequence is generated, update usage and analytics
    eventBus.onSequenceGenerated(async (sequenceData) => {
      try {
        logger.info('📧 Processing sequence generation', {
          sequenceId: sequenceData.sequenceId,
          userId: sequenceData.userId,
          industry: sequenceData.industry
        })

        // Update usage statistics
        const usageStats = await usageService.getUsageStats(sequenceData.userId)
        
        // Check if user is approaching limits
        if (usageStats.usagePercentage >= 80 && usageStats.usagePercentage < 95) {
          eventBus.emitUsageThresholdReached(
            { _id: sequenceData.userId },
            '80%',
            usageStats
          )
        } else if (usageStats.usagePercentage >= 95) {
          eventBus.emitUsageThresholdReached(
            { _id: sequenceData.userId },
            '95%',
            usageStats
          )
        }

        // Track analytics
        eventBus.emit('analytics:track', {
          event: 'sequence_generated',
          userId: sequenceData.userId,
          properties: {
            industry: sequenceData.industry,
            emailCount: sequenceData.emailCount,
            aiScore: sequenceData.aiScore
          }
        })

        // Update AI model performance tracking
        if (sequenceData.aiScore) {
          eventBus.emit('ai:performance_tracked', {
            score: sequenceData.aiScore,
            industry: sequenceData.industry,
            timestamp: sequenceData.timestamp
          })
        }

      } catch (error) {
        logger.error('❌ Error processing sequence generation:', error)
      }
    })

    // When sequence is shared, track engagement
    eventBus.on('sequence:shared', async (shareData) => {
      try {
        logger.info('📤 Processing sequence share', {
          sequenceId: shareData.sequenceId,
          userId: shareData.userId,
          method: shareData.shareMethod
        })

        // Track analytics
        eventBus.emit('analytics:track', {
          event: 'sequence_shared',
          userId: shareData.userId,
          properties: {
            method: shareData.shareMethod,
            sequenceId: shareData.sequenceId
          }
        })

      } catch (error) {
        logger.error('❌ Error processing sequence share:', error)
      }
    })
  }

  setupUsageHandlers() {
    // When usage threshold is reached, send notifications
    eventBus.onUsageThreshold(async (thresholdData) => {
      try {
        logger.info('⚠️  Processing usage threshold alert', {
          userId: thresholdData.userId,
          thresholdType: thresholdData.thresholdType,
          usage: `${thresholdData.currentUsage}/${thresholdData.limit}`
        })

        // Send usage warning email
        eventBus.emit('email:send', {
          type: 'usage_warning',
          userId: thresholdData.userId,
          email: thresholdData.email,
          thresholdType: thresholdData.thresholdType,
          currentUsage: thresholdData.currentUsage,
          limit: thresholdData.limit,
          percentage: thresholdData.percentage
        })

        // If at limit, check if user can enable overage
        if (thresholdData.thresholdType === 'limit') {
          const canEnableOverage = ['pro', 'business'].includes(
            await this.getUserPlan(thresholdData.userId)
          )
          
          if (canEnableOverage) {
            eventBus.emit('email:send', {
              type: 'overage_available',
              userId: thresholdData.userId,
              email: thresholdData.email
            })
          } else {
            eventBus.emit('email:send', {
              type: 'upgrade_required',
              userId: thresholdData.userId,
              email: thresholdData.email
            })
          }
        }

      } catch (error) {
        logger.error('❌ Error processing usage threshold:', error)
      }
    })

    // When overage is enabled, confirm with user
    eventBus.on('usage:overage_enabled', async (overageData) => {
      try {
        logger.info('💳 Processing overage enablement', {
          userId: overageData.userId,
          plan: overageData.plan
        })

        // Send overage confirmation email
        eventBus.emit('email:send', {
          type: 'overage_enabled',
          userId: overageData.userId,
          email: overageData.email,
          plan: overageData.plan
        })

      } catch (error) {
        logger.error('❌ Error processing overage enablement:', error)
      }
    })

    // When overage charge occurs, send receipt
    eventBus.on('usage:overage_charged', async (chargeData) => {
      try {
        logger.info('💰 Processing overage charge', {
          userId: chargeData.userId,
          amount: chargeData.chargeAmount,
          sequences: chargeData.sequenceCount
        })

        // Send overage charge receipt
        eventBus.emit('email:send', {
          type: 'overage_receipt',
          userId: chargeData.userId,
          email: chargeData.email,
          chargeAmount: chargeData.chargeAmount,
          sequenceCount: chargeData.sequenceCount
        })

      } catch (error) {
        logger.error('❌ Error processing overage charge:', error)
      }
    })
  }

  setupPaymentHandlers() {
    // When payment succeeds, update user subscription
    eventBus.onPaymentSucceeded(async (paymentData) => {
      try {
        logger.info('💳 Processing successful payment', {
          userId: paymentData.userId,
          amount: paymentData.amount,
          planType: paymentData.planType
        })

        // Update user subscription status
        // This would typically involve database updates
        
        // Send payment confirmation email
        eventBus.emit('email:send', {
          type: 'payment_receipt',
          userId: paymentData.userId,
          amount: paymentData.amount,
          currency: paymentData.currency,
          planType: paymentData.planType
        })

        // Track analytics
        eventBus.emit('analytics:track', {
          event: 'payment_succeeded',
          userId: paymentData.userId,
          properties: {
            amount: paymentData.amount,
            planType: paymentData.planType,
            paymentMethod: paymentData.paymentMethod
          }
        })

      } catch (error) {
        logger.error('❌ Error processing successful payment:', error)
      }
    })

    // When payment fails, notify user and support
    eventBus.on('payment:failed', async (paymentData) => {
      try {
        logger.error('💳 Processing failed payment', {
          userId: paymentData.userId,
          amount: paymentData.amount,
          error: paymentData.error
        })

        // Send payment failure notification
        eventBus.emit('email:send', {
          type: 'payment_failed',
          userId: paymentData.userId,
          amount: paymentData.amount,
          error: paymentData.error
        })

        // Alert support team for high-value failures
        if (paymentData.amount > 100) {
          eventBus.emitSystemAlert('payment', {
            type: 'high_value_payment_failed',
            userId: paymentData.userId,
            amount: paymentData.amount,
            error: paymentData.error,
            severity: 'high'
          })
        }

      } catch (error) {
        logger.error('❌ Error processing failed payment:', error)
      }
    })
  }

  setupAIHandlers() {
    // Track AI performance metrics
    eventBus.on('ai:request_completed', async (requestData) => {
      try {
        // Update AI performance cache
        const performanceKey = `ai_performance_${requestData.userId}`
        const existingData = cache.get(performanceKey) || {
          totalRequests: 0,
          averageDuration: 0,
          cacheHitRate: 0
        }

        existingData.totalRequests++
        existingData.averageDuration = (
          (existingData.averageDuration * (existingData.totalRequests - 1) + requestData.duration) / 
          existingData.totalRequests
        )

        if (requestData.cacheHit) {
          existingData.cacheHitRate = (existingData.cacheHitRate + 1) / existingData.totalRequests * 100
        }

        cache.set(performanceKey, existingData, { ttl: 1000 * 60 * 60 }) // 1 hour

      } catch (error) {
        logger.error('❌ Error tracking AI performance:', error)
      }
    })

    // Alert on AI service failures
    eventBus.on('ai:request_failed', async (requestData) => {
      try {
        logger.warn('🤖 AI request failed', {
          requestId: requestData.requestId,
          userId: requestData.userId,
          error: requestData.error,
          duration: requestData.duration
        })

        // Alert if multiple failures occur
        const failureKey = `ai_failures_${Date.now() - (Date.now() % (5 * 60 * 1000))}` // 5-minute window
        const failureCount = (cache.get(failureKey) || 0) + 1
        cache.set(failureKey, failureCount, { ttl: 5 * 60 * 1000 })

        if (failureCount > 5) {
          eventBus.emitSystemAlert('ai', {
            type: 'multiple_ai_failures',
            failureCount,
            timeWindow: '5 minutes',
            severity: 'high'
          })
        }

      } catch (error) {
        logger.error('❌ Error processing AI failure:', error)
      }
    })
  }

  setupSystemHandlers() {
    // Handle system alerts
    eventBus.onSystemAlert(async (alertData) => {
      try {
        logger.warn('🚨 Processing system alert', {
          alertType: alertData.alertType,
          severity: alertData.severity
        })

        // For high-severity alerts, notify administrators
        if (alertData.severity === 'high') {
          eventBus.emit('email:send', {
            type: 'system_alert',
            recipients: process.env.ADMIN_EMAILS?.split(',') || [],
            alertType: alertData.alertType,
            details: alertData.details,
            severity: alertData.severity
          })
        }

        // Track system alerts in cache for monitoring dashboard
        const alertKey = `system_alerts_${alertData.alertType}`
        const alertCount = (cache.get(alertKey) || 0) + 1
        cache.set(alertKey, alertCount, { ttl: 60 * 60 * 1000 }) // 1 hour

      } catch (error) {
        logger.error('❌ Error processing system alert:', error)
      }
    })

    // Handle circuit breaker events
    eventBus.on('circuit_breaker:opened', async (breakerData) => {
      try {
        logger.error('🔌 Circuit breaker opened', {
          serviceName: breakerData.serviceName
        })

        eventBus.emitSystemAlert('circuit_breaker', {
          type: 'circuit_breaker_opened',
          serviceName: breakerData.serviceName,
          severity: 'high'
        })

      } catch (error) {
        logger.error('❌ Error processing circuit breaker event:', error)
      }
    })
  }

  // Helper methods
  async getUserPlan(userId) {
    try {
      // This would typically fetch from database
      // For now, return a default
      return 'free'
    } catch (error) {
      logger.error('Error fetching user plan:', error)
      return 'free'
    }
  }

  // Get event handler statistics
  getStats() {
    return {
      handlersActive: true,
      eventBusStats: eventBus.getStats(),
      setupComplete: true
    }
  }

  // Health check for event handlers
  healthCheck() {
    const eventBusHealth = eventBus.healthCheck()
    
    return {
      status: eventBusHealth.status,
      eventBus: eventBusHealth,
      handlersActive: true
    }
  }
}

// Export singleton instance
export const eventHandlers = new EventHandlers()
export default eventHandlers