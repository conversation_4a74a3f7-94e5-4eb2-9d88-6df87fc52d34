import { logger } from '../utils/logger.js'
import User from '../models/User.js'
import EmailSequence from '../models/EmailSequence.js'
import aiService from './aiService.js'

/**
 * User Onboarding Service
 * Guides new users through the platform and increases activation rates
 */
class OnboardingService {
  constructor() {
    this.onboardingSteps = [
      'welcome',
      'profile_setup',
      'first_sequence',
      'customize_sequence',
      'understand_analytics',
      'explore_features',
      'consider_upgrade'
    ]
    
    this.demoBusinessInfo = {
      industry: 'e-commerce',
      productService: 'Online fitness coaching',
      targetAudience: 'Busy professionals aged 25-45 who want to get fit',
      pricePoint: '$97/month',
      uniqueSellingProposition: 'Personalized workout plans that fit into busy schedules',
      mainBenefit: 'Get fit in just 20 minutes a day',
      painPoint: 'No time for traditional gym workouts'
    }
  }

  /**
   * Start onboarding process for new user
   */
  async startOnboarding(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) throw new Error('User not found')

      // Initialize onboarding progress
      const onboardingData = {
        currentStep: 'welcome',
        completedSteps: [],
        startedAt: new Date(),
        demoSequenceCreated: false,
        profileCompleted: false,
        firstRealSequenceCreated: false
      }

      await User.findByIdAndUpdate(userId, {
        'onboarding': onboardingData
      })

      logger.info('Onboarding started:', { userId })
      return onboardingData
    } catch (error) {
      logger.error('Onboarding start error:', error)
      throw error
    }
  }

  /**
   * Get onboarding progress
   */
  async getOnboardingProgress(userId) {
    try {
      const user = await User.findById(userId)
      if (!user || !user.onboarding) {
        return this.startOnboarding(userId)
      }

      return user.onboarding
    } catch (error) {
      logger.error('Get onboarding progress error:', error)
      throw error
    }
  }

  /**
   * Complete an onboarding step
   */
  async completeStep(userId, stepName, data = {}) {
    try {
      const user = await User.findById(userId)
      if (!user) throw new Error('User not found')

      if (!user.onboarding) {
        await this.startOnboarding(userId)
      }

      // Mark step as completed
      const updatedSteps = [...(user.onboarding?.completedSteps || []), stepName]
      const nextStepIndex = this.onboardingSteps.indexOf(stepName) + 1
      const nextStep = this.onboardingSteps[nextStepIndex] || 'completed'

      const updateData = {
        'onboarding.completedSteps': updatedSteps,
        'onboarding.currentStep': nextStep,
        'onboarding.lastUpdated': new Date()
      }

      // Handle specific step completions
      switch (stepName) {
        case 'profile_setup':
          updateData['onboarding.profileCompleted'] = true
          if (data.industry) updateData['profile.industry'] = data.industry
          if (data.company) updateData['profile.company'] = data.company
          break
        
        case 'first_sequence':
          updateData['onboarding.firstRealSequenceCreated'] = true
          break
      }

      await User.findByIdAndUpdate(userId, updateData)

      // Track completion event
      await this.trackOnboardingEvent(userId, `step_completed_${stepName}`, data)

      logger.info('Onboarding step completed:', { userId, stepName })
      
      return {
        completedStep: stepName,
        nextStep,
        progress: (updatedSteps.length / this.onboardingSteps.length) * 100
      }
    } catch (error) {
      logger.error('Complete onboarding step error:', error)
      throw error
    }
  }

  /**
   * Create demo sequence for new users
   */
  async createDemoSequence(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) throw new Error('User not found')

      // Check if demo sequence already exists
      const existingDemo = await EmailSequence.findOne({ 
        user: userId, 
        title: { $regex: /demo/i } 
      })

      if (existingDemo) {
        return existingDemo
      }

      const generationSettings = {
        sequenceLength: 5,
        tone: 'friendly',
        primaryGoal: 'sales',
        includeCTA: true,
        includePersonalization: true
      }

      // Generate demo sequence
      const demoSequence = await aiService.generateEmailSequence(
        this.demoBusinessInfo, 
        generationSettings
      )

      // Create sequence in database
      const sequence = new EmailSequence({
        user: userId,
        title: '🎯 Demo: Fitness Coaching Sequence',
        description: 'Sample email sequence to showcase NeuroColony capabilities',
        businessInfo: this.demoBusinessInfo,
        generationSettings,
        emails: demoSequence.emails,
        aiAnalysis: demoSequence.analysis,
        status: 'draft',
        isTemplate: false,
        tags: ['demo', 'onboarding']
      })

      await sequence.save()

      // Update onboarding progress
      await User.findByIdAndUpdate(userId, {
        'onboarding.demoSequenceCreated': true
      })

      logger.info('Demo sequence created:', { userId, sequenceId: sequence._id })
      return sequence
    } catch (error) {
      logger.error('Demo sequence creation error:', error)
      throw error
    }
  }

  /**
   * Get onboarding checklist for user
   */
  async getOnboardingChecklist(userId) {
    try {
      const user = await User.findById(userId)
      const sequences = await EmailSequence.find({ user: userId })
      
      const progress = user?.onboarding || {}
      const completedSteps = progress.completedSteps || []

      const checklist = [
        {
          id: 'welcome',
          title: 'Welcome to NeuroColony',
          description: 'Learn what makes our AI different',
          completed: completedSteps.includes('welcome'),
          required: true,
          estimatedTime: '2 min'
        },
        {
          id: 'profile_setup',
          title: 'Complete your profile',
          description: 'Tell us about your business for better AI recommendations',
          completed: completedSteps.includes('profile_setup'),
          required: true,
          estimatedTime: '3 min'
        },
        {
          id: 'first_sequence',
          title: 'Create your first sequence',
          description: 'Generate your first AI-powered email sequence',
          completed: completedSteps.includes('first_sequence') || sequences.length > 0,
          required: true,
          estimatedTime: '5 min'
        },
        {
          id: 'customize_sequence',
          title: 'Customize and optimize',
          description: 'Edit your sequence and understand AI suggestions',
          completed: completedSteps.includes('customize_sequence'),
          required: false,
          estimatedTime: '5 min'
        },
        {
          id: 'understand_analytics',
          title: 'Explore analytics',
          description: 'See how AI predicts performance and suggests improvements',
          completed: completedSteps.includes('understand_analytics'),
          required: false,
          estimatedTime: '3 min'
        },
        {
          id: 'explore_features',
          title: 'Discover advanced features',
          description: 'Learn about A/B testing, integrations, and more',
          completed: completedSteps.includes('explore_features'),
          required: false,
          estimatedTime: '5 min'
        }
      ]

      const completedCount = checklist.filter(item => item.completed).length
      const requiredCount = checklist.filter(item => item.required).length
      const requiredCompleted = checklist.filter(item => item.required && item.completed).length

      return {
        checklist,
        progress: {
          total: checklist.length,
          completed: completedCount,
          required: requiredCount,
          requiredCompleted,
          percentage: Math.round((completedCount / checklist.length) * 100),
          requiredPercentage: Math.round((requiredCompleted / requiredCount) * 100)
        },
        nextAction: this.getNextAction(checklist, user),
        showUpgradePrompt: this.shouldShowUpgradePrompt(user, sequences)
      }
    } catch (error) {
      logger.error('Get onboarding checklist error:', error)
      throw error
    }
  }

  /**
   * Get personalized getting started guide
   */
  async getPersonalizedGuide(userId) {
    try {
      const user = await User.findById(userId)
      const sequences = await EmailSequence.find({ user: userId })
      
      const userIndustry = user?.profile?.industry
      const hasSequences = sequences.length > 0
      
      const guide = {
        welcome: {
          title: 'Welcome to NeuroColony!',
          message: 'We\'ll help you create high-converting email sequences in minutes, not hours.',
          action: 'Start with your profile setup'
        },
        quickStart: this.getQuickStartSteps(userIndustry, hasSequences),
        industryTips: this.getIndustrySpecificTips(userIndustry),
        timeToValue: this.calculateTimeToValue(user, sequences),
        nextMilestones: this.getNextMilestones(user, sequences)
      }

      return guide
    } catch (error) {
      logger.error('Get personalized guide error:', error)
      throw error
    }
  }

  /**
   * Track onboarding analytics
   */
  async trackOnboardingEvent(userId, eventType, metadata = {}) {
    try {
      // This would integrate with analytics platform
      logger.info('Onboarding event tracked:', {
        userId,
        eventType,
        metadata,
        timestamp: new Date()
      })

      // Update user onboarding analytics
      const user = await User.findById(userId)
      if (user) {
        if (!user.onboarding) {
          user.onboarding = { events: [] }
        }
        if (!user.onboarding.events) {
          user.onboarding.events = []
        }

        user.onboarding.events.push({
          type: eventType,
          timestamp: new Date(),
          metadata
        })

        await user.save()
      }
    } catch (error) {
      logger.error('Onboarding tracking error:', error)
    }
  }

  /**
   * Get activation metrics
   */
  async getActivationMetrics() {
    try {
      const totalUsers = await User.countDocuments()
      const usersWithSequences = await User.countDocuments({
        '_id': { 
          $in: await EmailSequence.distinct('user')
        }
      })

      const activationRate = (usersWithSequences / totalUsers) * 100

      // Get onboarding step completion rates
      const stepCompletionRates = {}
      for (const step of this.onboardingSteps) {
        const completed = await User.countDocuments({
          'onboarding.completedSteps': step
        })
        stepCompletionRates[step] = (completed / totalUsers) * 100
      }

      return {
        totalUsers,
        usersWithSequences,
        activationRate: activationRate.toFixed(1),
        stepCompletionRates,
        averageTimeToFirstSequence: await this.calculateAverageTimeToFirstSequence()
      }
    } catch (error) {
      logger.error('Activation metrics error:', error)
      return null
    }
  }

  /**
   * Helper methods
   */
  getNextAction(checklist, user) {
    const nextIncomplete = checklist.find(item => !item.completed)
    if (!nextIncomplete) {
      return {
        type: 'completed',
        message: 'Onboarding complete! Ready to scale your email marketing.',
        action: 'Explore Pro features'
      }
    }

    return {
      type: 'next_step',
      step: nextIncomplete.id,
      message: nextIncomplete.title,
      action: nextIncomplete.description
    }
  }

  shouldShowUpgradePrompt(user, sequences) {
    const isFreeTier = user.subscription.type === 'free'
    const hasCreatedSequences = sequences.length >= 2
    const usagePercentage = user.getUsageStats().usagePercentage
    
    return isFreeTier && (hasCreatedSequences || usagePercentage > 50)
  }

  getQuickStartSteps(industry, hasSequences) {
    const baseSteps = [
      'Set up your business profile',
      'Create your first email sequence',
      'Review AI suggestions and optimize'
    ]

    if (industry) {
      baseSteps.unshift(`Explore ${industry}-specific templates`)
    }

    if (hasSequences) {
      baseSteps.push('Export to your email platform')
    }

    return baseSteps
  }

  getIndustrySpecificTips(industry) {
    const tips = {
      'e-commerce': [
        'Focus on abandoned cart recovery sequences',
        'Use scarcity and urgency triggers effectively',
        'Include product recommendations'
      ],
      'saas': [
        'Create strong onboarding sequences',
        'Focus on feature education',
        'Include case studies and social proof'
      ],
      'education': [
        'Build trust through authority positioning',
        'Use storytelling for emotional connection',
        'Include student success stories'
      ]
    }

    return tips[industry] || [
      'Start with your biggest customer pain point',
      'Use psychology triggers strategically',
      'Test different email lengths and styles'
    ]
  }

  calculateTimeToValue(user, sequences) {
    if (sequences.length === 0) {
      return 'Create your first sequence to start seeing value'
    }

    const firstSequence = sequences.sort((a, b) => a.createdAt - b.createdAt)[0]
    const timeToFirst = Math.floor((firstSequence.createdAt - user.createdAt) / (1000 * 60 * 60 * 24))
    
    return `You created your first sequence in ${timeToFirst} day${timeToFirst !== 1 ? 's' : ''}`
  }

  getNextMilestones(user, sequences) {
    const milestones = []

    if (sequences.length < 3) {
      milestones.push('Create 3 sequences to unlock usage insights')
    }

    if (user.subscription.type === 'free') {
      milestones.push('Upgrade to Pro for unlimited sequences')
    }

    if (!sequences.some(seq => seq.status === 'active')) {
      milestones.push('Activate your first sequence')
    }

    return milestones
  }

  async calculateAverageTimeToFirstSequence() {
    try {
      const usersWithSequences = await User.aggregate([
        {
          $lookup: {
            from: 'emailsequences',
            localField: '_id',
            foreignField: 'user',
            as: 'sequences'
          }
        },
        {
          $match: {
            'sequences.0': { $exists: true }
          }
        },
        {
          $project: {
            createdAt: 1,
            firstSequenceDate: { $min: '$sequences.createdAt' },
            timeToFirst: {
              $divide: [
                { $subtract: [{ $min: '$sequences.createdAt' }, '$createdAt'] },
                1000 * 60 * 60 * 24 // Convert to days
              ]
            }
          }
        },
        {
          $group: {
            _id: null,
            averageTimeToFirst: { $avg: '$timeToFirst' }
          }
        }
      ])

      return usersWithSequences.length > 0 ? 
        Math.round(usersWithSequences[0].averageTimeToFirst) : 0
    } catch (error) {
      logger.error('Calculate average time to first sequence error:', error)
      return 0
    }
  }
}

export default new OnboardingService()