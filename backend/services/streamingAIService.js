import { EventEmitter } from 'events'
import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import { logger } from '../utils/logger.js'
import localAiService from './localAiService.js'
import dotenv from 'dotenv'

dotenv.config()

// Initialize AI clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'sk-dummy-key'
})

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'sk-ant-dummy-key'
})

class StreamingAIService extends EventEmitter {
  constructor() {
    super()
    this.activeStreams = new Map()
  }

  /**
   * Generate email sequence with streaming progress
   * @param {string} streamId - Unique ID for this stream
   * @param {object} businessInfo - Business information
   * @param {object} settings - Generation settings
   * @param {string} provider - AI provider to use (claude, openai, local)
   */
  async generateStreamingSequence(streamId, businessInfo, settings, provider = 'auto') {
    try {
      // Store stream info
      this.activeStreams.set(streamId, {
        status: 'initializing',
        progress: 0,
        startTime: Date.now()
      })

      // Emit initial status
      this.emit('stream-update', {
        streamId,
        type: 'status',
        data: {
          status: 'initializing',
          progress: 0,
          message: 'Starting AI generation...'
        }
      })

      // Select provider
      const selectedProvider = await this.selectProvider(provider)
      
      this.emit('stream-update', {
        streamId,
        type: 'status',
        data: {
          status: 'generating',
          progress: 10,
          message: `Using ${selectedProvider} AI provider...`,
          provider: selectedProvider
        }
      })

      // Generate based on provider
      let result
      switch (selectedProvider) {
        case 'claude':
          result = await this.streamClaudeSequence(streamId, businessInfo, settings)
          break
        case 'openai':
          result = await this.streamOpenAISequence(streamId, businessInfo, settings)
          break
        case 'local':
          result = await this.streamLocalSequence(streamId, businessInfo, settings)
          break
        default:
          throw new Error('No AI provider available')
      }

      // Emit completion
      this.emit('stream-update', {
        streamId,
        type: 'complete',
        data: {
          status: 'completed',
          progress: 100,
          message: 'Generation complete!',
          result: result,
          duration: Date.now() - this.activeStreams.get(streamId).startTime
        }
      })

      // Clean up
      this.activeStreams.delete(streamId)
      
      return result

    } catch (error) {
      logger.error('Streaming AI generation error:', error)
      
      // Emit error
      this.emit('stream-update', {
        streamId,
        type: 'error',
        data: {
          status: 'error',
          message: error.message || 'Generation failed',
          error: error.toString()
        }
      })

      // Clean up
      this.activeStreams.delete(streamId)
      
      throw error
    }
  }

  async selectProvider(preferredProvider) {
    if (preferredProvider !== 'auto') {
      return preferredProvider
    }

    // Try providers in order of preference
    if (process.env.ANTHROPIC_API_KEY && !process.env.ANTHROPIC_API_KEY.includes('dummy')) {
      return 'claude'
    }
    
    if (process.env.OPENAI_API_KEY && !process.env.OPENAI_API_KEY.includes('dummy')) {
      return 'openai'
    }

    // Check if local AI is available
    try {
      await localAiService.checkAvailability()
      return 'local'
    } catch (error) {
      logger.warn('Local AI not available:', error.message)
    }

    throw new Error('No AI provider available')
  }

  async streamClaudeSequence(streamId, businessInfo, settings) {
    const prompt = this.buildSequencePrompt(businessInfo, settings)
    
    try {
      const stream = await anthropic.messages.create({
        model: 'claude-3-opus-20240229',
        max_tokens: 4000,
        temperature: 0.7,
        system: this.getSystemPrompt(),
        messages: [{
          role: 'user',
          content: prompt
        }],
        stream: true
      })

      let fullContent = ''
      let currentEmail = 1
      const totalEmails = settings.sequenceLength || 5

      for await (const messageStream of stream) {
        if (messageStream.type === 'content_block_delta') {
          fullContent += messageStream.delta.text
          
          // Calculate progress based on content
          const emailMatches = fullContent.match(/Email \d+:/g) || []
          const progress = Math.min(90, 10 + (emailMatches.length / totalEmails) * 80)
          
          this.emit('stream-update', {
            streamId,
            type: 'progress',
            data: {
              status: 'generating',
              progress: progress,
              message: `Generating email ${Math.min(emailMatches.length + 1, totalEmails)} of ${totalEmails}...`,
              partial: messageStream.delta.text
            }
          })
        }
      }

      return this.parseGeneratedSequence(fullContent, settings)

    } catch (error) {
      logger.error('Claude streaming error:', error)
      throw error
    }
  }

  async streamOpenAISequence(streamId, businessInfo, settings) {
    const prompt = this.buildSequencePrompt(businessInfo, settings)
    
    try {
      const stream = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000,
        stream: true
      })

      let fullContent = ''
      let currentEmail = 1
      const totalEmails = settings.sequenceLength || 5

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || ''
        fullContent += content
        
        // Calculate progress
        const emailMatches = fullContent.match(/Email \d+:/g) || []
        const progress = Math.min(90, 10 + (emailMatches.length / totalEmails) * 80)
        
        this.emit('stream-update', {
          streamId,
          type: 'progress',
          data: {
            status: 'generating',
            progress: progress,
            message: `Generating email ${Math.min(emailMatches.length + 1, totalEmails)} of ${totalEmails}...`,
            partial: content
          }
        })
      }

      return this.parseGeneratedSequence(fullContent, settings)

    } catch (error) {
      logger.error('OpenAI streaming error:', error)
      throw error
    }
  }

  async streamLocalSequence(streamId, businessInfo, settings) {
    // For local AI, we'll simulate streaming by breaking up the generation
    try {
      const totalEmails = settings.sequenceLength || 5
      const emails = []

      for (let i = 0; i < totalEmails; i++) {
        const progress = 10 + ((i + 1) / totalEmails) * 80
        
        this.emit('stream-update', {
          streamId,
          type: 'progress',
          data: {
            status: 'generating',
            progress: progress,
            message: `Generating email ${i + 1} of ${totalEmails}...`
          }
        })

        // Generate individual email
        const emailPrompt = this.buildSingleEmailPrompt(businessInfo, settings, i + 1)
        const email = await localAiService.generateSingleEmail(emailPrompt)
        emails.push(email)

        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      return {
        sequence: emails,
        metadata: {
          generatedAt: new Date(),
          provider: 'local',
          model: 'llama3'
        }
      }

    } catch (error) {
      logger.error('Local AI streaming error:', error)
      throw error
    }
  }

  getSystemPrompt() {
    return `You are an expert email marketing copywriter and conversion specialist with 15+ years of experience. 
    You understand psychology, persuasion, and what makes people buy. 
    You create email sequences that convert strangers into paying customers using proven frameworks like AIDA, PAS, and StoryBrand.
    
    Your emails should:
    - Have compelling subject lines with 30%+ open rates
    - Use conversational, engaging copy that builds trust
    - Include clear calls-to-action (CTAs)
    - Follow a logical progression from awareness to purchase
    - Address common objections and pain points
    - Use social proof and urgency appropriately`
  }

  buildSequencePrompt(businessInfo, settings) {
    const {
      businessName,
      industry,
      targetAudience,
      productDescription,
      uniqueValue,
      painPoints,
      goals
    } = businessInfo

    const {
      sequenceLength = 5,
      sequenceType = 'nurture',
      tone = 'professional',
      emailLength = 'medium'
    } = settings

    return `Create a ${sequenceLength}-email ${sequenceType} sequence for:

Business: ${businessName}
Industry: ${industry}
Target Audience: ${targetAudience}
Product/Service: ${productDescription}
Unique Value: ${uniqueValue}
Pain Points: ${painPoints}
Goals: ${goals}

Requirements:
- Tone: ${tone}
- Email Length: ${emailLength} (150-250 words)
- Include subject lines and preview text
- Each email should have a clear CTA
- Progressive value delivery
- Natural flow between emails

Format each email as:
Email X: [Title]
Subject: [Subject Line]
Preview: [Preview Text]
Body: [Email Content]
CTA: [Call to Action]

Generate all ${sequenceLength} emails now:`
  }

  buildSingleEmailPrompt(businessInfo, settings, emailNumber) {
    return `Generate email ${emailNumber} for a ${settings.sequenceType} sequence.
    
Business: ${businessInfo.businessName}
Product: ${businessInfo.productDescription}
Audience: ${businessInfo.targetAudience}

Create a ${settings.emailLength} email with:
- Compelling subject line
- Engaging body copy
- Clear call-to-action
- ${settings.tone} tone`
  }

  parseGeneratedSequence(content, settings) {
    const emails = []
    const emailRegex = /Email \d+:[\s\S]*?(?=Email \d+:|$)/g
    const matches = content.match(emailRegex) || []

    for (const match of matches) {
      const subjectMatch = match.match(/Subject:\s*(.+)/i)
      const previewMatch = match.match(/Preview:\s*(.+)/i)
      const bodyMatch = match.match(/Body:\s*([\s\S]+?)(?=CTA:|$)/i)
      const ctaMatch = match.match(/CTA:\s*(.+)/i)
      const titleMatch = match.match(/Email \d+:\s*(.+)/i)

      emails.push({
        title: titleMatch?.[1]?.trim() || 'Email',
        subject: subjectMatch?.[1]?.trim() || '',
        preview: previewMatch?.[1]?.trim() || '',
        body: bodyMatch?.[1]?.trim() || '',
        cta: ctaMatch?.[1]?.trim() || '',
        delay: this.calculateDelay(emails.length, settings.sequenceType)
      })
    }

    return {
      sequence: emails,
      metadata: {
        generatedAt: new Date(),
        sequenceType: settings.sequenceType,
        tone: settings.tone,
        length: emails.length
      }
    }
  }

  calculateDelay(index, sequenceType) {
    const delays = {
      welcome: [0, 1, 3, 7, 14],
      nurture: [0, 2, 5, 9, 14],
      sales: [0, 1, 2, 4, 7],
      're-engagement': [0, 7, 14, 21, 30]
    }

    const typeDelays = delays[sequenceType] || delays.nurture
    return typeDelays[index] || index * 3
  }

  // Cancel an active stream
  cancelStream(streamId) {
    if (this.activeStreams.has(streamId)) {
      this.emit('stream-update', {
        streamId,
        type: 'cancelled',
        data: {
          status: 'cancelled',
          message: 'Generation cancelled by user'
        }
      })
      
      this.activeStreams.delete(streamId)
      return true
    }
    return false
  }

  // Get active streams
  getActiveStreams() {
    return Array.from(this.activeStreams.entries()).map(([id, info]) => ({
      streamId: id,
      ...info
    }))
  }
}

export default new StreamingAIService()