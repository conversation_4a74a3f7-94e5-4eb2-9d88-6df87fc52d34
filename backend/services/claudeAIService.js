import Anthropic from '@anthropic-ai/sdk'
import { logger } from '../utils/logger.js'
import dotenv from 'dotenv'

dotenv.config()

/**
 * Claude 4 AI Service - Advanced email sequence generation with <PERSON>
 * Leverages <PERSON>'s superior understanding of context, nuance, and human communication
 */
class ClaudeAIService {
  constructor() {
    this.anthropic = null
    this.initialized = false
    this.initialize()
  }

  initialize() {
    try {
      if (process.env.ANTHROPIC_API_KEY && !process.env.ANTHROPIC_API_KEY.includes('dummy')) {
        this.anthropic = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY
        })
        this.initialized = true
        logger.info('✅ Claude AI Service initialized successfully')
      } else {
        logger.warn('⚠️ Claude AI Service: No valid API key found')
      }
    } catch (error) {
      logger.error('❌ Claude AI Service initialization error:', error)
    }
  }

  /**
   * Generate email sequence using Claude 4
   */
  async generateEmailSequence(businessInfo, settings) {
    if (!this.initialized) {
      throw new Error('Claude AI Service not initialized. Please provide a valid ANTHROPIC_API_KEY.')
    }

    try {
      const prompt = this.buildAdvancedSequencePrompt(businessInfo, settings)
      
      const message = await this.anthropic.messages.create({
        model: 'claude-3-opus-20240229', // Using Claude 3 Opus (most capable)
        max_tokens: 4000,
        temperature: 0.7,
        system: this.getSystemPrompt(),
        messages: [{
          role: 'user',
          content: prompt
        }]
      })

      const generatedContent = message.content[0].text
      return this.parseClaudeResponse(generatedContent, settings)
      
    } catch (error) {
      logger.error('Claude AI sequence generation error:', error)
      throw new Error(`Claude AI generation failed: ${error.message}`)
    }
  }

  /**
   * Get system prompt for Claude - specialized for email marketing
   */
  getSystemPrompt() {
    return `You are an elite email marketing strategist and conversion copywriter with deep expertise in:
    
1. **Consumer Psychology**: Understanding decision-making processes, emotional triggers, and behavioral patterns
2. **Persuasion Science**: Applying principles from Cialdini, Kahneman, and modern behavioral economics
3. **Email Marketing Best Practices**: Subject line optimization, deliverability, timing, segmentation
4. **Conversion Optimization**: A/B testing insights, CRO principles, funnel optimization
5. **Brand Voice Development**: Creating consistent, authentic communication that builds trust

Your email sequences achieve above-industry-average metrics:
- Open rates: 35-45% (vs 20% average)
- Click rates: 8-12% (vs 2.5% average)
- Conversion rates: 4-8% (vs 1-2% average)

You understand that great email marketing is about building relationships, not just selling. Every email should provide value while moving the prospect closer to a purchase decision.

When generating sequences, you:
- Use storytelling to create emotional connections
- Apply the PAS (Problem-Agitation-Solution) framework subtly
- Include social proof and credibility markers naturally
- Create urgency without being pushy
- Write in a conversational, human tone that builds rapport
- Optimize for mobile reading (short paragraphs, clear CTAs)
- Use power words and emotional triggers ethically
- Include pattern interrupts to maintain engagement`
  }

  /**
   * Build advanced prompt for Claude with deeper context
   */
  buildAdvancedSequencePrompt(businessInfo, settings) {
    return `Create a sophisticated ${settings.sequenceLength}-email marketing sequence for this business:

**BUSINESS INTELLIGENCE**
- Industry: ${businessInfo.industry}
- Product/Service: ${businessInfo.productService}
- Target Audience: ${businessInfo.targetAudience}
- Price Point: ${businessInfo.pricePoint}
- Unique Value Proposition: ${businessInfo.uniqueSellingProposition || 'To be developed'}
- Core Benefit: ${businessInfo.mainBenefit || 'To be identified'}
- Primary Pain Point: ${businessInfo.painPoint || 'To be discovered'}

**CAMPAIGN PARAMETERS**
- Tone: ${settings.tone}
- Primary Goal: ${settings.primaryGoal}
- Include CTAs: ${settings.includeCTA ? 'Yes - Multiple per email' : 'Soft CTAs only'}
- Personalization Level: ${settings.includePersonalization ? 'High - Use merge tags and behavioral triggers' : 'Standard'}

**PSYCHOLOGICAL FRAMEWORK**
Apply these principles throughout the sequence:
1. **Reciprocity**: Lead with value in every email
2. **Commitment/Consistency**: Small yes's leading to bigger commitments
3. **Social Proof**: Weave in testimonials, case studies, numbers
4. **Liking**: Build rapport through shared values and understanding
5. **Authority**: Establish expertise without arrogance
6. **Scarcity**: Ethical urgency based on real constraints
7. **Unity**: Create a sense of belonging to an exclusive group

**SEQUENCE ARCHITECTURE**
${this.getSequenceArchitecture(settings.sequenceLength)}

**OUTPUT REQUIREMENTS**
Return a JSON object with this exact structure:
{
  "emails": [
    {
      "dayDelay": 0,
      "subject": "Compelling subject line (40-50 chars ideal)",
      "preheader": "Preview text that complements subject",
      "body": "Full email body with proper formatting and personalization",
      "psychologyTriggers": ["specific triggers used"],
      "conversionScore": 85,
      "subjectLineVariations": ["A/B test variant 1", "A/B test variant 2", "A/B test variant 3"],
      "callToAction": {
        "primary": "Main CTA text",
        "secondary": "Soft CTA text"
      },
      "personalizationPoints": ["First name", "Company name", "Previous interaction"]
    }
  ],
  "aiAnalysis": {
    "overallScore": 92,
    "strengths": ["Specific strong points of this sequence"],
    "improvements": ["Specific optimization opportunities"],
    "predictedMetrics": {
      "openRate": "35-40%",
      "clickRate": "8-10%",
      "conversionRate": "4-5%"
    },
    "competitiveAdvantage": "What makes this sequence stand out",
    "implementationNotes": ["Important tips for maximum effectiveness"]
  }
}

Make each email feel like a valuable conversation with a trusted advisor. Use natural language, tell stories, and always focus on the reader's success.`
  }

  /**
   * Get sequence architecture based on length
   */
  getSequenceArchitecture(length) {
    const architectures = {
      3: `Email 1 (Day 0): Welcome & Quick Win - Deliver immediate value
Email 2 (Day 2): Problem Deep Dive & Case Study - Build understanding
Email 3 (Day 4): Solution & Irresistible Offer - Drive conversion`,
      
      5: `Email 1 (Day 0): Welcome & Quick Win - Establish relationship
Email 2 (Day 1): Problem Education - Agitate pain point subtly
Email 3 (Day 3): Success Story - Social proof and possibility
Email 4 (Day 5): Objection Handling - Remove barriers
Email 5 (Day 7): Final Opportunity - Ethical urgency`,
      
      7: `Email 1 (Day 0): Welcome & Instant Value - Hook with quick win
Email 2 (Day 1): Problem Awareness - Educational content
Email 3 (Day 2): Transformation Story - Emotional connection
Email 4 (Day 4): Product Introduction - Soft sell with value
Email 5 (Day 6): Social Proof Compilation - Overcome skepticism
Email 6 (Day 8): FAQ & Objections - Clear remaining doubts
Email 7 (Day 10): Deadline & Bonus - Create urgency`
    }
    
    return architectures[length] || architectures[5]
  }

  /**
   * Parse Claude's response with advanced error handling
   */
  parseClaudeResponse(content, settings) {
    try {
      // Claude usually provides well-formatted JSON, but let's be safe
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Claude response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      
      // Validate and enhance the response
      if (!parsed.emails || !Array.isArray(parsed.emails)) {
        throw new Error('Invalid email sequence structure from Claude')
      }

      // Ensure we have the right number of emails
      if (parsed.emails.length !== settings.sequenceLength) {
        logger.warn(`Expected ${settings.sequenceLength} emails, got ${parsed.emails.length}`)
      }

      // Enhance each email with additional metadata
      parsed.emails = parsed.emails.map((email, index) => ({
        ...email,
        sequencePosition: index + 1,
        estimatedReadTime: this.calculateReadTime(email.body),
        mobileOptimized: this.checkMobileOptimization(email.body),
        spamScore: this.calculateSpamScore(email),
        sentimentScore: this.analyzeSentiment(email.body)
      }))

      return parsed
      
    } catch (error) {
      logger.error('Failed to parse Claude response:', error)
      throw new Error('Failed to parse AI response. Please try again.')
    }
  }

  /**
   * Generate advanced subject line variations using Claude
   */
  async generateSubjectLineVariations(originalSubject, businessInfo, emailContext) {
    if (!this.initialized) {
      throw new Error('Claude AI Service not initialized')
    }

    try {
      const prompt = `Generate 5 high-converting subject line variations for this email.

Original subject: "${originalSubject}"
Business: ${businessInfo.industry} - ${businessInfo.productService}
Target audience: ${businessInfo.targetAudience}
Email context: ${emailContext}

Create variations using different psychological approaches:
1. Curiosity Gap
2. Benefit-Focused
3. Social Proof
4. Urgency/Scarcity
5. Personal/Question

Requirements:
- 30-50 characters ideal (max 60)
- Avoid spam triggers
- Test different emotional angles
- Include emoji variation if appropriate
- Mobile-optimized length

Return as JSON:
{
  "variations": [
    {
      "subject": "Subject line text",
      "approach": "Psychological approach used",
      "length": 45,
      "mobileOptimized": true,
      "predictedOpenRate": "35%",
      "emojiVersion": "📧 Subject with emoji"
    }
  ]
}`

      const message = await this.anthropic.messages.create({
        model: 'claude-3-sonnet-20240229', // Faster model for quick tasks
        max_tokens: 1000,
        temperature: 0.8,
        messages: [{
          role: 'user',
          content: prompt
        }]
      })

      const response = JSON.parse(message.content[0].text)
      return response.variations
      
    } catch (error) {
      logger.error('Subject line generation error:', error)
      throw error
    }
  }

  /**
   * Analyze competitor sequences and provide strategic insights
   */
  async analyzeCompetitorSequence(competitorEmails, yourBrand, businessInfo) {
    if (!this.initialized) {
      throw new Error('Claude AI Service not initialized')
    }

    try {
      const prompt = `Analyze this competitor email sequence and provide strategic insights for ${yourBrand}.

Competitor emails:
${JSON.stringify(competitorEmails, null, 2)}

Your brand context:
- Industry: ${businessInfo.industry}
- Product: ${businessInfo.productService}
- Target: ${businessInfo.targetAudience}
- USP: ${businessInfo.uniqueSellingProposition}

Provide comprehensive analysis:
1. Competitor's strategy breakdown
2. Strengths to learn from
3. Weaknesses to exploit
4. Opportunities for differentiation
5. Specific tactics to implement
6. Tactics to avoid

Return as JSON with actionable insights and specific recommendations.`

      const message = await this.anthropic.messages.create({
        model: 'claude-3-opus-20240229',
        max_tokens: 2000,
        temperature: 0.3,
        messages: [{
          role: 'user',
          content: prompt
        }]
      })

      return JSON.parse(message.content[0].text)
      
    } catch (error) {
      logger.error('Competitor analysis error:', error)
      throw error
    }
  }

  /**
   * Generate email content for specific industries with deep expertise
   */
  async generateIndustrySpecificContent(industry, emailType, context) {
    const industryPrompts = {
      'SaaS': 'Focus on ROI, time savings, integration capabilities, and scalability',
      'E-commerce': 'Emphasize product benefits, social proof, urgency, and visual appeal',
      'Consulting': 'Highlight expertise, case studies, transformation stories, and trust',
      'Healthcare': 'Prioritize compliance, patient outcomes, trust, and empathy',
      'Finance': 'Focus on security, ROI, compliance, and quantifiable results',
      'Education': 'Emphasize outcomes, success stories, accessibility, and support'
    }

    const industryContext = industryPrompts[industry] || 'General business communication'

    // Generate industry-specific content using Claude's deep understanding
    // Implementation continues...
  }

  /**
   * Performance prediction using Claude's analytical capabilities
   */
  async predictSequencePerformance(emailSequence, historicalData = null) {
    if (!this.initialized) {
      throw new Error('Claude AI Service not initialized')
    }

    const prompt = `Analyze this email sequence and predict its performance metrics.

Email sequence:
${JSON.stringify(emailSequence, null, 2)}

${historicalData ? `Historical performance data:
${JSON.stringify(historicalData, null, 2)}` : ''}

Based on email marketing best practices and the content quality, predict:
1. Open rates for each email
2. Click-through rates
3. Overall conversion rate
4. Unsubscribe risk
5. Engagement trajectory

Consider factors like:
- Subject line effectiveness
- Content relevance and value
- CTA clarity and positioning
- Email timing and frequency
- Mobile optimization
- Personalization level

Provide specific insights and recommendations for optimization.`

    const message = await this.anthropic.messages.create({
      model: 'claude-3-opus-20240229',
      max_tokens: 1500,
      temperature: 0.3,
      messages: [{
        role: 'user',
        content: prompt
      }]
    })

    return JSON.parse(message.content[0].text)
  }

  // Helper methods for email analysis
  calculateReadTime(text) {
    const wordsPerMinute = 200
    const wordCount = text.split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  checkMobileOptimization(text) {
    const lines = text.split('\n')
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length
    const hasShortParagraphs = lines.filter(line => line.length > 0).every(line => line.length < 300)
    return avgLineLength < 60 && hasShortParagraphs
  }

  calculateSpamScore(email) {
    const spamWords = ['free', 'guarantee', 'no obligation', 'risk-free', 'urgent', 'act now', 
                       'limited time', 'exclusive deal', 'click here', 'buy now', 'special offer']
    
    let score = 0
    const lowerBody = email.body.toLowerCase()
    const lowerSubject = email.subject.toLowerCase()
    
    spamWords.forEach(word => {
      if (lowerBody.includes(word)) score += 0.5
      if (lowerSubject.includes(word)) score += 1
    })
    
    // Check for excessive capitalization
    const capsRatio = (email.subject.match(/[A-Z]/g) || []).length / email.subject.length
    if (capsRatio > 0.3) score += 2
    
    // Check for excessive punctuation
    if (email.subject.match(/!{2,}/) || email.subject.match(/\${2,}/)) score += 2
    
    return Math.min(score, 10) // Cap at 10
  }

  analyzeSentiment(text) {
    // Simple sentiment analysis - in production, use a proper NLP library
    const positiveWords = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 
                          'perfect', 'happy', 'excited', 'delighted', 'success', 'achieve']
    const negativeWords = ['problem', 'issue', 'struggle', 'difficult', 'hard', 'frustrat', 
                          'disappoint', 'fail', 'worry', 'concern', 'unfortunate']
    
    const words = text.toLowerCase().split(/\s+/)
    let sentiment = 0
    
    words.forEach(word => {
      if (positiveWords.some(pos => word.includes(pos))) sentiment += 1
      if (negativeWords.some(neg => word.includes(neg))) sentiment -= 1
    })
    
    return sentiment / words.length // Normalize by word count
  }
}

export default new ClaudeAIService()