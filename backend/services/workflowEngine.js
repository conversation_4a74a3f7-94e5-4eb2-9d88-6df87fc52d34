import { logger } from '../utils/logger.js'
import agentEngine from './agentEngine.js'
import { EventEmitter } from 'events'

/**
 * NeuroColony Workflow Engine - Visual workflow execution system
 * Manages complex multi-agent workflows with dependency resolution and error handling
 */
class WorkflowEngine extends EventEmitter {
  constructor() {
    super()
    this.runningWorkflows = new Map() // Active workflow executions
    this.workflowDefinitions = new Map() // Saved workflow templates
    this.executionHistory = new Map() // Execution logs and analytics
    
    logger.info('🔧 Workflow Engine initialized for visual agent workflows')
  }

  /**
   * Register a workflow definition for future execution
   */
  registerWorkflow(workflowId, definition) {
    const workflow = {
      id: workflowId,
      name: definition.name,
      description: definition.description,
      nodes: definition.agents || [],
      connections: definition.connections || [],
      triggers: definition.triggers || [],
      metadata: {
        createdAt: new Date(),
        version: '1.0',
        author: definition.author,
        category: 'marketing-automation'
      },
      settings: {
        maxConcurrency: definition.maxConcurrency || 3,
        timeout: definition.timeout || 300000, // 5 minutes
        retryPolicy: definition.retryPolicy || { attempts: 2, delay: 5000 }
      }
    }

    this.workflowDefinitions.set(workflowId, workflow)
    this.emit('workflowRegistered', { workflowId, workflow })
    
    logger.info(`📝 Workflow registered: ${workflow.name} (${workflowId})`)
    return workflow
  }

  /**
   * Execute a complete workflow with dependency resolution
   */
  async executeWorkflow(workflowId, inputs = {}, executionContext = {}) {
    const executionId = this.generateExecutionId()
    
    try {
      logger.info(`🚀 Starting workflow execution: ${workflowId} (${executionId})`)
      
      const workflowDef = this.workflowDefinitions.get(workflowId)
      if (!workflowDef) {
        throw new Error(`Workflow ${workflowId} not found`)
      }

      // Create execution state
      const execution = {
        id: executionId,
        workflowId,
        workflowName: workflowDef.name,
        inputs,
        context: executionContext,
        startTime: new Date(),
        status: 'running',
        progress: 0,
        nodes: this.initializeNodeStates(workflowDef.nodes),
        connections: workflowDef.connections,
        results: new Map(),
        errors: [],
        metrics: {
          totalNodes: workflowDef.nodes.length,
          completedNodes: 0,
          failedNodes: 0,
          executionTimes: new Map()
        }
      }

      this.runningWorkflows.set(executionId, execution)
      this.emit('workflowStarted', execution)

      // Execute workflow using topological sort for dependency resolution
      const executionOrder = this.resolveExecutionOrder(workflowDef.nodes, workflowDef.connections)
      
      for (const nodeGroup of executionOrder) {
        await this.executeNodeGroup(execution, nodeGroup)
        
        // Update progress
        execution.progress = (execution.metrics.completedNodes / execution.metrics.totalNodes) * 100
        this.emit('workflowProgress', { executionId, progress: execution.progress })
        
        // Check for critical failures
        if (execution.errors.length > 0 && this.hasCriticalFailure(execution)) {
          throw new Error(`Workflow stopped due to critical failure in ${execution.errors[execution.errors.length - 1].nodeId}`)
        }
      }

      // Complete execution
      execution.status = execution.errors.length > 0 ? 'completed_with_errors' : 'completed'
      execution.endTime = new Date()
      execution.duration = execution.endTime - execution.startTime
      execution.progress = 100

      this.emit('workflowCompleted', execution)
      logger.info(`✅ Workflow execution completed: ${workflowId} (${execution.duration}ms)`)

      // Store execution history
      this.executionHistory.set(executionId, execution)

      return {
        executionId,
        status: execution.status,
        duration: execution.duration,
        results: Object.fromEntries(execution.results),
        metrics: execution.metrics,
        errors: execution.errors
      }

    } catch (error) {
      logger.error(`❌ Workflow execution failed: ${workflowId} (${executionId})`, error)
      
      const execution = this.runningWorkflows.get(executionId)
      if (execution) {
        execution.status = 'failed'
        execution.error = error.message
        execution.endTime = new Date()
        this.executionHistory.set(executionId, execution)
      }

      this.emit('workflowFailed', { executionId, error: error.message })
      throw error
    } finally {
      this.runningWorkflows.delete(executionId)
    }
  }

  /**
   * Initialize node states for execution tracking
   */
  initializeNodeStates(nodes) {
    return nodes.map(node => ({
      ...node,
      state: {
        status: 'pending',
        startTime: null,
        endTime: null,
        duration: null,
        inputs: null,
        outputs: null,
        error: null,
        retryCount: 0
      }
    }))
  }

  /**
   * Resolve execution order using topological sort
   */
  resolveExecutionOrder(nodes, connections) {
    // Build dependency graph
    const graph = new Map()
    const inDegree = new Map()
    
    // Initialize graph
    nodes.forEach(node => {
      graph.set(node.id, [])
      inDegree.set(node.id, 0)
    })
    
    // Build edges and calculate in-degrees
    connections.forEach(conn => {
      graph.get(conn.source).push(conn.target)
      inDegree.set(conn.target, inDegree.get(conn.target) + 1)
    })
    
    // Topological sort with parallel execution groups
    const executionOrder = []
    const queue = []
    
    // Find nodes with no dependencies
    inDegree.forEach((degree, nodeId) => {
      if (degree === 0) {
        queue.push(nodeId)
      }
    })
    
    while (queue.length > 0) {
      // Current group can execute in parallel
      const currentGroup = [...queue]
      queue.length = 0
      
      executionOrder.push(currentGroup)
      
      // Process each node in current group
      currentGroup.forEach(nodeId => {
        graph.get(nodeId).forEach(dependentNode => {
          inDegree.set(dependentNode, inDegree.get(dependentNode) - 1)
          if (inDegree.get(dependentNode) === 0) {
            queue.push(dependentNode)
          }
        })
      })
    }
    
    // Check for circular dependencies
    if (executionOrder.flat().length !== nodes.length) {
      throw new Error('Circular dependency detected in workflow')
    }
    
    return executionOrder
  }

  /**
   * Execute a group of nodes that can run in parallel
   */
  async executeNodeGroup(execution, nodeIds) {
    const nodePromises = nodeIds.map(nodeId => this.executeNode(execution, nodeId))
    
    try {
      await Promise.allSettled(nodePromises)
    } catch (error) {
      logger.error(`Node group execution error: ${nodeIds.join(', ')}`, error)
    }
  }

  /**
   * Execute a single node with retry logic and error handling
   */
  async executeNode(execution, nodeId) {
    const node = execution.nodes.find(n => n.id === nodeId)
    if (!node) {
      throw new Error(`Node ${nodeId} not found in workflow`)
    }

    const maxRetries = execution.context.retryPolicy?.attempts || 2
    let lastError = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`🔄 Executing node: ${node.name} (${nodeId}) - Attempt ${attempt + 1}`)
        
        node.state.status = 'running'
        node.state.startTime = new Date()
        node.state.retryCount = attempt
        
        // Prepare inputs from previous nodes
        const nodeInputs = await this.prepareNodeInputs(execution, node)
        node.state.inputs = nodeInputs
        
        // Execute the agent
        const agentResult = await agentEngine.executeAgent(
          node.type, 
          nodeInputs, 
          {
            ...execution.context,
            workflowId: execution.workflowId,
            executionId: execution.id,
            nodeId: node.id,
            attempt: attempt + 1
          }
        )
        
        // Success
        node.state.status = 'completed'
        node.state.endTime = new Date()
        node.state.duration = node.state.endTime - node.state.startTime
        node.state.outputs = agentResult.outputs
        
        execution.results.set(nodeId, agentResult.outputs)
        execution.metrics.completedNodes++
        execution.metrics.executionTimes.set(nodeId, node.state.duration)
        
        this.emit('nodeCompleted', { 
          executionId: execution.id, 
          nodeId, 
          nodeName: node.name,
          duration: node.state.duration,
          outputs: agentResult.outputs 
        })
        
        logger.info(`✅ Node completed: ${node.name} (${node.state.duration}ms)`)
        return agentResult
        
      } catch (error) {
        lastError = error
        node.state.error = error.message
        
        if (attempt < maxRetries) {
          logger.warn(`⚠️ Node failed, retrying: ${node.name} - ${error.message}`)
          await new Promise(resolve => setTimeout(resolve, 5000)) // 5 second delay
        } else {
          // Final failure
          node.state.status = 'failed'
          node.state.endTime = new Date()
          node.state.duration = node.state.endTime - node.state.startTime
          
          execution.metrics.failedNodes++
          execution.errors.push({
            nodeId,
            nodeName: node.name,
            error: error.message,
            timestamp: new Date(),
            attempt: attempt + 1
          })
          
          this.emit('nodeFailed', { 
            executionId: execution.id, 
            nodeId, 
            nodeName: node.name,
            error: error.message 
          })
          
          logger.error(`❌ Node failed permanently: ${node.name} - ${error.message}`)
          
          // Check if this is a critical node
          if (node.configuration?.critical !== false) {
            throw error // Propagate critical failures
          }
        }
      }
    }
    
    return null // Non-critical failure
  }

  /**
   * Prepare inputs for a node based on its dependencies
   */
  async prepareNodeInputs(execution, node) {
    const inputs = { ...execution.inputs }
    
    // Add outputs from dependent nodes
    execution.connections
      .filter(conn => conn.target === node.id)
      .forEach(conn => {
        const sourceResult = execution.results.get(conn.source)
        if (sourceResult) {
          inputs[`${conn.source}_output`] = sourceResult
        }
      })
    
    // Merge with node-specific configuration
    if (node.configuration) {
      Object.assign(inputs, node.configuration)
    }
    
    return inputs
  }

  /**
   * Check if workflow has critical failures that should stop execution
   */
  hasCriticalFailure(execution) {
    return execution.errors.some(error => {
      const node = execution.nodes.find(n => n.id === error.nodeId)
      return node?.configuration?.critical !== false
    })
  }

  /**
   * Get workflow execution status
   */
  getWorkflowStatus(executionId) {
    const running = this.runningWorkflows.get(executionId)
    if (running) {
      return {
        status: 'running',
        progress: running.progress,
        startTime: running.startTime,
        duration: Date.now() - running.startTime,
        completedNodes: running.metrics.completedNodes,
        totalNodes: running.metrics.totalNodes
      }
    }
    
    const completed = this.executionHistory.get(executionId)
    if (completed) {
      return {
        status: completed.status,
        progress: 100,
        startTime: completed.startTime,
        endTime: completed.endTime,
        duration: completed.duration,
        completedNodes: completed.metrics.completedNodes,
        totalNodes: completed.metrics.totalNodes,
        errors: completed.errors
      }
    }
    
    return null
  }

  /**
   * Get all registered workflows
   */
  getWorkflows() {
    return Array.from(this.workflowDefinitions.values())
  }

  /**
   * Get workflow execution history
   */
  getExecutionHistory(workflowId = null) {
    const history = Array.from(this.executionHistory.values())
    
    if (workflowId) {
      return history.filter(execution => execution.workflowId === workflowId)
    }
    
    return history
  }

  /**
   * Cancel a running workflow
   */
  async cancelWorkflow(executionId) {
    const execution = this.runningWorkflows.get(executionId)
    if (!execution) {
      throw new Error(`Workflow execution ${executionId} not found`)
    }
    
    execution.status = 'cancelled'
    execution.endTime = new Date()
    execution.duration = execution.endTime - execution.startTime
    
    this.emit('workflowCancelled', { executionId })
    this.executionHistory.set(executionId, execution)
    this.runningWorkflows.delete(executionId)
    
    logger.info(`🛑 Workflow execution cancelled: ${executionId}`)
  }

  /**
   * Generate unique execution ID
   */
  generateExecutionId() {
    return `wf_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get workflow analytics and metrics
   */
  getWorkflowAnalytics(workflowId) {
    const executions = this.getExecutionHistory(workflowId)
    
    if (executions.length === 0) {
      return {
        totalExecutions: 0,
        successRate: 0,
        averageDuration: 0,
        mostFailedNode: null,
        performanceTrends: []
      }
    }
    
    const successful = executions.filter(e => e.status === 'completed').length
    const totalDuration = executions.reduce((sum, e) => sum + (e.duration || 0), 0)
    
    // Find most failed node
    const nodeFailures = new Map()
    executions.forEach(execution => {
      execution.errors?.forEach(error => {
        nodeFailures.set(error.nodeId, (nodeFailures.get(error.nodeId) || 0) + 1)
      })
    })
    
    const mostFailedNode = nodeFailures.size > 0 
      ? Array.from(nodeFailures.entries()).sort((a, b) => b[1] - a[1])[0]
      : null
    
    return {
      totalExecutions: executions.length,
      successRate: (successful / executions.length) * 100,
      averageDuration: totalDuration / executions.length,
      mostFailedNode: mostFailedNode ? {
        nodeId: mostFailedNode[0],
        failures: mostFailedNode[1]
      } : null,
      performanceTrends: executions.slice(-10).map(e => ({
        timestamp: e.startTime,
        duration: e.duration,
        success: e.status === 'completed'
      }))
    }
  }
}

// Export singleton instance
export default new WorkflowEngine()