import Agent from '../models/Agent.js';
import User from '../models/User.js';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger.js';

// Agent Marketplace Service - Community-driven agent sharing
class AgentMarketplaceService extends EventEmitter {
  constructor() {
    super();
    
    // Marketplace categories
    this.categories = {
      marketing: {
        name: 'Marketing & Sales',
        description: 'Email campaigns, lead generation, customer outreach',
        icon: 'megaphone'
      },
      analytics: {
        name: 'Analytics & Reporting',
        description: 'Data analysis, performance tracking, insights',
        icon: 'chart-line'
      },
      automation: {
        name: 'Process Automation',
        description: 'Workflow automation, task management, scheduling',
        icon: 'robot'
      },
      integration: {
        name: 'Integrations',
        description: 'API connectors, data sync, platform bridges',
        icon: 'plug'
      },
      content: {
        name: 'Content Generation',
        description: 'AI writing, image creation, video production',
        icon: 'pencil'
      },
      utility: {
        name: 'Utilities',
        description: 'Helper tools, converters, validators',
        icon: 'tools'
      }
    };
    
    // Featured collections
    this.collections = {
      starter: {
        name: 'Starter Pack',
        description: 'Essential agents for beginners',
        agents: []
      },
      enterprise: {
        name: 'Enterprise Suite',
        description: 'Advanced agents for large organizations',
        agents: []
      },
      trending: {
        name: 'Trending Now',
        description: 'Most popular agents this week',
        agents: []
      }
    };
    
    // Marketplace metrics
    this.metrics = {
      totalAgents: 0,
      totalDownloads: 0,
      totalCreators: 0,
      revenue: 0
    };
    
    this.initializeMarketplace();
  }

  async initializeMarketplace() {
    try {
      // Initialize default marketplace agents
      await this.seedMarketplaceAgents();
      
      // Update metrics
      await this.updateMarketplaceMetrics();
      
      logger.info('Agent Marketplace initialized');
    } catch (error) {
      logger.error('Failed to initialize marketplace:', error);
    }
  }

  // Seed marketplace with default agents
  async seedMarketplaceAgents() {
    const defaultAgents = [
      // Marketing Agents
      {
        name: 'Email Campaign Maestro',
        type: 'worker',
        category: 'marketing',
        description: 'Advanced email campaign creation with A/B testing and personalization',
        price: 0, // Free
        capabilities: [
          {
            name: 'campaign_creation',
            description: 'Create multi-step email campaigns',
            complexity: 'advanced'
          },
          {
            name: 'ab_testing',
            description: 'Generate A/B test variations',
            complexity: 'advanced'
          },
          {
            name: 'personalization',
            description: 'Dynamic content personalization',
            complexity: 'expert'
          }
        ],
        tags: ['email', 'marketing', 'campaigns', 'automation'],
        requirements: {
          minVersion: '1.0.0',
          dependencies: [],
          integrations: ['mailchimp', 'sendgrid']
        }
      },
      
      // Analytics Agents
      {
        name: 'Performance Analytics Pro',
        type: 'scout',
        category: 'analytics',
        description: 'Comprehensive performance monitoring and reporting',
        price: 29.99,
        capabilities: [
          {
            name: 'performance_tracking',
            description: 'Track KPIs and metrics',
            complexity: 'moderate'
          },
          {
            name: 'report_generation',
            description: 'Generate detailed reports',
            complexity: 'advanced'
          },
          {
            name: 'anomaly_detection',
            description: 'Detect performance anomalies',
            complexity: 'expert'
          }
        ],
        tags: ['analytics', 'reporting', 'monitoring', 'kpi'],
        requirements: {
          minVersion: '1.2.0',
          dependencies: ['data_processor'],
          integrations: ['google_analytics']
        }
      },
      
      // Automation Agents
      {
        name: 'Workflow Orchestrator Supreme',
        type: 'queen',
        category: 'automation',
        description: 'Enterprise-grade workflow orchestration with intelligent routing',
        price: 99.99,
        capabilities: [
          {
            name: 'workflow_design',
            description: 'Visual workflow builder',
            complexity: 'advanced'
          },
          {
            name: 'intelligent_routing',
            description: 'Smart task distribution',
            complexity: 'expert'
          },
          {
            name: 'error_handling',
            description: 'Advanced error recovery',
            complexity: 'expert'
          }
        ],
        tags: ['workflow', 'automation', 'orchestration', 'enterprise'],
        requirements: {
          minVersion: '2.0.0',
          dependencies: ['worker_pool', 'queue_manager'],
          integrations: []
        }
      },
      
      // Integration Agents
      {
        name: 'Universal API Connector',
        type: 'worker',
        category: 'integration',
        description: 'Connect to any REST API with intelligent mapping',
        price: 19.99,
        capabilities: [
          {
            name: 'api_discovery',
            description: 'Automatic API endpoint discovery',
            complexity: 'advanced'
          },
          {
            name: 'data_mapping',
            description: 'Intelligent field mapping',
            complexity: 'moderate'
          },
          {
            name: 'auth_handling',
            description: 'Multiple auth method support',
            complexity: 'advanced'
          }
        ],
        tags: ['api', 'integration', 'connector', 'rest'],
        requirements: {
          minVersion: '1.0.0',
          dependencies: [],
          integrations: []
        }
      },
      
      // Content Agents
      {
        name: 'Content Creator Elite',
        type: 'worker',
        category: 'content',
        description: 'AI-powered content generation for blogs, social media, and more',
        price: 49.99,
        capabilities: [
          {
            name: 'blog_writing',
            description: 'Long-form blog content',
            complexity: 'advanced'
          },
          {
            name: 'social_media',
            description: 'Social media post generation',
            complexity: 'moderate'
          },
          {
            name: 'seo_optimization',
            description: 'SEO-optimized content',
            complexity: 'expert'
          }
        ],
        tags: ['content', 'writing', 'ai', 'seo', 'blog'],
        requirements: {
          minVersion: '1.5.0',
          dependencies: ['ai_service'],
          integrations: ['wordpress', 'medium']
        }
      }
    ];
    
    // Create marketplace agents
    for (const agentData of defaultAgents) {
      const existingAgent = await Agent.findOne({
        name: agentData.name,
        isMarketplace: true
      });
      
      if (!existingAgent) {
        await Agent.create({
          ...agentData,
          isMarketplace: true,
          isPublic: true,
          isTemplate: true,
          marketplaceInfo: {
            price: agentData.price,
            currency: 'USD',
            downloads: Math.floor(Math.random() * 1000),
            rating: 4 + Math.random(),
            reviews: Math.floor(Math.random() * 100),
            creator: {
              name: 'NeuroColony Team',
              verified: true
            },
            revenue: {
              total: 0,
              lastMonth: 0
            },
            featured: Math.random() > 0.5,
            trending: Math.random() > 0.7
          },
          status: 'active',
          version: '1.0.0'
        });
        
        logger.info(`Created marketplace agent: ${agentData.name}`);
      }
    }
  }

  // Search marketplace agents
  async searchAgents(query = {}) {
    const {
      search,
      category,
      type,
      priceRange,
      tags,
      sort = 'popular',
      limit = 20,
      offset = 0
    } = query;
    
    // Build search criteria
    const criteria = {
      isMarketplace: true,
      isPublic: true,
      status: 'active'
    };
    
    if (search) {
      criteria.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    if (category) {
      criteria.category = category;
    }
    
    if (type) {
      criteria.type = type;
    }
    
    if (tags && tags.length > 0) {
      criteria.tags = { $in: tags };
    }
    
    if (priceRange) {
      criteria['marketplaceInfo.price'] = {};
      if (priceRange.min !== undefined) {
        criteria['marketplaceInfo.price'].$gte = priceRange.min;
      }
      if (priceRange.max !== undefined) {
        criteria['marketplaceInfo.price'].$lte = priceRange.max;
      }
    }
    
    // Build sort options
    let sortOptions = {};
    switch (sort) {
      case 'popular':
        sortOptions = { 'marketplaceInfo.downloads': -1 };
        break;
      case 'rating':
        sortOptions = { 'marketplaceInfo.rating': -1 };
        break;
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'price-low':
        sortOptions = { 'marketplaceInfo.price': 1 };
        break;
      case 'price-high':
        sortOptions = { 'marketplaceInfo.price': -1 };
        break;
      default:
        sortOptions = { 'marketplaceInfo.downloads': -1 };
    }
    
    // Execute search
    const [agents, total] = await Promise.all([
      Agent.find(criteria)
        .sort(sortOptions)
        .limit(limit)
        .skip(offset)
        .select('-configuration.customPrompts'),
      Agent.countDocuments(criteria)
    ]);
    
    return {
      agents: agents.map(agent => this.formatMarketplaceAgent(agent)),
      total,
      limit,
      offset
    };
  }

  // Get featured agents
  async getFeaturedAgents() {
    const featured = await Agent.find({
      isMarketplace: true,
      isPublic: true,
      status: 'active',
      'marketplaceInfo.featured': true
    })
    .sort({ 'marketplaceInfo.downloads': -1 })
    .limit(6)
    .select('-configuration.customPrompts');
    
    return featured.map(agent => this.formatMarketplaceAgent(agent));
  }

  // Get trending agents
  async getTrendingAgents() {
    // Calculate trending score based on recent downloads and ratings
    const trending = await Agent.find({
      isMarketplace: true,
      isPublic: true,
      status: 'active'
    })
    .sort({ 
      'marketplaceInfo.trending': -1,
      'marketplaceInfo.downloads': -1 
    })
    .limit(10)
    .select('-configuration.customPrompts');
    
    return trending.map(agent => this.formatMarketplaceAgent(agent));
  }

  // Get agent collections
  async getCollections() {
    const collections = {};
    
    // Starter Pack
    collections.starter = await Agent.find({
      isMarketplace: true,
      isPublic: true,
      status: 'active',
      'marketplaceInfo.price': 0
    })
    .sort({ 'marketplaceInfo.downloads': -1 })
    .limit(5)
    .select('-configuration.customPrompts');
    
    // Enterprise Suite
    collections.enterprise = await Agent.find({
      isMarketplace: true,
      isPublic: true,
      status: 'active',
      type: { $in: ['queen', 'worker'] },
      'marketplaceInfo.price': { $gte: 50 }
    })
    .sort({ 'marketplaceInfo.rating': -1 })
    .limit(5)
    .select('-configuration.customPrompts');
    
    // Format collections
    return {
      starter: {
        ...this.collections.starter,
        agents: collections.starter.map(a => this.formatMarketplaceAgent(a))
      },
      enterprise: {
        ...this.collections.enterprise,
        agents: collections.enterprise.map(a => this.formatMarketplaceAgent(a))
      },
      trending: {
        ...this.collections.trending,
        agents: await this.getTrendingAgents()
      }
    };
  }

  // Get agent details
  async getAgentDetails(agentId) {
    const agent = await Agent.findOne({
      _id: agentId,
      isMarketplace: true,
      isPublic: true
    });
    
    if (!agent) {
      throw new Error('Agent not found in marketplace');
    }
    
    // Get similar agents
    const similar = await this.getSimilarAgents(agent);
    
    // Get creator's other agents
    const creatorAgents = await Agent.find({
      'marketplaceInfo.creator.name': agent.marketplaceInfo.creator.name,
      _id: { $ne: agent._id },
      isMarketplace: true,
      isPublic: true
    })
    .limit(4)
    .select('-configuration.customPrompts');
    
    return {
      agent: this.formatMarketplaceAgent(agent, true),
      similar: similar.map(a => this.formatMarketplaceAgent(a)),
      moreFromCreator: creatorAgents.map(a => this.formatMarketplaceAgent(a))
    };
  }

  // Install agent
  async installAgent(agentId, userId, paymentInfo = null) {
    const marketplaceAgent = await Agent.findOne({
      _id: agentId,
      isMarketplace: true,
      isPublic: true
    });
    
    if (!marketplaceAgent) {
      throw new Error('Agent not found in marketplace');
    }
    
    // Check if already installed
    const existingInstall = await Agent.findOne({
      marketplaceSourceId: agentId,
      owner: userId
    });
    
    if (existingInstall) {
      throw new Error('Agent already installed');
    }
    
    // Handle payment if required
    if (marketplaceAgent.marketplaceInfo.price > 0) {
      if (!paymentInfo) {
        throw new Error('Payment required for this agent');
      }
      
      // Process payment (integrate with Stripe)
      await this.processPayment(marketplaceAgent, userId, paymentInfo);
    }
    
    // Create user's copy of the agent
    const installedAgent = new Agent({
      ...marketplaceAgent.toObject(),
      _id: undefined,
      owner: userId,
      isMarketplace: false,
      isPublic: false,
      isTemplate: false,
      marketplaceSourceId: agentId,
      installedAt: new Date(),
      status: 'active'
    });
    
    await installedAgent.save();
    
    // Update download count
    marketplaceAgent.marketplaceInfo.downloads++;
    await marketplaceAgent.save();
    
    // Emit installation event
    this.emit('agent:installed', {
      agentId: installedAgent._id,
      marketplaceId: agentId,
      userId
    });
    
    logger.info(`Agent installed: ${marketplaceAgent.name} by user ${userId}`);
    
    return installedAgent;
  }

  // Publish agent to marketplace
  async publishAgent(agentId, userId, publishInfo) {
    const agent = await Agent.findOne({
      _id: agentId,
      owner: userId
    });
    
    if (!agent) {
      throw new Error('Agent not found or unauthorized');
    }
    
    // Validate agent for marketplace
    await this.validateAgentForMarketplace(agent);
    
    // Create marketplace version
    const marketplaceAgent = new Agent({
      ...agent.toObject(),
      _id: undefined,
      isMarketplace: true,
      isPublic: true,
      isTemplate: true,
      originalAgentId: agentId,
      marketplaceInfo: {
        price: publishInfo.price || 0,
        currency: 'USD',
        downloads: 0,
        rating: 0,
        reviews: 0,
        creator: {
          id: userId,
          name: publishInfo.creatorName,
          verified: false
        },
        revenue: {
          total: 0,
          lastMonth: 0
        },
        featured: false,
        trending: false,
        description: publishInfo.description,
        screenshots: publishInfo.screenshots || [],
        documentation: publishInfo.documentation || '',
        supportEmail: publishInfo.supportEmail,
        tags: publishInfo.tags || agent.tags || []
      },
      version: publishInfo.version || '1.0.0',
      changelog: publishInfo.changelog || 'Initial release'
    });
    
    await marketplaceAgent.save();
    
    // Update original agent
    agent.marketplaceId = marketplaceAgent._id;
    agent.publishedAt = new Date();
    await agent.save();
    
    // Emit publication event
    this.emit('agent:published', {
      agentId: marketplaceAgent._id,
      originalId: agentId,
      userId
    });
    
    logger.info(`Agent published to marketplace: ${agent.name} by user ${userId}`);
    
    return marketplaceAgent;
  }

  // Update marketplace agent
  async updateMarketplaceAgent(agentId, userId, updates) {
    const agent = await Agent.findOne({
      _id: agentId,
      isMarketplace: true,
      'marketplaceInfo.creator.id': userId
    });
    
    if (!agent) {
      throw new Error('Agent not found or unauthorized');
    }
    
    // Update allowed fields
    if (updates.description) {
      agent.description = updates.description;
      agent.marketplaceInfo.description = updates.description;
    }
    
    if (updates.price !== undefined) {
      agent.marketplaceInfo.price = updates.price;
    }
    
    if (updates.tags) {
      agent.tags = updates.tags;
      agent.marketplaceInfo.tags = updates.tags;
    }
    
    if (updates.documentation) {
      agent.marketplaceInfo.documentation = updates.documentation;
    }
    
    if (updates.screenshots) {
      agent.marketplaceInfo.screenshots = updates.screenshots;
    }
    
    if (updates.version) {
      agent.version = updates.version;
      agent.changelog = updates.changelog || `Updated to version ${updates.version}`;
    }
    
    agent.updatedAt = new Date();
    await agent.save();
    
    // Notify installed users of update
    this.notifyAgentUpdate(agentId, updates.version);
    
    return agent;
  }

  // Rate and review agent
  async rateAgent(agentId, userId, rating, review = null) {
    const agent = await Agent.findOne({
      _id: agentId,
      isMarketplace: true
    });
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    // Check if user has installed the agent
    const installation = await Agent.findOne({
      marketplaceSourceId: agentId,
      owner: userId
    });
    
    if (!installation) {
      throw new Error('You must install the agent before rating it');
    }
    
    // Store review (in production, use a separate Review model)
    const reviewData = {
      userId,
      rating,
      review,
      createdAt: new Date()
    };
    
    // Update agent rating
    const currentRating = agent.marketplaceInfo.rating || 0;
    const currentReviews = agent.marketplaceInfo.reviews || 0;
    
    agent.marketplaceInfo.rating = 
      (currentRating * currentReviews + rating) / (currentReviews + 1);
    agent.marketplaceInfo.reviews = currentReviews + 1;
    
    await agent.save();
    
    return {
      agentId,
      rating: agent.marketplaceInfo.rating,
      reviews: agent.marketplaceInfo.reviews
    };
  }

  // Get creator analytics
  async getCreatorAnalytics(userId) {
    const publishedAgents = await Agent.find({
      isMarketplace: true,
      'marketplaceInfo.creator.id': userId
    });
    
    const analytics = {
      totalAgents: publishedAgents.length,
      totalDownloads: 0,
      totalRevenue: 0,
      totalRatings: 0,
      averageRating: 0,
      agents: []
    };
    
    publishedAgents.forEach(agent => {
      const info = agent.marketplaceInfo;
      analytics.totalDownloads += info.downloads || 0;
      analytics.totalRevenue += info.revenue.total || 0;
      analytics.totalRatings += info.reviews || 0;
      
      analytics.agents.push({
        id: agent._id,
        name: agent.name,
        downloads: info.downloads,
        revenue: info.revenue.total,
        rating: info.rating,
        reviews: info.reviews,
        status: agent.status
      });
    });
    
    if (analytics.totalRatings > 0) {
      const totalRatingSum = publishedAgents.reduce(
        (sum, agent) => sum + (agent.marketplaceInfo.rating * agent.marketplaceInfo.reviews),
        0
      );
      analytics.averageRating = totalRatingSum / analytics.totalRatings;
    }
    
    return analytics;
  }

  // Process payment for agent
  async processPayment(agent, userId, paymentInfo) {
    // In production, integrate with Stripe
    // This is a placeholder implementation
    
    const payment = {
      agentId: agent._id,
      userId,
      amount: agent.marketplaceInfo.price,
      currency: agent.marketplaceInfo.currency,
      status: 'completed',
      createdAt: new Date()
    };
    
    // Update agent revenue
    agent.marketplaceInfo.revenue.total += payment.amount;
    agent.marketplaceInfo.revenue.lastMonth += payment.amount;
    await agent.save();
    
    // Calculate platform fee (30%)
    const platformFee = payment.amount * 0.3;
    const creatorEarnings = payment.amount * 0.7;
    
    // Emit payment event
    this.emit('payment:processed', {
      payment,
      platformFee,
      creatorEarnings
    });
    
    return payment;
  }

  // Validate agent for marketplace
  async validateAgentForMarketplace(agent) {
    const errors = [];
    
    // Check required fields
    if (!agent.name || agent.name.length < 3) {
      errors.push('Agent name must be at least 3 characters');
    }
    
    if (!agent.description || agent.description.length < 50) {
      errors.push('Agent description must be at least 50 characters');
    }
    
    if (!agent.capabilities || agent.capabilities.length === 0) {
      errors.push('Agent must have at least one capability');
    }
    
    // Check for unique name in marketplace
    const existing = await Agent.findOne({
      name: agent.name,
      isMarketplace: true,
      _id: { $ne: agent._id }
    });
    
    if (existing) {
      errors.push('An agent with this name already exists in the marketplace');
    }
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    return true;
  }

  // Get similar agents
  async getSimilarAgents(agent) {
    const similar = await Agent.find({
      _id: { $ne: agent._id },
      isMarketplace: true,
      isPublic: true,
      status: 'active',
      $or: [
        { category: agent.category },
        { type: agent.type },
        { tags: { $in: agent.tags || [] } }
      ]
    })
    .sort({ 'marketplaceInfo.downloads': -1 })
    .limit(4)
    .select('-configuration.customPrompts');
    
    return similar;
  }

  // Notify users of agent update
  async notifyAgentUpdate(marketplaceAgentId, newVersion) {
    // Find all installations of this agent
    const installations = await Agent.find({
      marketplaceSourceId: marketplaceAgentId
    }).populate('owner');
    
    // Send notifications
    installations.forEach(installation => {
      this.emit('agent:update:available', {
        userId: installation.owner._id,
        agentId: installation._id,
        marketplaceId: marketplaceAgentId,
        currentVersion: installation.version,
        newVersion
      });
    });
  }

  // Update marketplace metrics
  async updateMarketplaceMetrics() {
    const [totalAgents, totalCreators] = await Promise.all([
      Agent.countDocuments({ isMarketplace: true }),
      Agent.distinct('marketplaceInfo.creator.id', { isMarketplace: true })
    ]);
    
    const agents = await Agent.find({ isMarketplace: true });
    
    this.metrics.totalAgents = totalAgents;
    this.metrics.totalCreators = totalCreators.length;
    this.metrics.totalDownloads = agents.reduce(
      (sum, agent) => sum + (agent.marketplaceInfo?.downloads || 0),
      0
    );
    this.metrics.revenue = agents.reduce(
      (sum, agent) => sum + (agent.marketplaceInfo?.revenue?.total || 0),
      0
    );
    
    return this.metrics;
  }

  // Format agent for marketplace display
  formatMarketplaceAgent(agent, detailed = false) {
    const formatted = {
      id: agent._id,
      name: agent.name,
      type: agent.type,
      category: agent.category,
      description: agent.description,
      version: agent.version,
      capabilities: agent.capabilities.map(cap => ({
        name: typeof cap === 'string' ? cap : cap.name,
        description: typeof cap === 'object' ? cap.description : null
      })),
      tags: agent.tags || [],
      price: agent.marketplaceInfo?.price || 0,
      currency: agent.marketplaceInfo?.currency || 'USD',
      downloads: agent.marketplaceInfo?.downloads || 0,
      rating: agent.marketplaceInfo?.rating || 0,
      reviews: agent.marketplaceInfo?.reviews || 0,
      creator: agent.marketplaceInfo?.creator || { name: 'Unknown' },
      featured: agent.marketplaceInfo?.featured || false,
      trending: agent.marketplaceInfo?.trending || false
    };
    
    if (detailed) {
      formatted.requirements = agent.requirements;
      formatted.documentation = agent.marketplaceInfo?.documentation;
      formatted.screenshots = agent.marketplaceInfo?.screenshots;
      formatted.changelog = agent.changelog;
      formatted.createdAt = agent.createdAt;
      formatted.updatedAt = agent.updatedAt;
    }
    
    return formatted;
  }

  // Get marketplace stats
  getMarketplaceStats() {
    return {
      ...this.metrics,
      categories: Object.keys(this.categories).length,
      averagePrice: this.metrics.totalAgents > 0
        ? (this.metrics.revenue / this.metrics.totalAgents).toFixed(2)
        : 0,
      popularCategory: 'marketing' // In production, calculate from data
    };
  }
}

// Export singleton instance
const agentMarketplaceService = new AgentMarketplaceService();
export default agentMarketplaceService;