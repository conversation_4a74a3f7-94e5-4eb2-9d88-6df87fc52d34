import { logger } from '../utils/logger.js'
import { sendEmail } from '../utils/email.js'
import User from '../models/User.js'

class UsageService {
  
  // Check usage and send notifications if needed
  async checkAndNotifyUsage(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }

      await user.checkAndResetPeriod()
      const notifications = user.shouldSendUsageNotification()
      
      for (const notificationType of notifications) {
        await this.sendUsageNotification(user, notificationType)
        await user.markNotificationSent(notificationType)
      }
      
      return {
        notificationsSent: notifications,
        usageStats: user.getUsageStats()
      }
      
    } catch (error) {
      logger.error('Usage check error:', error)
      throw error
    }
  }
  
  // Send usage notification emails
  async sendUsageNotification(user, type) {
    try {
      const stats = user.getUsageStats()
      let subject, template, data
      
      switch (type) {
        case 'usage_80':
          subject = '⚠️ 80% Usage Alert - NeuroColony'
          template = 'usage-warning'
          data = {
            name: user.name,
            usagePercentage: stats.usagePercentage,
            sequencesGenerated: stats.sequencesGenerated,
            sequencesLimit: stats.sequencesLimit,
            remaining: stats.sequencesLimit - stats.sequencesGenerated,
            periodEnd: stats.periodEnd.toLocaleDateString(),
            planType: user.subscription.type,
            canUpgrade: user.subscription.type === 'free'
          }
          break
          
        case 'usage_95':
          subject = '🚨 95% Usage Alert - NeuroColony'
          template = 'usage-critical'
          data = {
            name: user.name,
            usagePercentage: stats.usagePercentage,
            sequencesGenerated: stats.sequencesGenerated,
            sequencesLimit: stats.sequencesLimit,
            remaining: stats.sequencesLimit - stats.sequencesGenerated,
            periodEnd: stats.periodEnd.toLocaleDateString(),
            planType: user.subscription.type,
            canUpgrade: user.subscription.type === 'free',
            canGoOverage: stats.canGoOverage,
            overageRate: stats.overageRate
          }
          break
          
        case 'overage_consent':
          subject = '💡 Enable Overage to Continue - NeuroColony'
          template = 'overage-consent'
          data = {
            name: user.name,
            planType: user.subscription.type,
            overageRate: stats.overageRate,
            dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`
          }
          break
      }
      
      if (template) {
        await sendEmail({
          to: user.email,
          subject,
          template,
          data
        })
        
        logger.info(`Usage notification sent: ${type} to ${user.email}`)
      }
      
    } catch (error) {
      logger.error(`Failed to send ${type} notification:`, error)
      throw error
    }
  }
  
  // Check if user can generate sequence and handle overage flow
  async checkGenerationPermission(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }
      
      await user.checkAndResetPeriod()
      const stats = user.getUsageStats()
      const canGenerate = user.canGenerateSequence()
      
      let response = {
        canGenerate,
        stats,
        requiresOverageConsent: false,
        isOverage: false
      }
      
      // If at limit but can go into overage
      if (!canGenerate && stats.canGoOverage) {
        response.requiresOverageConsent = true
        response.canGenerate = false
      }
      
      // If already consented and in overage
      if (stats.sequencesGenerated >= stats.sequencesLimit && 
          user.usage.notifications.overageConsentGiven) {
        response.canGenerate = true
        response.isOverage = true
      }
      
      return response
      
    } catch (error) {
      logger.error('Generation permission check error:', error)
      throw error
    }
  }
  
  // Process sequence generation with usage tracking
  async processSequenceGeneration(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }
      
      const permission = await this.checkGenerationPermission(userId)
      
      if (!permission.canGenerate) {
        throw new Error('Usage limit exceeded. Please upgrade or enable overage billing.')
      }
      
      const beforeStats = user.getUsageStats()
      const willBeOverage = beforeStats.sequencesGenerated >= beforeStats.sequencesLimit
      
      // Increment usage
      await user.incrementUsage()
      
      // Report overage usage to Stripe if applicable
      if (willBeOverage && beforeStats.canGoOverage && user.subscription.stripeSubscriptionId) {
        try {
          const response = await fetch(`${process.env.API_URL || 'http://localhost:5000'}/api/payments/report-usage`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${userId}` // Internal call
            },
            body: JSON.stringify({ quantity: 1 })
          })
          
          if (response.ok) {
            logger.info(`Overage usage reported to Stripe for user ${userId}`)
          }
        } catch (stripeError) {
          logger.warn('Failed to report usage to Stripe:', stripeError)
          // Don't fail the generation for Stripe issues
        }
      }
      
      // Check for notifications after increment
      await this.checkAndNotifyUsage(userId)
      
      const updatedStats = user.getUsageStats()
      
      return {
        success: true,
        isOverage: willBeOverage,
        overageCharge: willBeOverage ? updatedStats.overageRate : 0,
        stats: updatedStats
      }
      
    } catch (error) {
      logger.error('Sequence generation processing error:', error)
      throw error
    }
  }
  
  // Give overage consent and set up Stripe usage billing
  async giveOverageConsent(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }
      
      const stats = user.getUsageStats()
      if (!stats.canGoOverage) {
        throw new Error('Overage billing not available for this plan')
      }
      
      // Enable overage consent
      await user.giveOverageConsent()
      
      // Set up Stripe usage billing if user has subscription
      if (user.subscription.stripeSubscriptionId) {
        try {
          const response = await fetch(`${process.env.API_URL || 'http://localhost:5000'}/api/payments/add-usage-item`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${userId}` // Internal call
            }
          })
          
          if (!response.ok) {
            logger.warn(`Failed to add Stripe usage item for user ${userId}`)
          }
        } catch (stripeError) {
          logger.warn('Stripe usage item setup failed:', stripeError)
          // Don't fail the whole operation for Stripe issues
        }
      }
      
      logger.info(`Overage consent given by user: ${userId}`)
      
      return {
        success: true,
        message: 'Overage billing enabled',
        stats: user.getUsageStats()
      }
      
    } catch (error) {
      logger.error('Overage consent error:', error)
      throw error
    }
  }
  
  // Get usage history for analytics
  async getUsageHistory(userId, months = 6) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }
      
      await user.checkAndResetPeriod()
      
      const currentStats = user.getUsageStats()
      const history = user.usage.history.slice(-months)
      
      // Calculate projections based on current usage
      const daysInPeriod = Math.ceil(
        (currentStats.periodEnd - user.usage.currentPeriod.startDate) / (1000 * 60 * 60 * 24)
      )
      const daysSoFar = Math.ceil(
        (new Date() - user.usage.currentPeriod.startDate) / (1000 * 60 * 60 * 24)
      )
      
      const projectedUsage = daysSoFar > 0 
        ? Math.round((currentStats.sequencesGenerated / daysSoFar) * daysInPeriod)
        : currentStats.sequencesGenerated
      
      const projectedOverage = Math.max(0, projectedUsage - currentStats.sequencesLimit)
      const projectedOverageCharges = projectedOverage * (currentStats.overageRate || 0)
      
      return {
        current: currentStats,
        history,
        projections: {
          estimatedMonthlyUsage: projectedUsage,
          estimatedOverageSequences: projectedOverage,
          estimatedOverageCharges: projectedOverageCharges
        }
      }
      
    } catch (error) {
      logger.error('Usage history error:', error)
      throw error
    }
  }
  
  // Calculate monthly overage charges for billing
  async calculateMonthlyOverages(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw new Error('User not found')
      }
      
      return {
        userId,
        period: {
          start: user.usage.currentPeriod.startDate,
          end: user.usage.currentPeriod.endDate
        },
        overage: {
          sequences: user.usage.currentPeriod.overageSequences,
          charges: user.usage.currentPeriod.overageCharges,
          rate: user.getSubscriptionLimits().overageRate
        },
        shouldBill: user.usage.currentPeriod.overageCharges > 0
      }
      
    } catch (error) {
      logger.error('Overage calculation error:', error)
      throw error
    }
  }
}

export default new UsageService()