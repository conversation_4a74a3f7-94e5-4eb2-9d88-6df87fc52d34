/**
 * High-Performance Analytics Service
 * Optimized aggregation pipelines for lightning-fast dashboard statistics
 */

import EmailSequence from '../models/EmailSequence.js'
import User from '../models/User.js'
import cacheService from './cacheService.js'
import { logger } from '../utils/logger.js'

class AnalyticsService {
  constructor() {
    this.aggregationCache = new Map()
  }

  /**
   * Get billing and revenue analytics for a user
   */
  async getBillingAnalytics(userId) {
    const cacheKey = `billing_analytics:${userId}`
    
    // Check cache first
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return { ...cached, _fromCache: true }
    }

    try {
      const user = await User.findById(userId)
      if (!user) throw new Error('User not found')

      const plan = user.subscriptionPlan || 'free'
      const planPricing = {
        free: { monthly: 0, yearly: 0 },
        pro: { monthly: 29, yearly: 290 },
        business: { monthly: 99, yearly: 990 },
        enterprise: { monthly: 299, yearly: 2990 }
      }

      const currentPlan = planPricing[plan] || planPricing.free
      const usage = user.usage?.currentPeriod || {}
      const limits = this.getPlanLimits(plan)

      let overageCharges = 0
      if (usage.sequencesGenerated > limits.sequences && plan !== 'free') {
        overageCharges = (usage.sequencesGenerated - limits.sequences) * 3
      }

      const analytics = {
        currentPlan: {
          name: plan,
          monthlyPrice: currentPlan.monthly,
          yearlyPrice: currentPlan.yearly
        },
        currentPeriod: {
          baseCharges: currentPlan.monthly,
          overageCharges,
          totalCharges: currentPlan.monthly + overageCharges,
          sequences: usage.sequencesGenerated || 0,
          limit: limits.sequences
        },
        projections: {
          monthlyEstimate: currentPlan.monthly + overageCharges,
          yearlyEstimate: (currentPlan.monthly + overageCharges) * 12,
          potentialSavings: currentPlan.yearly - ((currentPlan.monthly + overageCharges) * 12)
        },
        recommendations: this.generatePricingRecommendations(user, currentPlan, overageCharges)
      }

      // Cache for 5 minutes
      await cacheService.set(cacheKey, analytics, 300)

      return analytics
    } catch (error) {
      logger.error('Error getting billing analytics:', error)
      throw error
    }
  }

  /**
   * Get usage trends and predictions
   */
  async getUsageTrends(userId) {
    try {
      const user = await User.findById(userId)
      if (!user) throw new Error('User not found')

      const trends = {
        currentPeriod: user.usage?.currentPeriod || {},
        history: user.usage?.history || [],
        predictions: {},
        recommendations: []
      }

      // Calculate growth trends
      if (trends.history.length >= 2) {
        const recent = trends.history.slice(-3)
        const avgGrowth = this.calculateGrowthRate(recent)
        
        trends.predictions = {
          nextMonthEstimate: Math.round(trends.currentPeriod.sequencesGenerated * (1 + avgGrowth)),
          growthRate: avgGrowth,
          trend: avgGrowth > 0.1 ? 'increasing' : avgGrowth < -0.1 ? 'decreasing' : 'stable'
        }
      }

      trends.recommendations = this.generateUsageRecommendations(user, trends)

      return trends
    } catch (error) {
      logger.error('Error getting usage trends:', error)
      throw error
    }
  }

  /**
   * Get platform-wide revenue analytics (admin only)
   */
  async getPlatformRevenueAnalytics() {
    try {
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

      // Subscription metrics
      const subscriptionStats = await User.aggregate([
        {
          $group: {
            _id: '$subscriptionPlan',
            count: { $sum: 1 },
            totalRevenue: {
              $sum: {
                $switch: {
                  branches: [
                    { case: { $eq: ['$subscriptionPlan', 'pro'] }, then: 29 },
                    { case: { $eq: ['$subscriptionPlan', 'business'] }, then: 99 },
                    { case: { $eq: ['$subscriptionPlan', 'enterprise'] }, then: 299 }
                  ],
                  default: 0
                }
              }
            }
          }
        }
      ])

      // Usage and overage metrics
      const usageStats = await User.aggregate([
        {
          $group: {
            _id: null,
            totalSequencesGenerated: { $sum: '$usage.currentPeriod.sequencesGenerated' },
            totalOverageCharges: { $sum: '$usage.currentPeriod.overageCharges' },
            avgSequencesPerUser: { $avg: '$usage.currentPeriod.sequencesGenerated' }
          }
        }
      ])

      const mrr = subscriptionStats.reduce((sum, sub) => sum + (sub.totalRevenue * sub.count), 0)
      const arr = mrr * 12
      const overageRevenue = (usageStats[0]?.totalOverageCharges || 0)

      return {
        subscriptions: subscriptionStats,
        usage: usageStats[0] || { totalSequencesGenerated: 0, totalOverageCharges: 0, avgSequencesPerUser: 0 },
        revenue: {
          mrr,
          arr,
          overageRevenue,
          totalMonthlyRevenue: mrr + overageRevenue,
          projectedAnnualRevenue: (mrr + overageRevenue) * 12
        },
        generatedAt: now
      }
    } catch (error) {
      logger.error('Error getting platform revenue analytics:', error)
      throw error
    }
  }

  // Helper methods
  getPlanLimits(plan) {
    const limits = {
      free: { sequences: 5, emailsPerSequence: 5 },
      pro: { sequences: 75, emailsPerSequence: 15 },
      business: { sequences: 200, emailsPerSequence: 25 },
      enterprise: { sequences: Infinity, emailsPerSequence: Infinity }
    }
    return limits[plan] || limits.free
  }

  calculateGrowthRate(history) {
    if (history.length < 2) return 0
    
    let totalGrowth = 0
    for (let i = 1; i < history.length; i++) {
      const prev = history[i - 1].sequencesGenerated || 1
      const curr = history[i].sequencesGenerated || 0
      totalGrowth += (curr - prev) / prev
    }
    
    return totalGrowth / (history.length - 1)
  }

  generateUsageRecommendations(user, trends) {
    const recommendations = []
    const current = trends.currentPeriod
    const plan = user.subscriptionPlan || 'free'
    const limits = this.getPlanLimits(plan)

    if (current.sequencesGenerated >= limits.sequences * 0.8) {
      recommendations.push({
        type: 'usage_warning',
        title: 'Approaching Usage Limit',
        message: `You've used ${Math.round((current.sequencesGenerated / limits.sequences) * 100)}% of your monthly sequences.`,
        action: 'Consider upgrading your plan or monitoring usage closely.'
      })
    }

    if (plan === 'free' && current.sequencesGenerated >= 3) {
      recommendations.push({
        type: 'upgrade_suggestion',
        title: 'Unlock More Sequences',
        message: 'Upgrade to Pro to get 75 sequences per month plus advanced features.',
        action: 'Upgrade to Pro for $29/month'
      })
    }

    return recommendations
  }

  generatePricingRecommendations(user, currentPlan, overageCharges) {
    const recommendations = []
    const plan = user.subscriptionPlan || 'free'

    if (overageCharges > 0) {
      recommendations.push({
        type: 'overage_alert',
        title: 'Overage Charges Detected',
        message: `You have $${overageCharges} in overage charges this month.`,
        action: 'Consider upgrading to a higher plan'
      })
    }

    if (plan === 'pro' && overageCharges > 70) {
      recommendations.push({
        type: 'plan_upgrade',
        title: 'Business Plan Recommended',
        message: 'Your overage charges exceed the cost difference. Business plan offers better value.',
        action: 'Upgrade to Business plan for $99/month'
      })
    }

    return recommendations
  }

  /**
   * Get comprehensive dashboard statistics with single aggregation query
   * Replaces multiple separate queries with optimized pipeline
   */
  async getDashboardStats(userId) {
    const cacheKey = `dashboard_stats:${userId}`
    
    // Check cache first
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return { ...cached, _fromCache: true }
    }

    const startTime = Date.now()

    try {
      // Single optimized aggregation pipeline
      const pipeline = [
        { $match: { user: userId } },
        {
          $group: {
            _id: null,
            totalSequences: { $sum: 1 },
            totalEmails: { $sum: { $size: '$emails' } },
            avgEmailsPerSequence: { $avg: { $size: '$emails' } },
            
            // Performance metrics
            avgGenerationTime: { $avg: '$aiAnalysis.generationTimeMs' },
            avgScore: { $avg: '$aiAnalysis.averageScore' },
            
            // Industry breakdown
            industries: { 
              $push: '$businessInfo.industry' 
            },
            
            // Goal breakdown
            goals: { 
              $push: '$generationSettings.primaryGoal' 
            },
            
            // Recent sequences
            recentSequences: {
              $push: {
                $cond: {
                  if: { $gte: ['$createdAt', { $subtract: [new Date(), 7 * 24 * 60 * 60 * 1000] }] },
                  then: {
                    _id: '$_id',
                    title: '$title',
                    createdAt: '$createdAt',
                    emailCount: { $size: '$emails' },
                    industry: '$businessInfo.industry',
                    score: '$aiAnalysis.averageScore'
                  },
                  else: '$$REMOVE'
                }
              }
            },

            // Date-based analytics
            sequencesByDate: {
              $push: {
                date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                count: 1
              }
            }
          }
        },
        {
          $project: {
            totalSequences: 1,
            totalEmails: 1,
            avgEmailsPerSequence: { $round: ['$avgEmailsPerSequence', 1] },
            avgGenerationTime: { $round: ['$avgGenerationTime', 0] },
            avgScore: { $round: ['$avgScore', 2] },
            
            // Industry distribution
            industryBreakdown: {
              $arrayToObject: {
                $map: {
                  input: {
                    $setUnion: ['$industries']
                  },
                  as: 'industry',
                  in: {
                    k: '$$industry',
                    v: {
                      $size: {
                        $filter: {
                          input: '$industries',
                          cond: { $eq: ['$$this', '$$industry'] }
                        }
                      }
                    }
                  }
                }
              }
            },
            
            // Goal distribution
            goalBreakdown: {
              $arrayToObject: {
                $map: {
                  input: {
                    $setUnion: ['$goals']
                  },
                  as: 'goal',
                  in: {
                    k: '$$goal',
                    v: {
                      $size: {
                        $filter: {
                          input: '$goals',
                          cond: { $eq: ['$$this', '$$goal'] }
                        }
                      }
                    }
                  }
                }
              }
            },

            recentSequences: { $slice: ['$recentSequences', -10] },
            
            // Time-series data for charts
            dailyActivity: {
              $arrayToObject: {
                $map: {
                  input: {
                    $setUnion: [{
                      $map: {
                        input: '$sequencesByDate',
                        as: 'item',
                        in: '$$item.date'
                      }
                    }]
                  },
                  as: 'date',
                  in: {
                    k: '$$date',
                    v: {
                      $size: {
                        $filter: {
                          input: '$sequencesByDate',
                          cond: { $eq: ['$$this.date', '$$date'] }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      ]

      const result = await EmailSequence.aggregate(pipeline)
      const stats = result[0] || {
        totalSequences: 0,
        totalEmails: 0,
        avgEmailsPerSequence: 0,
        avgGenerationTime: 0,
        avgScore: 0,
        industryBreakdown: {},
        goalBreakdown: {},
        recentSequences: [],
        dailyActivity: {}
      }

      // Add performance metadata
      stats._performance = {
        queryTime: Date.now() - startTime,
        generatedAt: new Date()
      }

      // Cache for 5 minutes
      await cacheService.set(cacheKey, stats, 300)
      
      logger.info(`📊 Dashboard stats generated in ${stats._performance.queryTime}ms`)
      
      return stats

    } catch (error) {
      logger.error('Dashboard stats aggregation error:', error)
      
      // Fallback to basic stats
      return this.getBasicStats(userId)
    }
  }

  /**
   * Get user usage analytics with optimized aggregation
   */
  async getUsageAnalytics(userId) {
    const cacheKey = `usage_analytics:${userId}`
    
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return { ...cached, _fromCache: true }
    }

    try {
      const pipeline = [
        { $match: { user: userId } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 },
            totalEmails: { $sum: { $size: '$emails' } },
            avgScore: { $avg: '$aiAnalysis.averageScore' },
            industries: { $addToSet: '$businessInfo.industry' }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
        },
        {
          $group: {
            _id: null,
            dailyUsage: {
              $push: {
                date: {
                  $dateFromParts: {
                    year: '$_id.year',
                    month: '$_id.month',
                    day: '$_id.day'
                  }
                },
                sequences: '$count',
                emails: '$totalEmails',
                avgScore: '$avgScore',
                industries: '$industries'
              }
            },
            totalDays: { $sum: 1 },
            totalSequences: { $sum: '$count' },
            totalEmails: { $sum: '$totalEmails' },
            avgDailySequences: { $avg: '$count' }
          }
        }
      ]

      const result = await EmailSequence.aggregate(pipeline)
      const analytics = result[0] || {
        dailyUsage: [],
        totalDays: 0,
        totalSequences: 0,
        totalEmails: 0,
        avgDailySequences: 0
      }

      // Cache for 1 hour
      await cacheService.set(cacheKey, analytics, 3600)
      
      return analytics

    } catch (error) {
      logger.error('Usage analytics error:', error)
      return { error: error.message }
    }
  }

  /**
   * Get industry performance comparison
   */
  async getIndustryPerformance() {
    const cacheKey = 'industry_performance:global'
    
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return { ...cached, _fromCache: true }
    }

    try {
      const pipeline = [
        {
          $group: {
            _id: '$businessInfo.industry',
            sequenceCount: { $sum: 1 },
            avgScore: { $avg: '$aiAnalysis.averageScore' },
            avgEmailCount: { $avg: { $size: '$emails' } },
            avgGenerationTime: { $avg: '$aiAnalysis.generationTimeMs' },
            topGoals: { $push: '$generationSettings.primaryGoal' }
          }
        },
        {
          $project: {
            industry: '$_id',
            sequenceCount: 1,
            avgScore: { $round: ['$avgScore', 2] },
            avgEmailCount: { $round: ['$avgEmailCount', 1] },
            avgGenerationTime: { $round: ['$avgGenerationTime', 0] },
            
            // Calculate top goal for this industry
            topGoal: {
              $arrayElemAt: [
                {
                  $map: {
                    input: { $setUnion: ['$topGoals'] },
                    as: 'goal',
                    in: {
                      goal: '$$goal',
                      count: {
                        $size: {
                          $filter: {
                            input: '$topGoals',
                            cond: { $eq: ['$$this', '$$goal'] }
                          }
                        }
                      }
                    }
                  }
                },
                0
              ]
            }
          }
        },
        {
          $sort: { sequenceCount: -1 }
        },
        {
          $limit: 20
        }
      ]

      const result = await EmailSequence.aggregate(pipeline)
      
      // Cache for 24 hours (industry data changes slowly)
      await cacheService.set(cacheKey, result, 86400)
      
      return result

    } catch (error) {
      logger.error('Industry performance error:', error)
      return []
    }
  }

  /**
   * Fallback basic stats for error cases
   */
  async getBasicStats(userId) {
    try {
      const totalSequences = await EmailSequence.countDocuments({ user: userId })
      const recentSequences = await EmailSequence
        .find({ user: userId })
        .select('title createdAt emails.subject businessInfo.industry')
        .sort({ createdAt: -1 })
        .limit(5)
        .lean()

      return {
        totalSequences,
        totalEmails: 0,
        avgEmailsPerSequence: 0,
        recentSequences: recentSequences.map(seq => ({
          _id: seq._id,
          title: seq.title,
          createdAt: seq.createdAt,
          emailCount: seq.emails?.length || 0,
          industry: seq.businessInfo?.industry
        })),
        _fallback: true
      }
    } catch (error) {
      logger.error('Basic stats fallback error:', error)
      return { error: 'Unable to fetch statistics' }
    }
  }

  /**
   * Invalidate analytics caches for a user
   */
  async invalidateUserAnalytics(userId) {
    const keys = [
      `dashboard_stats:${userId}`,
      `usage_analytics:${userId}`
    ]

    for (const key of keys) {
      await cacheService.get(key, { useMemoryCache: false }) // Remove from memory
      if (cacheService.redis) {
        await cacheService.redis.del(key)
      }
    }
  }

  /**
   * Precompute analytics for active users (background job)
   */
  async precomputeAnalytics(userIds) {
    logger.info(`🔥 Precomputing analytics for ${userIds.length} users`)
    
    const promises = userIds.map(userId => 
      this.getDashboardStats(userId).catch(err => {
        logger.error(`Precompute failed for user ${userId}:`, err)
        return null
      })
    )

    const results = await Promise.allSettled(promises)
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length
    
    logger.info(`✅ Precomputed analytics for ${successful}/${userIds.length} users`)
    
    return { successful, total: userIds.length }
  }
}

export default new AnalyticsService()