import User from '../models/User.js'
import { logger } from '../utils/logger.js'
import emailService from './emailService.js'
import usageService from './usageService.js'

class StripeWebhookService {
  constructor() {
    this.eventHandlers = {
      // Subscription events
      'customer.subscription.created': this.handleSubscriptionCreated.bind(this),
      'customer.subscription.updated': this.handleSubscriptionUpdated.bind(this),
      'customer.subscription.deleted': this.handleSubscriptionDeleted.bind(this),
      'customer.subscription.trial_will_end': this.handleTrialWillEnd.bind(this),
      
      // Payment events
      'invoice.payment_succeeded': this.handlePaymentSucceeded.bind(this),
      'invoice.payment_failed': this.handlePaymentFailed.bind(this),
      'invoice.upcoming': this.handleUpcomingInvoice.bind(this),
      'invoice.finalized': this.handleInvoiceFinalized.bind(this),
      
      // Payment method events
      'payment_method.attached': this.handlePaymentMethodAttached.bind(this),
      'payment_method.detached': this.handlePaymentMethodDetached.bind(this),
      'payment_method.updated': this.handlePaymentMethodUpdated.bind(this),
      
      // Customer events
      'customer.created': this.handleCustomerCreated.bind(this),
      'customer.updated': this.handleCustomerUpdated.bind(this),
      'customer.deleted': this.handleCustomerDeleted.bind(this),
      
      // Usage events (for metered billing)
      'invoice.created': this.handleInvoiceCreated.bind(this),
      'invoice_item.created': this.handleInvoiceItemCreated.bind(this)
    }
  }

  async processWebhook(event) {
    const handler = this.eventHandlers[event.type]
    
    if (handler) {
      try {
        logger.info(`Processing webhook event: ${event.type}`, {
          eventId: event.id,
          livemode: event.livemode
        })
        
        await handler(event.data.object, event)
        
        logger.info(`Successfully processed webhook: ${event.type}`)
      } catch (error) {
        logger.error(`Error processing webhook ${event.type}:`, error)
        throw error
      }
    } else {
      logger.info(`Unhandled webhook event type: ${event.type}`)
    }
  }

  // Subscription Event Handlers
  async handleSubscriptionCreated(subscription) {
    const user = await this.getUserFromSubscription(subscription)
    if (!user) return

    const planId = subscription.metadata.planId || this.getPlanFromPriceId(subscription.items.data[0].price.id)
    
    user.subscription = {
      ...user.subscription,
      type: planId,
      status: subscription.status,
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: subscription.customer,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    }
    
    // Reset usage for new subscription
    await user.checkAndResetPeriod()
    await user.save()
    
    // Send welcome email for new paid subscription
    if (subscription.status === 'active' && planId !== 'free') {
      await emailService.sendSubscriptionWelcome(user.email, user.name, planId)
    }
    
    logger.info(`Subscription created for user ${user._id}: ${planId}`)
  }

  async handleSubscriptionUpdated(subscription) {
    const user = await this.getUserFromSubscription(subscription)
    if (!user) return

    const oldPlan = user.subscription.type
    const newPlanId = subscription.metadata.planId || this.getPlanFromPriceId(subscription.items.data[0].price.id)
    
    user.subscription = {
      ...user.subscription,
      type: newPlanId,
      status: subscription.status,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    }
    
    // Handle plan changes
    if (oldPlan !== newPlanId) {
      // Reset usage if upgrading
      if (this.isUpgrade(oldPlan, newPlanId)) {
        await user.checkAndResetPeriod()
      }
      
      // Send plan change notification
      await emailService.sendPlanChangeNotification(
        user.email, 
        user.name, 
        oldPlan, 
        newPlanId
      )
    }
    
    await user.save()
    logger.info(`Subscription updated for user ${user._id}: ${oldPlan} -> ${newPlanId}`)
  }

  async handleSubscriptionDeleted(subscription) {
    const user = await this.getUserFromSubscription(subscription)
    if (!user) return

    const oldPlan = user.subscription.type
    
    // Downgrade to free plan
    user.subscription = {
      type: 'free',
      status: 'canceled',
      stripeSubscriptionId: null,
      currentPeriodStart: new Date(),
      currentPeriodEnd: null,
      cancelAtPeriodEnd: false
    }
    
    // Reset usage to free limits
    await user.checkAndResetPeriod()
    await user.save()
    
    // Send cancellation confirmation
    await emailService.sendSubscriptionCanceled(user.email, user.name, oldPlan)
    
    logger.info(`Subscription canceled for user ${user._id}`)
  }

  async handleTrialWillEnd(subscription) {
    const user = await this.getUserFromSubscription(subscription)
    if (!user) return

    // Send trial ending reminder (3 days before)
    await emailService.sendTrialEndingReminder(
      user.email, 
      user.name,
      new Date(subscription.trial_end * 1000)
    )
    
    logger.info(`Trial ending reminder sent to user ${user._id}`)
  }

  // Payment Event Handlers
  async handlePaymentSucceeded(invoice) {
    const user = await this.getUserFromCustomer(invoice.customer)
    if (!user) return

    // Update subscription status if needed
    if (user.subscription.status !== 'active') {
      user.subscription.status = 'active'
      await user.save()
    }
    
    // Reset usage notifications for new billing period
    user.usage.notifications = {
      usage80Sent: false,
      usage95Sent: false,
      overageConsentRequested: false
    }
    await user.save()
    
    // Send payment success email (only for renewals, not first payment)
    if (invoice.billing_reason === 'subscription_cycle') {
      await emailService.sendPaymentSuccess(
        user.email,
        user.name,
        (invoice.amount_paid / 100).toFixed(2),
        user.subscription.type
      )
    }
    
    logger.info(`Payment succeeded for user ${user._id}: $${(invoice.amount_paid / 100).toFixed(2)}`)
  }

  async handlePaymentFailed(invoice) {
    const user = await this.getUserFromCustomer(invoice.customer)
    if (!user) return

    // Update subscription status
    user.subscription.status = 'past_due'
    await user.save()
    
    // Send payment failed email
    await emailService.sendPaymentFailed(
      user.email,
      user.name,
      invoice.attempt_count,
      invoice.next_payment_attempt ? new Date(invoice.next_payment_attempt * 1000) : null
    )
    
    logger.info(`Payment failed for user ${user._id}, attempt ${invoice.attempt_count}`)
  }

  async handleUpcomingInvoice(invoice) {
    const user = await this.getUserFromCustomer(invoice.customer)
    if (!user) return

    // Calculate usage charges if any
    const usageCharges = invoice.lines.data
      .filter(line => line.price?.recurring?.usage_type === 'metered')
      .reduce((sum, line) => sum + line.amount, 0)
    
    // Send upcoming invoice notification (7 days before)
    await emailService.sendUpcomingInvoice(
      user.email,
      user.name,
      (invoice.amount_due / 100).toFixed(2),
      new Date(invoice.period_end * 1000),
      usageCharges > 0 ? (usageCharges / 100).toFixed(2) : null
    )
    
    logger.info(`Upcoming invoice notification sent to user ${user._id}`)
  }

  async handleInvoiceFinalized(invoice) {
    const user = await this.getUserFromCustomer(invoice.customer)
    if (!user) return

    // Log invoice details for accounting
    logger.info(`Invoice finalized for user ${user._id}`, {
      invoiceId: invoice.id,
      amount: invoice.amount_due / 100,
      period: {
        start: new Date(invoice.period_start * 1000),
        end: new Date(invoice.period_end * 1000)
      }
    })
  }

  // Payment Method Event Handlers
  async handlePaymentMethodAttached(paymentMethod) {
    const user = await this.getUserFromCustomer(paymentMethod.customer)
    if (!user) return

    logger.info(`Payment method attached for user ${user._id}: ${paymentMethod.type} ****${paymentMethod.card?.last4}`)
  }

  async handlePaymentMethodDetached(paymentMethod) {
    const user = await this.getUserFromCustomer(paymentMethod.customer)
    if (!user) return

    logger.info(`Payment method detached for user ${user._id}`)
  }

  async handlePaymentMethodUpdated(paymentMethod) {
    const user = await this.getUserFromCustomer(paymentMethod.customer)
    if (!user) return

    logger.info(`Payment method updated for user ${user._id}`)
  }

  // Customer Event Handlers
  async handleCustomerCreated(customer) {
    logger.info(`Stripe customer created: ${customer.id}`)
  }

  async handleCustomerUpdated(customer) {
    const user = await this.getUserFromCustomer(customer.id)
    if (!user) return

    // Sync email if changed
    if (customer.email && customer.email !== user.email) {
      logger.info(`Customer email updated in Stripe for user ${user._id}: ${user.email} -> ${customer.email}`)
    }
  }

  async handleCustomerDeleted(customer) {
    const user = await this.getUserFromCustomer(customer.id)
    if (!user) return

    // Clear Stripe references
    user.subscription.stripeCustomerId = null
    user.subscription.stripeSubscriptionId = null
    await user.save()
    
    logger.info(`Stripe customer deleted for user ${user._id}`)
  }

  // Usage Event Handlers
  async handleInvoiceCreated(invoice) {
    // Log for usage tracking
    logger.info(`Invoice created: ${invoice.id}`, {
      customer: invoice.customer,
      amount: invoice.amount_due / 100
    })
  }

  async handleInvoiceItemCreated(invoiceItem) {
    // Track usage-based charges
    if (invoiceItem.price?.recurring?.usage_type === 'metered') {
      logger.info(`Usage charge created: ${invoiceItem.id}`, {
        customer: invoiceItem.customer,
        amount: invoiceItem.amount / 100,
        quantity: invoiceItem.quantity,
        description: invoiceItem.description
      })
    }
  }

  // Helper Methods
  async getUserFromSubscription(subscription) {
    try {
      return await User.findOne({
        'subscription.stripeSubscriptionId': subscription.id
      })
    } catch (error) {
      logger.error('Error finding user from subscription:', error)
      return null
    }
  }

  async getUserFromCustomer(customerId) {
    try {
      return await User.findOne({
        'subscription.stripeCustomerId': customerId
      })
    } catch (error) {
      logger.error('Error finding user from customer:', error)
      return null
    }
  }

  getPlanFromPriceId(priceId) {
    // Map price IDs to plan names
    const priceMap = {
      [process.env.STRIPE_STARTER_PRICE_ID]: 'starter',
      [process.env.STRIPE_PROFESSIONAL_PRICE_ID]: 'professional',
      [process.env.STRIPE_ENTERPRISE_PRICE_ID]: 'enterprise'
    }
    
    return priceMap[priceId] || 'free'
  }

  isUpgrade(oldPlan, newPlan) {
    const planHierarchy = ['free', 'starter', 'professional', 'enterprise']
    return planHierarchy.indexOf(newPlan) > planHierarchy.indexOf(oldPlan)
  }
}

export default new StripeWebhookService()