import Workflow, { WOR<PERSON><PERSON>OW_STATUS, STEP_TYPES, WOR<PERSON><PERSON><PERSON>_TRIGGERS } from '../models/Workflow.js'
import Agent, { AGENT_SPECIALIZATIONS } from '../models/Agent.js'
import aiService from './aiService.js'
import { logger } from '../utils/logger.js'

/**
 * Natural Language Workflow Generation Service
 * Key differentiator from n8n - users can describe workflows in plain English
 * and the AI will generate the complete workflow with agent coordination
 */
class NaturalLanguageWorkflowService {
  constructor() {
    this.workflowTemplates = new Map()
    this.initializeWorkflowTemplates()
  }

  /**
   * Initialize common workflow templates for faster generation
   */
  initializeWorkflowTemplates() {
    this.workflowTemplates.set('email_campaign', {
      name: 'Email Marketing Campaign',
      description: 'Complete email marketing campaign with segmentation and optimization',
      steps: [
        { type: 'agent', specialization: 'audience_segmentation' },
        { type: 'agent', specialization: 'email_sequence' },
        { type: 'agent', specialization: 'subject_line_testing' },
        { type: 'agent', specialization: 'send_time_optimization' }
      ]
    })

    this.workflowTemplates.set('competitor_analysis', {
      name: 'Competitor Intelligence Workflow',
      description: 'Monitor competitors and generate strategic insights',
      steps: [
        { type: 'agent', specialization: 'competitor_monitoring' },
        { type: 'agent', specialization: 'sentiment_analysis' },
        { type: 'agent', specialization: 'trend_detection' }
      ]
    })

    this.workflowTemplates.set('social_media_automation', {
      name: 'Social Media Automation',
      description: 'Automated social media content creation and scheduling',
      steps: [
        { type: 'agent', specialization: 'social_content' },
        { type: 'agent', specialization: 'hashtag_research' },
        { type: 'agent', specialization: 'social_scheduling' }
      ]
    })
  }

  /**
   * Generate workflow from natural language description
   * This is the main differentiator - users describe what they want in plain English
   */
  async generateWorkflowFromDescription(description, userId, options = {}) {
    try {
      logger.info(`🧠 Generating workflow from description: "${description}"`)

      // Step 1: Analyze the description with AI
      const analysis = await this.analyzeWorkflowDescription(description)
      
      // Step 2: Identify required agents and their sequence
      const agentSequence = await this.identifyRequiredAgents(analysis)
      
      // Step 3: Generate workflow structure
      const workflowStructure = await this.generateWorkflowStructure(analysis, agentSequence)
      
      // Step 4: Create connections and data flow
      const connections = await this.generateConnections(workflowStructure)
      
      // Step 5: Set up triggers and scheduling
      const triggers = await this.generateTriggers(analysis)
      
      // Step 6: Create the workflow document
      const workflow = await this.createWorkflow({
        name: analysis.suggestedName,
        description: analysis.refinedDescription,
        steps: workflowStructure.steps,
        connections,
        triggers,
        userId,
        category: analysis.category,
        ...options
      })

      logger.info(`✅ Generated workflow: ${workflow.name}`)
      
      return {
        workflow,
        analysis,
        recommendations: this.generateRecommendations(analysis, agentSequence)
      }
    } catch (error) {
      logger.error('Failed to generate workflow from description:', error)
      throw error
    }
  }

  /**
   * Analyze natural language description to understand intent
   */
  async analyzeWorkflowDescription(description) {
    const prompt = `
Analyze this workflow description and extract key information:

Description: "${description}"

Please provide a JSON response with:
{
  "intent": "primary goal of the workflow",
  "category": "marketing|sales|support|analytics|content|automation|integration",
  "suggestedName": "concise workflow name",
  "refinedDescription": "improved description with technical details",
  "requiredCapabilities": ["list of required agent capabilities"],
  "triggers": ["manual|schedule|webhook|email|api|event"],
  "dataFlow": "description of how data should flow between steps",
  "complexity": "simple|moderate|complex",
  "estimatedSteps": "number of steps needed",
  "businessValue": "expected business impact"
}

Focus on marketing automation, email campaigns, social media, competitor analysis, and customer engagement workflows.
`

    const response = await aiService.generateContent(prompt)
    
    try {
      // Parse AI response
      const analysis = JSON.parse(response.replace(/```json\n?|\n?```/g, ''))
      
      // Validate and enhance analysis
      return {
        ...analysis,
        originalDescription: description,
        timestamp: new Date(),
        confidence: this.calculateAnalysisConfidence(analysis)
      }
    } catch (parseError) {
      logger.warn('Failed to parse AI analysis, using fallback')
      return this.generateFallbackAnalysis(description)
    }
  }

  /**
   * Identify which agents are needed for the workflow
   */
  async identifyRequiredAgents(analysis) {
    const { requiredCapabilities, category, intent } = analysis
    
    // Get available agents that match the requirements
    const availableAgents = await Agent.find({
      isTemplate: true,
      status: 'active'
    })

    const selectedAgents = []
    
    // Match capabilities to agent specializations
    for (const capability of requiredCapabilities) {
      const matchingAgent = this.findBestAgentForCapability(capability, availableAgents)
      if (matchingAgent && !selectedAgents.find(a => a.specialization === matchingAgent.specialization)) {
        selectedAgents.push(matchingAgent)
      }
    }

    // Add orchestrator (Queen agent) for complex workflows
    if (analysis.complexity === 'complex' || selectedAgents.length > 3) {
      const orchestrator = availableAgents.find(a => 
        a.specialization === AGENT_SPECIALIZATIONS.CAMPAIGN_MANAGER
      )
      if (orchestrator) {
        selectedAgents.unshift(orchestrator) // Add at beginning
      }
    }

    return selectedAgents.map((agent, index) => ({
      agent,
      stepIndex: index,
      role: index === 0 && agent.agentType === 'queen' ? 'orchestrator' : 'executor'
    }))
  }

  /**
   * Generate workflow structure with steps and configuration
   */
  async generateWorkflowStructure(analysis, agentSequence) {
    const steps = []
    let yPosition = 100

    // Create steps for each agent
    agentSequence.forEach((agentInfo, index) => {
      const { agent, role } = agentInfo
      
      const step = {
        id: `step_${index + 1}`,
        name: agent.name,
        type: STEP_TYPES.AGENT,
        position: {
          x: 200 + (index * 300),
          y: yPosition
        },
        config: {
          agentId: agent._id,
          agentConfig: {
            specialization: agent.specialization,
            role: role
          }
        },
        inputs: this.generateStepInputs(agent.specialization, index === 0),
        outputs: this.generateStepOutputs(agent.specialization),
        errorHandling: {
          strategy: 'retry',
          retries: { maxAttempts: 3, backoff: 'exponential' }
        }
      }

      steps.push(step)
    })

    // Add conditional steps if needed
    if (analysis.complexity === 'complex') {
      steps.push(this.generateConditionalStep(steps.length))
    }

    return { steps, layout: 'horizontal' }
  }

  /**
   * Generate connections between workflow steps (ant trails)
   */
  async generateConnections(workflowStructure) {
    const { steps } = workflowStructure
    const connections = []

    // Create sequential connections
    for (let i = 0; i < steps.length - 1; i++) {
      connections.push({
        from: steps[i].id,
        to: steps[i + 1].id,
        label: `Data Flow ${i + 1}`,
        style: {
          color: '#8B5CF6', // Purple ant trail
          thickness: 2,
          pattern: 'solid'
        }
      })
    }

    // Add conditional connections for complex workflows
    const conditionalSteps = steps.filter(s => s.type === STEP_TYPES.CONDITION)
    conditionalSteps.forEach(condStep => {
      const condIndex = steps.findIndex(s => s.id === condStep.id)
      
      // Success path
      if (condIndex < steps.length - 1) {
        connections.push({
          from: condStep.id,
          to: steps[condIndex + 1].id,
          condition: 'success',
          label: 'Success',
          style: {
            color: '#10B981', // Green for success
            thickness: 2,
            pattern: 'solid'
          }
        })
      }
      
      // Failure path (could loop back or go to error handling)
      connections.push({
        from: condStep.id,
        to: steps[0].id, // Loop back to start
        condition: 'failure',
        label: 'Retry',
        style: {
          color: '#F59E0B', // Orange for retry
          thickness: 1,
          pattern: 'dashed'
        }
      })
    })

    return connections
  }

  /**
   * Generate appropriate triggers for the workflow
   */
  async generateTriggers(analysis) {
    const triggers = []

    // Default manual trigger
    triggers.push({
      type: WORKFLOW_TRIGGERS.MANUAL,
      config: {},
      enabled: true
    })

    // Add schedule trigger for recurring workflows
    if (analysis.category === 'marketing' || analysis.intent.includes('monitor')) {
      triggers.push({
        type: WORKFLOW_TRIGGERS.SCHEDULE,
        config: {
          schedule: {
            cron: '0 9 * * 1', // Every Monday at 9 AM
            timezone: 'UTC',
            enabled: false // User can enable later
          }
        },
        enabled: false
      })
    }

    // Add webhook trigger for integration workflows
    if (analysis.category === 'integration' || analysis.intent.includes('api')) {
      triggers.push({
        type: WORKFLOW_TRIGGERS.WEBHOOK,
        config: {
          webhook: {
            method: 'POST',
            secret: this.generateWebhookSecret()
          }
        },
        enabled: false
      })
    }

    return triggers
  }

  /**
   * Create the workflow document
   */
  async createWorkflow(workflowData) {
    const workflow = new Workflow({
      name: workflowData.name,
      description: workflowData.description,
      steps: workflowData.steps,
      connections: workflowData.connections,
      triggers: workflowData.triggers,
      owner: workflowData.userId,
      category: workflowData.category,
      status: WORKFLOW_STATUS.DRAFT,
      settings: {
        timeout: 3600000, // 1 hour
        maxConcurrentExecutions: 5,
        retryPolicy: {
          enabled: true,
          maxRetries: 3,
          backoffStrategy: 'exponential'
        },
        notifications: {
          onSuccess: true,
          onFailure: true,
          channels: ['email']
        }
      },
      inputs: workflowData.inputs || [],
      outputs: workflowData.outputs || []
    })

    await workflow.save()
    return workflow
  }

  /**
   * Generate recommendations for workflow optimization
   */
  generateRecommendations(analysis, agentSequence) {
    const recommendations = []

    // Performance recommendations
    if (agentSequence.length > 5) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Consider Parallel Execution',
        description: 'Some steps could run in parallel to improve performance',
        action: 'Review step dependencies and enable parallel execution where possible'
      })
    }

    // Cost optimization
    if (analysis.complexity === 'complex') {
      recommendations.push({
        type: 'cost',
        priority: 'low',
        title: 'Monitor Compute Usage',
        description: 'Complex workflows may consume more compute resources',
        action: 'Set up usage alerts and consider scheduling during off-peak hours'
      })
    }

    // Feature suggestions
    recommendations.push({
      type: 'feature',
      priority: 'high',
      title: 'Enable Monitoring',
      description: 'Set up monitoring and alerts for this workflow',
      action: 'Configure success/failure notifications and performance tracking'
    })

    return recommendations
  }

  // Helper methods
  findBestAgentForCapability(capability, availableAgents) {
    // Simple matching logic - could be enhanced with ML
    const capabilityMap = {
      'email': AGENT_SPECIALIZATIONS.EMAIL_SEQUENCE,
      'subject': AGENT_SPECIALIZATIONS.SUBJECT_LINE_TESTING,
      'social': AGENT_SPECIALIZATIONS.SOCIAL_CONTENT,
      'competitor': AGENT_SPECIALIZATIONS.COMPETITOR_MONITORING,
      'audience': AGENT_SPECIALIZATIONS.AUDIENCE_SEGMENTATION,
      'content': AGENT_SPECIALIZATIONS.CONTENT_GENERATION,
      'analytics': AGENT_SPECIALIZATIONS.ANALYTICS_REPORTING
    }

    for (const [key, specialization] of Object.entries(capabilityMap)) {
      if (capability.toLowerCase().includes(key)) {
        return availableAgents.find(a => a.specialization === specialization)
      }
    }

    return null
  }

  generateStepInputs(specialization, isFirst) {
    if (isFirst) {
      return [{ name: 'workflow_input', source: 'workflow.input', transformation: null }]
    }

    const inputMap = {
      [AGENT_SPECIALIZATIONS.EMAIL_SEQUENCE]: [
        { name: 'business_info', source: 'workflow.input.business_info' },
        { name: 'audience_segments', source: 'step_1.output.segments' }
      ],
      [AGENT_SPECIALIZATIONS.SUBJECT_LINE_TESTING]: [
        { name: 'email_content', source: 'step_2.output.emails' }
      ]
    }

    return inputMap[specialization] || [{ name: 'input_data', source: 'previous_step.output' }]
  }

  generateStepOutputs(specialization) {
    const outputMap = {
      [AGENT_SPECIALIZATIONS.EMAIL_SEQUENCE]: [
        { name: 'emails', type: 'array', description: 'Generated email sequence' },
        { name: 'performance_predictions', type: 'object', description: 'Predicted performance metrics' }
      ],
      [AGENT_SPECIALIZATIONS.AUDIENCE_SEGMENTATION]: [
        { name: 'segments', type: 'array', description: 'Audience segments' },
        { name: 'targeting_recommendations', type: 'object', description: 'Targeting recommendations' }
      ]
    }

    return outputMap[specialization] || [{ name: 'result', type: 'object', description: 'Agent output' }]
  }

  generateConditionalStep(stepIndex) {
    return {
      id: `condition_${stepIndex}`,
      name: 'Quality Check',
      type: STEP_TYPES.CONDITION,
      position: { x: 200 + (stepIndex * 300), y: 300 },
      config: {
        condition: {
          expression: 'output.quality_score > 0.8',
          operator: 'greater_than',
          value: 0.8
        }
      }
    }
  }

  generateWebhookSecret() {
    return `wh_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`
  }

  calculateAnalysisConfidence(analysis) {
    let confidence = 0.5 // Base confidence
    
    if (analysis.category && analysis.category !== 'unknown') confidence += 0.2
    if (analysis.requiredCapabilities && analysis.requiredCapabilities.length > 0) confidence += 0.2
    if (analysis.suggestedName && analysis.suggestedName.length > 5) confidence += 0.1
    
    return Math.min(1.0, confidence)
  }

  generateFallbackAnalysis(description) {
    return {
      intent: 'Automate business process',
      category: 'automation',
      suggestedName: 'Custom Workflow',
      refinedDescription: description,
      requiredCapabilities: ['automation', 'processing'],
      triggers: ['manual'],
      dataFlow: 'Sequential processing',
      complexity: 'moderate',
      estimatedSteps: 3,
      businessValue: 'Process automation',
      confidence: 0.3
    }
  }
}

// Export singleton instance
const naturalLanguageWorkflowService = new NaturalLanguageWorkflowService()
export default naturalLanguageWorkflowService
