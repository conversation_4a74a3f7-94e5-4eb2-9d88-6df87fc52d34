import Agent from '../models/Agent.js'
import AgentExecution, { EXECUTION_STATUS } from '../models/AgentExecution.js'
import aiService from './aiService.js'
import { logger } from '../utils/logger.js'
import EventEmitter from 'events'
import mongoose from 'mongoose'

/**
 * Agent Execution Service
 * Handles the actual execution of AI agents and manages their lifecycle
 */
class AgentExecutionService extends EventEmitter {
  constructor() {
    super()
    this.executionQueue = []
    this.runningExecutions = new Map()
    this.maxConcurrentExecutions = 10
    this.queueProcessorStarted = false
    
    // Wait for database connection before starting queue processor
    this.initializeWhenReady()
  }

  /**
   * Initialize service when database is ready
   */
  async initializeWhenReady() {
    // Check if already connected
    if (mongoose.connection.readyState === 1) {
      this.startQueueProcessor()
      return
    }

    // Wait for connection
    mongoose.connection.once('connected', () => {
      logger.info('AgentExecutionService: Database connected, starting queue processor')
      this.startQueueProcessor()
    })

    // Handle disconnection
    mongoose.connection.on('disconnected', () => {
      logger.warn('AgentExecutionService: Database disconnected, stopping queue processor')
      this.stopQueueProcessor()
    })
  }

  /**
   * Start the execution queue processor
   */
  startQueueProcessor() {
    if (this.queueProcessorStarted) return
    
    this.queueProcessorStarted = true
    this.queueInterval = setInterval(async () => {
      await this.processExecutionQueue()
    }, 1000) // Process every second
    
    logger.info('AgentExecutionService: Queue processor started')
  }

  /**
   * Stop the execution queue processor
   */
  stopQueueProcessor() {
    if (this.queueInterval) {
      clearInterval(this.queueInterval)
      this.queueInterval = null
      this.queueProcessorStarted = false
      logger.info('AgentExecutionService: Queue processor stopped')
    }
  }

  /**
   * Process pending executions in the queue
   */
  async processExecutionQueue() {
    try {
      // Check database connection
      if (mongoose.connection.readyState !== 1) {
        return // Skip if not connected
      }

      if (this.runningExecutions.size >= this.maxConcurrentExecutions) {
        return // At capacity
      }

      // Get pending executions
      const pendingExecutions = await AgentExecution.find({
        status: 'pending'
      })
      .sort({ 'timing.queuedAt': 1 })
      .limit(this.maxConcurrentExecutions - this.runningExecutions.size)
      .populate('agent')

      for (const execution of pendingExecutions) {
        await this.startExecution(execution)
      }
    } catch (error) {
      logger.error('Queue processing error:', error)
    }
  }

  /**
   * Create and queue a new agent execution
   */
  async createExecution(agentId, userId, inputs = {}, context = {}) {
    try {
      // Validate agent exists
      const agent = await Agent.findById(agentId)
      if (!agent) {
        throw new Error('Agent not found')
      }

      // Create execution record
      const execution = new AgentExecution({
        agent: agentId,
        user: userId,
        inputs,
        context,
        status: 'pending',
        timing: {
          queuedAt: new Date()
        }
      })

      await execution.save()
      
      logger.info(`Agent execution queued: ${execution._id}`)
      this.emit('execution:queued', execution)

      return execution
    } catch (error) {
      logger.error('Failed to create execution:', error)
      throw error
    }
  }

  /**
   * Start executing an agent
   */
  async startExecution(execution) {
    try {
      if (this.runningExecutions.has(execution._id.toString())) {
        return // Already running
      }

      // Update status
      execution.status = 'running'
      execution.timing.startedAt = new Date()
      await execution.save()

      this.runningExecutions.set(execution._id.toString(), execution)
      this.emit('execution:started', execution)

      logger.info(`Agent execution started: ${execution._id}`)

      // Execute agent logic
      await this.executeAgent(execution)
    } catch (error) {
      logger.error('Failed to start execution:', error)
      await this.failExecution(execution, error)
    }
  }

  /**
   * Execute the agent's logic
   */
  async executeAgent(execution) {
    try {
      const agent = execution.agent

      // Prepare prompt with agent configuration
      const systemPrompt = this.buildSystemPrompt(agent)
      const userPrompt = this.buildUserPrompt(agent, execution.inputs)

      // Call AI service
      const result = await aiService.generateResponse(userPrompt, {
        systemPrompt,
        model: agent.model || 'gpt-3.5-turbo',
        temperature: agent.parameters?.temperature || 0.7,
        maxTokens: agent.parameters?.maxTokens || 2000
      })

      // Process and store results
      execution.outputs = {
        response: result.text,
        metadata: result.metadata
      }
      execution.status = 'completed'
      execution.timing.completedAt = new Date()
      execution.metrics = {
        tokensUsed: result.usage?.total_tokens || 0,
        executionTime: Date.now() - execution.timing.startedAt.getTime()
      }

      await execution.save()

      this.runningExecutions.delete(execution._id.toString())
      this.emit('execution:completed', execution)

      logger.info(`Agent execution completed: ${execution._id}`)

      // Trigger any post-execution actions
      await this.handlePostExecution(execution)
    } catch (error) {
      await this.failExecution(execution, error)
    }
  }

  /**
   * Build system prompt from agent configuration
   */
  buildSystemPrompt(agent) {
    let prompt = agent.systemPrompt || 'You are a helpful AI assistant.'
    
    if (agent.role) {
      prompt = `You are ${agent.role}. ${prompt}`
    }

    if (agent.constraints?.length > 0) {
      prompt += '\n\nConstraints:\n' + agent.constraints.map(c => `- ${c}`).join('\n')
    }

    if (agent.capabilities?.length > 0) {
      prompt += '\n\nCapabilities:\n' + agent.capabilities.map(c => `- ${c}`).join('\n')
    }

    return prompt
  }

  /**
   * Build user prompt with inputs
   */
  buildUserPrompt(agent, inputs) {
    if (agent.promptTemplate) {
      return this.interpolateTemplate(agent.promptTemplate, inputs)
    }

    // Default prompt construction
    return Object.entries(inputs)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n')
  }

  /**
   * Interpolate template variables
   */
  interpolateTemplate(template, variables) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match
    })
  }

  /**
   * Handle execution failure
   */
  async failExecution(execution, error) {
    try {
      execution.status = 'failed'
      execution.error = {
        message: error.message,
        stack: error.stack,
        timestamp: new Date()
      }
      execution.timing.completedAt = new Date()
      
      await execution.save()

      this.runningExecutions.delete(execution._id.toString())
      this.emit('execution:failed', execution)

      logger.error(`Agent execution failed: ${execution._id}`, error)
    } catch (saveError) {
      logger.error('Failed to save execution failure:', saveError)
    }
  }

  /**
   * Handle post-execution actions
   */
  async handlePostExecution(execution) {
    try {
      const agent = execution.agent

      // Trigger webhooks
      if (agent.integrations?.webhooks?.onComplete) {
        await this.triggerWebhook(agent.integrations.webhooks.onComplete, execution)
      }

      // Update usage statistics
      await this.updateUsageStats(execution)

      // Check for chained agents
      if (agent.chainedAgents?.length > 0) {
        await this.triggerChainedAgents(agent.chainedAgents, execution)
      }
    } catch (error) {
      logger.error('Post-execution error:', error)
    }
  }

  /**
   * Trigger webhook
   */
  async triggerWebhook(webhookUrl, execution) {
    try {
      await axios.post(webhookUrl, {
        executionId: execution._id,
        status: execution.status,
        outputs: execution.outputs,
        metrics: execution.metrics
      })
    } catch (error) {
      logger.error('Webhook trigger failed:', error)
    }
  }

  /**
   * Update usage statistics
   */
  async updateUsageStats(execution) {
    // Implementation for usage tracking
  }

  /**
   * Trigger chained agents
   */
  async triggerChainedAgents(chainedAgents, previousExecution) {
    for (const chainConfig of chainedAgents) {
      const inputs = {
        ...chainConfig.inputMapping,
        previousOutput: previousExecution.outputs.response
      }

      await this.createExecution(
        chainConfig.agentId,
        previousExecution.user,
        inputs,
        {
          chainedFrom: previousExecution._id,
          ...chainConfig.context
        }
      )
    }
  }

  /**
   * Get execution status
   */
  async getExecutionStatus(executionId) {
    const execution = await AgentExecution.findById(executionId)
      .populate('agent')
      .populate('user', 'name email')

    return execution
  }

  /**
   * Cancel an execution
   */
  async cancelExecution(executionId) {
    const execution = await AgentExecution.findById(executionId)
    
    if (!execution) {
      throw new Error('Execution not found')
    }

    if (execution.status !== 'pending' && execution.status !== 'running') {
      throw new Error('Execution cannot be cancelled')
    }

    execution.status = 'cancelled'
    execution.timing.completedAt = new Date()
    await execution.save()

    this.runningExecutions.delete(executionId)
    this.emit('execution:cancelled', execution)

    return execution
  }

  /**
   * Get execution history for a user
   */
  async getUserExecutions(userId, options = {}) {
    const {
      limit = 50,
      skip = 0,
      status,
      agentId
    } = options

    const query = { user: userId }
    if (status) query.status = status
    if (agentId) query.agent = agentId

    const executions = await AgentExecution.find(query)
      .populate('agent', 'name description')
      .sort({ 'timing.queuedAt': -1 })
      .limit(limit)
      .skip(skip)

    const total = await AgentExecution.countDocuments(query)

    return { executions, total }
  }
}

// Export singleton instance
export default new AgentExecutionService()