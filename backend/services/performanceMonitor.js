/**
 * Comprehensive Performance Monitor - Phase 3 Integration
 * Monitors: Database, Cache, Network, CPU, Memory, System-wide metrics
 * Real-time performance tracking and optimization recommendations
 */

import { EventEmitter } from 'events'
import { performance } from 'perf_hooks'
import v8 from 'v8'
import os from 'os'
import { logger } from '../utils/logger.js'

// Import optimizers
import systemOptimizer from './systemOptimizer.js'
import databaseOptimizer from './databaseOptimizer.js'
import networkOptimizer from './networkOptimizer.js'
import cpuOptimizer from './cpuOptimizer.js'
import hyperOptimizedCacheService from './hyperOptimizedCacheService.js'

class PerformanceMonitor extends EventEmitter {
  constructor() {
    super()
    
    this.metrics = {
      system: {
        uptime: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        loadAverage: [],
        eventLoopLag: 0
      },
      
      database: {
        queryTime: 0,
        connectionPool: 0,
        cacheHitRate: 0,
        indexUtilization: 0
      },
      
      network: {
        requestThroughput: 0,
        responseTime: 0,
        compressionRatio: 0,
        http2Usage: 0
      },
      
      cpu: {
        utilization: 0,
        workerThreads: 0,
        taskQueue: 0,
        jitOptimizations: 0
      },
      
      cache: {
        hitRate: 0,
        memoryUsage: 0,
        tieredPerformance: {}
      }
    }
    
    this.thresholds = {
      criticalCPU: 85,
      criticalMemory: 85,
      criticalEventLoopLag: 100,
      criticalResponseTime: 2000,
      minCacheHitRate: 80,
      minCompressionRatio: 30
    }
    
    this.alerts = []
    this.recommendations = []
    this.performanceHistory = []
    
    this.startMonitoring()
  }

  /**
   * Start comprehensive performance monitoring
   */
  startMonitoring() {
    // Real-time metrics collection every 5 seconds
    setInterval(() => {
      this.collectMetrics()
    }, 5000)
    
    // Performance analysis every 30 seconds
    setInterval(() => {
      this.analyzePerformance()
    }, 30000)
    
    // Generate recommendations every 5 minutes
    setInterval(() => {
      this.generateRecommendations()
    }, 300000)
    
    // Archive metrics every hour
    setInterval(() => {
      this.archiveMetrics()
    }, 3600000)
    
    logger.info('✅ Comprehensive performance monitoring started')
  }

  /**
   * Collect metrics from all system components
   */
  async collectMetrics() {
    const timestamp = Date.now()
    
    try {
      // System metrics
      await this.collectSystemMetrics()
      
      // Database metrics
      await this.collectDatabaseMetrics()
      
      // Network metrics
      await this.collectNetworkMetrics()
      
      // CPU metrics
      await this.collectCPUMetrics()
      
      // Cache metrics
      await this.collectCacheMetrics()
      
      // Add to history
      this.performanceHistory.push({
        timestamp,
        metrics: JSON.parse(JSON.stringify(this.metrics))
      })
      
      // Keep only recent history (last 1000 entries)
      if (this.performanceHistory.length > 1000) {
        this.performanceHistory.shift()
      }
      
      // Emit metrics update
      this.emit('metricsUpdate', this.metrics)
      
    } catch (error) {
      logger.error('Metrics collection error:', error)
    }
  }

  /**
   * Collect system-level metrics
   */
  async collectSystemMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    const heapStats = v8.getHeapStatistics()
    const loadAvg = os.loadavg()
    
    this.metrics.system = {
      uptime: process.uptime(),
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000,
      memoryUsage: {
        rss: memUsage.rss,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        heapUtilization: (memUsage.heapUsed / heapStats.heap_size_limit) * 100
      },
      loadAverage: loadAvg,
      eventLoopLag: await this.measureEventLoopLag(),
      v8: {
        heapSizeLimit: heapStats.heap_size_limit,
        totalHeapSize: heapStats.total_heap_size,
        usedHeapSize: heapStats.used_heap_size,
        totalAvailableSize: heapStats.total_available_size
      }
    }
  }

  /**
   * Measure event loop lag
   */
  measureEventLoopLag() {
    return new Promise((resolve) => {
      const start = process.hrtime.bigint()
      setImmediate(() => {
        const lag = Number(process.hrtime.bigint() - start) / 1e6
        resolve(lag)
      })
    })
  }

  /**
   * Collect database performance metrics
   */
  async collectDatabaseMetrics() {
    try {
      const dbStats = databaseOptimizer.getPerformanceStats()
      
      this.metrics.database = {
        queryTime: dbStats.queries?.avgQueryTime || 0,
        totalQueries: dbStats.queries?.total || 0,
        cacheHitRate: dbStats.cache?.hitRate || 0,
        slowQueries: dbStats.queries?.slowQueries || 0,
        indexHitRate: dbStats.indexes?.indexHitRate || 0,
        connectionPools: dbStats.connections?.pools || 0,
        aggregationPipelines: dbStats.aggregations?.totalPipelines || 0
      }
    } catch (error) {
      logger.error('Database metrics collection error:', error)
    }
  }

  /**
   * Collect network performance metrics
   */
  async collectNetworkMetrics() {
    try {
      const networkStats = networkOptimizer.getPerformanceStats()
      
      this.metrics.network = {
        totalRequests: networkStats.requests?.total || 0,
        compressionRate: networkStats.requests?.compressionRate || 0,
        compressionRatio: networkStats.performance?.compressionRatio || 0,
        avgLatency: networkStats.performance?.avgLatency || 0,
        http2Rate: networkStats.requests?.http2Rate || 0,
        batchedRequests: networkStats.requests?.batched || 0,
        streamedResponses: networkStats.requests?.streamed || 0,
        cacheHits: networkStats.performance?.cacheHits || 0
      }
    } catch (error) {
      logger.error('Network metrics collection error:', error)
    }
  }

  /**
   * Collect CPU performance metrics
   */
  async collectCPUMetrics() {
    try {
      const cpuStats = cpuOptimizer.getPerformanceStats()
      
      this.metrics.cpu = {
        totalTasks: cpuStats.tasks?.total || 0,
        completedTasks: cpuStats.tasks?.completed || 0,
        successRate: cpuStats.tasks?.successRate || 0,
        avgExecutionTime: cpuStats.tasks?.avgExecutionTime || 0,
        workerUtilization: cpuStats.workers?.utilization || 0,
        totalWorkers: cpuStats.workers?.total || 0,
        activeWorkers: cpuStats.workers?.active || 0,
        eventLoopLag: cpuStats.performance?.eventLoopLag || 0,
        jitOptimizations: cpuStats.performance?.jitOptimizations || 0
      }
    } catch (error) {
      logger.error('CPU metrics collection error:', error)
    }
  }

  /**
   * Collect cache performance metrics
   */
  async collectCacheMetrics() {
    try {
      const cacheStats = hyperOptimizedCacheService.getAdvancedStats()
      
      this.metrics.cache = {
        overallHitRate: cacheStats.performance?.overallHitRate || 0,
        avgResponseTime: parseFloat(cacheStats.performance?.avgResponseTime || 0),
        l1HitRate: cacheStats.performance?.l1HitRate || 0,
        l2HitRate: cacheStats.performance?.l2HitRate || 0,
        l3HitRate: cacheStats.performance?.l3HitRate || 0,
        bloomFilterEfficiency: cacheStats.performance?.bloomFilterEfficiency || 0,
        prefetchHitRate: cacheStats.performance?.prefetchHitRate || 0,
        memoryUsage: {
          l1Size: cacheStats.memory?.l1Size || 0,
          l2Size: cacheStats.memory?.l2Size || 0,
          l3Size: cacheStats.memory?.l3Size || 0,
          stringPoolSize: cacheStats.memory?.stringPoolSize || 0,
          estimatedTotal: cacheStats.memory?.estimatedMemoryUsage || '0KB'
        },
        optimization: {
          adaptivePromotions: cacheStats.optimization?.adaptivePromotions || 0,
          memoryOptimizations: cacheStats.optimization?.memoryOptimizations || 0,
          accessPatternsTracked: cacheStats.optimization?.accessPatternsTracked || 0
        }
      }
    } catch (error) {
      logger.error('Cache metrics collection error:', error)
    }
  }

  /**
   * Analyze performance and detect issues
   */
  analyzePerformance() {
    this.alerts = []
    
    // Analyze system performance
    this.analyzeSystemPerformance()
    
    // Analyze database performance
    this.analyzeDatabasePerformance()
    
    // Analyze network performance
    this.analyzeNetworkPerformance()
    
    // Analyze CPU performance
    this.analyzeCPUPerformance()
    
    // Analyze cache performance
    this.analyzeCachePerformance()
    
    // Emit alerts if any critical issues found
    if (this.alerts.length > 0) {
      this.emit('performanceAlerts', this.alerts)
      logger.warn(`⚠️ Performance alerts: ${this.alerts.length} issues detected`)
    }
  }

  /**
   * Analyze system performance
   */
  analyzeSystemPerformance() {
    const { system } = this.metrics
    
    // Memory usage analysis
    if (system.memoryUsage.heapUtilization > this.thresholds.criticalMemory) {
      this.alerts.push({
        level: 'critical',
        type: 'memory',
        message: `High memory usage: ${system.memoryUsage.heapUtilization.toFixed(1)}%`,
        value: system.memoryUsage.heapUtilization,
        threshold: this.thresholds.criticalMemory
      })
    }
    
    // Event loop lag analysis
    if (system.eventLoopLag > this.thresholds.criticalEventLoopLag) {
      this.alerts.push({
        level: 'critical',
        type: 'eventLoop',
        message: `High event loop lag: ${system.eventLoopLag.toFixed(2)}ms`,
        value: system.eventLoopLag,
        threshold: this.thresholds.criticalEventLoopLag
      })
    }
    
    // Load average analysis
    const avgLoad = system.loadAverage[0]
    const cpuCount = os.cpus().length
    if (avgLoad > cpuCount * 0.8) {
      this.alerts.push({
        level: 'warning',
        type: 'load',
        message: `High system load: ${avgLoad.toFixed(2)} (${cpuCount} cores)`,
        value: avgLoad,
        threshold: cpuCount * 0.8
      })
    }
  }

  /**
   * Analyze database performance
   */
  analyzeDatabasePerformance() {
    const { database } = this.metrics
    
    // Query time analysis
    if (database.queryTime > 1000) { // > 1 second
      this.alerts.push({
        level: 'warning',
        type: 'database',
        message: `Slow database queries: ${database.queryTime.toFixed(2)}ms average`,
        value: database.queryTime,
        threshold: 1000
      })
    }
    
    // Cache hit rate analysis
    if (database.cacheHitRate < this.thresholds.minCacheHitRate) {
      this.alerts.push({
        level: 'warning',
        type: 'database',
        message: `Low database cache hit rate: ${database.cacheHitRate.toFixed(1)}%`,
        value: database.cacheHitRate,
        threshold: this.thresholds.minCacheHitRate
      })
    }
    
    // Index utilization analysis
    if (database.indexHitRate < 90) {
      this.alerts.push({
        level: 'info',
        type: 'database',
        message: `Low index hit rate: ${database.indexHitRate.toFixed(1)}%`,
        value: database.indexHitRate,
        threshold: 90
      })
    }
  }

  /**
   * Analyze network performance
   */
  analyzeNetworkPerformance() {
    const { network } = this.metrics
    
    // Response time analysis
    if (network.avgLatency > this.thresholds.criticalResponseTime) {
      this.alerts.push({
        level: 'critical',
        type: 'network',
        message: `High network latency: ${network.avgLatency.toFixed(2)}ms`,
        value: network.avgLatency,
        threshold: this.thresholds.criticalResponseTime
      })
    }
    
    // Compression ratio analysis
    if (network.compressionRatio < this.thresholds.minCompressionRatio) {
      this.alerts.push({
        level: 'info',
        type: 'network',
        message: `Low compression ratio: ${network.compressionRatio.toFixed(1)}%`,
        value: network.compressionRatio,
        threshold: this.thresholds.minCompressionRatio
      })
    }
    
    // HTTP/2 usage analysis
    if (network.http2Rate < 50) {
      this.alerts.push({
        level: 'info',
        type: 'network',
        message: `Low HTTP/2 usage: ${network.http2Rate.toFixed(1)}%`,
        value: network.http2Rate,
        threshold: 50
      })
    }
  }

  /**
   * Analyze CPU performance
   */
  analyzeCPUPerformance() {
    const { cpu } = this.metrics
    
    // Worker utilization analysis
    if (cpu.workerUtilization > 90) {
      this.alerts.push({
        level: 'warning',
        type: 'cpu',
        message: `High worker utilization: ${cpu.workerUtilization.toFixed(1)}%`,
        value: cpu.workerUtilization,
        threshold: 90
      })
    }
    
    // Task success rate analysis
    if (cpu.successRate < 95) {
      this.alerts.push({
        level: 'warning',
        type: 'cpu',
        message: `Low task success rate: ${cpu.successRate.toFixed(1)}%`,
        value: cpu.successRate,
        threshold: 95
      })
    }
    
    // Task execution time analysis
    if (cpu.avgExecutionTime > 5000) { // > 5 seconds
      this.alerts.push({
        level: 'warning',
        type: 'cpu',
        message: `Long task execution time: ${cpu.avgExecutionTime.toFixed(2)}ms`,
        value: cpu.avgExecutionTime,
        threshold: 5000
      })
    }
  }

  /**
   * Analyze cache performance
   */
  analyzeCachePerformance() {
    const { cache } = this.metrics
    
    // Overall hit rate analysis
    if (cache.overallHitRate < this.thresholds.minCacheHitRate) {
      this.alerts.push({
        level: 'warning',
        type: 'cache',
        message: `Low cache hit rate: ${cache.overallHitRate.toFixed(1)}%`,
        value: cache.overallHitRate,
        threshold: this.thresholds.minCacheHitRate
      })
    }
    
    // L1 cache performance
    if (cache.l1HitRate < 70) {
      this.alerts.push({
        level: 'info',
        type: 'cache',
        message: `Low L1 cache hit rate: ${cache.l1HitRate.toFixed(1)}%`,
        value: cache.l1HitRate,
        threshold: 70
      })
    }
    
    // Response time analysis
    if (cache.avgResponseTime > 10) { // > 10ms
      this.alerts.push({
        level: 'info',
        type: 'cache',
        message: `High cache response time: ${cache.avgResponseTime.toFixed(2)}ms`,
        value: cache.avgResponseTime,
        threshold: 10
      })
    }
  }

  /**
   * Generate performance optimization recommendations
   */
  generateRecommendations() {
    this.recommendations = []
    
    const { system, database, network, cpu, cache } = this.metrics
    
    // System recommendations
    if (system.memoryUsage.heapUtilization > 70) {
      this.recommendations.push({
        type: 'memory',
        priority: 'high',
        suggestion: 'Consider increasing heap size or implementing memory optimization',
        impact: 'Prevents memory exhaustion and improves stability'
      })
    }
    
    // Database recommendations
    if (database.queryTime > 100) {
      this.recommendations.push({
        type: 'database',
        priority: 'high',
        suggestion: 'Optimize database queries and add missing indexes',
        impact: 'Reduces response times and improves user experience'
      })
    }
    
    if (database.cacheHitRate < 80) {
      this.recommendations.push({
        type: 'database',
        priority: 'medium',
        suggestion: 'Tune cache configuration and increase cache size',
        impact: 'Reduces database load and improves performance'
      })
    }
    
    // Network recommendations
    if (network.compressionRatio < 40) {
      this.recommendations.push({
        type: 'network',
        priority: 'medium',
        suggestion: 'Enable Brotli compression and optimize compression levels',
        impact: 'Reduces bandwidth usage and improves load times'
      })
    }
    
    if (network.http2Rate < 60) {
      this.recommendations.push({
        type: 'network',
        priority: 'medium',
        suggestion: 'Encourage HTTP/2 adoption and enable server push',
        impact: 'Improves connection efficiency and reduces latency'
      })
    }
    
    // CPU recommendations
    if (cpu.workerUtilization > 80) {
      this.recommendations.push({
        type: 'cpu',
        priority: 'medium',
        suggestion: 'Scale worker threads or optimize task distribution',
        impact: 'Prevents CPU bottlenecks and improves throughput'
      })
    }
    
    // Cache recommendations
    if (cache.overallHitRate < 85) {
      this.recommendations.push({
        type: 'cache',
        priority: 'medium',
        suggestion: 'Optimize cache policies and increase cache capacity',
        impact: 'Improves response times and reduces backend load'
      })
    }
    
    if (cache.l1HitRate < 80) {
      this.recommendations.push({
        type: 'cache',
        priority: 'low',
        suggestion: 'Tune L1 cache size and promotion algorithms',
        impact: 'Optimizes hot data access patterns'
      })
    }
    
    // Log recommendations
    if (this.recommendations.length > 0) {
      logger.info(`💡 Generated ${this.recommendations.length} performance recommendations`)
      this.emit('recommendationsGenerated', this.recommendations)
    }
  }

  /**
   * Archive old metrics to prevent memory leaks
   */
  archiveMetrics() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
    
    this.performanceHistory = this.performanceHistory.filter(
      entry => entry.timestamp > cutoffTime
    )
    
    logger.info(`📦 Archived old metrics, keeping ${this.performanceHistory.length} recent entries`)
  }

  /**
   * Get current performance summary
   */
  getPerformanceSummary() {
    const overallScore = this.calculateOverallScore()
    
    return {
      timestamp: Date.now(),
      overallScore,
      status: this.getPerformanceStatus(overallScore),
      
      summary: {
        system: {
          cpuUsage: this.metrics.system.cpuUsage,
          memoryUtilization: this.metrics.system.memoryUsage.heapUtilization,
          eventLoopLag: this.metrics.system.eventLoopLag,
          uptime: this.metrics.system.uptime
        },
        
        database: {
          avgQueryTime: this.metrics.database.queryTime,
          cacheHitRate: this.metrics.database.cacheHitRate,
          totalQueries: this.metrics.database.totalQueries
        },
        
        network: {
          avgLatency: this.metrics.network.avgLatency,
          compressionRatio: this.metrics.network.compressionRatio,
          totalRequests: this.metrics.network.totalRequests
        },
        
        cpu: {
          workerUtilization: this.metrics.cpu.workerUtilization,
          taskSuccessRate: this.metrics.cpu.successRate,
          avgTaskTime: this.metrics.cpu.avgExecutionTime
        },
        
        cache: {
          hitRate: this.metrics.cache.overallHitRate,
          avgResponseTime: this.metrics.cache.avgResponseTime,
          memoryUsage: this.metrics.cache.memoryUsage.estimatedTotal
        }
      },
      
      alerts: this.alerts,
      recommendations: this.recommendations,
      
      trends: this.calculateTrends()
    }
  }

  /**
   * Calculate overall performance score (0-100)
   */
  calculateOverallScore() {
    const weights = {
      system: 0.2,
      database: 0.25,
      network: 0.2,
      cpu: 0.2,
      cache: 0.15
    }
    
    const scores = {
      system: this.calculateSystemScore(),
      database: this.calculateDatabaseScore(),
      network: this.calculateNetworkScore(),
      cpu: this.calculateCPUScore(),
      cache: this.calculateCacheScore()
    }
    
    const weightedScore = Object.entries(weights).reduce((total, [key, weight]) => {
      return total + (scores[key] * weight)
    }, 0)
    
    return Math.round(weightedScore)
  }

  /**
   * Calculate individual component scores
   */
  calculateSystemScore() {
    const { system } = this.metrics
    let score = 100
    
    // Penalize high memory usage
    if (system.memoryUsage.heapUtilization > 80) score -= 30
    else if (system.memoryUsage.heapUtilization > 60) score -= 15
    
    // Penalize high event loop lag
    if (system.eventLoopLag > 50) score -= 25
    else if (system.eventLoopLag > 20) score -= 10
    
    return Math.max(0, score)
  }

  calculateDatabaseScore() {
    const { database } = this.metrics
    let score = 100
    
    // Penalize slow queries
    if (database.queryTime > 500) score -= 30
    else if (database.queryTime > 100) score -= 15
    
    // Penalize low cache hit rate
    if (database.cacheHitRate < 70) score -= 25
    else if (database.cacheHitRate < 85) score -= 10
    
    return Math.max(0, score)
  }

  calculateNetworkScore() {
    const { network } = this.metrics
    let score = 100
    
    // Penalize high latency
    if (network.avgLatency > 1000) score -= 30
    else if (network.avgLatency > 500) score -= 15
    
    // Reward good compression
    if (network.compressionRatio > 50) score += 10
    else if (network.compressionRatio < 20) score -= 15
    
    return Math.max(0, Math.min(100, score))
  }

  calculateCPUScore() {
    const { cpu } = this.metrics
    let score = 100
    
    // Penalize high worker utilization
    if (cpu.workerUtilization > 90) score -= 25
    else if (cpu.workerUtilization > 75) score -= 10
    
    // Penalize low success rate
    if (cpu.successRate < 90) score -= 30
    else if (cpu.successRate < 95) score -= 15
    
    return Math.max(0, score)
  }

  calculateCacheScore() {
    const { cache } = this.metrics
    let score = 100
    
    // Penalize low hit rate
    if (cache.overallHitRate < 70) score -= 30
    else if (cache.overallHitRate < 85) score -= 15
    
    // Penalize high response time
    if (cache.avgResponseTime > 20) score -= 20
    else if (cache.avgResponseTime > 10) score -= 10
    
    return Math.max(0, score)
  }

  /**
   * Get performance status based on overall score
   */
  getPerformanceStatus(score) {
    if (score >= 90) return 'excellent'
    if (score >= 80) return 'good'
    if (score >= 70) return 'fair'
    if (score >= 60) return 'poor'
    return 'critical'
  }

  /**
   * Calculate performance trends
   */
  calculateTrends() {
    if (this.performanceHistory.length < 10) {
      return { insufficient_data: true }
    }
    
    const recent = this.performanceHistory.slice(-10)
    const older = this.performanceHistory.slice(-20, -10)
    
    const recentAvg = this.averageMetrics(recent)
    const olderAvg = this.averageMetrics(older)
    
    return {
      responseTime: this.calculateTrend(recentAvg.network?.avgLatency, olderAvg.network?.avgLatency),
      memoryUsage: this.calculateTrend(recentAvg.system?.memoryUsage?.heapUtilization, olderAvg.system?.memoryUsage?.heapUtilization),
      cacheHitRate: this.calculateTrend(recentAvg.cache?.overallHitRate, olderAvg.cache?.overallHitRate),
      queryTime: this.calculateTrend(recentAvg.database?.queryTime, olderAvg.database?.queryTime)
    }
  }

  /**
   * Calculate trend direction and magnitude
   */
  calculateTrend(recent, older) {
    if (!recent || !older) return 'stable'
    
    const change = ((recent - older) / older) * 100
    
    if (Math.abs(change) < 5) return 'stable'
    if (change > 0) return change > 20 ? 'increasing_rapidly' : 'increasing'
    return change < -20 ? 'decreasing_rapidly' : 'decreasing'
  }

  /**
   * Average metrics across multiple entries
   */
  averageMetrics(entries) {
    if (entries.length === 0) return {}
    
    const avg = {}
    const sampleMetrics = entries[0].metrics
    
    // Deep merge and average all numeric values
    this.deepAverageObject(avg, entries.map(e => e.metrics))
    
    return avg
  }

  /**
   * Deep average object properties
   */
  deepAverageObject(target, sources) {
    const keys = new Set()
    sources.forEach(source => this.collectKeys(source, keys))
    
    for (const key of keys) {
      const values = sources.map(source => this.getNestedValue(source, key)).filter(v => typeof v === 'number')
      if (values.length > 0) {
        this.setNestedValue(target, key, values.reduce((sum, val) => sum + val, 0) / values.length)
      }
    }
  }

  /**
   * Helper methods for nested object operations
   */
  collectKeys(obj, keys, prefix = '') {
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        this.collectKeys(value, keys, fullKey)
      } else if (typeof value === 'number') {
        keys.add(fullKey)
      }
    }
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  setNestedValue(obj, path, value) {
    const keys = path.split('.')
    const lastKey = keys.pop()
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {}
      return current[key]
    }, obj)
    target[lastKey] = value
  }

  /**
   * Get historical performance data
   */
  getHistoricalData(timeRange = 3600000) { // Default 1 hour
    const cutoffTime = Date.now() - timeRange
    return this.performanceHistory.filter(entry => entry.timestamp > cutoffTime)
  }

  /**
   * Export performance report
   */
  exportPerformanceReport() {
    return {
      generatedAt: new Date().toISOString(),
      summary: this.getPerformanceSummary(),
      historicalData: this.getHistoricalData(),
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCount: os.cpus().length,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem()
      },
      optimizations: {
        database: databaseOptimizer.getPerformanceStats(),
        network: networkOptimizer.getPerformanceStats(),
        cpu: cpuOptimizer.getPerformanceStats(),
        cache: hyperOptimizedCacheService.getAdvancedStats()
      }
    }
  }

  /**
   * Shutdown performance monitor
   */
  async shutdown() {
    logger.info('🔄 Shutting down performance monitor...')
    
    // Final metrics collection
    await this.collectMetrics()
    
    // Generate final report
    const finalReport = this.exportPerformanceReport()
    logger.info('📊 Final performance report generated')
    
    logger.info('✅ Performance monitor shutdown complete')
    return finalReport
  }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitor()

export default performanceMonitor