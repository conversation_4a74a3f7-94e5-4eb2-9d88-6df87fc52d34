/**
 * Unified AI Service
 * Consolidates all AI functionality from multiple service files
 * Supports OpenAI, Claude, and local models with automatic fallback
 */

const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const { logger } = require('../../utils/logger');
const { cache } = require('../../config/redis');
const { featureFlags } = require('../../config/features');

class UnifiedAIService {
  constructor() {
    this.providers = {};
    this.initializeProviders();
    this.defaultProvider = process.env.AI_PROVIDER || 'openai';
  }
  
  initializeProviders() {
    // OpenAI
    if (process.env.OPENAI_API_KEY) {
      this.providers.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    }
    
    // Anthropic Claude
    if (process.env.ANTHROPIC_API_KEY) {
      this.providers.anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY
      });
    }
    
    // Local AI (Ollama or similar)
    if (process.env.LOCAL_AI_ENABLED === 'true') {
      this.providers.local = {
        endpoint: process.env.LOCAL_AI_ENDPOINT || 'http://localhost:11434',
        model: process.env.LOCAL_AI_MODEL || 'llama2'
      };
    }
  }
  
  /**
   * Generate email sequence using AI
   */
  async generateEmailSequence(params) {
    const {
      topic,
      tone = 'professional',
      length = 5,
      targetAudience = 'general business professionals',
      goals = ['engagement', 'conversion'],
      industry = 'general'
    } = params;
    
    // Check cache first
    const cacheKey = `ai:sequence:${JSON.stringify(params)}`;
    if (featureFlags.caching) {
      const cached = await cache.get(cacheKey);
      if (cached) return cached;
    }
    
    const prompt = this.buildSequencePrompt(params);
    
    try {
      let result;
      
      // Try primary provider
      try {
        result = await this.generateWithProvider(this.defaultProvider, prompt);
      } catch (error) {
        logger.warn(`Primary AI provider failed: ${error.message}`);
        // Fallback to other providers
        result = await this.fallbackGeneration(prompt);
      }
      
      const emails = this.parseEmailSequence(result);
      
      const response = {
        emails,
        model: result.model,
        prompt: prompt.substring(0, 200) + '...',
        generatedAt: new Date()
      };
      
      // Cache the result
      if (featureFlags.caching) {
        await cache.set(cacheKey, response, 3600); // 1 hour cache
      }
      
      return response;
      
    } catch (error) {
      logger.error('All AI providers failed:', error);
      throw new Error('Failed to generate email sequence. Please try again later.');
    }
  }
  
  /**
   * Generate with specific provider
   */
  async generateWithProvider(provider, prompt) {
    switch (provider) {
      case 'openai':
        return await this.generateWithOpenAI(prompt);
      case 'anthropic':
        return await this.generateWithAnthropic(prompt);
      case 'local':
        return await this.generateWithLocal(prompt);
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }
  
  /**
   * OpenAI generation
   */
  async generateWithOpenAI(prompt) {
    if (!this.providers.openai) {
      throw new Error('OpenAI not configured');
    }
    
    const completion = await this.providers.openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert email marketing strategist. Generate professional, engaging email sequences.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });
    
    return {
      content: completion.choices[0].message.content,
      model: completion.model,
      provider: 'openai'
    };
  }
  
  /**
   * Anthropic Claude generation
   */
  async generateWithAnthropic(prompt) {
    if (!this.providers.anthropic) {
      throw new Error('Anthropic not configured');
    }
    
    const message = await this.providers.anthropic.messages.create({
      model: process.env.ANTHROPIC_MODEL || 'claude-3-opus-20240229',
      max_tokens: 2000,
      temperature: 0.7,
      system: 'You are an expert email marketing strategist. Generate professional, engaging email sequences.',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    });
    
    return {
      content: message.content[0].text,
      model: message.model,
      provider: 'anthropic'
    };
  }
  
  /**
   * Local AI generation (Ollama)
   */
  async generateWithLocal(prompt) {
    if (!this.providers.local) {
      throw new Error('Local AI not configured');
    }
    
    const response = await fetch(`${this.providers.local.endpoint}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: this.providers.local.model,
        prompt: `You are an expert email marketing strategist. ${prompt}`,
        stream: false
      })
    });
    
    const data = await response.json();
    
    return {
      content: data.response,
      model: this.providers.local.model,
      provider: 'local'
    };
  }
  
  /**
   * Fallback generation - try all available providers
   */
  async fallbackGeneration(prompt) {
    const providers = Object.keys(this.providers).filter(p => p !== this.defaultProvider);
    
    for (const provider of providers) {
      try {
        return await this.generateWithProvider(provider, prompt);
      } catch (error) {
        logger.warn(`Fallback provider ${provider} failed: ${error.message}`);
      }
    }
    
    // If all fail, return a basic template
    return {
      content: this.getBasicTemplate(),
      model: 'template',
      provider: 'fallback'
    };
  }
  
  /**
   * Build prompt for email sequence generation
   */
  buildSequencePrompt(params) {
    const { topic, tone, length, targetAudience, goals, industry } = params;
    
    return `Generate a ${length}-email sequence about "${topic}" for ${targetAudience} in the ${industry} industry.

Requirements:
- Tone: ${tone}
- Goals: ${goals.join(', ')}
- Each email should have a compelling subject line
- Emails should follow a logical progression
- Include clear calls-to-action
- Make content engaging and valuable

Format the response as JSON with this structure:
[
  {
    "subject": "Email subject line",
    "preview": "Email preview text",
    "content": "Full email content with proper formatting",
    "dayDelay": 0,
    "callToAction": "Primary CTA"
  }
]

Generate all ${length} emails following best practices for email marketing.`;
  }
  
  /**
   * Parse AI response into email structure
   */
  parseEmailSequence(result) {
    try {
      // Try to parse as JSON first
      const content = result.content.trim();
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: parse text format
      return this.parseTextFormat(content);
      
    } catch (error) {
      logger.error('Failed to parse AI response:', error);
      // Return a basic structure
      return this.getBasicEmailStructure();
    }
  }
  
  /**
   * Parse text format response
   */
  parseTextFormat(content) {
    const emails = [];
    const emailBlocks = content.split(/Email \d+:|Subject:/i).filter(Boolean);
    
    for (let i = 0; i < emailBlocks.length; i++) {
      const block = emailBlocks[i];
      const subjectMatch = block.match(/Subject[:\s]+(.+)/i);
      const previewMatch = block.match(/Preview[:\s]+(.+)/i);
      
      emails.push({
        subject: subjectMatch ? subjectMatch[1].trim() : `Email ${i + 1}`,
        preview: previewMatch ? previewMatch[1].trim() : '',
        content: block.trim(),
        dayDelay: i * 2,
        callToAction: 'Learn More'
      });
    }
    
    return emails.length > 0 ? emails : this.getBasicEmailStructure();
  }
  
  /**
   * Get basic email structure template
   */
  getBasicEmailStructure() {
    return [
      {
        subject: 'Welcome to Our Journey',
        preview: 'Get started with our exclusive content',
        content: 'Welcome! We\'re excited to have you here...',
        dayDelay: 0,
        callToAction: 'Get Started'
      },
      {
        subject: 'Discover Key Benefits',
        preview: 'Learn how we can help you succeed',
        content: 'In this email, we\'ll explore the key benefits...',
        dayDelay: 2,
        callToAction: 'Learn More'
      },
      {
        subject: 'Success Stories',
        preview: 'See how others achieved their goals',
        content: 'Let me share some inspiring success stories...',
        dayDelay: 4,
        callToAction: 'Read Stories'
      },
      {
        subject: 'Special Offer Inside',
        preview: 'Exclusive opportunity for you',
        content: 'As a valued subscriber, we have a special offer...',
        dayDelay: 6,
        callToAction: 'Claim Offer'
      },
      {
        subject: 'Next Steps',
        preview: 'Your path forward',
        content: 'Ready to take the next step? Here\'s how...',
        dayDelay: 8,
        callToAction: 'Take Action'
      }
    ];
  }
  
  /**
   * Get basic template for fallback
   */
  getBasicTemplate() {
    return JSON.stringify(this.getBasicEmailStructure());
  }
  
  /**
   * Optimize existing sequence
   */
  async optimizeSequence(sequence) {
    const prompt = `Analyze and optimize this email sequence for better engagement and conversion:

Current sequence:
${JSON.stringify(sequence.emails, null, 2)}

Provide:
1. Optimized versions of each email
2. Specific improvements made
3. Suggestions for better performance

Format as JSON with structure:
{
  "emails": [...optimized emails...],
  "improvements": ["improvement 1", "improvement 2", ...],
  "suggestions": ["suggestion 1", "suggestion 2", ...]
}`;
    
    const result = await this.generateWithProvider(this.defaultProvider, prompt);
    
    try {
      return JSON.parse(result.content);
    } catch (error) {
      return {
        emails: sequence.emails,
        improvements: ['Unable to parse optimization results'],
        suggestions: ['Please try again later']
      };
    }
  }
  
  /**
   * Analyze sequence for improvements
   */
  async analyzeSequence(sequence) {
    const prompt = `Analyze this email sequence and provide actionable suggestions:

Sequence: ${JSON.stringify(sequence.emails, null, 2)}

Analyze:
1. Subject line effectiveness
2. Content engagement potential
3. CTA clarity and placement
4. Email timing and frequency
5. Overall flow and coherence

Provide specific, actionable suggestions for improvement.`;
    
    const result = await this.generateWithProvider(this.defaultProvider, prompt);
    
    return {
      analysis: result.content,
      model: result.model,
      analyzedAt: new Date()
    };
  }
  
  /**
   * Generate A/B test variations
   */
  async generateABTestVariants(sequence, count = 2) {
    const variants = [];
    
    for (let i = 0; i < count; i++) {
      const prompt = `Create variation ${i + 1} of this email sequence for A/B testing:

Original: ${JSON.stringify(sequence.emails[0], null, 2)}

Create a different version that:
- Has a different subject line approach
- Uses alternative messaging angle
- Maintains the same goal
- Could potentially perform better

Format as JSON matching the original structure.`;
      
      const result = await this.generateWithProvider(this.defaultProvider, prompt);
      
      try {
        variants.push({
          variant: String.fromCharCode(65 + i), // A, B, C...
          email: JSON.parse(result.content)
        });
      } catch (error) {
        logger.error('Failed to parse variant:', error);
      }
    }
    
    return variants;
  }
}

// Export singleton instance
module.exports = {
  aiService: new UnifiedAIService()
};