import OpenAI from 'openai'
import { logger } from '../utils/logger.js'
import localAiService from './localAiService.js'
import claudeAIService from './claudeAIService.js'
import dotenv from 'dotenv'
// import compromise from 'compromise' // Temporarily disabled for deployment

dotenv.config()

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'sk-dummy-key'
})

class AIService {
  constructor() {
    // Temporarily disabled - will be enabled when compromise is properly imported
    // this.nlp = compromise
    this.initialized = true
  }
  
  // Generate email sequence using Claude 4 > Local AI > OpenAI > Demo
  async generateEmailSequence(businessInfo, settings) {
    try {
      // Try Claude 4 first (best quality, human-like output)
      try {
        logger.info('🧠 Attempting Claude 4 generation...')
        const claudeResult = await claudeAIService.generateEmailSequence(businessInfo, settings)
        logger.info('✅ Claude 4 generation successful!')
        return {
          ...claude<PERSON><PERSON><PERSON>,
          provider: 'claude-4',
          quality: 'premium'
        }
      } catch (claudeError) {
        logger.warn('Claude 4 not available, trying local AI:', claudeError.message)
      }

      // Try local AI second (zero cost, unlimited usage)
      try {
        logger.info('🧠 Attempting local AI generation (Ollama)...')
        const localResult = await localAiService.generateEmailSequence(businessInfo, settings)
        logger.info('✅ Local AI generation successful!')
        return {
          ...localResult,
          provider: 'local-ai',
          quality: 'good'
        }
      } catch (localError) {
        logger.warn('Local AI not available, trying OpenAI:', localError.message)
      }

      // Check if we should use demo mode for OpenAI - IMPROVED DETECTION
      const shouldUseDemoMode = (
        process.env.NODE_ENV === 'development' || 
        !process.env.OPENAI_API_KEY || 
        process.env.OPENAI_API_KEY.includes('dummy') ||
        process.env.OPENAI_API_KEY === 'demo-mode' ||
        process.env.OPENAI_API_KEY.startsWith('sk-dummy') ||
        process.env.OPENAI_API_KEY === 'sk-proj-REPLACE_WITH_YOUR_OPENAI_API_KEY'
      )
      
      if (shouldUseDemoMode) {
        logger.info('Using demo AI sequence generation (OpenAI key not configured)')
        return {
          ...this.generateDemoSequence(businessInfo, settings),
          provider: 'demo',
          quality: 'demo'
        }
      }

      const prompt = this.buildSequencePrompt(businessInfo, settings)
      
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an expert email marketing copywriter and conversion specialist with 15+ years of experience. You understand psychology, persuasion, and what makes people buy. You create email sequences that convert strangers into paying customers using proven frameworks like AIDA, PAS, and StoryBrand.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })

      const generatedContent = response.choices[0].message.content
      return {
        ...this.parseGeneratedSequence(generatedContent, settings),
        provider: 'openai-gpt4',
        quality: 'standard'
      }
      
    } catch (error) {
      logger.error('AI sequence generation error:', error)
      logger.info('Falling back to demo sequence generation')
      return {
        ...this.generateDemoSequence(businessInfo, settings),
        provider: 'demo-fallback',
        quality: 'demo'
      }
    }
  }

  // Build the prompt for sequence generation
  buildSequencePrompt(businessInfo, settings) {
    return `
Create a ${settings.sequenceLength}-email sequence for this business:

BUSINESS DETAILS:
- Industry: ${businessInfo.industry}
- Product/Service: ${businessInfo.productService}
- Target Audience: ${businessInfo.targetAudience}
- Price Point: ${businessInfo.pricePoint}
- Unique Selling Proposition: ${businessInfo.uniqueSellingProposition || 'Not specified'}
- Main Benefit: ${businessInfo.mainBenefit || 'Not specified'}
- Main Pain Point: ${businessInfo.painPoint || 'Not specified'}

REQUIREMENTS:
- Tone: ${settings.tone}
- Primary Goal: ${settings.primaryGoal}
- Include CTAs: ${settings.includeCTA ? 'Yes' : 'No'}
- Personalization: ${settings.includePersonalization ? 'Yes' : 'No'}

PSYCHOLOGY PRINCIPLES TO USE:
- Scarcity and urgency
- Social proof and authority
- Reciprocity and value-first approach
- Pain agitation and solution
- Storytelling and emotional connection
- Clear, compelling calls-to-action

EMAIL SEQUENCE STRUCTURE:
Email 1 (Day 0): Welcome & Value Introduction
Email 2 (Day 1): Problem Agitation & Empathy
Email 3 (Day 2): Solution Introduction & Social Proof
Email 4 (Day 3): Objection Handling & Value Stack
Email 5 (Day 4): Urgency & Scarcity
Email 6 (Day 5): Final Call & Last Chance
Email 7 (Day 7): Break-up & Door Still Open

Please format your response as JSON with this exact structure:
{
  "emails": [
    {
      "dayDelay": 0,
      "subject": "Subject line here",
      "body": "Full email body here with proper formatting",
      "psychologyTriggers": ["scarcity", "social_proof"],
      "conversionScore": 85,
      "subjectLineVariations": ["Alt subject 1", "Alt subject 2", "Alt subject 3"]
    }
  ],
  "aiAnalysis": {
    "overallScore": 92,
    "strengths": ["Strong emotional hooks", "Clear value proposition"],
    "improvements": ["Could add more social proof", "Enhance urgency in email 3"],
    "predictedConversionRate": 4.5
  }
}

Make each email compelling, actionable, and focused on moving the prospect closer to purchase. Use proven copywriting frameworks and psychological triggers. Each email should be 150-400 words with clear CTAs.
`
  }

  // Parse the generated sequence from OpenAI response
  parseGeneratedSequence(content, settings) {
    try {
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      
      // Validate required structure
      if (!parsed.emails || !Array.isArray(parsed.emails)) {
        throw new Error('Invalid email sequence structure')
      }

      // Ensure we have the right number of emails
      if (parsed.emails.length !== settings.sequenceLength) {
        logger.warn(`Expected ${settings.sequenceLength} emails, got ${parsed.emails.length}`)
      }

      return parsed
      
    } catch (error) {
      logger.error('Failed to parse AI response:', error)
      
      // Fallback: return a basic structure
      return this.getFallbackSequence(settings)
    }
  }

  // Generate subject line variations with Claude 4 priority
  async generateSubjectLineVariations(originalSubject, businessInfo) {
    try {
      // Try Claude 4 first for superior subject line creativity
      try {
        const claudeVariations = await claudeAIService.generateSubjectLineVariations(
          originalSubject, 
          businessInfo, 
          'Email marketing sequence'
        )
        logger.info('✅ Claude 4 subject line variations generated')
        return claudeVariations
      } catch (claudeError) {
        logger.warn('Claude 4 subject lines not available, trying OpenAI:', claudeError.message)
      }

      // Check demo mode for OpenAI
      const shouldUseDemoMode = (
        process.env.NODE_ENV === 'development' || 
        !process.env.OPENAI_API_KEY || 
        process.env.OPENAI_API_KEY.includes('dummy') ||
        process.env.OPENAI_API_KEY === 'demo-mode'
      )
      
      if (shouldUseDemoMode) {
        return [
          originalSubject,
          originalSubject.replace(/[!?]/, ''),
          originalSubject + ' (Limited Time)',
          `${businessInfo.industry}: ${originalSubject}`,
          `Quick question about ${originalSubject.toLowerCase()}`
        ]
      }

      const prompt = `
Generate 5 alternative subject lines for this email:
Original: "${originalSubject}"

Business context:
- Industry: ${businessInfo.industry}
- Product: ${businessInfo.productService}
- Target: ${businessInfo.targetAudience}

Requirements:
- Maintain the same core message and urgency level
- Use different psychological triggers (curiosity, urgency, benefit, social proof, personal)
- Keep under 60 characters for mobile optimization
- Avoid spam trigger words

Return only the 5 subject lines, one per line, without numbering or formatting.
`

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.8,
        max_tokens: 200
      })

      return response.choices[0].message.content
        .split('\n')
        .filter(line => line.trim())
        .slice(0, 5)
        
    } catch (error) {
      logger.error('Subject line generation error:', error)
      return [originalSubject] // Return original if generation fails
    }
  }

  // Analyze sequence performance and provide recommendations
  async analyzeSequence(emails, businessInfo, performanceData = null) {
    try {
      // Check demo mode first
      const shouldUseDemoMode = (
        process.env.NODE_ENV === 'development' || 
        !process.env.OPENAI_API_KEY || 
        process.env.OPENAI_API_KEY.includes('dummy') ||
        process.env.OPENAI_API_KEY === 'demo-mode'
      )
      
      if (shouldUseDemoMode) {
        return this.getDemoAnalysis(emails, businessInfo)
      }

      const prompt = `
Analyze this email sequence for conversion potential:

BUSINESS: ${businessInfo.industry} - ${businessInfo.productService}
TARGET: ${businessInfo.targetAudience}
PRICE: ${businessInfo.pricePoint}

EMAIL SEQUENCE:
${emails.map((email, index) => `
Email ${index + 1} (Day ${email.dayDelay}):
Subject: ${email.subject}
Body: ${email.body.substring(0, 200)}...
`).join('\n')}

${performanceData ? `
ACTUAL PERFORMANCE:
- Open Rate: ${performanceData.openRate}%
- Click Rate: ${performanceData.clickRate}%
- Conversion Rate: ${performanceData.conversionRate}%
` : ''}

Provide analysis in this JSON format:
{
  "overallScore": 85,
  "strengths": ["List of 3-5 strengths"],
  "improvements": ["List of 3-5 specific improvements"],
  "predictedConversionRate": 4.2,
  "competitorComparison": "How this compares to industry standards"
}
`

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 1000
      })

      const jsonMatch = response.choices[0].message.content.match(/\{[\s\S]*\}/)
      return jsonMatch ? JSON.parse(jsonMatch[0]) : this.getDefaultAnalysis()
      
    } catch (error) {
      logger.error('Sequence analysis error:', error)
      return this.getDefaultAnalysis()
    }
  }

  // Generate lead magnet ideas
  async generateLeadMagnet(businessInfo) {
    try {
      // Check demo mode first
      const shouldUseDemoMode = (
        process.env.NODE_ENV === 'development' || 
        !process.env.OPENAI_API_KEY || 
        process.env.OPENAI_API_KEY.includes('dummy') ||
        process.env.OPENAI_API_KEY === 'demo-mode'
      )
      
      if (shouldUseDemoMode) {
        return this.getDemoLeadMagnets(businessInfo)
      }

      const prompt = `
Create 3 lead magnet ideas for this business:
- Industry: ${businessInfo.industry}
- Product/Service: ${businessInfo.productService}
- Target Audience: ${businessInfo.targetAudience}
- Main Pain Point: ${businessInfo.painPoint || 'General industry challenges'}

Each lead magnet should:
- Address a specific pain point
- Provide immediate value
- Be easily deliverable (PDF, checklist, template, mini-course)
- Position the business as the expert
- Lead naturally to the paid offering

Format as JSON:
{
  "leadMagnets": [
    {
      "title": "The Ultimate [X] Checklist",
      "description": "Brief description of what it includes",
      "type": "checklist",
      "valueProposition": "What problem it solves"
    }
  ]
}
`

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 800
      })

      const jsonMatch = response.choices[0].message.content.match(/\{[\s\S]*\}/)
      return jsonMatch ? JSON.parse(jsonMatch[0]) : { leadMagnets: [] }
      
    } catch (error) {
      logger.error('Lead magnet generation error:', error)
      return { leadMagnets: [] }
    }
  }

  // Generate demo sequence for testing - ENHANCED VERSION
  generateDemoSequence(businessInfo, settings) {
    const emails = []
    
    for (let i = 0; i < settings.sequenceLength; i++) {
      const dayDelay = i
      let subject, body
      
      switch (i) {
        case 0:
          subject = `Welcome to ${businessInfo.industry}! Here's what happens next...`
          body = `Hi there!

Welcome to our exclusive community! I'm thrilled you've decided to join us on this journey.

You're about to discover how ${businessInfo.productService} can transform your business and help you ${businessInfo.targetAudience.toLowerCase()}.

Over the next few days, I'll share:
• Proven strategies that work in ${businessInfo.industry}
• Real success stories from customers like you
• Exclusive tips you won't find anywhere else

Your first step? Reply and tell me your biggest challenge right now.

Looking forward to hearing from you!

Best regards,
The NeuroColony Team

P.S. Keep an eye on your inbox - tomorrow's email reveals the #1 mistake most businesses make (and how to avoid it).`
          break
          
        case 1:
          subject = "The #1 mistake costing you customers (and how to fix it)"
          body = `Hi again!

Yesterday you joined our community, and I promised to reveal the #1 mistake most businesses make.

Here it is: They focus on features instead of benefits.

Your customers don't care about what your product DOES - they care about what it does FOR THEM.

In ${businessInfo.industry}, this means:
❌ Don't say: "${businessInfo.productService} has advanced features"
✅ Do say: "Get results faster and easier than ever before"

Quick exercise: Look at your current marketing. Are you talking about features or benefits?

Tomorrow, I'll show you exactly how to transform your messaging for maximum impact.

Talk soon,
The NeuroColony Team

P.S. If you're ready to dive deeper, ${businessInfo.pricePoint} is a small investment for the results you'll see.`
          break
          
        case 2:
          subject = "How [Customer Name] increased results by 300%"
          body = `Hi there!

I want to share an incredible success story with you.

Meet Sarah, a business owner just like you in ${businessInfo.industry}.

Three months ago, she was struggling with the same challenges you face. She felt overwhelmed, frustrated, and ready to give up.

Then she discovered ${businessInfo.productService}.

The results? 
• 300% increase in customer engagement
• 50% more sales in just 30 days
• More free time to focus on what matters

"I couldn't believe how simple it was," Sarah told me. "Within days, everything changed."

You can achieve similar results.

The question is: Are you ready to take action?

If yes, ${businessInfo.productService} is waiting for you at just ${businessInfo.pricePoint}.

But here's the thing - spots are limited, and we're almost full for this month.

Don't wait. Your transformation starts today.

Get started now: [CLICK HERE]

Cheering you on,
The NeuroColony Team`
          break
          
        default:
          subject = `Don't miss out: Last chance for ${businessInfo.productService}`
          body = `Hi there,

This is it - your final opportunity.

Over the past few days, I've shared:
✓ The #1 mistake costing you customers
✓ Real success stories from people just like you
✓ Proven strategies that work

You have everything you need to succeed.

The only question remaining is: Will you take action?

${businessInfo.productService} has helped hundreds of businesses in ${businessInfo.industry} achieve incredible results.

At ${businessInfo.pricePoint}, it's the best investment you'll ever make.

But after midnight tonight, this opportunity disappears.

Don't let regret be your companion tomorrow.

Secure your spot now: [CLICK HERE]

To your success,
The NeuroColony Team

P.S. Remember Sarah's 300% increase? It all started with one decision. Make yours today.`
      }
      
      emails.push({
        dayDelay,
        subject,
        body,
        psychologyTriggers: ["urgency", "social_proof", "scarcity", "reciprocity"],
        conversionScore: 75 + Math.floor(Math.random() * 20),
        subjectLineVariations: [
          subject,
          subject.replace(/[!?]/, ''),
          subject + ' (Limited Time)',
        ]
      })
    }
    
    return {
      emails,
      aiAnalysis: {
        overallScore: 88,
        strengths: [
          "Strong emotional hooks in subject lines",
          "Clear value proposition throughout sequence",
          "Effective use of social proof and urgency",
          "Personalized to industry and audience"
        ],
        improvements: [
          "Could add more specific statistics",
          "Consider A/B testing subject line variations",
          "Add more interactive elements"
        ],
        predictedConversionRate: 4.2,
        mode: "demo"
      }
    }
  }

  // Demo analysis for development mode
  getDemoAnalysis(emails, businessInfo) {
    return {
      overallScore: 85,
      strengths: [
        "Well-structured email sequence",
        "Good use of psychological triggers",
        "Clear progression from introduction to close",
        "Appropriate for target audience"
      ],
      improvements: [
        "Add more industry-specific examples",
        "Include additional social proof elements",
        "Consider mobile optimization",
        "Test different timing sequences"
      ],
      predictedConversionRate: 4.5,
      competitorComparison: "Above average for demo mode sequences",
      mode: "demo"
    }
  }

  // Demo lead magnets for development mode
  getDemoLeadMagnets(businessInfo) {
    return {
      leadMagnets: [
        {
          title: `The Ultimate ${businessInfo.industry} Success Checklist`,
          description: "A comprehensive 25-point checklist covering everything from setup to optimization",
          type: "checklist",
          valueProposition: "Avoid the most common mistakes and accelerate your success"
        },
        {
          title: `${businessInfo.targetAudience} Quick Start Guide`,
          description: "Step-by-step guide to get results in your first 30 days",
          type: "guide",
          valueProposition: "Skip the learning curve and get immediate results"
        },
        {
          title: `${businessInfo.industry} ROI Calculator`,
          description: "Interactive spreadsheet to calculate your potential return on investment",
          type: "template",
          valueProposition: "Make data-driven decisions with confidence"
        }
      ]
    }
  }

  // Fallback sequence if AI generation fails
  getFallbackSequence(settings) {
    return {
      emails: [
        {
          dayDelay: 0,
          subject: "Welcome! Here's what happens next...",
          body: "Thank you for your interest! We're excited to help you achieve your goals.",
          psychologyTriggers: ["reciprocity"],
          conversionScore: 70,
          subjectLineVariations: [
            "Welcome aboard!",
            "Thanks for joining us",
            "Your journey starts now"
          ]
        }
      ],
      aiAnalysis: {
        overallScore: 70,
        strengths: ["Professional tone"],
        improvements: ["Add more personalization", "Include stronger CTAs"],
        predictedConversionRate: 2.0,
        mode: "fallback"
      }
    }
  }

  // Default analysis if AI analysis fails
  getDefaultAnalysis() {
    return {
      overallScore: 75,
      strengths: ["Clear structure", "Professional tone"],
      improvements: ["Add more social proof", "Enhance urgency"],
      predictedConversionRate: 3.0,
      competitorComparison: "Analysis temporarily unavailable",
      mode: "default"
    }
  }

  // Calculate conversion score based on content analysis
  calculateConversionScore(emailContent) {
    let score = 50 // Base score
    
    // Check for psychological triggers
    const triggers = {
      urgency: /\b(limited|deadline|expires|hurry|act now|don't wait)\b/i,
      scarcity: /\b(only|last|few left|limited spots|exclusive)\b/i,
      social_proof: /\b(customers|clients|testimonial|review|success)\b/i,
      authority: /\b(expert|proven|research|study|guarantee)\b/i
    }
    
    Object.values(triggers).forEach(regex => {
      if (regex.test(emailContent)) score += 5
    })
    
    // Check for clear CTA
    if (/\b(click|buy|get|start|download|sign up|learn more)\b/i.test(emailContent)) {
      score += 10
    }
    
    // Check length (optimal range)
    const wordCount = emailContent.split(' ').length
    if (wordCount >= 150 && wordCount <= 400) {
      score += 5
    }
    
    return Math.min(score, 100)
  }

  // ADVANCED AI FEATURES - NeuroColony v2.0

  // Multi-language Email Generation
  async generateMultilingualEmail(content, targetLanguages) {
    const results = {}

    for (const lang of targetLanguages) {
      try {
        const translationPrompt = `
          Translate and culturally adapt this email for ${lang} market:
          
          Subject: ${content.subject}
          Body: ${content.body}
          
          Consider:
          1. Cultural norms and business etiquette
          2. Appropriate greetings and closings
          3. Local time formats and references
          4. Cultural communication style
          
          Return in JSON format:
          {
            "subject": "translated subject",
            "body": "culturally adapted body",
            "culturalNotes": ["note1", "note2"],
            "bestSendTime": "10:00 AM local time"
          }
        `

        const response = await openai.chat.completions.create({
          model: 'gpt-4',
          messages: [
            { 
              role: 'system', 
              content: `You are a cultural communication expert and native ${lang} speaker.` 
            },
            { role: 'user', content: translationPrompt }
          ],
          temperature: 0.7
        })

        const parsed = JSON.parse(response.choices[0].message.content)
        results[lang] = parsed
      } catch (error) {
        logger.error(`Translation error for ${lang}:`, error)
        results[lang] = { error: error.message }
      }
    }

    return results
  }

  // Email Performance Prediction
  async predictEmailPerformance(emailContent) {
    const features = this.extractEmailFeatures(emailContent)
    const prediction = this.performancePredictor.run(features)

    const insights = []
    
    // Generate insights based on prediction
    if (prediction[0] < 0.2) insights.push("Subject line needs more emotional appeal")
    if (prediction[1] < 0.05) insights.push("Add stronger call-to-action buttons")
    if (prediction[2] < 0.02) insights.push("Include social proof or testimonials")
    if (prediction[3] > 0.02) insights.push("Content may be too aggressive, soften approach")

    return {
      predictedMetrics: {
        openRate: (prediction[0] * 100).toFixed(1) + '%',
        clickRate: (prediction[1] * 100).toFixed(1) + '%',
        conversionRate: (prediction[2] * 100).toFixed(1) + '%',
        unsubscribeRate: (prediction[3] * 100).toFixed(1) + '%',
        overallScore: Math.round(prediction[4] * 100)
      },
      insights,
      recommendations: this.getOptimizationRecommendations(prediction)
    }
  }

  // Subject Line Optimizer with Psychology
  async optimizeSubjectLine(originalSubject, context) {
    const psychologicalTriggers = [
      { trigger: 'urgency', words: ['limited', 'expires', 'deadline', 'now'] },
      { trigger: 'curiosity', words: ['secret', 'discover', 'revealed', 'truth'] },
      { trigger: 'benefit', words: ['free', 'save', 'gain', 'improve'] },
      { trigger: 'fear', words: ['mistake', 'avoid', 'missing', 'losing'] },
      { trigger: 'social', words: ['everyone', 'join', 'trending', 'popular'] }
    ]

    const variations = []

    for (const { trigger, words } of psychologicalTriggers) {
      const prompt = `
        Rewrite this subject line using ${trigger} psychology:
        Original: "${originalSubject}"
        
        Use words like: ${words.join(', ')}
        Keep under 50 characters.
        Make it compelling but authentic.
        
        Return only the new subject line.
      `

      try {
        const response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.9,
          max_tokens: 50
        })

        variations.push({
          text: response.choices[0].message.content.trim(),
          trigger,
          predictedOpenRate: this.predictSubjectLinePerformance(response.choices[0].message.content)
        })
      } catch (error) {
        logger.error(`Subject line optimization error for ${trigger}:`, error)
      }
    }

    return {
      original: originalSubject,
      variations: variations.sort((a, b) => b.predictedOpenRate - a.predictedOpenRate),
      winner: variations[0] || { text: originalSubject, trigger: 'original' }
    }
  }

  // Competitor Analysis
  async analyzeCompetitorEmail(competitorEmail, yourBrand) {
    const analysis = {
      strengths: [],
      weaknesses: [],
      opportunities: []
    }

    // Analyze subject line
    const subjectScore = this.analyzeSubjectLine(competitorEmail.subject)
    if (subjectScore > 0.7) {
      analysis.strengths.push(`Strong subject line with ${(subjectScore * 100).toFixed(0)}% effectiveness`)
    } else {
      analysis.weaknesses.push("Subject line could be more compelling")
      analysis.opportunities.push("Create stronger emotional hooks in your subject lines")
    }

    // Analyze body content
    const sentiment = this.analyzeSentiment(competitorEmail.body)
    const readability = this.calculateReadabilityScore(competitorEmail.body)
    
    if (sentiment > 0.5) {
      analysis.strengths.push("Positive, uplifting tone")
    } else if (sentiment < -0.5) {
      analysis.weaknesses.push("Overly negative tone may discourage readers")
    }

    if (readability > 0.7) {
      analysis.strengths.push("Easy to read and understand")
    } else {
      analysis.weaknesses.push("Complex language may lose readers")
      analysis.opportunities.push("Use simpler, more direct language")
    }

    // Generate strategic recommendations
    const recommendations = []
    if (analysis.weaknesses.length > analysis.strengths.length) {
      recommendations.push("This competitor has clear vulnerabilities you can exploit")
      recommendations.push("Focus on their weak areas while maintaining your strengths")
    } else {
      recommendations.push("Study their successful tactics and adapt for your brand")
      recommendations.push("Find unique angles they haven't covered")
    }

    return {
      ...analysis,
      recommendations,
      competitiveAdvantage: this.calculateCompetitiveScore(analysis)
    }
  }

  // Helper Methods for Advanced Features
  trainPerformanceModel() {
    // Train with sample data - in production, use real historical data
    const trainingData = [
      { input: [0.8, 0.7, 0.6, 0.9, 0.5], output: [0.25, 0.05, 0.02, 0.01, 0.75] },
      { input: [0.6, 0.5, 0.7, 0.8, 0.4], output: [0.20, 0.04, 0.015, 0.02, 0.65] },
      { input: [0.9, 0.8, 0.9, 0.9, 0.7], output: [0.35, 0.08, 0.04, 0.005, 0.85] },
      { input: [0.4, 0.3, 0.5, 0.6, 0.3], output: [0.15, 0.02, 0.01, 0.03, 0.45] }
    ]

    this.performancePredictor.train(trainingData, {
      iterations: 20000,
      errorThresh: 0.005
    })
  }

  extractEmailFeatures(emailContent) {
    const features = [
      this.calculateReadabilityScore(emailContent.body || ''),
      this.analyzeSubjectLine(emailContent.subject || ''),
      this.calculatePersonalizationScore(emailContent.body || ''),
      this.calculateCTAStrength(emailContent.body || ''),
      this.calculateLengthScore(emailContent.body || '')
    ]
    
    return features
  }

  calculateReadabilityScore(text) {
    // Simple readability based on sentence and word length
    const sentences = text.split(/[.!?]+/).filter(s => s.trim())
    const words = text.split(/\s+/).filter(w => w.trim())
    
    if (sentences.length === 0 || words.length === 0) return 0
    
    const avgWordsPerSentence = words.length / sentences.length
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length
    
    // Lower scores = better readability
    const readabilityIndex = (avgWordsPerSentence * 0.5) + (avgWordLength * 0.5)
    
    // Convert to 0-1 scale (inverted)
    return Math.max(0, Math.min(1, 1 - (readabilityIndex / 20)))
  }

  analyzeSubjectLine(subject) {
    if (!subject) return 0
    
    let score = 0.5 // Base score
    
    // Length optimization (30-50 chars is ideal)
    const length = subject.length
    if (length >= 30 && length <= 50) score += 0.1
    else if (length < 20 || length > 70) score -= 0.2
    
    // Emotional words
    if (/\b(amazing|incredible|exclusive|free|new|important)\b/i.test(subject)) score += 0.1
    
    // Questions increase open rates
    if (subject.includes('?')) score += 0.1
    
    // Numbers/lists perform well
    if (/\d+/.test(subject)) score += 0.1
    
    // Personalization
    if (/\b(you|your)\b/i.test(subject)) score += 0.1
    
    return Math.max(0, Math.min(1, score))
  }

  calculatePersonalizationScore(text) {
    if (!text) return 0
    
    let score = 0
    const personalWords = ['you', 'your', "you're", "you'll", "you've"]
    const wordCount = text.split(/\s+/).length
    
    personalWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi')
      const matches = text.match(regex)
      if (matches) {
        score += matches.length / wordCount
      }
    })
    
    return Math.min(1, score * 10) // Scale up and cap at 1
  }

  calculateCTAStrength(text) {
    if (!text) return 0
    
    const ctaPatterns = [
      /\b(click|tap|press)\s+(here|now|today)\b/i,
      /\b(get|grab|claim|secure)\s+(your|the|this)\b/i,
      /\b(start|begin|join|sign up)\s+(now|today|free)\b/i,
      /\b(don't|do not)\s+(miss|wait|delay)\b/i,
      /\b(limited|exclusive|special)\s+(time|offer|deal)\b/i
    ]
    
    let score = 0
    ctaPatterns.forEach(pattern => {
      if (pattern.test(text)) score += 0.2
    })
    
    return Math.min(1, score)
  }

  calculateLengthScore(text) {
    if (!text) return 0
    
    const wordCount = text.split(/\s+/).filter(w => w.trim()).length
    
    // Optimal email length is 150-200 words
    if (wordCount >= 150 && wordCount <= 200) return 1
    if (wordCount >= 100 && wordCount <= 300) return 0.8
    if (wordCount >= 50 && wordCount <= 400) return 0.6
    
    return 0.4 // Too short or too long
  }

  analyzeSentiment(text) {
    if (!text) return 0
    
    // Simple sentiment analysis - natural library not imported
    const positiveWords = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 
                          'perfect', 'happy', 'excited', 'delighted', 'success', 'achieve']
    const negativeWords = ['problem', 'issue', 'struggle', 'difficult', 'hard', 'frustrat', 
                          'disappoint', 'fail', 'worry', 'concern', 'unfortunate']
    
    const words = text.toLowerCase().split(/\s+/)
    let sentiment = 0
    
    words.forEach(word => {
      if (positiveWords.some(pos => word.includes(pos))) sentiment += 1
      if (negativeWords.some(neg => word.includes(neg))) sentiment -= 1
    })
    
    return sentiment / words.length // Normalize by word count
  }

  predictSubjectLinePerformance(subject) {
    const score = this.analyzeSubjectLine(subject)
    // Convert to predicted open rate (industry average ~20%)
    return 0.15 + (score * 0.25) // 15-40% range
  }

  getOptimizationRecommendations(prediction) {
    const recommendations = []
    
    if (prediction[0] < 0.2) {
      recommendations.push("Improve subject line with emotional triggers or questions")
    }
    if (prediction[1] < 0.05) {
      recommendations.push("Add multiple clear CTAs throughout the email")
    }
    if (prediction[2] < 0.02) {
      recommendations.push("Include testimonials or case studies for social proof")
    }
    if (prediction[3] > 0.02) {
      recommendations.push("Reduce promotional language to decrease unsubscribes")
    }
    
    return recommendations
  }

  calculateCompetitiveScore(analysis) {
    const score = (
      analysis.opportunities.length * 10 +
      analysis.weaknesses.length * 5 -
      analysis.strengths.length * 3
    )
    
    return Math.max(0, Math.min(100, score))
  }
}

export default new AIService()