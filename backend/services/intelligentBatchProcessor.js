/**
 * Intelligent Batch Processor - Phase 2 Algorithmic Enhancement  
 * Features: Dynamic queue sizing, Priority scheduling, Load balancing, Smart batching
 * Performance Target: 50ms→25ms batch processing, 90%+ throughput efficiency, adaptive scaling
 */

import { logger } from '../utils/logger.js'
import { EventEmitter } from 'events'

class IntelligentBatchProcessor extends EventEmitter {
  constructor() {
    super()
    
    // Multi-tier priority queues
    this.criticalQueue = new PriorityQueue('critical')
    this.highQueue = new PriorityQueue('high') 
    this.normalQueue = new PriorityQueue('normal')
    this.lowQueue = new PriorityQueue('low')
    
    // Advanced batch management
    this.activeBatches = new Map()
    this.batchHistory = new Map()
    this.queueMonitor = new QueueMonitor()
    
    // Dynamic configuration
    this.config = {
      baseBatchSize: 3,
      maxBatchSize: 15,
      minBatchSize: 1,
      baseTimeout: 50,
      maxTimeout: 500,
      minTimeout: 10,
      adaptiveScaling: true,
      loadBalancing: true,
      priorityWeights: {
        critical: 1.0,
        high: 0.8,
        normal: 0.6,
        low: 0.4
      }
    }
    
    // Performance tracking
    this.metrics = {
      totalRequests: 0,
      totalBatches: 0,
      avgBatchSize: 0,
      avgProcessingTime: 0,
      throughputPerSecond: 0,
      adaptiveAdjustments: 0,
      loadBalanceActions: 0,
      priorityPromotions: 0,
      queueStarvation: 0
    }
    
    // Load monitoring
    this.loadMonitor = {
      cpuUsage: 0,
      memoryUsage: 0,
      queueDepth: 0,
      processingRate: 0,
      lastMeasurement: Date.now()
    }
    
    // Processing state
    this.isProcessing = false
    this.processingTimer = null
    this.adaptiveTimer = null
    
    // Initialize adaptive systems
    this.startAdaptiveOptimization()
    this.startLoadMonitoring()
  }

  /**
   * Intelligent request queuing with dynamic prioritization
   */
  async queueRequest(request, priority = 'normal', options = {}) {
    const startTime = performance.now()
    this.metrics.totalRequests++
    
    // Create enhanced request object
    const enhancedRequest = this.enhanceRequest(request, priority, options)
    
    // Dynamic priority adjustment based on system load
    const adjustedPriority = this.adjustPriorityDynamically(enhancedRequest, priority)
    
    // Route to appropriate queue
    const targetQueue = this.selectOptimalQueue(adjustedPriority)
    
    // Add to queue with intelligent positioning
    const queuePosition = targetQueue.enqueue(enhancedRequest)
    
    // Update queue monitoring
    this.queueMonitor.recordEnqueue(adjustedPriority, queuePosition)
    
    // Trigger adaptive batch processing
    this.triggerIntelligentProcessing()
    
    // Return promise for request completion
    return new Promise((resolve, reject) => {
      enhancedRequest.resolve = resolve
      enhancedRequest.reject = reject
      enhancedRequest.queueTime = performance.now() - startTime
    })
  }

  /**
   * Enhanced request object creation
   */
  enhanceRequest(request, priority, options) {
    return {
      id: this.generateRequestId(),
      originalRequest: request,
      priority,
      timestamp: Date.now(),
      timeoutMs: options.timeout || this.calculateDynamicTimeout(priority),
      retryCount: 0,
      maxRetries: options.maxRetries || 2,
      processingHints: this.extractProcessingHints(request),
      batchCompatible: this.isBatchCompatible(request),
      estimatedComplexity: this.estimateComplexity(request),
      ...options
    }
  }

  /**
   * Dynamic priority adjustment based on system state
   */
  adjustPriorityDynamically(request, originalPriority) {
    let adjustedPriority = originalPriority
    
    // Age-based priority promotion
    const age = Date.now() - request.timestamp
    if (age > 30000 && originalPriority !== 'critical') { // 30 seconds
      adjustedPriority = this.promotePriority(originalPriority)
      this.metrics.priorityPromotions++
    }
    
    // System load-based adjustments
    if (this.loadMonitor.queueDepth > 100) {
      // Under heavy load, be more selective
      if (originalPriority === 'low') {
        request.timeoutMs = Math.max(request.timeoutMs * 1.5, this.config.maxTimeout)
      }
    }
    
    // Request type optimization
    if (request.processingHints.type === 'ai_generation') {
      // AI requests get priority during low load
      if (this.loadMonitor.processingRate < 0.7) {
        adjustedPriority = this.promotePriority(originalPriority)
      }
    }
    
    return adjustedPriority
  }

  /**
   * Select optimal queue based on current system state
   */
  selectOptimalQueue(priority) {
    const queues = {
      critical: this.criticalQueue,
      high: this.highQueue,
      normal: this.normalQueue,
      low: this.lowQueue
    }
    
    const targetQueue = queues[priority] || this.normalQueue
    
    // Load balancing: redirect to less busy queues if needed
    if (this.config.loadBalancing && targetQueue.size() > 50) {
      const alternativeQueue = this.findLeastBusyQueue(priority)
      if (alternativeQueue && alternativeQueue.size() < targetQueue.size() * 0.7) {
        this.metrics.loadBalanceActions++
        return alternativeQueue
      }
    }
    
    return targetQueue
  }

  /**
   * Intelligent batch processing trigger
   */
  triggerIntelligentProcessing() {
    if (this.isProcessing) return
    
    // Clear existing timer
    if (this.processingTimer) {
      clearTimeout(this.processingTimer)
    }
    
    // Calculate optimal processing delay
    const delay = this.calculateOptimalDelay()
    
    this.processingTimer = setTimeout(() => {
      this.processQueues()
    }, delay)
  }

  /**
   * Calculate optimal processing delay based on system state
   */
  calculateOptimalDelay() {
    const totalQueueSize = this.getTotalQueueSize()
    const currentLoad = this.getCurrentSystemLoad()
    
    let delay = this.config.baseTimeout
    
    // Reduce delay for critical requests
    if (this.criticalQueue.size() > 0) {
      delay = Math.min(delay, 10)
    }
    
    // Increase delay if system is under heavy load
    if (currentLoad > 0.8) {
      delay = Math.min(delay * 1.5, this.config.maxTimeout)
    }
    
    // Reduce delay if queues are backing up
    if (totalQueueSize > 20) {
      delay = Math.max(delay * 0.7, this.config.minTimeout)
    }
    
    return Math.round(delay)
  }

  /**
   * Advanced queue processing with intelligent batching
   */
  async processQueues() {
    if (this.isProcessing) return
    
    this.isProcessing = true
    const startTime = performance.now()
    
    try {
      // Process queues in priority order
      const processingTasks = []
      
      // Critical queue - process immediately, small batches
      if (this.criticalQueue.size() > 0) {
        processingTasks.push(this.processPriorityQueue('critical', 1, 5))
      }
      
      // High priority queue - larger batches
      if (this.highQueue.size() > 0) {
        processingTasks.push(this.processPriorityQueue('high', 2, 8))
      }
      
      // Normal queue - standard batching
      if (this.normalQueue.size() > 0) {
        processingTasks.push(this.processPriorityQueue('normal', 3, 12))
      }
      
      // Low priority queue - large batches when system is idle
      if (this.lowQueue.size() > 0 && this.getCurrentSystemLoad() < 0.6) {
        processingTasks.push(this.processPriorityQueue('low', 5, 15))
      }
      
      // Execute all batches concurrently
      await Promise.all(processingTasks)
      
      // Check for queue starvation
      this.checkQueueStarvation()
      
    } catch (error) {
      logger.error('Batch processing error:', error)
    } finally {
      this.isProcessing = false
      const processingTime = performance.now() - startTime
      this.updatePerformanceMetrics(processingTime)
      
      // Schedule next processing cycle if needed
      if (this.getTotalQueueSize() > 0) {
        this.triggerIntelligentProcessing()
      }
    }
  }

  /**
   * Process specific priority queue with adaptive batching
   */
  async processPriorityQueue(priority, minBatch, maxBatch) {
    const queue = this.getQueueByPriority(priority)
    if (queue.size() === 0) return
    
    // Calculate optimal batch size
    const optimalBatchSize = this.calculateOptimalBatchSize(queue, minBatch, maxBatch)
    
    // Extract batch from queue
    const batch = this.extractOptimalBatch(queue, optimalBatchSize)
    if (batch.length === 0) return
    
    this.metrics.totalBatches++
    
    // Group by processing type for efficiency
    const groupedBatch = this.groupByProcessingType(batch)
    
    // Process each group
    const processingPromises = Object.entries(groupedBatch).map(([type, requests]) => 
      this.processRequestGroup(type, requests, priority)
    )
    
    await Promise.all(processingPromises)
  }

  /**
   * Calculate optimal batch size using machine learning-like adaptation
   */
  calculateOptimalBatchSize(queue, minBatch, maxBatch) {
    const queueSize = queue.size()
    const currentLoad = this.getCurrentSystemLoad()
    const historicalPerformance = this.getHistoricalPerformance()
    
    let optimalSize = this.config.baseBatchSize
    
    // Adjust based on queue pressure
    if (queueSize > 20) {
      optimalSize = Math.min(optimalSize * 1.5, maxBatch)
    } else if (queueSize < 5) {
      optimalSize = Math.max(optimalSize * 0.7, minBatch)
    }
    
    // Adjust based on system load
    if (currentLoad > 0.8) {
      optimalSize = Math.max(optimalSize * 0.6, minBatch)
    } else if (currentLoad < 0.4) {
      optimalSize = Math.min(optimalSize * 1.3, maxBatch)
    }
    
    // Historical performance optimization
    if (historicalPerformance.avgBatchEfficiency < 0.7) {
      optimalSize = Math.max(optimalSize * 0.8, minBatch)
    }
    
    return Math.round(Math.max(minBatch, Math.min(maxBatch, optimalSize)))
  }

  /**
   * Extract optimal batch considering request compatibility
   */
  extractOptimalBatch(queue, targetSize) {
    const batch = []
    const processed = new Set()
    
    // First pass: get compatible requests
    while (batch.length < targetSize && queue.size() > 0) {
      const request = queue.peek()
      if (!request || processed.has(request.id)) break
      
      if (this.isCompatibleWithBatch(request, batch)) {
        batch.push(queue.dequeue())
        processed.add(request.id)
      } else {
        // Skip incompatible request for now
        const skipped = queue.dequeue()
        queue.enqueue(skipped) // Re-queue at end
        if (processed.has(skipped.id)) break // Prevent infinite loop
        processed.add(skipped.id)
      }
    }
    
    return batch
  }

  /**
   * Group requests by processing type for efficient batching
   */
  groupByProcessingType(batch) {
    const groups = {}
    
    batch.forEach(request => {
      const type = request.processingHints.type || 'default'
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(request)
    })
    
    return groups
  }

  /**
   * Process a group of similar requests
   */
  async processRequestGroup(type, requests, priority) {
    const startTime = performance.now()
    
    try {
      let results
      
      switch (type) {
        case 'ai_generation':
          results = await this.processAIGenerationBatch(requests)
          break
        case 'database_query':
          results = await this.processDatabaseBatch(requests)
          break
        case 'email_validation':
          results = await this.processEmailValidationBatch(requests)
          break
        default:
          results = await this.processGenericBatch(requests)
      }
      
      // Resolve all requests in batch
      requests.forEach((request, index) => {
        if (request.resolve) {
          request.resolve(results[index])
        }
      })
      
    } catch (error) {
      // Handle batch failure
      logger.error(`Batch processing failed for type ${type}:`, error)
      
      // Try individual processing for failed batch
      await this.fallbackToIndividualProcessing(requests, error)
      
    } finally {
      const processingTime = performance.now() - startTime
      this.recordBatchPerformance(type, requests.length, processingTime, priority)
    }
  }

  /**
   * Process AI generation requests in batch
   */
  async processAIGenerationBatch(requests) {
    // Group similar AI requests for efficiency
    const grouped = this.groupAIRequestsBySimilarity(requests)
    const results = []
    
    for (const group of grouped) {
      if (group.length === 1) {
        // Single request
        const result = await this.processSingleAIRequest(group[0])
        results.push(result)
      } else {
        // Batch AI requests
        const batchResults = await this.processBatchedAIRequests(group)
        results.push(...batchResults)
      }
    }
    
    return results
  }

  /**
   * Fallback to individual processing when batch fails
   */
  async fallbackToIndividualProcessing(requests, originalError) {
    for (const request of requests) {
      try {
        const result = await this.processSingleRequest(request)
        if (request.resolve) {
          request.resolve(result)
        }
      } catch (error) {
        if (request.reject) {
          request.reject(error)
        }
      }
    }
  }

  /**
   * Start adaptive optimization system
   */
  startAdaptiveOptimization() {
    this.adaptiveTimer = setInterval(() => {
      this.performAdaptiveOptimization()
    }, 30000) // Every 30 seconds
  }

  /**
   * Perform adaptive optimization based on performance metrics
   */
  performAdaptiveOptimization() {
    if (!this.config.adaptiveScaling) return
    
    const metrics = this.getRecentPerformanceMetrics()
    
    // Adjust batch sizes based on performance
    if (metrics.avgProcessingTime > 100) {
      // Processing is slow, reduce batch sizes
      this.config.baseBatchSize = Math.max(1, this.config.baseBatchSize - 1)
      this.metrics.adaptiveAdjustments++
    } else if (metrics.avgProcessingTime < 25 && metrics.throughputPerSecond < 10) {
      // Processing is fast but low throughput, increase batch sizes
      this.config.baseBatchSize = Math.min(this.config.maxBatchSize, this.config.baseBatchSize + 1)
      this.metrics.adaptiveAdjustments++
    }
    
    // Adjust timeouts based on queue pressure
    if (this.getTotalQueueSize() > 50) {
      this.config.baseTimeout = Math.max(this.config.minTimeout, this.config.baseTimeout - 5)
    } else if (this.getTotalQueueSize() < 5) {
      this.config.baseTimeout = Math.min(this.config.maxTimeout, this.config.baseTimeout + 5)
    }
  }

  /**
   * Start load monitoring system
   */
  startLoadMonitoring() {
    setInterval(() => {
      this.updateLoadMetrics()
    }, 5000) // Every 5 seconds
  }

  /**
   * Update system load metrics
   */
  updateLoadMetrics() {
    const memUsage = process.memoryUsage()
    
    this.loadMonitor = {
      cpuUsage: this.estimateCPUUsage(),
      memoryUsage: memUsage.heapUsed / memUsage.heapTotal,
      queueDepth: this.getTotalQueueSize(),
      processingRate: this.calculateProcessingRate(),
      lastMeasurement: Date.now()
    }
  }

  /**
   * Get comprehensive performance statistics
   */
  getPerformanceStats() {
    return {
      processing: {
        totalRequests: this.metrics.totalRequests,
        totalBatches: this.metrics.totalBatches,
        avgBatchSize: this.metrics.avgBatchSize.toFixed(2),
        avgProcessingTime: this.metrics.avgProcessingTime.toFixed(2) + 'ms',
        throughputPerSecond: this.metrics.throughputPerSecond.toFixed(2),
        batchEfficiency: this.calculateBatchEfficiency().toFixed(2) + '%'
      },
      optimization: {
        adaptiveAdjustments: this.metrics.adaptiveAdjustments,
        loadBalanceActions: this.metrics.loadBalanceActions,
        priorityPromotions: this.metrics.priorityPromotions,
        queueStarvationEvents: this.metrics.queueStarvation
      },
      queues: {
        critical: { size: this.criticalQueue.size(), avgWaitTime: this.criticalQueue.getAvgWaitTime() },
        high: { size: this.highQueue.size(), avgWaitTime: this.highQueue.getAvgWaitTime() },
        normal: { size: this.normalQueue.size(), avgWaitTime: this.normalQueue.getAvgWaitTime() },
        low: { size: this.lowQueue.size(), avgWaitTime: this.lowQueue.getAvgWaitTime() }
      },
      system: {
        currentLoad: this.getCurrentSystemLoad().toFixed(2),
        memoryUsage: (this.loadMonitor.memoryUsage * 100).toFixed(1) + '%',
        processingRate: this.loadMonitor.processingRate.toFixed(2),
        queueDepth: this.loadMonitor.queueDepth
      },
      configuration: {
        baseBatchSize: this.config.baseBatchSize,
        baseTimeout: this.config.baseTimeout,
        adaptiveScaling: this.config.adaptiveScaling,
        loadBalancing: this.config.loadBalancing
      }
    }
  }

  // Utility methods
  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  extractProcessingHints(request) {
    // Analyze request to determine processing hints
    if (request.businessInfo && request.generationSettings) {
      return { type: 'ai_generation', complexity: 'medium' }
    }
    if (request.query || request.filter) {
      return { type: 'database_query', complexity: 'low' }
    }
    return { type: 'default', complexity: 'low' }
  }

  estimateComplexity(request) {
    const hints = this.extractProcessingHints(request)
    const complexityMap = { low: 1, medium: 3, high: 5 }
    return complexityMap[hints.complexity] || 1
  }

  isBatchCompatible(request) {
    const hints = this.extractProcessingHints(request)
    return hints.type !== 'realtime' && hints.type !== 'interactive'
  }

  isCompatibleWithBatch(request, batch) {
    if (batch.length === 0) return true
    
    const requestType = request.processingHints.type
    const batchType = batch[0].processingHints.type
    
    return requestType === batchType
  }

  calculateDynamicTimeout(priority) {
    const baseTimeouts = {
      critical: 10,
      high: 25,
      normal: 50,
      low: 100
    }
    
    return baseTimeouts[priority] || 50
  }

  promotePriority(priority) {
    const promotionMap = {
      low: 'normal',
      normal: 'high',
      high: 'critical',
      critical: 'critical'
    }
    
    return promotionMap[priority] || priority
  }

  getQueueByPriority(priority) {
    const queues = {
      critical: this.criticalQueue,
      high: this.highQueue,
      normal: this.normalQueue,
      low: this.lowQueue
    }
    
    return queues[priority] || this.normalQueue
  }

  getTotalQueueSize() {
    return this.criticalQueue.size() + this.highQueue.size() + 
           this.normalQueue.size() + this.lowQueue.size()
  }

  getCurrentSystemLoad() {
    return Math.max(this.loadMonitor.cpuUsage, this.loadMonitor.memoryUsage)
  }

  findLeastBusyQueue(excludePriority) {
    const queues = [
      { queue: this.criticalQueue, priority: 'critical' },
      { queue: this.highQueue, priority: 'high' },
      { queue: this.normalQueue, priority: 'normal' },
      { queue: this.lowQueue, priority: 'low' }
    ].filter(q => q.priority !== excludePriority)
    
    return queues.reduce((least, current) => 
      (least.queue.size() < current.queue.size()) ? least : current
    ).queue
  }

  // Placeholder methods for complex implementations
  checkQueueStarvation() { /* Implementation */ }
  updatePerformanceMetrics(processingTime) { /* Implementation */ }
  getHistoricalPerformance() { return { avgBatchEfficiency: 0.8 } }
  getRecentPerformanceMetrics() { return { avgProcessingTime: 50, throughputPerSecond: 5 } }
  estimateCPUUsage() { return 0.5 }
  calculateProcessingRate() { return 0.8 }
  calculateBatchEfficiency() { return 85 }
  recordBatchPerformance(type, size, time, priority) { /* Implementation */ }
  groupAIRequestsBySimilarity(requests) { return [requests] }
  processSingleAIRequest(request) { return Promise.resolve({}) }
  processBatchedAIRequests(requests) { return Promise.resolve(requests.map(() => ({}))) }
  processDatabaseBatch(requests) { return Promise.resolve(requests.map(() => ({}))) }
  processEmailValidationBatch(requests) { return Promise.resolve(requests.map(() => ({}))) }
  processGenericBatch(requests) { return Promise.resolve(requests.map(() => ({}))) }
  processSingleRequest(request) { return Promise.resolve({}) }
}

/**
 * Priority Queue with enhanced features
 */
class PriorityQueue {
  constructor(priorityLevel) {
    this.heap = []
    this.priorityLevel = priorityLevel
    this.enqueueCount = 0
    this.dequeueCount = 0
    this.totalWaitTime = 0
  }

  enqueue(item) {
    item.enqueueTime = Date.now()
    this.enqueueCount++
    
    this.heap.push(item)
    this.heapifyUp(this.heap.length - 1)
    
    return this.heap.length
  }

  dequeue() {
    if (this.heap.length === 0) return null
    
    const result = this.heap[0]
    const waitTime = Date.now() - result.enqueueTime
    this.totalWaitTime += waitTime
    this.dequeueCount++
    
    const end = this.heap.pop()
    
    if (this.heap.length > 0) {
      this.heap[0] = end
      this.heapifyDown(0)
    }
    
    return result
  }

  peek() {
    return this.heap.length > 0 ? this.heap[0] : null
  }

  size() {
    return this.heap.length
  }

  getAvgWaitTime() {
    return this.dequeueCount > 0 ? (this.totalWaitTime / this.dequeueCount) : 0
  }

  heapifyUp(index) {
    while (index > 0) {
      const parentIndex = Math.floor((index - 1) / 2)
      
      if (this.compare(this.heap[parentIndex], this.heap[index]) <= 0) break
      
      [this.heap[parentIndex], this.heap[index]] = [this.heap[index], this.heap[parentIndex]]
      index = parentIndex
    }
  }

  heapifyDown(index) {
    while (true) {
      const leftChild = 2 * index + 1
      const rightChild = 2 * index + 2
      let smallest = index
      
      if (leftChild < this.heap.length && this.compare(this.heap[leftChild], this.heap[smallest]) < 0) {
        smallest = leftChild
      }
      
      if (rightChild < this.heap.length && this.compare(this.heap[rightChild], this.heap[smallest]) < 0) {
        smallest = rightChild
      }
      
      if (smallest === index) break
      
      [this.heap[index], this.heap[smallest]] = [this.heap[smallest], this.heap[index]]
      index = smallest
    }
  }

  compare(a, b) {
    // Higher priority first (lower timestamp = higher priority)
    const priorityDiff = a.estimatedComplexity - b.estimatedComplexity
    if (priorityDiff !== 0) return priorityDiff
    
    return a.timestamp - b.timestamp
  }
}

/**
 * Queue Monitor for performance tracking
 */
class QueueMonitor {
  constructor() {
    this.queueStats = new Map()
    this.performanceHistory = []
  }

  recordEnqueue(priority, position) {
    if (!this.queueStats.has(priority)) {
      this.queueStats.set(priority, { enqueues: 0, totalPosition: 0 })
    }
    
    const stats = this.queueStats.get(priority)
    stats.enqueues++
    stats.totalPosition += position
  }

  getAveragePosition(priority) {
    const stats = this.queueStats.get(priority)
    return stats ? (stats.totalPosition / stats.enqueues) : 0
  }
}

export default new IntelligentBatchProcessor()