import { EventEmitter } from 'events'
import { logger } from '../utils/logger.js'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'

/**
 * NeuroColony Enterprise SSO Service
 * Supports SAML 2.0, OIDC, and Active Directory integration
 */

class SSOService extends EventEmitter {
  constructor() {
    super()
    this.providers = new Map()
    this.configurations = new Map()
    this.activeTokens = new Map()
    this.samlRequests = new Map()
    
    this.initializeDefaultProviders()
    logger.info('🔐 SSO Service initialized')
  }

  initializeDefaultProviders() {
    // Default SSO provider configurations
    const defaultProviders = {
      'saml': {
        id: 'saml',
        name: 'SAML 2.0',
        type: 'saml',
        description: 'Enterprise SAML 2.0 authentication',
        endpoints: {
          sso: '/auth/saml/sso',
          acs: '/auth/saml/acs',
          sls: '/auth/saml/sls',
          metadata: '/auth/saml/metadata'
        },
        supports: ['SP-initiated', 'IdP-initiated', 'SLO']
      },
      'oidc': {
        id: 'oidc',
        name: 'OpenID Connect',
        type: 'oidc',
        description: 'OpenID Connect authentication',
        endpoints: {
          auth: '/auth/oidc/auth',
          callback: '/auth/oidc/callback',
          logout: '/auth/oidc/logout'
        },
        supports: ['authorization_code', 'implicit', 'hybrid']
      },
      'azure-ad': {
        id: 'azure-ad',
        name: 'Azure Active Directory',
        type: 'azure-ad',
        description: 'Microsoft Azure AD authentication',
        endpoints: {
          auth: '/auth/azure/auth',
          callback: '/auth/azure/callback'
        },
        supports: ['v1.0', 'v2.0', 'B2C']
      },
      'google-workspace': {
        id: 'google-workspace',
        name: 'Google Workspace',
        type: 'google-workspace',
        description: 'Google Workspace (G Suite) authentication',
        endpoints: {
          auth: '/auth/google/auth',
          callback: '/auth/google/callback'
        },
        supports: ['OAuth 2.0', 'OpenID Connect']
      },
      'okta': {
        id: 'okta',
        name: 'Okta',
        type: 'okta',
        description: 'Okta identity provider',
        endpoints: {
          auth: '/auth/okta/auth',
          callback: '/auth/okta/callback'
        },
        supports: ['SAML 2.0', 'OpenID Connect']
      }
    }

    for (const [id, provider] of Object.entries(defaultProviders)) {
      this.providers.set(id, provider)
    }
  }

  /**
   * Configure SSO for organization
   */
  async configureSSOProvider(orgId, adminUserId, providerConfig) {
    try {
      const configId = `sso_${orgId}_${Date.now()}`
      
      const configuration = {
        id: configId,
        orgId,
        adminUserId,
        providerId: providerConfig.providerId,
        name: providerConfig.name || this.providers.get(providerConfig.providerId)?.name,
        type: providerConfig.type || this.providers.get(providerConfig.providerId)?.type,
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
        
        // SAML Configuration
        saml: providerConfig.saml ? {
          entryPoint: providerConfig.saml.entryPoint,
          issuer: providerConfig.saml.issuer || `https://sequenceai.com/saml/${orgId}`,
          cert: providerConfig.saml.cert,
          privateCert: providerConfig.saml.privateCert,
          signatureAlgorithm: providerConfig.saml.signatureAlgorithm || 'sha256',
          digestAlgorithm: providerConfig.saml.digestAlgorithm || 'sha256',
          nameIDFormat: providerConfig.saml.nameIDFormat || 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
          wantAssertionsSigned: providerConfig.saml.wantAssertionsSigned !== false,
          wantAuthnResponseSigned: providerConfig.saml.wantAuthnResponseSigned !== false,
          attributeMapping: {
            email: providerConfig.saml.emailAttribute || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
            firstName: providerConfig.saml.firstNameAttribute || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
            lastName: providerConfig.saml.lastNameAttribute || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
            displayName: providerConfig.saml.displayNameAttribute || 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name',
            groups: providerConfig.saml.groupsAttribute || 'http://schemas.microsoft.com/ws/2008/06/identity/claims/groups'
          }
        } : null,

        // OIDC Configuration
        oidc: providerConfig.oidc ? {
          issuer: providerConfig.oidc.issuer,
          clientId: providerConfig.oidc.clientId,
          clientSecret: providerConfig.oidc.clientSecret,
          redirectUri: providerConfig.oidc.redirectUri || `https://sequenceai.com/auth/oidc/callback/${orgId}`,
          scope: providerConfig.oidc.scope || 'openid profile email',
          responseType: providerConfig.oidc.responseType || 'code',
          userinfoEndpoint: providerConfig.oidc.userinfoEndpoint,
          jwksUri: providerConfig.oidc.jwksUri,
          tokenEndpoint: providerConfig.oidc.tokenEndpoint,
          authorizationEndpoint: providerConfig.oidc.authorizationEndpoint
        } : null,

        // Azure AD Configuration
        azureAd: providerConfig.azureAd ? {
          tenantId: providerConfig.azureAd.tenantId,
          clientId: providerConfig.azureAd.clientId,
          clientSecret: providerConfig.azureAd.clientSecret,
          version: providerConfig.azureAd.version || 'v2.0',
          redirectUri: providerConfig.azureAd.redirectUri || `https://sequenceai.com/auth/azure/callback/${orgId}`,
          scope: providerConfig.azureAd.scope || 'openid profile email User.Read'
        } : null,

        // Role and Attribute Mapping
        roleMapping: {
          defaultRole: providerConfig.defaultRole || 'viewer',
          adminGroups: providerConfig.adminGroups || [],
          editorGroups: providerConfig.editorGroups || [],
          viewerGroups: providerConfig.viewerGroups || [],
          attributeBasedRoles: providerConfig.attributeBasedRoles || false
        },

        // Security Settings
        security: {
          enforceSSO: providerConfig.enforceSSO || false,
          allowLocalLogin: providerConfig.allowLocalLogin !== false,
          sessionTimeout: providerConfig.sessionTimeout || 8, // hours
          requireMFA: providerConfig.requireMFA || false,
          allowedDomains: providerConfig.allowedDomains || [],
          autoProvisioning: providerConfig.autoProvisioning !== false,
          autoProvisioningRole: providerConfig.autoProvisioningRole || 'viewer'
        },

        // Testing Configuration
        testing: {
          testMode: providerConfig.testMode || false,
          testUsers: providerConfig.testUsers || [],
          bypassAuthentication: false
        }
      }

      this.configurations.set(configId, configuration)
      this.emit('ssoConfigured', { configId, configuration, orgId, adminUserId })
      
      logger.info(`SSO configuration created: ${configuration.name} for org ${orgId}`)
      
      return {
        success: true,
        configId,
        configuration: this.sanitizeConfiguration(configuration)
      }
    } catch (error) {
      logger.error('Configure SSO provider error:', error)
      throw error
    }
  }

  /**
   * Initiate SAML authentication
   */
  async initiateSAMLAuth(orgId, relayState = null) {
    try {
      const config = this.getActiveConfigurationByOrg(orgId, 'saml')
      if (!config) {
        throw new Error('SAML configuration not found')
      }

      const requestId = crypto.randomUUID()
      const timestamp = new Date().toISOString()
      
      // Generate SAML AuthnRequest
      const authnRequest = this.generateSAMLAuthnRequest(config, requestId, timestamp)
      
      // Store request for validation
      this.samlRequests.set(requestId, {
        orgId,
        configId: config.id,
        timestamp,
        relayState,
        status: 'pending'
      })

      // Clean up expired requests
      this.cleanupExpiredSAMLRequests()

      const redirectUrl = `${config.saml.entryPoint}?SAMLRequest=${encodeURIComponent(authnRequest)}&RelayState=${encodeURIComponent(relayState || '')}`
      
      logger.info(`SAML authentication initiated for org ${orgId}`)
      
      return {
        success: true,
        redirectUrl,
        requestId
      }
    } catch (error) {
      logger.error('Initiate SAML auth error:', error)
      throw error
    }
  }

  /**
   * Process SAML assertion
   */
  async processSAMLAssertion(samlResponse, relayState) {
    try {
      // Parse and validate SAML response
      const assertion = this.parseSAMLResponse(samlResponse)
      const config = this.configurations.get(assertion.configId)
      
      if (!config) {
        throw new Error('Invalid SAML configuration')
      }

      // Validate assertion
      await this.validateSAMLAssertion(assertion, config)
      
      // Extract user attributes
      const userAttributes = this.extractSAMLAttributes(assertion, config)
      
      // Create or update user
      const user = await this.provisionSSOUser(config.orgId, userAttributes, config)
      
      // Generate JWT token
      const token = this.generateSSOToken(user, config)
      
      // Store active session
      this.activeTokens.set(token, {
        userId: user.id,
        orgId: config.orgId,
        configId: config.id,
        loginTime: new Date(),
        expiresAt: new Date(Date.now() + (config.security.sessionTimeout * 60 * 60 * 1000))
      })

      this.emit('ssoLoginSuccess', { user, config, method: 'saml' })
      
      logger.info(`SAML authentication successful for user ${user.email}`)
      
      return {
        success: true,
        token,
        user: this.sanitizeUser(user),
        redirectUrl: relayState || '/dashboard'
      }
    } catch (error) {
      logger.error('Process SAML assertion error:', error)
      throw error
    }
  }

  /**
   * Initiate OIDC authentication
   */
  async initiateOIDCAuth(orgId, state = null) {
    try {
      const config = this.getActiveConfigurationByOrg(orgId, 'oidc')
      if (!config) {
        throw new Error('OIDC configuration not found')
      }

      const nonce = crypto.randomBytes(16).toString('hex')
      const stateParam = state || crypto.randomBytes(16).toString('hex')
      
      const authUrl = new URL(config.oidc.authorizationEndpoint)
      authUrl.searchParams.set('client_id', config.oidc.clientId)
      authUrl.searchParams.set('response_type', config.oidc.responseType)
      authUrl.searchParams.set('scope', config.oidc.scope)
      authUrl.searchParams.set('redirect_uri', config.oidc.redirectUri)
      authUrl.searchParams.set('state', stateParam)
      authUrl.searchParams.set('nonce', nonce)

      // Store state for validation
      this.activeTokens.set(stateParam, {
        orgId,
        configId: config.id,
        nonce,
        timestamp: new Date(),
        status: 'pending'
      })

      logger.info(`OIDC authentication initiated for org ${orgId}`)
      
      return {
        success: true,
        redirectUrl: authUrl.toString(),
        state: stateParam
      }
    } catch (error) {
      logger.error('Initiate OIDC auth error:', error)
      throw error
    }
  }

  /**
   * Get SSO configuration for organization
   */
  getOrganizationSSOConfig(orgId) {
    const configs = []
    
    for (const [id, config] of this.configurations.entries()) {
      if (config.orgId === orgId && config.status === 'active') {
        configs.push(this.sanitizeConfiguration(config))
      }
    }
    
    return configs
  }

  /**
   * Test SSO configuration
   */
  async testSSOConfiguration(configId, adminUserId) {
    try {
      const config = this.configurations.get(configId)
      if (!config) {
        throw new Error('Configuration not found')
      }

      if (config.adminUserId !== adminUserId) {
        throw new Error('Insufficient permissions')
      }

      const testResults = {
        configId,
        timestamp: new Date(),
        tests: []
      }

      // Test configuration validity
      testResults.tests.push({
        name: 'Configuration Validation',
        status: this.validateConfiguration(config) ? 'passed' : 'failed',
        details: 'Basic configuration structure and required fields'
      })

      // Test network connectivity
      if (config.saml) {
        testResults.tests.push({
          name: 'SAML Endpoint Connectivity',
          status: await this.testSAMLEndpoint(config.saml.entryPoint) ? 'passed' : 'failed',
          details: 'SAML Identity Provider endpoint accessibility'
        })
      }

      if (config.oidc) {
        testResults.tests.push({
          name: 'OIDC Discovery',
          status: await this.testOIDCDiscovery(config.oidc.issuer) ? 'passed' : 'failed',
          details: 'OIDC provider discovery document'
        })
      }

      // Test certificate validation (for SAML)
      if (config.saml?.cert) {
        testResults.tests.push({
          name: 'Certificate Validation',
          status: this.validateCertificate(config.saml.cert) ? 'passed' : 'failed',
          details: 'X.509 certificate format and validity'
        })
      }

      const overallStatus = testResults.tests.every(test => test.status === 'passed') ? 'passed' : 'failed'
      
      logger.info(`SSO configuration test completed: ${configId} - ${overallStatus}`)
      
      return {
        success: true,
        status: overallStatus,
        results: testResults
      }
    } catch (error) {
      logger.error('Test SSO configuration error:', error)
      throw error
    }
  }

  /**
   * Generate SSO analytics
   */
  getSSOAnalytics(orgId, timeRange = '30d') {
    const analytics = {
      overview: {
        totalConfigurations: 0,
        activeConfigurations: 0,
        totalLogins: 0,
        successfulLogins: 0,
        failedLogins: 0
      },
      providerUsage: {},
      loginTrends: this.generateMockLoginTrends(timeRange),
      topUsers: this.generateMockTopUsers(),
      securityEvents: this.generateMockSecurityEvents()
    }

    // Calculate real stats from configurations
    for (const [id, config] of this.configurations.entries()) {
      if (config.orgId === orgId) {
        analytics.overview.totalConfigurations++
        if (config.status === 'active') {
          analytics.overview.activeConfigurations++
        }
      }
    }

    return analytics
  }

  // Helper methods
  getActiveConfigurationByOrg(orgId, type = null) {
    for (const [id, config] of this.configurations.entries()) {
      if (config.orgId === orgId && config.status === 'active') {
        if (!type || config.type === type) {
          return config
        }
      }
    }
    return null
  }

  generateSAMLAuthnRequest(config, requestId, timestamp) {
    // Simplified SAML AuthnRequest generation
    // In production, use a proper SAML library
    const authnRequest = `
      <samlp:AuthnRequest 
        xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
        xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
        ID="${requestId}"
        Version="2.0"
        IssueInstant="${timestamp}"
        Destination="${config.saml.entryPoint}"
        AssertionConsumerServiceURL="${config.saml.callbackUrl}">
        <saml:Issuer>${config.saml.issuer}</saml:Issuer>
        <samlp:NameIDPolicy Format="${config.saml.nameIDFormat}" AllowCreate="true"/>
      </samlp:AuthnRequest>
    `.trim()

    return Buffer.from(authnRequest).toString('base64')
  }

  parseSAMLResponse(samlResponse) {
    // Mock SAML response parsing
    // In production, use a proper SAML library like node-saml
    return {
      configId: 'mock-config-id',
      nameId: '<EMAIL>',
      attributes: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        groups: ['Users', 'Managers']
      }
    }
  }

  validateSAMLAssertion(assertion, config) {
    // Mock validation - in production, validate signatures, timestamps, etc.
    return true
  }

  extractSAMLAttributes(assertion, config) {
    const mapping = config.saml.attributeMapping
    return {
      email: assertion.attributes[mapping.email] || assertion.nameId,
      firstName: assertion.attributes[mapping.firstName] || '',
      lastName: assertion.attributes[mapping.lastName] || '',
      displayName: assertion.attributes[mapping.displayName] || '',
      groups: assertion.attributes[mapping.groups] || []
    }
  }

  async provisionSSOUser(orgId, attributes, config) {
    // Mock user provisioning
    // In production, this would create/update user in database
    const user = {
      id: crypto.randomUUID(),
      email: attributes.email,
      firstName: attributes.firstName,
      lastName: attributes.lastName,
      orgId,
      role: this.determineUserRole(attributes.groups, config.roleMapping),
      ssoProvider: config.type,
      lastLogin: new Date(),
      createdAt: new Date()
    }

    logger.info(`SSO user provisioned: ${user.email}`)
    return user
  }

  determineUserRole(userGroups, roleMapping) {
    if (roleMapping.adminGroups.some(group => userGroups.includes(group))) {
      return 'admin'
    }
    if (roleMapping.editorGroups.some(group => userGroups.includes(group))) {
      return 'editor'
    }
    return roleMapping.defaultRole
  }

  generateSSOToken(user, config) {
    const payload = {
      userId: user.id,
      email: user.email,
      orgId: user.orgId,
      role: user.role,
      ssoProvider: config.type,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (config.security.sessionTimeout * 60 * 60)
    }

    return jwt.sign(payload, process.env.JWT_SECRET || 'default-secret')
  }

  validateConfiguration(config) {
    if (!config.orgId || !config.type) return false
    
    if (config.type === 'saml') {
      return !!(config.saml?.entryPoint && config.saml?.issuer)
    }
    
    if (config.type === 'oidc') {
      return !!(config.oidc?.issuer && config.oidc?.clientId)
    }
    
    return true
  }

  async testSAMLEndpoint(endpoint) {
    // Mock endpoint test
    return true
  }

  async testOIDCDiscovery(issuer) {
    // Mock OIDC discovery test
    return true
  }

  validateCertificate(cert) {
    // Mock certificate validation
    return cert && cert.includes('BEGIN CERTIFICATE')
  }

  cleanupExpiredSAMLRequests() {
    const now = Date.now()
    const expiration = 5 * 60 * 1000 // 5 minutes

    for (const [requestId, request] of this.samlRequests.entries()) {
      if (now - new Date(request.timestamp).getTime() > expiration) {
        this.samlRequests.delete(requestId)
      }
    }
  }

  generateMockLoginTrends(timeRange) {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
    const data = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      
      data.push({
        date: date.toISOString().split('T')[0],
        logins: Math.floor(Math.random() * 50) + 10
      })
    }
    
    return data
  }

  generateMockTopUsers() {
    return [
      { email: '<EMAIL>', logins: 145, lastLogin: new Date() },
      { email: '<EMAIL>', logins: 132, lastLogin: new Date() },
      { email: '<EMAIL>', logins: 98, lastLogin: new Date() }
    ]
  }

  generateMockSecurityEvents() {
    return [
      { type: 'login_success', user: '<EMAIL>', timestamp: new Date(), ip: '*************' },
      { type: 'login_failed', user: '<EMAIL>', timestamp: new Date(), ip: '********' },
      { type: 'config_updated', user: '<EMAIL>', timestamp: new Date(), changes: ['security.sessionTimeout'] }
    ]
  }

  sanitizeConfiguration(config) {
    return {
      id: config.id,
      name: config.name,
      type: config.type,
      status: config.status,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
      security: {
        enforceSSO: config.security.enforceSSO,
        allowLocalLogin: config.security.allowLocalLogin,
        sessionTimeout: config.security.sessionTimeout,
        requireMFA: config.security.requireMFA
      },
      endpoints: this.providers.get(config.type)?.endpoints
    }
  }

  sanitizeUser(user) {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      lastLogin: user.lastLogin
    }
  }
}

// Export singleton instance
export default new SSOService()