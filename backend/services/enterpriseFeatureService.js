import { logger } from '../utils/logger.js'
import EmailSequence from '../models/EmailSequence.js'
import User from '../models/User.js'

/**
 * Enterprise Feature Service
 * Manages premium features that justify higher pricing tiers
 */
class EnterpriseFeatureService {
  constructor() {
    this.premiumFeatures = {
      pro: [
        'advanced_analytics',
        'ab_testing', 
        'custom_templates',
        'priority_support',
        'integration_webhook'
      ],
      business: [
        'team_collaboration',
        'white_label',
        'custom_branding',
        'advanced_integrations',
        'dedicated_success_manager',
        'custom_ai_training',
        'enterprise_security',
        'sla_guarantee'
      ]
    }
  }

  /**
   * Check if user has access to a premium feature
   */
  hasFeatureAccess(user, featureName) {
    const userTier = user.subscription.type
    
    if (userTier === 'business') {
      return [...this.premiumFeatures.pro, ...this.premiumFeatures.business].includes(featureName)
    }
    
    if (userTier === 'pro') {
      return this.premiumFeatures.pro.includes(featureName)
    }
    
    return false // Free tier has no premium features
  }

  /**
   * Generate team collaboration workspace
   */
  async createTeamWorkspace(userId, workspaceName) {
    try {
      const user = await User.findById(userId)
      
      if (!this.hasFeatureAccess(user, 'team_collaboration')) {
        throw new Error('Team collaboration requires Business plan')
      }
      
      // Create workspace logic here
      const workspace = {
        id: `workspace_${Date.now()}`,
        name: workspaceName,
        owner: userId,
        members: [userId],
        createdAt: new Date(),
        settings: {
          permissions: {
            canCreateSequences: ['owner', 'admin'],
            canEditSequences: ['owner', 'admin', 'editor'],
            canViewAnalytics: ['owner', 'admin', 'editor', 'viewer']
          }
        }
      }
      
      logger.info('Team workspace created:', { userId, workspaceName })
      return workspace
    } catch (error) {
      logger.error('Team workspace creation error:', error)
      throw error
    }
  }

  /**
   * Generate advanced analytics dashboard
   */
  async generateAdvancedAnalytics(userId) {
    try {
      const user = await User.findById(userId)
      
      if (!this.hasFeatureAccess(user, 'advanced_analytics')) {
        throw new Error('Advanced analytics requires Pro plan or higher')
      }
      
      const sequences = await EmailSequence.find({ user: userId })
      
      // Advanced metrics calculation
      const analytics = {
        overview: await this.calculateOverviewMetrics(sequences),
        performance: await this.calculatePerformanceMetrics(sequences),
        psychology: await this.calculatePsychologyMetrics(sequences),
        predictions: await this.generatePredictiveAnalytics(sequences),
        benchmarks: await this.getIndustryBenchmarks(sequences),
        recommendations: await this.generateRecommendations(sequences)
      }
      
      return analytics
    } catch (error) {
      logger.error('Advanced analytics error:', error)
      throw error
    }
  }

  /**
   * Calculate overview metrics
   */
  async calculateOverviewMetrics(sequences) {
    const totalSequences = sequences.length
    const totalEmails = sequences.reduce((sum, seq) => sum + seq.emails.length, 0)
    const avgSequenceLength = totalEmails / totalSequences || 0
    const avgAIScore = sequences.reduce((sum, seq) => sum + (seq.aiAnalysis?.overallScore || 0), 0) / totalSequences || 0
    
    const industries = [...new Set(sequences.map(seq => seq.businessInfo.industry))]
    const goals = [...new Set(sequences.map(seq => seq.generationSettings.primaryGoal))]
    
    return {
      totalSequences,
      totalEmails,
      avgSequenceLength: Math.round(avgSequenceLength * 10) / 10,
      avgAIScore: Math.round(avgAIScore),
      industries,
      goals,
      creationTrend: this.calculateCreationTrend(sequences)
    }
  }

  /**
   * Calculate performance metrics
   */
  async calculatePerformanceMetrics(sequences) {
    const performanceData = sequences.map(seq => ({
      id: seq._id,
      title: seq.title,
      industry: seq.businessInfo.industry,
      aiScore: seq.aiAnalysis?.overallScore || 0,
      emailCount: seq.emails.length,
      performance: seq.performance
    }))
    
    // Top performers
    const topPerformers = performanceData
      .sort((a, b) => b.aiScore - a.aiScore)
      .slice(0, 5)
    
    // Performance by industry
    const performanceByIndustry = this.groupPerformanceByIndustry(performanceData)
    
    return {
      topPerformers,
      performanceByIndustry,
      avgOpenRate: this.calculateAvgMetric(performanceData, 'openRate'),
      avgClickRate: this.calculateAvgMetric(performanceData, 'clickRate'),
      avgConversionRate: this.calculateAvgMetric(performanceData, 'conversionRate')
    }
  }

  /**
   * Calculate psychology metrics
   */
  async calculatePsychologyMetrics(sequences) {
    const psychologyData = []
    
    sequences.forEach(sequence => {
      sequence.emails.forEach(email => {
        if (email.psychologyTriggers) {
          psychologyData.push(...email.psychologyTriggers)
        }
      })
    })
    
    // Count trigger usage
    const triggerCounts = psychologyData.reduce((acc, trigger) => {
      acc[trigger] = (acc[trigger] || 0) + 1
      return acc
    }, {})
    
    // Most effective triggers (mock data for now)
    const effectivenessByTrigger = {
      'scarcity': 85,
      'urgency': 82,
      'social_proof': 78,
      'authority': 75,
      'reciprocity': 72,
      'commitment': 70,
      'liking': 68,
      'anchoring': 65,
      'loss_aversion': 80,
      'curiosity': 88,
      'trust': 76
    }
    
    return {
      triggerUsage: triggerCounts,
      triggerEffectiveness: effectivenessByTrigger,
      recommendedTriggers: this.getRecommendedTriggers(triggerCounts, effectivenessByTrigger)
    }
  }

  /**
   * Generate predictive analytics
   */
  async generatePredictiveAnalytics(sequences) {
    // Simple predictive model based on historical data
    const recentSequences = sequences
      .filter(seq => seq.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
      .length
    
    const growthRate = this.calculateGrowthRate(sequences)
    
    return {
      projectedSequences: {
        nextMonth: Math.round(recentSequences * (1 + growthRate)),
        next3Months: Math.round(recentSequences * 3 * (1 + growthRate)),
        nextYear: Math.round(recentSequences * 12 * (1 + growthRate))
      },
      conversionPredictions: this.generateConversionPredictions(sequences),
      optimalTiming: this.calculateOptimalTiming(sequences)
    }
  }

  /**
   * Get industry benchmarks
   */
  async getIndustryBenchmarks(sequences) {
    // Mock industry benchmarks (would come from aggregated data in production)
    const benchmarks = {
      'e-commerce': { openRate: 21.5, clickRate: 3.2, conversionRate: 1.8 },
      'saas': { openRate: 23.1, clickRate: 4.1, conversionRate: 2.3 },
      'education': { openRate: 26.8, clickRate: 3.8, conversionRate: 1.5 },
      'healthcare': { openRate: 22.4, clickRate: 2.9, conversionRate: 1.2 },
      'finance': { openRate: 19.8, clickRate: 2.7, conversionRate: 1.9 },
      'real-estate': { openRate: 24.2, clickRate: 3.5, conversionRate: 2.1 }
    }
    
    const userIndustries = [...new Set(sequences.map(seq => seq.businessInfo.industry))]
    
    return userIndustries.reduce((acc, industry) => {
      acc[industry] = benchmarks[industry] || { openRate: 22.0, clickRate: 3.3, conversionRate: 1.7 }
      return acc
    }, {})
  }

  /**
   * Generate actionable recommendations
   */
  async generateRecommendations(sequences) {
    const recommendations = []
    
    // Analyze sequence performance
    const avgScore = sequences.reduce((sum, seq) => sum + (seq.aiAnalysis?.overallScore || 0), 0) / sequences.length || 0
    
    if (avgScore < 70) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        title: 'Optimize AI Scoring',
        description: 'Your sequences have room for improvement. Focus on stronger psychology triggers and emotional journeys.',
        impact: 'Potential 25% increase in conversion rates'
      })
    }
    
    // Check sequence length
    const avgLength = sequences.reduce((sum, seq) => sum + seq.emails.length, 0) / sequences.length || 0
    
    if (avgLength < 5) {
      recommendations.push({
        type: 'optimization',
        priority: 'medium',
        title: 'Extend Sequence Length',
        description: 'Longer sequences (7-10 emails) typically perform better for relationship building.',
        impact: 'Potential 15% increase in trust and conversion'
      })
    }
    
    return recommendations
  }

  /**
   * Helper methods
   */
  calculateCreationTrend(sequences) {
    const last30Days = sequences.filter(seq => 
      seq.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length
    
    const previous30Days = sequences.filter(seq => 
      seq.createdAt > new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) &&
      seq.createdAt <= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length
    
    const growth = previous30Days === 0 ? 100 : ((last30Days - previous30Days) / previous30Days * 100)
    
    return {
      last30Days,
      previous30Days,
      growthPercentage: Math.round(growth)
    }
  }

  groupPerformanceByIndustry(performanceData) {
    return performanceData.reduce((acc, data) => {
      const industry = data.industry
      if (!acc[industry]) {
        acc[industry] = { sequences: [], avgScore: 0 }
      }
      acc[industry].sequences.push(data)
      acc[industry].avgScore = acc[industry].sequences.reduce((sum, seq) => sum + seq.aiScore, 0) / acc[industry].sequences.length
      return acc
    }, {})
  }

  calculateAvgMetric(performanceData, metric) {
    const validData = performanceData.filter(data => data.performance && data.performance[metric] > 0)
    if (validData.length === 0) return 0
    
    return validData.reduce((sum, data) => sum + data.performance[metric], 0) / validData.length
  }

  getRecommendedTriggers(usage, effectiveness) {
    const unusedTriggers = Object.keys(effectiveness).filter(trigger => !usage[trigger] || usage[trigger] === 0)
    return unusedTriggers
      .sort((a, b) => effectiveness[b] - effectiveness[a])
      .slice(0, 3)
  }

  calculateGrowthRate(sequences) {
    // Simple growth calculation
    const recentMonth = sequences.filter(seq => 
      seq.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length
    
    const previousMonth = sequences.filter(seq => 
      seq.createdAt > new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) &&
      seq.createdAt <= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length
    
    return previousMonth === 0 ? 0.1 : (recentMonth - previousMonth) / previousMonth
  }

  generateConversionPredictions(sequences) {
    // Mock conversion predictions
    return {
      emailOptimization: '12% improvement with better subject lines',
      timingOptimization: '8% improvement with optimal send times',
      personalizationImpact: '15% improvement with dynamic content'
    }
  }

  calculateOptimalTiming(sequences) {
    // Mock optimal timing data
    return {
      bestDayOfWeek: 'Tuesday',
      bestTimeOfDay: '10:00 AM',
      bestEmailInterval: '3 days',
      seasonalTrends: 'Higher engagement in Q4'
    }
  }

  /**
   * Generate white-label customization
   */
  async createWhiteLabelBranding(userId, brandingOptions) {
    try {
      const user = await User.findById(userId)
      
      if (!this.hasFeatureAccess(user, 'white_label')) {
        throw new Error('White label branding requires Business plan')
      }
      
      const branding = {
        logo: brandingOptions.logo,
        colors: {
          primary: brandingOptions.primaryColor,
          secondary: brandingOptions.secondaryColor,
          accent: brandingOptions.accentColor
        },
        typography: brandingOptions.fontFamily,
        customDomain: brandingOptions.customDomain,
        emailFooter: brandingOptions.emailFooter
      }
      
      // Save branding to user profile
      await User.findByIdAndUpdate(userId, {
        'enterprise.branding': branding
      })
      
      logger.info('White label branding created:', { userId })
      return branding
    } catch (error) {
      logger.error('White label branding error:', error)
      throw error
    }
  }
}

export default new EnterpriseFeatureService()