import claudeAIService from './claudeAIService.js'
import { logger } from '../utils/logger.js'

/**
 * Email Intelligence Service - Advanced AI-powered email optimization
 * Features: Subject line optimization, send time intelligence, deliverability scoring
 */
class EmailIntelligenceService {
  constructor() {
    this.industryBenchmarks = this.loadIndustryBenchmarks()
    this.timezoneData = this.loadTimezoneOptimizations()
    this.deliverabilityRules = this.loadDeliverabilityRules()
  }

  /**
   * Optimize subject lines using advanced psychology and data science
   */
  async optimizeSubjectLines(originalSubject, context) {
    try {
      const { businessInfo, audience, emailType, historicalData } = context

      // Get <PERSON>'s advanced subject line variations
      const claudeVariations = await claudeAIService.generateSubjectLineVariations(
        originalSubject, 
        businessInfo, 
        emailType
      )

      // Add our proprietary optimization analysis
      const optimizedVariations = await Promise.all(
        claudeVariations.map(async (variation) => {
          const analysis = await this.analyzeSubjectLine(variation.subject, context)
          return {
            ...variation,
            ...analysis,
            optimizationScore: this.calculateOptimizationScore(analysis),
            abTestRecommendation: this.getABTestRecommendation(variation, historicalData)
          }
        })
      )

      // Generate additional variations using our algorithms
      const algorithmicVariations = await this.generateAlgorithmicVariations(
        originalSubject, 
        context
      )

      const allVariations = [...optimizedVariations, ...algorithmicVariations]
      
      return {
        originalSubject,
        variations: allVariations.sort((a, b) => b.optimizationScore - a.optimizationScore),
        recommendations: {
          winner: allVariations[0],
          backupOptions: allVariations.slice(1, 4),
          abTestStrategy: this.createABTestStrategy(allVariations.slice(0, 5))
        },
        insights: await this.generateSubjectLineInsights(allVariations, context)
      }
    } catch (error) {
      logger.error('Subject line optimization error:', error)
      throw error
    }
  }

  /**
   * Intelligent send time optimization based on multiple factors
   */
  async optimizeSendTimes(emailSequence, context) {
    try {
      const { audience, timezone, industry, historicalData } = context
      
      const optimizations = []

      for (let i = 0; i < emailSequence.length; i++) {
        const email = emailSequence[i]
        
        const sendTimeAnalysis = {
          emailIndex: i,
          recommendedTimes: await this.calculateOptimalSendTimes({
            audience,
            timezone,
            industry,
            emailType: this.classifyEmailType(email),
            dayOfSequence: email.dayDelay || i,
            historicalData
          }),
          considerations: await this.getSendTimeConsiderations(email, context),
          timezoneOptimizations: await this.getTimezoneOptimizations(audience, timezone)
        }

        optimizations.push(sendTimeAnalysis)
      }

      return {
        sequenceOptimizations: optimizations,
        overallStrategy: await this.createSendTimeStrategy(optimizations, context),
        performancePredictions: await this.predictSendTimePerformance(optimizations),
        implementationGuide: this.createImplementationGuide(optimizations)
      }
    } catch (error) {
      logger.error('Send time optimization error:', error)
      throw error
    }
  }

  /**
   * Advanced deliverability scoring and optimization
   */
  async analyzeDeliverability(email, context) {
    try {
      const deliverabilityFactors = {
        subjectLineScore: await this.scoreSubjectDeliverability(email.subject),
        contentScore: await this.scoreContentDeliverability(email.body),
        senderReputationFactors: await this.analyzeSenderFactors(context),
        technicalFactors: await this.analyzeTechnicalFactors(email),
        audienceEngagementPrediction: await this.predictAudienceEngagement(email, context)
      }

      const overallScore = this.calculateDeliverabilityScore(deliverabilityFactors)
      const improvements = await this.generateDeliverabilityImprovements(deliverabilityFactors)

      return {
        score: overallScore,
        breakdown: deliverabilityFactors,
        improvements,
        riskAssessment: this.assessDeliverabilityRisks(deliverabilityFactors),
        optimizationPlan: await this.createDeliverabilityOptimizationPlan(deliverabilityFactors)
      }
    } catch (error) {
      logger.error('Deliverability analysis error:', error)
      throw error
    }
  }

  /**
   * Comprehensive email performance prediction
   */
  async predictPerformance(emailSequence, context) {
    try {
      const predictions = []

      for (const email of emailSequence) {
        const performance = await this.predictEmailPerformance(email, context)
        predictions.push(performance)
      }

      const sequenceMetrics = this.calculateSequenceMetrics(predictions)
      const optimizationOpportunities = await this.identifyOptimizationOpportunities(predictions)

      return {
        individualPredictions: predictions,
        sequenceMetrics,
        optimizationOpportunities,
        competitiveBenchmark: this.benchmarkAgainstIndustry(sequenceMetrics, context.industry),
        actionableRecommendations: await this.generateActionableRecommendations(predictions, context)
      }
    } catch (error) {
      logger.error('Performance prediction error:', error)
      throw error
    }
  }

  // Subject Line Analysis Methods
  async analyzeSubjectLine(subject, context) {
    const analysis = {
      length: subject.length,
      wordCount: subject.split(' ').length,
      emotionalTriggers: this.detectEmotionalTriggers(subject),
      psychologyTriggers: this.detectPsychologyTriggers(subject),
      spamScore: this.calculateSpamScore(subject),
      readabilityScore: this.calculateReadabilityScore(subject),
      personalizations: this.detectPersonalizations(subject),
      industryRelevance: this.scoreIndustryRelevance(subject, context.businessInfo?.industry),
      competitiveAnalysis: await this.compareToCompetitors(subject, context)
    }

    analysis.mobileOptimization = this.scoreMobileOptimization(analysis)
    analysis.predictedOpenRate = this.predictOpenRate(analysis, context)

    return analysis
  }

  calculateOptimizationScore(analysis) {
    let score = 50 // Base score

    // Length optimization (30-50 chars ideal)
    if (analysis.length >= 30 && analysis.length <= 50) score += 10
    else if (analysis.length < 20 || analysis.length > 70) score -= 15

    // Emotional triggers
    score += analysis.emotionalTriggers.length * 5

    // Psychology triggers
    score += analysis.psychologyTriggers.length * 8

    // Spam score (lower is better)
    score -= analysis.spamScore * 2

    // Mobile optimization
    score += analysis.mobileOptimization * 0.2

    // Industry relevance
    score += analysis.industryRelevance * 0.15

    return Math.max(0, Math.min(100, score))
  }

  async generateAlgorithmicVariations(originalSubject, context) {
    const variations = []
    const { businessInfo, audience } = context

    // Pattern-based variations
    const patterns = [
      { type: 'question', pattern: (s) => `${s}?` },
      { type: 'urgency', pattern: (s) => `${s} (Limited Time)` },
      { type: 'curiosity', pattern: (s) => `The Secret Behind ${s}` },
      { type: 'benefit', pattern: (s) => `${s} - Boost Your Results` },
      { type: 'social_proof', pattern: (s) => `${s} (Used by 10,000+ Professionals)` }
    ]

    for (const pattern of patterns) {
      const newSubject = pattern.pattern(originalSubject)
      if (newSubject.length <= 70) {
        const analysis = await this.analyzeSubjectLine(newSubject, context)
        variations.push({
          subject: newSubject,
          approach: pattern.type,
          source: 'algorithmic',
          ...analysis,
          optimizationScore: this.calculateOptimizationScore(analysis)
        })
      }
    }

    return variations
  }

  // Send Time Optimization Methods
  async calculateOptimalSendTimes({ audience, timezone, industry, emailType, dayOfSequence }) {
    const industryDefaults = this.industryBenchmarks[industry] || this.industryBenchmarks.default
    const audienceProfile = this.analyzeAudienceProfile(audience)
    
    const baseRecommendations = industryDefaults.sendTimes[emailType] || industryDefaults.sendTimes.default
    
    // Adjust for audience behavior
    const adjustedTimes = baseRecommendations.map(time => {
      return this.adjustTimeForAudience(time, audienceProfile, dayOfSequence)
    })

    // Timezone optimization
    const timezoneOptimized = adjustedTimes.map(time => {
      return this.optimizeForTimezone(time, timezone, audience)
    })

    return timezoneOptimized.map(time => ({
      time,
      confidence: this.calculateTimeConfidence(time, { audience, industry, emailType }),
      reasoning: this.explainTimeChoice(time, { audience, industry, emailType, dayOfSequence })
    }))
  }

  classifyEmailType(email) {
    const subject = email.subject?.toLowerCase() || ''
    const body = email.body?.toLowerCase() || ''
    
    if (subject.includes('welcome') || body.includes('welcome')) return 'welcome'
    if (subject.includes('thank') || body.includes('thank')) return 'thank_you'
    if (subject.includes('reminder') || body.includes('reminder')) return 'reminder'
    if (subject.includes('last chance') || body.includes('expires')) return 'urgency'
    if (body.includes('testimonial') || body.includes('success story')) return 'social_proof'
    
    return 'promotional'
  }

  // Deliverability Analysis Methods
  async scoreSubjectDeliverability(subject) {
    let score = 80 // Base score

    // Check for spam trigger words
    const spamWords = ['free', 'guarantee', 'no obligation', 'act now', '!!!', 'urgent', 'click here']
    const spamCount = spamWords.reduce((count, word) => {
      return count + (subject.toLowerCase().includes(word) ? 1 : 0)
    }, 0)
    score -= spamCount * 10

    // Check for excessive punctuation
    const exclamationCount = (subject.match(/!/g) || []).length
    if (exclamationCount > 1) score -= 15

    // Check for all caps
    const capsRatio = (subject.match(/[A-Z]/g) || []).length / subject.length
    if (capsRatio > 0.3) score -= 20

    // Check for numbers/symbols that might trigger spam filters
    if (subject.includes('$') && subject.match(/\d+/)) score -= 10

    return Math.max(0, score)
  }

  async scoreContentDeliverability(body) {
    let score = 80 // Base score

    // Text-to-image ratio (should be mostly text)
    const textLength = body.length
    const imageCount = (body.match(/<img|!\[.*?\]\(/g) || []).length
    if (imageCount > 0 && textLength / imageCount < 100) score -= 15

    // Link density
    const linkCount = (body.match(/https?:\/\/|<a /g) || []).length
    const linkDensity = linkCount / (textLength / 100)
    if (linkDensity > 5) score -= 10

    // Spam content analysis
    const spamPhrases = ['make money fast', 'work from home', 'click here now', 'limited time offer']
    const spamPhraseCount = spamPhrases.reduce((count, phrase) => {
      return count + (body.toLowerCase().includes(phrase) ? 1 : 0)
    }, 0)
    score -= spamPhraseCount * 8

    return Math.max(0, score)
  }

  // Helper Methods
  detectEmotionalTriggers(subject) {
    const triggers = {
      excitement: ['amazing', 'incredible', 'fantastic', 'awesome', '!'],
      urgency: ['limited', 'deadline', 'expires', 'hurry', 'act now'],
      curiosity: ['secret', 'revealed', 'discover', 'hidden', '?'],
      fear: ['mistake', 'avoid', 'missing out', 'losing'],
      trust: ['proven', 'guaranteed', 'certified', 'trusted']
    }

    const detected = []
    for (const [emotion, words] of Object.entries(triggers)) {
      if (words.some(word => subject.toLowerCase().includes(word))) {
        detected.push(emotion)
      }
    }

    return detected
  }

  loadIndustryBenchmarks() {
    return {
      'SaaS': {
        sendTimes: {
          welcome: ['09:00', '14:00'],
          promotional: ['10:00', '15:00'],
          educational: ['11:00', '16:00'],
          urgency: ['09:00', '17:00']
        },
        openRates: { avg: 22, good: 28, excellent: 35 },
        clickRates: { avg: 3.2, good: 4.5, excellent: 6.8 }
      },
      'E-commerce': {
        sendTimes: {
          welcome: ['10:00', '19:00'],
          promotional: ['12:00', '18:00', '20:00'],
          educational: ['11:00', '15:00'],
          urgency: ['19:00', '21:00']
        },
        openRates: { avg: 18, good: 25, excellent: 32 },
        clickRates: { avg: 2.8, good: 4.2, excellent: 6.5 }
      },
      'default': {
        sendTimes: {
          welcome: ['09:00', '14:00'],
          promotional: ['10:00', '15:00'],
          educational: ['11:00', '16:00'],
          urgency: ['09:00', '17:00']
        },
        openRates: { avg: 20, good: 26, excellent: 33 },
        clickRates: { avg: 3.0, good: 4.3, excellent: 6.2 }
      }
    }
  }

  loadTimezoneOptimizations() {
    return {
      'America/New_York': { offset: -5, peakHours: ['09:00', '14:00', '17:00'] },
      'America/Los_Angeles': { offset: -8, peakHours: ['10:00', '15:00', '18:00'] },
      'Europe/London': { offset: 0, peakHours: ['08:00', '13:00', '16:00'] },
      'Asia/Tokyo': { offset: 9, peakHours: ['09:00', '12:00', '19:00'] }
    }
  }

  loadDeliverabilityRules() {
    return {
      subjectLength: { min: 30, max: 50, optimal: 40 },
      spamTriggers: ['free', 'guarantee', 'no obligation', 'risk-free', 'act now'],
      contentRatio: { textToImage: 80, textToLink: 90 }
    }
  }

  // Additional helper methods would continue here...
  // For brevity, I'm including the key methods that demonstrate the functionality
}

export default new EmailIntelligenceService()