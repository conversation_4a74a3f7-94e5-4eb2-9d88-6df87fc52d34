import { EventEmitter } from 'events'
import <PERSON> from 'bull'
import { logger } from '../utils/logger.js'
import Agent from '../models/Agent.js'
import Workflow from '../models/Workflow.js'
import ColonyWorkflow from '../models/ColonyWorkflow.js'
import AgentExecution from '../models/AgentExecution.js'
import agentEngine from './agentEngine.js'
import naturalLanguageWorkflowService from './naturalLanguageWorkflowService.js'

// Redis connection for Bull queues
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'

class WorkflowExecutionService extends EventEmitter {
  constructor() {
    super()
    
    // Create queues for different job types
    this.queues = {
      workflow: new Bull('workflow-execution', REDIS_URL),
      agent: new <PERSON>('agent-execution', REDIS_URL),
      integration: new Bull('integration-sync', REDIS_URL),
      email: new <PERSON>('email-processing', REDIS_URL)
    }
    
    // Active executions tracking
    this.activeExecutions = new Map()
    
    // Initialize queue processors
    this.initializeProcessors()
  }

  initializeProcessors() {
    // Workflow execution processor
    this.queues.workflow.process(async (job) => {
      const { workflowId, userId, inputs, executionId } = job.data
      
      try {
        await this.executeWorkflowSteps(workflowId, userId, inputs, executionId, job)
        return { success: true, executionId }
      } catch (error) {
        logger.error(`Workflow execution failed: ${executionId}`, error)
        throw error
      }
    })

    // Agent execution processor
    this.queues.agent.process(async (job) => {
      const { agentId, inputs, userId, executionId } = job.data
      
      try {
        const result = await agentEngine.executeAgent(agentId, inputs, {
          onProgress: (progress) => {
            job.progress(progress.percentage)
            this.emit('agent-progress', {
              executionId,
              agentId,
              progress
            })
          }
        })
        
        return result
      } catch (error) {
        logger.error(`Agent execution failed: ${executionId}`, error)
        throw error
      }
    })

    // Integration sync processor
    this.queues.integration.process(async (job) => {
      const { integrationId, action, data } = job.data
      
      try {
        // Handle integration actions (webhook processing, data sync, etc.)
        logger.info(`Processing integration job: ${integrationId} - ${action}`)
        
        // TODO: Implement actual integration processing
        await this.processIntegrationAction(integrationId, action, data)
        
        return { success: true }
      } catch (error) {
        logger.error(`Integration processing failed: ${integrationId}`, error)
        throw error
      }
    })

    // Email processing processor
    this.queues.email.process(async (job) => {
      const { sequenceId, emailIndex, recipientId } = job.data
      
      try {
        // Handle email sending/scheduling
        logger.info(`Processing email job: ${sequenceId} - Email ${emailIndex}`)
        
        // TODO: Implement actual email processing
        await this.processEmailDelivery(sequenceId, emailIndex, recipientId)
        
        return { success: true }
      } catch (error) {
        logger.error(`Email processing failed: ${sequenceId}`, error)
        throw error
      }
    })

    // Set up event listeners for queue events
    Object.entries(this.queues).forEach(([name, queue]) => {
      queue.on('completed', (job, result) => {
        logger.info(`${name} job completed: ${job.id}`)
        this.emit('job-completed', { queue: name, jobId: job.id, result })
      })

      queue.on('failed', (job, error) => {
        logger.error(`${name} job failed: ${job.id}`, error)
        this.emit('job-failed', { queue: name, jobId: job.id, error })
      })

      queue.on('progress', (job, progress) => {
        this.emit('job-progress', { queue: name, jobId: job.id, progress })
      })
    })
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflowId, userId, inputs = {}) {
    try {
      // Load workflow
      const workflow = await Workflow.findById(workflowId)
        .populate('agents.agent')
      
      if (!workflow) {
        throw new Error('Workflow not found')
      }

      // Create execution record
      const execution = await AgentExecution.create({
        userId,
        agentId: null, // Workflow execution
        workflowId,
        status: 'running',
        inputs,
        startedAt: new Date(),
        metadata: {
          workflowName: workflow.name,
          totalSteps: workflow.agents.length
        }
      })

      // Add to queue
      const job = await this.queues.workflow.add({
        workflowId,
        userId,
        inputs,
        executionId: execution._id
      }, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      })

      // Track active execution
      this.activeExecutions.set(execution._id.toString(), {
        job,
        workflow,
        startTime: Date.now()
      })

      return {
        executionId: execution._id,
        jobId: job.id,
        status: 'queued'
      }

    } catch (error) {
      logger.error('Workflow execution error:', error)
      throw error
    }
  }

  /**
   * Execute workflow steps sequentially
   */
  async executeWorkflowSteps(workflowId, userId, inputs, executionId, job) {
    const workflow = await Workflow.findById(workflowId)
      .populate('agents.agent')
    
    let currentInputs = inputs
    const results = []
    
    for (let i = 0; i < workflow.agents.length; i++) {
      const step = workflow.agents[i]
      const progress = ((i + 1) / workflow.agents.length) * 100
      
      // Update progress
      job.progress(progress)
      
      this.emit('workflow-step', {
        executionId,
        step: i + 1,
        totalSteps: workflow.agents.length,
        agentName: step.agent.name
      })

      // Execute agent
      const agentResult = await agentEngine.executeAgent(
        step.agent._id,
        {
          ...currentInputs,
          ...step.config
        },
        {
          onProgress: (agentProgress) => {
            this.emit('agent-progress', {
              executionId,
              workflowStep: i + 1,
              agentId: step.agent._id,
              progress: agentProgress
            })
          }
        }
      )

      results.push({
        step: i + 1,
        agentId: step.agent._id,
        agentName: step.agent.name,
        result: agentResult
      })

      // Use output as input for next step
      if (step.outputMapping) {
        currentInputs = this.mapOutputs(currentInputs, agentResult, step.outputMapping)
      } else {
        currentInputs = { ...currentInputs, previousResult: agentResult }
      }

      // Check conditions for next step
      if (step.conditions && !this.evaluateConditions(agentResult, step.conditions)) {
        logger.info(`Skipping remaining steps due to conditions at step ${i + 1}`)
        break
      }
    }

    // Update execution record
    await AgentExecution.findByIdAndUpdate(executionId, {
      status: 'completed',
      completedAt: new Date(),
      outputs: results,
      metadata: {
        stepsCompleted: results.length,
        totalSteps: workflow.agents.length
      }
    })

    // Clean up
    this.activeExecutions.delete(executionId.toString())

    return results
  }

  /**
   * Execute a natural language workflow
   */
  async executeNaturalLanguageWorkflow(description, userId, context = {}) {
    try {
      // Parse natural language into workflow
      const workflow = await naturalLanguageWorkflowService.parseNaturalLanguage(
        description,
        context
      )

      // Create colony workflow
      const colonyWorkflow = await ColonyWorkflow.create({
        userId,
        name: workflow.name,
        description,
        naturalLanguageDescription: description,
        agents: workflow.agents,
        triggers: workflow.triggers,
        outputs: workflow.outputs,
        status: 'active'
      })

      // Execute the workflow
      return await this.executeWorkflow(colonyWorkflow._id, userId, context)

    } catch (error) {
      logger.error('Natural language workflow execution error:', error)
      throw error
    }
  }

  /**
   * Cancel an active execution
   */
  async cancelExecution(executionId) {
    const execution = this.activeExecutions.get(executionId)
    
    if (!execution) {
      throw new Error('Execution not found or already completed')
    }

    // Cancel the job
    await execution.job.remove()
    
    // Update execution record
    await AgentExecution.findByIdAndUpdate(executionId, {
      status: 'cancelled',
      completedAt: new Date(),
      metadata: {
        cancelledAt: new Date(),
        reason: 'User requested cancellation'
      }
    })

    // Clean up
    this.activeExecutions.delete(executionId)

    return { success: true, message: 'Execution cancelled' }
  }

  /**
   * Get execution status
   */
  async getExecutionStatus(executionId) {
    // Check if actively running
    const activeExecution = this.activeExecutions.get(executionId)
    
    if (activeExecution) {
      const job = await activeExecution.job.getState()
      const progress = await activeExecution.job.progress()
      
      return {
        status: 'running',
        progress,
        jobState: job,
        duration: Date.now() - activeExecution.startTime
      }
    }

    // Get from database
    const execution = await AgentExecution.findById(executionId)
    
    if (!execution) {
      throw new Error('Execution not found')
    }

    return {
      status: execution.status,
      startedAt: execution.startedAt,
      completedAt: execution.completedAt,
      outputs: execution.outputs,
      error: execution.error,
      metadata: execution.metadata
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const stats = {}
    
    for (const [name, queue] of Object.entries(this.queues)) {
      const jobCounts = await queue.getJobCounts()
      const isPaused = await queue.isPaused()
      
      stats[name] = {
        ...jobCounts,
        isPaused
      }
    }

    return stats
  }

  /**
   * Helper method to map outputs between agents
   */
  mapOutputs(currentInputs, agentResult, mapping) {
    const mapped = { ...currentInputs }
    
    for (const [sourceKey, targetKey] of Object.entries(mapping)) {
      if (agentResult[sourceKey] !== undefined) {
        mapped[targetKey] = agentResult[sourceKey]
      }
    }

    return mapped
  }

  /**
   * Helper method to evaluate conditions
   */
  evaluateConditions(result, conditions) {
    for (const condition of conditions) {
      const { field, operator, value } = condition
      const actualValue = result[field]

      switch (operator) {
        case 'equals':
          if (actualValue !== value) return false
          break
        case 'contains':
          if (!actualValue?.includes(value)) return false
          break
        case 'greater_than':
          if (!(actualValue > value)) return false
          break
        case 'less_than':
          if (!(actualValue < value)) return false
          break
        default:
          logger.warn(`Unknown condition operator: ${operator}`)
      }
    }

    return true
  }

  /**
   * Process integration action (placeholder)
   */
  async processIntegrationAction(integrationId, action, data) {
    // TODO: Implement actual integration processing
    logger.info(`Processing integration ${integrationId}: ${action}`)
    
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return { success: true }
  }

  /**
   * Process email delivery (placeholder)
   */
  async processEmailDelivery(sequenceId, emailIndex, recipientId) {
    // TODO: Implement actual email delivery
    logger.info(`Delivering email ${emailIndex} from sequence ${sequenceId} to ${recipientId}`)
    
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return { success: true }
  }

  /**
   * Clean up resources
   */
  async shutdown() {
    logger.info('Shutting down workflow execution service...')
    
    // Close all queues
    await Promise.all(
      Object.values(this.queues).map(queue => queue.close())
    )
    
    logger.info('Workflow execution service shut down complete')
  }
}

export default new WorkflowExecutionService()