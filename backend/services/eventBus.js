import EventEmitter from 'events'
import { logger } from '../utils/logger.js'

class ConvertFlowEventBus extends EventEmitter {
  constructor() {
    super()
    this.stats = {
      totalEvents: 0,
      eventTypes: new Map(),
      errorCount: 0,
      startTime: Date.now()
    }
    
    // Increase max listeners for high-traffic scenarios
    this.setMaxListeners(100)
    
    this.setupEventHandlers()
    
    logger.info('🎭 Event-driven architecture initialized')
  }

  setupEventHandlers() {
    // Set up error handling for the event bus
    this.on('error', (error) => {
      this.stats.errorCount++
      logger.error('❌ Event bus error:', error)
    })

    // Track all events for statistics
    this.on('newListener', (event, listener) => {
      logger.debug(`📡 New event listener registered: ${event}`)
    })

    // Log when events are emitted (debug mode)
    const originalEmit = this.emit
    this.emit = (event, ...args) => {
      this.stats.totalEvents++
      
      // Update event type statistics
      const count = this.stats.eventTypes.get(event) || 0
      this.stats.eventTypes.set(event, count + 1)
      
      logger.debug(`📤 Event emitted: ${event}`, {
        event,
        listenersCount: this.listenerCount(event),
        totalEvents: this.stats.totalEvents
      })
      
      return originalEmit.call(this, event, ...args)
    }
  }

  // User-related events
  emitUserRegistered(userData) {
    this.emit('user:registered', {
      userId: userData._id,
      email: userData.email,
      plan: userData.subscription?.type || 'free',
      timestamp: new Date().toISOString()
    })
  }

  emitUserUpgraded(userData, oldPlan, newPlan) {
    this.emit('user:upgraded', {
      userId: userData._id,
      email: userData.email,
      oldPlan,
      newPlan,
      timestamp: new Date().toISOString()
    })
  }

  emitUserDowngraded(userData, oldPlan, newPlan) {
    this.emit('user:downgraded', {
      userId: userData._id,
      email: userData.email,
      oldPlan,
      newPlan,
      timestamp: new Date().toISOString()
    })
  }

  // Sequence-related events
  emitSequenceGenerated(sequenceData, userData) {
    this.emit('sequence:generated', {
      sequenceId: sequenceData._id,
      userId: userData._id,
      title: sequenceData.title,
      industry: sequenceData.businessInfo?.industry,
      emailCount: sequenceData.emails?.length || 0,
      aiScore: sequenceData.aiAnalysis?.overallScore,
      timestamp: new Date().toISOString()
    })
  }

  emitSequenceShared(sequenceData, userData, shareData) {
    this.emit('sequence:shared', {
      sequenceId: sequenceData._id,
      userId: userData._id,
      shareMethod: shareData.method, // 'email', 'link', 'export'
      timestamp: new Date().toISOString()
    })
  }

  emitSequenceDeleted(sequenceId, userId) {
    this.emit('sequence:deleted', {
      sequenceId,
      userId,
      timestamp: new Date().toISOString()
    })
  }

  // Usage-related events
  emitUsageThresholdReached(userData, thresholdType, usageStats) {
    this.emit('usage:threshold', {
      userId: userData._id,
      email: userData.email,
      thresholdType, // '80%', '95%', 'limit'
      currentUsage: usageStats.sequencesGenerated,
      limit: usageStats.sequencesLimit,
      percentage: usageStats.usagePercentage,
      timestamp: new Date().toISOString()
    })
  }

  emitOverageEnabled(userData) {
    this.emit('usage:overage_enabled', {
      userId: userData._id,
      email: userData.email,
      plan: userData.subscription?.type,
      timestamp: new Date().toISOString()
    })
  }

  emitOverageCharged(userData, chargeAmount, sequenceCount) {
    this.emit('usage:overage_charged', {
      userId: userData._id,
      email: userData.email,
      chargeAmount,
      sequenceCount,
      timestamp: new Date().toISOString()
    })
  }

  // Payment-related events
  emitPaymentSucceeded(paymentData) {
    this.emit('payment:succeeded', {
      userId: paymentData.userId,
      amount: paymentData.amount,
      currency: paymentData.currency,
      planType: paymentData.planType,
      paymentMethod: paymentData.paymentMethod,
      timestamp: new Date().toISOString()
    })
  }

  emitPaymentFailed(paymentData, error) {
    this.emit('payment:failed', {
      userId: paymentData.userId,
      amount: paymentData.amount,
      error: error.message,
      timestamp: new Date().toISOString()
    })
  }

  emitSubscriptionCanceled(userData) {
    this.emit('subscription:canceled', {
      userId: userData._id,
      email: userData.email,
      plan: userData.subscription?.type,
      canceledAt: new Date().toISOString()
    })
  }

  // AI-related events
  emitAIRequestStarted(requestData) {
    this.emit('ai:request_started', {
      requestId: requestData.id,
      userId: requestData.userId,
      promptType: requestData.type,
      timestamp: new Date().toISOString()
    })
  }

  emitAIRequestCompleted(requestData, duration, cacheHit = false) {
    this.emit('ai:request_completed', {
      requestId: requestData.id,
      userId: requestData.userId,
      duration,
      cacheHit,
      success: true,
      timestamp: new Date().toISOString()
    })
  }

  emitAIRequestFailed(requestData, error, duration) {
    this.emit('ai:request_failed', {
      requestId: requestData.id,
      userId: requestData.userId,
      error: error.message,
      duration,
      timestamp: new Date().toISOString()
    })
  }

  // System-related events
  emitSystemAlert(alertType, details) {
    this.emit('system:alert', {
      alertType, // 'performance', 'error', 'capacity', 'security'
      details,
      severity: details.severity || 'medium',
      timestamp: new Date().toISOString()
    })
  }

  emitCacheWarmed(cacheType, itemCount) {
    this.emit('cache:warmed', {
      cacheType,
      itemCount,
      timestamp: new Date().toISOString()
    })
  }

  emitCircuitBreakerOpened(serviceName) {
    this.emit('circuit_breaker:opened', {
      serviceName,
      timestamp: new Date().toISOString()
    })
  }

  emitCircuitBreakerClosed(serviceName) {
    this.emit('circuit_breaker:closed', {
      serviceName,
      timestamp: new Date().toISOString()
    })
  }

  // Event listener registration helpers
  onUserRegistered(handler) {
    this.on('user:registered', handler)
  }

  onSequenceGenerated(handler) {
    this.on('sequence:generated', handler)
  }

  onUsageThreshold(handler) {
    this.on('usage:threshold', handler)
  }

  onPaymentSucceeded(handler) {
    this.on('payment:succeeded', handler)
  }

  onSystemAlert(handler) {
    this.on('system:alert', handler)
  }

  // Get event statistics
  getStats() {
    const uptime = Date.now() - this.stats.startTime
    const eventTypesArray = Array.from(this.stats.eventTypes.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)

    return {
      performance: {
        totalEvents: this.stats.totalEvents,
        errorCount: this.stats.errorCount,
        eventsPerMinute: Math.round((this.stats.totalEvents / (uptime / 1000)) * 60),
        uptime: `${Math.round(uptime / 1000)}s`
      },
      topEventTypes: eventTypesArray.map(([event, count]) => ({
        event,
        count,
        percentage: ((count / this.stats.totalEvents) * 100).toFixed(1) + '%'
      })),
      listeners: {
        totalListeners: this.eventNames().reduce((total, event) => 
          total + this.listenerCount(event), 0),
        uniqueEvents: this.eventNames().length
      }
    }
  }

  // Health check for event bus
  healthCheck() {
    const stats = this.getStats()
    const errorRate = this.stats.totalEvents > 0 ? 
      (this.stats.errorCount / this.stats.totalEvents * 100) : 0

    let status = 'healthy'
    if (errorRate > 10) {
      status = 'unhealthy'
    } else if (errorRate > 5) {
      status = 'degraded'
    }

    return {
      status,
      metrics: {
        totalEvents: this.stats.totalEvents,
        errorRate: errorRate.toFixed(1) + '%',
        activeListeners: this.eventNames().length
      }
    }
  }

  // Clear all listeners (for testing)
  clearAllListeners() {
    const eventNames = this.eventNames()
    eventNames.forEach(event => {
      this.removeAllListeners(event)
    })
    
    logger.info('🧹 All event listeners cleared')
    return eventNames.length
  }
}

// Export singleton instance
export const eventBus = new ConvertFlowEventBus()
export default eventBus