import { logger } from '../utils/logger.js'
import { cache } from '../utils/cache.js'

class AIBatchProcessor {
  constructor() {
    this.queue = []
    this.processing = false
    this.batchSize = 3 // Process up to 3 requests simultaneously
    this.batchTimeout = 50 // ms - Wait 50ms to batch requests
    this.stats = {
      totalRequests: 0,
      batchedRequests: 0,
      averageBatchSize: 0,
      totalProcessingTime: 0,
      cacheHits: 0
    }
    
    logger.info('🚀 AI Batch Processor initialized', {
      batchSize: this.batchSize,
      batchTimeout: `${this.batchTimeout}ms`
    })
  }

  async processRequest(prompt, businessInfo, aiService) {
    return new Promise((resolve, reject) => {
      const request = {
        prompt,
        businessInfo,
        aiService,
        resolve,
        reject,
        timestamp: Date.now(),
        id: Math.random().toString(36).substring(7)
      }

      this.queue.push(request)
      this.stats.totalRequests++

      logger.debug('📥 Request queued for batching', {
        requestId: request.id,
        queueSize: this.queue.length
      })

      // Start processing if not already running
      if (!this.processing) {
        this.scheduleProcessing()
      }
    })
  }

  scheduleProcessing() {
    this.processing = true
    
    // Wait for batch timeout to collect more requests
    setTimeout(() => {
      this.processBatch()
    }, this.batchTimeout)
  }

  async processBatch() {
    if (this.queue.length === 0) {
      this.processing = false
      return
    }

    // Take up to batchSize requests from queue
    const batch = this.queue.splice(0, this.batchSize)
    const batchStartTime = Date.now()
    
    logger.info('🔄 Processing AI batch', {
      batchSize: batch.length,
      queueRemaining: this.queue.length
    })

    // Update batch statistics
    this.stats.batchedRequests += batch.length
    this.stats.averageBatchSize = this.stats.batchedRequests / 
      Math.ceil(this.stats.totalRequests / this.batchSize)

    // Process requests in parallel
    const results = await Promise.allSettled(
      batch.map(request => this.processIndividualRequest(request))
    )

    // Resolve/reject original promises
    results.forEach((result, index) => {
      const request = batch[index]
      
      if (result.status === 'fulfilled') {
        request.resolve(result.value)
      } else {
        request.reject(result.reason)
      }
    })

    const batchEndTime = Date.now()
    const batchDuration = batchEndTime - batchStartTime
    this.stats.totalProcessingTime += batchDuration

    logger.info('✅ AI batch completed', {
      batchSize: batch.length,
      duration: `${batchDuration}ms`,
      avgTimePerRequest: `${Math.round(batchDuration / batch.length)}ms`
    })

    // Continue processing if more requests are queued
    if (this.queue.length > 0) {
      this.scheduleProcessing()
    } else {
      this.processing = false
    }
  }

  async processIndividualRequest(request) {
    const { prompt, businessInfo, aiService, id } = request
    const requestStartTime = Date.now()

    try {
      // Check cache first
      const cacheKey = cache.generateCacheKey(prompt, businessInfo)
      const cachedResult = cache.get(cacheKey, { 
        type: 'ai', 
        similarity: true 
      })

      if (cachedResult && !cachedResult.warmed) {
        this.stats.cacheHits++
        logger.debug('🚀 Cache hit in batch processing', {
          requestId: id,
          cacheKey: cacheKey.substring(0, 8)
        })
        return cachedResult
      }

      // Generate AI response
      logger.debug('🧠 Generating AI response in batch', {
        requestId: id,
        industry: businessInfo.industry
      })

      const result = await aiService.generateEmailSequence(businessInfo, {
        sequenceLength: 3,
        tone: 'professional',
        primaryGoal: 'convert'
      })

      // Cache the result
      cache.set(cacheKey, result, {
        type: 'ai',
        ttl: this.calculateCacheTTL(result)
      })

      const requestDuration = Date.now() - requestStartTime
      logger.debug('✅ Individual request completed', {
        requestId: id,
        duration: `${requestDuration}ms`
      })

      return result

    } catch (error) {
      const requestDuration = Date.now() - requestStartTime
      logger.error('❌ Individual request failed', {
        requestId: id,
        duration: `${requestDuration}ms`,
        error: error.message
      })
      throw error
    }
  }

  calculateCacheTTL(result) {
    if (result.aiAnalysis && result.aiAnalysis.overallScore) {
      const score = result.aiAnalysis.overallScore
      if (score >= 90) {
        return 1000 * 60 * 60 // 1 hour for high quality
      } else if (score >= 70) {
        return 1000 * 60 * 30 // 30 minutes for good quality
      } else {
        return 1000 * 60 * 10 // 10 minutes for lower quality
      }
    }
    return 1000 * 60 * 30 // Default 30 minutes
  }

  // Optimize batch processing based on system load
  optimizeBatchSettings() {
    const avgProcessingTime = this.stats.totalProcessingTime / this.stats.batchedRequests
    const cacheHitRate = (this.stats.cacheHits / this.stats.totalRequests) * 100

    // Adjust batch size based on performance
    if (avgProcessingTime > 2000) { // If processing is slow
      this.batchSize = Math.max(2, this.batchSize - 1)
      logger.info('📉 Reduced batch size due to slow processing', {
        newBatchSize: this.batchSize,
        avgProcessingTime: `${Math.round(avgProcessingTime)}ms`
      })
    } else if (avgProcessingTime < 500 && cacheHitRate < 50) {
      this.batchSize = Math.min(5, this.batchSize + 1)
      logger.info('📈 Increased batch size due to fast processing', {
        newBatchSize: this.batchSize,
        avgProcessingTime: `${Math.round(avgProcessingTime)}ms`
      })
    }

    // Adjust timeout based on queue behavior
    if (this.queue.length > 10) {
      this.batchTimeout = Math.max(25, this.batchTimeout - 10)
    } else if (this.queue.length < 2) {
      this.batchTimeout = Math.min(100, this.batchTimeout + 10)
    }
  }

  // Get current statistics
  getStats() {
    const cacheHitRate = this.stats.totalRequests > 0 ? 
      (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(1) : 0

    const avgBatchProcessingTime = this.stats.batchedRequests > 0 ? 
      Math.round(this.stats.totalProcessingTime / (this.stats.batchedRequests / this.stats.averageBatchSize)) : 0

    return {
      performance: {
        totalRequests: this.stats.totalRequests,
        batchedRequests: this.stats.batchedRequests,
        averageBatchSize: this.stats.averageBatchSize.toFixed(1),
        cacheHitRate: cacheHitRate + '%',
        avgBatchProcessingTime: `${avgBatchProcessingTime}ms`
      },
      configuration: {
        batchSize: this.batchSize,
        batchTimeout: `${this.batchTimeout}ms`,
        currentQueueSize: this.queue.length,
        processing: this.processing
      }
    }
  }

  // Health check for batch processor
  healthCheck() {
    const queueSize = this.queue.length
    const avgProcessingTime = this.stats.totalProcessingTime / this.stats.batchedRequests

    let status = 'healthy'
    if (queueSize > 20 || avgProcessingTime > 5000) {
      status = 'unhealthy'
    } else if (queueSize > 10 || avgProcessingTime > 2000) {
      status = 'degraded'
    }

    return {
      status,
      metrics: {
        queueSize,
        avgProcessingTime: `${Math.round(avgProcessingTime || 0)}ms`,
        batchSize: this.batchSize,
        processing: this.processing
      }
    }
  }

  // Clear queue (for emergency situations)
  clearQueue() {
    const clearedCount = this.queue.length
    this.queue.forEach(request => {
      request.reject(new Error('Queue cleared by administrator'))
    })
    this.queue = []
    
    logger.warn('🧹 AI batch queue cleared', {
      clearedRequests: clearedCount
    })
    
    return clearedCount
  }
}

// Export singleton instance
export const aiBatchProcessor = new AIBatchProcessor()
export default aiBatchProcessor