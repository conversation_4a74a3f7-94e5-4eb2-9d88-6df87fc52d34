import PDFDocument from 'pdfkit'
import { Parser } from 'json2csv'
import { logger } from '../utils/logger.js'
import fs from 'fs/promises'
import path from 'path'

class ExportService {
  constructor() {
    this.exportPath = process.env.EXPORT_PATH || './exports'
    this.ensureExportDirectory()
  }

  async ensureExportDirectory() {
    try {
      await fs.mkdir(this.exportPath, { recursive: true })
    } catch (error) {
      logger.error('Failed to create export directory:', error)
    }
  }

  /**
   * Export email sequence to PDF
   */
  async exportSequenceToPDF(sequence, options = {}) {
    try {
      const filename = `sequence_${sequence._id}_${Date.now()}.pdf`
      const filepath = path.join(this.exportPath, filename)
      
      // Create PDF document
      const doc = new PDFDocument({
        size: 'A4',
        margins: {
          top: 50,
          bottom: 50,
          left: 50,
          right: 50
        }
      })

      // Pipe to file
      const stream = doc.pipe(await fs.open(filepath, 'w').then(f => f.createWriteStream()))

      // Add header
      doc.fontSize(24)
         .font('Helvetica-Bold')
         .text(sequence.name, { align: 'center' })
         .moveDown()

      // Add metadata
      doc.fontSize(10)
         .font('Helvetica')
         .fillColor('#666666')
         .text(`Generated: ${new Date().toLocaleDateString()}`, { align: 'right' })
         .text(`Total Emails: ${sequence.emails.length}`, { align: 'right' })
         .moveDown()

      // Add business info if available
      if (sequence.businessInfo) {
        doc.fontSize(14)
           .font('Helvetica-Bold')
           .fillColor('#000000')
           .text('Business Information')
           .moveDown(0.5)
           
        doc.fontSize(11)
           .font('Helvetica')
           .text(`Business: ${sequence.businessInfo.businessName || 'N/A'}`)
           .text(`Industry: ${sequence.businessInfo.industry || 'N/A'}`)
           .text(`Target Audience: ${sequence.businessInfo.targetAudience || 'N/A'}`)
           .moveDown()
      }

      // Add sequence settings
      if (sequence.settings) {
        doc.fontSize(14)
           .font('Helvetica-Bold')
           .text('Sequence Settings')
           .moveDown(0.5)
           
        doc.fontSize(11)
           .font('Helvetica')
           .text(`Type: ${sequence.settings.sequenceType || 'N/A'}`)
           .text(`Tone: ${sequence.settings.tone || 'N/A'}`)
           .text(`Length: ${sequence.settings.emailLength || 'N/A'}`)
           .moveDown(2)
      }

      // Add emails
      doc.fontSize(16)
         .font('Helvetica-Bold')
         .text('Email Sequence')
         .moveDown()

      sequence.emails.forEach((email, index) => {
        // Check if we need a new page
        if (doc.y > 700) {
          doc.addPage()
        }

        // Email header
        doc.fontSize(14)
           .font('Helvetica-Bold')
           .fillColor('#2563eb')
           .text(`Email ${index + 1}: ${email.title || 'Untitled'}`)
           .moveDown(0.5)

        // Email details
        doc.fontSize(11)
           .font('Helvetica')
           .fillColor('#000000')

        // Subject line
        doc.font('Helvetica-Bold')
           .text('Subject: ', { continued: true })
           .font('Helvetica')
           .text(email.subject || 'No subject')
           .moveDown(0.3)

        // Preview text
        if (email.preview) {
          doc.font('Helvetica-Bold')
             .text('Preview: ', { continued: true })
             .font('Helvetica')
             .text(email.preview)
             .moveDown(0.3)
        }

        // Delay
        if (index > 0 && email.delay !== undefined) {
          doc.font('Helvetica-Bold')
             .text('Send After: ', { continued: true })
             .font('Helvetica')
             .text(`${email.delay} days`)
             .moveDown(0.3)
        }

        // Body
        doc.font('Helvetica-Bold')
           .text('Body:')
           .font('Helvetica')
           .moveDown(0.3)

        // Format body text with proper line breaks
        const bodyLines = email.body?.split('\n') || []
        bodyLines.forEach(line => {
          if (line.trim()) {
            doc.text(line, {
              align: 'justify',
              indent: 20
            })
          } else {
            doc.moveDown(0.5)
          }
        })

        // CTA
        if (email.cta) {
          doc.moveDown(0.5)
             .font('Helvetica-Bold')
             .text('Call to Action: ', { continued: true })
             .font('Helvetica')
             .text(email.cta)
        }

        // Separator
        doc.moveDown(2)
           .strokeColor('#e5e5e5')
           .moveTo(50, doc.y)
           .lineTo(545, doc.y)
           .stroke()
           .moveDown(2)
      })

      // Add footer
      const pageCount = doc.bufferedPageRange().count
      for (let i = 0; i < pageCount; i++) {
        doc.switchToPage(i)
        doc.fontSize(9)
           .fillColor('#999999')
           .text(
             `Page ${i + 1} of ${pageCount}`,
             50,
             doc.page.height - 30,
             { align: 'center' }
           )
      }

      // Finalize PDF
      doc.end()
      
      await new Promise((resolve) => stream.on('finish', resolve))

      logger.info(`PDF exported: ${filename}`)

      return {
        filename,
        filepath,
        size: (await fs.stat(filepath)).size
      }

    } catch (error) {
      logger.error('PDF export error:', error)
      throw new Error(`Failed to export PDF: ${error.message}`)
    }
  }

  /**
   * Export email sequence to CSV
   */
  async exportSequenceToCSV(sequence, options = {}) {
    try {
      const filename = `sequence_${sequence._id}_${Date.now()}.csv`
      const filepath = path.join(this.exportPath, filename)

      // Prepare data for CSV
      const csvData = sequence.emails.map((email, index) => ({
        'Email Number': index + 1,
        'Title': email.title || '',
        'Subject Line': email.subject || '',
        'Preview Text': email.preview || '',
        'Send Delay (Days)': index === 0 ? 0 : (email.delay || 0),
        'Body': email.body || '',
        'Call to Action': email.cta || '',
        'Notes': email.notes || ''
      }))

      // Define CSV fields
      const fields = [
        'Email Number',
        'Title',
        'Subject Line',
        'Preview Text',
        'Send Delay (Days)',
        'Body',
        'Call to Action',
        'Notes'
      ]

      // Create CSV
      const parser = new Parser({ fields })
      const csv = parser.parse(csvData)

      // Write to file
      await fs.writeFile(filepath, csv, 'utf8')

      logger.info(`CSV exported: ${filename}`)

      return {
        filename,
        filepath,
        size: (await fs.stat(filepath)).size
      }

    } catch (error) {
      logger.error('CSV export error:', error)
      throw new Error(`Failed to export CSV: ${error.message}`)
    }
  }

  /**
   * Export email sequence to JSON
   */
  async exportSequenceToJSON(sequence, options = {}) {
    try {
      const filename = `sequence_${sequence._id}_${Date.now()}.json`
      const filepath = path.join(this.exportPath, filename)

      // Prepare export data
      const exportData = {
        metadata: {
          exportedAt: new Date(),
          version: '1.0',
          platform: 'NeuroColony'
        },
        sequence: {
          name: sequence.name,
          description: sequence.description,
          businessInfo: sequence.businessInfo,
          settings: sequence.settings,
          emails: sequence.emails.map((email, index) => ({
            position: index + 1,
            ...email
          }))
        }
      }

      // Write to file
      await fs.writeFile(
        filepath, 
        JSON.stringify(exportData, null, 2), 
        'utf8'
      )

      logger.info(`JSON exported: ${filename}`)

      return {
        filename,
        filepath,
        size: (await fs.stat(filepath)).size
      }

    } catch (error) {
      logger.error('JSON export error:', error)
      throw new Error(`Failed to export JSON: ${error.message}`)
    }
  }

  /**
   * Export email sequence to Markdown
   */
  async exportSequenceToMarkdown(sequence, options = {}) {
    try {
      const filename = `sequence_${sequence._id}_${Date.now()}.md`
      const filepath = path.join(this.exportPath, filename)

      let markdown = `# ${sequence.name}\n\n`
      
      if (sequence.description) {
        markdown += `${sequence.description}\n\n`
      }

      markdown += `**Generated:** ${new Date().toLocaleDateString()}\n`
      markdown += `**Total Emails:** ${sequence.emails.length}\n\n`

      // Business info
      if (sequence.businessInfo) {
        markdown += `## Business Information\n\n`
        markdown += `- **Business:** ${sequence.businessInfo.businessName || 'N/A'}\n`
        markdown += `- **Industry:** ${sequence.businessInfo.industry || 'N/A'}\n`
        markdown += `- **Target Audience:** ${sequence.businessInfo.targetAudience || 'N/A'}\n\n`
      }

      // Emails
      markdown += `## Email Sequence\n\n`

      sequence.emails.forEach((email, index) => {
        markdown += `### Email ${index + 1}: ${email.title || 'Untitled'}\n\n`
        
        markdown += `**Subject:** ${email.subject || 'No subject'}\n\n`
        
        if (email.preview) {
          markdown += `**Preview:** ${email.preview}\n\n`
        }
        
        if (index > 0 && email.delay !== undefined) {
          markdown += `**Send After:** ${email.delay} days\n\n`
        }
        
        markdown += `**Body:**\n\n${email.body || 'No content'}\n\n`
        
        if (email.cta) {
          markdown += `**Call to Action:** ${email.cta}\n\n`
        }
        
        markdown += `---\n\n`
      })

      // Write to file
      await fs.writeFile(filepath, markdown, 'utf8')

      logger.info(`Markdown exported: ${filename}`)

      return {
        filename,
        filepath,
        size: (await fs.stat(filepath)).size
      }

    } catch (error) {
      logger.error('Markdown export error:', error)
      throw new Error(`Failed to export Markdown: ${error.message}`)
    }
  }

  /**
   * Export to integration format (Mailchimp, ConvertKit, etc.)
   */
  async exportToIntegrationFormat(sequence, platform, options = {}) {
    try {
      switch (platform.toLowerCase()) {
        case 'mailchimp':
          return this.exportToMailchimpFormat(sequence, options)
        
        case 'convertkit':
          return this.exportToConvertKitFormat(sequence, options)
        
        case 'activecampaign':
          return this.exportToActiveCampaignFormat(sequence, options)
        
        default:
          throw new Error(`Unsupported platform: ${platform}`)
      }
    } catch (error) {
      logger.error(`Integration export error (${platform}):`, error)
      throw error
    }
  }

  /**
   * Export to Mailchimp format
   */
  async exportToMailchimpFormat(sequence, options = {}) {
    const campaignData = {
      campaigns: sequence.emails.map((email, index) => ({
        settings: {
          subject_line: email.subject,
          preview_text: email.preview || '',
          title: email.title || `Email ${index + 1}`,
          from_name: options.fromName || 'Your Company',
          reply_to: options.replyTo || '<EMAIL>'
        },
        content: {
          html: this.emailToHTML(email),
          plain_text: email.body
        },
        send_time: index === 0 ? 'immediate' : `+${email.delay}d`
      }))
    }

    const filename = `mailchimp_${sequence._id}_${Date.now()}.json`
    const filepath = path.join(this.exportPath, filename)
    
    await fs.writeFile(filepath, JSON.stringify(campaignData, null, 2), 'utf8')

    return { filename, filepath, format: 'mailchimp' }
  }

  /**
   * Export to ConvertKit format
   */
  async exportToConvertKitFormat(sequence, options = {}) {
    const sequenceData = {
      name: sequence.name,
      emails: sequence.emails.map((email, index) => ({
        subject: email.subject,
        content: email.body,
        preview_text: email.preview || '',
        send_after: index === 0 ? 0 : (email.delay || index),
        published: true
      }))
    }

    const filename = `convertkit_${sequence._id}_${Date.now()}.json`
    const filepath = path.join(this.exportPath, filename)
    
    await fs.writeFile(filepath, JSON.stringify(sequenceData, null, 2), 'utf8')

    return { filename, filepath, format: 'convertkit' }
  }

  /**
   * Export to ActiveCampaign format
   */
  async exportToActiveCampaignFormat(sequence, options = {}) {
    const automationData = {
      automation: {
        name: sequence.name,
        emails: sequence.emails.map((email, index) => ({
          subject: email.subject,
          preheader: email.preview || '',
          content: this.emailToHTML(email),
          wait: index === 0 ? 0 : (email.delay || 1) * 24 // Convert days to hours
        }))
      }
    }

    const filename = `activecampaign_${sequence._id}_${Date.now()}.json`
    const filepath = path.join(this.exportPath, filename)
    
    await fs.writeFile(filepath, JSON.stringify(automationData, null, 2), 'utf8')

    return { filename, filepath, format: 'activecampaign' }
  }

  /**
   * Convert email to HTML format
   */
  emailToHTML(email) {
    const paragraphs = email.body?.split('\n').filter(p => p.trim()) || []
    const ctaButton = email.cta ? 
      `<div style="text-align: center; margin: 30px 0;">
        <a href="#" style="display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">${email.cta}</a>
      </div>` : ''

    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${email.subject}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
  ${paragraphs.map(p => `<p style="margin: 16px 0;">${p}</p>`).join('')}
  ${ctaButton}
</body>
</html>`
  }

  /**
   * Clean up old export files
   */
  async cleanupOldExports(daysToKeep = 7) {
    try {
      const files = await fs.readdir(this.exportPath)
      const now = Date.now()
      const maxAge = daysToKeep * 24 * 60 * 60 * 1000

      for (const file of files) {
        const filepath = path.join(this.exportPath, file)
        const stats = await fs.stat(filepath)
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filepath)
          logger.info(`Cleaned up old export: ${file}`)
        }
      }
    } catch (error) {
      logger.error('Export cleanup error:', error)
    }
  }
}

export default new ExportService()