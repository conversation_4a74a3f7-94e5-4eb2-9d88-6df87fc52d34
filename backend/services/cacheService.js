/**
 * High-Performance Caching Service for NeuroColony
 * Implements multi-level caching with intelligent eviction
 */

import Redis from 'ioredis'
import crypto from 'crypto'
import { logger } from '../utils/logger.js'

class CacheService {
  constructor() {
    this.redis = null
    this.memoryCache = new Map()
    this.memoryCacheLimit = 1000
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      redisHits: 0
    }
    
    this.initializeRedis()
  }

  /**
   * Initialize Redis connection with optimized settings
   */
  async initializeRedis() {
    try {
      this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6380', {
        // Performance optimizations
        maxRetriesPerRequest: 3,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxLoadingTimeout: 1000,
        
        // Connection pool optimization
        lazyConnect: true,
        keepAlive: 30000,
        
        // Compression for large values
        compression: 'gzip'
      })

      this.redis.on('error', (err) => {
        logger.error('Redis connection error:', err)
      })

      this.redis.on('connect', () => {
        logger.info('✅ Cache service connected to Redis')
      })

      await this.redis.connect()
    } catch (error) {
      logger.warn('Redis unavailable, using memory cache only:', error.message)
    }
  }

  /**
   * Generate optimized cache key for AI responses
   */
  generateAICacheKey(businessInfo, settings, prefix = 'ai_seq') {
    // Create deterministic hash from business info and settings
    const normalizedData = {
      industry: businessInfo.industry?.toLowerCase().trim(),
      productService: businessInfo.productService?.toLowerCase().trim(),
      targetAudience: businessInfo.targetAudience?.toLowerCase().trim(),
      pricePoint: businessInfo.pricePoint?.toLowerCase().trim(),
      uniqueValue: businessInfo.uniqueValue?.toLowerCase().trim(),
      tone: settings.tone,
      primaryGoal: settings.primaryGoal,
      sequenceLength: settings.sequenceLength,
      template: settings.template
    }

    const dataString = JSON.stringify(normalizedData, Object.keys(normalizedData).sort())
    const hash = crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16)
    
    return `${prefix}:${hash}`
  }

  /**
   * Multi-level cache get with LRU memory cache
   */
  async get(key, options = {}) {
    const { useMemoryCache = true, deserialize = true } = options

    try {
      // Level 1: Memory cache (fastest)
      if (useMemoryCache && this.memoryCache.has(key)) {
        this.stats.hits++
        this.stats.memoryHits++
        
        // Move to end for LRU
        const value = this.memoryCache.get(key)
        this.memoryCache.delete(key)
        this.memoryCache.set(key, value)
        
        return deserialize ? JSON.parse(value.data) : value.data
      }

      // Level 2: Redis cache
      if (this.redis) {
        const value = await this.redis.get(key)
        if (value) {
          this.stats.hits++
          this.stats.redisHits++
          
          // Promote to memory cache
          if (useMemoryCache) {
            this.setMemoryCache(key, value)
          }
          
          return deserialize ? JSON.parse(value) : value
        }
      }

      // Cache miss
      this.stats.misses++
      return null

    } catch (error) {
      logger.error('Cache get error:', error)
      this.stats.misses++
      return null
    }
  }

  /**
   * Multi-level cache set with TTL
   */
  async set(key, value, ttl = 3600, options = {}) {
    const { useMemoryCache = true, serialize = true } = options
    
    try {
      const serializedValue = serialize ? JSON.stringify(value) : value

      // Set in Redis with TTL
      if (this.redis) {
        await this.redis.setex(key, ttl, serializedValue)
      }

      // Set in memory cache
      if (useMemoryCache) {
        this.setMemoryCache(key, serializedValue)
      }

      return true
    } catch (error) {
      logger.error('Cache set error:', error)
      return false
    }
  }

  /**
   * LRU memory cache implementation
   */
  setMemoryCache(key, value) {
    // Evict oldest if at limit
    if (this.memoryCache.size >= this.memoryCacheLimit) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }

    this.memoryCache.set(key, {
      data: value,
      timestamp: Date.now()
    })
  }

  /**
   * Cache AI sequence response with intelligent TTL
   */
  async cacheAISequence(businessInfo, settings, response) {
    const key = this.generateAICacheKey(businessInfo, settings)
    
    // Dynamic TTL based on sequence quality/complexity
    const baseTTL = 3600 // 1 hour
    const qualityMultiplier = response.aiAnalysis?.averageScore || 1
    const ttl = Math.floor(baseTTL * Math.min(qualityMultiplier, 2))
    
    const cacheData = {
      response,
      metadata: {
        businessInfo: {
          industry: businessInfo.industry,
          productService: businessInfo.productService
        },
        settings,
        generatedAt: new Date(),
        ttl
      }
    }

    await this.set(key, cacheData, ttl)
    logger.info(`🔥 Cached AI sequence: ${key} (TTL: ${ttl}s)`)
    
    return key
  }

  /**
   * Get cached AI sequence
   */
  async getCachedAISequence(businessInfo, settings) {
    const key = this.generateAICacheKey(businessInfo, settings)
    const cached = await this.get(key)
    
    if (cached) {
      logger.info(`⚡ Cache HIT for AI sequence: ${key}`)
      return {
        ...cached.response,
        _cached: true,
        _cacheKey: key,
        _cachedAt: cached.metadata.generatedAt
      }
    }
    
    logger.info(`💸 Cache MISS for AI sequence: ${key}`)
    return null
  }

  /**
   * Cache user data with smart invalidation
   */
  async cacheUser(userId, userData, ttl = 300) {
    const key = `user:${userId}`
    return this.set(key, userData, ttl)
  }

  /**
   * Get cached user data
   */
  async getCachedUser(userId) {
    const key = `user:${userId}`
    return this.get(key)
  }

  /**
   * Invalidate user cache (after updates)
   */
  async invalidateUser(userId) {
    const key = `user:${userId}`
    
    this.memoryCache.delete(key)
    
    if (this.redis) {
      await this.redis.del(key)
    }
  }

  /**
   * Cache dashboard statistics
   */
  async cacheDashboardStats(userId, stats, ttl = 600) {
    const key = `dashboard:${userId}`
    return this.set(key, stats, ttl)
  }

  /**
   * Get cached dashboard statistics
   */
  async getCachedDashboardStats(userId) {
    const key = `dashboard:${userId}`
    return this.get(key)
  }

  /**
   * Batch cache operations for better performance
   */
  async setMultiple(entries, ttl = 3600) {
    if (!this.redis) return false

    try {
      const pipeline = this.redis.pipeline()
      
      for (const [key, value] of entries) {
        const serializedValue = JSON.stringify(value)
        pipeline.setex(key, ttl, serializedValue)
      }
      
      await pipeline.exec()
      return true
    } catch (error) {
      logger.error('Batch cache set error:', error)
      return false
    }
  }

  /**
   * Get cache performance statistics
   */
  getStats() {
    const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    
    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryCacheSize: this.memoryCache.size,
      memoryCacheLimit: this.memoryCacheLimit
    }
  }

  /**
   * Clear all caches (for development/testing)
   */
  async clear() {
    this.memoryCache.clear()
    
    if (this.redis) {
      await this.redis.flushdb()
    }
    
    // Reset stats
    this.stats = { hits: 0, misses: 0, memoryHits: 0, redisHits: 0 }
  }

  /**
   * Cleanup expired memory cache entries
   */
  cleanupMemoryCache() {
    const now = Date.now()
    const maxAge = 600000 // 10 minutes
    
    for (const [key, value] of this.memoryCache.entries()) {
      if (now - value.timestamp > maxAge) {
        this.memoryCache.delete(key)
      }
    }
  }

  /**
   * Graceful shutdown
   */
  async disconnect() {
    if (this.redis) {
      await this.redis.disconnect()
    }
  }
}

// Singleton instance
const cacheService = new CacheService()

// Cleanup memory cache every 5 minutes
setInterval(() => {
  cacheService.cleanupMemoryCache()
}, 300000)

export default cacheService