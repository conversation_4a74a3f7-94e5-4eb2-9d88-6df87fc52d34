import nodemailer from 'nodemailer';

/**
 * Professional Email Service for Contact Forms
 * Sends emails to support team without exposing email addresses
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.initialize();
  }

  initialize() {
    // Initialize email transporter
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Send support ticket email
   */
  async sendSupportTicket(ticketData) {
    const {
      name,
      email,
      issueType,
      subject,
      message,
      userAgent,
      timestamp,
      userId
    } = ticketData;

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: 600; color: #6d28d9; }
          .value { background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #8b5cf6; }
          .message-box { background: white; padding: 15px; border-radius: 6px; border: 1px solid #e5e5e5; white-space: pre-wrap; }
          .meta { background: #e5e5e5; padding: 10px; border-radius: 4px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>🎯 NeuroColony Support Ticket</h2>
            <p>New ${issueType === 'other' ? 'General' : 'Help'} Request</p>
          </div>
          
          <div class="content">
            <div class="field">
              <div class="label">Customer Name:</div>
              <div class="value">${name}</div>
            </div>
            
            <div class="field">
              <div class="label">Email Address:</div>
              <div class="value">${email}</div>
            </div>
            
            <div class="field">
              <div class="label">Issue Type:</div>
              <div class="value">${this.formatIssueType(issueType)}</div>
            </div>
            
            <div class="field">
              <div class="label">Subject:</div>
              <div class="value">${subject}</div>
            </div>
            
            <div class="field">
              <div class="label">Message:</div>
              <div class="message-box">${message}</div>
            </div>
            
            <div class="meta">
              <strong>Technical Details:</strong><br>
              User ID: ${userId || 'Anonymous'}<br>
              Timestamp: ${timestamp}<br>
              User Agent: ${userAgent}<br>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Support" <${process.env.SMTP_USER}>`,
        to: '<EMAIL>',  // Hidden support email
        subject: `[NeuroColony] ${issueType === 'other' ? 'Contact' : 'Support'}: ${subject}`,
        html: emailContent,
        replyTo: email  // Allow direct reply to customer
      });

      console.log('Support ticket sent:', result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending support ticket:', error);
      throw new Error('Failed to send support ticket');
    }
  }

  /**
   * Send contact form email
   */
  async sendContactForm(contactData) {
    const {
      name,
      email,
      company,
      message,
      contactReason,
      timestamp,
      userAgent
    } = contactData;

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: 600; color: #d97706; }
          .value { background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #f59e0b; }
          .message-box { background: white; padding: 15px; border-radius: 6px; border: 1px solid #e5e5e5; white-space: pre-wrap; }
          .meta { background: #e5e5e5; padding: 10px; border-radius: 4px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>💼 NeuroColony Contact Form</h2>
            <p>New Business Inquiry</p>
          </div>
          
          <div class="content">
            <div class="field">
              <div class="label">Contact Name:</div>
              <div class="value">${name}</div>
            </div>
            
            <div class="field">
              <div class="label">Email Address:</div>
              <div class="value">${email}</div>
            </div>
            
            ${company ? `
            <div class="field">
              <div class="label">Company:</div>
              <div class="value">${company}</div>
            </div>
            ` : ''}
            
            <div class="field">
              <div class="label">Contact Reason:</div>
              <div class="value">${this.formatContactReason(contactReason)}</div>
            </div>
            
            <div class="field">
              <div class="label">Message:</div>
              <div class="message-box">${message}</div>
            </div>
            
            <div class="meta">
              <strong>Contact Details:</strong><br>
              Timestamp: ${timestamp}<br>
              User Agent: ${userAgent}<br>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Contact" <${process.env.SMTP_USER}>`,
        to: '<EMAIL>',  // Hidden contact email
        subject: `[NeuroColony] Contact: ${this.formatContactReason(contactReason)}`,
        html: emailContent,
        replyTo: email  // Allow direct reply to contact
      });

      console.log('Contact form sent:', result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending contact form:', error);
      throw new Error('Failed to send contact form');
    }
  }

  /**
   * Send auto-reply to customer
   */
  async sendAutoReply(customerEmail, customerName, issueType) {
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .logo { font-size: 24px; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <p>Thank you for contacting us!</p>
          </div>
          
          <div class="content">
            <h3>Hi ${customerName},</h3>
            
            <p>Thank you for reaching out to NeuroColony. We've received your ${issueType === 'other' ? 'message' : 'support request'} and will get back to you as soon as possible.</p>
            
            <p><strong>What happens next?</strong></p>
            <ul>
              <li>We'll review your ${issueType === 'other' ? 'inquiry' : 'issue'} within 24 hours</li>
              <li>Our team will respond with a solution or follow-up questions</li>
              <li>For urgent matters, we prioritize based on your subscription plan</li>
            </ul>
            
            <p>In the meantime, you might find these resources helpful:</p>
            <ul>
              <li><a href="${process.env.FRONTEND_URL}/help">Help Center</a> - Common questions and guides</li>
              <li><a href="${process.env.FRONTEND_URL}/dashboard">Dashboard</a> - Manage your sequences</li>
              <li><a href="${process.env.FRONTEND_URL}/pricing">Pricing</a> - Upgrade for priority support</li>
            </ul>
            
            <p>Best regards,<br>
            <strong>The NeuroColony Team</strong></p>
          </div>
          
          <div class="footer">
            <p>This is an automated response. Please don't reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      await this.transporter.sendMail({
        from: `"NeuroColony" <${process.env.SMTP_USER}>`,
        to: customerEmail,
        subject: 'Thank you for contacting NeuroColony',
        html: emailContent
      });

      console.log('Auto-reply sent to:', customerEmail);
    } catch (error) {
      console.error('Error sending auto-reply:', error);
      // Don't throw error for auto-reply failures
    }
  }

  // Helper methods
  formatIssueType(type) {
    const types = {
      'billing': '💳 Billing & Payments',
      'technical': '🔧 Technical Issues',
      'account': '👤 Account Management',
      'features': '✨ Feature Requests',
      'bug': '🐛 Bug Reports',
      'integration': '🔗 Integrations',
      'performance': '⚡ Performance Issues',
      'other': '💬 General Inquiry'
    };
    return types[type] || '💬 General Inquiry';
  }

  formatContactReason(reason) {
    const reasons = {
      'sales': '💼 Sales Inquiry',
      'partnership': '🤝 Partnership Opportunity',
      'enterprise': '🏢 Enterprise Solutions',
      'feedback': '📝 Product Feedback',
      'media': '📰 Media & Press',
      'other': '💬 General Contact'
    };
    return reasons[reason] || '💬 General Contact';
  }

  /**
   * Send payment failure notification to customer
   */
  async sendPaymentFailureNotification(user, invoice) {
    const { email, name, subscriptionPlan } = user;
    const amount = (invoice.amount_due / 100).toFixed(2);
    const attemptCount = invoice.attempt_count || 1;
    const nextAttempt = invoice.next_payment_attempt ? new Date(invoice.next_payment_attempt * 1000) : null;

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .alert-box { background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 15px; margin: 15px 0; }
          .action-button { display: inline-block; background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .logo { font-size: 24px; font-weight: bold; }
          .amount { font-size: 20px; font-weight: bold; color: #ef4444; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <p>🚨 Payment Issue - Action Required</p>
          </div>
          
          <div class="content">
            <h3>Hi ${name},</h3>
            
            <div class="alert-box">
              <strong>⚠️ Payment Failed</strong><br>
              We were unable to process your subscription payment for NeuroColony.
            </div>
            
            <p><strong>Payment Details:</strong></p>
            <ul>
              <li>Plan: ${subscriptionPlan?.toUpperCase() || 'Pro'} Subscription</li>
              <li>Amount: <span class="amount">$${amount}</span></li>
              <li>Attempt: ${attemptCount} of 4</li>
              ${nextAttempt ? `<li>Next Attempt: ${nextAttempt.toLocaleDateString()}</li>` : ''}
            </ul>
            
            <p><strong>What you need to do:</strong></p>
            <ol>
              <li>Update your payment method in your account settings</li>
              <li>Ensure your card has sufficient funds</li>
              <li>Check with your bank if the payment was declined</li>
            </ol>
            
            <div style="text-align: center; margin: 20px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard/billing" class="action-button">
                Update Payment Method
              </a>
            </div>
            
            <p><strong>What happens if payment fails?</strong></p>
            <ul>
              <li>We'll attempt to charge your card 4 times over 2 weeks</li>
              <li>If all attempts fail, your subscription will be cancelled</li>
              <li>You'll lose access to premium features</li>
              <li>Your data will be preserved for 30 days</li>
            </ul>
            
            <p>Need help? Reply to this email or contact our support team.</p>
            
            <p>Best regards,<br>
            <strong>The NeuroColony Team</strong></p>
          </div>
          
          <div class="footer">
            <p>You're receiving this because your NeuroColony subscription payment failed.</p>
            <p><a href="${process.env.FRONTEND_URL}/dashboard/billing">Manage Billing</a> | <a href="${process.env.FRONTEND_URL}/support">Contact Support</a></p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Billing" <${process.env.SMTP_USER}>`,
        to: email,
        subject: `🚨 Action Required: Payment Failed for NeuroColony (Attempt ${attemptCount})`,
        html: emailContent
      });

      console.log(`Payment failure notification sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending payment failure notification:', error);
      throw new Error('Failed to send payment failure notification');
    }
  }

  /**
   * Send subscription cancelled notification to customer
   */
  async sendSubscriptionCancelledNotification(user) {
    const { email, name, subscriptionPlan } = user;

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 15px; margin: 15px 0; }
          .action-button { display: inline-block; background: #8b5cf6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .logo { font-size: 24px; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <p>Subscription Cancelled</p>
          </div>
          
          <div class="content">
            <h3>Hi ${name},</h3>
            
            <div class="info-box">
              <strong>📋 Subscription Status Update</strong><br>
              Your ${subscriptionPlan?.toUpperCase() || 'Pro'} subscription has been cancelled due to payment failure.
            </div>
            
            <p><strong>What this means:</strong></p>
            <ul>
              <li>Your account has been moved to the Free plan</li>
              <li>You can still access your existing sequences</li>
              <li>You're limited to 5 new sequences per month</li>
              <li>Premium features are no longer available</li>
            </ul>
            
            <p><strong>Your data is safe:</strong></p>
            <ul>
              <li>All your sequences and data are preserved</li>
              <li>You can export your data anytime</li>
              <li>Reactivation restores full access instantly</li>
            </ul>
            
            <div style="text-align: center; margin: 20px 0;">
              <a href="${process.env.FRONTEND_URL}/pricing" class="action-button">
                Reactivate Subscription
              </a>
            </div>
            
            <p>We'd love to have you back! If you have any questions or need help resolving payment issues, please don't hesitate to contact us.</p>
            
            <p>Thank you for using NeuroColony.</p>
            
            <p>Best regards,<br>
            <strong>The NeuroColony Team</strong></p>
          </div>
          
          <div class="footer">
            <p>You can reactivate your subscription anytime.</p>
            <p><a href="${process.env.FRONTEND_URL}/pricing">View Plans</a> | <a href="${process.env.FRONTEND_URL}/support">Contact Support</a></p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony" <${process.env.SMTP_USER}>`,
        to: email,
        subject: 'Your NeuroColony subscription has been cancelled',
        html: emailContent
      });

      console.log(`Subscription cancelled notification sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending subscription cancelled notification:', error);
      throw new Error('Failed to send subscription cancelled notification');
    }
  }

  /**
   * Send welcome email with email verification
   */
  async sendWelcomeEmail(email, name, verificationToken) {
    const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
    
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
          .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .tagline { color: #e2e8f0; font-size: 16px; }
          .content { padding: 40px 20px; }
          .welcome-text { font-size: 18px; color: #2d3748; margin-bottom: 20px; }
          .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; margin: 20px 0; }
          .features { margin: 30px 0; }
          .feature { margin: 15px 0; padding: 15px; background-color: #f7fafc; border-radius: 8px; }
          .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 30px 20px; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <div class="tagline">Business Intelligence Platform</div>
          </div>
          
          <div class="content">
            <h1 style="color: #2d3748; font-size: 24px;">Welcome to NeuroColony, ${name}!</h1>
            
            <p class="welcome-text">
              Thank you for joining NeuroColony! You're now part of an innovative platform that transforms how businesses approach AI-driven sequence generation and optimization.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationLink}" class="cta-button">Verify Your Email Address</a>
            </div>
            
            <div class="features">
              <h3 style="color: #2d3748;">What you can do with NeuroColony:</h3>
              
              <div class="feature">
                <strong>🧠 AI-Powered Sequences:</strong> Generate intelligent sequences with advanced AI algorithms
              </div>
              
              <div class="feature">
                <strong>📊 Analytics Dashboard:</strong> Track performance and optimize your sequences
              </div>
              
              <div class="feature">
                <strong>🔗 Integrations:</strong> Connect with your favorite tools and platforms
              </div>
              
              <div class="feature">
                <strong>👥 Team Collaboration:</strong> Work together with your team seamlessly
              </div>
            </div>
            
            <p style="color: #4a5568; margin-top: 30px;">
              If you have any questions, feel free to reach out to our support team. We're here to help you succeed!
            </p>
          </div>
          
          <div class="footer">
            <p>© 2025 NeuroColony. All rights reserved.</p>
            <p>This email was sent to ${email}. If you didn't create an account, please ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony" <${process.env.SMTP_USER}>`,
        to: email,
        subject: 'Welcome to NeuroColony - Verify Your Email',
        html: emailContent
      });

      console.log(`Welcome email sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending welcome email:', error);
      throw new Error('Failed to send welcome email');
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email, name, resetToken) {
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
          .header { background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); padding: 40px 20px; text-align: center; }
          .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 40px 20px; }
          .alert { background-color: #fed7d7; border: 1px solid #feb2b2; color: #c53030; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .cta-button { display: inline-block; background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; margin: 20px 0; }
          .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 30px 20px; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <div style="color: #fed7d7;">Password Reset Request</div>
          </div>
          
          <div class="content">
            <h1 style="color: #2d3748; font-size: 24px;">Reset Your Password</h1>
            
            <p style="color: #4a5568; font-size: 16px;">
              Hi ${name},
            </p>
            
            <p style="color: #4a5568; font-size: 16px;">
              We received a request to reset your password for your NeuroColony account. Click the button below to create a new password:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" class="cta-button">Reset My Password</a>
            </div>
            
            <div class="alert">
              <strong>⚠️ Security Notice:</strong> This link will expire in 1 hour for your security. If you didn't request this password reset, please ignore this email.
            </div>
            
            <p style="color: #4a5568; font-size: 14px; margin-top: 30px;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <span style="word-break: break-all; color: #667eea;">${resetLink}</span>
            </p>
          </div>
          
          <div class="footer">
            <p>© 2025 NeuroColony. All rights reserved.</p>
            <p>This email was sent to ${email}. If you didn't request this reset, please secure your account.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Security" <${process.env.SMTP_USER}>`,
        to: email,
        subject: 'Reset Your NeuroColony Password',
        html: emailContent
      });

      console.log(`Password reset email sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  /**
   * Send PayPal payment success notification
   */
  async sendPayPalPaymentSuccess(email, name, plan, amount) {
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
          .header { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); padding: 40px 20px; text-align: center; }
          .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 40px 20px; }
          .success-icon { font-size: 48px; color: #48bb78; text-align: center; margin: 20px 0; }
          .payment-details { background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .action-button { display: inline-block; background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; margin: 20px 0; }
          .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 30px 20px; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <div style="color: #c6f6d5;">PayPal Payment Successful</div>
          </div>
          
          <div class="content">
            <div class="success-icon">✅</div>
            
            <h1 style="color: #2d3748; font-size: 24px; text-align: center;">Payment Successful!</h1>
            
            <p style="color: #4a5568; font-size: 16px;">
              Hi ${name},
            </p>
            
            <p style="color: #4a5568; font-size: 16px;">
              Thank you for your PayPal payment! Your subscription has been activated and you now have access to all ${plan} features.
            </p>
            
            <div class="payment-details">
              <h3 style="color: #2d3748; margin-top: 0;">Payment Details</h3>
              <p><strong>Plan:</strong> ${plan}</p>
              <p><strong>Amount:</strong> $${amount}</p>
              <p><strong>Payment Method:</strong> PayPal</p>
              <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard" class="action-button">
                Access Dashboard
              </a>
            </div>
            
            <p style="color: #4a5568; margin-top: 30px;">
              Your PayPal subscription will automatically renew each month. You can manage your subscription anytime in your account settings.
            </p>
            
            <p style="color: #4a5568; margin-top: 30px;">
              If you have any questions about your subscription, please don't hesitate to contact our support team.
            </p>
          </div>
          
          <div class="footer">
            <p>© 2025 NeuroColony. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Billing" <${process.env.SMTP_USER}>`,
        to: email,
        subject: 'PayPal Payment Successful - Welcome to NeuroColony Pro!',
        html: emailContent
      });

      console.log(`PayPal payment success email sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending PayPal payment success email:', error);
      throw new Error('Failed to send PayPal payment success email');
    }
  }

  /**
   * Send Stripe payment success notification
   */
  async sendStripePaymentSuccess(email, name, plan, amount) {
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
          .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
          .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
          .content { padding: 40px 20px; }
          .success-icon { font-size: 48px; color: #667eea; text-align: center; margin: 20px 0; }
          .payment-details { background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .action-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; margin: 20px 0; }
          .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 30px 20px; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">NeuroColony</div>
            <div style="color: #e2e8f0;">Stripe Payment Successful</div>
          </div>
          
          <div class="content">
            <div class="success-icon">💳</div>
            
            <h1 style="color: #2d3748; font-size: 24px; text-align: center;">Payment Successful!</h1>
            
            <p style="color: #4a5568; font-size: 16px;">
              Hi ${name},
            </p>
            
            <p style="color: #4a5568; font-size: 16px;">
              Thank you for your payment! Your subscription has been activated and you now have access to all ${plan} features.
            </p>
            
            <div class="payment-details">
              <h3 style="color: #2d3748; margin-top: 0;">Payment Details</h3>
              <p><strong>Plan:</strong> ${plan}</p>
              <p><strong>Amount:</strong> $${amount}</p>
              <p><strong>Payment Method:</strong> Credit Card (Stripe)</p>
              <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard" class="action-button">
                Access Dashboard
              </a>
            </div>
            
            <p style="color: #4a5568; margin-top: 30px;">
              Your subscription will automatically renew each month. You can manage your subscription anytime in your account settings.
            </p>
            
            <p style="color: #4a5568; margin-top: 30px;">
              If you have any questions about your subscription, please don't hesitate to contact our support team.
            </p>
          </div>
          
          <div class="footer">
            <p>© 2025 NeuroColony. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    try {
      const result = await this.transporter.sendMail({
        from: `"NeuroColony Billing" <${process.env.SMTP_USER}>`,
        to: email,
        subject: 'Payment Successful - Welcome to NeuroColony Pro!',
        html: emailContent
      });

      console.log(`Stripe payment success email sent to ${email}:`, result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending Stripe payment success email:', error);
      throw new Error('Failed to send Stripe payment success email');
    }
  }
}

export default new EmailService();