import OpenAI from 'openai'
import { logger } from '../utils/logger.js'
import localAiService from './localAiService.js'
import claudeAIService from './claudeAIService.js'
import dotenv from 'dotenv'

dotenv.config()

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'sk-dummy-key'
})

class AIService {
  constructor() {
    this.initialized = false
  }

  async initialize() {
    try {
      // Disable local AI for now - use fallback templates
      this.useLocalAI = false
      this.initialized = true
      logger.info('AI Service initialized with fallback templates')
    } catch (error) {
      logger.error('AI Service initialization failed', error)
      this.useLocalAI = false
      this.initialized = false
    }
  }

  async generateEmailSequence(prompt, options = {}) {
    try {
      // Try Claude 4 first (highest quality)
      try {
        logger.info('🧠 Attempting Claude 4 generation...')
        const businessInfo = this.extractBusinessInfo(prompt, options)
        const settings = this.extractSettings(options)
        
        const claudeResult = await claudeAIService.generateEmailSequence(businessInfo, settings)
        logger.info('✅ Claude 4 generation successful!')
        
        return {
          success: true,
          emails: claudeResult.emails,
          aiAnalysis: claudeResult.aiAnalysis,
          provider: 'claude-4',
          quality: 'premium'
        }
      } catch (claudeError) {
        logger.warn('Claude 4 not available, trying local AI:', claudeError.message)
      }

      // Use local AI service second
      if (this.useLocalAI) {
        const result = await localAiService.generateEmailSequence(prompt, options)
        if (result.success) {
          return {
            ...result,
            provider: 'local-ai',
            quality: 'good'
          }
        }
      }

      // Fallback to simple template-based generation
      return {
        ...this.generateFallbackSequence(prompt, options),
        provider: 'fallback-templates',
        quality: 'basic'
      }
    } catch (error) {
      logger.error('AI generation failed', error)
      return {
        ...this.generateFallbackSequence(prompt, options),
        provider: 'error-fallback',
        quality: 'basic'
      }
    }
  }

  generateFallbackSequence(prompt, options = {}) {
    const { sequenceLength = 5, tone = 'professional', industry = 'general' } = options

    const sequences = [
      {
        subject: "Welcome to our community! 🚀",
        content: "Thank you for joining us. We're excited to help you achieve your goals with our proven strategies and personalized guidance.",
        callToAction: "Get started now"
      },
      {
        subject: "Your exclusive resources are ready",
        content: "We've prepared something special for you. Access our comprehensive guide that has helped thousands of people succeed.",
        callToAction: "Access your resources"
      },
      {
        subject: "Quick wins you can implement today",
        content: "Here are three simple strategies you can start using immediately to see results in your business or personal projects.",
        callToAction: "Start implementing"
      },
      {
        subject: "Success story: How [Name] achieved amazing results",
        content: "Learn from this inspiring case study of someone who transformed their approach and achieved remarkable success.",
        callToAction: "Read the story"
      },
      {
        subject: "Final opportunity to transform your approach",
        content: "This is your chance to take action and create the change you've been waiting for. Don't let this opportunity pass by.",
        callToAction: "Take action now"
      }
    ]

    return {
      success: true,
      sequence: sequences.slice(0, sequenceLength).map((email, index) => ({
        ...email,
        emailNumber: index + 1
      })),
      model: 'fallback-templates',
      metadata: {
        generatedAt: new Date(),
        prompt: prompt,
        options: options
      }
    }
  }

  // Helper method to extract business info for Claude
  extractBusinessInfo(prompt, options) {
    return {
      industry: options.industry || 'General Business',
      productService: this.extractFromPrompt(prompt, 'product|service|offering') || 'Professional Services',
      targetAudience: this.extractFromPrompt(prompt, 'audience|customer|client') || 'Business Professionals',
      pricePoint: this.extractFromPrompt(prompt, 'price|cost|investment') || '$97-$497',
      uniqueSellingProposition: this.extractFromPrompt(prompt, 'unique|special|different') || 'Results-driven approach',
      mainBenefit: this.extractFromPrompt(prompt, 'benefit|result|outcome') || 'Increased efficiency and growth',
      painPoint: this.extractFromPrompt(prompt, 'problem|challenge|struggle') || 'Lack of effective strategy'
    }
  }

  // Helper method to extract settings for Claude
  extractSettings(options) {
    return {
      sequenceLength: options.sequenceLength || 5,
      tone: options.tone || 'professional',
      primaryGoal: options.primaryGoal || 'sales',
      includeCTA: true,
      includePersonalization: true
    }
  }

  // Helper method to extract info from prompt text
  extractFromPrompt(prompt, keywords) {
    if (!prompt || typeof prompt !== 'string') return null
    
    const keywordArray = keywords.split('|')
    const sentences = prompt.split(/[.!?]+/)
    
    for (const sentence of sentences) {
      for (const keyword of keywordArray) {
        if (sentence.toLowerCase().includes(keyword)) {
          // Return the sentence or a cleaned version
          return sentence.trim().substring(0, 100)
        }
      }
    }
    
    return null
  }
}

const aiService = new AIService()

// Initialize on import (non-blocking)
if (process.env.NODE_ENV !== 'test') {
  aiService.initialize().catch(error => {
    logger.warn('AI Service using fallback mode', error.message)
  })
}

export default aiService