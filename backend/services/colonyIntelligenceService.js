import Agent, { AGENT_TYPES, AGENT_SPECIALIZATIONS, COMMUNICATION_PROTOCOLS } from '../models/Agent.js'
import AgentExecution, { EXECUTION_STATUS, TRIGGER_TYPES } from '../models/AgentExecution.js'
import Workflow from '../models/Workflow.js'
import { logger } from '../utils/logger.js'
import EventEmitter from 'events'

/**
 * Colony Intelligence Service
 * Manages the hierarchical AI agent system inspired by ant/bee colonies
 * Handles agent communication, coordination, and autonomous collaboration
 */
class ColonyIntelligenceService extends EventEmitter {
  constructor() {
    super()
    this.activeQueens = new Map() // Queen agents currently orchestrating
    this.workerPools = new Map()  // Worker agent pools by specialization
    this.scoutReports = new Map() // Scout agent discoveries
    this.communicationChannels = new Map() // Active communication channels
    this.colonyMetrics = {
      totalAgents: 0,
      activeExecutions: 0,
      communicationEvents: 0,
      collaborationScore: 0
    }
    
    // Initialize colony (will be called after DB connection)
    this.initialized = false
  }

  /**
   * Initialize the colony with default agents and communication protocols
   */
  async initializeColony() {
    if (this.initialized) return

    try {
      logger.info('🐜 Initializing NeuroColony Intelligence System...')

      // Set up communication protocols
      this.setupCommunicationProtocols()

      // Initialize default agent templates (only if DB is connected)
      if (global.dbConnected) {
        await this.createDefaultAgentTemplates()
      }

      // Start colony monitoring
      this.startColonyMonitoring()

      this.initialized = true
      logger.info('✅ Colony Intelligence System initialized successfully')
    } catch (error) {
      logger.error('❌ Failed to initialize colony:', error)
      // Don't throw error to prevent server crash
    }
  }

  /**
   * Set up communication protocols between agents
   */
  setupCommunicationProtocols() {
    // Direct communication for immediate responses
    this.communicationChannels.set(COMMUNICATION_PROTOCOLS.DIRECT, {
      handler: this.handleDirectCommunication.bind(this),
      latency: 'immediate',
      reliability: 'high'
    })

    // Broadcast for colony-wide announcements
    this.communicationChannels.set(COMMUNICATION_PROTOCOLS.BROADCAST, {
      handler: this.handleBroadcastCommunication.bind(this),
      latency: 'low',
      reliability: 'medium'
    })

    // Queue for asynchronous task distribution
    this.communicationChannels.set(COMMUNICATION_PROTOCOLS.QUEUE, {
      handler: this.handleQueueCommunication.bind(this),
      latency: 'variable',
      reliability: 'high'
    })

    // Event-driven for reactive behaviors
    this.communicationChannels.set(COMMUNICATION_PROTOCOLS.EVENT, {
      handler: this.handleEventCommunication.bind(this),
      latency: 'immediate',
      reliability: 'high'
    })

    // Pipeline for sequential processing
    this.communicationChannels.set(COMMUNICATION_PROTOCOLS.PIPELINE, {
      handler: this.handlePipelineCommunication.bind(this),
      latency: 'sequential',
      reliability: 'high'
    })
  }

  /**
   * Create default agent templates for common use cases
   */
  async createDefaultAgentTemplates() {
    const defaultAgents = [
      // Queen Agents (Orchestrators)
      {
        name: 'Marketing Campaign Orchestrator',
        agentType: AGENT_TYPES.QUEEN,
        specialization: AGENT_SPECIALIZATIONS.CAMPAIGN_MANAGER,
        description: 'Orchestrates complex marketing campaigns across multiple channels',
        capabilities: [
          {
            name: 'campaign_planning',
            description: 'Plan multi-channel marketing campaigns',
            complexity: 'expert'
          },
          {
            name: 'resource_allocation',
            description: 'Allocate worker agents to campaign tasks',
            complexity: 'complex'
          }
        ],
        communicationProtocols: [
          COMMUNICATION_PROTOCOLS.BROADCAST,
          COMMUNICATION_PROTOCOLS.DIRECT,
          COMMUNICATION_PROTOCOLS.QUEUE
        ]
      },
      
      // Worker Agents (Specialists)
      {
        name: 'Email Sequence Generator',
        agentType: AGENT_TYPES.WORKER,
        specialization: AGENT_SPECIALIZATIONS.EMAIL_SEQUENCE,
        description: 'Generates high-converting email sequences using AI',
        capabilities: [
          {
            name: 'sequence_generation',
            description: 'Generate email sequences based on business context',
            complexity: 'moderate'
          },
          {
            name: 'personalization',
            description: 'Personalize emails for different audience segments',
            complexity: 'complex'
          }
        ],
        communicationProtocols: [
          COMMUNICATION_PROTOCOLS.DIRECT,
          COMMUNICATION_PROTOCOLS.PIPELINE
        ]
      },
      
      {
        name: 'Subject Line Optimizer',
        agentType: AGENT_TYPES.WORKER,
        specialization: AGENT_SPECIALIZATIONS.SUBJECT_LINE_TESTING,
        description: 'Optimizes email subject lines for maximum open rates',
        capabilities: [
          {
            name: 'ab_testing',
            description: 'Create and manage A/B tests for subject lines',
            complexity: 'moderate'
          },
          {
            name: 'performance_analysis',
            description: 'Analyze subject line performance metrics',
            complexity: 'complex'
          }
        ]
      },
      
      // Scout Agents (Discovery & Monitoring)
      {
        name: 'Competitor Intelligence Scout',
        agentType: AGENT_TYPES.SCOUT,
        specialization: AGENT_SPECIALIZATIONS.COMPETITOR_MONITORING,
        description: 'Monitors competitor activities and market trends',
        capabilities: [
          {
            name: 'competitor_tracking',
            description: 'Track competitor pricing, content, and campaigns',
            complexity: 'complex'
          },
          {
            name: 'trend_detection',
            description: 'Detect emerging market trends and opportunities',
            complexity: 'expert'
          }
        ],
        communicationProtocols: [
          COMMUNICATION_PROTOCOLS.EVENT,
          COMMUNICATION_PROTOCOLS.BROADCAST
        ]
      }
    ]

    // Create template agents (not tied to specific users)
    for (const agentTemplate of defaultAgents) {
      const existingTemplate = await Agent.findOne({
        name: agentTemplate.name,
        isTemplate: true
      })

      if (!existingTemplate) {
        await Agent.create({
          ...agentTemplate,
          isTemplate: true,
          isPublic: true,
          owner: null, // System-owned template
          status: 'active'
        })
        logger.info(`✅ Created agent template: ${agentTemplate.name}`)
      }
    }
  }

  /**
   * Start monitoring colony health and performance
   */
  startColonyMonitoring() {
    // Monitor every 30 seconds
    setInterval(async () => {
      try {
        await this.updateColonyMetrics()
        await this.optimizeColonyPerformance()
      } catch (error) {
        logger.error('Colony monitoring error:', error)
      }
    }, 30000)
  }

  /**
   * Update colony-wide metrics
   */
  async updateColonyMetrics() {
    const [totalAgents, activeExecutions] = await Promise.all([
      Agent.countDocuments({ status: 'active' }),
      AgentExecution.countDocuments({ status: 'running' })
    ])

    this.colonyMetrics = {
      ...this.colonyMetrics,
      totalAgents,
      activeExecutions,
      lastUpdated: new Date()
    }

    // Emit metrics update event
    this.emit('colonyMetricsUpdated', this.colonyMetrics)
  }

  /**
   * Optimize colony performance based on current metrics
   */
  async optimizeColonyPerformance() {
    // Auto-scale worker pools based on demand
    await this.autoScaleWorkerPools()
    
    // Optimize communication patterns
    await this.optimizeCommunicationPatterns()
    
    // Balance resource allocation
    await this.balanceResourceAllocation()
  }

  /**
   * Auto-scale worker agent pools based on demand
   */
  async autoScaleWorkerPools() {
    const queuedExecutions = await AgentExecution.find({
      status: 'pending'
    }).populate('agent')

    // Group by specialization
    const demandBySpecialization = {}
    queuedExecutions.forEach(execution => {
      const spec = execution.agent.specialization
      demandBySpecialization[spec] = (demandBySpecialization[spec] || 0) + 1
    })

    // Scale up high-demand specializations
    for (const [specialization, demand] of Object.entries(demandBySpecialization)) {
      if (demand > 5) { // Threshold for scaling
        logger.info(`🔄 High demand detected for ${specialization}: ${demand} queued executions`)
        // Could trigger creation of additional worker instances
        this.emit('scaleUpRequired', { specialization, demand })
      }
    }
  }

  /**
   * Communication Protocol Handlers
   */
  async handleDirectCommunication(fromAgent, toAgent, message) {
    logger.debug(`📡 Direct communication: ${fromAgent} -> ${toAgent}`)
    
    // Record communication event
    this.colonyMetrics.communicationEvents++
    
    // Process message immediately
    return await this.processAgentMessage(fromAgent, toAgent, message)
  }

  async handleBroadcastCommunication(fromAgent, message) {
    logger.debug(`📢 Broadcast from ${fromAgent}`)
    
    // Send to all active agents in colony
    const activeAgents = await Agent.find({ status: 'active' })
    const results = []
    
    for (const agent of activeAgents) {
      if (agent._id.toString() !== fromAgent) {
        results.push(await this.processAgentMessage(fromAgent, agent._id, message))
      }
    }
    
    return results
  }

  async handleQueueCommunication(fromAgent, message) {
    logger.debug(`📥 Queue message from ${fromAgent}`)
    
    // Add to processing queue
    // Implementation would depend on queue system (Redis, RabbitMQ, etc.)
    return { queued: true, timestamp: new Date() }
  }

  async handleEventCommunication(fromAgent, event) {
    logger.debug(`⚡ Event from ${fromAgent}: ${event.type}`)
    
    // Emit event for interested agents
    this.emit('agentEvent', {
      source: fromAgent,
      event,
      timestamp: new Date()
    })
    
    return { eventEmitted: true }
  }

  async handlePipelineCommunication(fromAgent, toAgent, data) {
    logger.debug(`🔄 Pipeline: ${fromAgent} -> ${toAgent}`)
    
    // Sequential processing with data transformation
    return await this.processAgentMessage(fromAgent, toAgent, data, { pipeline: true })
  }

  /**
   * Process message between agents
   */
  async processAgentMessage(fromAgentId, toAgentId, message, options = {}) {
    try {
      // Record communication in execution logs
      const communication = {
        timestamp: new Date(),
        direction: 'outbound',
        protocol: options.protocol || 'direct',
        targetAgent: toAgentId,
        message,
        acknowledged: false
      }

      // Find active execution for the receiving agent
      const activeExecution = await AgentExecution.findOne({
        agent: toAgentId,
        status: 'running'
      })

      if (activeExecution) {
        activeExecution.communications.push(communication)
        await activeExecution.save()
      }

      return {
        success: true,
        timestamp: new Date(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }
    } catch (error) {
      logger.error('Agent message processing error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Get colony status and metrics
   */
  getColonyStatus() {
    return {
      ...this.colonyMetrics,
      activeQueens: this.activeQueens.size,
      workerPools: Array.from(this.workerPools.keys()),
      scoutReports: this.scoutReports.size,
      communicationChannels: Array.from(this.communicationChannels.keys())
    }
  }

  /**
   * Get agent recommendations based on user needs
   */
  async getAgentRecommendations(userContext) {
    const { industry, goals, currentAgents = [] } = userContext

    // Analyze user's current agent setup
    const currentSpecializations = currentAgents.map(a => a.specialization)

    // Recommend complementary agents
    const recommendations = []

    // Always recommend a Queen agent if they don't have one
    if (!currentSpecializations.includes(AGENT_SPECIALIZATIONS.CAMPAIGN_MANAGER)) {
      recommendations.push({
        type: 'queen',
        specialization: AGENT_SPECIALIZATIONS.CAMPAIGN_MANAGER,
        reason: 'Orchestrate and coordinate your marketing efforts',
        priority: 'high'
      })
    }

    // Industry-specific recommendations
    if (industry === 'ecommerce' && !currentSpecializations.includes(AGENT_SPECIALIZATIONS.PRICE_TRACKING)) {
      recommendations.push({
        type: 'scout',
        specialization: AGENT_SPECIALIZATIONS.PRICE_TRACKING,
        reason: 'Monitor competitor pricing in real-time',
        priority: 'medium'
      })
    }

    return recommendations
  }

  /**
   * Create a new agent from template
   */
  async createAgentFromTemplate(templateId, userId, customization = {}) {
    try {
      const template = await Agent.findById(templateId)
      if (!template || !template.isTemplate) {
        throw new Error('Template not found')
      }

      const newAgent = new Agent({
        ...template.toObject(),
        _id: undefined,
        owner: userId,
        isTemplate: false,
        isPublic: false,
        name: customization.name || template.name,
        description: customization.description || template.description,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      await newAgent.save()
      logger.info(`✅ Created agent from template: ${newAgent.name}`)

      return newAgent
    } catch (error) {
      logger.error('Failed to create agent from template:', error)
      throw error
    }
  }

  /**
   * Execute an agent with given inputs
   */
  async executeAgent(agentId, inputs, userId, triggerType = 'manual') {
    try {
      const agent = await Agent.findById(agentId)
      if (!agent) {
        throw new Error('Agent not found')
      }

      // Create execution record
      const execution = new AgentExecution({
        agent: agentId,
        agentSnapshot: {
          name: agent.name,
          version: agent.version,
          specialization: agent.specialization,
          aiConfig: agent.aiConfig,
          executionConfig: agent.executionConfig
        },
        trigger: {
          type: triggerType,
          source: userId,
          metadata: { timestamp: new Date() }
        },
        input: {
          data: inputs,
          validation: { isValid: true, errors: [] }
        },
        user: userId,
        status: 'pending'
      })

      await execution.save()

      // Queue for execution
      this.emit('agentExecutionQueued', {
        executionId: execution.executionId,
        agentId,
        userId
      })

      return execution
    } catch (error) {
      logger.error('Failed to execute agent:', error)
      throw error
    }
  }
}

// Export singleton instance
const colonyIntelligenceService = new ColonyIntelligenceService()
export default colonyIntelligenceService
