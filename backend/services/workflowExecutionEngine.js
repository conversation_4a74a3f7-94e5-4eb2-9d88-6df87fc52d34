import Workflow from '../models/Workflow.js';
import ColonyExecution from '../models/ColonyExecution.js';
import Agent from '../models/Agent.js';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger.js';
import colonyIntelligenceEnhanced from './colonyIntelligenceEnhanced.js';

// Workflow Execution Engine - Enterprise-grade workflow orchestration
class WorkflowExecutionEngine extends EventEmitter {
  constructor() {
    super();
    
    // Active workflow tracking
    this.activeWorkflows = new Map();
    this.workflowQueues = new Map();
    this.executionContexts = new Map();
    
    // Execution metrics
    this.metrics = {
      totalWorkflows: 0,
      activeWorkflows: 0,
      completedWorkflows: 0,
      failedWorkflows: 0,
      avgExecutionTime: 0,
      nodeExecutions: 0
    };
    
    // Workflow patterns
    this.patterns = {
      sequential: this.executeSequentialPattern.bind(this),
      parallel: this.executeParallelPattern.bind(this),
      conditional: this.executeConditionalPattern.bind(this),
      loop: this.executeLoopPattern.bind(this),
      pipeline: this.executePipelinePattern.bind(this),
      scatter: this.executeScatterPattern.bind(this),
      gather: this.executeGatherPattern.bind(this)
    };
    
    this.initializeEngine();
  }

  initializeEngine() {
    // Set up event handlers
    this.on('workflow:started', this.handleWorkflowStarted.bind(this));
    this.on('workflow:completed', this.handleWorkflowCompleted.bind(this));
    this.on('workflow:failed', this.handleWorkflowFailed.bind(this));
    this.on('node:executed', this.handleNodeExecuted.bind(this));
    
    logger.info('Workflow Execution Engine initialized');
  }

  // Main workflow execution entry point
  async executeWorkflow(workflowId, userId, inputs = {}, options = {}) {
    const executionId = uuidv4();
    
    try {
      // Load workflow
      const workflow = await Workflow.findById(workflowId).populate('nodes.agentId');
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      
      // Validate user permissions
      if (workflow.createdBy.toString() !== userId && !workflow.sharedWith.includes(userId)) {
        throw new Error('Unauthorized to execute this workflow');
      }
      
      // Create execution context
      const context = {
        executionId,
        workflowId,
        userId,
        inputs,
        variables: { ...workflow.variables, ...inputs },
        state: {},
        startTime: Date.now(),
        options
      };
      
      // Store context
      this.executionContexts.set(executionId, context);
      this.activeWorkflows.set(executionId, workflow);
      
      // Emit start event
      this.emit('workflow:started', {
        executionId,
        workflowId,
        name: workflow.name
      });
      
      // Update metrics
      this.metrics.totalWorkflows++;
      this.metrics.activeWorkflows++;
      
      // Create execution record
      const execution = await ColonyExecution.create({
        executionId,
        workflowId,
        workflowName: workflow.name,
        userId,
        status: 'running',
        trigger: {
          type: options.triggerType || 'manual',
          source: userId,
          metadata: options.triggerMetadata || {}
        },
        context: {
          inputs,
          variables: context.variables
        },
        startTime: new Date()
      });
      
      // Execute based on workflow pattern
      const pattern = workflow.executionPattern || 'sequential';
      const result = await this.patterns[pattern](workflow, context);
      
      // Update execution record
      execution.status = 'completed';
      execution.result = result;
      execution.endTime = new Date();
      execution.duration = execution.endTime - execution.startTime;
      await execution.save();
      
      // Cleanup
      this.executionContexts.delete(executionId);
      this.activeWorkflows.delete(executionId);
      
      // Emit completion
      this.emit('workflow:completed', {
        executionId,
        workflowId,
        result,
        duration: Date.now() - context.startTime
      });
      
      // Update metrics
      this.metrics.activeWorkflows--;
      this.metrics.completedWorkflows++;
      this.updateAvgExecutionTime(Date.now() - context.startTime);
      
      return {
        executionId,
        status: 'completed',
        result,
        duration: Date.now() - context.startTime
      };
      
    } catch (error) {
      logger.error('Workflow execution failed', {
        executionId,
        workflowId,
        error: error.message
      });
      
      // Update execution record if exists
      try {
        await ColonyExecution.findOneAndUpdate(
          { executionId },
          {
            status: 'failed',
            error: {
              message: error.message,
              stack: error.stack
            },
            endTime: new Date()
          }
        );
      } catch (updateError) {
        logger.error('Failed to update execution record', { updateError });
      }
      
      // Cleanup
      this.executionContexts.delete(executionId);
      this.activeWorkflows.delete(executionId);
      
      // Emit failure
      this.emit('workflow:failed', {
        executionId,
        workflowId,
        error: error.message
      });
      
      // Update metrics
      this.metrics.activeWorkflows--;
      this.metrics.failedWorkflows++;
      
      throw error;
    }
  }

  // Sequential pattern execution
  async executeSequentialPattern(workflow, context) {
    const results = {};
    
    for (const node of workflow.nodes) {
      try {
        // Check if node should be skipped
        if (await this.shouldSkipNode(node, context)) {
          results[node.id] = { skipped: true };
          continue;
        }
        
        // Execute node
        const result = await this.executeNode(node, context);
        results[node.id] = result;
        
        // Update context with node output
        context.state[node.id] = result;
        
        // Check if we should stop execution
        if (result.stopWorkflow) {
          break;
        }
      } catch (error) {
        // Handle node error based on error strategy
        const handled = await this.handleNodeError(node, error, workflow, context);
        if (!handled) {
          throw error;
        }
        results[node.id] = { error: error.message };
      }
    }
    
    return results;
  }

  // Parallel pattern execution
  async executeParallelPattern(workflow, context) {
    const results = {};
    
    // Group nodes by dependencies
    const batches = this.createExecutionBatches(workflow.nodes);
    
    for (const batch of batches) {
      // Execute all nodes in batch concurrently
      const batchPromises = batch.map(async (node) => {
        try {
          if (await this.shouldSkipNode(node, context)) {
            return { nodeId: node.id, result: { skipped: true } };
          }
          
          const result = await this.executeNode(node, context);
          context.state[node.id] = result;
          
          return { nodeId: node.id, result };
        } catch (error) {
          const handled = await this.handleNodeError(node, error, workflow, context);
          if (!handled) {
            throw error;
          }
          return { nodeId: node.id, result: { error: error.message } };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      
      // Merge results
      batchResults.forEach(({ nodeId, result }) => {
        results[nodeId] = result;
      });
      
      // Check if any node requested workflow stop
      if (batchResults.some(r => r.result.stopWorkflow)) {
        break;
      }
    }
    
    return results;
  }

  // Conditional pattern execution
  async executeConditionalPattern(workflow, context) {
    const results = {};
    
    for (const node of workflow.nodes) {
      // Evaluate conditions
      const shouldExecute = await this.evaluateNodeConditions(node, context);
      
      if (!shouldExecute) {
        results[node.id] = { skipped: true, reason: 'Condition not met' };
        continue;
      }
      
      try {
        const result = await this.executeNode(node, context);
        results[node.id] = result;
        context.state[node.id] = result;
        
        if (result.stopWorkflow) {
          break;
        }
      } catch (error) {
        const handled = await this.handleNodeError(node, error, workflow, context);
        if (!handled) {
          throw error;
        }
        results[node.id] = { error: error.message };
      }
    }
    
    return results;
  }

  // Loop pattern execution
  async executeLoopPattern(workflow, context) {
    const results = {};
    const maxIterations = workflow.loopConfig?.maxIterations || 100;
    let iteration = 0;
    
    while (iteration < maxIterations) {
      const iterationResults = {};
      let shouldContinue = false;
      
      for (const node of workflow.nodes) {
        try {
          // Add iteration context
          const nodeContext = {
            ...context,
            iteration,
            isFirstIteration: iteration === 0,
            isLastIteration: false // Will be set if loop exits
          };
          
          const result = await this.executeNode(node, nodeContext);
          iterationResults[node.id] = result;
          
          // Check loop continuation condition
          if (node.isLoopController && result.continueLoop === false) {
            shouldContinue = false;
            break;
          } else if (node.isLoopController && result.continueLoop === true) {
            shouldContinue = true;
          }
        } catch (error) {
          const handled = await this.handleNodeError(node, error, workflow, context);
          if (!handled) {
            throw error;
          }
          iterationResults[node.id] = { error: error.message };
        }
      }
      
      // Store iteration results
      results[`iteration_${iteration}`] = iterationResults;
      
      if (!shouldContinue) {
        break;
      }
      
      iteration++;
    }
    
    return results;
  }

  // Pipeline pattern execution
  async executePipelinePattern(workflow, context) {
    const results = {};
    let pipelineData = context.inputs;
    
    for (const node of workflow.nodes) {
      try {
        // Pass output of previous node as input to next
        const nodeInput = {
          ...context.inputs,
          pipelineData,
          previousNodeOutput: pipelineData
        };
        
        const result = await this.executeNode(node, {
          ...context,
          inputs: nodeInput
        });
        
        results[node.id] = result;
        
        // Use node output as input for next node
        pipelineData = result.output || result;
        
        if (result.stopWorkflow) {
          break;
        }
      } catch (error) {
        const handled = await this.handleNodeError(node, error, workflow, context);
        if (!handled) {
          throw error;
        }
        results[node.id] = { error: error.message };
        break; // Pipeline broken on error
      }
    }
    
    return {
      results,
      finalOutput: pipelineData
    };
  }

  // Scatter pattern execution
  async executeScatterPattern(workflow, context) {
    const results = {};
    const scatterNode = workflow.nodes.find(n => n.isScatterNode);
    
    if (!scatterNode) {
      throw new Error('No scatter node found in workflow');
    }
    
    // Execute scatter node to get items to process
    const scatterResult = await this.executeNode(scatterNode, context);
    const itemsToProcess = scatterResult.items || [];
    
    results[scatterNode.id] = scatterResult;
    
    // Process each item in parallel
    const itemPromises = itemsToProcess.map(async (item, index) => {
      const itemContext = {
        ...context,
        scatterItem: item,
        scatterIndex: index
      };
      
      const itemResults = {};
      
      // Execute worker nodes for this item
      for (const node of workflow.nodes.filter(n => n.isWorkerNode)) {
        try {
          const result = await this.executeNode(node, itemContext);
          itemResults[node.id] = result;
        } catch (error) {
          itemResults[node.id] = { error: error.message };
        }
      }
      
      return { item, results: itemResults };
    });
    
    const scatteredResults = await Promise.all(itemPromises);
    results.scattered = scatteredResults;
    
    return results;
  }

  // Gather pattern execution
  async executeGatherPattern(workflow, context) {
    // Execute scatter pattern first
    const scatterResults = await this.executeScatterPattern(workflow, context);
    
    // Find gather node
    const gatherNode = workflow.nodes.find(n => n.isGatherNode);
    if (!gatherNode) {
      return scatterResults;
    }
    
    // Prepare gathered data
    const gatheredData = scatterResults.scattered.map(s => s.results);
    
    // Execute gather node with all results
    const gatherContext = {
      ...context,
      inputs: {
        ...context.inputs,
        scatteredResults: gatheredData
      }
    };
    
    const gatherResult = await this.executeNode(gatherNode, gatherContext);
    
    return {
      ...scatterResults,
      gathered: gatherResult
    };
  }

  // Execute individual node
  async executeNode(node, context) {
    const startTime = Date.now();
    const nodeExecutionId = uuidv4();
    
    try {
      logger.info(`Executing node: ${node.name || node.id}`, {
        nodeId: node.id,
        type: node.type,
        executionId: context.executionId
      });
      
      // Get agent for node
      const agent = await this.getNodeAgent(node);
      
      // Prepare node inputs
      const nodeInputs = await this.prepareNodeInputs(node, context);
      
      // Record node execution start
      await ColonyExecution.create({
        executionId: nodeExecutionId,
        parentExecutionId: context.executionId,
        nodeId: node.id,
        agentId: agent._id,
        status: 'running',
        input: nodeInputs,
        startTime: new Date()
      });
      
      // Execute through colony intelligence
      const result = await colonyIntelligenceEnhanced.executeWorkerTask(
        agent,
        {
          type: node.type,
          configuration: node.configuration
        },
        nodeInputs
      );
      
      // Update node execution record
      await ColonyExecution.findOneAndUpdate(
        { executionId: nodeExecutionId },
        {
          status: 'completed',
          output: result,
          endTime: new Date(),
          duration: Date.now() - startTime
        }
      );
      
      // Emit node execution event
      this.emit('node:executed', {
        nodeId: node.id,
        executionId: nodeExecutionId,
        duration: Date.now() - startTime,
        success: true
      });
      
      // Update metrics
      this.metrics.nodeExecutions++;
      
      return result;
      
    } catch (error) {
      logger.error(`Node execution failed: ${node.name || node.id}`, {
        nodeId: node.id,
        error: error.message
      });
      
      // Update node execution record
      await ColonyExecution.findOneAndUpdate(
        { executionId: nodeExecutionId },
        {
          status: 'failed',
          error: {
            message: error.message,
            stack: error.stack
          },
          endTime: new Date(),
          duration: Date.now() - startTime
        }
      );
      
      // Emit node failure event
      this.emit('node:failed', {
        nodeId: node.id,
        executionId: nodeExecutionId,
        error: error.message
      });
      
      throw error;
    }
  }

  // Get agent for node execution
  async getNodeAgent(node) {
    if (node.agentId) {
      // If populated
      if (node.agentId._id) {
        return node.agentId;
      }
      
      // Fetch agent
      const agent = await Agent.findById(node.agentId);
      if (!agent) {
        throw new Error(`Agent not found for node: ${node.id}`);
      }
      return agent;
    }
    
    // Create temporary agent for node
    return {
      _id: `temp_${node.id}`,
      name: node.name || 'Workflow Node',
      type: 'worker',
      configuration: {
        aiModel: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000
      }
    };
  }

  // Prepare inputs for node execution
  async prepareNodeInputs(node, context) {
    const inputs = {
      ...context.inputs,
      ...node.configuration?.inputs
    };
    
    // Add outputs from connected nodes
    if (node.connections && node.connections.length > 0) {
      for (const connection of node.connections) {
        const sourceNodeId = connection.sourceNodeId;
        const sourceOutput = context.state[sourceNodeId];
        
        if (sourceOutput) {
          inputs[`${sourceNodeId}_output`] = sourceOutput;
          
          // Map specific fields if configured
          if (connection.fieldMapping) {
            Object.entries(connection.fieldMapping).forEach(([source, target]) => {
              inputs[target] = this.getNestedValue(sourceOutput, source);
            });
          }
        }
      }
    }
    
    // Evaluate dynamic expressions
    if (node.configuration?.expressions) {
      for (const [key, expression] of Object.entries(node.configuration.expressions)) {
        inputs[key] = await this.evaluateExpression(expression, context);
      }
    }
    
    return inputs;
  }

  // Check if node should be skipped
  async shouldSkipNode(node, context) {
    if (!node.skipCondition) {
      return false;
    }
    
    try {
      return await this.evaluateCondition(node.skipCondition, context);
    } catch (error) {
      logger.error('Error evaluating skip condition', {
        nodeId: node.id,
        error: error.message
      });
      return false;
    }
  }

  // Evaluate node conditions
  async evaluateNodeConditions(node, context) {
    if (!node.conditions || node.conditions.length === 0) {
      return true;
    }
    
    const operator = node.conditionOperator || 'AND';
    
    const results = await Promise.all(
      node.conditions.map(condition => this.evaluateCondition(condition, context))
    );
    
    if (operator === 'AND') {
      return results.every(r => r === true);
    } else if (operator === 'OR') {
      return results.some(r => r === true);
    } else {
      throw new Error(`Unknown condition operator: ${operator}`);
    }
  }

  // Evaluate single condition
  async evaluateCondition(condition, context) {
    const { type, field, operator, value } = condition;
    
    let fieldValue;
    
    if (type === 'variable') {
      fieldValue = context.variables[field];
    } else if (type === 'state') {
      fieldValue = this.getNestedValue(context.state, field);
    } else if (type === 'input') {
      fieldValue = this.getNestedValue(context.inputs, field);
    } else {
      throw new Error(`Unknown condition type: ${type}`);
    }
    
    return this.compareValues(fieldValue, operator, value);
  }

  // Compare values based on operator
  compareValues(fieldValue, operator, compareValue) {
    switch (operator) {
      case 'equals':
        return fieldValue === compareValue;
      case 'notEquals':
        return fieldValue !== compareValue;
      case 'contains':
        return String(fieldValue).includes(String(compareValue));
      case 'notContains':
        return !String(fieldValue).includes(String(compareValue));
      case 'greaterThan':
        return Number(fieldValue) > Number(compareValue);
      case 'lessThan':
        return Number(fieldValue) < Number(compareValue);
      case 'greaterOrEqual':
        return Number(fieldValue) >= Number(compareValue);
      case 'lessOrEqual':
        return Number(fieldValue) <= Number(compareValue);
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      case 'notIn':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      case 'isEmpty':
        return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0);
      case 'isNotEmpty':
        return fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0);
      default:
        throw new Error(`Unknown operator: ${operator}`);
    }
  }

  // Evaluate dynamic expression
  async evaluateExpression(expression, context) {
    // Simple expression evaluation - in production, use a safe expression parser
    try {
      // Replace variables in expression
      let evaluatedExpression = expression;
      
      // Replace {{variable}} with actual values
      const variableRegex = /\{\{(\w+)\}\}/g;
      evaluatedExpression = evaluatedExpression.replace(variableRegex, (match, varName) => {
        return context.variables[varName] || '';
      });
      
      // Replace [[state.path]] with state values
      const stateRegex = /\[\[state\.([^\]]+)\]\]/g;
      evaluatedExpression = evaluatedExpression.replace(stateRegex, (match, path) => {
        return this.getNestedValue(context.state, path) || '';
      });
      
      return evaluatedExpression;
    } catch (error) {
      logger.error('Expression evaluation failed', {
        expression,
        error: error.message
      });
      return expression;
    }
  }

  // Get nested value from object
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // Handle node execution error
  async handleNodeError(node, error, workflow, context) {
    const errorStrategy = workflow.errorHandling?.strategy || 'stop';
    
    logger.error(`Node error: ${node.name || node.id}`, {
      nodeId: node.id,
      error: error.message,
      strategy: errorStrategy
    });
    
    switch (errorStrategy) {
      case 'stop':
        return false; // Don't handle, propagate error
        
      case 'continue':
        // Log and continue
        this.emit('node:error:ignored', {
          nodeId: node.id,
          error: error.message
        });
        return true;
        
      case 'retry':
        const maxRetries = workflow.errorHandling?.retryAttempts || 3;
        const retryDelay = workflow.errorHandling?.retryDelay || 1000;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            logger.info(`Retrying node: ${node.name || node.id} (attempt ${attempt})`, {
              nodeId: node.id
            });
            
            await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            
            const result = await this.executeNode(node, context);
            context.state[node.id] = result;
            
            return true; // Successfully handled
          } catch (retryError) {
            if (attempt === maxRetries) {
              return false; // All retries failed
            }
          }
        }
        break;
        
      case 'fallback':
        // Execute fallback node if configured
        if (node.fallbackNodeId) {
          const fallbackNode = workflow.nodes.find(n => n.id === node.fallbackNodeId);
          if (fallbackNode) {
            try {
              const result = await this.executeNode(fallbackNode, context);
              context.state[node.id] = result;
              return true;
            } catch (fallbackError) {
              logger.error('Fallback node also failed', {
                nodeId: fallbackNode.id,
                error: fallbackError.message
              });
            }
          }
        }
        return false;
        
      default:
        return false;
    }
  }

  // Create execution batches for parallel execution
  createExecutionBatches(nodes) {
    const batches = [];
    const visited = new Set();
    const inDegree = new Map();
    
    // Calculate in-degree for each node
    nodes.forEach(node => {
      inDegree.set(node.id, 0);
    });
    
    nodes.forEach(node => {
      if (node.connections) {
        node.connections.forEach(conn => {
          const currentDegree = inDegree.get(conn.targetNodeId) || 0;
          inDegree.set(conn.targetNodeId, currentDegree + 1);
        });
      }
    });
    
    // Create batches based on dependencies
    while (visited.size < nodes.length) {
      const batch = [];
      
      nodes.forEach(node => {
        if (!visited.has(node.id) && inDegree.get(node.id) === 0) {
          batch.push(node);
          visited.add(node.id);
        }
      });
      
      if (batch.length === 0 && visited.size < nodes.length) {
        // Circular dependency detected
        throw new Error('Circular dependency detected in workflow');
      }
      
      // Update in-degrees
      batch.forEach(node => {
        if (node.connections) {
          node.connections.forEach(conn => {
            const currentDegree = inDegree.get(conn.targetNodeId);
            inDegree.set(conn.targetNodeId, currentDegree - 1);
          });
        }
      });
      
      batches.push(batch);
    }
    
    return batches;
  }

  // Schedule workflow execution
  async scheduleWorkflow(workflowId, userId, schedule, inputs = {}) {
    const workflow = await Workflow.findById(workflowId);
    if (!workflow) {
      throw new Error('Workflow not found');
    }
    
    // Store schedule configuration
    workflow.schedule = {
      enabled: true,
      ...schedule,
      userId,
      inputs,
      lastRun: null,
      nextRun: this.calculateNextRun(schedule)
    };
    
    await workflow.save();
    
    logger.info('Workflow scheduled', {
      workflowId,
      schedule: workflow.schedule
    });
    
    return workflow.schedule;
  }

  // Calculate next run time based on schedule
  calculateNextRun(schedule) {
    const now = new Date();
    
    switch (schedule.type) {
      case 'once':
        return new Date(schedule.at);
        
      case 'interval':
        return new Date(now.getTime() + schedule.interval);
        
      case 'cron':
        // Use cron parser in production
        return new Date(now.getTime() + 3600000); // 1 hour default
        
      case 'daily':
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(schedule.hour || 0);
        tomorrow.setMinutes(schedule.minute || 0);
        return tomorrow;
        
      case 'weekly':
        const nextWeek = new Date(now);
        nextWeek.setDate(nextWeek.getDate() + 7);
        return nextWeek;
        
      default:
        throw new Error(`Unknown schedule type: ${schedule.type}`);
    }
  }

  // Event handlers
  handleWorkflowStarted(data) {
    logger.info('Workflow started', data);
  }

  handleWorkflowCompleted(data) {
    logger.info('Workflow completed', data);
    
    // Trigger any dependent workflows
    this.triggerDependentWorkflows(data.workflowId, data.result);
  }

  handleWorkflowFailed(data) {
    logger.error('Workflow failed', data);
    
    // Send alerts if configured
    this.sendWorkflowAlerts(data.workflowId, data.error);
  }

  handleNodeExecuted(data) {
    logger.debug('Node executed', data);
  }

  // Trigger dependent workflows
  async triggerDependentWorkflows(workflowId, result) {
    const dependentWorkflows = await Workflow.find({
      'trigger.type': 'workflow',
      'trigger.sourceWorkflowId': workflowId
    });
    
    for (const workflow of dependentWorkflows) {
      try {
        await this.executeWorkflow(
          workflow._id,
          workflow.createdBy,
          {
            triggerWorkflowResult: result
          },
          {
            triggerType: 'workflow',
            triggerMetadata: {
              sourceWorkflowId: workflowId
            }
          }
        );
      } catch (error) {
        logger.error('Failed to trigger dependent workflow', {
          workflowId: workflow._id,
          error: error.message
        });
      }
    }
  }

  // Send workflow alerts
  async sendWorkflowAlerts(workflowId, error) {
    const workflow = await Workflow.findById(workflowId);
    if (!workflow || !workflow.errorHandling?.alerting) {
      return;
    }
    
    const alerting = workflow.errorHandling.alerting;
    
    if (alerting.email) {
      // Send email alert
      this.emit('alert:email', {
        to: alerting.email,
        subject: `Workflow Failed: ${workflow.name}`,
        body: `Workflow ${workflow.name} failed with error: ${error}`
      });
    }
    
    if (alerting.webhook) {
      // Send webhook alert
      this.emit('alert:webhook', {
        url: alerting.webhook,
        data: {
          workflowId,
          workflowName: workflow.name,
          error,
          timestamp: new Date()
        }
      });
    }
  }

  // Update average execution time
  updateAvgExecutionTime(duration) {
    const totalDuration = this.metrics.avgExecutionTime * (this.metrics.completedWorkflows - 1) + duration;
    this.metrics.avgExecutionTime = totalDuration / this.metrics.completedWorkflows;
  }

  // Get workflow execution history
  async getWorkflowHistory(workflowId, options = {}) {
    const { limit = 10, offset = 0, status } = options;
    
    const query = { workflowId };
    if (status) {
      query.status = status;
    }
    
    const executions = await ColonyExecution.find(query)
      .sort({ startTime: -1 })
      .limit(limit)
      .skip(offset);
    
    const total = await ColonyExecution.countDocuments(query);
    
    return {
      executions,
      total,
      limit,
      offset
    };
  }

  // Get workflow metrics
  async getWorkflowMetrics(workflowId) {
    const executions = await ColonyExecution.find({ workflowId });
    
    const metrics = {
      totalExecutions: executions.length,
      successfulExecutions: executions.filter(e => e.status === 'completed').length,
      failedExecutions: executions.filter(e => e.status === 'failed').length,
      avgDuration: 0,
      minDuration: Infinity,
      maxDuration: 0,
      successRate: 0
    };
    
    if (executions.length > 0) {
      const durations = executions
        .filter(e => e.duration)
        .map(e => e.duration);
      
      if (durations.length > 0) {
        metrics.avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
        metrics.minDuration = Math.min(...durations);
        metrics.maxDuration = Math.max(...durations);
      }
      
      metrics.successRate = (metrics.successfulExecutions / metrics.totalExecutions) * 100;
    }
    
    return metrics;
  }

  // Validate workflow
  async validateWorkflow(workflow) {
    const errors = [];
    const warnings = [];
    
    // Check for empty workflow
    if (!workflow.nodes || workflow.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }
    
    // Check for orphaned nodes
    const nodeIds = new Set(workflow.nodes.map(n => n.id));
    workflow.nodes.forEach(node => {
      if (node.connections) {
        node.connections.forEach(conn => {
          if (!nodeIds.has(conn.targetNodeId)) {
            errors.push(`Node ${node.id} references non-existent target ${conn.targetNodeId}`);
          }
        });
      }
    });
    
    // Check for circular dependencies
    try {
      this.createExecutionBatches(workflow.nodes);
    } catch (error) {
      if (error.message.includes('Circular dependency')) {
        errors.push('Workflow contains circular dependencies');
      }
    }
    
    // Check agent assignments
    for (const node of workflow.nodes) {
      if (!node.agentId && !node.type) {
        warnings.push(`Node ${node.id} has no agent assigned and no type specified`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Get engine metrics
  getEngineMetrics() {
    return {
      ...this.metrics,
      activeWorkflowIds: Array.from(this.activeWorkflows.keys()),
      queuedWorkflows: this.workflowQueues.size
    };
  }
}

// Export singleton instance
const workflowExecutionEngine = new WorkflowExecutionEngine();
export default workflowExecutionEngine;