import { Server } from 'socket.io'
import jwt from 'jsonwebtoken'
import { logger } from '../utils/logger.js'

/**
 * NeuroColony WebSocket Service
 * Real-time colony updates, inter-agent communication, and live monitoring
 */
class ColonyWebSocketService {
  constructor() {
    this.io = null
    this.colonies = new Map() // colonyId -> Set of socket IDs
    this.agentConnections = new Map() // agentId -> socket ID
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3003',
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token
        if (!token) {
          return next(new Error('Authentication error'))
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET)
        socket.userId = decoded.id
        next()
      } catch (err) {
        next(new Error('Authentication error'))
      }
    })

    // Connection handling
    this.io.on('connection', (socket) => {
      logger.info(`🔌 Colony node connected: ${socket.id} (User: ${socket.userId})`)

      // Join user's colony room
      socket.join(`user:${socket.userId}`)

      // Colony event handlers
      socket.on('colony:join', (colonyId) => this.handleColonyJoin(socket, colonyId))
      socket.on('colony:leave', (colonyId) => this.handleColonyLeave(socket, colonyId))
      socket.on('agent:status', (data) => this.handleAgentStatus(socket, data))
      socket.on('agent:communicate', (data) => this.handleAgentCommunication(socket, data))
      socket.on('colony:monitor', (colonyId) => this.handleColonyMonitoring(socket, colonyId))

      socket.on('disconnect', () => {
        logger.info(`🔌 Colony node disconnected: ${socket.id}`)
        this.handleDisconnect(socket)
      })
    })

    logger.info('🌐 Colony WebSocket service initialized')
  }

  /**
   * Handle colony join
   */
  handleColonyJoin(socket, colonyId) {
    socket.join(`colony:${colonyId}`)
    
    if (!this.colonies.has(colonyId)) {
      this.colonies.set(colonyId, new Set())
    }
    this.colonies.get(colonyId).add(socket.id)

    // Send initial colony state
    this.emitColonyState(colonyId)
    
    logger.info(`👥 Socket ${socket.id} joined colony ${colonyId}`)
  }

  /**
   * Handle colony leave
   */
  handleColonyLeave(socket, colonyId) {
    socket.leave(`colony:${colonyId}`)
    
    if (this.colonies.has(colonyId)) {
      this.colonies.get(colonyId).delete(socket.id)
      if (this.colonies.get(colonyId).size === 0) {
        this.colonies.delete(colonyId)
      }
    }
    
    logger.info(`👋 Socket ${socket.id} left colony ${colonyId}`)
  }

  /**
   * Handle agent status updates
   */
  handleAgentStatus(socket, data) {
    const { agentId, status, metrics } = data
    
    // Update agent connection
    this.agentConnections.set(agentId, socket.id)
    
    // Broadcast to colony
    const colonyRoom = `colony:${data.colonyId}`
    this.io.to(colonyRoom).emit('agent:statusUpdate', {
      agentId,
      status,
      metrics: {
        ...metrics,
        timestamp: new Date(),
        synapticLatency: Math.random() * 20 + 5, // 5-25ms
        neuralLoad: Math.random() * 100
      }
    })
  }

  /**
   * Handle inter-agent communication
   */
  handleAgentCommunication(socket, data) {
    const { fromAgent, toAgent, message, protocol } = data
    
    // Direct agent-to-agent communication
    if (protocol === 'direct' && this.agentConnections.has(toAgent)) {
      const targetSocketId = this.agentConnections.get(toAgent)
      this.io.to(targetSocketId).emit('agent:message', {
        fromAgent,
        message,
        timestamp: new Date()
      })
    } 
    // Broadcast to colony
    else if (protocol === 'broadcast') {
      this.io.to(`colony:${data.colonyId}`).emit('agent:broadcast', {
        fromAgent,
        message,
        timestamp: new Date()
      })
    }
    // Queue-based communication
    else if (protocol === 'queue') {
      this.io.to(`colony:${data.colonyId}`).emit('agent:queued', {
        fromAgent,
        toAgent,
        message,
        queueId: `queue_${Date.now()}`,
        timestamp: new Date()
      })
    }
  }

  /**
   * Handle colony monitoring requests
   */
  handleColonyMonitoring(socket, colonyId) {
    // Start sending real-time metrics
    const interval = setInterval(() => {
      if (!this.colonies.has(colonyId) || !this.colonies.get(colonyId).has(socket.id)) {
        clearInterval(interval)
        return
      }

      this.io.to(socket.id).emit('colony:metrics', {
        colonyId,
        timestamp: new Date(),
        health: {
          overall: 'optimal',
          synapticDensity: Math.random() * 20 + 80, // 80-100%
          neuralEfficiency: Math.random() * 15 + 85, // 85-100%
          swarmCoordination: Math.random() * 10 + 90 // 90-100%
        },
        performance: {
          executionsPerMinute: Math.floor(Math.random() * 50 + 20),
          averageResponseTime: Math.random() * 100 + 50, // 50-150ms
          successRate: Math.random() * 5 + 95 // 95-100%
        },
        agents: {
          active: Math.floor(Math.random() * 10 + 5),
          idle: Math.floor(Math.random() * 5),
          evolving: Math.floor(Math.random() * 3)
        }
      })
    }, 2000) // Update every 2 seconds

    socket.on('disconnect', () => clearInterval(interval))
  }

  /**
   * Handle disconnection
   */
  handleDisconnect(socket) {
    // Remove from all colonies
    for (const [colonyId, sockets] of this.colonies.entries()) {
      if (sockets.has(socket.id)) {
        sockets.delete(socket.id)
        if (sockets.size === 0) {
          this.colonies.delete(colonyId)
        }
      }
    }

    // Remove agent connections
    for (const [agentId, socketId] of this.agentConnections.entries()) {
      if (socketId === socket.id) {
        this.agentConnections.delete(agentId)
      }
    }
  }

  /**
   * Emit colony state to all members
   */
  emitColonyState(colonyId) {
    const state = {
      colonyId,
      timestamp: new Date(),
      activeNodes: this.colonies.get(colonyId)?.size || 0,
      status: 'synchronized'
    }

    this.io.to(`colony:${colonyId}`).emit('colony:state', state)
  }

  /**
   * Broadcast agent execution update
   */
  broadcastAgentExecution(agentId, colonyId, execution) {
    this.io.to(`colony:${colonyId}`).emit('agent:execution', {
      agentId,
      executionId: execution.id,
      status: execution.status,
      progress: execution.progress,
      timestamp: new Date()
    })
  }

  /**
   * Broadcast colony evolution event
   */
  broadcastColonyEvolution(colonyId, evolutionData) {
    this.io.to(`colony:${colonyId}`).emit('colony:evolution', {
      colonyId,
      previousStage: evolutionData.previousStage,
      newStage: evolutionData.newStage,
      improvements: evolutionData.improvements,
      timestamp: new Date()
    })
  }

  /**
   * Send alert to colony members
   */
  sendColonyAlert(colonyId, alert) {
    this.io.to(`colony:${colonyId}`).emit('colony:alert', {
      colonyId,
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      timestamp: new Date()
    })
  }
}

export default new ColonyWebSocketService()