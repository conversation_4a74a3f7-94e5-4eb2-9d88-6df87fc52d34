/**
 * High-Performance Job Queue System
 * Implements background processing for AI generation and other heavy tasks
 */

import { EventEmitter } from 'events'
import { Worker } from 'worker_threads'
import path from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { logger } from '../utils/logger.js'
import cacheService from './cacheService.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

class JobQueue extends EventEmitter {
  constructor() {
    super()
    
    this.queues = {
      aiGeneration: [],
      analytics: [],
      email: [],
      cleanup: []
    }

    this.workers = new Map()
    this.jobResults = new Map()
    this.processingJobs = new Map()
    
    this.config = {
      maxWorkers: 4,
      jobTimeout: 300000, // 5 minutes
      retryAttempts: 3,
      retryDelay: 5000 // 5 seconds
    }

    this.stats = {
      jobsProcessed: 0,
      jobsFailed: 0,
      avgProcessingTime: 0,
      queueSizes: {}
    }

    this.initializeWorkers()
    this.startQueueProcessor()
  }

  /**
   * Add AI generation job to queue
   */
  async addAIGenerationJob(jobData) {
    const job = {
      id: this.generateJobId(),
      type: 'aiGeneration',
      data: jobData,
      priority: jobData.priority || 5,
      createdAt: new Date(),
      attempts: 0,
      userId: jobData.userId
    }

    this.queues.aiGeneration.push(job)
    this.sortQueue('aiGeneration')
    
    logger.info(`📋 AI generation job queued: ${job.id}`)
    this.emit('jobQueued', job)
    
    return job.id
  }

  /**
   * Add analytics computation job
   */
  async addAnalyticsJob(jobData) {
    const job = {
      id: this.generateJobId(),
      type: 'analytics',
      data: jobData,
      priority: jobData.priority || 3,
      createdAt: new Date(),
      attempts: 0,
      userId: jobData.userId
    }

    this.queues.analytics.push(job)
    this.sortQueue('analytics')
    
    logger.info(`📊 Analytics job queued: ${job.id}`)
    this.emit('jobQueued', job)
    
    return job.id
  }

  /**
   * Add email sending job
   */
  async addEmailJob(jobData) {
    const job = {
      id: this.generateJobId(),
      type: 'email',
      data: jobData,
      priority: jobData.priority || 7,
      createdAt: new Date(),
      attempts: 0,
      userId: jobData.userId
    }

    this.queues.email.push(job)
    this.sortQueue('email')
    
    logger.info(`📧 Email job queued: ${job.id}`)
    this.emit('jobQueued', job)
    
    return job.id
  }

  /**
   * Get job status and result
   */
  async getJobStatus(jobId) {
    // Check if job is processing
    if (this.processingJobs.has(jobId)) {
      const job = this.processingJobs.get(jobId)
      return {
        status: 'processing',
        progress: job.progress || 0,
        startedAt: job.startedAt,
        estimatedCompletion: this.estimateCompletion(job)
      }
    }

    // Check if job is completed
    if (this.jobResults.has(jobId)) {
      const result = this.jobResults.get(jobId)
      return {
        status: result.success ? 'completed' : 'failed',
        result: result.data,
        error: result.error,
        completedAt: result.completedAt,
        processingTime: result.processingTime
      }
    }

    // Check if job is queued
    for (const [queueName, queue] of Object.entries(this.queues)) {
      const queuedJob = queue.find(job => job.id === jobId)
      if (queuedJob) {
        return {
          status: 'queued',
          queuePosition: queue.indexOf(queuedJob) + 1,
          estimatedStartTime: this.estimateQueueTime(queueName, queuedJob)
        }
      }
    }

    return { status: 'not_found' }
  }

  /**
   * Initialize worker threads
   */
  initializeWorkers() {
    for (let i = 0; i < this.config.maxWorkers; i++) {
      this.createWorker(i)
    }
  }

  /**
   * Create a new worker thread
   */
  createWorker(workerId) {
    try {
      const workerPath = path.join(__dirname, '../workers/jobWorker.js')
      const worker = new Worker(workerPath, {
        workerData: { workerId }
      })

      worker.on('message', (message) => {
        this.handleWorkerMessage(workerId, message)
      })

      worker.on('error', (error) => {
        logger.error(`Worker ${workerId} error:`, error)
        this.restartWorker(workerId)
      })

      worker.on('exit', (code) => {
        if (code !== 0) {
          logger.error(`Worker ${workerId} exited with code ${code}`)
          this.restartWorker(workerId)
        }
      })

      this.workers.set(workerId, {
        worker,
        busy: false,
        currentJob: null,
        startedAt: new Date()
      })

      logger.info(`👷 Worker ${workerId} created`)

    } catch (error) {
      logger.error(`Failed to create worker ${workerId}:`, error)
    }
  }

  /**
   * Handle messages from worker threads
   */
  handleWorkerMessage(workerId, message) {
    const { type, jobId, data, error, progress } = message

    switch (type) {
      case 'jobStarted':
        this.handleJobStarted(workerId, jobId)
        break

      case 'jobProgress':
        this.handleJobProgress(jobId, progress)
        break

      case 'jobCompleted':
        this.handleJobCompleted(workerId, jobId, data)
        break

      case 'jobFailed':
        this.handleJobFailed(workerId, jobId, error)
        break

      default:
        logger.warn(`Unknown message type from worker ${workerId}:`, type)
    }
  }

  /**
   * Handle job started
   */
  handleJobStarted(workerId, jobId) {
    const workerInfo = this.workers.get(workerId)
    if (workerInfo) {
      workerInfo.busy = true
      
      if (this.processingJobs.has(jobId)) {
        const job = this.processingJobs.get(jobId)
        job.startedAt = new Date()
        job.workerId = workerId
      }
    }

    logger.info(`🚀 Job ${jobId} started on worker ${workerId}`)
    this.emit('jobStarted', { jobId, workerId })
  }

  /**
   * Handle job progress update
   */
  handleJobProgress(jobId, progress) {
    if (this.processingJobs.has(jobId)) {
      const job = this.processingJobs.get(jobId)
      job.progress = progress
      
      this.emit('jobProgress', { jobId, progress })
    }
  }

  /**
   * Handle job completion
   */
  handleJobCompleted(workerId, jobId, result) {
    const processingTime = this.markJobCompleted(workerId, jobId, true, result)
    
    this.stats.jobsProcessed++
    this.updateAvgProcessingTime(processingTime)
    
    logger.info(`✅ Job ${jobId} completed in ${processingTime}ms`)
    this.emit('jobCompleted', { jobId, result, processingTime })
  }

  /**
   * Handle job failure
   */
  handleJobFailed(workerId, jobId, error) {
    const job = this.processingJobs.get(jobId)
    
    if (job && job.attempts < this.config.retryAttempts) {
      // Retry job
      job.attempts++
      logger.warn(`🔄 Retrying job ${jobId} (attempt ${job.attempts}/${this.config.retryAttempts})`)
      
      setTimeout(() => {
        this.requeueJob(job)
      }, this.config.retryDelay)
      
    } else {
      // Job failed permanently
      const processingTime = this.markJobCompleted(workerId, jobId, false, null, error)
      
      this.stats.jobsFailed++
      
      logger.error(`❌ Job ${jobId} failed permanently:`, error)
      this.emit('jobFailed', { jobId, error, processingTime })
    }
  }

  /**
   * Mark job as completed and clean up
   */
  markJobCompleted(workerId, jobId, success, result = null, error = null) {
    const job = this.processingJobs.get(jobId)
    const processingTime = job ? Date.now() - job.startedAt.getTime() : 0

    // Store result
    this.jobResults.set(jobId, {
      success,
      data: result,
      error: error?.message || error,
      completedAt: new Date(),
      processingTime
    })

    // Clean up processing job
    this.processingJobs.delete(jobId)

    // Mark worker as available
    const workerInfo = this.workers.get(workerId)
    if (workerInfo) {
      workerInfo.busy = false
      workerInfo.currentJob = null
    }

    // Clean up old results (keep last 1000)
    if (this.jobResults.size > 1000) {
      const oldestKey = this.jobResults.keys().next().value
      this.jobResults.delete(oldestKey)
    }

    return processingTime
  }

  /**
   * Requeue failed job for retry
   */
  requeueJob(job) {
    const queueName = job.type
    if (this.queues[queueName]) {
      // Add back to front of queue for priority
      this.queues[queueName].unshift(job)
      this.emit('jobRequeued', job)
    }
  }

  /**
   * Start the queue processor
   */
  startQueueProcessor() {
    setInterval(() => {
      this.processQueues()
    }, 1000) // Check every second

    // Update queue size stats every 10 seconds
    setInterval(() => {
      this.updateQueueStats()
    }, 10000)

    logger.info('🏭 Job queue processor started')
  }

  /**
   * Process all queues
   */
  processQueues() {
    // Find available workers
    const availableWorkers = Array.from(this.workers.entries())
      .filter(([_, info]) => !info.busy)
      .map(([id]) => id)

    if (availableWorkers.length === 0) {
      return // No workers available
    }

    // Process queues in priority order
    const queueOrder = ['email', 'aiGeneration', 'analytics', 'cleanup']
    
    for (const queueName of queueOrder) {
      const queue = this.queues[queueName]
      
      while (queue.length > 0 && availableWorkers.length > 0) {
        const job = queue.shift()
        const workerId = availableWorkers.shift()
        
        this.assignJobToWorker(job, workerId)
      }
    }
  }

  /**
   * Assign job to worker
   */
  assignJobToWorker(job, workerId) {
    const workerInfo = this.workers.get(workerId)
    
    if (!workerInfo || workerInfo.busy) {
      // Worker not available, put job back
      this.queues[job.type].unshift(job)
      return
    }

    // Mark job as processing
    this.processingJobs.set(job.id, {
      ...job,
      assignedAt: new Date(),
      workerId
    })

    // Send job to worker
    workerInfo.worker.postMessage({
      type: 'processJob',
      job
    })

    logger.info(`📤 Job ${job.id} assigned to worker ${workerId}`)
  }

  /**
   * Sort queue by priority (higher priority first)
   */
  sortQueue(queueName) {
    const queue = this.queues[queueName]
    if (queue) {
      queue.sort((a, b) => b.priority - a.priority)
    }
  }

  /**
   * Generate unique job ID
   */
  generateJobId() {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Estimate job completion time
   */
  estimateCompletion(job) {
    const avgTime = this.stats.avgProcessingTime || 30000 // 30 seconds default
    const elapsed = Date.now() - job.startedAt.getTime()
    const estimated = job.startedAt.getTime() + avgTime
    
    return new Date(Math.max(estimated, Date.now() + 5000)) // At least 5 seconds from now
  }

  /**
   * Estimate queue wait time
   */
  estimateQueueTime(queueName, job) {
    const queue = this.queues[queueName]
    const position = queue.indexOf(job)
    const avgTime = this.stats.avgProcessingTime || 30000
    
    const estimatedWait = position * (avgTime / this.config.maxWorkers)
    return new Date(Date.now() + estimatedWait)
  }

  /**
   * Update average processing time
   */
  updateAvgProcessingTime(newTime) {
    if (this.stats.jobsProcessed === 1) {
      this.stats.avgProcessingTime = newTime
    } else {
      this.stats.avgProcessingTime = 
        (this.stats.avgProcessingTime * (this.stats.jobsProcessed - 1) + newTime) / 
        this.stats.jobsProcessed
    }
  }

  /**
   * Update queue statistics
   */
  updateQueueStats() {
    for (const [queueName, queue] of Object.entries(this.queues)) {
      this.stats.queueSizes[queueName] = queue.length
    }
  }

  /**
   * Restart worker
   */
  restartWorker(workerId) {
    const workerInfo = this.workers.get(workerId)
    
    if (workerInfo) {
      try {
        workerInfo.worker.terminate()
      } catch (error) {
        logger.error(`Error terminating worker ${workerId}:`, error)
      }
      
      this.workers.delete(workerId)
    }

    // Create new worker
    setTimeout(() => {
      this.createWorker(workerId)
    }, 1000)
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    const availableWorkers = Array.from(this.workers.values())
      .filter(info => !info.busy).length

    return {
      workers: {
        total: this.workers.size,
        available: availableWorkers,
        busy: this.workers.size - availableWorkers
      },
      queues: this.stats.queueSizes,
      processing: this.processingJobs.size,
      stats: {
        jobsProcessed: this.stats.jobsProcessed,
        jobsFailed: this.stats.jobsFailed,
        successRate: this.stats.jobsProcessed / (this.stats.jobsProcessed + this.stats.jobsFailed) || 1,
        avgProcessingTime: Math.round(this.stats.avgProcessingTime)
      }
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('🛑 Shutting down job queue...')

    // Stop accepting new jobs
    this.removeAllListeners()

    // Wait for current jobs to complete (with timeout)
    const timeout = 30000 // 30 seconds
    const shutdownPromise = this.waitForJobsToComplete()
    const timeoutPromise = new Promise(resolve => setTimeout(resolve, timeout))

    await Promise.race([shutdownPromise, timeoutPromise])

    // Terminate all workers
    for (const [workerId, workerInfo] of this.workers) {
      try {
        await workerInfo.worker.terminate()
        logger.info(`👷 Worker ${workerId} terminated`)
      } catch (error) {
        logger.error(`Error terminating worker ${workerId}:`, error)
      }
    }

    this.workers.clear()
    logger.info('✅ Job queue shutdown complete')
  }

  /**
   * Wait for all jobs to complete
   */
  async waitForJobsToComplete() {
    return new Promise((resolve) => {
      const checkJobs = () => {
        if (this.processingJobs.size === 0) {
          resolve()
        } else {
          setTimeout(checkJobs, 1000)
        }
      }
      checkJobs()
    })
  }
}

// Export singleton instance
export default new JobQueue()