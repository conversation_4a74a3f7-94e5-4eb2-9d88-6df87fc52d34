import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import mongoose from 'mongoose'
import dotenv from 'dotenv'

// Import basic routes only
import authRoutes from './routes/auth.js'
import contactRoutes from './routes/contact.js'

// Import basic middleware
import { errorHandler } from './middleware/errorHandler.js'

dotenv.config()

const app = express()
const PORT = process.env.PORT || 5003

// Basic middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))
app.use(helmet())
app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000 // limit each IP to 1000 requests per windowMs
})
app.use(limiter)

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'NeuroColony Backend Minimal',
    version: '2.0.0-minimal'
  })
})

// Simple test endpoints
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'NeuroColony API is working!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  })
})

// Mock sequences endpoint for frontend testing
app.get('/api/sequences', (req, res) => {
  res.json({
    success: true,
    sequences: [
      {
        id: '1',
        title: 'Welcome Sequence',
        status: 'active',
        created: new Date().toISOString()
      }
    ]
  })
})

// Mock AI generation endpoint
app.post('/api/sequences/generate', (req, res) => {
  const { prompt, type } = req.body
  
  res.json({
    success: true,
    sequence: {
      id: Date.now().toString(),
      title: `Generated: ${type || 'Email Sequence'}`,
      content: `This is a mock AI-generated sequence based on: "${prompt}"`,
      emails: [
        {
          subject: 'Welcome to Our Community!',
          content: 'Thank you for joining us. We\'re excited to have you on board.'
        },
        {
          subject: 'Getting Started Guide',
          content: 'Here are some tips to help you get the most out of our platform.'
        }
      ],
      created: new Date().toISOString()
    }
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/contact', contactRoutes)

// Error handling
app.use(errorHandler)

// Start server without MongoDB for now (will connect once working)
const startServer = async () => {
  try {
    // Try MongoDB connection but don't fail if it's not available
    if (process.env.MONGODB_URI) {
      try {
        await mongoose.connect(process.env.MONGODB_URI)
        console.log('✅ MongoDB connected successfully')
      } catch (mongoError) {
        console.log('⚠️  MongoDB not available, running without database')
        console.log('   Frontend will still work with mock data')
      }
    }
    
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 NeuroColony Backend (Minimal) running on port ${PORT}`)
      console.log(`📡 Health check: http://localhost:${PORT}/health`)
      console.log(`🧪 API test: http://localhost:${PORT}/api/test`)
      console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`)
      console.log('')
      console.log('✅ Backend is ready for frontend testing!')
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error.message)
    // Don't exit, just log the error
    console.log('🔄 Attempting to start without problematic dependencies...')
    
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 NeuroColony Backend (Emergency Mode) running on port ${PORT}`)
    })
  }
}

startServer()