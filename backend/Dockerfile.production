# Multi-stage build for production
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    bash \
    && addgroup -g 1001 -S nodejs \
    && adduser -S neurocolony -u 1001

FROM base AS deps
WORKDIR /app
COPY package*.json ./
RUN npm install --omit=dev && npm cache clean --force

FROM base AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build 2>/dev/null || echo "No build script found"

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=5000

RUN mkdir -p /app/logs && chown -R neurocolony:nodejs /app/logs

# Copy node_modules from deps stage
COPY --from=deps --chown=neurocolony:nodejs /app/node_modules ./node_modules

# Copy application code
COPY --chown=neurocolony:nodejs . .

# Create non-root user
USER neurocolony

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

EXPOSE 5000

CMD ["node", "server-simple.js"]