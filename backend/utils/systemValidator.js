/**
 * 🔧 System Validator & Auto-Fixer
 * Advanced debugging and system health validation
 */

import mongoose from 'mongoose'
import redis from 'redis'
import { logger } from './logger.js'
import dotenv from 'dotenv'

dotenv.config()

class SystemValidator {
  constructor() {
    this.validationResults = {}
    this.autoFixAttempts = {}
  }

  // Comprehensive system validation
  async validateSystem() {
    logger.info('🔍 Starting comprehensive system validation...')
    
    const results = {
      mongodb: await this.validateMongoDB(),
      redis: await this.validateRedis(),
      environment: await this.validateEnvironment(),
      openai: await this.validateOpenAI(),
      dependencies: await this.validateDependencies(),
      overall: 'pending'
    }
    
    // Calculate overall health
    const passed = Object.values(results).filter(r => r.status === 'pass').length
    const total = Object.keys(results).length - 1 // Exclude 'overall'
    const healthPercentage = (passed / total) * 100
    
    results.overall = healthPercentage >= 80 ? 'healthy' : 
                     healthPercentage >= 60 ? 'degraded' : 'critical'
    
    results.healthPercentage = healthPercentage
    results.timestamp = new Date().toISOString()
    
    this.validationResults = results
    
    logger.info(`🏥 System validation complete: ${results.overall} (${healthPercentage.toFixed(1)}%)`)
    
    return results
  }

  // Validate MongoDB connection
  async validateMongoDB() {
    try {
      logger.info('🗄️ Validating MongoDB connection...')
      
      const mongoUri = process.env.MONGODB_URI
      if (!mongoUri) {
        return {
          status: 'fail',
          message: 'MONGODB_URI not configured',
          recommendation: 'Set MONGODB_URI in .env file'
        }
      }

      // Test connection
      const startTime = Date.now()
      await mongoose.connect(mongoUri, { serverSelectionTimeoutMS: 5000 })
      const responseTime = Date.now() - startTime
      
      // Test basic operations
      await mongoose.connection.db.admin().ping()
      
      await mongoose.disconnect()
      
      return {
        status: 'pass',
        message: 'MongoDB connection successful',
        responseTime: `${responseTime}ms`,
        uri: mongoUri.replace(/:([^:@]+)@/, ':***@') // Hide password
      }
      
    } catch (error) {
      return {
        status: 'fail',
        message: `MongoDB connection failed: ${error.message}`,
        error: error.name,
        recommendation: await this.getMongoDBRecommendation(error)
      }
    }
  }

  // Validate Redis connection
  async validateRedis() {
    try {
      logger.info('🔴 Validating Redis connection...')
      
      const redisUrl = process.env.REDIS_URL
      if (!redisUrl) {
        return {
          status: 'fail',
          message: 'REDIS_URL not configured',
          recommendation: 'Set REDIS_URL in .env file'
        }
      }

      const client = redis.createClient({ url: redisUrl })
      
      const startTime = Date.now()
      await client.connect()
      const responseTime = Date.now() - startTime
      
      // Test basic operations
      await client.ping()
      await client.set('health_check', 'ok', { EX: 5 })
      const testValue = await client.get('health_check')
      
      await client.disconnect()
      
      if (testValue !== 'ok') {
        throw new Error('Redis read/write test failed')
      }
      
      return {
        status: 'pass',
        message: 'Redis connection successful',
        responseTime: `${responseTime}ms`,
        url: redisUrl
      }
      
    } catch (error) {
      return {
        status: 'fail',
        message: `Redis connection failed: ${error.message}`,
        error: error.name,
        recommendation: await this.getRedisRecommendation(error)
      }
    }
  }

  // Validate environment configuration
  async validateEnvironment() {
    try {
      logger.info('🌍 Validating environment configuration...')
      
      const requiredVars = [
        'NODE_ENV',
        'PORT',
        'MONGODB_URI',
        'REDIS_URL',
        'JWT_SECRET'
      ]
      
      const missing = []
      const configured = []
      
      for (const varName of requiredVars) {
        if (!process.env[varName]) {
          missing.push(varName)
        } else {
          configured.push(varName)
        }
      }
      
      const optionalVars = [
        'OPENAI_API_KEY',
        'STRIPE_SECRET_KEY',
        'SMTP_HOST'
      ]
      
      const optionalStatus = {}
      for (const varName of optionalVars) {
        optionalStatus[varName] = process.env[varName] ? 'configured' : 'not_configured'
      }
      
      return {
        status: missing.length === 0 ? 'pass' : 'fail',
        message: missing.length === 0 ? 
          'All required environment variables configured' : 
          `Missing required variables: ${missing.join(', ')}`,
        configured,
        missing,
        optional: optionalStatus,
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT
      }
      
    } catch (error) {
      return {
        status: 'fail',
        message: `Environment validation failed: ${error.message}`,
        error: error.name
      }
    }
  }

  // Validate OpenAI configuration
  async validateOpenAI() {
    try {
      logger.info('🤖 Validating OpenAI configuration...')
      
      const apiKey = process.env.OPENAI_API_KEY
      
      if (!apiKey || apiKey === 'demo-mode' || apiKey.includes('dummy')) {
        return {
          status: 'warning',
          message: 'OpenAI API key not configured - using demo mode',
          mode: 'demo',
          recommendation: 'Set valid OPENAI_API_KEY for AI features'
        }
      }

      // Basic key format validation
      if (!apiKey.startsWith('sk-')) {
        return {
          status: 'fail',
          message: 'Invalid OpenAI API key format',
          recommendation: 'API key should start with "sk-"'
        }
      }

      // We won't test the actual API to avoid costs/rate limits
      return {
        status: 'pass',
        message: 'OpenAI API key configured (not tested)',
        keyFormat: `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`,
        note: 'Key format valid but not tested to avoid API costs'
      }
      
    } catch (error) {
      return {
        status: 'fail',
        message: `OpenAI validation failed: ${error.message}`,
        error: error.name
      }
    }
  }

  // Validate Node.js dependencies
  async validateDependencies() {
    try {
      logger.info('📦 Validating dependencies...')
      
      const critical = [
        'express',
        'mongoose',
        'redis',
        'jsonwebtoken',
        'bcryptjs'
      ]
      
      const optional = [
        'openai',
        'stripe',
        'nodemailer'
      ]
      
      const issues = []
      const versions = {}
      
      for (const dep of [...critical, ...optional]) {
        try {
          const pkg = await import(dep)
          versions[dep] = 'loaded'
        } catch (error) {
          if (critical.includes(dep)) {
            issues.push(`Critical dependency missing: ${dep}`)
          } else {
            versions[dep] = 'optional_missing'
          }
        }
      }
      
      return {
        status: issues.length === 0 ? 'pass' : 'fail',
        message: issues.length === 0 ? 
          'All critical dependencies available' : 
          `Dependency issues: ${issues.join(', ')}`,
        versions,
        issues,
        nodeVersion: process.version
      }
      
    } catch (error) {
      return {
        status: 'fail',
        message: `Dependency validation failed: ${error.message}`,
        error: error.name
      }
    }
  }

  // Get MongoDB-specific recommendations
  async getMongoDBRecommendation(error) {
    if (error.message.includes('Authentication failed')) {
      return 'Check MongoDB credentials in MONGODB_URI. Ensure user exists and has proper permissions.'
    }
    if (error.message.includes('ECONNREFUSED')) {
      return 'MongoDB server not running on specified host/port. Check if MongoDB is started.'
    }
    if (error.message.includes('timeout')) {
      return 'MongoDB connection timeout. Check network connectivity and server status.'
    }
    return 'Check MongoDB configuration and server status.'
  }

  // Get Redis-specific recommendations
  async getRedisRecommendation(error) {
    if (error.message.includes('ECONNREFUSED')) {
      return 'Redis server not running on specified host/port. Check if Redis is started.'
    }
    if (error.message.includes('timeout')) {
      return 'Redis connection timeout. Check network connectivity and server status.'
    }
    if (error.message.includes('authentication')) {
      return 'Redis authentication failed. Check Redis password configuration.'
    }
    return 'Check Redis configuration and server status.'
  }

  // Auto-fix common issues
  async autoFix() {
    logger.info('🔧 Attempting auto-fix for detected issues...')
    
    const results = await this.validateSystem()
    const fixes = []
    
    // MongoDB fixes
    if (results.mongodb.status === 'fail') {
      const mongoFix = await this.attemptMongoDBFix(results.mongodb)
      if (mongoFix) fixes.push(mongoFix)
    }
    
    // Redis fixes
    if (results.redis.status === 'fail') {
      const redisFix = await this.attemptRedisFix(results.redis)
      if (redisFix) fixes.push(redisFix)
    }
    
    // Environment fixes
    if (results.environment.status === 'fail') {
      const envFix = await this.attemptEnvironmentFix(results.environment)
      if (envFix) fixes.push(envFix)
    }
    
    return {
      applied: fixes,
      timestamp: new Date().toISOString(),
      revalidationNeeded: fixes.length > 0
    }
  }

  // Attempt MongoDB auto-fix
  async attemptMongoDBFix(mongoResult) {
    // This would contain auto-fix logic
    // For now, just return recommendations
    return {
      component: 'mongodb',
      issue: mongoResult.message,
      recommendation: mongoResult.recommendation,
      autoFixAvailable: false
    }
  }

  // Attempt Redis auto-fix
  async attemptRedisFix(redisResult) {
    // This would contain auto-fix logic
    return {
      component: 'redis',
      issue: redisResult.message,
      recommendation: redisResult.recommendation,
      autoFixAvailable: false
    }
  }

  // Attempt environment auto-fix
  async attemptEnvironmentFix(envResult) {
    if (envResult.missing && envResult.missing.length > 0) {
      return {
        component: 'environment',
        issue: `Missing variables: ${envResult.missing.join(', ')}`,
        recommendation: 'Add missing environment variables to .env file',
        autoFixAvailable: false
      }
    }
    return null
  }

  // Get system health summary
  getHealthSummary() {
    if (!this.validationResults.overall) {
      return { status: 'unknown', message: 'System not validated yet' }
    }
    
    const { overall, healthPercentage } = this.validationResults
    
    return {
      status: overall,
      percentage: healthPercentage,
      components: Object.keys(this.validationResults).length - 2, // Exclude overall and healthPercentage
      lastValidation: this.validationResults.timestamp,
      issues: this.getActiveIssues()
    }
  }

  // Get list of active issues
  getActiveIssues() {
    if (!this.validationResults) return []
    
    const issues = []
    
    Object.entries(this.validationResults).forEach(([component, result]) => {
      if (component === 'overall' || component === 'healthPercentage' || component === 'timestamp') return
      
      if (result.status === 'fail') {
        issues.push({
          component,
          severity: 'critical',
          message: result.message,
          recommendation: result.recommendation
        })
      } else if (result.status === 'warning') {
        issues.push({
          component,
          severity: 'warning',
          message: result.message,
          recommendation: result.recommendation
        })
      }
    })
    
    return issues
  }

  // Generate system health report
  generateHealthReport() {
    return {
      summary: this.getHealthSummary(),
      details: this.validationResults,
      issues: this.getActiveIssues(),
      recommendations: this.getSystemRecommendations(),
      generatedAt: new Date().toISOString()
    }
  }

  // Get system-wide recommendations
  getSystemRecommendations() {
    const recommendations = []
    
    if (!this.validationResults) {
      return ['Run system validation first']
    }
    
    // MongoDB recommendations
    if (this.validationResults.mongodb?.status === 'fail') {
      recommendations.push('Fix MongoDB connection issues before proceeding')
    }
    
    // Redis recommendations
    if (this.validationResults.redis?.status === 'fail') {
      recommendations.push('Redis issues may affect performance and caching')
    }
    
    // OpenAI recommendations
    if (this.validationResults.openai?.status === 'warning') {
      recommendations.push('Configure OpenAI API key for full AI functionality')
    }
    
    // Environment recommendations
    if (this.validationResults.environment?.missing?.length > 0) {
      recommendations.push('Configure missing environment variables')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('System appears healthy - monitor regularly')
    }
    
    return recommendations
  }
}

export default new SystemValidator()