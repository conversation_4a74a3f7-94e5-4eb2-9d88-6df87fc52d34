import { LRUCache } from 'lru-cache'
import crypto from 'crypto'
import { logger } from './logger.js'

class EnhancedCache {
  constructor() {
    // Upgraded from 1000 to 5000 items for better hit rate
    this.memoryCache = new LRUCache({
      max: 5000,
      ttl: 1000 * 60 * 5, // 5 minutes default
      updateAgeOnGet: false,
      updateAgeOnHas: false
    })

    // AI response cache with longer TTL
    this.aiCache = new LRUCache({
      max: 2000,
      ttl: 1000 * 60 * 30, // 30 minutes for AI responses
      updateAgeOnGet: true // Refresh frequently accessed responses
    })

    // Statistics tracking
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      startTime: Date.now()
    }

    // Log cache statistics every 5 minutes
    setInterval(() => {
      this.logStats()
    }, 5 * 60 * 1000)

    logger.info('🧠 Enhanced cache initialized', {
      memorySize: 5000,
      aiCacheSize: 2000,
      defaultTTL: '5 minutes'
    })
  }

  // Generate cache key with business context
  generateCacheKey(prompt, businessInfo = {}) {
    const normalizedPrompt = this.normalizePrompt(prompt)
    const businessContext = `${businessInfo.industry || 'general'}_${businessInfo.type || 'default'}`
    const combinedInput = `${normalizedPrompt}_${businessContext}`
    
    return crypto.createHash('sha256')
      .update(combinedInput)
      .digest('hex')
      .substring(0, 16) // Shorter keys for memory efficiency
  }

  // Normalize prompt for better cache hit rates
  normalizePrompt(prompt) {
    return prompt
      .toLowerCase()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s]/g, '') // Remove special chars
      .trim()
  }

  // Get from cache with similarity matching
  get(key, options = {}) {
    let value = this.memoryCache.get(key)
    
    if (value) {
      this.stats.hits++
      logger.debug('Cache hit', { key: key.substring(0, 8) })
      return value
    }

    // Try AI cache for AI-related requests
    if (options.type === 'ai') {
      value = this.aiCache.get(key)
      if (value) {
        this.stats.hits++
        logger.debug('AI cache hit', { key: key.substring(0, 8) })
        return value
      }
    }

    // Check for similar prompts if enabled
    if (options.similarity && options.type === 'ai') {
      const similarKey = this.findSimilarPrompt(key)
      if (similarKey) {
        value = this.aiCache.get(similarKey)
        if (value) {
          this.stats.hits++
          logger.debug('Similarity cache hit', { 
            originalKey: key.substring(0, 8),
            similarKey: similarKey.substring(0, 8)
          })
          return value
        }
      }
    }

    this.stats.misses++
    return null
  }

  // Set with intelligent TTL based on content quality
  set(key, value, options = {}) {
    let ttl = options.ttl

    // Dynamic TTL based on AI response quality
    if (options.type === 'ai' && value.quality) {
      if (value.quality >= 90) {
        ttl = 1000 * 60 * 60 // 1 hour for high quality
      } else if (value.quality >= 70) {
        ttl = 1000 * 60 * 30 // 30 minutes for good quality
      } else {
        ttl = 1000 * 60 * 10 // 10 minutes for lower quality
      }
    }

    // Store in appropriate cache
    if (options.type === 'ai') {
      this.aiCache.set(key, value, { ttl })
      logger.debug('AI cache set', { 
        key: key.substring(0, 8),
        ttl: ttl ? `${ttl/1000}s` : 'default'
      })
    } else {
      this.memoryCache.set(key, value, { ttl })
      logger.debug('Memory cache set', { 
        key: key.substring(0, 8),
        ttl: ttl ? `${ttl/1000}s` : 'default'
      })
    }

    this.stats.sets++
  }

  // Find similar prompts for better cache utilization
  findSimilarPrompt(targetKey) {
    const aiKeys = [...this.aiCache.keys()]
    
    // Simple similarity check - could be enhanced with more sophisticated algorithms
    for (const key of aiKeys) {
      const similarity = this.calculateSimilarity(targetKey, key)
      if (similarity > 0.8) { // 80% similarity threshold
        return key
      }
    }
    
    return null
  }

  // Calculate simple string similarity
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  // Levenshtein distance for similarity calculation
  levenshteinDistance(str1, str2) {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  // Delete from cache
  delete(key, options = {}) {
    if (options.type === 'ai') {
      return this.aiCache.delete(key)
    }
    return this.memoryCache.delete(key)
  }

  // Clear cache
  clear(options = {}) {
    if (options.type === 'ai') {
      this.aiCache.clear()
      logger.info('AI cache cleared')
    } else if (options.type === 'memory') {
      this.memoryCache.clear()
      logger.info('Memory cache cleared')
    } else {
      this.memoryCache.clear()
      this.aiCache.clear()
      logger.info('All caches cleared')
    }
  }

  // Cache warming - pre-populate with common queries
  async warmCache() {
    logger.info('🔥 Starting cache warming...')
    
    // Common business industries and types for pre-caching
    const commonBusinessTypes = [
      { industry: 'technology', type: 'saas' },
      { industry: 'ecommerce', type: 'retail' },
      { industry: 'healthcare', type: 'clinic' },
      { industry: 'finance', type: 'fintech' },
      { industry: 'education', type: 'online' }
    ]

    // Pre-generate cache keys for common scenarios
    const commonPrompts = [
      'Create a welcome email sequence',
      'Generate a product launch sequence', 
      'Create a customer onboarding sequence',
      'Build a sales funnel sequence',
      'Generate a newsletter sequence'
    ]

    let warmedCount = 0
    for (const businessInfo of commonBusinessTypes) {
      for (const prompt of commonPrompts) {
        const key = this.generateCacheKey(prompt, businessInfo)
        // Mark as warmed but don't actually generate content
        this.set(key, { warmed: true, timestamp: Date.now() }, { 
          type: 'ai',
          ttl: 1000 * 60 * 10 // 10 minutes for warm cache
        })
        warmedCount++
      }
    }

    logger.info(`🔥 Cache warming completed: ${warmedCount} entries pre-cached`)
  }

  // Get cache statistics
  getStats() {
    const uptime = Date.now() - this.stats.startTime
    const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) * 100

    return {
      memory: {
        size: this.memoryCache.size,
        max: this.memoryCache.max,
        usage: `${(this.memoryCache.size / this.memoryCache.max * 100).toFixed(1)}%`
      },
      ai: {
        size: this.aiCache.size,
        max: this.aiCache.max,
        usage: `${(this.aiCache.size / this.aiCache.max * 100).toFixed(1)}%`
      },
      performance: {
        hits: this.stats.hits,
        misses: this.stats.misses,
        sets: this.stats.sets,
        hitRate: `${hitRate.toFixed(1)}%`,
        uptime: `${Math.round(uptime / 1000)}s`
      }
    }
  }

  // Log cache statistics
  logStats() {
    const stats = this.getStats()
    logger.info('📊 Cache performance report', stats)
  }

  // Health check
  healthCheck() {
    const stats = this.getStats()
    const hitRate = parseFloat(stats.performance.hitRate)
    
    return {
      status: hitRate > 50 ? 'healthy' : 'degraded',
      hitRate: stats.performance.hitRate,
      memoryUsage: stats.memory.usage,
      aiCacheUsage: stats.ai.usage,
      details: stats
    }
  }
}

// Export singleton instance
export const cache = new EnhancedCache()
export default cache