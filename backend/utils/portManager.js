/**
 * Smart Port Management System
 * Resolves port conflicts automatically
 */

import { createServer } from 'net'
import { logger } from './logger.js'

export class PortManager {
  constructor() {
    this.usedPorts = new Set()
  }

  /**
   * Find an available port starting from the preferred port
   */
  async findAvailablePort(startPort = 5001, maxAttempts = 10) {
    let currentPort = startPort
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        const isAvailable = await this.checkPortAvailable(currentPort)
        
        if (isAvailable) {
          this.usedPorts.add(currentPort)
          logger.info(`🔌 Found available port: ${currentPort}`)
          return currentPort
        }
        
        logger.warn(`⚠️ Port ${currentPort} is busy, trying ${currentPort + 1}`)
        currentPort++
        attempts++
        
      } catch (error) {
        logger.error(`Port check error for ${currentPort}:`, error.message)
        currentPort++
        attempts++
      }
    }

    throw new Error(`Could not find available port after ${maxAttempts} attempts starting from ${startPort}`)
  }

  /**
   * Check if a specific port is available
   */
  async checkPortAvailable(port) {
    return new Promise((resolve) => {
      const server = createServer()
      
      server.listen(port, '0.0.0.0', () => {
        server.close(() => resolve(true))
      })
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          resolve(false)
        } else {
          logger.error(`Port check error for ${port}:`, err.message)
          resolve(false)
        }
      })
    })
  }

  /**
   * Kill process using a specific port (with caution)
   */
  async killProcessOnPort(port) {
    try {
      const { execSync } = await import('child_process')
      
      // Find process using the port
      const command = process.platform === 'win32' 
        ? `netstat -ano | findstr :${port}`
        : `lsof -ti:${port}`
      
      const result = execSync(command, { encoding: 'utf8', timeout: 5000 })
      
      if (result.trim()) {
        logger.warn(`🔪 Killing process on port ${port}`)
        
        if (process.platform === 'win32') {
          // Windows: Extract PID and kill
          const lines = result.trim().split('\n')
          const pid = lines[0].trim().split(/\s+/).pop()
          execSync(`taskkill /PID ${pid} /F`)
        } else {
          // Unix: Kill directly
          execSync(`kill -9 ${result.trim()}`)
        }
        
        // Wait a moment for the port to be released
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        logger.info(`✅ Successfully killed process on port ${port}`)
        return true
      }
      
      return false
      
    } catch (error) {
      logger.error(`Failed to kill process on port ${port}:`, error.message)
      return false
    }
  }

  /**
   * Smart port allocation with conflict resolution
   */
  async allocatePort(preferredPort, options = {}) {
    const {
      killExisting = false,
      autoIncrement = true,
      maxAttempts = 10
    } = options

    try {
      // First, try the preferred port
      const isAvailable = await this.checkPortAvailable(preferredPort)
      
      if (isAvailable) {
        this.usedPorts.add(preferredPort)
        return preferredPort
      }

      logger.warn(`⚠️ Port ${preferredPort} is already in use`)

      // Option 1: Kill existing process
      if (killExisting) {
        logger.info(`🔄 Attempting to free port ${preferredPort}`)
        const killed = await this.killProcessOnPort(preferredPort)
        
        if (killed) {
          const isNowAvailable = await this.checkPortAvailable(preferredPort)
          if (isNowAvailable) {
            this.usedPorts.add(preferredPort)
            return preferredPort
          }
        }
      }

      // Option 2: Find alternative port
      if (autoIncrement) {
        logger.info(`🔍 Searching for alternative port starting from ${preferredPort + 1}`)
        return await this.findAvailablePort(preferredPort + 1, maxAttempts)
      }

      throw new Error(`Port ${preferredPort} is not available and auto-increment is disabled`)
      
    } catch (error) {
      logger.error(`Port allocation failed:`, error.message)
      throw error
    }
  }

  /**
   * Release a port
   */
  releasePort(port) {
    this.usedPorts.delete(port)
    logger.info(`🔓 Released port ${port}`)
  }

  /**
   * Get all currently allocated ports
   */
  getAllocatedPorts() {
    return Array.from(this.usedPorts)
  }

  /**
   * Check system port usage
   */
  async getPortUsageInfo() {
    try {
      const { execSync } = await import('child_process')
      
      const command = process.platform === 'win32'
        ? 'netstat -an | findstr LISTENING'
        : 'netstat -tuln | grep LISTEN'
      
      const result = execSync(command, { encoding: 'utf8', timeout: 5000 })
      
      const ports = []
      const lines = result.trim().split('\n')
      
      for (const line of lines) {
        const match = line.match(/:(\d+)\s/)
        if (match) {
          ports.push(parseInt(match[1]))
        }
      }
      
      return {
        listeningPorts: [...new Set(ports)].sort((a, b) => a - b),
        allocatedPorts: this.getAllocatedPorts(),
        timestamp: new Date().toISOString()
      }
      
    } catch (error) {
      logger.error('Failed to get port usage info:', error.message)
      return {
        listeningPorts: [],
        allocatedPorts: this.getAllocatedPorts(),
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Singleton instance
export const portManager = new PortManager()
export default portManager