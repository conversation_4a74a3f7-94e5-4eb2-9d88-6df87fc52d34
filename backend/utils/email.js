import nodemailer from 'nodemailer'
import { logger } from './logger.js'

// Email templates
const templates = {
  'email-verification': {
    subject: 'Verify Your NeuroColony Account',
    html: (data) => `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to NeuroColony!</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.name},</h2>
            <p>Thank you for signing up for NeuroColony! To complete your registration, please verify your email address by clicking the button below:</p>
            <a href="${data.verificationUrl}" class="button">Verify Email Address</a>
            <p>This link will expire in 24 hours for security reasons.</p>
            <p>If you didn't create an account with us, please ignore this email.</p>
            <p>Best regards,<br>The NeuroColony Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 NeuroColony. All rights reserved.</p>
            <p>You received this email because you signed up for NeuroColony.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data) => `
      Welcome to NeuroColony!
      
      Hello ${data.name},
      
      Thank you for signing up for NeuroColony! To complete your registration, please verify your email address by visiting this link:
      
      ${data.verificationUrl}
      
      This link will expire in 24 hours for security reasons.
      
      If you didn't create an account with us, please ignore this email.
      
      Best regards,
      The NeuroColony Team
    `
  },
  
  'password-reset': {
    subject: 'Reset Your NeuroColony Password',
    html: (data) => `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: #ef4444; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .warning { background: #fef2f2; border-left: 4px solid #ef4444; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.name},</h2>
            <p>We received a request to reset the password for your NeuroColony account.</p>
            <a href="${data.resetUrl}" class="button">Reset Password</a>
            <div class="warning">
              <strong>Important:</strong> This link will expire in 10 minutes for security reasons.
            </div>
            <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
            <p>For security reasons, we recommend you:</p>
            <ul>
              <li>Use a unique, strong password</li>
              <li>Enable two-factor authentication when available</li>
              <li>Never share your login credentials</li>
            </ul>
            <p>Best regards,<br>The NeuroColony Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 NeuroColony. All rights reserved.</p>
            <p>If you need help, contact our support team.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data) => `
      Password Reset Request
      
      Hello ${data.name},
      
      We received a request to reset the password for your NeuroColony account.
      
      To reset your password, visit this link:
      ${data.resetUrl}
      
      IMPORTANT: This link will expire in 10 minutes for security reasons.
      
      If you didn't request a password reset, please ignore this email and your password will remain unchanged.
      
      Best regards,
      The NeuroColony Team
    `
  },
  
  'usage-warning': {
    subject: '⚠️ 80% Usage Alert - NeuroColony',
    html: (data) => `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: #f59e0b; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .warning { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }
          .stats { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Usage Alert - 80% Limit Reached</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.name},</h2>
            <div class="warning">
              <strong>You're approaching your monthly limit!</strong> You've used ${data.usagePercentage}% of your sequences.
            </div>
            <div class="stats">
              <h3>Current Usage:</h3>
              <p><strong>${data.sequencesGenerated}</strong> of <strong>${data.sequencesLimit}</strong> sequences used</p>
              <p><strong>${data.remaining}</strong> sequences remaining</p>
              <p>Billing period ends: <strong>${data.periodEnd}</strong></p>
            </div>
            ${data.canUpgrade ? `
              <p>Consider upgrading to Pro for 75 sequences/month + overage billing at just $3 per extra sequence!</p>
              <a href="${process.env.FRONTEND_URL}/pricing" class="button">Upgrade Plan</a>
            ` : `
              <p>Don't worry - with overage billing, you can continue generating sequences at $3 each if you exceed your limit.</p>
              <a href="${process.env.FRONTEND_URL}/dashboard" class="button">View Dashboard</a>
            `}
            <p>Best regards,<br>The NeuroColony Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 NeuroColony. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data) => `
      Usage Alert - 80% Limit Reached
      
      Hello ${data.name},
      
      You're approaching your monthly limit! You've used ${data.usagePercentage}% of your sequences.
      
      Current Usage:
      ${data.sequencesGenerated} of ${data.sequencesLimit} sequences used
      ${data.remaining} sequences remaining
      Billing period ends: ${data.periodEnd}
      
      ${data.canUpgrade ? 'Consider upgrading to Pro for more sequences and overage billing!' : 'With overage billing, you can continue at $3 per extra sequence.'}
      
      Best regards,
      The NeuroColony Team
    `
  },
  
  'usage-critical': {
    subject: '🚨 95% Usage Alert - NeuroColony',
    html: (data) => `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: #ef4444; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .critical { background: #fee2e2; border-left: 4px solid #ef4444; padding: 15px; margin: 20px 0; }
          .stats { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Critical Usage Alert - 95% Limit</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.name},</h2>
            <div class="critical">
              <strong>Critical Alert!</strong> You've used ${data.usagePercentage}% of your monthly sequences and are very close to your limit.
            </div>
            <div class="stats">
              <h3>Current Usage:</h3>
              <p><strong>${data.sequencesGenerated}</strong> of <strong>${data.sequencesLimit}</strong> sequences used</p>
              <p><strong>${data.remaining}</strong> sequences remaining</p>
              <p>Billing period ends: <strong>${data.periodEnd}</strong></p>
            </div>
            ${data.canUpgrade ? `
              <p>To avoid hitting your limit, consider upgrading to Pro for 75 sequences/month plus unlimited overage at $3 each!</p>
              <a href="${process.env.FRONTEND_URL}/pricing" class="button">Upgrade Now</a>
            ` : data.canGoOverage ? `
              <p>When you hit your limit, you can enable overage billing to continue at $${data.overageRate} per additional sequence.</p>
              <a href="${process.env.FRONTEND_URL}/dashboard" class="button">Manage Overage</a>
            ` : `
              <p>You'll need to wait until your next billing period or upgrade to continue generating sequences.</p>
              <a href="${process.env.FRONTEND_URL}/pricing" class="button">View Plans</a>
            `}
            <p>Best regards,<br>The NeuroColony Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 NeuroColony. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data) => `
      Critical Usage Alert - 95% Limit
      
      Hello ${data.name},
      
      Critical Alert! You've used ${data.usagePercentage}% of your monthly sequences.
      
      Current Usage:
      ${data.sequencesGenerated} of ${data.sequencesLimit} sequences used
      ${data.remaining} sequences remaining
      Billing period ends: ${data.periodEnd}
      
      ${data.canUpgrade ? 'Consider upgrading to avoid hitting your limit!' : data.canGoOverage ? `Enable overage billing to continue at $${data.overageRate} per sequence.` : 'Consider upgrading to continue generating sequences.'}
      
      Best regards,
      The NeuroColony Team
    `
  },
  
  'overage-consent': {
    subject: '💡 Enable Overage to Continue - NeuroColony',
    html: (data) => `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .info { background: #dbeafe; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Enable Overage Billing</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.name},</h2>
            <p>You've reached your ${data.planType} plan limit for this month. To continue generating email sequences, you can enable overage billing.</p>
            <div class="info">
              <h3>Overage Pricing:</h3>
              <p><strong>$${data.overageRate} per additional sequence</strong></p>
              <p>You'll only pay for what you use beyond your plan limit. Overage charges are billed at the end of your monthly cycle.</p>
            </div>
            <p>You can enable or disable overage billing anytime from your dashboard.</p>
            <a href="${data.dashboardUrl}" class="button">Enable Overage Billing</a>
            <p>Questions? Just reply to this email and we'll help!</p>
            <p>Best regards,<br>The NeuroColony Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 NeuroColony. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (data) => `
      Enable Overage Billing
      
      Hello ${data.name},
      
      You've reached your ${data.planType} plan limit for this month. To continue generating email sequences, you can enable overage billing.
      
      Overage Pricing: $${data.overageRate} per additional sequence
      
      You'll only pay for what you use beyond your plan limit. Overage charges are billed monthly.
      
      Enable overage billing from your dashboard: ${data.dashboardUrl}
      
      Best regards,
      The NeuroColony Team
    `
  }
}

// Create transporter
let transporter = null

const createTransporter = () => {
  if (!process.env.SMTP_HOST) {
    logger.warn('Email service not configured - SMTP_HOST missing')
    return null
  }

  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_PORT === '465', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  })
}

// Initialize transporter (skip verification to prevent startup blocking)
if (process.env.SMTP_HOST && process.env.EMAIL_ENABLED !== 'false') {
  transporter = createTransporter()
  logger.info('Email service configured (verification skipped for startup performance)')
} else {
  logger.info('Email service disabled or not configured')
}

export const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    if (!transporter) {
      logger.warn('Email service not available - skipping email send')
      return { success: false, message: 'Email service not configured' }
    }

    let emailHtml, emailText, emailSubject

    if (template && templates[template]) {
      emailSubject = subject || templates[template].subject
      emailHtml = templates[template].html(data)
      emailText = templates[template].text(data)
    } else {
      emailSubject = subject
      emailHtml = html
      emailText = text
    }

    const mailOptions = {
      from: {
        name: 'NeuroColony',
        address: process.env.FROM_EMAIL || '<EMAIL>'
      },
      to,
      subject: emailSubject,
      html: emailHtml,
      text: emailText
    }

    const result = await transporter.sendMail(mailOptions)
    
    logger.info('Email sent successfully:', {
      to,
      subject: emailSubject,
      messageId: result.messageId
    })

    return { success: true, messageId: result.messageId }

  } catch (error) {
    logger.error('Email send error:', {
      error: error.message,
      to,
      subject
    })
    
    throw error
  }
}

export const sendBulkEmails = async (emails) => {
  const results = []
  
  for (const email of emails) {
    try {
      const result = await sendEmail(email)
      results.push({ ...email, success: true, result })
    } catch (error) {
      results.push({ ...email, success: false, error: error.message })
    }
  }
  
  return results
}

export default { sendEmail, sendBulkEmails }