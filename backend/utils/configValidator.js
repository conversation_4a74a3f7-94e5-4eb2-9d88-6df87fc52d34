/**
 * NeuroColony Configuration Validator
 * Prevents deployment issues by validating environment configuration
 */

import mongoose from 'mongoose'
import Redis from 'ioredis'
import { logger } from './logger.js'

export class ConfigValidator {
  constructor() {
    this.errors = []
    this.warnings = []
    this.config = {
      port: process.env.PORT || 5000,
      mongoUri: process.env.MONGODB_URI,
      redisUrl: process.env.REDIS_URL,
      jwtSecret: process.env.JWT_SECRET,
      openaiKey: process.env.OPENAI_API_KEY,
      stripeKey: process.env.STRIPE_SECRET_KEY,
      frontendUrl: process.env.FRONTEND_URL
    }
  }

  /**
   * Validate all critical configuration
   */
  async validateAll() {
    logger.info('🔍 Starting configuration validation...')
    
    this.validateRequired()
    this.validateSecrets()
    await this.validateConnections()
    
    return this.getReport()
  }

  /**
   * Validate required environment variables
   */
  validateRequired() {
    const required = ['MONGODB_URI', 'JWT_SECRET', 'REDIS_URL']
    
    for (const key of required) {
      if (!process.env[key]) {
        this.errors.push(`Missing required environment variable: ${key}`)
      }
    }

    // Check for Docker vs localhost mismatch
    if (this.config.mongoUri?.includes('mongodb:27017')) {
      this.warnings.push('MongoDB URI uses Docker hostname "mongodb" - ensure containers are running')
    }
    
    if (this.config.redisUrl?.includes('redis:6379')) {
      this.warnings.push('Redis URL uses Docker hostname "redis" - ensure containers are running')
    }
  }

  /**
   * Validate secrets and API keys
   */
  validateSecrets() {
    // JWT Secret strength
    if (this.config.jwtSecret && this.config.jwtSecret.length < 32) {
      this.warnings.push('JWT secret should be at least 32 characters for security')
    }

    // OpenAI API Key format
    if (this.config.openaiKey && !this.config.openaiKey.startsWith('sk-')) {
      this.warnings.push('OpenAI API key format appears invalid (should start with sk-)')
    }

    // Stripe Key format
    if (this.config.stripeKey && !this.config.stripeKey.startsWith('sk_')) {
      this.warnings.push('Stripe secret key format appears invalid (should start with sk_)')
    }
  }

  /**
   * Test actual connections to external services
   */
  async validateConnections() {
    // Test MongoDB
    if (this.config.mongoUri) {
      try {
        await mongoose.connect(this.config.mongoUri, { serverSelectionTimeoutMS: 5000 })
        logger.info('✅ MongoDB connection validated')
        await mongoose.disconnect()
      } catch (error) {
        this.errors.push(`MongoDB connection failed: ${error.message}`)
      }
    }

    // Test Redis
    if (this.config.redisUrl) {
      try {
        const redis = new Redis(this.config.redisUrl, { 
          connectTimeout: 5000,
          lazyConnect: true 
        })
        await redis.connect()
        await redis.ping()
        logger.info('✅ Redis connection validated')
        await redis.disconnect()
      } catch (error) {
        this.errors.push(`Redis connection failed: ${error.message}`)
      }
    }
  }

  /**
   * Get validation report
   */
  getReport() {
    const hasErrors = this.errors.length > 0
    const hasWarnings = this.warnings.length > 0

    const report = {
      valid: !hasErrors,
      errors: this.errors,
      warnings: this.warnings,
      summary: {
        status: hasErrors ? '❌ FAILED' : hasWarnings ? '⚠️ WARNINGS' : '✅ PASSED',
        errorCount: this.errors.length,
        warningCount: this.warnings.length
      }
    }

    // Log report
    if (hasErrors) {
      logger.error('❌ Configuration validation failed', report)
    } else if (hasWarnings) {
      logger.warn('⚠️ Configuration validation passed with warnings', report)
    } else {
      logger.info('✅ Configuration validation passed', report)
    }

    return report
  }

  /**
   * Auto-fix common configuration issues
   */
  autoFix() {
    const fixes = []

    // Fix Docker hostname issues
    if (this.config.mongoUri?.includes('mongodb:27017')) {
      const fixed = this.config.mongoUri.replace('mongodb:27017', 'localhost:27018')
      fixes.push(`MongoDB URI: ${this.config.mongoUri} → ${fixed}`)
      process.env.MONGODB_URI = fixed
    }

    if (this.config.redisUrl?.includes('redis:6379')) {
      const fixed = this.config.redisUrl.replace('redis:6379', 'localhost:6380')
      fixes.push(`Redis URL: ${this.config.redisUrl} → ${fixed}`)
      process.env.REDIS_URL = fixed
    }

    return fixes
  }
}

export default ConfigValidator