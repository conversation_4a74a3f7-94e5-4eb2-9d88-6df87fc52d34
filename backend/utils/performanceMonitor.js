/**
 * Performance Monitor - Real-time performance tracking and optimization
 * Provides comprehensive metrics for identifying and resolving bottlenecks
 */

import { EventEmitter } from 'events'
import { logger } from './logger.js'

class PerformanceMonitor extends EventEmitter {
  constructor() {
    super()
    
    // Performance metrics storage
    this.metrics = {
      requests: new Map(),
      database: new Map(),
      cache: new Map(),
      ai: new Map(),
      memory: [],
      cpu: []
    }

    // Aggregated statistics
    this.stats = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        avgResponseTime: 0,
        slowQueries: 0
      },
      database: {
        queries: 0,
        avgQueryTime: 0,
        slowQueries: 0,
        connectionPool: 0
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      ai: {
        requests: 0,
        avgGenerationTime: 0,
        cacheHits: 0,
        totalCost: 0
      }
    }

    // Performance thresholds
    this.thresholds = {
      slowRequest: 1000, // 1 second
      slowDbQuery: 500,  // 500ms
      slowAIGeneration: 5000, // 5 seconds
      highMemoryUsage: 500 * 1024 * 1024, // 500MB
      highCpuUsage: 80 // 80%
    }

    // Start monitoring
    this.startMonitoring()
  }

  /**
   * Track HTTP request performance
   */
  trackRequest(routeName, method, startTime, endTime, success = true, error = null) {
    const duration = endTime - startTime
    const timestamp = new Date()

    const requestMetric = {
      routeName,
      method,
      duration,
      timestamp,
      success,
      error: error?.message || null
    }

    // Store in metrics
    const key = `${method}:${routeName}`
    if (!this.metrics.requests.has(key)) {
      this.metrics.requests.set(key, [])
    }
    this.metrics.requests.get(key).push(requestMetric)

    // Update aggregated stats
    this.stats.requests.total++
    if (success) {
      this.stats.requests.successful++
    } else {
      this.stats.requests.failed++
    }

    // Update average response time
    this.stats.requests.avgResponseTime = 
      (this.stats.requests.avgResponseTime * (this.stats.requests.total - 1) + duration) / 
      this.stats.requests.total

    // Track slow requests
    if (duration > this.thresholds.slowRequest) {
      this.stats.requests.slowQueries++
      logger.warn(`🐌 Slow request detected: ${method} ${routeName} took ${duration}ms`)
      this.emit('slowRequest', requestMetric)
    }

    // Cleanup old metrics (keep last 1000 per route)
    const routeMetrics = this.metrics.requests.get(key)
    if (routeMetrics.length > 1000) {
      routeMetrics.splice(0, routeMetrics.length - 1000)
    }

    return requestMetric
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(operation, collection, startTime, endTime, success = true, error = null) {
    const duration = endTime - startTime
    const timestamp = new Date()

    const dbMetric = {
      operation,
      collection,
      duration,
      timestamp,
      success,
      error: error?.message || null
    }

    // Store in metrics
    const key = `${operation}:${collection}`
    if (!this.metrics.database.has(key)) {
      this.metrics.database.set(key, [])
    }
    this.metrics.database.get(key).push(dbMetric)

    // Update aggregated stats
    this.stats.database.queries++
    this.stats.database.avgQueryTime = 
      (this.stats.database.avgQueryTime * (this.stats.database.queries - 1) + duration) / 
      this.stats.database.queries

    // Track slow queries
    if (duration > this.thresholds.slowDbQuery) {
      this.stats.database.slowQueries++
      logger.warn(`🐌 Slow DB query detected: ${operation} on ${collection} took ${duration}ms`)
      this.emit('slowDbQuery', dbMetric)
    }

    // Cleanup old metrics
    const queryMetrics = this.metrics.database.get(key)
    if (queryMetrics.length > 500) {
      queryMetrics.splice(0, queryMetrics.length - 500)
    }

    return dbMetric
  }

  /**
   * Track cache performance
   */
  trackCacheOperation(operation, key, hit = false, duration = 0) {
    const timestamp = new Date()

    const cacheMetric = {
      operation,
      key,
      hit,
      duration,
      timestamp
    }

    // Store in metrics
    if (!this.metrics.cache.has(operation)) {
      this.metrics.cache.set(operation, [])
    }
    this.metrics.cache.get(operation).push(cacheMetric)

    // Update aggregated stats
    if (hit) {
      this.stats.cache.hits++
    } else {
      this.stats.cache.misses++
    }

    this.stats.cache.hitRate = 
      this.stats.cache.hits / (this.stats.cache.hits + this.stats.cache.misses)

    // Cleanup old metrics
    const cacheMetrics = this.metrics.cache.get(operation)
    if (cacheMetrics.length > 1000) {
      cacheMetrics.splice(0, cacheMetrics.length - 1000)
    }

    return cacheMetric
  }

  /**
   * Track AI generation performance
   */
  trackAIGeneration(provider, model, startTime, endTime, tokensUsed = 0, cost = 0, cached = false) {
    const duration = endTime - startTime
    const timestamp = new Date()

    const aiMetric = {
      provider,
      model,
      duration,
      tokensUsed,
      cost,
      cached,
      timestamp
    }

    // Store in metrics
    const key = `${provider}:${model}`
    if (!this.metrics.ai.has(key)) {
      this.metrics.ai.set(key, [])
    }
    this.metrics.ai.get(key).push(aiMetric)

    // Update aggregated stats
    this.stats.ai.requests++
    this.stats.ai.avgGenerationTime = 
      (this.stats.ai.avgGenerationTime * (this.stats.ai.requests - 1) + duration) / 
      this.stats.ai.requests
    this.stats.ai.totalCost += cost

    if (cached) {
      this.stats.ai.cacheHits++
    }

    // Track slow AI generations
    if (duration > this.thresholds.slowAIGeneration) {
      logger.warn(`🐌 Slow AI generation detected: ${provider}:${model} took ${duration}ms`)
      this.emit('slowAIGeneration', aiMetric)
    }

    // Cleanup old metrics
    const aiMetrics = this.metrics.ai.get(key)
    if (aiMetrics.length > 200) {
      aiMetrics.splice(0, aiMetrics.length - 200)
    }

    return aiMetric
  }

  /**
   * Track system resource usage
   */
  trackSystemResources() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    const timestamp = new Date()

    const memoryMetric = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      timestamp
    }

    const cpuMetric = {
      user: cpuUsage.user,
      system: cpuUsage.system,
      timestamp
    }

    // Store metrics
    this.metrics.memory.push(memoryMetric)
    this.metrics.cpu.push(cpuMetric)

    // Check thresholds
    if (memUsage.heapUsed > this.thresholds.highMemoryUsage) {
      logger.warn(`🚨 High memory usage detected: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`)
      this.emit('highMemoryUsage', memoryMetric)
    }

    // Cleanup old metrics (keep last 1000 samples)
    if (this.metrics.memory.length > 1000) {
      this.metrics.memory.splice(0, this.metrics.memory.length - 1000)
    }
    if (this.metrics.cpu.length > 1000) {
      this.metrics.cpu.splice(0, this.metrics.cpu.length - 1000)
    }

    return { memory: memoryMetric, cpu: cpuMetric }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

    // Get recent metrics
    const recentRequests = this.getRecentMetrics(this.metrics.requests, oneHourAgo)
    const recentDbQueries = this.getRecentMetrics(this.metrics.database, oneHourAgo)
    const recentMemory = this.metrics.memory.filter(m => m.timestamp >= oneHourAgo)

    return {
      overview: {
        timestamp: now,
        period: '1 hour',
        status: this.getOverallStatus()
      },
      requests: {
        ...this.stats.requests,
        recentCount: this.countRecentMetrics(recentRequests),
        topSlowRoutes: this.getTopSlowRoutes(recentRequests)
      },
      database: {
        ...this.stats.database,
        recentQueries: this.countRecentMetrics(recentDbQueries),
        topSlowQueries: this.getTopSlowQueries(recentDbQueries)
      },
      cache: this.stats.cache,
      ai: this.stats.ai,
      memory: {
        current: this.getCurrentMemoryUsage(),
        peak: this.getPeakMemoryUsage(recentMemory),
        trend: this.getMemoryTrend(recentMemory)
      },
      recommendations: this.generateRecommendations()
    }
  }

  /**
   * Get recent metrics from a Map of metric arrays
   */
  getRecentMetrics(metricsMap, since) {
    const recent = new Map()
    
    for (const [key, metrics] of metricsMap) {
      const recentMetrics = metrics.filter(m => m.timestamp >= since)
      if (recentMetrics.length > 0) {
        recent.set(key, recentMetrics)
      }
    }

    return recent
  }

  /**
   * Count total recent metrics
   */
  countRecentMetrics(metricsMap) {
    let total = 0
    for (const metrics of metricsMap.values()) {
      total += metrics.length
    }
    return total
  }

  /**
   * Get top slow routes
   */
  getTopSlowRoutes(recentRequests, limit = 5) {
    const routeStats = new Map()

    for (const [route, metrics] of recentRequests) {
      const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length
      const slowCount = metrics.filter(m => m.duration > this.thresholds.slowRequest).length
      
      routeStats.set(route, {
        route,
        avgDuration: Math.round(avgDuration),
        slowCount,
        totalCount: metrics.length,
        slowPercentage: Math.round((slowCount / metrics.length) * 100)
      })
    }

    return Array.from(routeStats.values())
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, limit)
  }

  /**
   * Get top slow database queries
   */
  getTopSlowQueries(recentDbQueries, limit = 5) {
    const queryStats = new Map()

    for (const [operation, metrics] of recentDbQueries) {
      const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length
      const slowCount = metrics.filter(m => m.duration > this.thresholds.slowDbQuery).length
      
      queryStats.set(operation, {
        operation,
        avgDuration: Math.round(avgDuration),
        slowCount,
        totalCount: metrics.length,
        slowPercentage: Math.round((slowCount / metrics.length) * 100)
      })
    }

    return Array.from(queryStats.values())
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, limit)
  }

  /**
   * Get current memory usage
   */
  getCurrentMemoryUsage() {
    const mem = process.memoryUsage()
    return {
      heapUsed: Math.round(mem.heapUsed / 1024 / 1024),
      heapTotal: Math.round(mem.heapTotal / 1024 / 1024),
      external: Math.round(mem.external / 1024 / 1024),
      rss: Math.round(mem.rss / 1024 / 1024)
    }
  }

  /**
   * Get peak memory usage
   */
  getPeakMemoryUsage(recentMemory) {
    if (recentMemory.length === 0) return null

    const peak = recentMemory.reduce((max, current) => 
      current.heapUsed > max.heapUsed ? current : max
    )

    return {
      heapUsed: Math.round(peak.heapUsed / 1024 / 1024),
      timestamp: peak.timestamp
    }
  }

  /**
   * Get memory trend
   */
  getMemoryTrend(recentMemory) {
    if (recentMemory.length < 2) return 'stable'

    const first = recentMemory[0].heapUsed
    const last = recentMemory[recentMemory.length - 1].heapUsed
    const change = ((last - first) / first) * 100

    if (change > 10) return 'increasing'
    if (change < -10) return 'decreasing'
    return 'stable'
  }

  /**
   * Get overall system status
   */
  getOverallStatus() {
    const errorRate = this.stats.requests.failed / this.stats.requests.total || 0
    const slowRequestRate = this.stats.requests.slowQueries / this.stats.requests.total || 0
    const memUsage = process.memoryUsage().heapUsed

    if (errorRate > 0.1 || slowRequestRate > 0.2 || memUsage > this.thresholds.highMemoryUsage) {
      return 'critical'
    }
    if (errorRate > 0.05 || slowRequestRate > 0.1) {
      return 'warning'
    }
    return 'healthy'
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = []

    // Check cache hit rate
    if (this.stats.cache.hitRate < 0.6) {
      recommendations.push({
        type: 'cache',
        priority: 'high',
        message: `Cache hit rate is low (${Math.round(this.stats.cache.hitRate * 100)}%). Consider optimizing cache keys and TTL values.`
      })
    }

    // Check average response time
    if (this.stats.requests.avgResponseTime > 500) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: `Average response time is ${Math.round(this.stats.requests.avgResponseTime)}ms. Consider optimizing slow routes.`
      })
    }

    // Check database performance
    if (this.stats.database.avgQueryTime > 100) {
      recommendations.push({
        type: 'database',
        priority: 'medium',
        message: `Average database query time is ${Math.round(this.stats.database.avgQueryTime)}ms. Consider adding indexes or optimizing queries.`
      })
    }

    // Check AI costs
    if (this.stats.ai.totalCost > 10 && this.stats.ai.cacheHits / this.stats.ai.requests < 0.4) {
      recommendations.push({
        type: 'ai',
        priority: 'high',
        message: `AI costs are high ($${this.stats.ai.totalCost.toFixed(2)}) with low cache hit rate. Implement more aggressive caching.`
      })
    }

    return recommendations
  }

  /**
   * Start automated monitoring
   */
  startMonitoring() {
    // Track system resources every 30 seconds
    setInterval(() => {
      this.trackSystemResources()
    }, 30000)

    // Generate hourly reports
    setInterval(() => {
      const summary = this.getPerformanceSummary()
      logger.info('📊 Hourly Performance Report:', summary.overview)
      
      if (summary.overview.status === 'critical') {
        this.emit('criticalPerformance', summary)
      }
    }, 3600000) // 1 hour

    logger.info('📊 Performance monitoring started')
  }

  /**
   * Reset all metrics (for testing)
   */
  resetMetrics() {
    this.metrics = {
      requests: new Map(),
      database: new Map(),
      cache: new Map(),
      ai: new Map(),
      memory: [],
      cpu: []
    }

    this.stats = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        avgResponseTime: 0,
        slowQueries: 0
      },
      database: {
        queries: 0,
        avgQueryTime: 0,
        slowQueries: 0,
        connectionPool: 0
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      ai: {
        requests: 0,
        avgGenerationTime: 0,
        cacheHits: 0,
        totalCost: 0
      }
    }
  }
}

// Export singleton instance
export default new PerformanceMonitor()