import { logger } from './logger.js'

/**
 * Port Configuration Validator
 * Ensures all services use consistent, non-conflicting ports
 */
class PortValidator {
  constructor() {
    this.portConfig = {
      backend: {
        expected: 5000,
        env: 'PORT'
      },
      mongodb: {
        expected: 27018,
        env: 'MONGODB_URI',
        internal: 27017
      },
      redis: {
        expected: 6380, 
        env: 'REDIS_URL',
        internal: 6379
      },
      frontend: {
        expected: 3001,
        env: 'FRONTEND_URL'
      },
      nginx: {
        expected: 8080,
        https: 8443
      }
    }
  }

  /**
   * Validate all port configurations
   */
  validatePorts() {
    const results = {
      valid: true,
      conflicts: [],
      misconfigurations: [],
      recommendations: []
    }

    try {
      // Check backend port
      const backendPort = parseInt(process.env.PORT) || 5000
      if (backendPort !== this.portConfig.backend.expected) {
        results.misconfigurations.push({
          service: 'backend',
          expected: this.portConfig.backend.expected,
          actual: backendPort,
          severity: 'warning'
        })
      }

      // Check MongoDB URI port
      if (process.env.MONGODB_URI) {
        const mongoMatch = process.env.MONGODB_URI.match(/:(\d+)\//)
        if (mongoMatch) {
          const mongoPort = parseInt(mongoMatch[1])
          if (mongoPort !== this.portConfig.mongodb.expected) {
            results.misconfigurations.push({
              service: 'mongodb',
              expected: this.portConfig.mongodb.expected,
              actual: mongoPort,
              severity: 'error'
            })
            results.valid = false
          }
        }
      }

      // Check Redis URL port
      if (process.env.REDIS_URL) {
        const redisMatch = process.env.REDIS_URL.match(/:(\d+)$/)
        if (redisMatch) {
          const redisPort = parseInt(redisMatch[1])
          if (redisPort !== this.portConfig.redis.expected) {
            results.misconfigurations.push({
              service: 'redis',
              expected: this.portConfig.redis.expected,
              actual: redisPort,
              severity: 'error'
            })
            results.valid = false
          }
        }
      }

      // Check for port conflicts
      const usedPorts = new Set()
      const services = []

      services.push({ name: 'backend', port: backendPort })
      
      if (process.env.MONGODB_URI && process.env.MONGODB_URI.includes('localhost')) {
        services.push({ name: 'mongodb', port: this.portConfig.mongodb.expected })
      }
      
      if (process.env.REDIS_URL && process.env.REDIS_URL.includes('localhost')) {
        services.push({ name: 'redis', port: this.portConfig.redis.expected })
      }

      for (const service of services) {
        if (usedPorts.has(service.port)) {
          results.conflicts.push({
            port: service.port,
            services: services.filter(s => s.port === service.port).map(s => s.name),
            severity: 'error'
          })
          results.valid = false
        } else {
          usedPorts.add(service.port)
        }
      }

      // Generate recommendations
      if (results.misconfigurations.length > 0) {
        results.recommendations.push('Update environment variables to use standard ports')
        results.recommendations.push('Restart services after port configuration changes')
      }

      if (results.conflicts.length > 0) {
        results.recommendations.push('Resolve port conflicts by updating docker-compose.yml')
        results.recommendations.push('Ensure no other services are using conflicting ports')
      }

      // Add success recommendations
      if (results.valid && results.misconfigurations.length === 0) {
        results.recommendations.push('Port configuration is optimal')
        results.recommendations.push('All services configured for standard development ports')
      }

    } catch (error) {
      logger.error('Port validation error:', error)
      results.valid = false
      results.misconfigurations.push({
        service: 'validator',
        error: error.message,
        severity: 'error'
      })
    }

    return results
  }

  /**
   * Get the current port mapping summary
   */
  getPortSummary() {
    return {
      backend: process.env.PORT || 5000,
      mongodb: this.extractPortFromUri(process.env.MONGODB_URI, 27018),
      redis: this.extractPortFromUri(process.env.REDIS_URL, 6380),
      frontend: this.extractPortFromUri(process.env.FRONTEND_URL, 3001),
      configured: {
        mongoUri: process.env.MONGODB_URI || 'not_configured',
        redisUri: process.env.REDIS_URL || 'not_configured',
        frontendUrl: process.env.FRONTEND_URL || 'not_configured'
      }
    }
  }

  /**
   * Extract port number from URI string
   */
  extractPortFromUri(uri, defaultPort) {
    if (!uri) return defaultPort
    
    const match = uri.match(/:(\d+)[\/\?]?/)
    return match ? parseInt(match[1]) : defaultPort
  }

  /**
   * Generate Docker Compose port validation
   */
  validateDockerCompose() {
    const expectedPorts = {
      'mongodb': '27018:27017',
      'redis': '6380:6379', 
      'backend': '5000:5000',
      'frontend': '3001:80',
      'nginx': ['8080:80', '8443:443']
    }

    return {
      expected: expectedPorts,
      status: 'validate_manually',
      note: 'Check docker-compose.yml ports section against these expected values'
    }
  }
}

export default PortValidator