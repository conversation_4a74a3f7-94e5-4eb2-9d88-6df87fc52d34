export const emailTemplates = {
  welcome: {
    category: 'Onboarding',
    templates: [
      {
        id: 'welcome-1',
        name: 'Warm Welcome',
        description: 'Friendly introduction with clear next steps',
        tags: ['onboarding', 'welcome', 'new-user'],
        structure: {
          subject: 'Welcome to {company}! Here\'s how to get started',
          preview: 'We\'re thrilled to have you join us...',
          sections: [
            {
              type: 'greeting',
              content: 'Hi {firstName},'
            },
            {
              type: 'introduction',
              content: 'Welcome to {company}! We\'re absolutely thrilled to have you join our community of {customerCount} {industry} professionals who are {mainBenefit}.'
            },
            {
              type: 'value-prop',
              content: 'Over the next few days, I\'ll show you exactly how to {primaryGoal} without {painPoint}.'
            },
            {
              type: 'cta',
              content: 'Get Started Now',
              link: '{onboardingLink}'
            }
          ]
        },
        variables: ['firstName', 'company', 'customerCount', 'industry', 'mainBenefit', 'primaryGoal', 'painPoint', 'onboardingLink']
      },
      {
        id: 'welcome-2',
        name: 'Quick Win Welcome',
        description: 'Gets users to immediate value',
        tags: ['onboarding', 'welcome', 'quick-win'],
        structure: {
          subject: '{firstName}, here\'s your 5-minute quick win',
          preview: 'Let me show you the fastest way to see results...',
          sections: [
            {
              type: 'greeting',
              content: 'Hey {firstName}!'
            },
            {
              type: 'hook',
              content: 'I know you\'re busy, so let me cut to the chase...'
            },
            {
              type: 'quick-win',
              content: 'In the next 5 minutes, you can {quickWin} that will immediately {immediateResult}.'
            },
            {
              type: 'steps',
              content: [
                'Step 1: {step1}',
                'Step 2: {step2}',
                'Step 3: {step3}'
              ]
            },
            {
              type: 'cta',
              content: 'Start Your Quick Win',
              link: '{quickWinLink}'
            }
          ]
        },
        variables: ['firstName', 'quickWin', 'immediateResult', 'step1', 'step2', 'step3', 'quickWinLink']
      }
    ]
  },
  
  nurture: {
    category: 'Lead Nurturing',
    templates: [
      {
        id: 'nurture-1',
        name: 'Educational Value',
        description: 'Provides value through education',
        tags: ['nurture', 'education', 'value'],
        structure: {
          subject: 'The surprising truth about {topic}',
          preview: 'Most people get this wrong...',
          sections: [
            {
              type: 'greeting',
              content: 'Hi {firstName},'
            },
            {
              type: 'hook',
              content: 'Did you know that {statistic} of {targetAudience} struggle with {problem}?'
            },
            {
              type: 'education',
              content: 'Here\'s what most people don\'t realize: {insight}'
            },
            {
              type: 'tips',
              content: [
                '{tip1}',
                '{tip2}',
                '{tip3}'
              ]
            },
            {
              type: 'soft-cta',
              content: 'Want to learn more? Check out our complete guide',
              link: '{resourceLink}'
            }
          ]
        },
        variables: ['firstName', 'topic', 'statistic', 'targetAudience', 'problem', 'insight', 'tip1', 'tip2', 'tip3', 'resourceLink']
      },
      {
        id: 'nurture-2',
        name: 'Story-Based Nurture',
        description: 'Uses storytelling to build connection',
        tags: ['nurture', 'story', 'engagement'],
        structure: {
          subject: 'How {customerName} achieved {result} in {timeframe}',
          preview: 'And how you can too...',
          sections: [
            {
              type: 'greeting',
              content: '{firstName},'
            },
            {
              type: 'story-intro',
              content: 'Let me tell you about {customerName}...'
            },
            {
              type: 'problem',
              content: 'Just {timeframe} ago, they were struggling with {problem}. Sound familiar?'
            },
            {
              type: 'solution',
              content: 'But then they discovered {solution}. The results? {result}'
            },
            {
              type: 'lesson',
              content: 'The key lesson here: {keyTakeaway}'
            },
            {
              type: 'cta',
              content: 'See How You Can Get Similar Results',
              link: '{caseStudyLink}'
            }
          ]
        },
        variables: ['firstName', 'customerName', 'result', 'timeframe', 'problem', 'solution', 'keyTakeaway', 'caseStudyLink']
      }
    ]
  },
  
  sales: {
    category: 'Sales & Conversion',
    templates: [
      {
        id: 'sales-1',
        name: 'Limited Time Offer',
        description: 'Creates urgency with time-sensitive offer',
        tags: ['sales', 'urgency', 'conversion'],
        structure: {
          subject: '{firstName}, your {discount}% discount expires in 24 hours',
          preview: 'Don\'t miss out on this exclusive offer...',
          sections: [
            {
              type: 'greeting',
              content: '{firstName},'
            },
            {
              type: 'urgency',
              content: 'Quick heads up - the {discount}% discount on {product} expires tomorrow at midnight.'
            },
            {
              type: 'value-reminder',
              content: 'Remember, {product} helps you {mainBenefit} without {painPoint}.'
            },
            {
              type: 'social-proof',
              content: 'Join {customerCount} {targetAudience} who are already {successMetric}.'
            },
            {
              type: 'guarantee',
              content: 'Plus, you\'re protected by our {guarantee}.'
            },
            {
              type: 'cta',
              content: 'Claim Your {discount}% Discount',
              link: '{offerLink}'
            }
          ]
        },
        variables: ['firstName', 'discount', 'product', 'mainBenefit', 'painPoint', 'customerCount', 'targetAudience', 'successMetric', 'guarantee', 'offerLink']
      },
      {
        id: 'sales-2',
        name: 'Problem-Agitate-Solve',
        description: 'Classic PAS framework for conversion',
        tags: ['sales', 'pas', 'psychology'],
        structure: {
          subject: 'Still struggling with {problem}?',
          preview: 'There\'s a better way...',
          sections: [
            {
              type: 'greeting',
              content: '{firstName},'
            },
            {
              type: 'problem',
              content: 'Are you tired of {problem}?'
            },
            {
              type: 'agitate',
              content: 'It\'s frustrating, isn\'t it? {agitation}. And the worst part? {consequence}.'
            },
            {
              type: 'solution',
              content: 'That\'s exactly why we created {product}. It {solution} so you can finally {desiredOutcome}.'
            },
            {
              type: 'proof',
              content: '"{testimonial}" - {customerName}, {customerTitle}'
            },
            {
              type: 'cta',
              content: 'End Your {problem} Today',
              link: '{salesLink}'
            }
          ]
        },
        variables: ['firstName', 'problem', 'agitation', 'consequence', 'product', 'solution', 'desiredOutcome', 'testimonial', 'customerName', 'customerTitle', 'salesLink']
      }
    ]
  },
  
  retention: {
    category: 'Customer Retention',
    templates: [
      {
        id: 'retention-1',
        name: 'Check-in & Support',
        description: 'Proactive customer success check-in',
        tags: ['retention', 'support', 'success'],
        structure: {
          subject: '{firstName}, how\'s everything going with {product}?',
          preview: 'We\'re here to help you succeed...',
          sections: [
            {
              type: 'greeting',
              content: 'Hi {firstName},'
            },
            {
              type: 'check-in',
              content: 'It\'s been {timeframe} since you started using {product}, and I wanted to personally check in.'
            },
            {
              type: 'value-reminder',
              content: 'By now, you should be {expectedResult}. Are you seeing those results?'
            },
            {
              type: 'support-offer',
              content: 'If you need any help or have questions, I\'m here for you. You can:'
            },
            {
              type: 'options',
              content: [
                'Book a free success call with me',
                'Check out our advanced tutorials',
                'Join our community for peer support'
              ]
            },
            {
              type: 'cta',
              content: 'Schedule Your Success Call',
              link: '{calendlyLink}'
            }
          ]
        },
        variables: ['firstName', 'product', 'timeframe', 'expectedResult', 'calendlyLink']
      }
    ]
  },
  
  winback: {
    category: 'Win-Back Campaigns',
    templates: [
      {
        id: 'winback-1',
        name: 'We Miss You',
        description: 'Re-engages inactive users',
        tags: ['winback', 're-engagement', 'retention'],
        structure: {
          subject: '{firstName}, we miss you (and have something special for you)',
          preview: 'Come back to a special surprise...',
          sections: [
            {
              type: 'greeting',
              content: 'Hey {firstName},'
            },
            {
              type: 'acknowledgment',
              content: 'We noticed you haven\'t used {product} in a while, and honestly? We miss having you around.'
            },
            {
              type: 'updates',
              content: 'Since you\'ve been gone, we\'ve added {newFeature} that makes it even easier to {benefit}.'
            },
            {
              type: 'incentive',
              content: 'To welcome you back, here\'s {incentive} - no strings attached.'
            },
            {
              type: 'cta',
              content: 'Claim Your Welcome Back Gift',
              link: '{winbackLink}'
            }
          ]
        },
        variables: ['firstName', 'product', 'newFeature', 'benefit', 'incentive', 'winbackLink']
      }
    ]
  }
}

export const getTemplateById = (templateId) => {
  for (const category of Object.values(emailTemplates)) {
    const template = category.templates.find(t => t.id === templateId)
    if (template) {
      return { ...template, category: category.category }
    }
  }
  return null
}

export const getTemplatesByCategory = (categoryName) => {
  const category = emailTemplates[categoryName.toLowerCase()]
  return category ? category.templates : []
}

export const getAllTemplates = () => {
  const allTemplates = []
  for (const [key, category] of Object.entries(emailTemplates)) {
    category.templates.forEach(template => {
      allTemplates.push({
        ...template,
        category: category.category,
        categoryKey: key
      })
    })
  }
  return allTemplates
}

export const searchTemplates = (query) => {
  const searchTerm = query.toLowerCase()
  const results = []
  
  for (const [key, category] of Object.entries(emailTemplates)) {
    category.templates.forEach(template => {
      if (
        template.name.toLowerCase().includes(searchTerm) ||
        template.description.toLowerCase().includes(searchTerm) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        category.category.toLowerCase().includes(searchTerm)
      ) {
        results.push({
          ...template,
          category: category.category,
          categoryKey: key
        })
      }
    })
  }
  
  return results
}