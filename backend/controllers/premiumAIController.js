import { PremiumAIService } from '../services/premiumAIService.js';
import User from '../models/User.js';
import EmailSequence from '../models/EmailSequence.js';

const premiumAI = new PremiumAIService();

/**
 * Get available AI providers for user's plan
 */
export const getAvailableProviders = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    const userPlan = user.subscriptionPlan || 'free';
    
    const providers = premiumAI.getAvailableProviders(userPlan);
    
    res.json({
      success: true,
      data: {
        currentPlan: userPlan,
        providers,
        upgradeInfo: {
          business: 'Unlock Claude 3 models with Business plan',
          enterprise: 'Get access to Gemini Pro with Enterprise plan'
        }
      }
    });
  } catch (error) {
    console.error('Error getting available providers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available providers'
    });
  }
};

/**
 * Generate sequence with premium AI
 */
export const generatePremiumSequence = async (req, res) => {
  try {
    const {
      prompt,
      provider = 'openai',
      model = 'gpt-4o',
      sequenceLength = 5,
      enhancedOptions = {}
    } = req.body;

    const user = await User.findById(req.user.id);
    const userPlan = user.subscriptionPlan || 'free';

    // Check if user has access to requested provider
    if (!premiumAI.hasAccess(provider, userPlan)) {
      const requiredPlan = premiumAI.getRequiredPlan(provider);
      return res.status(403).json({
        success: false,
        error: `Access to ${provider} requires ${requiredPlan} plan or higher`,
        upgrade: {
          currentPlan: userPlan,
          requiredPlan,
          upgradeUrl: '/pricing'
        }
      });
    }

    // Enhance prompt with advanced options for premium plans
    let enhancedPrompt = prompt;
    if (userPlan !== 'free' && Object.keys(enhancedOptions).length > 0) {
      enhancedPrompt = premiumAI.enhancePrompt(prompt, enhancedOptions);
    }

    // Generate sequence
    const result = await premiumAI.generateSequence(enhancedPrompt, {
      provider,
      model,
      userPlan,
      sequenceLength,
      temperature: req.body.temperature || 0.7,
      maxTokens: req.body.maxTokens || 4000
    });

    // Save sequence to database
    if (result.sequence && result.sequence.length > 0) {
      const emailSequence = new EmailSequence({
        userId: req.user.id,
        title: enhancedOptions.title || `AI Generated Sequence - ${provider}`,
        emails: result.sequence.map((email, index) => ({
          subject: email.subject,
          content: email.content,
          timing: email.timing || `Day ${index + 1}`,
          goal: email.goal || 'Engagement'
        })),
        aiProvider: provider,
        aiModel: model,
        generationPrompt: enhancedPrompt,
        aiAnalysis: {
          ...result.analytics,
          generationCost: result.usage?.estimatedCost || 0,
          tokensUsed: result.usage?.totalTokens || 0
        },
        businessInfo: enhancedOptions.industry ? {
          industry: enhancedOptions.industry,
          targetAudience: enhancedOptions.targetAudience
        } : undefined
      });

      await emailSequence.save();

      // Update user's AI usage for billing
      if (result.usage?.estimatedCost) {
        user.aiUsage = user.aiUsage || { totalCost: 0, monthlyRequests: 0 };
        user.aiUsage.totalCost += result.usage.estimatedCost;
        user.aiUsage.monthlyRequests += 1;
        await user.save();
      }

      res.json({
        success: true,
        data: {
          sequence: emailSequence,
          aiAnalysis: result.analytics,
          usage: result.usage,
          provider: result.provider
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Failed to generate sequence content',
        details: result.message || 'No sequence data returned'
      });
    }

  } catch (error) {
    console.error('Error generating premium sequence:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate sequence',
      details: error.message
    });
  }
};

/**
 * Generate A/B test subject line variants
 */
export const generateSubjectLineVariants = async (req, res) => {
  try {
    const { emailContent, provider = 'openai', model = 'gpt-4o', variantCount = 5 } = req.body;

    const user = await User.findById(req.user.id);
    const userPlan = user.subscriptionPlan || 'free';

    // Check access
    if (!premiumAI.hasAccess(provider, userPlan)) {
      return res.status(403).json({
        success: false,
        error: `A/B testing requires ${premiumAI.getRequiredPlan(provider)} plan or higher`
      });
    }

    const result = await premiumAI.generateSubjectLineVariants(emailContent, {
      provider,
      model,
      variantCount
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error generating subject line variants:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate subject line variants'
    });
  }
};

/**
 * Analyze and optimize existing sequence
 */
export const analyzeSequence = async (req, res) => {
  try {
    const { sequenceId } = req.params;
    const { provider = 'openai', model = 'gpt-4' } = req.body;

    const user = await User.findById(req.user.id);
    const userPlan = user.subscriptionPlan || 'free';

    // Free plan users get basic analysis only
    if (userPlan === 'free') {
      return res.status(403).json({
        success: false,
        error: 'Sequence analysis requires Pro plan or higher',
        upgrade: {
          feature: 'AI-powered sequence analysis',
          requiredPlan: 'pro',
          upgradeUrl: '/pricing'
        }
      });
    }

    const sequence = await EmailSequence.findOne({
      _id: sequenceId,
      userId: req.user.id
    });

    if (!sequence) {
      return res.status(404).json({
        success: false,
        error: 'Sequence not found'
      });
    }

    const analysis = await premiumAI.analyzeSequence(sequence.emails, {
      provider,
      model
    });

    // Save analysis to sequence
    sequence.aiAnalysis = {
      ...sequence.aiAnalysis,
      optimizationSuggestions: analysis.recommendations,
      overallScore: analysis.overallScore,
      predictedPerformance: analysis.predictedPerformance,
      lastAnalyzed: new Date()
    };
    await sequence.save();

    res.json({
      success: true,
      data: analysis
    });

  } catch (error) {
    console.error('Error analyzing sequence:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to analyze sequence'
    });
  }
};

/**
 * Get AI usage statistics for billing
 */
export const getAIUsageStats = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    const stats = await premiumAI.getUsageStats(req.user.id, timeframe);
    
    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error getting AI usage stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get usage statistics'
    });
  }
};

/**
 * Get model comparison and recommendations
 */
export const getModelRecommendations = async (req, res) => {
  try {
    const { useCase, industry, sequenceLength } = req.query;
    
    const user = await User.findById(req.user.id);
    const userPlan = user.subscriptionPlan || 'free';
    
    const availableProviders = premiumAI.getAvailableProviders(userPlan);
    
    // Generate recommendations based on use case
    const recommendations = {
      recommended: [],
      alternatives: [],
      reasoning: {}
    };

    // Logic for recommendations based on use case
    if (useCase === 'creative') {
      recommendations.recommended.push({
        provider: 'openai',
        model: 'gpt-4',
        reason: 'Best for creative and engaging content'
      });
      if (userPlan === 'business' || userPlan === 'enterprise') {
        recommendations.recommended.push({
          provider: 'anthropic',
          model: 'claude-3-opus',
          reason: 'Exceptional at nuanced, human-like writing'
        });
      }
    } else if (useCase === 'analytical') {
      recommendations.recommended.push({
        provider: 'openai',
        model: 'gpt-4-turbo',
        reason: 'Strong analytical and data-driven content'
      });
      if (userPlan === 'enterprise') {
        recommendations.recommended.push({
          provider: 'google',
          model: 'gemini-pro',
          reason: 'Advanced reasoning capabilities'
        });
      }
    }

    res.json({
      success: true,
      data: {
        recommendations,
        availableProviders,
        upgradeOptions: {
          business: 'Unlock Claude 3 models',
          enterprise: 'Get access to all premium models'
        }
      }
    });

  } catch (error) {
    console.error('Error getting model recommendations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get model recommendations'
    });
  }
};

export default {
  getAvailableProviders,
  generatePremiumSequence,
  generateSubjectLineVariants,
  analyzeSequence,
  getAIUsageStats,
  getModelRecommendations
};