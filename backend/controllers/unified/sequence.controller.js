/**
 * Unified Sequence Controller
 * Consolidates all sequence-related business logic
 */

const EmailSequence = require('../../models/EmailSequence');
const User = require('../../models/User');
const { aiService } = require('../../services/unified/ai.service');
const { analyticsService } = require('../../services/unified/analytics.service');
const { exportService } = require('../../services/unified/export.service');
const { cache } = require('../../config/redis');
const { logger } = require('../../utils/logger');

class SequenceController {
  /**
   * Generate AI-powered email sequence
   */
  async generateSequence(req, res) {
    try {
      const { topic, tone, length, targetAudience, goals, industry } = req.body;
      const userId = req.user.id;
      
      // Check user limits
      const user = await User.findById(userId);
      const usageCheck = await user.checkSequenceLimit();
      
      if (!usageCheck.allowed) {
        return res.status(403).json({
          error: 'Sequence limit reached',
          usage: usageCheck
        });
      }
      
      // Generate sequence using AI service
      const generatedContent = await aiService.generateEmailSequence({
        topic,
        tone: tone || 'professional',
        length: length || 5,
        targetAudience,
        goals,
        industry
      });
      
      // Create sequence document
      const sequence = new EmailSequence({
        userId,
        name: `${topic} - Email Sequence`,
        topic,
        tone,
        targetAudience,
        goals,
        emails: generatedContent.emails,
        metadata: {
          generatedBy: 'ai',
          aiModel: generatedContent.model,
          prompt: generatedContent.prompt
        }
      });
      
      await sequence.save();
      
      // Update user usage
      await user.incrementSequenceUsage();
      
      // Track analytics
      if (req.app.locals.analytics) {
        analyticsService.track('sequence_generated', {
          userId,
          sequenceId: sequence._id,
          method: 'ai',
          emailCount: sequence.emails.length
        });
      }
      
      // Clear user's sequence cache
      await cache.del(`sequences:${userId}:*`);
      
      res.status(201).json({
        success: true,
        sequence,
        usage: await user.getUsageStats()
      });
      
    } catch (error) {
      logger.error('Error generating sequence:', error);
      res.status(500).json({
        error: 'Failed to generate sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Create sequence manually
   */
  async createSequence(req, res) {
    try {
      const userId = req.user.id;
      const sequenceData = {
        ...req.body,
        userId
      };
      
      const sequence = new EmailSequence(sequenceData);
      await sequence.save();
      
      // Clear cache
      await cache.del(`sequences:${userId}:*`);
      
      res.status(201).json({
        success: true,
        sequence
      });
      
    } catch (error) {
      logger.error('Error creating sequence:', error);
      res.status(500).json({
        error: 'Failed to create sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Get all sequences for user
   */
  async getUserSequences(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10, status, sort = '-createdAt' } = req.query;
      
      const query = { userId };
      if (status) query.status = status;
      
      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        sort,
        populate: 'templateId'
      };
      
      const sequences = await EmailSequence.paginate(query, options);
      
      res.json({
        success: true,
        ...sequences
      });
      
    } catch (error) {
      logger.error('Error fetching sequences:', error);
      res.status(500).json({
        error: 'Failed to fetch sequences',
        message: error.message
      });
    }
  }
  
  /**
   * Get single sequence
   */
  async getSequenceById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId })
        .populate('templateId')
        .populate('workflowId');
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      res.json({
        success: true,
        sequence
      });
      
    } catch (error) {
      logger.error('Error fetching sequence:', error);
      res.status(500).json({
        error: 'Failed to fetch sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Update sequence
   */
  async updateSequence(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const updates = req.body;
      
      const sequence = await EmailSequence.findOneAndUpdate(
        { _id: id, userId },
        { ...updates, lastModified: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      // Clear cache
      await cache.del(`sequences:${userId}:*`);
      
      res.json({
        success: true,
        sequence
      });
      
    } catch (error) {
      logger.error('Error updating sequence:', error);
      res.status(500).json({
        error: 'Failed to update sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Delete sequence
   */
  async deleteSequence(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOneAndDelete({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      // Clear cache
      await cache.del(`sequences:${userId}:*`);
      
      res.json({
        success: true,
        message: 'Sequence deleted successfully'
      });
      
    } catch (error) {
      logger.error('Error deleting sequence:', error);
      res.status(500).json({
        error: 'Failed to delete sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Clone sequence
   */
  async cloneSequence(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const original = await EmailSequence.findOne({ _id: id, userId });
      
      if (!original) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const clonedData = original.toObject();
      delete clonedData._id;
      delete clonedData.createdAt;
      delete clonedData.updatedAt;
      
      const cloned = new EmailSequence({
        ...clonedData,
        name: `${clonedData.name} (Copy)`,
        status: 'draft'
      });
      
      await cloned.save();
      
      // Clear cache
      await cache.del(`sequences:${userId}:*`);
      
      res.status(201).json({
        success: true,
        sequence: cloned
      });
      
    } catch (error) {
      logger.error('Error cloning sequence:', error);
      res.status(500).json({
        error: 'Failed to clone sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Export sequence
   */
  async exportSequence(req, res) {
    try {
      const { id } = req.params;
      const { format = 'json' } = req.query;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const exported = await exportService.exportSequence(sequence, format);
      
      res.setHeader('Content-Type', exported.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${sequence.name}.${format}"`);
      res.send(exported.data);
      
    } catch (error) {
      logger.error('Error exporting sequence:', error);
      res.status(500).json({
        error: 'Failed to export sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Schedule sequence
   */
  async scheduleSequence(req, res) {
    try {
      const { id } = req.params;
      const { sendAt } = req.body;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOneAndUpdate(
        { _id: id, userId },
        { 
          scheduledAt: new Date(sendAt),
          status: 'scheduled'
        },
        { new: true }
      );
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      // Schedule job for sending
      // await jobQueue.schedule('send_sequence', { sequenceId: id }, sendAt);
      
      res.json({
        success: true,
        sequence
      });
      
    } catch (error) {
      logger.error('Error scheduling sequence:', error);
      res.status(500).json({
        error: 'Failed to schedule sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Get sequence analytics
   */
  async getSequenceAnalytics(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const analytics = await analyticsService.getSequenceAnalytics(id);
      
      res.json({
        success: true,
        analytics
      });
      
    } catch (error) {
      logger.error('Error fetching analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch analytics',
        message: error.message
      });
    }
  }
  
  /**
   * Bulk delete sequences
   */
  async bulkDeleteSequences(req, res) {
    try {
      const { ids } = req.body;
      const userId = req.user.id;
      
      const result = await EmailSequence.deleteMany({
        _id: { $in: ids },
        userId
      });
      
      // Clear cache
      await cache.del(`sequences:${userId}:*`);
      
      res.json({
        success: true,
        deleted: result.deletedCount
      });
      
    } catch (error) {
      logger.error('Error bulk deleting sequences:', error);
      res.status(500).json({
        error: 'Failed to delete sequences',
        message: error.message
      });
    }
  }
  
  /**
   * Bulk export sequences
   */
  async bulkExportSequences(req, res) {
    try {
      const { ids, format = 'zip' } = req.body;
      const userId = req.user.id;
      
      const sequences = await EmailSequence.find({
        _id: { $in: ids },
        userId
      });
      
      const exported = await exportService.bulkExportSequences(sequences, format);
      
      res.setHeader('Content-Type', exported.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="sequences-export.${format}"`);
      res.send(exported.data);
      
    } catch (error) {
      logger.error('Error bulk exporting sequences:', error);
      res.status(500).json({
        error: 'Failed to export sequences',
        message: error.message
      });
    }
  }
  
  /**
   * AI-powered sequence optimization
   */
  async optimizeSequence(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const optimized = await aiService.optimizeSequence(sequence);
      
      sequence.emails = optimized.emails;
      sequence.metadata.lastOptimized = new Date();
      sequence.metadata.optimizationSuggestions = optimized.suggestions;
      
      await sequence.save();
      
      res.json({
        success: true,
        sequence,
        improvements: optimized.improvements
      });
      
    } catch (error) {
      logger.error('Error optimizing sequence:', error);
      res.status(500).json({
        error: 'Failed to optimize sequence',
        message: error.message
      });
    }
  }
  
  /**
   * Get AI suggestions for sequence improvement
   */
  async getAISuggestions(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const suggestions = await aiService.analyzeSequence(sequence);
      
      res.json({
        success: true,
        suggestions
      });
      
    } catch (error) {
      logger.error('Error getting AI suggestions:', error);
      res.status(500).json({
        error: 'Failed to get suggestions',
        message: error.message
      });
    }
  }
  
  /**
   * Generate A/B test variations
   */
  async generateABTestVariations(req, res) {
    try {
      const { id } = req.params;
      const { variations = 2 } = req.body;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: id, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const variants = await aiService.generateABTestVariants(sequence, variations);
      
      res.json({
        success: true,
        variants
      });
      
    } catch (error) {
      logger.error('Error generating A/B test variations:', error);
      res.status(500).json({
        error: 'Failed to generate variations',
        message: error.message
      });
    }
  }
  
  /**
   * Get template library
   */
  async getTemplateLibrary(req, res) {
    try {
      const { category, industry } = req.query;
      
      const templates = await exportService.getTemplates({ category, industry });
      
      res.json({
        success: true,
        templates
      });
      
    } catch (error) {
      logger.error('Error fetching templates:', error);
      res.status(500).json({
        error: 'Failed to fetch templates',
        message: error.message
      });
    }
  }
  
  /**
   * Save sequence as template
   */
  async saveAsTemplate(req, res) {
    try {
      const { sequenceId, name, description, category } = req.body;
      const userId = req.user.id;
      
      const sequence = await EmailSequence.findOne({ _id: sequenceId, userId });
      
      if (!sequence) {
        return res.status(404).json({
          error: 'Sequence not found'
        });
      }
      
      const template = await exportService.createTemplate({
        sequence,
        name,
        description,
        category,
        createdBy: userId
      });
      
      res.status(201).json({
        success: true,
        template
      });
      
    } catch (error) {
      logger.error('Error saving template:', error);
      res.status(500).json({
        error: 'Failed to save template',
        message: error.message
      });
    }
  }
}

module.exports = new SequenceController();