import emailService from '../services/emailService.js';
import User from '../models/User.js';

/**
 * Handle support ticket submission
 */
export const submitSupportTicket = async (req, res) => {
  try {
    const {
      name,
      email,
      issueType,
      subject,
      message
    } = req.body;

    // Validation
    if (!name || !email || !issueType || !subject || !message) {
      return res.status(400).json({
        success: false,
        error: 'All fields are required'
      });
    }

    if (message.length > 2000) {
      return res.status(400).json({
        success: false,
        error: 'Message must be less than 2000 characters'
      });
    }

    // Get user info if authenticated
    let userId = null;
    if (req.user) {
      userId = req.user.id;
    }

    // Prepare ticket data
    const ticketData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      issueType,
      subject: subject.trim(),
      message: message.trim(),
      userId,
      timestamp: new Date().toISOString(),
      userAgent: req.headers['user-agent'] || 'Unknown'
    };

    // Send support ticket email
    await emailService.sendSupportTicket(ticketData);

    // Send auto-reply to customer
    await emailService.sendAutoReply(ticketData.email, ticketData.name, issueType);

    res.json({
      success: true,
      message: 'Your support ticket has been submitted successfully. We\'ll get back to you within 24 hours.'
    });

  } catch (error) {
    console.error('Error submitting support ticket:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit support ticket. Please try again later.'
    });
  }
};

/**
 * Handle general contact form submission
 */
export const submitContactForm = async (req, res) => {
  try {
    const {
      name,
      email,
      company,
      contactReason,
      message
    } = req.body;

    // Validation
    if (!name || !email || !contactReason || !message) {
      return res.status(400).json({
        success: false,
        error: 'Name, email, contact reason, and message are required'
      });
    }

    if (message.length > 2000) {
      return res.status(400).json({
        success: false,
        error: 'Message must be less than 2000 characters'
      });
    }

    // Prepare contact data
    const contactData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      company: company ? company.trim() : null,
      contactReason,
      message: message.trim(),
      timestamp: new Date().toISOString(),
      userAgent: req.headers['user-agent'] || 'Unknown'
    };

    // Send contact form email
    await emailService.sendContactForm(contactData);

    // Send auto-reply to contact
    await emailService.sendAutoReply(contactData.email, contactData.name, 'other');

    res.json({
      success: true,
      message: 'Thank you for your message! We\'ll respond to your inquiry soon.'
    });

  } catch (error) {
    console.error('Error submitting contact form:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send your message. Please try again later.'
    });
  }
};

/**
 * Get contact options and FAQs
 */
export const getContactInfo = async (req, res) => {
  try {
    const contactInfo = {
      supportCategories: [
        {
          id: 'billing',
          title: 'Billing & Payments',
          description: 'Questions about subscriptions, invoices, or payment issues',
          icon: '💳'
        },
        {
          id: 'technical',
          title: 'Technical Issues',
          description: 'Problems with the app, bugs, or functionality issues',
          icon: '🔧'
        },
        {
          id: 'account',
          title: 'Account Management',
          description: 'Profile settings, password resets, or account access',
          icon: '👤'
        },
        {
          id: 'features',
          title: 'Feature Requests',
          description: 'Suggestions for new features or improvements',
          icon: '✨'
        },
        {
          id: 'bug',
          title: 'Bug Reports',
          description: 'Report software bugs or unexpected behavior',
          icon: '🐛'
        },
        {
          id: 'integration',
          title: 'Integrations',
          description: 'Help with third-party integrations and APIs',
          icon: '🔗'
        },
        {
          id: 'performance',
          title: 'Performance Issues',
          description: 'Slow loading, timeouts, or performance problems',
          icon: '⚡'
        },
        {
          id: 'other',
          title: 'Other',
          description: 'Any other questions or concerns',
          icon: '💬'
        }
      ],
      contactReasons: [
        {
          id: 'sales',
          title: 'Sales Inquiry',
          description: 'Learn about pricing, plans, or schedule a demo'
        },
        {
          id: 'partnership',
          title: 'Partnership Opportunity',
          description: 'Discuss potential partnerships or collaborations'
        },
        {
          id: 'enterprise',
          title: 'Enterprise Solutions',
          description: 'Custom solutions for large organizations'
        },
        {
          id: 'feedback',
          title: 'Product Feedback',
          description: 'Share your thoughts on how we can improve'
        },
        {
          id: 'media',
          title: 'Media & Press',
          description: 'Press inquiries and media requests'
        },
        {
          id: 'other',
          title: 'Other',
          description: 'General inquiries and other topics'
        }
      ],
      faq: [
        {
          question: 'How do I upgrade my subscription?',
          answer: 'Go to your Dashboard > Billing to upgrade your plan instantly.'
        },
        {
          question: 'Can I cancel my subscription anytime?',
          answer: 'Yes, you can cancel anytime from your billing dashboard.'
        },
        {
          question: 'Do you offer refunds?',
          answer: 'We offer a 14-day free trial. Contact us for refund requests.'
        },
        {
          question: 'How do I export my sequences?',
          answer: 'Use the export feature in your sequence editor for multiple formats.'
        },
        {
          question: 'Is there an API available?',
          answer: 'Yes! API access is available for Business and Enterprise plans.'
        }
      ],
      responseTime: {
        free: '48-72 hours',
        pro: '24-48 hours',
        business: '12-24 hours',
        enterprise: '4-12 hours'
      }
    };

    res.json({
      success: true,
      data: contactInfo
    });

  } catch (error) {
    console.error('Error getting contact info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load contact information'
    });
  }
};

export default {
  submitSupportTicket,
  submitContactForm,
  getContactInfo
};