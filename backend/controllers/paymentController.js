import { stripe, PRICE_IDS, WEBHOOK_SECRET } from '../config/stripe.js';
import User from '../models/User.js';
import emailService from '../services/emailService.js';
// Note: Using console.log for now instead of logger since it may not exist

// Create checkout session for subscription
export const createCheckoutSession = async (req, res) => {
  try {
    const { plan, successUrl, cancelUrl } = req.body;
    const userId = req.user.id;

    // Validate plan
    if (!['pro', 'business', 'enterprise'].includes(plan)) {
      return res.status(400).json({ error: 'Invalid subscription plan' });
    }

    // Get price ID for the selected plan
    const priceId = PRICE_IDS[plan];

    // Create Stripe customer if not exists
    const user = await User.findById(userId);
    let customerId = user.stripeCustomerId;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: userId.toString()
        }
      });
      customerId = customer.id;
      user.stripeCustomerId = customerId;
      await user.save();
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [{
        price: priceId,
        quantity: 1
      }],
      mode: 'subscription',
      success_url: successUrl || `${process.env.FRONTEND_URL}/dashboard?payment=success`,
      cancel_url: cancelUrl || `${process.env.FRONTEND_URL}/pricing?payment=cancelled`,
      metadata: {
        userId: userId.toString(),
        plan
      },
      subscription_data: {
        trial_period_days: 14, // 14-day free trial
        metadata: {
          userId: userId.toString(),
          plan
        }
      },
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto'
      }
    });

    console.log(`Created checkout session for user ${userId}, plan: ${plan}`);

    res.json({ 
      sessionId: session.id,
      url: session.url 
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Failed to create checkout session' });
  }
};

// Create customer portal session
export const createPortalSession = async (req, res) => {
  try {
    const userId = req.user.id;
    const { returnUrl } = req.body;

    const user = await User.findById(userId);
    if (!user.stripeCustomerId) {
      return res.status(400).json({ error: 'No active subscription found' });
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: user.stripeCustomerId,
      return_url: returnUrl || `${process.env.FRONTEND_URL}/dashboard`
    });

    res.json({ url: session.url });
  } catch (error) {
    console.error('Error creating portal session:', error);
    res.status(500).json({ error: 'Failed to create portal session' });
  }
};

// Get subscription status
export const getSubscriptionStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findById(userId);

    if (!user.stripeCustomerId) {
      return res.json({ 
        hasActiveSubscription: false,
        plan: 'free',
        status: 'inactive'
      });
    }

    // Get active subscriptions
    const subscriptions = await stripe.subscriptions.list({
      customer: user.stripeCustomerId,
      status: 'active',
      limit: 1
    });

    if (subscriptions.data.length === 0) {
      return res.json({ 
        hasActiveSubscription: false,
        plan: 'free',
        status: 'inactive'
      });
    }

    const subscription = subscriptions.data[0];
    const plan = subscription.metadata.plan || 'pro';

    res.json({
      hasActiveSubscription: true,
      plan,
      status: subscription.status,
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null
    });
  } catch (error) {
    console.error('Error getting subscription status:', error);
    res.status(500).json({ error: 'Failed to get subscription status' });
  }
};

// Handle usage-based billing (for overage charges)
export const recordUsage = async (req, res) => {
  try {
    const userId = req.user.id;
    const { quantity, action } = req.body;

    const user = await User.findById(userId);
    if (!user.stripeCustomerId) {
      return res.status(400).json({ error: 'No active subscription found' });
    }

    // Get active subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: user.stripeCustomerId,
      status: 'active',
      limit: 1
    });

    if (subscriptions.data.length === 0) {
      return res.status(400).json({ error: 'No active subscription found' });
    }

    const subscription = subscriptions.data[0];
    const plan = subscription.metadata.plan || 'pro';

    // Check if user exceeded plan limits
    const planLimits = {
      free: 5,
      pro: 75,
      business: 200,
      enterprise: Infinity
    };

    const currentUsage = user.monthlyUsage || 0;
    const limit = planLimits[plan];

    if (currentUsage >= limit && plan !== 'enterprise') {
      // Create usage record for overage
      const subscriptionItem = subscription.items.data.find(item => 
        item.price.metadata.type === 'metered'
      );

      if (subscriptionItem) {
        await stripe.subscriptionItems.createUsageRecord(
          subscriptionItem.id,
          {
            quantity: quantity || 1,
            action: action || 'increment',
            timestamp: Math.floor(Date.now() / 1000)
          }
        );
      }
    }

    // Update user's monthly usage
    user.monthlyUsage = currentUsage + (quantity || 1);
    await user.save();

    res.json({ 
      success: true,
      currentUsage: user.monthlyUsage,
      limit
    });
  } catch (error) {
    console.error('Error recording usage:', error);
    res.status(500).json({ error: 'Failed to record usage' });
  }
};

// Webhook handler for Stripe events
export const handleWebhook = async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.rawBody, sig, WEBHOOK_SECRET);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdate(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
};

// Helper functions for webhook handlers
async function handleCheckoutSessionCompleted(session) {
  const userId = session.metadata.userId;
  const plan = session.metadata.plan;

  const user = await User.findById(userId);
  if (user) {
    user.subscriptionPlan = plan;
    user.subscriptionStatus = 'active';
    user.stripeCustomerId = session.customer;
    user.stripeSubscriptionId = session.subscription;
    await user.save();

    console.log(`Subscription activated for user ${userId}, plan: ${plan}`);
  }
}

async function handleSubscriptionUpdate(subscription) {
  const customerId = subscription.customer;
  const user = await User.findOne({ stripeCustomerId: customerId });
  
  if (user) {
    user.subscriptionStatus = subscription.status;
    user.subscriptionPlan = subscription.metadata.plan || user.subscriptionPlan;
    user.stripeSubscriptionId = subscription.id;
    await user.save();

    console.log(`Subscription updated for user ${user.id}, status: ${subscription.status}`);
  }
}

async function handleSubscriptionDeleted(subscription) {
  const customerId = subscription.customer;
  const user = await User.findOne({ stripeCustomerId: customerId });
  
  if (user) {
    const wasActive = user.subscriptionStatus === 'active';
    
    user.subscriptionStatus = 'cancelled';
    user.subscriptionPlan = 'free';
    user.stripeSubscriptionId = null;
    await user.save();

    console.log(`Subscription cancelled for user ${user.id}`);
    
    // Send cancellation notification only if subscription was previously active
    if (wasActive) {
      try {
        await emailService.sendSubscriptionCancelledNotification(user);
        console.log(`Subscription cancellation notification sent to user ${user.id}`);
      } catch (emailError) {
        console.error(`Failed to send cancellation notification to user ${user.id}:`, emailError);
        // Don't throw error - subscription processing should continue even if email fails
      }
    }
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  const customerId = invoice.customer;
  const user = await User.findOne({ stripeCustomerId: customerId });
  
  if (user) {
    // Reset monthly usage on successful payment
    user.monthlyUsage = 0;
    user.lastPaymentDate = new Date();
    user.lastPaymentAmount = invoice.amount_paid / 100; // Convert from cents
    await user.save();

    console.log(`Payment succeeded for user ${user.id}, amount: $${invoice.amount_paid / 100}`);
  }
}

async function handleInvoicePaymentFailed(invoice) {
  const customerId = invoice.customer;
  const user = await User.findOne({ stripeCustomerId: customerId });
  
  if (user) {
    user.paymentFailureCount = (user.paymentFailureCount || 0) + 1;
    await user.save();

    console.warn(`Payment failed for user ${user.id}, attempt: ${invoice.attempt_count}`);
    
    // Send payment failure email notification
    try {
      await emailService.sendPaymentFailureNotification(user, invoice);
      console.log(`Payment failure notification sent to user ${user.id}`);
    } catch (emailError) {
      console.error(`Failed to send payment failure notification to user ${user.id}:`, emailError);
      // Don't throw error - payment processing should continue even if email fails
    }
  }
}