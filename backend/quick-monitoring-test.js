// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Quick Monitoring System Test
// ====================================================================

import { setTimeout } from 'timers/promises';

console.log('🔥 Quick Monitoring System Test - Ultra Debug God Mode');
console.log('='.repeat(70));

async function testMonitoringComponents() {
    let passed = 0;
    let total = 0;

    // Test 1: Circuit Breaker
    total++;
    console.log('🧪 Testing Circuit Breaker...');
    try {
        const { default: circuitBreakerManager } = await import('./monitoring/circuitBreaker.js');
        
        const testBreaker = circuitBreakerManager.create('quick-test', {
            failureThreshold: 2,
            timeout: 5000
        });
        
        const result = await testBreaker.execute(async () => {
            return 'success';
        });
        
        if (result === 'success') {
            console.log('✅ Circuit Breaker: PASS');
            passed++;
        } else {
            console.log('❌ Circuit Breaker: FAIL');
        }
    } catch (error) {
        console.log(`❌ Circuit Breaker: ERROR - ${error.message}`);
    }

    // Test 2: Production Logger
    total++;
    console.log('🧪 Testing Production Logger...');
    try {
        const { getProductionLogger } = await import('./monitoring/productionLogger.js');
        
        const logger = getProductionLogger({ enableConsole: false });
        
        // Simple test
        logger.info('Test log message', { test: true });
        
        const stats = logger.getLogStats();
        if (stats && typeof stats === 'object') {
            console.log('✅ Production Logger: PASS');
            passed++;
        } else {
            console.log('❌ Production Logger: FAIL');
        }
    } catch (error) {
        console.log(`❌ Production Logger: ERROR - ${error.message}`);
    }

    // Test 3: System Validator
    total++;
    console.log('🧪 Testing System Validator...');
    try {
        const { default: SystemValidator } = await import('./monitoring/systemValidator.js');
        
        const validator = new SystemValidator();
        
        // Simple validation check
        const envCheck = validator.checkEnvironmentVariables();
        if (envCheck && envCheck.name === 'environment_variables') {
            console.log('✅ System Validator: PASS');
            passed++;
        } else {
            console.log('❌ System Validator: FAIL');
        }
    } catch (error) {
        console.log(`❌ System Validator: ERROR - ${error.message}`);
    }

    // Test 4: Health Monitor
    total++;
    console.log('🧪 Testing Health Monitor...');
    try {
        const { default: HealthMonitor } = await import('./monitoring/healthMonitor.js');
        
        const monitor = new HealthMonitor({
            checkInterval: 60000, // 1 minute for testing
            mongoUri: process.env.MONGODB_URI,
            redisUrl: process.env.REDIS_URL
        });
        
        // Test initialization
        if (monitor.metrics && monitor.config) {
            console.log('✅ Health Monitor: PASS');
            passed++;
        } else {
            console.log('❌ Health Monitor: FAIL');
        }
    } catch (error) {
        console.log(`❌ Health Monitor: ERROR - ${error.message}`);
    }

    // Test 5: Alert System
    total++;
    console.log('🧪 Testing Alert System...');
    try {
        const { default: AlertSystem } = await import('./monitoring/alertSystem.js');
        
        const alertSystem = new AlertSystem({
            emailEnabled: false, // Disable for testing
            webhookEnabled: false
        });
        
        const config = alertSystem.getConfig();
        if (config && typeof config === 'object') {
            console.log('✅ Alert System: PASS');
            passed++;
        } else {
            console.log('❌ Alert System: FAIL');
        }
    } catch (error) {
        console.log(`❌ Alert System: ERROR - ${error.message}`);
    }

    // Test 6: Self Healer
    total++;
    console.log('🧪 Testing Self Healer...');
    try {
        const { default: SelfHealer } = await import('./monitoring/selfHealer.js');
        
        const healer = new SelfHealer({
            healthCheckInterval: 60000 // 1 minute for testing
        });
        
        const stats = healer.getHealingStats();
        if (stats && typeof stats.totalActions === 'number') {
            console.log('✅ Self Healer: PASS');
            passed++;
        } else {
            console.log('❌ Self Healer: FAIL');
        }
    } catch (error) {
        console.log(`❌ Self Healer: ERROR - ${error.message}`);
    }

    // Test 7: Monitoring Dashboard
    total++;
    console.log('🧪 Testing Monitoring Dashboard...');
    try {
        const { default: MonitoringDashboard } = await import('./monitoring/monitoringDashboard.js');
        
        const dashboard = new MonitoringDashboard({
            port: 5003, // Use different port for testing
            enableWebSocket: false // Disable for testing
        });
        
        if (dashboard.app && dashboard.config) {
            console.log('✅ Monitoring Dashboard: PASS');
            passed++;
        } else {
            console.log('❌ Monitoring Dashboard: FAIL');
        }
    } catch (error) {
        console.log(`❌ Monitoring Dashboard: ERROR - ${error.message}`);
    }

    // Display Results
    console.log('\n' + '='.repeat(70));
    console.log('🏆 QUICK MONITORING TEST RESULTS');
    console.log('='.repeat(70));
    
    const passRate = ((passed / total) * 100).toFixed(1);
    
    console.log(`📊 Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${total - passed}`);
    console.log(`📈 Pass Rate: ${passRate}%`);
    
    if (passRate >= 90) {
        console.log('\n🎉 EXCELLENT! Monitoring components are working great!');
    } else if (passRate >= 70) {
        console.log('\n👍 GOOD! Most monitoring components are functional.');
    } else if (passRate >= 50) {
        console.log('\n⚠️ WARNING! Some monitoring components need attention.');
    } else {
        console.log('\n🚨 CRITICAL! Major issues with monitoring components.');
    }
    
    console.log('='.repeat(70));
    
    return passRate >= 70;
}

// Run the test
testMonitoringComponents()
    .then((success) => {
        console.log('\n🎯 Quick monitoring test complete!');
        
        if (success) {
            console.log('🚀 Monitoring system is ready for Phase 4 deployment!');
        } else {
            console.log('🔧 Monitoring system needs fixes before deployment.');
        }
        
        process.exit(success ? 0 : 1);
    })
    .catch((error) => {
        console.error('💥 Quick test failed:', error);
        process.exit(1);
    });