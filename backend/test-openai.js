import OpenAI from 'openai'
import dotenv from 'dotenv'

dotenv.config()

const openai = new OpenAI({
  apiKey: 'sk-be1d24ea03bf4ace831cb19d4ne21856'
})

async function testOpenAI() {
  try {
    console.log('🔄 Testing OpenAI API...')
    console.log('API Key starts with:', process.env.OPENAI_API_KEY?.substring(0, 10))
    
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Say "API Works!"' }],
      max_tokens: 10
    })
    
    console.log('✅ Success! Response:', response.choices[0].message.content)
    process.exit(0)
  } catch (error) {
    console.error('❌ Error:', error.message)
    console.error('❌ Status:', error.status)
    console.error('❌ Type:', error.type)
    process.exit(1)
  }
}

testOpenAI()