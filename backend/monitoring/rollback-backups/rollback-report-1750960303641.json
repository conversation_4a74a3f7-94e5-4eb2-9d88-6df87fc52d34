{"backupId": "phase4_backup_1750960287762", "executionTime": 578, "procedures": [{"name": "Stop Phase 4 Services", "success": true, "result": {"message": "Phase 4 services stopped", "timestamp": "2025-06-26T17:51:43.065Z"}, "timestamp": "2025-06-26T17:51:43.065Z"}, {"name": "Restore Original Files", "success": true, "result": {"restoredFiles": ["server.js", "package.json", "middleware/errorHandler.js", "utils/logger.js", "routes/health.js"], "errors": [], "count": 5}, "timestamp": "2025-06-26T17:51:43.066Z"}, {"name": "Remove Phase 4 Components", "success": true, "result": {"removedComponents": ["monitoring/circuitBreaker.js", "monitoring/advancedMongoManager.js", "monitoring/errorRecoverySystem.js", "monitoring/healthDashboard.js", "monitoring/performanceOptimizer.js", "monitoring/securityHardening.js", "monitoring/comprehensiveTesting.js", "monitoring/phase4Integration.js", "monitoring/automaticRollback.js"], "errors": [], "count": 9}, "timestamp": "2025-06-26T17:51:43.068Z"}, {"name": "Restore Dependencies", "success": true, "result": {"restoredFiles": ["package.json", "package-lock.json"], "count": 2}, "timestamp": "2025-06-26T17:51:43.640Z"}, {"name": "Restore Configuration", "success": true, "result": {"restoredConfigs": [".env", ".env.example", "Dockerfile"], "count": 3}, "timestamp": "2025-06-26T17:51:43.640Z"}, {"name": "Restart Services", "success": true, "result": {"message": "Services restart initiated", "note": "Manual restart may be required", "timestamp": "2025-06-26T17:51:43.641Z"}, "timestamp": "2025-06-26T17:51:43.641Z"}], "success": true, "timestamp": "2025-06-26T17:51:43.641Z"}