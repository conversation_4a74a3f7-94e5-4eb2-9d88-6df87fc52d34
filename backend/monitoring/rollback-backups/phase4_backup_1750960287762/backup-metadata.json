{"id": "phase4_backup_1750960287762", "timestamp": "2025-06-26T17:51:27.762Z", "path": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762", "components": {"files": {"path": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files", "files": {"server.js": {"originalPath": "/home/<USER>/convertflow/backend/server.js", "backupPath": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files/server.js", "exists": true}, "package.json": {"originalPath": "/home/<USER>/convertflow/backend/package.json", "backupPath": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files/package.json", "exists": true}, "middleware/errorHandler.js": {"originalPath": "/home/<USER>/convertflow/backend/middleware/errorHandler.js", "backupPath": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files/middleware/errorHandler.js", "exists": true}, "utils/logger.js": {"originalPath": "/home/<USER>/convertflow/backend/utils/logger.js", "backupPath": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files/utils/logger.js", "exists": true}, "routes/health.js": {"originalPath": "/home/<USER>/convertflow/backend/routes/health.js", "backupPath": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/original-files/routes/health.js", "exists": true}}, "count": 5}, "database": {"path": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/database", "stateFile": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/database/database-state.json", "collections": 0}, "config": {"path": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/config", "files": [".env", ".env.example", "Dockerfile"], "envBackup": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/config/environment-backup.json"}, "dependencies": {"path": "/home/<USER>/convertflow/backend/monitoring/rollback-backups/phase4_backup_1750960287762/dependencies", "files": ["package.json", "package-lock.json"]}, "systemSnapshot": {"error": "require is not defined"}}}