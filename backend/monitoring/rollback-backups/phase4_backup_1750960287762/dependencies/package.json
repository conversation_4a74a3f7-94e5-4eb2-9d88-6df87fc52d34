{"name": "neurocolony-backend", "version": "1.0.0", "description": "Backend API for NeuroColony SaaS with Ultra Debug God Mode", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "start-debug": "node debug-server.js", "start-enhanced": "node server-with-monitoring.js", "start-monitoring": "node monitoring/index.js", "dashboard": "node monitoring/monitoringDashboard.js", "validate": "node monitoring/systemValidator.js", "build": "echo 'Backend build complete'", "test": "echo 'Running tests...' && npm run test:health", "test:health": "node health-test.js", "test:monitoring": "node monitoring/test.js"}, "dependencies": {"argon2": "^0.43.0", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "mongoose": "^8.0.3", "nodemailer": "^6.9.7", "openai": "^4.20.1", "redis": "^4.6.11", "socket.io": "^4.8.1", "stripe": "^14.7.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["neurocolony", "backend", "api", "monitoring", "debugging", "preventive-hardening", "ultra-debug-god-mode"], "author": "NeuroColony Team", "license": "MIT", "engines": {"node": ">=16.0.0"}}