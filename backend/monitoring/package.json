{"name": "@neurocolony/monitoring", "version": "1.0.0", "description": "Ultra Debug God Mode - Phase 4: Preventive Hardening Monitoring System", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "test": "echo 'Testing monitoring system...' && node test.js", "dashboard": "node dashboard-standalone.js", "validate": "node validate-system.js"}, "keywords": ["monitoring", "health-check", "circuit-breaker", "self-healing", "alerts", "debugging", "preventive-hardening"], "dependencies": {"winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "ioredis": "^5.6.1", "mongoose": "^8.0.3", "axios": "^4.6.0", "express": "^4.18.2", "socket.io": "^4.7.5", "nodemailer": "^6.9.7"}, "author": "NeuroColony Team", "license": "MIT", "engines": {"node": ">=16.0.0"}}