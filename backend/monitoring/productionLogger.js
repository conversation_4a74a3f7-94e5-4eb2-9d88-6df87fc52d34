// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Production-Grade Logging - Structured Logs & Error Aggregation
// ====================================================================

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { EventEmitter } from 'events';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ProductionLogger extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            level: process.env.LOG_LEVEL || 'info',
            logDir: config.logDir || path.join(process.cwd(), 'logs'),
            maxFiles: config.maxFiles || '30d',
            maxSize: config.maxSize || '100m',
            enableConsole: config.enableConsole !== false,
            enableErrorAggregation: config.enableErrorAggregation !== false,
            errorPattern: {
                maxSimilarErrors: 10,
                timeWindow: 300000, // 5 minutes
            },
            structured: true,
            ...config
        };

        this.logger = null;
        this.errorAggregator = new Map();
        this.performanceMetrics = {
            requests: 0,
            errors: 0,
            warnings: 0,
            responseTimeSum: 0,
            avgResponseTime: 0
        };

        this.init();
    }

    async init() {
        try {
            // Ensure log directory exists
            await fs.mkdir(this.config.logDir, { recursive: true });

            // Create Winston logger with multiple transports
            this.logger = winston.createLogger({
                level: this.config.level,
                format: this.createLogFormat(),
                defaultMeta: {
                    service: 'sequenceai-backend',
                    environment: process.env.NODE_ENV || 'development',
                    version: process.env.npm_package_version || '1.0.0',
                    nodeVersion: process.version,
                    platform: process.platform
                },
                transports: this.createTransports()
            });

            // Handle uncaught exceptions and rejections
            this.setupGlobalErrorHandling();

            console.log('🔧 Production Logger initialized');
            this.emit('initialized');

        } catch (error) {
            console.error('Failed to initialize logger:', error);
            throw error;
        }
    }

    createLogFormat() {
        const { combine, timestamp, errors, json, printf, colorize } = winston.format;

        // Custom format for structured logging
        const structuredFormat = printf(({ level, message, timestamp, stack, ...meta }) => {
            const logEntry = {
                timestamp,
                level,
                message,
                ...meta
            };

            if (stack) {
                logEntry.stack = stack;
            }

            return JSON.stringify(logEntry, null, this.config.structured ? 2 : 0);
        });

        // Console format (human readable)
        const consoleFormat = printf(({ level, message, timestamp, ...meta }) => {
            let metaStr = '';
            if (Object.keys(meta).length > 0) {
                metaStr = ` ${JSON.stringify(meta)}`;
            }
            return `${timestamp} [${level.toUpperCase()}]: ${message}${metaStr}`;
        });

        // Return individual formats, not nested object
        this.fileFormat = combine(
            timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
            errors({ stack: true }),
            structuredFormat
        );

        this.consoleFormat = combine(
            timestamp({ format: 'HH:mm:ss' }),
            colorize(),
            consoleFormat
        );

        return this.fileFormat;
    }

    createTransports() {
        const transports = [];

        // Daily rotating file for all logs
        transports.push(new DailyRotateFile({
            filename: path.join(this.config.logDir, 'combined-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: this.config.maxFiles,
            maxSize: this.config.maxSize,
            format: this.fileFormat,
            auditFile: path.join(this.config.logDir, 'audit.json')
        }));

        // Daily rotating file for errors only
        transports.push(new DailyRotateFile({
            filename: path.join(this.config.logDir, 'error-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            level: 'error',
            maxFiles: this.config.maxFiles,
            maxSize: this.config.maxSize,
            format: this.fileFormat,
            auditFile: path.join(this.config.logDir, 'error-audit.json')
        }));

        // Performance logs
        transports.push(new DailyRotateFile({
            filename: path.join(this.config.logDir, 'performance-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: '7d',
            maxSize: this.config.maxSize,
            format: this.fileFormat,
            level: 'debug'
        }));

        // Security logs
        transports.push(new DailyRotateFile({
            filename: path.join(this.config.logDir, 'security-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: '90d', // Keep security logs longer
            maxSize: this.config.maxSize,
            format: this.fileFormat
        }));

        // Console transport for development
        if (this.config.enableConsole) {
            transports.push(new winston.transports.Console({
                format: this.consoleFormat,
                level: process.env.NODE_ENV === 'production' ? 'warn' : 'debug'
            }));
        }

        return transports;
    }

    setupGlobalErrorHandling() {
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            this.error('Uncaught Exception', {
                error: {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                },
                type: 'uncaughtException',
                fatal: true
            });

            // Give time for log to be written
            setTimeout(() => {
                process.exit(1);
            }, 1000);
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            this.error('Unhandled Promise Rejection', {
                reason: reason?.toString(),
                stack: reason?.stack,
                promise: promise?.toString(),
                type: 'unhandledRejection'
            });
        });

        // Handle process warnings
        process.on('warning', (warning) => {
            this.warn('Process Warning', {
                warning: {
                    name: warning.name,
                    message: warning.message,
                    stack: warning.stack
                },
                type: 'processWarning'
            });
        });
    }

    // Main logging methods
    error(message, meta = {}) {
        this.performanceMetrics.errors++;
        
        if (this.config.enableErrorAggregation) {
            this.aggregateError(message, meta);
        }

        if (this.logger) {
            this.logger.error(message, meta);
        } else {
            console.error(`[ERROR] ${message}`, meta);
        }
        this.emit('error', { message, meta });
    }

    warn(message, meta = {}) {
        this.performanceMetrics.warnings++;
        
        if (this.logger) {
            this.logger.warn(message, meta);
        } else {
            console.warn(`[WARN] ${message}`, meta);
        }
        this.emit('warn', { message, meta });
    }

    info(message, meta = {}) {
        if (this.logger) {
            this.logger.info(message, meta);
        } else {
            console.log(`[INFO] ${message}`, meta);
        }
        this.emit('info', { message, meta });
    }

    debug(message, meta = {}) {
        if (this.logger) {
            this.logger.debug(message, meta);
        } else {
            console.log(`[DEBUG] ${message}`, meta);
        }
    }

    // Specialized logging methods
    logRequest(req, res, responseTime) {
        this.performanceMetrics.requests++;
        this.performanceMetrics.responseTimeSum += responseTime;
        this.performanceMetrics.avgResponseTime = 
            this.performanceMetrics.responseTimeSum / this.performanceMetrics.requests;

        const logData = {
            method: req.method,
            url: req.url,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            contentLength: res.get('Content-Length'),
            referer: req.get('Referer'),
            userId: req.user?.id,
            type: 'request'
        };

        if (res.statusCode >= 400) {
            this.error('HTTP Error', logData);
        } else if (responseTime > 5000) {
            this.warn('Slow Request', logData);
        } else {
            this.info('Request', logData);
        }
    }

    logSecurity(event, details = {}) {
        const securityLog = {
            event,
            timestamp: new Date().toISOString(),
            severity: details.severity || 'medium',
            ip: details.ip,
            userId: details.userId,
            userAgent: details.userAgent,
            type: 'security',
            ...details
        };

        // Always log security events as warnings or errors
        if (securityLog.severity === 'high' || securityLog.severity === 'critical') {
            this.error(`Security Event: ${event}`, securityLog);
        } else {
            this.warn(`Security Event: ${event}`, securityLog);
        }
    }

    logPerformance(operation, duration, details = {}) {
        const perfLog = {
            operation,
            duration: `${duration}ms`,
            timestamp: new Date().toISOString(),
            type: 'performance',
            ...details
        };

        if (duration > 10000) {
            this.warn(`Slow Operation: ${operation}`, perfLog);
        } else {
            this.debug(`Performance: ${operation}`, perfLog);
        }
    }

    logDatabaseOperation(operation, collection, duration, error = null) {
        const dbLog = {
            operation,
            collection,
            duration: `${duration}ms`,
            type: 'database'
        };

        if (error) {
            this.error(`Database Error: ${operation}`, {
                ...dbLog,
                error: {
                    message: error.message,
                    code: error.code,
                    name: error.name
                }
            });
        } else if (duration > 5000) {
            this.warn(`Slow Database Operation: ${operation}`, dbLog);
        } else {
            this.debug(`Database: ${operation}`, dbLog);
        }
    }

    logAIOperation(provider, model, tokens, duration, error = null) {
        const aiLog = {
            provider,
            model,
            tokens,
            duration: `${duration}ms`,
            type: 'ai_operation'
        };

        if (error) {
            this.error(`AI Operation Error: ${provider}`, {
                ...aiLog,
                error: {
                    message: error.message,
                    code: error.code,
                    status: error.status
                }
            });
        } else {
            this.info(`AI Operation: ${provider}`, aiLog);
        }
    }

    // Error aggregation to prevent log spam
    aggregateError(message, meta) {
        const errorKey = this.generateErrorKey(message, meta);
        const now = Date.now();
        
        if (!this.errorAggregator.has(errorKey)) {
            this.errorAggregator.set(errorKey, {
                count: 1,
                firstSeen: now,
                lastSeen: now,
                message,
                meta
            });
        } else {
            const aggregated = this.errorAggregator.get(errorKey);
            aggregated.count++;
            aggregated.lastSeen = now;
            
            // Log aggregation summary if threshold reached
            if (aggregated.count === this.config.errorPattern.maxSimilarErrors) {
                this.warn('Error Pattern Detected', {
                    errorKey,
                    count: aggregated.count,
                    timeSpan: `${(now - aggregated.firstSeen) / 1000}s`,
                    type: 'error_aggregation'
                });
            }
        }

        // Cleanup old error aggregations
        this.cleanupErrorAggregation();
    }

    generateErrorKey(message, meta) {
        // Generate a key for similar errors
        const errorType = meta.error?.name || 'unknown';
        const errorCode = meta.error?.code || '';
        return `${errorType}_${errorCode}_${message.substring(0, 50)}`;
    }

    cleanupErrorAggregation() {
        const cutoff = Date.now() - this.config.errorPattern.timeWindow;
        
        for (const [key, data] of this.errorAggregator.entries()) {
            if (data.lastSeen < cutoff) {
                this.errorAggregator.delete(key);
            }
        }
    }

    // Analytics and reporting
    getLogStats() {
        return {
            performance: { ...this.performanceMetrics },
            errors: {
                aggregated: this.errorAggregator.size,
                patterns: Array.from(this.errorAggregator.entries()).map(([key, data]) => ({
                    key,
                    count: data.count,
                    timeSpan: `${(data.lastSeen - data.firstSeen) / 1000}s`
                }))
            },
            config: {
                level: this.config.level,
                logDir: this.config.logDir,
                enableConsole: this.config.enableConsole
            }
        };
    }

    async generateLogReport(hours = 24) {
        try {
            const reportData = {
                timestamp: new Date().toISOString(),
                timeRange: `${hours} hours`,
                stats: this.getLogStats(),
                systemInfo: {
                    nodeVersion: process.version,
                    platform: process.platform,
                    memory: process.memoryUsage(),
                    uptime: process.uptime()
                }
            };

            const reportFile = path.join(this.config.logDir, `log-report-${Date.now()}.json`);
            await fs.writeFile(reportFile, JSON.stringify(reportData, null, 2));
            
            this.info('Log Report Generated', { reportFile });
            return reportFile;

        } catch (error) {
            this.error('Failed to generate log report', { error: error.message });
            throw error;
        }
    }

    // Configuration methods
    setLevel(level) {
        this.config.level = level;
        this.logger.level = level;
        this.info('Log level changed', { newLevel: level });
    }

    async rotateLogs() {
        try {
            // Force rotation of daily rotate files
            for (const transport of this.logger.transports) {
                if (transport.rotate) {
                    await transport.rotate();
                }
            }
            this.info('Logs rotated manually');
        } catch (error) {
            this.error('Failed to rotate logs', { error: error.message });
        }
    }

    async cleanupOldLogs(daysOld = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            const files = await fs.readdir(this.config.logDir);
            let deletedCount = 0;
            
            for (const file of files) {
                const filePath = path.join(this.config.logDir, file);
                const stats = await fs.stat(filePath);
                
                if (stats.mtime < cutoffDate && file.endsWith('.log')) {
                    await fs.unlink(filePath);
                    deletedCount++;
                }
            }
            
            this.info('Old logs cleaned up', { deletedCount, daysOld });
            
        } catch (error) {
            this.error('Failed to cleanup old logs', { error: error.message });
        }
    }

    // Graceful shutdown
    async shutdown() {
        return new Promise((resolve) => {
            this.info('Shutting down logger');
            
            if (this.logger) {
                this.logger.end(() => {
                    console.log('✅ Logger shutdown complete');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

// Singleton instance
let productionLogger = null;

function getProductionLogger(config = {}) {
    if (!productionLogger) {
        productionLogger = new ProductionLogger(config);
    }
    return productionLogger;
}

export { ProductionLogger, getProductionLogger };
export default getProductionLogger;