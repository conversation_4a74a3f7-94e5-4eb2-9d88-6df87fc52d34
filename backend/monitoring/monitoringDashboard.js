// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Monitoring Dashboard - Real-time System Status & Metrics
// ====================================================================

import express from 'express';
import http from 'http';
import { Server as SocketIO } from 'socket.io';
import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class MonitoringDashboard {
    constructor(config = {}) {
        this.config = {
            port: config.port || 5001,
            updateInterval: config.updateInterval || 5000, // 5 seconds
            maxDataPoints: config.maxDataPoints || 100,
            enableWebSocket: config.enableWebSocket !== false,
            ...config
        };

        this.app = express();
        this.server = http.createServer(this.app);
        this.io = this.config.enableWebSocket ? new SocketIO(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        }) : null;

        this.healthMonitor = null;
        this.alertSystem = null;
        this.circuitBreakerManager = null;
        this.selfHealer = null;
        this.productionLogger = null;
        this.systemValidator = null;

        this.dashboardData = {
            system: {
                status: 'unknown',
                uptime: 0,
                lastUpdate: null,
                services: {},
                metrics: {
                    cpu: [],
                    memory: [],
                    disk: [],
                    connections: []
                }
            },
            alerts: {
                recent: [],
                count: {
                    total: 0,
                    critical: 0,
                    warning: 0,
                    info: 0
                }
            },
            circuitBreakers: {
                total: 0,
                open: 0,
                halfOpen: 0,
                closed: 0,
                breakers: []
            },
            healing: {
                totalActions: 0,
                successfulActions: 0,
                failedActions: 0,
                recentActions: []
            },
            performance: {
                requests: 0,
                errors: 0,
                warnings: 0,
                avgResponseTime: 0
            }
        };

        this.connectedClients = new Set();
        this.setupRoutes();
        this.setupWebSocket();
    }

    setupRoutes() {
        // Serve static dashboard files
        this.app.use('/static', express.static(path.join(__dirname, 'dashboard-static')));
        
        // Main dashboard route
        this.app.get('/', (req, res) => {
            res.send(this.generateDashboardHTML());
        });

        // API routes
        this.app.get('/api/status', (req, res) => {
            res.json(this.dashboardData);
        });

        this.app.get('/api/health', (req, res) => {
            if (this.healthMonitor) {
                res.json(this.healthMonitor.getStatus());
            } else {
                res.json({ status: 'health_monitor_not_initialized' });
            }
        });

        this.app.get('/api/alerts', (req, res) => {
            if (this.alertSystem) {
                res.json({
                    config: this.alertSystem.getConfig(),
                    recent: this.dashboardData.alerts.recent
                });
            } else {
                res.json({ status: 'alert_system_not_initialized' });
            }
        });

        this.app.get('/api/circuit-breakers', (req, res) => {
            if (this.circuitBreakerManager) {
                res.json(this.circuitBreakerManager.getGlobalStats());
            } else {
                res.json({ status: 'circuit_breaker_not_initialized' });
            }
        });

        this.app.get('/api/healing', (req, res) => {
            if (this.selfHealer) {
                res.json(this.selfHealer.getHealingStats());
            } else {
                res.json({ status: 'self_healer_not_initialized' });
            }
        });

        this.app.get('/api/logs', (req, res) => {
            if (this.productionLogger) {
                res.json(this.productionLogger.getLogStats());
            } else {
                res.json({ status: 'logger_not_initialized' });
            }
        });

        this.app.get('/api/validation', (req, res) => {
            if (this.systemValidator && this.systemValidator.validationResults) {
                res.json(this.systemValidator.getValidationSummary());
            } else {
                res.json({ status: 'validator_not_initialized' });
            }
        });

        // Force actions
        this.app.post('/api/force-heal/:service', async (req, res) => {
            try {
                if (this.selfHealer) {
                    const result = await this.selfHealer.forceHealService(req.params.service);
                    res.json({ success: result });
                } else {
                    res.status(503).json({ error: 'Self healer not available' });
                }
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });

        this.app.post('/api/reset-circuit-breaker/:name', (req, res) => {
            try {
                if (this.circuitBreakerManager) {
                    const breaker = this.circuitBreakerManager.get(req.params.name);
                    if (breaker) {
                        breaker.reset();
                        res.json({ success: true });
                    } else {
                        res.status(404).json({ error: 'Circuit breaker not found' });
                    }
                } else {
                    res.status(503).json({ error: 'Circuit breaker manager not available' });
                }
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });

        this.app.post('/api/test-alerts', async (req, res) => {
            try {
                if (this.alertSystem) {
                    const result = await this.alertSystem.testAlerts();
                    res.json({ success: result });
                } else {
                    res.status(503).json({ error: 'Alert system not available' });
                }
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });

        this.app.post('/api/validate-system', async (req, res) => {
            try {
                if (this.systemValidator) {
                    const result = await this.systemValidator.validateSystem();
                    res.json(result);
                } else {
                    res.status(503).json({ error: 'System validator not available' });
                }
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });
    }

    setupWebSocket() {
        if (!this.io) return;

        this.io.on('connection', (socket) => {
            console.log(`📡 Dashboard client connected: ${socket.id}`);
            this.connectedClients.add(socket.id);

            // Send initial data
            socket.emit('dashboard-data', this.dashboardData);

            socket.on('disconnect', () => {
                console.log(`📡 Dashboard client disconnected: ${socket.id}`);
                this.connectedClients.delete(socket.id);
            });

            // Handle client requests
            socket.on('request-data', () => {
                socket.emit('dashboard-data', this.dashboardData);
            });

            socket.on('force-heal', async (service) => {
                try {
                    if (this.selfHealer) {
                        const result = await this.selfHealer.forceHealService(service);
                        socket.emit('heal-result', { service, success: result });
                    }
                } catch (error) {
                    socket.emit('heal-result', { service, success: false, error: error.message });
                }
            });
        });
    }

    registerMonitors(monitors) {
        this.healthMonitor = monitors.healthMonitor;
        this.alertSystem = monitors.alertSystem;
        this.circuitBreakerManager = monitors.circuitBreakerManager;
        this.selfHealer = monitors.selfHealer;
        this.productionLogger = monitors.productionLogger;
        this.systemValidator = monitors.systemValidator;

        // Setup event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Health Monitor events
        if (this.healthMonitor) {
            this.healthMonitor.on('alert', (alert) => {
                this.addAlert(alert);
                this.broadcastAlert(alert);
            });

            this.healthMonitor.on('error', (error) => {
                this.addAlert({
                    type: 'health_monitor_error',
                    message: error.message,
                    severity: 'critical',
                    timestamp: new Date()
                });
            });
        }

        // Alert System events
        if (this.alertSystem) {
            // Alert system events are handled by health monitor
        }

        // Circuit Breaker events
        if (this.circuitBreakerManager) {
            this.circuitBreakerManager.getAll().forEach(breaker => {
                breaker.on('stateChange', (data) => {
                    this.updateCircuitBreakerData();
                    this.broadcastUpdate('circuit-breaker-state', data);
                });
            });
        }

        // Self Healer events
        if (this.selfHealer) {
            this.selfHealer.on('healingAction', (action) => {
                this.updateHealingData();
                this.broadcastUpdate('healing-action', action);
            });

            this.selfHealer.on('serviceHealed', (data) => {
                this.addAlert({
                    type: 'service_healed',
                    message: `Service ${data.service} healed successfully`,
                    severity: 'info',
                    timestamp: new Date()
                });
            });
        }

        // Logger events
        if (this.productionLogger) {
            this.productionLogger.on('error', (error) => {
                this.dashboardData.performance.errors++;
                this.updatePerformanceData();
            });

            this.productionLogger.on('warn', (warning) => {
                this.dashboardData.performance.warnings++;
                this.updatePerformanceData();
            });
        }
    }

    async start() {
        console.log('📊 Starting Monitoring Dashboard...');

        // Start update loop
        this.updateInterval = setInterval(() => {
            this.updateDashboardData().catch(error => {
                console.error('Dashboard update failed:', error);
            });
        }, this.config.updateInterval);

        // Initial data update
        await this.updateDashboardData();

        // Start server
        return new Promise((resolve, reject) => {
            this.server.listen(this.config.port, (error) => {
                if (error) {
                    reject(error);
                } else {
                    console.log(`✅ Monitoring Dashboard running on port ${this.config.port}`);
                    console.log(`🌐 Dashboard URL: http://localhost:${this.config.port}`);
                    resolve();
                }
            });
        });
    }

    async stop() {
        console.log('🛑 Stopping Monitoring Dashboard...');

        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        if (this.server) {
            this.server.close();
        }

        console.log('✅ Monitoring Dashboard stopped');
    }

    async updateDashboardData() {
        try {
            // Update system status
            if (this.healthMonitor) {
                const healthStatus = this.healthMonitor.getStatus();
                this.dashboardData.system = {
                    ...this.dashboardData.system,
                    status: healthStatus.status,
                    uptime: healthStatus.uptime,
                    lastUpdate: new Date(),
                    services: healthStatus.services
                };

                // Update metrics
                if (healthStatus.system) {
                    this.addMetricDataPoint('cpu', healthStatus.system.cpu);
                    this.addMetricDataPoint('memory', healthStatus.system.memory);
                    this.addMetricDataPoint('disk', healthStatus.system.disk);
                    this.addMetricDataPoint('connections', healthStatus.system.connections);
                }
            }

            // Update circuit breaker data
            this.updateCircuitBreakerData();

            // Update healing data
            this.updateHealingData();

            // Update performance data
            this.updatePerformanceData();

            // Broadcast to connected clients
            this.broadcastDashboardData();

        } catch (error) {
            console.error('Failed to update dashboard data:', error);
        }
    }

    updateCircuitBreakerData() {
        if (this.circuitBreakerManager) {
            const stats = this.circuitBreakerManager.getGlobalStats();
            this.dashboardData.circuitBreakers = {
                total: stats.totalBreakers,
                open: stats.openBreakers,
                halfOpen: stats.halfOpenBreakers,
                closed: stats.closedBreakers,
                breakers: stats.breakers
            };
        }
    }

    updateHealingData() {
        if (this.selfHealer) {
            const stats = this.selfHealer.getHealingStats();
            this.dashboardData.healing = {
                totalActions: stats.totalActions,
                successfulActions: stats.successfulActions,
                failedActions: stats.failedActions,
                recentActions: stats.recentActions
            };
        }
    }

    updatePerformanceData() {
        if (this.productionLogger) {
            const stats = this.productionLogger.getLogStats();
            this.dashboardData.performance = {
                requests: stats.performance.requests,
                errors: stats.performance.errors,
                warnings: stats.performance.warnings,
                avgResponseTime: stats.performance.avgResponseTime
            };
        }
    }

    addMetricDataPoint(metric, value) {
        if (!this.dashboardData.system.metrics[metric]) {
            this.dashboardData.system.metrics[metric] = [];
        }

        this.dashboardData.system.metrics[metric].push({
            timestamp: Date.now(),
            value: value
        });

        // Keep only recent data points
        if (this.dashboardData.system.metrics[metric].length > this.config.maxDataPoints) {
            this.dashboardData.system.metrics[metric].shift();
        }
    }

    addAlert(alert) {
        this.dashboardData.alerts.recent.unshift(alert);
        
        // Keep only recent alerts
        if (this.dashboardData.alerts.recent.length > 100) {
            this.dashboardData.alerts.recent = this.dashboardData.alerts.recent.slice(0, 100);
        }

        // Update counts
        this.dashboardData.alerts.count.total++;
        if (alert.severity) {
            this.dashboardData.alerts.count[alert.severity]++;
        }
    }

    broadcastDashboardData() {
        if (this.io && this.connectedClients.size > 0) {
            this.io.emit('dashboard-data', this.dashboardData);
        }
    }

    broadcastAlert(alert) {
        if (this.io && this.connectedClients.size > 0) {
            this.io.emit('new-alert', alert);
        }
    }

    broadcastUpdate(type, data) {
        if (this.io && this.connectedClients.size > 0) {
            this.io.emit('update', { type, data });
        }
    }

    generateDashboardHTML() {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroColony Monitoring Dashboard</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .dashboard {
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            background: #2a2a2a;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: 1px solid #3a3a3a;
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            border-bottom: 2px solid #3a3a3a;
            padding-bottom: 0.5rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0.5rem 0;
        }
        
        .metric-label {
            color: #aaa;
            font-size: 0.9rem;
        }
        
        .service-list {
            list-style: none;
        }
        
        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #3a3a3a;
        }
        
        .service-item:last-child {
            border-bottom: none;
        }
        
        .alert-item {
            background: #3a3a3a;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #667eea;
        }
        
        .alert-critical { border-left-color: #dc3545; }
        .alert-warning { border-left-color: #ffc107; }
        .alert-info { border-left-color: #17a2b8; }
        
        .alert-time {
            font-size: 0.8rem;
            color: #aaa;
            margin-top: 0.5rem;
        }
        
        .chart-container {
            height: 200px;
            margin-top: 1rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .connected-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
        }
        
        .disconnected {
            background: #dc3545 !important;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="connected-indicator" id="connectionStatus">🔄 Connecting...</div>
    
    <div class="header">
        <h1>🔥 NeuroColony Monitoring Dashboard</h1>
        <p class="subtitle">Ultra Debug God Mode - Phase 4: Preventive Hardening</p>
    </div>
    
    <div class="dashboard">
        <!-- System Status -->
        <div class="card">
            <h3>🖥️ System Status</h3>
            <div id="systemStatus">
                <div class="metric-value" id="systemStatusValue">Unknown</div>
                <div class="metric-label">Overall System Health</div>
                <div style="margin-top: 1rem;">
                    <strong>Uptime:</strong> <span id="systemUptime">-</span><br>
                    <strong>Last Check:</strong> <span id="lastCheck">-</span>
                </div>
            </div>
        </div>
        
        <!-- Services -->
        <div class="card">
            <h3>🔧 Services</h3>
            <ul class="service-list" id="servicesList">
                <li class="service-item">
                    <span>Loading services...</span>
                    <span class="status-indicator status-unknown"></span>
                </li>
            </ul>
        </div>
        
        <!-- Recent Alerts -->
        <div class="card">
            <h3>🚨 Recent Alerts</h3>
            <div id="alertsList">
                <div class="alert-item">Loading alerts...</div>
            </div>
        </div>
        
        <!-- Circuit Breakers -->
        <div class="card">
            <h3>⚡ Circuit Breakers</h3>
            <div id="circuitBreakers">
                <div class="metric-value" id="circuitBreakerCount">-</div>
                <div class="metric-label">Total Circuit Breakers</div>
                <div style="margin-top: 1rem;" id="circuitBreakerStats">
                    <strong>Open:</strong> <span id="openBreakers">-</span><br>
                    <strong>Half-Open:</strong> <span id="halfOpenBreakers">-</span><br>
                    <strong>Closed:</strong> <span id="closedBreakers">-</span>
                </div>
            </div>
        </div>
        
        <!-- Self Healing -->
        <div class="card">
            <h3>🩺 Self Healing</h3>
            <div id="healingStats">
                <div class="metric-value" id="healingActions">-</div>
                <div class="metric-label">Total Healing Actions</div>
                <div style="margin-top: 1rem;">
                    <strong>Successful:</strong> <span id="successfulHealing">-</span><br>
                    <strong>Failed:</strong> <span id="failedHealing">-</span><br>
                    <strong>Success Rate:</strong> <span id="healingSuccessRate">-</span>
                </div>
            </div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="card">
            <h3>📊 Performance</h3>
            <div id="performanceMetrics">
                <div class="metric-value" id="totalRequests">-</div>
                <div class="metric-label">Total Requests</div>
                <div style="margin-top: 1rem;">
                    <strong>Errors:</strong> <span id="totalErrors">-</span><br>
                    <strong>Warnings:</strong> <span id="totalWarnings">-</span><br>
                    <strong>Avg Response:</strong> <span id="avgResponseTime">-</span>
                </div>
            </div>
        </div>
        
        <!-- System Metrics Chart -->
        <div class="card" style="grid-column: span 2;">
            <h3>📈 System Metrics</h3>
            <div class="chart-container">
                <canvas id="metricsChart"></canvas>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card">
            <h3>⚙️ Actions</h3>
            <div class="action-buttons">
                <button class="btn" onclick="validateSystem()">🔍 Validate System</button>
                <button class="btn" onclick="testAlerts()">📢 Test Alerts</button>
                <button class="btn btn-danger" onclick="refreshData()">🔄 Refresh Data</button>
            </div>
        </div>
    </div>
    
    <script>
        const socket = io();
        let chart = null;
        
        // Connection status
        socket.on('connect', () => {
            document.getElementById('connectionStatus').textContent = '🟢 Connected';
            document.getElementById('connectionStatus').classList.remove('disconnected');
        });
        
        socket.on('disconnect', () => {
            document.getElementById('connectionStatus').textContent = '🔴 Disconnected';
            document.getElementById('connectionStatus').classList.add('disconnected');
        });
        
        // Dashboard data updates
        socket.on('dashboard-data', (data) => {
            updateDashboard(data);
        });
        
        socket.on('new-alert', (alert) => {
            addNewAlert(alert);
        });
        
        function updateDashboard(data) {
            // System status
            const statusElement = document.getElementById('systemStatusValue');
            statusElement.textContent = data.system.status.toUpperCase();
            statusElement.className = 'metric-value status-' + data.system.status;
            
            document.getElementById('systemUptime').textContent = formatUptime(data.system.uptime);
            document.getElementById('lastCheck').textContent = data.system.lastUpdate ? 
                new Date(data.system.lastUpdate).toLocaleTimeString() : '-';
            
            // Services
            updateServicesList(data.system.services);
            
            // Alerts
            updateAlertsList(data.alerts.recent);
            
            // Circuit Breakers
            document.getElementById('circuitBreakerCount').textContent = data.circuitBreakers.total;
            document.getElementById('openBreakers').textContent = data.circuitBreakers.open;
            document.getElementById('halfOpenBreakers').textContent = data.circuitBreakers.halfOpen;
            document.getElementById('closedBreakers').textContent = data.circuitBreakers.closed;
            
            // Healing
            document.getElementById('healingActions').textContent = data.healing.totalActions;
            document.getElementById('successfulHealing').textContent = data.healing.successfulActions;
            document.getElementById('failedHealing').textContent = data.healing.failedActions;
            
            const healingRate = data.healing.totalActions > 0 ? 
                ((data.healing.successfulActions / data.healing.totalActions) * 100).toFixed(1) + '%' : '-';
            document.getElementById('healingSuccessRate').textContent = healingRate;
            
            // Performance
            document.getElementById('totalRequests').textContent = data.performance.requests;
            document.getElementById('totalErrors').textContent = data.performance.errors;
            document.getElementById('totalWarnings').textContent = data.performance.warnings;
            document.getElementById('avgResponseTime').textContent = data.performance.avgResponseTime.toFixed(0) + 'ms';
            
            // Update chart
            updateChart(data.system.metrics);
        }
        
        function updateServicesList(services) {
            const list = document.getElementById('servicesList');
            list.innerHTML = '';
            
            Object.entries(services).forEach(([name, service]) => {
                const item = document.createElement('li');
                item.className = 'service-item';
                item.innerHTML = \`
                    <span>\${name.toUpperCase()}</span>
                    <span class="status-indicator status-\${service.status}"></span>
                \`;
                list.appendChild(item);
            });
        }
        
        function updateAlertsList(alerts) {
            const list = document.getElementById('alertsList');
            list.innerHTML = '';
            
            alerts.slice(0, 5).forEach(alert => {
                const item = document.createElement('div');
                item.className = \`alert-item alert-\${alert.severity}\`;
                item.innerHTML = \`
                    <strong>\${alert.type.replace(/_/g, ' ').toUpperCase()}</strong><br>
                    \${alert.message}
                    <div class="alert-time">\${new Date(alert.timestamp).toLocaleString()}</div>
                \`;
                list.appendChild(item);
            });
            
            if (alerts.length === 0) {
                list.innerHTML = '<div class="alert-item">No recent alerts</div>';
            }
        }
        
        function updateChart(metrics) {
            const ctx = document.getElementById('metricsChart').getContext('2d');
            
            if (!chart) {
                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        datasets: [
                            {
                                label: 'CPU %',
                                borderColor: '#ff6384',
                                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                data: []
                            },
                            {
                                label: 'Memory %',
                                borderColor: '#36a2eb',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                data: []
                            },
                            {
                                label: 'Disk %',
                                borderColor: '#ffce56',
                                backgroundColor: 'rgba(255, 206, 86, 0.1)',
                                data: []
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'minute'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        }
                    }
                });
            }
            
            // Update chart data
            if (metrics.cpu && metrics.memory && metrics.disk) {
                chart.data.datasets[0].data = metrics.cpu.map(point => ({
                    x: point.timestamp,
                    y: point.value
                }));
                chart.data.datasets[1].data = metrics.memory.map(point => ({
                    x: point.timestamp,
                    y: point.value
                }));
                chart.data.datasets[2].data = metrics.disk.map(point => ({
                    x: point.timestamp,
                    y: point.value
                }));
                chart.update('none');
            }
        }
        
        function formatUptime(uptime) {
            if (!uptime) return '-';
            
            const seconds = Math.floor(uptime / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return \`\${days}d \${hours % 24}h\`;
            if (hours > 0) return \`\${hours}h \${minutes % 60}m\`;
            if (minutes > 0) return \`\${minutes}m \${seconds % 60}s\`;
            return \`\${seconds}s\`;
        }
        
        function validateSystem() {
            fetch('/api/validate-system', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    alert(\`System validation completed: \${result.overall}\`);
                })
                .catch(error => {
                    alert(\`Validation failed: \${error.message}\`);
                });
        }
        
        function testAlerts() {
            fetch('/api/test-alerts', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    alert(\`Alert test \${result.success ? 'completed successfully' : 'failed'}\`);
                })
                .catch(error => {
                    alert(\`Alert test failed: \${error.message}\`);
                });
        }
        
        function refreshData() {
            socket.emit('request-data');
        }
        
        // Request initial data
        socket.emit('request-data');
    </script>
</body>
</html>
        `;
    }
}

export default MonitoringDashboard;