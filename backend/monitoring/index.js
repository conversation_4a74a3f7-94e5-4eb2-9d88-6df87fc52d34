// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Monitoring System Integration - Master Control
// ====================================================================

import HealthMonitor from './healthMonitor.js';
import AlertSystem from './alertSystem.js';
import circuitBreakerManager from './circuitBreaker.js';
import SelfHealer from './selfHealer.js';
import { getProductionLogger } from './productionLogger.js';
import SystemValidator from './systemValidator.js';
import MonitoringDashboard from './monitoringDashboard.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class MonitoringSystem {
    constructor(config = {}) {
        this.config = {
            // Global monitoring settings
            enableHealthMonitor: config.enableHealthMonitor !== false,
            enableAlertSystem: config.enableAlertSystem !== false,
            enableCircuitBreakers: config.enableCircuitBreakers !== false,
            enableSelfHealer: config.enableSelfHealer !== false,
            enableLogger: config.enableLogger !== false,
            enableSystemValidator: config.enableSystemValidator !== false,
            enableDashboard: config.enableDashboard !== false,
            
            // Component configurations
            healthMonitor: config.healthMonitor || {},
            alertSystem: config.alertSystem || {},
            selfHealer: config.selfHealer || {},
            logger: config.logger || {},
            systemValidator: config.systemValidator || {},
            dashboard: config.dashboard || {},
            
            // Integration settings
            autoStart: config.autoStart !== false,
            gracefulShutdown: config.gracefulShutdown !== false,
            
            ...config
        };

        // Component instances
        this.healthMonitor = null;
        this.alertSystem = null;
        this.circuitBreakerManager = circuitBreakerManager;
        this.selfHealer = null;
        this.productionLogger = null;
        this.systemValidator = null;
        this.dashboard = null;

        // System state
        this.isRunning = false;
        this.startTime = null;
        this.shutdownHandlers = [];

        console.log('🔥 Monitoring System - Ultra Debug God Mode Initialized');
    }

    async initialize() {
        console.log('🚀 Initializing Ultra Debug God Mode Monitoring System...');
        
        try {
            // Initialize components in order
            await this.initializeLogger();
            await this.initializeSystemValidator();
            await this.initializeAlertSystem();
            await this.initializeHealthMonitor();
            await this.initializeCircuitBreakers();
            await this.initializeSelfHealer();
            await this.initializeDashboard();
            
            // Setup integrations
            this.setupIntegrations();
            
            console.log('✅ Monitoring System initialization complete');
            
        } catch (error) {
            console.error('❌ Monitoring System initialization failed:', error);
            throw error;
        }
    }

    async initializeLogger() {
        if (!this.config.enableLogger) return;
        
        console.log('📝 Initializing Production Logger...');
        this.productionLogger = getProductionLogger(this.config.logger);
        
        // Wait for logger initialization
        if (this.productionLogger && typeof this.productionLogger.init === 'function') {
            await this.productionLogger.init();
        }
        
        // Small delay to ensure logger is ready
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Log system startup
        this.productionLogger.info('Monitoring System starting', {
            environment: process.env.NODE_ENV,
            nodeVersion: process.version,
            platform: process.platform
        });
    }

    async initializeSystemValidator() {
        if (!this.config.enableSystemValidator) return;
        
        console.log('🔍 Initializing System Validator...');
        this.systemValidator = new SystemValidator(this.config.systemValidator);
    }

    async initializeAlertSystem() {
        if (!this.config.enableAlertSystem) return;
        
        console.log('📢 Initializing Alert System...');
        this.alertSystem = new AlertSystem(this.config.alertSystem);
    }

    async initializeHealthMonitor() {
        if (!this.config.enableHealthMonitor) return;
        
        console.log('🩺 Initializing Health Monitor...');
        this.healthMonitor = new HealthMonitor(this.config.healthMonitor);
    }

    async initializeCircuitBreakers() {
        if (!this.config.enableCircuitBreakers) return;
        
        console.log('⚡ Initializing Circuit Breakers...');
        
        // Create standard circuit breakers for common services
        const standardBreakers = [
            {
                name: 'mongodb',
                config: {
                    failureThreshold: 5,
                    timeout: 30000,
                    fallback: () => ({ error: 'Database unavailable', fallback: true })
                }
            },
            {
                name: 'redis',
                config: {
                    failureThreshold: 5,
                    timeout: 30000,
                    fallback: () => ({ error: 'Cache unavailable', fallback: true })
                }
            },
            {
                name: 'openai',
                config: {
                    failureThreshold: 3,
                    timeout: 60000,
                    fallback: () => ({ 
                        content: 'AI service temporarily unavailable. Please try again later.',
                        fallback: true 
                    })
                }
            },
            {
                name: 'stripe',
                config: {
                    failureThreshold: 3,
                    timeout: 30000,
                    fallback: () => ({ error: 'Payment service unavailable', fallback: true })
                }
            }
        ];

        standardBreakers.forEach(breaker => {
            this.circuitBreakerManager.create(breaker.name, breaker.config);
        });
    }

    async initializeSelfHealer() {
        if (!this.config.enableSelfHealer) return;
        
        console.log('🛠️ Initializing Self Healer...');
        this.selfHealer = new SelfHealer(this.config.selfHealer);
    }

    async initializeDashboard() {
        if (!this.config.enableDashboard) return;
        
        console.log('📊 Initializing Monitoring Dashboard...');
        this.dashboard = new MonitoringDashboard(this.config.dashboard);
        
        // Register all monitoring components with dashboard
        this.dashboard.registerMonitors({
            healthMonitor: this.healthMonitor,
            alertSystem: this.alertSystem,
            circuitBreakerManager: this.circuitBreakerManager,
            selfHealer: this.selfHealer,
            productionLogger: this.productionLogger,
            systemValidator: this.systemValidator
        });
    }

    setupIntegrations() {
        console.log('🔗 Setting up component integrations...');
        
        // Health Monitor -> Alert System integration
        if (this.healthMonitor && this.alertSystem) {
            this.healthMonitor.on('alert', async (alert) => {
                await this.alertSystem.sendAlert(alert);
            });
            
            this.healthMonitor.on('error', (error) => {
                if (this.productionLogger) {
                    this.productionLogger.error('Health Monitor Error', error);
                }
            });
        }

        // Self Healer -> Alert System integration
        if (this.selfHealer && this.alertSystem) {
            this.selfHealer.on('serviceHealed', async (data) => {
                await this.alertSystem.sendAlert({
                    type: 'service_healed',
                    message: `Service ${data.service} healed successfully in ${data.duration}ms`,
                    severity: 'info',
                    timestamp: new Date()
                });
            });
            
            this.selfHealer.on('healingFailed', async (data) => {
                await this.alertSystem.sendAlert({
                    type: 'healing_failed',
                    message: `Failed to heal service ${data.service}`,
                    severity: 'critical',
                    timestamp: new Date()
                });
            });
        }

        // Circuit Breaker -> Alert System integration
        if (this.circuitBreakerManager && this.alertSystem) {
            this.circuitBreakerManager.getAll().forEach(breaker => {
                breaker.on('circuitOpened', async (data) => {
                    await this.alertSystem.sendAlert({
                        type: 'circuit_breaker_opened',
                        message: `Circuit breaker ${data.name} opened after ${data.failures} failures`,
                        severity: 'warning',
                        timestamp: new Date()
                    });
                });
                
                breaker.on('circuitClosed', async (data) => {
                    await this.alertSystem.sendAlert({
                        type: 'circuit_breaker_closed',
                        message: `Circuit breaker ${data.name} closed - service recovered`,
                        severity: 'info',
                        timestamp: new Date()
                    });
                });
            });
        }

        // Logger integration
        if (this.productionLogger) {
            // Log all major events
            const logEvent = (source, event, data) => {
                this.productionLogger.info(`${source} Event: ${event}`, {
                    source,
                    event,
                    data,
                    type: 'monitoring_event'
                });
            };

            if (this.healthMonitor) {
                this.healthMonitor.on('alert', (alert) => logEvent('HealthMonitor', 'alert', alert));
            }
            
            if (this.selfHealer) {
                this.selfHealer.on('healingAction', (action) => logEvent('SelfHealer', 'healing', action));
            }
        }

        // Setup graceful shutdown
        if (this.config.gracefulShutdown) {
            this.setupGracefulShutdown();
        }
    }

    async start() {
        if (this.isRunning) {
            console.log('⚠️ Monitoring System already running');
            return;
        }

        console.log('🚀 Starting Ultra Debug God Mode Monitoring System...');
        this.startTime = Date.now();
        
        try {
            // Start components in order
            const startPromises = [];
            
            if (this.healthMonitor) {
                startPromises.push(this.healthMonitor.start());
            }
            
            if (this.selfHealer) {
                startPromises.push(this.selfHealer.start());
            }
            
            if (this.dashboard) {
                startPromises.push(this.dashboard.start());
            }
            
            // Wait for all components to start
            await Promise.all(startPromises);
            
            // Perform initial system validation
            if (this.systemValidator) {
                console.log('🔍 Performing initial system validation...');
                const validationResult = await this.systemValidator.validateSystem();
                
                if (validationResult.overall === 'error' || validationResult.overall === 'fail') {
                    console.warn('⚠️ System validation found issues:', validationResult.overall);
                    
                    if (this.alertSystem) {
                        await this.alertSystem.sendAlert({
                            type: 'system_validation_issues',
                            message: `System validation found ${validationResult.overall} issues`,
                            severity: validationResult.overall === 'error' ? 'critical' : 'warning',
                            timestamp: new Date()
                        });
                    }
                } else {
                    console.log('✅ Initial system validation passed');
                }
            }
            
            this.isRunning = true;
            
            // Log successful startup
            if (this.productionLogger) {
                this.productionLogger.info('Monitoring System started successfully', {
                    startupTime: Date.now() - this.startTime,
                    components: this.getComponentStatus()
                });
            }
            
            // Send startup alert
            if (this.alertSystem) {
                await this.alertSystem.sendAlert({
                    type: 'monitoring_system_started',
                    message: 'Ultra Debug God Mode Monitoring System started successfully',
                    severity: 'info',
                    timestamp: new Date()
                });
            }
            
            console.log('✅ Ultra Debug God Mode Monitoring System started successfully');
            console.log(this.getStatusSummary());
            
        } catch (error) {
            console.error('❌ Failed to start Monitoring System:', error);
            
            if (this.productionLogger) {
                this.productionLogger.error('Monitoring System startup failed', { error: error.message });
            }
            
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            console.log('⚠️ Monitoring System not running');
            return;
        }

        console.log('🛑 Stopping Ultra Debug God Mode Monitoring System...');
        
        try {
            // Stop components in reverse order
            const stopPromises = [];
            
            if (this.dashboard) {
                stopPromises.push(this.dashboard.stop());
            }
            
            if (this.selfHealer) {
                stopPromises.push(this.selfHealer.stop());
            }
            
            if (this.healthMonitor) {
                stopPromises.push(this.healthMonitor.stop());
            }
            
            // Wait for all components to stop
            await Promise.all(stopPromises);
            
            // Save final stats
            await this.saveFinalStats();
            
            this.isRunning = false;
            
            // Log shutdown
            if (this.productionLogger) {
                this.productionLogger.info('Monitoring System stopped', {
                    uptime: Date.now() - this.startTime
                });
                
                // Shutdown logger last
                await this.productionLogger.shutdown();
            }
            
            console.log('✅ Ultra Debug God Mode Monitoring System stopped');
            
        } catch (error) {
            console.error('❌ Error during Monitoring System shutdown:', error);
            throw error;
        }
    }

    setupGracefulShutdown() {
        const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
        
        signals.forEach(signal => {
            process.on(signal, async () => {
                console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
                
                try {
                    await this.stop();
                    process.exit(0);
                } catch (error) {
                    console.error('Error during graceful shutdown:', error);
                    process.exit(1);
                }
            });
        });
        
        // Handle uncaught exceptions
        process.on('uncaughtException', async (error) => {
            console.error('Uncaught Exception:', error);
            
            if (this.productionLogger) {
                this.productionLogger.error('Uncaught Exception', { error: error.message, stack: error.stack });
            }
            
            try {
                await this.stop();
            } catch (stopError) {
                console.error('Error during emergency shutdown:', stopError);
            }
            
            process.exit(1);
        });
    }

    getComponentStatus() {
        return {
            healthMonitor: !!this.healthMonitor,
            alertSystem: !!this.alertSystem,
            circuitBreakers: !!this.circuitBreakerManager,
            selfHealer: !!this.selfHealer,
            logger: !!this.productionLogger,
            systemValidator: !!this.systemValidator,
            dashboard: !!this.dashboard
        };
    }

    getStatusSummary() {
        const uptime = this.startTime ? Date.now() - this.startTime : 0;
        const components = this.getComponentStatus();
        const activeComponents = Object.values(components).filter(Boolean).length;
        
        return `
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔥 ULTRA DEBUG GOD MODE - PHASE 4 🔥                     ║
║                        PREVENTIVE HARDENING COMPLETE                        ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ Status: ${this.isRunning ? '🟢 RUNNING' : '🔴 STOPPED'}                                                          ║
║ Uptime: ${this.formatUptime(uptime)}                                              ║
║ Components: ${activeComponents}/7 Active                                           ║
║                                                                              ║
║ 🩺 Health Monitor:     ${components.healthMonitor ? '✅ Active' : '❌ Inactive'}                           ║
║ 📢 Alert System:       ${components.alertSystem ? '✅ Active' : '❌ Inactive'}                           ║
║ ⚡ Circuit Breakers:   ${components.circuitBreakers ? '✅ Active' : '❌ Inactive'}                           ║
║ 🛠️ Self Healer:        ${components.selfHealer ? '✅ Active' : '❌ Inactive'}                           ║
║ 📝 Logger:             ${components.logger ? '✅ Active' : '❌ Inactive'}                           ║
║ 🔍 System Validator:   ${components.systemValidator ? '✅ Active' : '❌ Inactive'}                           ║
║ 📊 Dashboard:          ${components.dashboard ? '✅ Active' : '❌ Inactive'}                           ║
║                                                                              ║
║ 🌐 Dashboard URL: http://localhost:${this.config.dashboard?.port || 5001}                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
        `;
    }

    formatUptime(uptime) {
        if (!uptime) return '0s';
        
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }

    async saveFinalStats() {
        try {
            const statsDir = path.join(__dirname, 'final-stats');
            await fs.mkdir(statsDir, { recursive: true });
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const statsFile = path.join(statsDir, `monitoring-session-${timestamp}.json`);
            
            const stats = {
                session: {
                    startTime: this.startTime,
                    endTime: Date.now(),
                    uptime: Date.now() - this.startTime,
                    components: this.getComponentStatus()
                },
                healthMonitor: this.healthMonitor?.getDetailedReport(),
                circuitBreakers: this.circuitBreakerManager?.getGlobalStats(),
                selfHealer: this.selfHealer?.getHealingStats(),
                logger: this.productionLogger?.getLogStats(),
                systemValidator: this.systemValidator?.getValidationSummary()
            };
            
            await fs.writeFile(statsFile, JSON.stringify(stats, null, 2));
            console.log(`📊 Final stats saved to: ${statsFile}`);
            
        } catch (error) {
            console.error('Failed to save final stats:', error);
        }
    }

    // Utility methods for external access
    getHealthMonitor() { return this.healthMonitor; }
    getAlertSystem() { return this.alertSystem; }
    getCircuitBreakerManager() { return this.circuitBreakerManager; }
    getSelfHealer() { return this.selfHealer; }
    getLogger() { return this.productionLogger; }
    getSystemValidator() { return this.systemValidator; }
    getDashboard() { return this.dashboard; }
}

// Export both class and singleton instance
const monitoringSystem = new MonitoringSystem();

export { MonitoringSystem };
export default monitoringSystem;