// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Self-Healing System - Automated Recovery & Service Restart
// ====================================================================

import EventEmitter from 'events';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import mongoose from 'mongoose';
import Redis from 'ioredis';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const execAsync = promisify(exec);

class SelfHealer extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            // Healing strategies
            maxRestartAttempts: config.maxRestartAttempts || 3,
            restartCooldown: config.restartCooldown || 300000, // 5 minutes
            healthCheckInterval: config.healthCheckInterval || 60000, // 1 minute
            
            // Service configurations
            services: {
                mongodb: {
                    enabled: true,
                    healthCheck: () => this.checkMongoDB(),
                    heal: () => this.healMongoDB(),
                    restartCommand: 'sudo systemctl restart mongod',
                    maxRestarts: 3
                },
                redis: {
                    enabled: true,
                    healthCheck: () => this.checkRedis(),
                    heal: () => this.healRedis(),
                    restartCommand: 'sudo systemctl restart redis-server',
                    maxRestarts: 3
                },
                application: {
                    enabled: true,
                    healthCheck: () => this.checkApplication(),
                    heal: () => this.healApplication(),
                    restartCommand: 'pm2 restart sequenceai-backend',
                    maxRestarts: 5
                },
                disk: {
                    enabled: true,
                    healthCheck: () => this.checkDiskSpace(),
                    heal: () => this.healDiskSpace(),
                    maxRestarts: 1
                },
                memory: {
                    enabled: true,
                    healthCheck: () => this.checkMemoryUsage(),
                    heal: () => this.healMemoryUsage(),
                    maxRestarts: 2
                }
            },
            
            // Thresholds
            thresholds: {
                diskUsage: 90,
                memoryUsage: 90,
                cpuUsage: 95,
                responseTime: 10000
            },
            
            ...config
        };

        this.healingHistory = [];
        this.serviceRestartCounts = {};
        this.lastRestartTimes = {};
        this.isHealing = false;
        this.healingInterval = null;
        this.redisConnection = null;

        // Initialize restart counters
        Object.keys(this.config.services).forEach(service => {
            this.serviceRestartCounts[service] = 0;
            this.lastRestartTimes[service] = 0;
        });
    }

    async start() {
        console.log('🩺 Starting Self-Healing System - Ultra Debug God Mode');
        
        // Initialize Redis connection for healing
        try {
            this.redisConnection = new Redis(process.env.REDIS_URL, {
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3,
                lazyConnect: true
            });
        } catch (error) {
            console.error('Redis connection setup failed for healer:', error.message);
        }

        // Start healing loop
        this.healingInterval = setInterval(() => {
            this.performHealingCheck().catch(error => {
                console.error('Healing check failed:', error);
            });
        }, this.config.healthCheckInterval);

        // Perform initial check
        await this.performHealingCheck();
        
        console.log('✅ Self-Healing System started');
        this.emit('started');
    }

    async stop() {
        console.log('🛑 Stopping Self-Healing System');
        
        if (this.healingInterval) {
            clearInterval(this.healingInterval);
        }
        
        if (this.redisConnection) {
            await this.redisConnection.disconnect();
        }
        
        console.log('✅ Self-Healing System stopped');
        this.emit('stopped');
    }

    async performHealingCheck() {
        if (this.isHealing) {
            console.log('⏳ Healing already in progress, skipping check');
            return;
        }

        console.log('🔍 Performing healing check...');
        
        const healingTasks = [];
        
        // Check all enabled services
        for (const [serviceName, serviceConfig] of Object.entries(this.config.services)) {
            if (serviceConfig.enabled) {
                healingTasks.push(this.checkAndHealService(serviceName, serviceConfig));
            }
        }

        // Execute all healing tasks
        const results = await Promise.allSettled(healingTasks);
        
        let healingActions = 0;
        results.forEach((result, index) => {
            const serviceName = Object.keys(this.config.services)[index];
            if (result.status === 'fulfilled' && result.value) {
                healingActions++;
            } else if (result.status === 'rejected') {
                console.error(`Healing check failed for ${serviceName}:`, result.reason);
            }
        });

        if (healingActions > 0) {
            console.log(`🩺 Completed healing check with ${healingActions} healing actions`);
        } else {
            console.log('✅ All services healthy, no healing needed');
        }
    }

    async checkAndHealService(serviceName, serviceConfig) {
        try {
            // Check if service needs healing
            const isHealthy = await serviceConfig.healthCheck();
            
            if (!isHealthy) {
                console.log(`🚨 Service ${serviceName} is unhealthy, attempting healing`);
                
                // Check restart cooldown
                const now = Date.now();
                const lastRestart = this.lastRestartTimes[serviceName];
                
                if (now - lastRestart < this.config.restartCooldown) {
                    console.log(`⏳ Service ${serviceName} is in restart cooldown`);
                    return false;
                }
                
                // Check restart limit
                if (this.serviceRestartCounts[serviceName] >= serviceConfig.maxRestarts) {
                    console.log(`🛑 Service ${serviceName} has exceeded restart limit`);
                    this.recordHealingAction(serviceName, 'restart_limit_reached', false);
                    return false;
                }
                
                // Attempt healing
                return await this.healService(serviceName, serviceConfig);
            }
            
            // Reset restart count on healthy service
            if (this.serviceRestartCounts[serviceName] > 0) {
                console.log(`✅ Service ${serviceName} is healthy, resetting restart count`);
                this.serviceRestartCounts[serviceName] = 0;
            }
            
            return false; // No healing needed
            
        } catch (error) {
            console.error(`Error checking service ${serviceName}:`, error);
            return false;
        }
    }

    async healService(serviceName, serviceConfig) {
        this.isHealing = true;
        
        try {
            console.log(`🩺 Healing service: ${serviceName}`);
            
            const healingStart = Date.now();
            const success = await serviceConfig.heal();
            const healingDuration = Date.now() - healingStart;
            
            if (success) {
                this.serviceRestartCounts[serviceName]++;
                this.lastRestartTimes[serviceName] = Date.now();
                
                console.log(`✅ Successfully healed ${serviceName} in ${healingDuration}ms`);
                this.recordHealingAction(serviceName, 'healed', true, healingDuration);
                
                // Wait for service to stabilize
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Verify healing worked
                const isNowHealthy = await serviceConfig.healthCheck();
                if (!isNowHealthy) {
                    console.log(`⚠️ Service ${serviceName} still unhealthy after healing`);
                    this.recordHealingAction(serviceName, 'healing_verification_failed', false);
                }
                
                this.emit('serviceHealed', { service: serviceName, duration: healingDuration });
                return true;
                
            } else {
                console.log(`❌ Failed to heal ${serviceName}`);
                this.recordHealingAction(serviceName, 'healing_failed', false);
                this.emit('healingFailed', { service: serviceName });
                return false;
            }
            
        } catch (error) {
            console.error(`Healing error for ${serviceName}:`, error);
            this.recordHealingAction(serviceName, 'healing_error', false, 0, error.message);
            this.emit('healingError', { service: serviceName, error });
            return false;
            
        } finally {
            this.isHealing = false;
        }
    }

    // Individual service health checks
    async checkMongoDB() {
        try {
            if (mongoose.connection.readyState !== 1) {
                return false;
            }
            await mongoose.connection.db.admin().ping();
            return true;
        } catch {
            return false;
        }
    }

    async checkRedis() {
        try {
            if (!this.redisConnection) {
                return false;
            }
            await this.redisConnection.ping();
            return true;
        } catch {
            return false;
        }
    }

    async checkApplication() {
        try {
            // Check if main process is responsive
            const { stdout } = await execAsync('ps aux | grep "node.*server.js" | grep -v grep | wc -l');
            const processCount = parseInt(stdout.trim());
            return processCount > 0;
        } catch {
            return false;
        }
    }

    async checkDiskSpace() {
        try {
            const { stdout } = await execAsync("df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1");
            const diskUsage = parseFloat(stdout.trim());
            return diskUsage < this.config.thresholds.diskUsage;
        } catch {
            return true; // Assume OK if check fails
        }
    }

    async checkMemoryUsage() {
        try {
            const { stdout } = await execAsync("free | awk 'NR==2{printf \"%.1f\", $3*100/$2}'");
            const memoryUsage = parseFloat(stdout.trim());
            return memoryUsage < this.config.thresholds.memoryUsage;
        } catch {
            return true; // Assume OK if check fails
        }
    }

    // Individual service healing methods
    async healMongoDB() {
        try {
            console.log('🩺 Attempting to heal MongoDB...');
            
            // Try to reconnect Mongoose
            if (mongoose.connection.readyState === 0) {
                await mongoose.connect(process.env.MONGODB_URI);
                console.log('✅ MongoDB reconnected via Mongoose');
                return true;
            }
            
            // Try system restart if we have permissions
            try {
                await execAsync('sudo systemctl restart mongod');
                console.log('✅ MongoDB system service restarted');
                
                // Wait for service to start
                await new Promise(resolve => setTimeout(resolve, 10000));
                
                // Reconnect Mongoose
                await mongoose.connect(process.env.MONGODB_URI);
                return true;
                
            } catch (systemError) {
                console.log('⚠️ Could not restart MongoDB system service, trying reconnect only');
                return false;
            }
            
        } catch (error) {
            console.error('MongoDB healing failed:', error);
            return false;
        }
    }

    async healRedis() {
        try {
            console.log('🩺 Attempting to heal Redis...');
            
            // Try to reconnect
            if (this.redisConnection) {
                await this.redisConnection.disconnect();
                this.redisConnection = new Redis(process.env.REDIS_URL);
                await this.redisConnection.ping();
                console.log('✅ Redis reconnected');
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('Redis healing failed:', error);
            return false;
        }
    }

    async healApplication() {
        try {
            console.log('🩺 Attempting to heal application...');
            
            // Try PM2 restart first
            try {
                await execAsync('pm2 restart sequenceai-backend');
                console.log('✅ Application restarted via PM2');
                return true;
            } catch {
                console.log('⚠️ PM2 restart failed, trying manual restart');
            }
            
            // Try to restart via process signal
            try {
                const { stdout } = await execAsync('ps aux | grep "node.*server.js" | grep -v grep | awk \'{print $2}\'');
                const pids = stdout.trim().split('\n').filter(pid => pid);
                
                if (pids.length > 0) {
                    for (const pid of pids) {
                        await execAsync(`kill -USR2 ${pid}`);
                    }
                    console.log('✅ Application restart signal sent');
                    return true;
                }
            } catch {
                console.log('⚠️ Could not send restart signal');
            }
            
            return false;
            
        } catch (error) {
            console.error('Application healing failed:', error);
            return false;
        }
    }

    async healDiskSpace() {
        try {
            console.log('🩺 Attempting to heal disk space...');
            
            // Clean log files
            const logCleanupCommands = [
                'find ./logs -name "*.log" -mtime +7 -delete',
                'find /tmp -name "*.tmp" -mtime +1 -delete',
                'docker system prune -f',
                'npm cache clean --force'
            ];
            
            let cleanedSpace = false;
            
            for (const command of logCleanupCommands) {
                try {
                    await execAsync(command);
                    cleanedSpace = true;
                    console.log(`✅ Executed cleanup: ${command}`);
                } catch (error) {
                    console.log(`⚠️ Cleanup failed: ${command} - ${error.message}`);
                }
            }
            
            if (cleanedSpace) {
                console.log('✅ Disk space cleanup completed');
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('Disk space healing failed:', error);
            return false;
        }
    }

    async healMemoryUsage() {
        try {
            console.log('🩺 Attempting to heal memory usage...');
            
            // Force garbage collection if possible
            if (global.gc) {
                global.gc();
                console.log('✅ Forced garbage collection');
            }
            
            // Clear caches
            try {
                const { exec } = require('child_process');
                exec('sync && echo 3 > /proc/sys/vm/drop_caches', (error) => {
                    if (error) {
                        console.log('⚠️ Could not clear system caches (requires root)');
                    } else {
                        console.log('✅ System caches cleared');
                    }
                });
            } catch {
                console.log('⚠️ System cache clearing not available');
            }
            
            return true;
            
        } catch (error) {
            console.error('Memory healing failed:', error);
            return false;
        }
    }

    recordHealingAction(service, action, success, duration = 0, error = null) {
        const healingRecord = {
            timestamp: new Date(),
            service,
            action,
            success,
            duration,
            error,
            restartCount: this.serviceRestartCounts[service]
        };

        this.healingHistory.push(healingRecord);
        
        // Keep only last 1000 healing records
        if (this.healingHistory.length > 1000) {
            this.healingHistory = this.healingHistory.slice(-1000);
        }

        console.log(`📝 Healing record: ${service} - ${action} - ${success ? 'SUCCESS' : 'FAILED'}`);
        
        this.emit('healingAction', healingRecord);
    }

    getHealingStats() {
        const stats = {
            totalActions: this.healingHistory.length,
            successfulActions: this.healingHistory.filter(h => h.success).length,
            failedActions: this.healingHistory.filter(h => !h.success).length,
            serviceStats: {},
            recentActions: this.healingHistory.slice(-20)
        };

        // Calculate per-service stats
        Object.keys(this.config.services).forEach(service => {
            const serviceActions = this.healingHistory.filter(h => h.service === service);
            stats.serviceStats[service] = {
                totalActions: serviceActions.length,
                successfulActions: serviceActions.filter(h => h.success).length,
                failedActions: serviceActions.filter(h => !h.success).length,
                restartCount: this.serviceRestartCounts[service],
                lastRestart: this.lastRestartTimes[service]
            };
        });

        stats.successRate = stats.totalActions > 0 ? 
            (stats.successfulActions / stats.totalActions) * 100 : 0;

        return stats;
    }

    async forceHealService(serviceName) {
        const serviceConfig = this.config.services[serviceName];
        if (!serviceConfig) {
            throw new Error(`Unknown service: ${serviceName}`);
        }

        console.log(`🩺 Force healing service: ${serviceName}`);
        return await this.healService(serviceName, serviceConfig);
    }

    resetServiceRestartCount(serviceName) {
        if (this.serviceRestartCounts[serviceName] !== undefined) {
            this.serviceRestartCounts[serviceName] = 0;
            this.lastRestartTimes[serviceName] = 0;
            console.log(`🔄 Reset restart count for service: ${serviceName}`);
        }
    }

    async saveHealingHistory() {
        try {
            const healingDir = path.join(__dirname, 'healing-history');
            await fs.mkdir(healingDir, { recursive: true });
            
            const timestamp = new Date().toISOString().split('T')[0];
            const historyFile = path.join(healingDir, `healing-history-${timestamp}.json`);
            
            const data = {
                timestamp: new Date(),
                stats: this.getHealingStats(),
                fullHistory: this.healingHistory,
                config: this.config
            };
            
            await fs.writeFile(historyFile, JSON.stringify(data, null, 2));
            
        } catch (error) {
            console.error('Failed to save healing history:', error);
        }
    }
}

export default SelfHealer;