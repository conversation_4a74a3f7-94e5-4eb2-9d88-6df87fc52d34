// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Alert System - Multi-Channel Notifications & Webhooks
// ====================================================================

import nodemailer from 'nodemailer';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AlertSystem {
    constructor(config = {}) {
        this.config = {
            email: {
                enabled: process.env.EMAIL_ENABLED === 'true',
                smtp: {
                    host: process.env.SMTP_HOST,
                    port: parseInt(process.env.SMTP_PORT) || 587,
                    secure: false,
                    auth: {
                        user: process.env.SMTP_USER,
                        pass: process.env.SMTP_PASS
                    }
                },
                recipients: config.emailRecipients || ['<EMAIL>'],
                from: config.emailFrom || '<EMAIL>'
            },
            webhook: {
                enabled: config.webhookEnabled || false,
                urls: config.webhookUrls || [],
                retries: 3
            },
            slack: {
                enabled: config.slackEnabled || false,
                webhookUrl: config.slackWebhookUrl,
                channel: config.slackChannel || '#alerts'
            },
            discord: {
                enabled: config.discordEnabled || false,
                webhookUrl: config.discordWebhookUrl
            },
            rateLimit: {
                maxPerHour: 50,
                maxPerMinute: 5
            },
            ...config
        };

        this.alertCounts = {
            hourly: new Map(),
            minutely: new Map()
        };

        this.emailTransporter = null;
        this.initializeTransporter();
    }

    async initializeTransporter() {
        if (this.config.email.enabled) {
            try {
                this.emailTransporter = nodemailer.createTransporter(this.config.email.smtp);
                
                // Verify connection
                await this.emailTransporter.verify();
                console.log('✅ Email transporter initialized successfully');
                
            } catch (error) {
                console.error('❌ Email transporter initialization failed:', error.message);
                this.config.email.enabled = false;
            }
        }
    }

    async sendAlert(alert) {
        const alertId = `${alert.type}_${Date.now()}`;
        
        try {
            // Check rate limits
            if (!this.checkRateLimit(alert.type)) {
                console.warn(`🚨 Alert rate limited: ${alert.type}`);
                return false;
            }

            console.log(`📢 Sending alert: ${alert.type} - ${alert.message}`);

            // Send to all configured channels
            const results = await Promise.allSettled([
                this.sendEmailAlert(alert),
                this.sendSlackAlert(alert),
                this.sendDiscordAlert(alert),
                this.sendWebhookAlert(alert)
            ]);

            // Log results
            let successCount = 0;
            results.forEach((result, index) => {
                const channels = ['email', 'slack', 'discord', 'webhook'];
                if (result.status === 'fulfilled' && result.value) {
                    successCount++;
                } else if (result.status === 'rejected') {
                    console.error(`Failed to send ${channels[index]} alert:`, result.reason);
                }
            });

            console.log(`📤 Alert sent to ${successCount}/${results.length} channels`);
            
            // Save alert to file
            await this.saveAlertToFile(alert, alertId);
            
            return successCount > 0;

        } catch (error) {
            console.error('Alert system error:', error);
            return false;
        }
    }

    checkRateLimit(alertType) {
        const now = Date.now();
        const currentMinute = Math.floor(now / 60000);
        const currentHour = Math.floor(now / 3600000);

        // Check minute limit
        const minuteKey = `${alertType}_${currentMinute}`;
        const minuteCount = this.alertCounts.minutely.get(minuteKey) || 0;
        if (minuteCount >= this.config.rateLimit.maxPerMinute) {
            return false;
        }

        // Check hour limit
        const hourKey = `${alertType}_${currentHour}`;
        const hourCount = this.alertCounts.hourly.get(hourKey) || 0;
        if (hourCount >= this.config.rateLimit.maxPerHour) {
            return false;
        }

        // Update counts
        this.alertCounts.minutely.set(minuteKey, minuteCount + 1);
        this.alertCounts.hourly.set(hourKey, hourCount + 1);

        // Cleanup old entries
        this.cleanupRateLimitCounters();

        return true;
    }

    cleanupRateLimitCounters() {
        const now = Date.now();
        const currentMinute = Math.floor(now / 60000);
        const currentHour = Math.floor(now / 3600000);

        // Clean minute counters (keep last 2 minutes)
        for (const [key] of this.alertCounts.minutely) {
            const keyMinute = parseInt(key.split('_').pop());
            if (keyMinute < currentMinute - 1) {
                this.alertCounts.minutely.delete(key);
            }
        }

        // Clean hour counters (keep last 2 hours)
        for (const [key] of this.alertCounts.hourly) {
            const keyHour = parseInt(key.split('_').pop());
            if (keyHour < currentHour - 1) {
                this.alertCounts.hourly.delete(key);
            }
        }
    }

    async sendEmailAlert(alert) {
        if (!this.config.email.enabled || !this.emailTransporter) {
            return false;
        }

        try {
            const emailContent = this.formatEmailAlert(alert);
            
            const mailOptions = {
                from: this.config.email.from,
                to: this.config.email.recipients.join(', '),
                subject: `🚨 NeuroColony Alert: ${alert.type}`,
                html: emailContent,
                text: this.stripHtml(emailContent)
            };

            await this.emailTransporter.sendMail(mailOptions);
            return true;

        } catch (error) {
            console.error('Email alert failed:', error);
            return false;
        }
    }

    async sendSlackAlert(alert) {
        if (!this.config.slack.enabled || !this.config.slack.webhookUrl) {
            return false;
        }

        try {
            const payload = {
                channel: this.config.slack.channel,
                username: 'NeuroColony Monitor',
                icon_emoji: this.getAlertEmoji(alert.severity),
                attachments: [{
                    color: this.getAlertColor(alert.severity),
                    title: `Alert: ${alert.type}`,
                    text: alert.message,
                    fields: [
                        {
                            title: 'Severity',
                            value: alert.severity,
                            short: true
                        },
                        {
                            title: 'Time',
                            value: new Date(alert.timestamp).toLocaleString(),
                            short: true
                        }
                    ],
                    footer: 'NeuroColony Health Monitor',
                    ts: Math.floor(new Date(alert.timestamp).getTime() / 1000)
                }]
            };

            await axios.post(this.config.slack.webhookUrl, payload);
            return true;

        } catch (error) {
            console.error('Slack alert failed:', error);
            return false;
        }
    }

    async sendDiscordAlert(alert) {
        if (!this.config.discord.enabled || !this.config.discord.webhookUrl) {
            return false;
        }

        try {
            const embed = {
                title: `🚨 NeuroColony Alert: ${alert.type}`,
                description: alert.message,
                color: this.getDiscordColor(alert.severity),
                fields: [
                    {
                        name: 'Severity',
                        value: alert.severity,
                        inline: true
                    },
                    {
                        name: 'Timestamp',
                        value: new Date(alert.timestamp).toLocaleString(),
                        inline: true
                    }
                ],
                footer: {
                    text: 'NeuroColony Health Monitor'
                },
                timestamp: alert.timestamp
            };

            await axios.post(this.config.discord.webhookUrl, {
                embeds: [embed]
            });
            return true;

        } catch (error) {
            console.error('Discord alert failed:', error);
            return false;
        }
    }

    async sendWebhookAlert(alert) {
        if (!this.config.webhook.enabled || this.config.webhook.urls.length === 0) {
            return false;
        }

        const payload = {
            type: 'health_alert',
            alert: {
                ...alert,
                source: 'sequenceai_backend',
                environment: process.env.NODE_ENV || 'development'
            },
            timestamp: new Date().toISOString()
        };

        let successCount = 0;

        for (const url of this.config.webhook.urls) {
            let retries = 0;
            let success = false;

            while (retries < this.config.webhook.retries && !success) {
                try {
                    await axios.post(url, payload, {
                        timeout: 5000,
                        headers: {
                            'Content-Type': 'application/json',
                            'User-Agent': 'NeuroColony-Monitor/1.0'
                        }
                    });
                    success = true;
                    successCount++;
                } catch (error) {
                    retries++;
                    console.error(`Webhook attempt ${retries} failed for ${url}:`, error.message);
                    
                    if (retries < this.config.webhook.retries) {
                        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                    }
                }
            }
        }

        return successCount > 0;
    }

    formatEmailAlert(alert) {
        return `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background: ${this.getAlertColor(alert.severity)}; color: white; padding: 20px; text-align: center;">
                    <h1>🚨 NeuroColony Alert</h1>
                    <h2>${alert.type}</h2>
                </div>
                
                <div style="padding: 20px; background: #f5f5f5;">
                    <h3>Alert Details</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Type:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${alert.type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${alert.severity}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Message:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${alert.message}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Time:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${new Date(alert.timestamp).toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Environment:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${process.env.NODE_ENV || 'development'}</td>
                        </tr>
                    </table>
                </div>
                
                <div style="padding: 20px; text-align: center; font-size: 12px; color: #666;">
                    <p>This alert was generated by NeuroColony Health Monitor</p>
                    <p>Time: ${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;
    }

    stripHtml(html) {
        return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }

    getAlertEmoji(severity) {
        switch (severity) {
            case 'critical': return ':red_circle:';
            case 'warning': return ':warning:';
            case 'info': return ':information_source:';
            default: return ':grey_question:';
        }
    }

    getAlertColor(severity) {
        switch (severity) {
            case 'critical': return '#dc3545';
            case 'warning': return '#ffc107';
            case 'info': return '#17a2b8';
            default: return '#6c757d';
        }
    }

    getDiscordColor(severity) {
        switch (severity) {
            case 'critical': return 0xdc3545;
            case 'warning': return 0xffc107;
            case 'info': return 0x17a2b8;
            default: return 0x6c757d;
        }
    }

    async saveAlertToFile(alert, alertId) {
        try {
            const alertsDir = path.join(__dirname, 'alerts');
            await fs.mkdir(alertsDir, { recursive: true });
            
            const date = new Date().toISOString().split('T')[0];
            const alertsFile = path.join(alertsDir, `alerts-${date}.json`);
            
            let alerts = [];
            try {
                const existingData = await fs.readFile(alertsFile, 'utf8');
                alerts = JSON.parse(existingData);
            } catch {
                // File doesn't exist yet
            }
            
            alerts.push({
                id: alertId,
                ...alert,
                savedAt: new Date()
            });
            
            await fs.writeFile(alertsFile, JSON.stringify(alerts, null, 2));
            
        } catch (error) {
            console.error('Failed to save alert to file:', error);
        }
    }

    async testAlerts() {
        console.log('🧪 Testing alert system...');
        
        const testAlert = {
            type: 'test_alert',
            message: 'This is a test alert from NeuroColony Health Monitor',
            severity: 'info',
            timestamp: new Date()
        };

        const success = await this.sendAlert(testAlert);
        
        if (success) {
            console.log('✅ Alert system test completed successfully');
        } else {
            console.log('❌ Alert system test failed');
        }
        
        return success;
    }

    getConfig() {
        return {
            email: {
                enabled: this.config.email.enabled,
                recipients: this.config.email.recipients
            },
            slack: {
                enabled: this.config.slack.enabled,
                channel: this.config.slack.channel
            },
            discord: {
                enabled: this.config.discord.enabled
            },
            webhook: {
                enabled: this.config.webhook.enabled,
                urlCount: this.config.webhook.urls.length
            }
        };
    }
}

export default AlertSystem;