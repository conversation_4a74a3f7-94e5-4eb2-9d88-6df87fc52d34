// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Health Monitoring System - Continuous System Monitoring
// ====================================================================

import EventEmitter from 'events';
import mongoose from 'mongoose';
import Redis from 'ioredis';
import axios from 'axios';
import fs from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const execAsync = promisify(exec);

class HealthMonitor extends EventEmitter {
    constructor(config = {}) {
        super();
        this.config = {
            mongoUri: process.env.MONGODB_URI,
            redisUrl: process.env.REDIS_URL,
            apiPort: process.env.PORT || 5000,
            checkInterval: config.checkInterval || 30000, // 30 seconds
            alertThresholds: {
                cpuUsage: 80,
                memoryUsage: 85,
                diskUsage: 90,
                responseTime: 5000,
                errorRate: 5, // errors per minute
                dbConnectionTime: 3000
            },
            ...config
        };

        this.metrics = {
            uptime: 0,
            lastCheck: null,
            status: 'starting',
            services: {
                mongodb: { status: 'unknown', lastCheck: null, responseTime: 0 },
                redis: { status: 'unknown', lastCheck: null, responseTime: 0 },
                api: { status: 'unknown', lastCheck: null, responseTime: 0 },
                openai: { status: 'unknown', lastCheck: null, responseTime: 0 },
                stripe: { status: 'unknown', lastCheck: null, responseTime: 0 }
            },
            system: {
                cpu: 0,
                memory: 0,
                disk: 0,
                connections: 0
            },
            errors: {
                total: 0,
                rate: 0,
                recent: []
            }
        };

        this.alertHistory = [];
        this.isRunning = false;
        this.startTime = Date.now();
        
        // Initialize connections
        this.mongoConnection = null;
        this.redisConnection = null;
    }

    async start() {
        console.log('🔥 Starting Health Monitor - Ultra Debug God Mode');
        this.isRunning = true;
        
        // Setup Redis connection for monitoring
        try {
            this.redisConnection = new Redis(this.config.redisUrl, {
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3,
                lazyConnect: true
            });
        } catch (error) {
            console.error('Redis connection setup failed:', error.message);
        }

        // Start monitoring loop
        this.monitoringInterval = setInterval(() => {
            this.performHealthCheck().catch(error => {
                console.error('Health check failed:', error);
                this.recordError('health_check_failed', error);
            });
        }, this.config.checkInterval);

        // Perform initial health check
        await this.performHealthCheck();
        
        console.log('✅ Health Monitor started successfully');
        this.emit('started');
    }

    async stop() {
        console.log('🛑 Stopping Health Monitor');
        this.isRunning = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        if (this.redisConnection) {
            await this.redisConnection.disconnect();
        }
        
        console.log('✅ Health Monitor stopped');
        this.emit('stopped');
    }

    async performHealthCheck() {
        const checkStart = Date.now();
        
        try {
            // Update uptime
            this.metrics.uptime = Date.now() - this.startTime;
            this.metrics.lastCheck = new Date();

            // Check all services in parallel
            const checks = await Promise.allSettled([
                this.checkMongoDB(),
                this.checkRedis(),
                this.checkAPI(),
                this.checkOpenAI(),
                this.checkStripe(),
                this.checkSystemResources()
            ]);

            // Process results
            let healthyServices = 0;
            let totalServices = 5; // MongoDB, Redis, API, OpenAI, Stripe

            checks.forEach((result, index) => {
                const serviceName = ['mongodb', 'redis', 'api', 'openai', 'stripe', 'system'][index];
                if (result.status === 'fulfilled' && result.value) {
                    healthyServices++;
                } else if (serviceName !== 'system') {
                    console.error(`Service ${serviceName} check failed:`, result.reason);
                    this.recordError(`${serviceName}_check_failed`, result.reason);
                }
            });

            // Update overall status
            const healthPercentage = (healthyServices / totalServices) * 100;
            this.metrics.status = healthPercentage >= 80 ? 'healthy' : 
                                  healthPercentage >= 60 ? 'degraded' : 'unhealthy';

            // Check for alerts
            await this.checkAlerts();

            // Save metrics
            await this.saveMetrics();

            console.log(`🔍 Health Check Complete - Status: ${this.metrics.status} (${healthPercentage.toFixed(1)}%)`);

        } catch (error) {
            console.error('Health check error:', error);
            this.metrics.status = 'error';
            this.recordError('health_check_error', error);
        }

        const checkDuration = Date.now() - checkStart;
        console.log(`⏱️ Health check completed in ${checkDuration}ms`);
    }

    async checkMongoDB() {
        const start = Date.now();
        
        try {
            // Check if mongoose is connected
            if (mongoose.connection.readyState !== 1) {
                throw new Error('Mongoose not connected');
            }

            // Perform a simple ping operation
            await mongoose.connection.db.admin().ping();
            
            const responseTime = Date.now() - start;
            this.metrics.services.mongodb = {
                status: 'healthy',
                lastCheck: new Date(),
                responseTime
            };

            if (responseTime > this.config.alertThresholds.dbConnectionTime) {
                this.recordAlert('mongodb_slow_response', `MongoDB response time: ${responseTime}ms`);
            }

            return true;

        } catch (error) {
            this.metrics.services.mongodb = {
                status: 'unhealthy',
                lastCheck: new Date(),
                responseTime: Date.now() - start,
                error: error.message
            };
            
            this.recordAlert('mongodb_connection_failed', error.message);
            return false;
        }
    }

    async checkRedis() {
        const start = Date.now();
        
        try {
            if (!this.redisConnection) {
                throw new Error('Redis connection not initialized');
            }

            // Test Redis connection with ping
            await this.redisConnection.ping();
            
            const responseTime = Date.now() - start;
            this.metrics.services.redis = {
                status: 'healthy',
                lastCheck: new Date(),
                responseTime
            };

            return true;

        } catch (error) {
            this.metrics.services.redis = {
                status: 'unhealthy',
                lastCheck: new Date(),
                responseTime: Date.now() - start,
                error: error.message
            };
            
            this.recordAlert('redis_connection_failed', error.message);
            return false;
        }
    }

    async checkAPI() {
        const start = Date.now();
        
        try {
            const response = await axios.get(`http://localhost:${this.config.apiPort}/api/health`, {
                timeout: 5000
            });
            
            const responseTime = Date.now() - start;
            this.metrics.services.api = {
                status: response.status === 200 ? 'healthy' : 'unhealthy',
                lastCheck: new Date(),
                responseTime
            };

            if (responseTime > this.config.alertThresholds.responseTime) {
                this.recordAlert('api_slow_response', `API response time: ${responseTime}ms`);
            }

            return response.status === 200;

        } catch (error) {
            this.metrics.services.api = {
                status: 'unhealthy',
                lastCheck: new Date(),
                responseTime: Date.now() - start,
                error: error.message
            };
            
            this.recordAlert('api_connection_failed', error.message);
            return false;
        }
    }

    async checkOpenAI() {
        const start = Date.now();
        
        try {
            // For demo mode, just mark as healthy
            if (process.env.AI_MODE === 'demo') {
                this.metrics.services.openai = {
                    status: 'demo_mode',
                    lastCheck: new Date(),
                    responseTime: Date.now() - start
                };
                return true;
            }

            // For actual OpenAI API
            const response = await axios.get('https://api.openai.com/v1/models', {
                headers: {
                    'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
                },
                timeout: 5000
            });

            const responseTime = Date.now() - start;
            this.metrics.services.openai = {
                status: 'healthy',
                lastCheck: new Date(),
                responseTime
            };

            return true;

        } catch (error) {
            this.metrics.services.openai = {
                status: 'unhealthy',
                lastCheck: new Date(),
                responseTime: Date.now() - start,
                error: error.message
            };
            
            this.recordAlert('openai_connection_failed', error.message);
            return false;
        }
    }

    async checkStripe() {
        const start = Date.now();
        
        try {
            // Basic validation - check if we can create a Stripe instance
            if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY.includes('sk_live_')) {
                // For security, we won't actually call Stripe in monitoring
                this.metrics.services.stripe = {
                    status: 'configured',
                    lastCheck: new Date(),
                    responseTime: Date.now() - start
                };
                return true;
            }

            this.metrics.services.stripe = {
                status: 'not_configured',
                lastCheck: new Date(),
                responseTime: Date.now() - start
            };

            return false;

        } catch (error) {
            this.metrics.services.stripe = {
                status: 'error',
                lastCheck: new Date(),
                responseTime: Date.now() - start,
                error: error.message
            };
            
            return false;
        }
    }

    async checkSystemResources() {
        try {
            // Get CPU usage
            const cpuUsage = await this.getCPUUsage();
            
            // Get memory usage
            const memoryUsage = await this.getMemoryUsage();
            
            // Get disk usage
            const diskUsage = await this.getDiskUsage();
            
            // Get connection count
            const connections = await this.getConnectionCount();

            this.metrics.system = {
                cpu: cpuUsage,
                memory: memoryUsage,
                disk: diskUsage,
                connections
            };

            // Check thresholds
            if (cpuUsage > this.config.alertThresholds.cpuUsage) {
                this.recordAlert('high_cpu_usage', `CPU usage: ${cpuUsage}%`);
            }
            
            if (memoryUsage > this.config.alertThresholds.memoryUsage) {
                this.recordAlert('high_memory_usage', `Memory usage: ${memoryUsage}%`);
            }
            
            if (diskUsage > this.config.alertThresholds.diskUsage) {
                this.recordAlert('high_disk_usage', `Disk usage: ${diskUsage}%`);
            }

            return true;

        } catch (error) {
            console.error('System resource check failed:', error);
            return false;
        }
    }

    async getCPUUsage() {
        try {
            const { stdout } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
            return parseFloat(stdout.trim()) || 0;
        } catch {
            return 0;
        }
    }

    async getMemoryUsage() {
        try {
            const { stdout } = await execAsync("free | awk 'NR==2{printf \"%.1f\", $3*100/$2}'");
            return parseFloat(stdout.trim()) || 0;
        } catch {
            return 0;
        }
    }

    async getDiskUsage() {
        try {
            const { stdout } = await execAsync("df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1");
            return parseFloat(stdout.trim()) || 0;
        } catch {
            return 0;
        }
    }

    async getConnectionCount() {
        try {
            const { stdout } = await execAsync("netstat -an | grep ESTABLISHED | wc -l");
            return parseInt(stdout.trim()) || 0;
        } catch {
            return 0;
        }
    }

    recordError(type, error) {
        const errorEntry = {
            type,
            message: error.message || error,
            timestamp: new Date(),
            stack: error.stack
        };

        this.metrics.errors.recent.push(errorEntry);
        this.metrics.errors.total++;

        // Keep only last 100 errors
        if (this.metrics.errors.recent.length > 100) {
            this.metrics.errors.recent = this.metrics.errors.recent.slice(-100);
        }

        // Calculate error rate (errors per minute)
        const oneMinuteAgo = Date.now() - 60000;
        const recentErrors = this.metrics.errors.recent.filter(e => 
            new Date(e.timestamp).getTime() > oneMinuteAgo
        );
        this.metrics.errors.rate = recentErrors.length;

        // Emit error event
        this.emit('error', errorEntry);
    }

    recordAlert(type, message) {
        const alert = {
            type,
            message,
            timestamp: new Date(),
            severity: this.getAlertSeverity(type)
        };

        this.alertHistory.push(alert);
        
        // Keep only last 1000 alerts
        if (this.alertHistory.length > 1000) {
            this.alertHistory = this.alertHistory.slice(-1000);
        }

        console.warn(`🚨 ALERT [${alert.severity}]: ${type} - ${message}`);
        
        // Emit alert event
        this.emit('alert', alert);
    }

    getAlertSeverity(type) {
        const criticalTypes = ['mongodb_connection_failed', 'api_connection_failed', 'high_disk_usage'];
        const warningTypes = ['high_cpu_usage', 'high_memory_usage', 'api_slow_response'];
        
        if (criticalTypes.includes(type)) return 'critical';
        if (warningTypes.includes(type)) return 'warning';
        return 'info';
    }

    async checkAlerts() {
        // Check error rate threshold
        if (this.metrics.errors.rate > this.config.alertThresholds.errorRate) {
            this.recordAlert('high_error_rate', `Error rate: ${this.metrics.errors.rate} errors/minute`);
        }

        // Check service health
        Object.entries(this.metrics.services).forEach(([service, data]) => {
            if (data.status === 'unhealthy') {
                this.recordAlert(`${service}_unhealthy`, `Service ${service} is unhealthy`);
            }
        });
    }

    async saveMetrics() {
        try {
            const metricsPath = path.join(__dirname, 'metrics');
            await fs.mkdir(metricsPath, { recursive: true });
            
            const timestamp = new Date().toISOString().split('T')[0];
            const metricsFile = path.join(metricsPath, `health-${timestamp}.json`);
            
            const data = {
                timestamp: new Date(),
                metrics: this.metrics,
                recentAlerts: this.alertHistory.slice(-10)
            };
            
            await fs.writeFile(metricsFile, JSON.stringify(data, null, 2));
            
            // Also save to Redis if available
            if (this.redisConnection && this.redisConnection.status === 'ready') {
                await this.redisConnection.setex('health:metrics', 300, JSON.stringify(data));
            }
            
        } catch (error) {
            console.error('Failed to save metrics:', error);
        }
    }

    getStatus() {
        return {
            status: this.metrics.status,
            uptime: this.metrics.uptime,
            lastCheck: this.metrics.lastCheck,
            services: this.metrics.services,
            system: this.metrics.system,
            errors: {
                total: this.metrics.errors.total,
                rate: this.metrics.errors.rate,
                recent: this.metrics.errors.recent.slice(-10)
            },
            alerts: this.alertHistory.slice(-10)
        };
    }

    getDetailedReport() {
        return {
            ...this.getStatus(),
            config: this.config,
            allAlerts: this.alertHistory,
            allErrors: this.metrics.errors.recent
        };
    }
}

export default HealthMonitor;