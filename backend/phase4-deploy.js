#!/usr/bin/env node

// ====================================================================
// PHASE 4 PREVENTIVE HARDENING - DEPLOYMENT SCRIPT
// Complete deployment and validation of bulletproof system
// ====================================================================

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Phase4Deployment {
    constructor() {
        this.deploymentSteps = [
            { name: 'Pre-deployment Backup', execute: this.createBackup.bind(this) },
            { name: 'Dependency Check', execute: this.checkDependencies.bind(this) },
            { name: 'Initialize Phase 4 Components', execute: this.initializeComponents.bind(this) },
            { name: 'Run Integration Tests', execute: this.runIntegrationTests.bind(this) },
            { name: 'Performance Validation', execute: this.validatePerformance.bind(this) },
            { name: 'Security Verification', execute: this.verifySecurityHardening.bind(this) },
            { name: 'Health Dashboard Setup', execute: this.setupHealthDashboard.bind(this) },
            { name: 'Final System Validation', execute: this.finalValidation.bind(this) }
        ];

        this.deploymentResults = {
            startTime: null,
            endTime: null,
            success: false,
            steps: [],
            errors: [],
            warnings: [],
            metrics: {}
        };
    }

    async deploy() {
        console.log('🚀 PHASE 4 PREVENTIVE HARDENING DEPLOYMENT STARTING...');
        console.log('================================================================');
        
        this.deploymentResults.startTime = new Date();
        
        try {
            // Execute deployment steps
            for (const step of this.deploymentSteps) {
                await this.executeStep(step);
            }
            
            this.deploymentResults.success = true;
            this.deploymentResults.endTime = new Date();
            
            await this.generateDeploymentReport();
            
            console.log('================================================================');
            console.log('✅ PHASE 4 DEPLOYMENT COMPLETED SUCCESSFULLY!');
            console.log('🛡️  SYSTEM IS NOW BULLETPROOF!');
            console.log('================================================================');
            
        } catch (error) {
            this.deploymentResults.success = false;
            this.deploymentResults.endTime = new Date();
            this.deploymentResults.errors.push({
                step: 'Deployment',
                error: error.message,
                timestamp: new Date()
            });
            
            console.error('================================================================');
            console.error('❌ PHASE 4 DEPLOYMENT FAILED!');
            console.error('Error:', error.message);
            console.error('================================================================');
            
            // Attempt rollback
            await this.attemptRollback();
            
            throw error;
        }
    }

    async executeStep(step) {
        console.log(`\n🔧 Executing: ${step.name}`);
        console.log('-'.repeat(50));
        
        const stepStart = Date.now();
        
        try {
            const result = await step.execute();
            const executionTime = Date.now() - stepStart;
            
            const stepResult = {
                name: step.name,
                success: true,
                executionTime,
                result,
                timestamp: new Date()
            };
            
            this.deploymentResults.steps.push(stepResult);
            
            console.log(`✅ ${step.name} completed in ${executionTime}ms`);
            
            return stepResult;
            
        } catch (error) {
            const executionTime = Date.now() - stepStart;
            
            const stepResult = {
                name: step.name,
                success: false,
                executionTime,
                error: error.message,
                timestamp: new Date()
            };
            
            this.deploymentResults.steps.push(stepResult);
            this.deploymentResults.errors.push(stepResult);
            
            console.error(`❌ ${step.name} failed after ${executionTime}ms: ${error.message}`);
            
            throw error;
        }
    }

    async createBackup() {
        console.log('📦 Creating system backup before deployment...');
        
        try {
            // Import rollback system
            const { default: automaticRollback } = await import('./monitoring/automaticRollback.js');
            
            const backup = await automaticRollback.createSystemBackup();
            
            return {
                message: 'System backup created successfully',
                backupId: backup.id,
                timestamp: backup.timestamp
            };
            
        } catch (error) {
            console.error('Backup creation failed:', error);
            // Continue deployment but note the issue
            this.deploymentResults.warnings.push({
                step: 'Backup',
                warning: 'Backup creation failed - proceeding without backup',
                error: error.message
            });
            
            return {
                message: 'Backup failed but continuing deployment',
                warning: true
            };
        }
    }

    async checkDependencies() {
        console.log('📦 Checking required dependencies...');
        
        const requiredDependencies = [
            'express',
            'mongoose', 
            'cors',
            'helmet',
            'compression',
            'express-rate-limit',
            'lru-cache',
            'winston'
        ];
        
        const missingDependencies = [];
        const installedVersions = {};
        
        try {
            // Check package.json
            const packageJsonPath = path.join(process.cwd(), 'package.json');
            const packageContent = await fs.readFile(packageJsonPath, 'utf8');
            const packageJson = JSON.parse(packageContent);
            
            const allDeps = {
                ...packageJson.dependencies,
                ...packageJson.devDependencies
            };
            
            for (const dep of requiredDependencies) {
                if (allDeps[dep]) {
                    installedVersions[dep] = allDeps[dep];
                    console.log(`✅ ${dep}: ${allDeps[dep]}`);
                } else {
                    missingDependencies.push(dep);
                    console.log(`❌ ${dep}: NOT FOUND`);
                }
            }
            
            if (missingDependencies.length > 0) {
                throw new Error(`Missing dependencies: ${missingDependencies.join(', ')}`);
            }
            
            // Verify node_modules exist
            const nodeModulesPath = path.join(process.cwd(), 'node_modules');
            await fs.access(nodeModulesPath);
            
            return {
                message: 'All dependencies verified',
                installedVersions,
                totalChecked: requiredDependencies.length
            };
            
        } catch (error) {
            if (error.code === 'ENOENT' && error.path?.includes('node_modules')) {
                console.log('📦 Installing dependencies...');
                
                try {
                    await execAsync('npm install');
                    console.log('✅ Dependencies installed successfully');
                    
                    return {
                        message: 'Dependencies installed and verified',
                        installed: true
                    };
                } catch (installError) {
                    throw new Error(`Failed to install dependencies: ${installError.message}`);
                }
            }
            
            throw error;
        }
    }

    async initializeComponents() {
        console.log('🔧 Initializing Phase 4 components...');
        
        const componentResults = [];
        
        try {
            // Initialize Circuit Breaker Manager
            console.log('🔌 Initializing Circuit Breaker Manager...');
            const { default: circuitBreakerManager } = await import('./monitoring/circuitBreaker.js');
            componentResults.push({ component: 'CircuitBreaker', status: 'initialized' });
            
            // Initialize Advanced MongoDB Manager
            console.log('🗄️ Initializing Advanced MongoDB Manager...');
            const { default: advancedMongoManager } = await import('./monitoring/advancedMongoManager.js');
            await advancedMongoManager.initialize();
            componentResults.push({ component: 'MongoManager', status: 'initialized' });
            
            // Initialize Error Recovery System
            console.log('🔄 Initializing Error Recovery System...');
            const { default: errorRecoverySystem } = await import('./monitoring/errorRecoverySystem.js');
            await errorRecoverySystem.initialize();
            componentResults.push({ component: 'ErrorRecovery', status: 'initialized' });
            
            // Initialize Performance Optimizer
            console.log('⚡ Initializing Performance Optimizer...');
            const { default: performanceOptimizer } = await import('./monitoring/performanceOptimizer.js');
            await performanceOptimizer.initialize();
            componentResults.push({ component: 'PerformanceOptimizer', status: 'initialized' });
            
            // Initialize Security Hardening
            console.log('🔒 Initializing Security Hardening...');
            const { default: securityHardening } = await import('./monitoring/securityHardening.js');
            await securityHardening.initialize();
            componentResults.push({ component: 'SecurityHardening', status: 'initialized' });
            
            // Initialize Comprehensive Testing
            console.log('🧪 Initializing Comprehensive Testing...');
            const { default: comprehensiveTesting } = await import('./monitoring/comprehensiveTesting.js');
            await comprehensiveTesting.initialize();
            componentResults.push({ component: 'ComprehensiveTesting', status: 'initialized' });
            
            console.log(`✅ All ${componentResults.length} components initialized successfully`);
            
            return {
                message: 'All Phase 4 components initialized',
                components: componentResults,
                count: componentResults.length
            };
            
        } catch (error) {
            throw new Error(`Component initialization failed: ${error.message}`);
        }
    }

    async runIntegrationTests() {
        console.log('🧪 Running integration tests...');
        
        try {
            const { default: comprehensiveTesting } = await import('./monitoring/comprehensiveTesting.js');
            
            // Run only integration tests for deployment validation
            await comprehensiveTesting.runIntegrationTests();
            
            const results = comprehensiveTesting.getTestResults();
            const integrationResults = results.integration;
            
            if (integrationResults.failed > 0) {
                const criticalFailures = integrationResults.results.filter(
                    test => !test.passed && test.metadata?.critical
                );
                
                if (criticalFailures.length > 0) {
                    throw new Error(`${criticalFailures.length} critical integration tests failed`);
                }
                
                this.deploymentResults.warnings.push({
                    step: 'Integration Tests',
                    warning: `${integrationResults.failed} non-critical tests failed`,
                    details: integrationResults
                });
            }
            
            return {
                message: 'Integration tests completed',
                passed: integrationResults.passed,
                failed: integrationResults.failed,
                total: integrationResults.total,
                success: integrationResults.failed === 0 || 
                        integrationResults.results.filter(t => !t.passed && t.metadata?.critical).length === 0
            };
            
        } catch (error) {
            throw new Error(`Integration tests failed: ${error.message}`);
        }
    }

    async validatePerformance() {
        console.log('⚡ Validating performance benchmarks...');
        
        try {
            const { default: comprehensiveTesting } = await import('./monitoring/comprehensiveTesting.js');
            
            const performanceResults = await comprehensiveTesting.runPerformanceBenchmarks();
            
            const failedBenchmarks = performanceResults.filter(test => !test.passed);
            
            if (failedBenchmarks.length > 0) {
                this.deploymentResults.warnings.push({
                    step: 'Performance Validation',
                    warning: `${failedBenchmarks.length} performance benchmarks failed`,
                    details: failedBenchmarks
                });
                
                console.log(`⚠️ ${failedBenchmarks.length} performance benchmarks failed (non-blocking)`);
            }
            
            return {
                message: 'Performance validation completed',
                benchmarks: performanceResults.length,
                passed: performanceResults.length - failedBenchmarks.length,
                failed: failedBenchmarks.length
            };
            
        } catch (error) {
            // Performance validation failures are warnings, not deployment blockers
            this.deploymentResults.warnings.push({
                step: 'Performance Validation',
                warning: 'Performance validation failed',
                error: error.message
            });
            
            return {
                message: 'Performance validation failed (non-blocking)',
                warning: true,
                error: error.message
            };
        }
    }

    async verifySecurityHardening() {
        console.log('🔒 Verifying security hardening implementation...');
        
        try {
            const { default: securityHardening } = await import('./monitoring/securityHardening.js');
            
            const securityStatus = securityHardening.getSecurityStatus();
            
            // Verify rate limiters are configured
            const rateLimiters = securityHardening.getRateLimiters();
            
            // Verify security headers are configured
            const securityHeaders = securityHardening.getSecurityHeaders();
            
            const verificationChecks = [
                { name: 'Rate Limiters', passed: !!rateLimiters.general && !!rateLimiters.strict },
                { name: 'Security Headers', passed: Object.keys(securityHeaders).length >= 5 },
                { name: 'Threat Detection', passed: securityStatus.metrics.threats !== undefined },
                { name: 'IP Blocking', passed: securityStatus.blockedIPs !== undefined }
            ];
            
            const failedChecks = verificationChecks.filter(check => !check.passed);
            
            if (failedChecks.length > 0) {
                throw new Error(`Security verification failed: ${failedChecks.map(c => c.name).join(', ')}`);
            }
            
            return {
                message: 'Security hardening verified',
                checks: verificationChecks,
                securityStatus: 'operational'
            };
            
        } catch (error) {
            throw new Error(`Security hardening verification failed: ${error.message}`);
        }
    }

    async setupHealthDashboard() {
        console.log('📊 Setting up health dashboard...');
        
        try {
            const { default: healthDashboard } = await import('./monitoring/healthDashboard.js');
            
            await healthDashboard.initialize();
            
            // Wait a moment for the dashboard to start
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            return {
                message: 'Health dashboard initialized',
                url: 'http://localhost:3001',
                status: 'running'
            };
            
        } catch (error) {
            // Dashboard failure is a warning, not a deployment blocker
            this.deploymentResults.warnings.push({
                step: 'Health Dashboard',
                warning: 'Health dashboard setup failed',
                error: error.message
            });
            
            return {
                message: 'Health dashboard setup failed (non-blocking)',
                warning: true,
                error: error.message
            };
        }
    }

    async finalValidation() {
        console.log('🎯 Performing final system validation...');
        
        try {
            // Initialize Phase 4 Integration System
            const { default: phase4Integration } = await import('./monitoring/phase4Integration.js');
            
            await phase4Integration.initialize();
            
            // Wait for system to stabilize
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Get system status
            const systemStatus = phase4Integration.getSystemStatus();
            
            // Validate system health
            if (systemStatus.health < 80) {
                throw new Error(`System health too low: ${systemStatus.health}%`);
            }
            
            // Check critical components
            const criticalComponents = ['mongoManager', 'errorRecovery', 'securityHardening'];
            const unhealthyComponents = criticalComponents.filter(
                comp => systemStatus.components[comp]?.status !== 'operational'
            );
            
            if (unhealthyComponents.length > 0) {
                throw new Error(`Critical components unhealthy: ${unhealthyComponents.join(', ')}`);
            }
            
            // Store final metrics
            this.deploymentResults.metrics = {
                systemHealth: systemStatus.health,
                componentsOperational: Object.values(systemStatus.components)
                    .filter(comp => comp.status === 'operational').length,
                totalComponents: Object.keys(systemStatus.components).length,
                uptime: systemStatus.uptime,
                alertCount: systemStatus.alerts?.length || 0
            };
            
            return {
                message: 'Final validation passed',
                systemHealth: systemStatus.health,
                componentsOperational: this.deploymentResults.metrics.componentsOperational,
                totalComponents: this.deploymentResults.metrics.totalComponents,
                status: 'bulletproof'
            };
            
        } catch (error) {
            throw new Error(`Final validation failed: ${error.message}`);
        }
    }

    async generateDeploymentReport() {
        console.log('\n📊 Generating deployment report...');
        
        const executionTime = this.deploymentResults.endTime - this.deploymentResults.startTime;
        
        const report = {
            deployment: {
                timestamp: this.deploymentResults.startTime,
                executionTime,
                success: this.deploymentResults.success,
                version: 'Phase 4 Preventive Hardening v1.0'
            },
            steps: this.deploymentResults.steps,
            metrics: this.deploymentResults.metrics,
            errors: this.deploymentResults.errors,
            warnings: this.deploymentResults.warnings,
            summary: {
                totalSteps: this.deploymentSteps.length,
                successfulSteps: this.deploymentResults.steps.filter(s => s.success).length,
                failedSteps: this.deploymentResults.steps.filter(s => !s.success).length,
                warningCount: this.deploymentResults.warnings.length,
                errorCount: this.deploymentResults.errors.length
            },
            recommendations: this.generateRecommendations()
        };
        
        // Save report to file
        const reportFile = path.join(process.cwd(), 'logs', `phase4-deployment-${Date.now()}.json`);
        
        try {
            await fs.mkdir(path.dirname(reportFile), { recursive: true });
            await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
            console.log(`📄 Deployment report saved: ${reportFile}`);
        } catch (error) {
            console.error('Failed to save deployment report:', error);
        }
        
        // Print summary
        this.printDeploymentSummary(report);
        
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.deploymentResults.warnings.length > 0) {
            recommendations.push({
                priority: 'medium',
                category: 'warnings',
                message: `Address ${this.deploymentResults.warnings.length} deployment warnings`,
                details: this.deploymentResults.warnings
            });
        }
        
        if (this.deploymentResults.metrics.systemHealth < 90) {
            recommendations.push({
                priority: 'medium',
                category: 'health',
                message: 'Monitor system health and optimize components',
                target: '90%+ system health'
            });
        }
        
        recommendations.push({
            priority: 'low',
            category: 'monitoring',
            message: 'Regularly check health dashboard for system status',
            url: 'http://localhost:3001'
        });
        
        return recommendations;
    }

    printDeploymentSummary(report) {
        console.log('\n📊 DEPLOYMENT SUMMARY');
        console.log('================================================');
        console.log(`⏱️  Execution Time: ${Math.round(report.deployment.executionTime / 1000)}s`);
        console.log(`✅ Successful Steps: ${report.summary.successfulSteps}/${report.summary.totalSteps}`);
        console.log(`⚠️  Warnings: ${report.summary.warningCount}`);
        console.log(`❌ Errors: ${report.summary.errorCount}`);
        
        if (report.metrics.systemHealth) {
            console.log(`💚 System Health: ${report.metrics.systemHealth}%`);
            console.log(`🔧 Components Operational: ${report.metrics.componentsOperational}/${report.metrics.totalComponents}`);
        }
        
        if (report.warnings.length > 0) {
            console.log('\n⚠️  WARNINGS:');
            report.warnings.forEach(warning => {
                console.log(`   • ${warning.step}: ${warning.warning}`);
            });
        }
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 RECOMMENDATIONS:');
            report.recommendations.forEach(rec => {
                console.log(`   • [${rec.priority.toUpperCase()}] ${rec.message}`);
            });
        }
        
        console.log('\n🎯 NEXT STEPS:');
        console.log('   • Visit health dashboard: http://localhost:3001');
        console.log('   • Monitor system health regularly');
        console.log('   • Review deployment report for any warnings');
        console.log('   • Run comprehensive tests periodically');
        console.log('================================================');
    }

    async attemptRollback() {
        console.log('\n🔄 Attempting automatic rollback...');
        
        try {
            const { default: automaticRollback } = await import('./monitoring/automaticRollback.js');
            
            const rollbackReport = await automaticRollback.executeRollback();
            
            if (rollbackReport.success) {
                console.log('✅ Automatic rollback completed successfully');
            } else {
                console.log('⚠️ Automatic rollback completed with issues');
            }
            
        } catch (error) {
            console.error('❌ Automatic rollback failed:', error.message);
            console.log('Manual intervention may be required');
        }
    }
}

// Main execution
async function main() {
    const deployment = new Phase4Deployment();
    
    try {
        await deployment.deploy();
        process.exit(0);
    } catch (error) {
        console.error('\n💥 DEPLOYMENT FAILED:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (process.argv[1] === __filename) {
    main();
}

export default Phase4Deployment;