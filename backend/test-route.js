import express from 'express'
import aiService from './services/aiService.js'

const app = express()
app.use(express.json())

app.post('/test-ai', async (req, res) => {
  try {
    console.log('Testing AI service...')
    
    const businessInfo = {
      industry: "Fitness Technology",
      productService: "AI-powered fitness app",
      targetAudience: "Busy professionals",
      pricePoint: "$19.99/month"
    }
    
    const settings = {
      sequenceLength: 3,
      tone: "friendly",
      primaryGoal: "sales"
    }
    
    const result = await aiService.generateEmailSequence(businessInfo, settings)
    console.log('AI service result:', result)
    
    res.json({ success: true, data: result })
  } catch (error) {
    console.error('Test error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

app.listen(3002, () => {
  console.log('Test server running on port 3002')
})