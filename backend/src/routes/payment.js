const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');
const { authenticateToken } = require('../middleware/auth');
const { rateLimiter } = require('../middleware/rateLimiter');

// Apply rate limiting to payment endpoints
const paymentLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: 'Too many payment requests, please try again later'
});

// Create checkout session
router.post('/checkout/session', 
  authenticateToken, 
  paymentLimiter,
  paymentController.createCheckoutSession
);

// Create customer portal session
router.post('/portal/session', 
  authenticateToken, 
  paymentLimiter,
  paymentController.createPortalSession
);

// Get subscription status
router.get('/subscription/status', 
  authenticateToken, 
  paymentController.getSubscriptionStatus
);

// Record usage for metered billing
router.post('/usage/record', 
  authenticateToken, 
  paymentController.recordUsage
);

// Stripe webhook endpoint (no auth required)
router.post('/webhook', 
  express.raw({ type: 'application/json' }),
  paymentController.handleWebhook
);

module.exports = router;