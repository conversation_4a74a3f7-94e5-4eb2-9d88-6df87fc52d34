import mongoose from 'mongoose';

console.log('Testing MongoDB connection...\n');

const uri = 'mongodb://127.0.0.1:27017/neurocolony';

async function testConnection() {
  try {
    console.log(`Attempting to connect to: ${uri}`);
    
    await mongoose.connect(uri, {
      serverSelectionTimeoutMS: 5000,
    });
    
    console.log('✅ Successfully connected to MongoDB!');
    
    // Test ping
    await mongoose.connection.db.admin().ping();
    console.log('✅ Ping successful!');
    
    // Get database info
    const admin = mongoose.connection.db.admin();
    const databases = await admin.listDatabases();
    console.log('\nAvailable databases:');
    databases.databases.forEach(db => {
      console.log(`  - ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
    });
    
    await mongoose.disconnect();
    console.log('\n✅ Disconnected successfully');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('\nPossible solutions:');
    console.error('1. Check if MongoDB is running: ps aux | grep mongod');
    console.error('2. Try starting MongoDB: sudo systemctl start mongod');
    console.error('3. Check MongoDB logs: sudo journalctl -u mongod');
    console.error('4. Verify MongoDB is listening on port 27017: netstat -tlnp | grep 27017');
    process.exit(1);
  }
}

testConnection();