import express from 'express'
import { auth } from '../middleware/auth.js'
import onboardingService from '../services/onboardingService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * Get onboarding progress for current user
 */
router.get('/progress', auth, async (req, res) => {
  try {
    const progress = await onboardingService.getOnboardingProgress(req.user.id)
    
    res.json({
      success: true,
      data: progress
    })
  } catch (error) {
    logger.error('Get onboarding progress error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch onboarding progress'
    })
  }
})

/**
 * Get onboarding checklist
 */
router.get('/checklist', auth, async (req, res) => {
  try {
    const checklist = await onboardingService.getOnboardingChecklist(req.user.id)
    
    res.json({
      success: true,
      data: checklist
    })
  } catch (error) {
    logger.error('Get onboarding checklist error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch onboarding checklist'
    })
  }
})

/**
 * Get personalized getting started guide
 */
router.get('/guide', auth, async (req, res) => {
  try {
    const guide = await onboardingService.getPersonalizedGuide(req.user.id)
    
    res.json({
      success: true,
      data: guide
    })
  } catch (error) {
    logger.error('Get personalized guide error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch personalized guide'
    })
  }
})

/**
 * Complete an onboarding step
 */
router.post('/complete-step', auth, async (req, res) => {
  try {
    const { stepName, data } = req.body
    
    if (!stepName) {
      return res.status(400).json({
        success: false,
        message: 'Step name is required'
      })
    }
    
    const result = await onboardingService.completeStep(req.user.id, stepName, data)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Complete onboarding step error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to complete onboarding step'
    })
  }
})

/**
 * Create demo sequence for new users
 */
router.post('/demo-sequence', auth, async (req, res) => {
  try {
    const demoSequence = await onboardingService.createDemoSequence(req.user.id)
    
    res.json({
      success: true,
      data: demoSequence,
      message: 'Demo sequence created successfully'
    })
  } catch (error) {
    logger.error('Create demo sequence error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create demo sequence'
    })
  }
})

/**
 * Track onboarding events
 */
router.post('/track-event', auth, async (req, res) => {
  try {
    const { eventType, metadata } = req.body
    
    if (!eventType) {
      return res.status(400).json({
        success: false,
        message: 'Event type is required'
      })
    }
    
    await onboardingService.trackOnboardingEvent(req.user.id, eventType, metadata)
    
    res.json({
      success: true,
      message: 'Event tracked successfully'
    })
  } catch (error) {
    logger.error('Track onboarding event error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to track event'
    })
  }
})

/**
 * Get activation metrics (admin only)
 */
router.get('/metrics', auth, async (req, res) => {
  try {
    // Add admin check here in production
    const metrics = await onboardingService.getActivationMetrics()
    
    if (!metrics) {
      return res.status(500).json({
        success: false,
        message: 'Metrics not available'
      })
    }
    
    res.json({
      success: true,
      data: metrics
    })
  } catch (error) {
    logger.error('Activation metrics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activation metrics'
    })
  }
})

export default router