import express from 'express';
const router = express.Router();
import {
  submitSupportTicket,
  submitContactForm,
  getContactInfo
} from '../controllers/contactController.js';
import { authenticateToken } from '../middleware/auth.js';

// Rate limiting for contact forms
const contactLimiter = (req, res, next) => {
  // Simple rate limiting - 5 submissions per 15 minutes per IP
  const submissions = req.app.locals.contactSubmissions || new Map();
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  
  const clientSubmissions = submissions.get(clientIP) || [];
  const recentSubmissions = clientSubmissions.filter(time => now - time < windowMs);
  
  if (recentSubmissions.length >= 5) {
    return res.status(429).json({
      success: false,
      error: 'Too many submissions. Please wait 15 minutes before trying again.'
    });
  }
  
  recentSubmissions.push(now);
  submissions.set(clientIP, recentSubmissions);
  req.app.locals.contactSubmissions = submissions;
  
  next();
};

// Get contact information and categories
router.get('/info', getContactInfo);

// Submit support ticket (can be used by authenticated or anonymous users)
router.post('/support', 
  contactLimiter,
  (req, res, next) => {
    // Optional authentication - if token exists, use it, otherwise continue
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (token) {
      return authenticateToken(req, res, next);
    }
    next();
  },
  submitSupportTicket
);

// Submit general contact form
router.post('/contact', 
  contactLimiter,
  submitContactForm
);

export default router;