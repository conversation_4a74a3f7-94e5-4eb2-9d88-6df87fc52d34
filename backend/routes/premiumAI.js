import express from 'express';
const router = express.Router();
import {
  getAvailableProviders,
  generatePremiumSequence,
  generateSubjectLineVariants,
  analyzeSequence,
  getAIUsageStats,
  getModelRecommendations
} from '../controllers/premiumAIController.js';
import { authenticateToken } from '../middleware/auth.js';

// Get available AI providers based on user's plan
router.get('/providers', 
  authenticateToken,
  getAvailableProviders
);

// Generate sequence with premium AI models
router.post('/generate/sequence', 
  authenticateToken,
  generatePremiumSequence
);

// Generate A/B test subject line variants
router.post('/generate/subject-variants', 
  authenticateToken,
  generateSubjectLineVariants
);

// Analyze and optimize existing sequence
router.post('/analyze/:sequenceId', 
  authenticateToken,
  analyzeSequence
);

// Get AI usage statistics
router.get('/usage/stats', 
  authenticateToken,
  getAIUsageStats
);

// Get model recommendations
router.get('/recommendations', 
  authenticateToken,
  getModelRecommendations
);

export default router;