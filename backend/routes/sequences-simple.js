import express from 'express'
import { body, validationResult } from 'express-validator'
import SimpleSequence from '../models/SimpleSequence.js'
import { auth } from '../middleware/auth.js'
import aiService from '../services/aiService-simple.js'
// import { logger } from '../utils/logger.js'

const router = express.Router()

// Get all sequences for user
router.get('/', auth, async (req, res) => {
  try {
    const sequences = await SimpleSequence.find({ user: req.user.id })
      .sort({ createdAt: -1 })
      .select('-content') // Exclude content for list view
      
    res.json({ 
      success: true, 
      sequences,
      count: sequences.length 
    })
  } catch (error) {
    logger.error('Error fetching sequences:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Server error while fetching sequences' 
    })
  }
})

// Get single sequence
router.get('/:id', auth, async (req, res) => {
  try {
    const sequence = await SimpleSequence.findOne({
      _id: req.params.id,
      user: req.user.id
    })

    if (!sequence) {
      return res.status(404).json({ 
        success: false, 
        message: 'Sequence not found' 
      })
    }

    res.json({ success: true, sequence })
  } catch (error) {
    logger.error('Error fetching sequence:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Server error while fetching sequence' 
    })
  }
})

// Test endpoint
router.post('/test', auth, async (req, res) => {
  try {
    console.log('🧪 Test endpoint hit by user:', req.user.id)
    
    const testSequence = new SimpleSequence({
      user: req.user.id,
      title: 'Test Sequence',
      description: 'Test description',
      emails: [{
        subject: 'Test Subject',
        content: 'Test content',
        emailNumber: 1
      }],
      tone: 'professional',
      industry: 'general',
      status: 'draft',
      aiModel: 'test'
    })
    
    await testSequence.save()
    
    res.json({ success: true, message: 'Test sequence created', id: testSequence._id })
  } catch (error) {
    console.error('❌ Test error:', error.message)
    res.status(500).json({ success: false, message: error.message })
  }
})

// Generate new sequence
router.post('/generate', auth, async (req, res) => {
  try {
    console.log('🚀 Starting sequence generation for user:', req.user.id)
    
    // Basic validation
    const { prompt, title, sequenceLength = 3, tone = 'professional', industry = 'general' } = req.body
    
    if (!prompt || prompt.length < 10) {
      return res.status(400).json({ 
        success: false, 
        message: 'Prompt must be at least 10 characters' 
      })
    }
    
    if (!title || title.length < 1) {
      return res.status(400).json({ 
        success: false, 
        message: 'Title is required' 
      })
    }

    console.log('🔧 Generating sequence:', title)

    // Generate template sequence
    const templateSequence = [
      {
        subject: "Welcome! Let's get started 🚀",
        content: `Hi there! Thank you for your interest in ${industry} solutions. We're excited to help you achieve your goals with our proven strategies.`,
        emailNumber: 1
      },
      {
        subject: "Your exclusive resources are ready",
        content: `We've prepared something special for you. Access our comprehensive guide that has helped thousands of ${industry} professionals succeed.`,
        emailNumber: 2
      },
      {
        subject: "Quick wins you can implement today",
        content: `Here are three simple ${industry} strategies you can start using immediately to see results in your business.`,
        emailNumber: 3
      },
      {
        subject: "Success story: Real results achieved",
        content: `Learn from this inspiring case study of someone in ${industry} who transformed their approach and achieved remarkable success.`,
        emailNumber: 4
      },
      {
        subject: "Final opportunity - don't miss out",
        content: `This is your chance to take action and create the change you've been waiting for in your ${industry} journey.`,
        emailNumber: 5
      }
    ]

    const selectedEmails = templateSequence.slice(0, Math.min(sequenceLength, 5))
    console.log('✅ Generated', selectedEmails.length, 'emails')

    // Save to database
    const emailSequence = new SimpleSequence({
      user: req.user.id,
      title,
      description: prompt,
      emails: selectedEmails,
      tone,
      industry,
      status: 'draft',
      aiModel: 'template'
    })

    await emailSequence.save()
    console.log('✅ Sequence saved with ID:', emailSequence._id)

    res.status(201).json({ 
      success: true, 
      sequence: emailSequence,
      message: 'Email sequence generated successfully'
    })

  } catch (error) {
    console.error('❌ Generate error:', error.message)
    res.status(500).json({ 
      success: false, 
      message: 'Server error: ' + error.message 
    })
  }
})

// Delete sequence
router.delete('/:id', auth, async (req, res) => {
  try {
    const sequence = await SimpleSequence.findOneAndDelete({
      _id: req.params.id,
      user: req.user.id
    })

    if (!sequence) {
      return res.status(404).json({ 
        success: false, 
        message: 'Sequence not found' 
      })
    }

    console.log('🗑️ Email sequence deleted for user:', req.user.id)

    res.json({ 
      success: true, 
      message: 'Sequence deleted successfully' 
    })
  } catch (error) {
    console.error('❌ Error deleting sequence:', error.message)
    res.status(500).json({ 
      success: false, 
      message: 'Server error while deleting sequence' 
    })
  }
})

export default router