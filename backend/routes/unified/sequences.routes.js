/**
 * Unified Email Sequences Routes
 * Consolidates all sequence-related endpoints from multiple route files
 */

const express = require('express');
const router = express.Router();
const { auth } = require('../../middleware/auth');
const { validateRequest } = require('../../middleware/validation');
const { body, param, query } = require('express-validator');
const sequenceController = require('../../controllers/unified/sequence.controller');
const { cache } = require('../../config/redis');
const { featureFlags } = require('../../config/features');

// Validation schemas
const sequenceValidation = {
  create: [
    body('topic').notEmpty().trim().withMessage('Topic is required'),
    body('tone').optional().isIn(['professional', 'friendly', 'casual', 'formal']),
    body('length').optional().isInt({ min: 1, max: 10 }),
    body('targetAudience').optional().trim(),
    body('goals').optional().isArray()
  ],
  update: [
    param('id').isMongoId().withMessage('Invalid sequence ID'),
    body('name').optional().trim(),
    body('emails').optional().isArray(),
    body('status').optional().isIn(['draft', 'active', 'paused', 'completed'])
  ],
  query: [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status').optional().isIn(['draft', 'active', 'paused', 'completed']),
    query('sort').optional().isIn(['createdAt', '-createdAt', 'name', '-name'])
  ]
};

// Cache middleware for read operations
const cacheMiddleware = (duration = 300) => async (req, res, next) => {
  if (!featureFlags.caching) return next();
  
  const key = `sequences:${req.user.id}:${req.originalUrl}`;
  const cached = await cache.get(key);
  
  if (cached) {
    return res.json(cached);
  }
  
  // Store the original json method
  const originalJson = res.json;
  res.json = function(data) {
    // Cache the response
    cache.set(key, data, duration);
    // Call the original json method
    return originalJson.call(this, data);
  };
  
  next();
};

// Routes

// Generate new sequence (AI-powered)
router.post(
  '/generate',
  auth,
  sequenceValidation.create,
  validateRequest,
  sequenceController.generateSequence
);

// Create sequence manually
router.post(
  '/',
  auth,
  sequenceValidation.create,
  validateRequest,
  sequenceController.createSequence
);

// Get all sequences for user
router.get(
  '/',
  auth,
  sequenceValidation.query,
  validateRequest,
  cacheMiddleware(300),
  sequenceController.getUserSequences
);

// Get single sequence
router.get(
  '/:id',
  auth,
  param('id').isMongoId(),
  validateRequest,
  cacheMiddleware(600),
  sequenceController.getSequenceById
);

// Update sequence
router.put(
  '/:id',
  auth,
  sequenceValidation.update,
  validateRequest,
  sequenceController.updateSequence
);

// Delete sequence
router.delete(
  '/:id',
  auth,
  param('id').isMongoId(),
  validateRequest,
  sequenceController.deleteSequence
);

// Clone sequence
router.post(
  '/:id/clone',
  auth,
  param('id').isMongoId(),
  validateRequest,
  sequenceController.cloneSequence
);

// Export sequence
router.get(
  '/:id/export',
  auth,
  param('id').isMongoId(),
  query('format').optional().isIn(['json', 'csv', 'html']),
  validateRequest,
  sequenceController.exportSequence
);

// Schedule sequence
router.post(
  '/:id/schedule',
  auth,
  param('id').isMongoId(),
  body('sendAt').isISO8601().withMessage('Valid date required'),
  validateRequest,
  sequenceController.scheduleSequence
);

// Get sequence analytics
if (featureFlags.analytics) {
  router.get(
    '/:id/analytics',
    auth,
    param('id').isMongoId(),
    validateRequest,
    cacheMiddleware(300),
    sequenceController.getSequenceAnalytics
  );
}

// Bulk operations (if enabled)
if (featureFlags.advancedDashboard) {
  router.post(
    '/bulk/delete',
    auth,
    body('ids').isArray().withMessage('Array of IDs required'),
    validateRequest,
    sequenceController.bulkDeleteSequences
  );
  
  router.post(
    '/bulk/export',
    auth,
    body('ids').isArray().withMessage('Array of IDs required'),
    body('format').optional().isIn(['json', 'csv', 'zip']),
    validateRequest,
    sequenceController.bulkExportSequences
  );
}

// AI-powered features (if enabled)
if (featureFlags.aiEngine) {
  // Optimize sequence
  router.post(
    '/:id/optimize',
    auth,
    param('id').isMongoId(),
    validateRequest,
    sequenceController.optimizeSequence
  );
  
  // Get AI suggestions
  router.get(
    '/:id/suggestions',
    auth,
    param('id').isMongoId(),
    validateRequest,
    cacheMiddleware(600),
    sequenceController.getAISuggestions
  );
  
  // A/B test generation
  router.post(
    '/:id/ab-test',
    auth,
    param('id').isMongoId(),
    body('variations').optional().isInt({ min: 2, max: 5 }),
    validateRequest,
    sequenceController.generateABTestVariations
  );
}

// Template library (if enabled)
if (featureFlags.emailSequences) {
  router.get(
    '/templates/library',
    auth,
    query('category').optional(),
    query('industry').optional(),
    validateRequest,
    cacheMiddleware(3600),
    sequenceController.getTemplateLibrary
  );
  
  router.post(
    '/templates/save',
    auth,
    body('sequenceId').isMongoId(),
    body('name').notEmpty(),
    body('description').optional(),
    validateRequest,
    sequenceController.saveAsTemplate
  );
}

module.exports = router;