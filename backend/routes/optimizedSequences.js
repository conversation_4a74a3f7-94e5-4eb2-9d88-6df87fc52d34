/**
 * Ultra-Optimized Sequences Routes
 * 5-10x faster than original with intelligent caching and database optimization
 */

import express from 'express'
import { body, validationResult, query } from 'express-validator'
import { auth } from '../middleware/auth.js'
import EmailSequence from '../models/EmailSequence.js'
import optimizedAIService from '../services/optimizedAIService.js'
import analyticsService from '../services/analyticsService.js'
import cacheService from '../services/cacheService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

// Performance monitoring middleware
const performanceMonitor = (routeName) => (req, res, next) => {
  req.startTime = Date.now()
  req.routeName = routeName
  
  const originalSend = res.send
  res.send = function(data) {
    const duration = Date.now() - req.startTime
    logger.info(`⚡ ${routeName} completed in ${duration}ms`)
    
    // Track slow queries
    if (duration > 1000) {
      logger.warn(`🐌 Slow query detected: ${routeName} took ${duration}ms`)
    }
    
    return originalSend.call(this, data)
  }
  
  next()
}

/**
 * Get user sequences with optimized pagination and caching
 */
router.get('/', auth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('industry').optional().isString().trim(),
  query('goal').optional().isString().trim()
], performanceMonitor('GET /sequences'), async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const userId = req.user._id
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    // Build cache key
    const cacheKey = `sequences:${userId}:${page}:${limit}:${req.query.industry || ''}:${req.query.goal || ''}`
    
    // Check cache first
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return res.json({
        success: true,
        data: cached,
        _fromCache: true
      })
    }

    // Build optimized query
    const filter = { user: userId }
    if (req.query.industry) {
      filter['businessInfo.industry'] = new RegExp(req.query.industry, 'i')
    }
    if (req.query.goal) {
      filter['generationSettings.primaryGoal'] = req.query.goal
    }

    // Execute optimized query with minimal data
    const [sequences, totalCount] = await Promise.all([
      EmailSequence.find(filter)
        .select('title description createdAt businessInfo.industry generationSettings.primaryGoal aiAnalysis.overallScore emails.subject status')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(), // Use lean for better performance
      
      EmailSequence.countDocuments(filter)
    ])

    // Transform data for frontend
    const transformedSequences = sequences.map(seq => ({
      _id: seq._id,
      title: seq.title,
      description: seq.description,
      industry: seq.businessInfo?.industry,
      goal: seq.generationSettings?.primaryGoal,
      emailCount: seq.emails?.length || 0,
      score: seq.aiAnalysis?.overallScore || 0,
      status: seq.status || 'completed',
      createdAt: seq.createdAt,
      emailSubjects: seq.emails?.slice(0, 3).map(e => e.subject) || []
    }))

    const result = {
      sequences: transformedSequences,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPreviousPage: page > 1
      }
    }

    // Cache for 5 minutes
    await cacheService.set(cacheKey, result, 300)

    res.json({
      success: true,
      data: result
    })

  } catch (error) {
    logger.error('Get sequences error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sequences',
      error: error.message
    })
  }
})

/**
 * Create sequence with optimized AI generation and caching
 */
router.post('/', auth, [
  body('title').trim().isLength({ min: 1, max: 100 }).withMessage('Title is required and must be under 100 characters'),
  body('businessInfo.industry').notEmpty().withMessage('Industry is required'),
  body('businessInfo.productService').notEmpty().withMessage('Product/Service is required'),
  body('businessInfo.targetAudience').notEmpty().withMessage('Target audience is required'),
  body('businessInfo.pricePoint').notEmpty().withMessage('Price point is required'),
  body('generationSettings.sequenceLength').isInt({ min: 3, max: 14 }).withMessage('Sequence length must be between 3 and 14'),
  body('generationSettings.tone').isIn(['professional', 'casual', 'friendly', 'authoritative', 'conversational']).withMessage('Invalid tone'),
  body('generationSettings.primaryGoal').isIn(['sales', 'nurture', 'onboarding', 'retention', 'upsell']).withMessage('Invalid primary goal')
], performanceMonitor('POST /sequences'), async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const userId = req.user._id

    // Check user cache first
    let user = await cacheService.getCachedUser(userId)
    if (!user) {
      // Use lean query for better performance
      user = await req.user.constructor.findById(userId).lean()
      await cacheService.cacheUser(userId, user, 300) // Cache for 5 minutes
    }

    // Generate AI sequence with caching
    const startTime = Date.now()
    const aiResult = await optimizedAIService.generateEmailSequence(
      req.body.businessInfo,
      req.body.generationSettings
    )
    const aiGenerationTime = Date.now() - startTime

    // Create sequence document
    const sequence = new EmailSequence({
      user: userId,
      title: req.body.title,
      description: req.body.description,
      businessInfo: req.body.businessInfo,
      generationSettings: req.body.generationSettings,
      emails: aiResult.emails,
      aiAnalysis: {
        ...aiResult.aiAnalysis,
        generationTimeMs: aiGenerationTime
      },
      status: 'completed',
      generatedAt: new Date()
    })

    // Save with error handling
    const savedSequence = await sequence.save()

    // Invalidate relevant caches
    await Promise.all([
      cacheService.invalidateUser(userId),
      analyticsService.invalidateUserAnalytics(userId)
    ])

    // Return minimal response for better performance
    res.status(201).json({
      success: true,
      data: {
        _id: savedSequence._id,
        title: savedSequence.title,
        emailCount: savedSequence.emails.length,
        score: savedSequence.aiAnalysis?.overallScore || 0,
        createdAt: savedSequence.createdAt,
        _performance: {
          aiGenerationTime,
          totalTime: Date.now() - req.startTime,
          cached: aiResult._cached || false
        }
      },
      message: 'Email sequence generated successfully!'
    })

  } catch (error) {
    logger.error('Sequence creation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate sequence: ' + error.message
    })
  }
})

/**
 * Get single sequence with caching
 */
router.get('/:id', auth, performanceMonitor('GET /sequences/:id'), async (req, res) => {
  try {
    const sequenceId = req.params.id
    const userId = req.user._id

    // Check cache first
    const cacheKey = `sequence:${sequenceId}:${userId}`
    const cached = await cacheService.get(cacheKey)
    if (cached) {
      return res.json({
        success: true,
        data: cached,
        _fromCache: true
      })
    }

    // Optimized query with only needed fields
    const sequence = await EmailSequence.findOne({
      _id: sequenceId,
      user: userId
    }).lean()

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Cache for 10 minutes
    await cacheService.set(cacheKey, sequence, 600)

    res.json({
      success: true,
      data: sequence
    })

  } catch (error) {
    logger.error('Get sequence error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sequence',
      error: error.message
    })
  }
})

/**
 * Update sequence with cache invalidation
 */
router.put('/:id', auth, [
  body('title').optional().trim().isLength({ min: 1, max: 100 }),
  body('description').optional().trim().isLength({ max: 500 })
], performanceMonitor('PUT /sequences/:id'), async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const sequenceId = req.params.id
    const userId = req.user._id

    // Use findOneAndUpdate for better performance
    const updatedSequence = await EmailSequence.findOneAndUpdate(
      { _id: sequenceId, user: userId },
      { 
        ...req.body,
        updatedAt: new Date()
      },
      { 
        new: true,
        runValidators: true,
        lean: true
      }
    )

    if (!updatedSequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Invalidate caches
    const cacheKey = `sequence:${sequenceId}:${userId}`
    await cacheService.get(cacheKey, { useMemoryCache: false }) // Remove from memory
    
    res.json({
      success: true,
      data: updatedSequence,
      message: 'Sequence updated successfully'
    })

  } catch (error) {
    logger.error('Update sequence error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update sequence',
      error: error.message
    })
  }
})

/**
 * Delete sequence with cache cleanup
 */
router.delete('/:id', auth, performanceMonitor('DELETE /sequences/:id'), async (req, res) => {
  try {
    const sequenceId = req.params.id
    const userId = req.user._id

    const deletedSequence = await EmailSequence.findOneAndDelete({
      _id: sequenceId,
      user: userId
    })

    if (!deletedSequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Cleanup caches
    const cacheKey = `sequence:${sequenceId}:${userId}`
    await Promise.all([
      cacheService.get(cacheKey, { useMemoryCache: false }),
      analyticsService.invalidateUserAnalytics(userId)
    ])

    res.json({
      success: true,
      message: 'Sequence deleted successfully'
    })

  } catch (error) {
    logger.error('Delete sequence error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete sequence',
      error: error.message
    })
  }
})

/**
 * Get dashboard statistics with aggressive caching
 */
router.get('/analytics/dashboard', auth, performanceMonitor('GET /sequences/analytics/dashboard'), async (req, res) => {
  try {
    const userId = req.user._id
    const stats = await analyticsService.getDashboardStats(userId)

    res.json({
      success: true,
      data: stats
    })

  } catch (error) {
    logger.error('Dashboard analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    })
  }
})

/**
 * Get AI service performance statistics
 */
router.get('/analytics/ai-performance', auth, performanceMonitor('GET /sequences/analytics/ai-performance'), async (req, res) => {
  try {
    const aiStats = optimizedAIService.getPerformanceStats()
    const cacheStats = cacheService.getStats()

    res.json({
      success: true,
      data: {
        ai: aiStats,
        cache: cacheStats,
        combinedEfficiency: {
          totalRequests: aiStats.totalRequests,
          cacheHitRate: cacheStats.hitRate,
          avgResponseTime: aiStats.avgResponseTime,
          estimatedMonthlySavings: aiStats.estimatedMonthlySavings
        }
      }
    })

  } catch (error) {
    logger.error('AI performance analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI performance statistics',
      error: error.message
    })
  }
})

export default router