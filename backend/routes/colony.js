import express from 'express'
import ColonyAgent from '../models/ColonyAgent.js'
import ColonyExecution from '../models/ColonyExecution.js'
import ColonyWorkflow from '../models/ColonyWorkflow.js'
import colonyIntelligenceService from '../services/colonyIntelligenceService.js'
import marketingAgentTemplates from '../services/marketingAgentTemplates.js'
import { auth } from '../middleware/auth.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * NeuroColony API Routes - Colony Intelligence Management
 * Provides REST API for managing Queen/Worker/Scout agents and workflows
 * Surpasses n8n with marketing-first approach and autonomous collaboration
 */

// ============================================================================
// COLONY MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/colony/status
 * Get overall colony status and performance metrics
 */
router.get('/status', auth, async (req, res) => {
  try {
    const userId = req.user.id
    
    // Get user's agents by type
    const agents = await ColonyAgent.aggregate([
      { $match: { owner: userId, status: 'active' } },
      { $group: { 
        _id: '$agentType', 
        count: { $sum: 1 },
        avgSuccessRate: { $avg: '$performance.successRate' }
      }}
    ])
    
    // Get recent executions
    const recentExecutions = await ColonyExecution.find({ user: userId })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('agent', 'name agentType category')
    
    // Get active workflows
    const activeWorkflows = await ColonyWorkflow.countDocuments({ 
      owner: userId, 
      status: 'active' 
    })
    
    // Calculate colony performance
    const colonyPerformance = await ColonyExecution.getColonyPerformance(userId, '24h')
    
    res.json({
      success: true,
      data: {
        agentsByType: agents,
        recentExecutions,
        activeWorkflows,
        performance: colonyPerformance,
        timestamp: new Date()
      }
    })
  } catch (error) {
    logger.error('Failed to get colony status:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve colony status',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/create
 * Create a new colony with Queen/Worker/Scout hierarchy
 */
router.post('/create', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const { name, description, marketingContext, agentTypes, useTemplates = true } = req.body

    if (useTemplates) {
      // Create colony from marketing templates
      const colonyConfig = {
        industry: marketingContext?.industry || 'general',
        targetAudience: marketingContext?.targetAudience || 'small_business',
        objectives: marketingContext?.objectives || ['lead_generation', 'email_marketing']
      }

      const colony = await marketingAgentTemplates.createMarketingColony(userId, colonyConfig)

      res.json({
        success: true,
        message: 'Marketing colony created successfully from templates',
        data: colony
      })
    } else {
      // Create custom colony
      const colonyConfig = {
        name: name || 'Marketing Colony',
        description,
        marketingContext: marketingContext || {},
        agentTypes: agentTypes || ['email_marketing', 'content_creation', 'analytics_reporting']
      }

      const colony = await colonyIntelligenceService.createColony(userId, colonyConfig)

      res.json({
        success: true,
        message: 'Custom colony created successfully',
        data: colony
      })
    }
  } catch (error) {
    logger.error('Failed to create colony:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create colony',
      error: error.message
    })
  }
})

/**
 * GET /api/colony/templates
 * Get available marketing agent templates
 */
router.get('/templates', auth, async (req, res) => {
  try {
    const templates = marketingAgentTemplates.getAllTemplates()

    // Organize templates by category and type
    const organizedTemplates = {
      queens: templates.filter(t => t.agentType === 'queen'),
      workers: templates.filter(t => t.agentType === 'worker'),
      scouts: templates.filter(t => t.agentType === 'scout')
    }

    res.json({
      success: true,
      data: {
        templates: organizedTemplates,
        total: templates.length
      }
    })
  } catch (error) {
    logger.error('Failed to get templates:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve templates',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/agents/from-template
 * Create agent from template
 */
router.post('/agents/from-template', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const { templateKey, customizations } = req.body

    const agent = await marketingAgentTemplates.createAgentFromTemplate(
      templateKey,
      userId,
      customizations
    )

    res.status(201).json({
      success: true,
      message: 'Agent created from template successfully',
      data: agent
    })
  } catch (error) {
    logger.error('Failed to create agent from template:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create agent from template',
      error: error.message
    })
  }
})

// ============================================================================
// AGENT MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/colony/agents
 * Get all user's agents with filtering and pagination
 */
router.get('/agents', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const { 
      agentType, 
      category, 
      status = 'active',
      page = 1, 
      limit = 20,
      search 
    } = req.query
    
    // Build query
    const query = { owner: userId }
    if (agentType) query.agentType = agentType
    if (category) query.category = category
    if (status) query.status = status
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }
    
    // Execute query with pagination
    const agents = await ColonyAgent.find(query)
      .populate('parentQueen', 'name agentType')
      .populate('subordinateAgents', 'name agentType')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
    
    const total = await ColonyAgent.countDocuments(query)
    
    res.json({
      success: true,
      data: {
        agents,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Failed to get agents:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve agents',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/agents
 * Create a new agent
 */
router.post('/agents', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const agentData = {
      ...req.body,
      owner: userId
    }
    
    const agent = new ColonyAgent(agentData)
    await agent.save()
    
    res.status(201).json({
      success: true,
      message: 'Agent created successfully',
      data: agent
    })
  } catch (error) {
    logger.error('Failed to create agent:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create agent',
      error: error.message
    })
  }
})

/**
 * GET /api/colony/agents/:id
 * Get specific agent details
 */
router.get('/agents/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const agentId = req.params.id
    
    const agent = await ColonyAgent.findOne({ _id: agentId, owner: userId })
      .populate('parentQueen', 'name agentType')
      .populate('subordinateAgents', 'name agentType')
      .populate('collaboratingAgents.agent', 'name agentType')
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      })
    }
    
    // Get recent executions for this agent
    const recentExecutions = await ColonyExecution.find({ agent: agentId })
      .sort({ createdAt: -1 })
      .limit(10)
    
    res.json({
      success: true,
      data: {
        agent,
        recentExecutions
      }
    })
  } catch (error) {
    logger.error('Failed to get agent:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve agent',
      error: error.message
    })
  }
})

/**
 * PUT /api/colony/agents/:id
 * Update agent configuration
 */
router.put('/agents/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const agentId = req.params.id
    
    const agent = await ColonyAgent.findOneAndUpdate(
      { _id: agentId, owner: userId },
      req.body,
      { new: true, runValidators: true }
    )
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      })
    }
    
    res.json({
      success: true,
      message: 'Agent updated successfully',
      data: agent
    })
  } catch (error) {
    logger.error('Failed to update agent:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update agent',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/agents/:id/execute
 * Execute a specific agent
 */
router.post('/agents/:id/execute', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const agentId = req.params.id
    const { inputData, context } = req.body
    
    const agent = await ColonyAgent.findOne({ _id: agentId, owner: userId })
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      })
    }
    
    // Execute agent with colony intelligence
    const result = await agent.executeWithColony(inputData || {}, context || {})
    
    res.json({
      success: true,
      message: 'Agent executed successfully',
      data: result
    })
  } catch (error) {
    logger.error('Failed to execute agent:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to execute agent',
      error: error.message
    })
  }
})

// ============================================================================
// WORKFLOW MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/colony/workflows
 * Get user's workflows
 */
router.get('/workflows', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const { status, page = 1, limit = 20 } = req.query
    
    const query = { owner: userId }
    if (status) query.status = status
    
    const workflows = await ColonyWorkflow.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
    
    const total = await ColonyWorkflow.countDocuments(query)
    
    res.json({
      success: true,
      data: {
        workflows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Failed to get workflows:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve workflows',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/workflows
 * Create a new workflow
 */
router.post('/workflows', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const workflowData = {
      ...req.body,
      owner: userId
    }
    
    const workflow = new ColonyWorkflow(workflowData)
    await workflow.save()
    
    res.status(201).json({
      success: true,
      message: 'Workflow created successfully',
      data: workflow
    })
  } catch (error) {
    logger.error('Failed to create workflow:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create workflow',
      error: error.message
    })
  }
})

/**
 * POST /api/colony/workflows/:id/execute
 * Execute a workflow
 */
router.post('/workflows/:id/execute', auth, async (req, res) => {
  try {
    const userId = req.user.id
    const workflowId = req.params.id
    const { inputData, context } = req.body
    
    const workflow = await ColonyWorkflow.findOne({ _id: workflowId, owner: userId })
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      })
    }
    
    const result = await workflow.execute(inputData || {}, context || {})
    
    res.json({
      success: true,
      message: 'Workflow executed successfully',
      data: result
    })
  } catch (error) {
    logger.error('Failed to execute workflow:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to execute workflow',
      error: error.message
    })
  }
})

export default router
