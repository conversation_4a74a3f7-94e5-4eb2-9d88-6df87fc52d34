import express from 'express';
const router = express.Router();
import {
  createCheckoutSession,
  createPortalSession,
  getSubscriptionStatus,
  recordUsage,
  handleWebhook
} from '../controllers/paymentController.js';
import { authenticateToken } from '../middleware/auth.js';
import AnalyticsService from '../services/analyticsService.js';

// Rate limiting for payment endpoints
const paymentLimiter = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10 // Limit each IP to 10 requests per windowMs
};

// Create checkout session
router.post('/checkout/session', 
  authenticateToken,
  createCheckoutSession
);

// Create customer portal session
router.post('/portal/session', 
  authenticateToken,
  createPortalSession
);

// Get subscription status
router.get('/subscription/status', 
  authenticateToken,
  getSubscriptionStatus
);

// Record usage for metered billing
router.post('/usage/record', 
  authenticateToken,
  recordUsage
);

// Stripe webhook endpoint (no auth required)
router.post('/webhook', 
  express.raw({ type: 'application/json' }),
  handleWebhook
);

// Billing analytics endpoints
router.get('/analytics/billing', 
  authenticateToken,
  async (req, res) => {
    try {
      const analytics = await AnalyticsService.prototype.getBillingAnalytics(req.user.id);
      res.json({ success: true, data: analytics });
    } catch (error) {
      console.error('Error getting billing analytics:', error);
      res.status(500).json({ success: false, error: 'Failed to get billing analytics' });
    }
  }
);

router.get('/analytics/usage-trends', 
  authenticateToken,
  async (req, res) => {
    try {
      const trends = await AnalyticsService.prototype.getUsageTrends(req.user.id);
      res.json({ success: true, data: trends });
    } catch (error) {
      console.error('Error getting usage trends:', error);
      res.status(500).json({ success: false, error: 'Failed to get usage trends' });
    }
  }
);

// Billing history endpoint
router.get('/billing/history', 
  authenticateToken,
  async (req, res) => {
    try {
      // Mock billing history for now - implement with actual Stripe data later
      const history = [
        {
          date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          description: 'Pro Plan - Monthly Subscription',
          amount: 29.00,
          status: 'paid'
        },
        {
          date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          description: 'Pro Plan - Monthly Subscription',
          amount: 29.00,
          status: 'paid'
        }
      ];
      
      res.json({ success: true, history });
    } catch (error) {
      console.error('Error getting billing history:', error);
      res.status(500).json({ success: false, error: 'Failed to get billing history' });
    }
  }
);

// Invoices endpoint
router.get('/invoices', 
  authenticateToken,
  async (req, res) => {
    try {
      // Mock invoices for now - implement with actual Stripe data later
      const invoices = [
        {
          id: 'inv_1234567890',
          number: 'INV-001',
          date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          amount: 29.00,
          status: 'paid'
        }
      ];
      
      res.json({ success: true, invoices });
    } catch (error) {
      console.error('Error getting invoices:', error);
      res.status(500).json({ success: false, error: 'Failed to get invoices' });
    }
  }
);

export default router;