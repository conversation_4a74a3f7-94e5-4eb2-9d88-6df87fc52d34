import express from 'express'
import axios from 'axios'
import performanceMonitor from '../middleware/performanceMonitor.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

// Enhanced health check endpoint
router.get('/system', async (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {},
    performance: {},
    issues: []
  }
  
  try {
    // Check Ollama AI service
    try {
      const ollamaStart = Date.now()
      await axios.get('http://localhost:11434/api/tags', { timeout: 5000 })
      const ollamaTime = Date.now() - ollamaStart
      
      healthCheck.services.ollama = {
        status: 'healthy',
        responseTime: `${ollamaTime}ms`,
        endpoint: 'http://localhost:11434'
      }
    } catch (error) {
      healthCheck.services.ollama = {
        status: 'unhealthy',
        error: error.message
      }
      healthCheck.issues.push('Ollama AI service unavailable')
      healthCheck.status = 'degraded'
    }
    
    // Check MongoDB
    try {
      // This would require importing mongoose or your DB connection
      healthCheck.services.mongodb = {
        status: 'healthy',
        note: 'Connection check not implemented'
      }
    } catch (error) {
      healthCheck.services.mongodb = {
        status: 'unhealthy',
        error: error.message
      }
      healthCheck.issues.push('MongoDB connection issues')
      healthCheck.status = 'unhealthy'
    }
    
    // Check Redis
    try {
      healthCheck.services.redis = {
        status: 'healthy',
        note: 'Connection check not implemented'
      }
    } catch (error) {
      healthCheck.services.redis = {
        status: 'unhealthy',
        error: error.message
      }
      healthCheck.issues.push('Redis connection issues')
    }
    
    // Get performance metrics
    healthCheck.performance = performanceMonitor.getHealthStatus()
    
    // Overall status determination
    if (healthCheck.issues.length === 0) {
      healthCheck.status = 'healthy'
    } else if (healthCheck.services.ollama?.status === 'healthy') {
      healthCheck.status = 'degraded'
    } else {
      healthCheck.status = 'unhealthy'
    }
    
    // Log health check results
    logger.info('🔍 System health check completed', {
      status: healthCheck.status,
      issues: healthCheck.issues.length,
      ollamaStatus: healthCheck.services.ollama?.status
    })
    
    // Return appropriate HTTP status
    const httpStatus = healthCheck.status === 'healthy' ? 200 : 
                      healthCheck.status === 'degraded' ? 200 : 503
    
    res.status(httpStatus).json(healthCheck)
    
  } catch (error) {
    logger.error('❌ Health check failed', { error: error.message })
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      services: {},
      performance: {},
      issues: ['Health check system failure']
    })
  }
})

// Simple health check for load balancers
router.get('/', (req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString() 
  })
})

// Performance metrics endpoint
router.get('/metrics', (req, res) => {
  const metrics = performanceMonitor.getHealthStatus()
  res.json(metrics)
})

// AI service specific health check
router.get('/ai', async (req, res) => {
  try {
    const startTime = Date.now()
    
    // Test Ollama with a simple prompt
    const response = await axios.post('http://localhost:11434/api/generate', {
      model: 'llama3.2:3b',
      prompt: 'Hello, health check test.',
      stream: false,
      options: { num_predict: 10 }
    }, { timeout: 10000 })
    
    const responseTime = Date.now() - startTime
    
    res.json({
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      model: 'llama3.2:3b',
      response: response.data.response?.substring(0, 100) + '...'
    })
    
  } catch (error) {
    logger.error('❌ AI health check failed', { error: error.message })
    
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      model: 'llama3.2:3b'
    })
  }
})

export default router