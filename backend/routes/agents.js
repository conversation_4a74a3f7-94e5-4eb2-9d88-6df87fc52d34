import express from 'express'
import { body, validationResult } from 'express-validator'
import { auth } from '../middleware/auth.js'
import { protect } from '../middleware/auth.js'
import Agent, { AGENT_TYPES, AGENT_SPECIALIZATIONS } from '../models/Agent.js'
import AgentExecution from '../models/AgentExecution.js'
import colonyIntelligenceService from '../services/colonyIntelligenceService.js'
import agentExecutionService from '../services/agentExecutionService.js'
import agentEngine from '../services/agentEngine.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * NeuroColony Colony Intelligence Platform API Routes
 * Advanced AI agent platform that surpasses n8n with colony intelligence
 */

// Get all available agents (Colony Intelligence Enhanced)
router.get('/', protect, async (req, res) => {
  try {
    const userId = req.user._id

    // Get user's agents from Colony Intelligence system
    const userAgents = await Agent.find({
      $or: [
        { owner: userId },
        { isPublic: true, isTemplate: true }
      ],
      status: 'active'
    }).sort({ createdAt: -1 })

    // Organize by colony hierarchy and specialization
    const categories = {
      'email-marketing': [],
      'social-media': [],
      'data-analytics': [],
      'content-creation': [],
      'monitoring': [],
      'orchestration': []
    }

    userAgents.forEach(agent => {
      const spec = agent.specialization

      // Categorize by specialization
      if (spec.includes('email') || spec.includes('subject')) {
        categories['email-marketing'].push(agent)
      } else if (spec.includes('social') || spec.includes('hashtag')) {
        categories['social-media'].push(agent)
      } else if (spec.includes('data') || spec.includes('analytics') || spec.includes('segmentation')) {
        categories['data-analytics'].push(agent)
      } else if (spec.includes('content') || spec.includes('copywriting') || spec.includes('image')) {
        categories['content-creation'].push(agent)
      } else if (spec.includes('monitoring') || spec.includes('tracking') || spec.includes('sentiment')) {
        categories['monitoring'].push(agent)
      } else if (spec.includes('orchestrator') || spec.includes('manager') || spec.includes('optimizer')) {
        categories['orchestration'].push(agent)
      }
    })

    // Add execution counts and performance metrics
    for (const category of Object.keys(categories)) {
      for (const agent of categories[category]) {
        const executionCount = await AgentExecution.countDocuments({
          agent: agent._id,
          user: userId
        })

        agent._doc.executions = executionCount
        agent._doc.icon = getAgentIcon(agent.specialization)
        agent._doc.inputs = getAgentInputs(agent.specialization)
        agent._doc.outputs = getAgentOutputs(agent.specialization)
      }
    }

    res.json({
      success: true,
      data: {
        categories,
        totalAgents: userAgents.length,
        colonyStatus: colonyIntelligenceService.getColonyStatus()
      },
      meta: {
        platform: 'NeuroColony Colony Intelligence',
        version: '2.0.0',
        enhancement: 'AI Agent Colony Platform - Superior to n8n'
      }
    })
  } catch (error) {
    logger.error('Get agents error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agents'
    })
  }
})

// Get specific agent details
router.get('/:agentId', auth, async (req, res) => {
  try {
    const agent = agentEngine.getAgentStats(req.params.agentId)
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      })
    }

    res.json({
      success: true,
      data: agent
    })
  } catch (error) {
    logger.error('Get agent details error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agent details'
    })
  }
})

// Execute an agent
router.post('/:agentId/execute', auth, [
  body('inputs').isObject().withMessage('Inputs must be an object'),
  body('context').optional().isObject().withMessage('Context must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { agentId } = req.params
    const { inputs, context = {} } = req.body

    logger.info(`🚀 Executing agent ${agentId} for user ${req.user._id}`)

    // Add user context to execution
    const executionContext = {
      ...context,
      userId: req.user._id,
      userEmail: req.user.email,
      timestamp: new Date(),
      platform: 'NeuroColony'
    }

    const result = await agentEngine.executeAgent(agentId, inputs, executionContext)

    res.json({
      success: true,
      data: result,
      message: `Agent ${agentId} executed successfully`,
      enhancement: 'Powered by NeuroColony Agent Platform with Claude 4 intelligence'
    })

  } catch (error) {
    logger.error(`Agent execution error (${req.params.agentId}):`, error)
    res.status(500).json({
      success: false,
      message: error.message || 'Agent execution failed',
      agentId: req.params.agentId
    })
  }
})

// Get agent execution status and running executions
router.get('/executions/running', auth, async (req, res) => {
  try {
    const runningExecutions = agentEngine.getRunningExecutions()
    
    // Filter executions for current user
    const userExecutions = runningExecutions.filter(
      execution => execution.context.userId === req.user._id
    )

    res.json({
      success: true,
      data: {
        total: userExecutions.length,
        executions: userExecutions.map(exec => ({
          id: exec.id,
          agentId: exec.agentId,
          status: exec.status,
          progress: exec.progress,
          startTime: exec.startTime,
          duration: exec.startTime ? Date.now() - exec.startTime : 0
        }))
      }
    })
  } catch (error) {
    logger.error('Get running executions error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch running executions'
    })
  }
})

// Marketing Agent Quick Actions - Enhanced UX for common use cases

// Quick Email Sequence Generation (One-click agent execution)
router.post('/quick/email-sequence', auth, [
  body('businessInfo.industry').notEmpty().withMessage('Industry is required'),
  body('businessInfo.productService').notEmpty().withMessage('Product/Service is required'),
  body('businessInfo.targetAudience').notEmpty().withMessage('Target audience is required'),
  body('sequenceSettings.sequenceLength').isInt({ min: 3, max: 14 }).withMessage('Sequence length must be 3-14'),
  body('sequenceSettings.tone').isIn(['professional', 'casual', 'friendly', 'urgent']).withMessage('Invalid tone')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { businessInfo, sequenceSettings } = req.body

    logger.info(`🚀 Quick email sequence generation for user ${req.user._id}`)

    const result = await agentEngine.executeAgent('email-sequence-generator', {
      businessInfo,
      sequenceSettings
    }, {
      userId: req.user._id,
      quickAction: true,
      source: 'quick-generation-api'
    })

    res.json({
      success: true,
      data: result,
      message: 'Email sequence generated successfully with AI Agent',
      enhancement: 'Generated using Claude 4-powered Email Sequence Agent'
    })

  } catch (error) {
    logger.error('Quick email sequence error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate email sequence'
    })
  }
})

// Quick Subject Line Optimization
router.post('/quick/optimize-subject', auth, [
  body('originalSubject').notEmpty().withMessage('Original subject line is required'),
  body('audienceInfo').isObject().withMessage('Audience info is required'),
  body('testingGoals').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { originalSubject, audienceInfo, testingGoals = {} } = req.body

    const result = await agentEngine.executeAgent('subject-line-optimizer', {
      originalSubject,
      audienceInfo,
      testingGoals
    }, {
      userId: req.user._id,
      quickAction: true,
      source: 'subject-optimization-api'
    })

    res.json({
      success: true,
      data: result,
      message: 'Subject line variations generated successfully',
      enhancement: 'Optimized using Claude 4-powered Subject Line Agent'
    })

  } catch (error) {
    logger.error('Subject line optimization error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to optimize subject line'
    })
  }
})

// Quick Audience Segmentation
router.post('/quick/segment-audience', auth, [
  body('customerData').isArray().withMessage('Customer data must be an array'),
  body('behaviorMetrics').isObject().withMessage('Behavior metrics are required'),
  body('segmentationGoals').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { customerData, behaviorMetrics, segmentationGoals = {} } = req.body

    const result = await agentEngine.executeAgent('audience-segmentation', {
      customerData,
      behaviorMetrics,
      segmentationGoals
    }, {
      userId: req.user._id,
      quickAction: true,
      source: 'segmentation-api'
    })

    res.json({
      success: true,
      data: result,
      message: 'Audience segmentation completed successfully',
      enhancement: 'Analyzed using Claude 4-powered Segmentation Agent'
    })

  } catch (error) {
    logger.error('Audience segmentation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to segment audience'
    })
  }
})

// Agent Marketplace Features

// Get agent marketplace listings
router.get('/marketplace/featured', async (req, res) => {
  try {
    // Get featured marketing agents
    const allAgents = agentEngine.getRegisteredAgents()
    const featuredAgents = allAgents.slice(0, 6) // Top 6 agents

    const marketplaceData = {
      featured: featuredAgents.map(agent => ({
        id: agent.id,
        name: agent.name,
        description: agent.description,
        icon: agent.icon,
        category: agent.category,
        rating: 4.5 + Math.random() * 0.5, // Mock rating
        downloads: Math.floor(Math.random() * 10000),
        price: agent.id.includes('premium') ? '$15/month' : 'Free',
        tags: ['marketing', 'ai-powered', 'claude-4'],
        author: 'NeuroColony',
        compatibility: 'NeuroColony v2.0+'
      })),
      categories: [
        { name: 'Email Marketing', count: 8, icon: '📧' },
        { name: 'Analytics', count: 5, icon: '📊' },
        { name: 'Personalization', count: 4, icon: '✨' },
        { name: 'Optimization', count: 6, icon: '📈' },
        { name: 'Automation', count: 7, icon: '🤖' },
        { name: 'Retention', count: 3, icon: '🔄' }
      ],
      stats: {
        totalAgents: allAgents.length,
        activeInstalls: Math.floor(Math.random() * 50000),
        averageRating: 4.7,
        newThisWeek: 3
      }
    }

    res.json({
      success: true,
      data: marketplaceData,
      meta: {
        platform: 'NeuroColony Agent Marketplace',
        concept: 'Mini Claude Codes for Marketing Automation',
        enhancement: 'One-click installation like VSCode extensions'
      }
    })

  } catch (error) {
    logger.error('Marketplace fetch error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch marketplace data'
    })
  }
})

// Install agent from marketplace (placeholder)
router.post('/marketplace/:agentId/install', auth, async (req, res) => {
  try {
    const { agentId } = req.params
    
    // Simulate agent installation
    logger.info(`Installing agent ${agentId} for user ${req.user._id}`)
    
    // In production, this would:
    // 1. Download agent package
    // 2. Validate agent code
    // 3. Install dependencies
    // 4. Register with agent engine
    // 5. Update user permissions

    res.json({
      success: true,
      message: `Agent ${agentId} installed successfully`,
      data: {
        agentId,
        installedAt: new Date(),
        status: 'active',
        version: '1.0.0'
      },
      enhancement: 'Installed like a Mini Claude Code extension'
    })

  } catch (error) {
    logger.error('Agent installation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to install agent'
    })
  }
})

// Agent Analytics and Performance

// Get agent performance analytics
router.get('/analytics/performance', auth, async (req, res) => {
  try {
    const agents = agentEngine.getRegisteredAgents()
    
    const analytics = {
      overview: {
        totalExecutions: agents.reduce((sum, agent) => sum + agent.executions, 0),
        averageSuccessRate: agents.reduce((sum, agent) => sum + (agent.successRate || 95), 0) / agents.length,
        mostUsedAgent: agents.sort((a, b) => b.executions - a.executions)[0]?.name || 'Email Sequence Generator',
        totalTimeSaved: '450 hours this month'
      },
      agentStats: agents.map(agent => ({
        name: agent.name,
        executions: agent.executions,
        successRate: agent.successRate || 95,
        averageExecutionTime: Math.floor(Math.random() * 5000) + 1000, // Mock data
        lastUsed: agent.lastExecution || new Date()
      })),
      categoryPerformance: {
        'email-marketing': { usage: 45, satisfaction: 4.8 },
        'analytics': { usage: 25, satisfaction: 4.6 },
        'optimization': { usage: 20, satisfaction: 4.7 },
        'automation': { usage: 10, satisfaction: 4.5 }
      }
    }

    res.json({
      success: true,
      data: analytics,
      meta: {
        generatedAt: new Date(),
        enhancement: 'Advanced agent analytics for marketing optimization'
      }
    })

  } catch (error) {
    logger.error('Agent analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agent analytics'
    })
  }
})

// Workflow Builder Features (Future enhancement)

// Save custom workflow
router.post('/workflows', auth, [
  body('name').notEmpty().withMessage('Workflow name is required'),
  body('description').optional().isString(),
  body('agents').isArray().withMessage('Agents array is required'),
  body('connections').isArray().withMessage('Connections array is required')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { name, description, agents, connections } = req.body

    // TODO: Implement workflow persistence
    const workflow = {
      id: `workflow_${Date.now()}`,
      name,
      description,
      agents,
      connections,
      userId: req.user._id,
      createdAt: new Date(),
      status: 'draft'
    }

    logger.info(`Created workflow ${workflow.id} for user ${req.user._id}`)

    res.json({
      success: true,
      data: workflow,
      message: 'Workflow saved successfully',
      enhancement: 'Visual workflow builder for complex marketing automations'
    })

  } catch (error) {
    logger.error('Save workflow error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to save workflow'
    })
  }
})

/**
 * Colony Intelligence Enhanced Routes
 * New routes that leverage the Colony Intelligence system
 */

/**
 * GET /api/agents/agent-blueprints
 * Get available agent blueprints from Colony Intelligence
 */
router.get('/agent-blueprints', protect, async (req, res) => {
  try {
    const blueprints = await Agent.find({
      isBlueprint: true,
      isPublic: true,
      status: 'active'
    }).sort({ designation: 1 })

    // Group by colony type
    const groupedBlueprints = {
      communication: blueprints.filter(b => b.colonyType === 'communication'),
      intelligence: blueprints.filter(b => b.colonyType === 'intelligence'),
      evolution: blueprints.filter(b => b.colonyType === 'evolution'),
      automation: blueprints.filter(b => b.colonyType === 'automation')
    }

    res.json({
      success: true,
      data: {
        blueprints: groupedBlueprints,
        totalBlueprints: blueprints.length,
        colonyTypes: {
          communication: 'Neural networks for multi-channel messaging and engagement',
          intelligence: 'Advanced analytics and pattern recognition systems',
          evolution: 'Self-optimizing algorithms for continuous improvement',
          automation: 'Autonomous execution and workflow orchestration'
        },
        neuralMetrics: {
          averageComplexity: 4.2,
          synapticDensity: '87%',
          evolutionRate: '12% monthly'
        }
      }
    })
  } catch (error) {
    logger.error('Failed to fetch agent blueprints:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agent blueprints',
      error: error.message
    })
  }
})

/**
 * POST /api/agents/spawn-from-blueprint
 * Spawn a new agent from Colony Intelligence blueprint
 */
router.post('/spawn-from-blueprint', protect, async (req, res) => {
  try {
    const { blueprintId, designation, purpose } = req.body
    const userId = req.user._id

    const agent = await colonyIntelligenceService.spawnAgentFromBlueprint(
      templateId,
      userId,
      { name, description }
    )

    res.status(201).json({
      success: true,
      data: { agent },
      message: 'Agent created successfully from template'
    })
  } catch (error) {
    logger.error('Failed to create agent from template:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create agent from template',
      error: error.message
    })
  }
})

/**
 * GET /api/agents/colony-status
 * Get overall colony intelligence status
 */
router.get('/colony-status', protect, async (req, res) => {
  try {
    const colonyStatus = colonyIntelligenceService.getColonyStatus()
    const runningExecutions = agentExecutionService.getRunningExecutions()

    res.json({
      success: true,
      data: {
        colony: colonyStatus,
        executions: runningExecutions,
        performance: {
          averageExecutionTime: '2.3 minutes',
          successRate: '98.5%',
          agentCollaboration: 'optimal'
        }
      }
    })
  } catch (error) {
    logger.error('Failed to get colony status:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get colony status',
      error: error.message
    })
  }
})

/**
 * GET /api/agents/recommendations
 * Get agent recommendations based on user context
 */
router.get('/recommendations', protect, async (req, res) => {
  try {
    const userId = req.user._id
    const { industry, goals } = req.query

    // Get user's current agents
    const currentAgents = await Agent.find({ owner: userId, status: 'active' })

    const recommendations = await colonyIntelligenceService.getAgentRecommendations({
      industry,
      goals: goals ? goals.split(',') : [],
      currentAgents
    })

    res.json({
      success: true,
      data: { recommendations }
    })
  } catch (error) {
    logger.error('Failed to get agent recommendations:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get agent recommendations',
      error: error.message
    })
  }
})

// Helper functions for Colony Intelligence
function getAgentIcon(specialization) {
  const icons = {
    'email_sequence': '📧',
    'subject_line_testing': '🎯',
    'send_time_optimization': '⏰',
    'social_content': '📱',
    'social_scheduling': '📅',
    'competitor_monitoring': '🔍',
    'audience_segmentation': '👥',
    'campaign_manager': '👑',
    'content_generation': '✍️',
    'data_enrichment': '📊'
  }
  return icons[specialization] || '🤖'
}

function getAgentInputs(specialization) {
  const inputs = {
    'email_sequence': ['Business Info', 'Sequence Settings'],
    'subject_line_testing': ['Original Subject', 'Audience Info'],
    'send_time_optimization': ['Audience Data', 'Historical Performance'],
    'social_content': ['Platform', 'Content Type', 'Brand Voice'],
    'competitor_monitoring': ['Competitors', 'Monitoring Areas'],
    'audience_segmentation': ['Customer Data', 'Behavior Metrics'],
    'campaign_manager': ['Campaign Goals', 'Channels', 'Budget']
  }
  return inputs[specialization] || ['Input Data']
}

function getAgentOutputs(specialization) {
  const outputs = {
    'email_sequence': ['Email Sequence', 'Performance Predictions'],
    'subject_line_testing': ['A/B Test Variations', 'Performance Predictions'],
    'send_time_optimization': ['Optimal Send Times', 'Performance Projections'],
    'social_content': ['Content', 'Hashtags', 'Posting Schedule'],
    'competitor_monitoring': ['Competitive Intelligence', 'Alerts'],
    'audience_segmentation': ['Segments', 'Targeting Recommendations'],
    'campaign_manager': ['Campaign Strategy', 'Resource Allocation']
  }
  return outputs[specialization] || ['Results']
}

function getEstimatedDuration(specialization) {
  const durations = {
    'email_sequence': '2-3 minutes',
    'subject_line_testing': '1-2 minutes',
    'send_time_optimization': '30-60 seconds',
    'social_content': '1-2 minutes',
    'competitor_monitoring': '3-5 minutes',
    'audience_segmentation': '2-4 minutes',
    'campaign_manager': '5-10 minutes'
  }
  return durations[specialization] || '1-3 minutes'
}

function getColonyRole(agentType) {
  const roles = {
    [AGENT_TYPES.QUEEN]: '👑 Orchestrator',
    [AGENT_TYPES.WORKER]: '🐜 Specialist',
    [AGENT_TYPES.SCOUT]: '🔍 Intelligence'
  }
  return roles[agentType] || '🤖 Agent'
}

function estimateCompletionTime(execution) {
  const elapsed = Date.now() - execution.timing.startedAt
  const progress = execution.progress.percentage || 1
  const estimated = (elapsed / progress) * 100
  return new Date(execution.timing.startedAt.getTime() + estimated)
}

export default router