import express from 'express'
import { auth } from '../middleware/auth.js'
import EmailSequence from '../models/EmailSequence.js'
import User from '../models/User.js'
import { logger } from '../utils/logger.js'
import aiService from '../services/aiService.js'
import streamingAIService from '../services/streamingAIService.js'

const router = express.Router()

// Get all sequences for user
router.get('/', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status = 'all',
      sortBy = 'createdAt',
      order = 'desc' 
    } = req.query

    const query = { userId: req.user._id }
    if (status !== 'all') {
      query.status = status
    }

    const skip = (page - 1) * limit
    const sort = { [sortBy]: order === 'desc' ? -1 : 1 }

    const [sequences, total] = await Promise.all([
      EmailSequence.find(query)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .select('-emails.body'), // Exclude email bodies for list view
      EmailSequence.countDocuments(query)
    ])

    res.json({
      success: true,
      data: {
        sequences,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    logger.error('Error fetching sequences:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sequences'
    })
  }
})

// Get single sequence
router.get('/:id', auth, async (req, res) => {
  try {
    const sequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    res.json({
      success: true,
      data: sequence
    })

  } catch (error) {
    logger.error('Error fetching sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sequence'
    })
  }
})

// Create new sequence
router.post('/', auth, async (req, res) => {
  try {
    const { name, description, businessInfo, settings, emails } = req.body

    // Validate user can create sequence
    const user = await User.findById(req.user._id)
    const canCreate = await user.canGenerateSequence()
    
    if (!canCreate.allowed) {
      return res.status(403).json({
        success: false,
        message: canCreate.reason,
        usage: canCreate.usage
      })
    }

    // Create sequence
    const sequence = new EmailSequence({
      userId: req.user._id,
      name,
      description,
      businessInfo,
      settings,
      emails,
      status: 'draft',
      metadata: {
        createdVia: 'manual',
        version: 1
      }
    })

    await sequence.save()

    // Update user usage
    await user.incrementSequenceCount()

    logger.info(`Sequence created: ${sequence._id} by user ${req.user._id}`)

    res.status(201).json({
      success: true,
      data: sequence
    })

  } catch (error) {
    logger.error('Error creating sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create sequence'
    })
  }
})

// Generate sequence with AI
router.post('/generate', auth, async (req, res) => {
  try {
    const { businessInfo, settings } = req.body

    // Validate user can generate
    const user = await User.findById(req.user._id)
    const canGenerate = await user.canGenerateSequence()
    
    if (!canGenerate.allowed) {
      return res.status(403).json({
        success: false,
        message: canGenerate.reason,
        usage: canGenerate.usage
      })
    }

    // Generate with AI
    const result = await aiService.generateEmailSequence(businessInfo, settings)

    // Create sequence
    const sequence = new EmailSequence({
      userId: req.user._id,
      name: `${businessInfo.businessName} - ${settings.sequenceType} Sequence`,
      description: `AI-generated ${settings.sequenceLength}-email ${settings.sequenceType} sequence`,
      businessInfo,
      settings,
      emails: result.sequence || result.emails,
      status: 'draft',
      metadata: {
        createdVia: 'ai',
        provider: result.provider,
        quality: result.quality,
        version: 1
      }
    })

    await sequence.save()

    // Update user usage
    await user.incrementSequenceCount()

    logger.info(`AI sequence generated: ${sequence._id} by user ${req.user._id}`)

    res.status(201).json({
      success: true,
      data: sequence
    })

  } catch (error) {
    logger.error('Error generating sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate sequence'
    })
  }
})

// Update sequence
router.put('/:id', auth, async (req, res) => {
  try {
    const { name, description, emails, status, businessInfo, settings } = req.body

    const sequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Update fields
    if (name !== undefined) sequence.name = name
    if (description !== undefined) sequence.description = description
    if (emails !== undefined) sequence.emails = emails
    if (status !== undefined) sequence.status = status
    if (businessInfo !== undefined) sequence.businessInfo = businessInfo
    if (settings !== undefined) sequence.settings = settings

    // Update metadata
    sequence.metadata.lastModified = new Date()
    sequence.metadata.version = (sequence.metadata.version || 1) + 1

    await sequence.save()

    logger.info(`Sequence updated: ${sequence._id} by user ${req.user._id}`)

    res.json({
      success: true,
      data: sequence
    })

  } catch (error) {
    logger.error('Error updating sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update sequence'
    })
  }
})

// Update single email in sequence
router.put('/:id/emails/:emailIndex', auth, async (req, res) => {
  try {
    const { emailIndex } = req.params
    const emailData = req.body

    const sequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    if (!sequence.emails[emailIndex]) {
      return res.status(404).json({
        success: false,
        message: 'Email not found'
      })
    }

    // Update email
    sequence.emails[emailIndex] = {
      ...sequence.emails[emailIndex],
      ...emailData
    }

    sequence.metadata.lastModified = new Date()
    await sequence.save()

    logger.info(`Email ${emailIndex} updated in sequence ${sequence._id}`)

    res.json({
      success: true,
      data: sequence.emails[emailIndex]
    })

  } catch (error) {
    logger.error('Error updating email:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update email'
    })
  }
})

// Delete sequence
router.delete('/:id', auth, async (req, res) => {
  try {
    const sequence = await EmailSequence.findOneAndDelete({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    logger.info(`Sequence deleted: ${req.params.id} by user ${req.user._id}`)

    res.json({
      success: true,
      message: 'Sequence deleted successfully'
    })

  } catch (error) {
    logger.error('Error deleting sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete sequence'
    })
  }
})

// Duplicate sequence
router.post('/:id/duplicate', auth, async (req, res) => {
  try {
    const originalSequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!originalSequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Check user limits
    const user = await User.findById(req.user._id)
    const canCreate = await user.canGenerateSequence()
    
    if (!canCreate.allowed) {
      return res.status(403).json({
        success: false,
        message: canCreate.reason,
        usage: canCreate.usage
      })
    }

    // Create duplicate
    const duplicate = new EmailSequence({
      userId: req.user._id,
      name: `${originalSequence.name} (Copy)`,
      description: originalSequence.description,
      businessInfo: originalSequence.businessInfo,
      settings: originalSequence.settings,
      emails: originalSequence.emails,
      status: 'draft',
      metadata: {
        createdVia: 'duplicate',
        originalId: originalSequence._id,
        version: 1
      }
    })

    await duplicate.save()

    // Update user usage
    await user.incrementSequenceCount()

    logger.info(`Sequence duplicated: ${originalSequence._id} -> ${duplicate._id}`)

    res.status(201).json({
      success: true,
      data: duplicate
    })

  } catch (error) {
    logger.error('Error duplicating sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to duplicate sequence'
    })
  }
})

// Export sequence to integration
router.post('/:id/export/:platform', auth, async (req, res) => {
  try {
    const { platform } = req.params
    const { integrationId, options = {} } = req.body

    const sequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // TODO: Implement actual export to integrations
    // This would use the integration service to export to Mailchimp, ConvertKit, etc.

    logger.info(`Sequence export initiated: ${sequence._id} to ${platform}`)

    res.json({
      success: true,
      message: `Export to ${platform} initiated`,
      jobId: `export_${Date.now()}`
    })

  } catch (error) {
    logger.error('Error exporting sequence:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to export sequence'
    })
  }
})

// Get sequence statistics
router.get('/:id/stats', auth, async (req, res) => {
  try {
    const sequence = await EmailSequence.findOne({
      _id: req.params.id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    // Calculate statistics
    const stats = {
      emailCount: sequence.emails.length,
      totalWords: sequence.emails.reduce((sum, email) => 
        sum + (email.body?.split(' ').length || 0), 0
      ),
      averageDelay: sequence.emails.reduce((sum, email, index) => 
        index === 0 ? 0 : sum + (email.delay || 0), 0
      ) / (sequence.emails.length - 1 || 1),
      hasAllSubjects: sequence.emails.every(email => email.subject),
      hasAllCTAs: sequence.emails.every(email => email.cta),
      status: sequence.status,
      createdAt: sequence.createdAt,
      lastModified: sequence.metadata?.lastModified || sequence.createdAt
    }

    res.json({
      success: true,
      data: stats
    })

  } catch (error) {
    logger.error('Error fetching sequence stats:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sequence statistics'
    })
  }
})

export default router