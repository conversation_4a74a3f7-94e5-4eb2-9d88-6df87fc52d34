import express from 'express'
import { auth } from '../middleware/auth.js'
import { logger } from '../utils/logger.js'
import whiteLabelService from '../services/whiteLabelService.js'

const router = express.Router()

// Get branding configuration
router.get('/branding', auth, async (req, res) => {
  try {
    const branding = await whiteLabelService.getBrandingConfig(req.user.id)
    
    res.json({
      success: true,
      data: branding
    })
  } catch (error) {
    logger.error('Get branding config error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch branding configuration'
    })
  }
})

// Update branding configuration
router.put('/branding', auth, async (req, res) => {
  try {
    const updatedBranding = await whiteLabelService.updateBrandingConfig(
      req.user.id, 
      req.body
    )
    
    res.json({
      success: true,
      data: updatedBranding,
      message: 'Branding configuration updated successfully'
    })
  } catch (error) {
    logger.error('Update branding config error:', error)
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update branding configuration'
    })
  }
})

// Get custom CSS theme
router.get('/theme/css', auth, async (req, res) => {
  try {
    const customCSS = await whiteLabelService.generateCustomTheme(req.user.id)
    
    res.set('Content-Type', 'text/css')
    res.send(customCSS)
  } catch (error) {
    logger.error('Generate custom theme error:', error)
    res.status(500).send('/* Error generating custom theme */')
  }
})

// Get dashboard configuration
router.get('/dashboard-config', auth, async (req, res) => {
  try {
    const config = await whiteLabelService.getDashboardConfig(req.user.id)
    
    res.json({
      success: true,
      data: config
    })
  } catch (error) {
    logger.error('Get dashboard config error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard configuration'
    })
  }
})

// Get email template for white-labeling
router.get('/email-template/:type?', auth, async (req, res) => {
  try {
    const templateType = req.params.type || 'default'
    const template = await whiteLabelService.getWhiteLabelEmailTemplate(
      req.user.id, 
      templateType
    )
    
    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Template not found'
      })
    }
    
    res.json({
      success: true,
      data: template
    })
  } catch (error) {
    logger.error('Get email template error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email template'
    })
  }
})

// Setup custom subdomain
router.post('/subdomain', auth, async (req, res) => {
  try {
    const { subdomain } = req.body
    
    if (!subdomain) {
      return res.status(400).json({
        success: false,
        message: 'Subdomain is required'
      })
    }
    
    const result = await whiteLabelService.setupCustomSubdomain(
      req.user.id, 
      subdomain
    )
    
    res.json({
      success: true,
      data: result,
      message: 'Custom subdomain setup initiated'
    })
  } catch (error) {
    logger.error('Setup subdomain error:', error)
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to setup custom subdomain'
    })
  }
})

// Check subdomain availability
router.get('/subdomain/check/:subdomain', auth, async (req, res) => {
  try {
    const { subdomain } = req.params
    
    // Check if user has access
    const hasAccess = await whiteLabelService.hasWhiteLabelAccess(req.user.id)
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'White-label features require Business or Enterprise plan'
      })
    }
    
    // Validate subdomain format
    if (!whiteLabelService.isValidSubdomain(subdomain)) {
      return res.json({
        success: true,
        available: false,
        reason: 'Invalid subdomain format. Use 3-63 characters, letters, numbers, and hyphens only.'
      })
    }
    
    // Check availability in database
    const CompleteUser = (await import('../models/CompleteUser.js')).default
    const existingUser = await CompleteUser.findOne({
      'branding.customSubdomain': subdomain
    })
    
    const isAvailable = !existingUser || existingUser._id.toString() === req.user.id
    
    res.json({
      success: true,
      available: isAvailable,
      subdomain,
      fullUrl: isAvailable ? `https://${subdomain}.sequenceai.app` : null,
      reason: isAvailable ? null : 'Subdomain already taken'
    })
  } catch (error) {
    logger.error('Check subdomain availability error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to check subdomain availability'
    })
  }
})

// Get export options
router.get('/export-options', auth, async (req, res) => {
  try {
    const options = await whiteLabelService.getExportOptions(req.user.id)
    
    res.json({
      success: true,
      data: options
    })
  } catch (error) {
    logger.error('Get export options error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch export options'
    })
  }
})

// Get white-label pricing information
router.get('/pricing', async (req, res) => {
  try {
    const pricing = whiteLabelService.getWhiteLabelPricing()
    
    res.json({
      success: true,
      data: pricing
    })
  } catch (error) {
    logger.error('Get white-label pricing error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pricing information'
    })
  }
})

// Preview branding changes
router.post('/preview', auth, async (req, res) => {
  try {
    const { brandingData } = req.body
    
    // Validate branding data without saving
    const validatedBranding = whiteLabelService.validateBrandingData(brandingData)
    
    // Generate preview CSS
    const previewCSS = `
      :root {
        --primary-color: ${validatedBranding.primaryColor || '#2563eb'};
        --secondary-color: ${validatedBranding.secondaryColor || '#1e40af'};
        --accent-color: ${validatedBranding.accentColor || '#3b82f6'};
      }
      
      .preview-card {
        border: 2px solid var(--primary-color);
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
      }
      
      .preview-button {
        background-color: var(--accent-color);
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        margin: 10px;
      }
    `
    
    res.json({
      success: true,
      data: {
        validatedBranding,
        previewCSS,
        preview: {
          companyName: validatedBranding.companyName || 'Your Company',
          logoUrl: validatedBranding.logoUrl,
          colors: {
            primary: validatedBranding.primaryColor,
            secondary: validatedBranding.secondaryColor,
            accent: validatedBranding.accentColor
          }
        }
      }
    })
  } catch (error) {
    logger.error('Preview branding error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate branding preview'
    })
  }
})

// Get white-label features by plan
router.get('/features/:plan?', async (req, res) => {
  try {
    const plan = req.params.plan || 'business'
    
    const features = {
      free: {
        whiteLabelAccess: false,
        features: [],
        upgradeRequired: true
      },
      pro: {
        whiteLabelAccess: false,
        features: [],
        upgradeRequired: true
      },
      business: {
        whiteLabelAccess: true,
        features: [
          'Remove "Powered by NeuroColony" branding',
          'Custom company logo and colors',
          'Branded email templates',
          'Custom footer text',
          'Branded PDF exports',
          'Branded CSV exports',
          'Custom email signatures'
        ],
        limitations: [
          'No custom domain',
          'Basic CSS customization only'
        ]
      },
      enterprise: {
        whiteLabelAccess: true,
        features: [
          'All Business plan features',
          'Custom subdomain (your-brand.sequenceai.app)',
          'Custom domain support',
          'Advanced CSS customization',
          'Complete brand control',
          'White-label API documentation',
          'Dedicated setup support'
        ],
        limitations: []
      }
    }
    
    res.json({
      success: true,
      data: features[plan] || features.business
    })
  } catch (error) {
    logger.error('Get white-label features error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch feature information'
    })
  }
})

export default router