import express from 'express'
import fetch from 'node-fetch'
import { auth } from '../middleware/auth.js'
import User from '../models/User.js'
import { logger } from '../utils/logger.js'
import emailService from '../services/emailService.js'

const router = express.Router()

// PayPal Configuration
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com'

// Plan configurations matching Stripe plans
const PAYPAL_PLANS = {
  pro: {
    id: 'pro',
    name: 'Pro Plan',
    price: '29.00',
    currency: 'USD',
    interval: 'MONTH',
    description: 'Professional automation with 75 sequences per month'
  },
  business: {
    id: 'business', 
    name: 'Business Plan',
    price: '99.00',
    currency: 'USD',
    interval: 'MONTH',
    description: 'Advanced business features with 200 sequences per month'
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise Plan', 
    price: '299.00',
    currency: 'USD',
    interval: 'MONTH',
    description: 'Enterprise-grade platform with unlimited sequences'
  }
}

// Get PayPal Access Token
async function getPayPalAccessToken() {
  try {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64')
    
    const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: 'grant_type=client_credentials'
    })
    
    const data = await response.json()
    return data.access_token
  } catch (error) {
    logger.error('Failed to get PayPal access token:', error)
    throw new Error('PayPal authentication failed')
  }
}

// Create PayPal Subscription
router.post('/create-subscription', auth, async (req, res) => {
  try {
    const { planId } = req.body
    const user = await User.findById(req.user.id)
    
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' })
    }
    
    const plan = PAYPAL_PLANS[planId]
    if (!plan) {
      return res.status(400).json({ success: false, message: 'Invalid plan selected' })
    }
    
    const accessToken = await getPayPalAccessToken()
    
    // Create subscription request
    const subscriptionData = {
      plan_id: plan.id,
      subscriber: {
        name: {
          given_name: user.name.split(' ')[0],
          surname: user.name.split(' ')[1] || ''
        },
        email_address: user.email
      },
      application_context: {
        brand_name: 'NeuroColony',
        locale: 'en-US',
        user_action: 'SUBSCRIBE_NOW',
        payment_method: {
          payer_selected: 'PAYPAL',
          payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
        },
        return_url: `${process.env.FRONTEND_URL}/payment/success?provider=paypal`,
        cancel_url: `${process.env.FRONTEND_URL}/payment/cancelled?provider=paypal`
      }
    }
    
    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(subscriptionData)
    })
    
    const subscription = await response.json()
    
    if (!response.ok) {
      logger.error('PayPal subscription creation failed:', subscription)
      return res.status(400).json({ 
        success: false, 
        message: 'Failed to create PayPal subscription',
        error: subscription
      })
    }
    
    // Store subscription info
    user.billing.paypalSubscriptionId = subscription.id
    user.billing.subscriptionPlan = planId
    user.billing.subscriptionStatus = 'pending'
    await user.save()
    
    // Get approval URL
    const approvalUrl = subscription.links.find(link => link.rel === 'approve')?.href
    
    res.json({
      success: true,
      subscriptionId: subscription.id,
      approvalUrl,
      message: 'PayPal subscription created successfully'
    })
    
  } catch (error) {
    logger.error('PayPal subscription creation error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create PayPal subscription',
      error: error.message 
    })
  }
})

// Execute PayPal Subscription (after user approval)
router.post('/execute-subscription', auth, async (req, res) => {
  try {
    const { subscriptionId, token } = req.body
    const user = await User.findById(req.user.id)
    
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' })
    }
    
    const accessToken = await getPayPalAccessToken()
    
    // Get subscription details
    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })
    
    const subscription = await response.json()
    
    if (subscription.status === 'ACTIVE') {
      // Update user subscription status
      user.billing.subscriptionStatus = 'active'
      user.billing.paypalSubscriptionId = subscriptionId
      user.billing.currentPeriodEnd = new Date(subscription.billing_info.next_billing_time)
      await user.save()
      
      logger.info(`PayPal subscription activated for user ${user.email}`)
      
      // Send payment success email
      try {
        const plan = PAYPAL_PLANS[user.billing.subscriptionPlan]
        await emailService.sendPayPalPaymentSuccess(
          user.email,
          user.name,
          plan?.name || 'Pro Plan',
          plan?.price || '29.00'
        )
        logger.info(`PayPal payment success email sent to ${user.email}`)
      } catch (emailError) {
        logger.error('Failed to send PayPal payment success email:', emailError)
        // Don't fail the request if email fails
      }
      
      res.json({
        success: true,
        message: 'Subscription activated successfully',
        subscription: {
          id: subscriptionId,
          status: 'active',
          nextBilling: subscription.billing_info.next_billing_time
        }
      })
    } else {
      res.status(400).json({
        success: false,
        message: 'Subscription not active',
        status: subscription.status
      })
    }
    
  } catch (error) {
    logger.error('PayPal subscription execution error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Failed to execute PayPal subscription',
      error: error.message 
    })
  }
})

// Cancel PayPal Subscription
router.post('/cancel-subscription', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
    
    if (!user || !user.billing.paypalSubscriptionId) {
      return res.status(404).json({ 
        success: false, 
        message: 'No active PayPal subscription found' 
      })
    }
    
    const accessToken = await getPayPalAccessToken()
    
    const response = await fetch(
      `${PAYPAL_BASE_URL}/v1/billing/subscriptions/${user.billing.paypalSubscriptionId}/cancel`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reason: 'User requested cancellation'
        })
      }
    )
    
    if (response.ok) {
      // Update user subscription status
      user.billing.subscriptionStatus = 'cancelled'
      await user.save()
      
      logger.info(`PayPal subscription cancelled for user ${user.email}`)
      
      res.json({
        success: true,
        message: 'Subscription cancelled successfully'
      })
    } else {
      const error = await response.json()
      logger.error('PayPal cancellation failed:', error)
      res.status(400).json({
        success: false,
        message: 'Failed to cancel subscription',
        error
      })
    }
    
  } catch (error) {
    logger.error('PayPal subscription cancellation error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Failed to cancel PayPal subscription',
      error: error.message 
    })
  }
})

// PayPal Webhook Handler
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const event = req.body
    
    logger.info('PayPal webhook received:', event.event_type)
    
    switch (event.event_type) {
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(event.resource)
        break
        
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await handleSubscriptionCancelled(event.resource)
        break
        
      case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
        await handlePaymentFailed(event.resource)
        break
        
      case 'BILLING.SUBSCRIPTION.UPDATED':
        await handleSubscriptionUpdated(event.resource)
        break
        
      default:
        logger.info(`Unhandled PayPal webhook event: ${event.event_type}`)
    }
    
    res.status(200).json({ received: true })
    
  } catch (error) {
    logger.error('PayPal webhook processing error:', error)
    res.status(500).json({ error: 'Webhook processing failed' })
  }
})

// Webhook event handlers
async function handleSubscriptionActivated(subscription) {
  try {
    const user = await User.findOne({ 
      'billing.paypalSubscriptionId': subscription.id 
    })
    
    if (user) {
      user.billing.subscriptionStatus = 'active'
      user.billing.currentPeriodEnd = new Date(subscription.billing_info.next_billing_time)
      await user.save()
      
      logger.info(`PayPal subscription activated for user ${user.email}`)
      
      // Send payment success email for webhook activation
      try {
        const plan = PAYPAL_PLANS[user.billing.subscriptionPlan]
        await emailService.sendPayPalPaymentSuccess(
          user.email,
          user.name,
          plan?.name || 'Pro Plan',
          plan?.price || '29.00'
        )
        logger.info(`PayPal payment success email sent to ${user.email}`)
      } catch (emailError) {
        logger.error('Failed to send PayPal payment success email:', emailError)
      }
    }
  } catch (error) {
    logger.error('Error handling subscription activated:', error)
  }
}

async function handleSubscriptionCancelled(subscription) {
  try {
    const user = await User.findOne({ 
      'billing.paypalSubscriptionId': subscription.id 
    })
    
    if (user) {
      user.billing.subscriptionStatus = 'cancelled'
      await user.save()
      
      logger.info(`PayPal subscription cancelled for user ${user.email}`)
    }
  } catch (error) {
    logger.error('Error handling subscription cancelled:', error)
  }
}

async function handlePaymentFailed(subscription) {
  try {
    const user = await User.findOne({ 
      'billing.paypalSubscriptionId': subscription.id 
    })
    
    if (user) {
      user.billing.subscriptionStatus = 'past_due'
      await user.save()
      
      logger.info(`PayPal payment failed for user ${user.email}`)
    }
  } catch (error) {
    logger.error('Error handling payment failed:', error)
  }
}

async function handleSubscriptionUpdated(subscription) {
  try {
    const user = await User.findOne({ 
      'billing.paypalSubscriptionId': subscription.id 
    })
    
    if (user) {
      user.billing.currentPeriodEnd = new Date(subscription.billing_info.next_billing_time)
      await user.save()
      
      logger.info(`PayPal subscription updated for user ${user.email}`)
    }
  } catch (error) {
    logger.error('Error handling subscription updated:', error)
  }
}

// Get available PayPal plans
router.get('/plans', (req, res) => {
  res.json({
    success: true,
    plans: Object.values(PAYPAL_PLANS)
  })
})

export default router