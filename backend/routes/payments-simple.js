import express from 'express'
import Stripe from 'stripe'
import { auth } from '../middleware/auth.js'
import User from '../models/User.js'
import usageService from '../services/usageService.js'
import { logger } from '../utils/logger.js'
import emailService from '../services/emailService.js'
import stripeWebhookService from '../services/stripeWebhookService.js'

const router = express.Router()
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_dummy')

// Colony Intelligence Plan Configurations
const PLANS = {
  free: {
    id: 'free',
    name: 'Free Trial',
    price: 0,
    priceId: null,
    sequences: 5,
    emailsPerSequence: 5,
    templates: 3,
    overageRate: null,
    // Colony Intelligence Features
    agents: 3,
    agentTypes: ['worker'],
    workflows: 5,
    computeHours: 10,
    naturalLanguageWorkflows: false,
    colonyIntelligence: false
  },
  starter: {
    id: 'starter',
    name: 'Starter Colony',
    price: 29,
    priceId: process.env.STRIPE_STARTER_PRICE_ID,
    sequences: 50,
    emailsPerSequence: 15,
    templates: 20,
    overageRate: 2,
    // Colony Intelligence Features
    agents: 5,
    agentTypes: ['worker', 'scout'],
    workflows: 10,
    computeHours: 50,
    naturalLanguageWorkflows: true,
    colonyIntelligence: 'basic'
  },
  professional: {
    id: 'professional',
    name: 'Professional Colony',
    price: 89,
    priceId: process.env.STRIPE_PROFESSIONAL_PRICE_ID,
    sequences: 200,
    emailsPerSequence: 25,
    templates: 50,
    overageRate: 3,
    // Colony Intelligence Features
    agents: 25,
    agentTypes: ['queen', 'worker', 'scout'],
    workflows: 50,
    computeHours: 200,
    naturalLanguageWorkflows: true,
    colonyIntelligence: 'advanced',
    premiumAI: true
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise Colony',
    price: 289,
    priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,
    sequences: -1, // unlimited
    emailsPerSequence: -1, // unlimited
    templates: -1, // unlimited
    overageRate: 0, // no overage
    // Colony Intelligence Features
    agents: -1, // unlimited
    agentTypes: ['queen', 'worker', 'scout', 'custom'],
    workflows: -1, // unlimited
    computeHours: -1, // unlimited
    naturalLanguageWorkflows: true,
    colonyIntelligence: 'enterprise',
    premiumAI: true,
    customAgents: true,
    whiteLabel: true,
    dedicatedSupport: true
  }
}

console.log('🔧 Enhanced payment routes loaded with usage billing!')

// Get available plans
router.get('/plans', (req, res) => {
  logger.info('Plans endpoint called')
  res.json({
    success: true,
    data: PLANS
  })
})

// Create subscription with usage billing
router.post('/create-subscription', auth, async (req, res) => {
  try {
    const { planId } = req.body
    const user = await User.findById(req.user._id)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    const plan = PLANS[planId]
    if (!plan || planId === 'free') {
      return res.status(400).json({
        success: false,
        message: 'Invalid plan selected'
      })
    }

    // Create or get Stripe customer
    let customerId = user.subscription.stripeCustomerId
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      })
      customerId = customer.id
      user.subscription.stripeCustomerId = customerId
      await user.save()
    }

    // Create subscription with usage-based billing
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [
        {
          price: plan.priceId,
          quantity: 1
        }
      ],
      metadata: {
        userId: user._id.toString(),
        planId: planId
      },
      expand: ['latest_invoice.payment_intent']
    })

    // Update user subscription
    user.subscription.type = planId
    user.subscription.status = subscription.status
    user.subscription.stripeSubscriptionId = subscription.id
    user.subscription.currentPeriodEnd = new Date(subscription.current_period_end * 1000)
    await user.save()

    logger.info(`Subscription created for user ${user._id}: ${planId}`)

    res.json({
      success: true,
      data: {
        subscriptionId: subscription.id,
        clientSecret: subscription.latest_invoice.payment_intent?.client_secret,
        status: subscription.status
      }
    })

  } catch (error) {
    logger.error('Subscription creation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription'
    })
  }
})

// Add usage item for overage billing
router.post('/add-usage-item', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeSubscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found'
      })
    }

    const plan = PLANS[user.subscription.type]
    if (!plan || !plan.overageRate) {
      return res.status(400).json({
        success: false,
        message: 'Plan does not support overage billing'
      })
    }

    // Check if usage item already exists
    const subscription = await stripe.subscriptions.retrieve(user.subscription.stripeSubscriptionId)
    const hasUsageItem = subscription.items.data.some(item => 
      item.price.metadata?.type === 'usage'
    )

    if (!hasUsageItem) {
      // Add usage-based pricing item to subscription
      await stripe.subscriptionItems.create({
        subscription: user.subscription.stripeSubscriptionId,
        price_data: {
          currency: 'usd',
          product_data: {
            name: 'Additional Sequences',
            metadata: {
              type: 'usage',
              rate: plan.overageRate.toString()
            }
          },
          unit_amount: plan.overageRate * 100, // Convert to cents
          recurring: {
            interval: 'month',
            usage_type: 'metered'
          }
        }
      })

      logger.info(`Usage item added to subscription for user ${user._id}`)
    }

    res.json({
      success: true,
      message: 'Usage billing enabled'
    })

  } catch (error) {
    logger.error('Usage item creation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to enable usage billing'
    })
  }
})

// Report usage to Stripe
router.post('/report-usage', auth, async (req, res) => {
  try {
    const { quantity = 1 } = req.body
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeSubscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found'
      })
    }

    // Get subscription and find usage item
    const subscription = await stripe.subscriptions.retrieve(user.subscription.stripeSubscriptionId)
    const usageItem = subscription.items.data.find(item => 
      item.price.metadata?.type === 'usage'
    )

    if (!usageItem) {
      return res.status(400).json({
        success: false,
        message: 'Usage billing not enabled'
      })
    }

    // Report usage to Stripe
    await stripe.subscriptionItems.createUsageRecord(usageItem.id, {
      quantity,
      timestamp: Math.floor(Date.now() / 1000),
      action: 'increment'
    })

    logger.info(`Usage reported to Stripe: ${quantity} for user ${user._id}`)

    res.json({
      success: true,
      message: 'Usage reported successfully'
    })

  } catch (error) {
    logger.error('Usage reporting error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to report usage'
    })
  }
})

// Webhook handler for Stripe events
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  let event

  try {
    const sig = req.headers['stripe-signature']
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET)
  } catch (err) {
    logger.error(`Webhook signature verification failed: ${err.message}`)
    return res.status(400).send(`Webhook Error: ${err.message}`)
  }

  try {
    // Process webhook through service
    await stripeWebhookService.processWebhook(event)
    res.json({ received: true })
  } catch (error) {
    logger.error('Webhook handling error:', error)
    res.status(500).json({ error: 'Webhook handling failed' })
  }
})

// Update payment method
router.post('/update-payment-method', auth, async (req, res) => {
  try {
    const { paymentMethodId } = req.body
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        message: 'No customer record found'
      })
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: user.subscription.stripeCustomerId
    })

    // Set as default payment method
    await stripe.customers.update(user.subscription.stripeCustomerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId
      }
    })

    res.json({
      success: true,
      message: 'Payment method updated successfully'
    })

  } catch (error) {
    logger.error('Payment method update error:', error)
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update payment method'
    })
  }
})

// Cancel subscription
router.post('/cancel-subscription', auth, async (req, res) => {
  try {
    const { immediately = false } = req.body
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeSubscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found'
      })
    }

    // Cancel subscription
    const subscription = await stripe.subscriptions.update(
      user.subscription.stripeSubscriptionId,
      {
        cancel_at_period_end: !immediately
      }
    )

    // If immediate cancellation, delete the subscription
    if (immediately) {
      await stripe.subscriptions.del(user.subscription.stripeSubscriptionId)
    }

    res.json({
      success: true,
      data: {
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        currentPeriodEnd: subscription.current_period_end
      }
    })

  } catch (error) {
    logger.error('Subscription cancellation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription'
    })
  }
})

// Resume canceled subscription
router.post('/resume-subscription', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeSubscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'No subscription found'
      })
    }

    // Resume subscription by removing cancellation
    const subscription = await stripe.subscriptions.update(
      user.subscription.stripeSubscriptionId,
      {
        cancel_at_period_end: false
      }
    )

    res.json({
      success: true,
      data: {
        status: subscription.status,
        currentPeriodEnd: subscription.current_period_end
      }
    })

  } catch (error) {
    logger.error('Subscription resume error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to resume subscription'
    })
  }
})

// Get customer portal session
router.post('/create-portal-session', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    
    if (!user || !user.subscription.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        message: 'No customer record found'
      })
    }

    // Create portal session
    const session = await stripe.billingPortal.sessions.create({
      customer: user.subscription.stripeCustomerId,
      return_url: `${process.env.FRONTEND_URL}/settings/billing`
    })

    res.json({
      success: true,
      data: {
        url: session.url
      }
    })

  } catch (error) {
    logger.error('Portal session creation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create billing portal session'
    })
  }
})

// Get subscription details
router.get('/subscription', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    // Get current plan details
    const plan = PLANS[user.subscription.type || 'free']
    
    // Get usage stats
    const usage = await usageService.getUsageStats(user._id)
    
    // Get Stripe subscription if exists
    let stripeSubscription = null
    if (user.subscription.stripeSubscriptionId) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(
          user.subscription.stripeSubscriptionId,
          {
            expand: ['default_payment_method']
          }
        )
      } catch (error) {
        logger.error('Error fetching Stripe subscription:', error)
      }
    }

    res.json({
      success: true,
      data: {
        plan: {
          ...plan,
          type: user.subscription.type || 'free'
        },
        subscription: {
          status: user.subscription.status,
          currentPeriodEnd: user.subscription.currentPeriodEnd,
          cancelAtPeriodEnd: stripeSubscription?.cancel_at_period_end || false
        },
        usage: usage,
        paymentMethod: stripeSubscription?.default_payment_method ? {
          brand: stripeSubscription.default_payment_method.card?.brand,
          last4: stripeSubscription.default_payment_method.card?.last4,
          expMonth: stripeSubscription.default_payment_method.card?.exp_month,
          expYear: stripeSubscription.default_payment_method.card?.exp_year
        } : null
      }
    })

  } catch (error) {
    logger.error('Get subscription error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription details'
    })
  }
})

export default router