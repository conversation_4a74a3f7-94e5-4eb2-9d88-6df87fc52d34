import express from 'express'
// import Stripe from 'stripe'
import User from '../models/User.js'
import { protect } from '../middleware/auth.js'

const router = express.Router()
// const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

console.log('🔧 Payment routes loaded!')

// Subscription plans configuration
const PLANS = {
  pro: {
    priceId: 'price_pro_monthly', // Replace with actual Stripe price ID
    name: 'Pro Plan',
    price: 29,
    features: ['50 sequences/month', '15 emails per sequence', '20 templates']
  },
  business: {
    priceId: 'price_business_monthly', // Replace with actual Stripe price ID  
    name: 'Business Plan',
    price: 99,
    features: ['Unlimited sequences', '25 emails per sequence', 'Unlimited templates']
  }
}

// Create Stripe customer and subscription
router.post('/create-subscription', protect, async (req, res) => {
  try {
    const { planType, paymentMethodId } = req.body
    const user = req.user

    if (!PLANS[planType]) {
      return res.status(400).json({
        success: false,
        message: 'Invalid plan type'
      })
    }

    // Create Stripe customer if doesn't exist
    let customerId = user.subscription.stripeCustomerId
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      })
      
      customerId = customer.id
      user.subscription.stripeCustomerId = customerId
      await user.save()
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId
    })

    // Set as default payment method
    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId
      }
    })

    // Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: PLANS[planType].priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent']
    })

    // Update user subscription
    user.subscription.type = planType
    user.subscription.stripeSubscriptionId = subscription.id
    user.subscription.status = 'trialing'
    user.subscription.currentPeriodEnd = new Date(subscription.current_period_end * 1000)
    user.subscription.trialEnd = new Date(subscription.trial_end * 1000)
    
    // Update usage limits based on plan
    const limits = user.getSubscriptionLimits()
    user.usage.sequencesLimit = limits.sequences
    
    await user.save()

    res.json({
      success: true,
      data: {
        subscriptionId: subscription.id,
        clientSecret: subscription.latest_invoice.payment_intent.client_secret,
        status: subscription.status
      }
    })

  } catch (error) {
    console.error('Subscription creation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription'
    })
  }
})

// Cancel subscription
router.post('/cancel-subscription', protect, async (req, res) => {
  try {
    const user = req.user
    const subscriptionId = user.subscription.stripeSubscriptionId

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found'
      })
    }

    // Cancel at period end
    await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true
    })

    user.subscription.status = 'canceled'
    await user.save()

    res.json({
      success: true,
      message: 'Subscription will be canceled at the end of current period'
    })

  } catch (error) {
    console.error('Subscription cancellation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription'
    })
  }
})

// Get subscription plans
router.get('/plans', (req, res) => {
  console.log('📋 Plans endpoint called!')
  res.json({
    success: true,
    data: PLANS
  })
})

// Stripe webhook handler
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature']
  let event

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET)
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message)
    return res.status(400).send(`Webhook Error: ${err.message}`)
  }

  try {
    switch (event.type) {
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscriptionUpdate(event.data.object)
        break
        
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object)
        break
        
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object)
        break
        
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    res.json({ received: true })
  } catch (error) {
    console.error('Webhook handler error:', error)
    res.status(500).json({ error: 'Webhook handler failed' })
  }
})

// Helper function to handle subscription updates
async function handleSubscriptionUpdate(subscription) {
  const user = await User.findOne({
    'subscription.stripeSubscriptionId': subscription.id
  })

  if (user) {
    user.subscription.status = subscription.status
    user.subscription.currentPeriodEnd = new Date(subscription.current_period_end * 1000)
    
    if (subscription.canceled_at) {
      user.subscription.status = 'canceled'
      user.subscription.type = 'free'
      user.usage.sequencesLimit = 3 // Reset to free tier
    }
    
    await user.save()
  }
}

// Helper function to handle successful payments
async function handlePaymentSucceeded(invoice) {
  const user = await User.findOne({
    'subscription.stripeCustomerId': invoice.customer
  })

  if (user) {
    user.subscription.status = 'active'
    await user.save()
  }
}

// Helper function to handle failed payments
async function handlePaymentFailed(invoice) {
  const user = await User.findOne({
    'subscription.stripeCustomerId': invoice.customer
  })

  if (user) {
    user.subscription.status = 'past_due'
    await user.save()
  }
}

export default router