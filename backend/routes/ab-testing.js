import express from 'express'
import { auth } from '../middleware/auth.js'
import aiService from '../services/aiService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

// Create A/B test variations
router.post('/create-test', auth, async (req, res) => {
  try {
    const { 
      originalEmail, 
      testType = 'subject_line', 
      variationCount = 3,
      businessInfo 
    } = req.body
    
    if (!originalEmail || !originalEmail.subject) {
      return res.status(400).json({ error: 'Original email with subject required' })
    }
    
    const variations = []
    
    // Add original as control
    variations.push({
      id: 'control',
      type: 'control',
      subject: originalEmail.subject,
      body: originalEmail.body,
      predictedPerformance: await predictPerformance(originalEmail)
    })
    
    // Generate variations based on test type
    for (let i = 0; i < variationCount; i++) {
      try {
        let variation
        
        if (testType === 'subject_line') {
          variation = await generateSubjectVariation(originalEmail, i + 1, businessInfo)
        } else if (testType === 'full_email') {
          variation = await generateFullEmailVariation(originalEmail, i + 1, businessInfo)
        } else if (testType === 'cta') {
          variation = await generateCTAVariation(originalEmail, i + 1, businessInfo)
        }
        
        variations.push({
          id: `variation_${i + 1}`,
          type: 'variation',
          ...variation,
          predictedPerformance: await predictPerformance(variation)
        })
      } catch (error) {
        logger.error(`Variation ${i + 1} generation error:`, error)
      }
    }
    
    // Calculate test recommendations
    const recommendations = generateTestRecommendations(variations, testType)
    
    res.json({ 
      testId: generateTestId(),
      testType,
      variations,
      recommendations,
      estimatedDuration: calculateTestDuration(variationCount),
      sampleSizeRecommendation: calculateSampleSize(variations)
    })
  } catch (error) {
    logger.error('A/B test creation error:', error)
    res.status(500).json({ error: 'A/B test creation failed' })
  }
})

// Analyze A/B test results
router.post('/analyze-results', auth, async (req, res) => {
  try {
    const { testId, results } = req.body
    
    if (!results || results.length === 0) {
      return res.status(400).json({ error: 'Test results required' })
    }
    
    const analysis = {
      winner: null,
      confidence: 0,
      statisticalSignificance: false,
      insights: [],
      recommendations: []
    }
    
    // Calculate statistical significance
    const significance = calculateStatisticalSignificance(results)
    analysis.statisticalSignificance = significance.isSignificant
    analysis.confidence = significance.confidence
    
    // Determine winner
    const sortedResults = results.sort((a, b) => b.conversionRate - a.conversionRate)
    analysis.winner = sortedResults[0]
    
    // Generate insights
    analysis.insights = generateTestInsights(results, significance)
    
    // Generate recommendations
    analysis.recommendations = generatePostTestRecommendations(analysis, results)
    
    res.json({ analysis })
  } catch (error) {
    logger.error('A/B test analysis error:', error)
    res.status(500).json({ error: 'A/B test analysis failed' })
  }
})

// Get A/B testing best practices
router.get('/best-practices', auth, async (req, res) => {
  try {
    const bestPractices = {
      planning: [
        'Test one variable at a time for clear results',
        'Ensure sufficient sample size (minimum 1000 per variation)',
        'Run tests for at least one full business cycle',
        'Set clear success metrics before starting'
      ],
      execution: [
        'Split traffic randomly and evenly',
        'Run tests simultaneously, not sequentially',
        'Avoid external factors that could skew results',
        'Monitor tests regularly but avoid early stopping'
      ],
      analysis: [
        'Wait for statistical significance before concluding',
        'Consider practical significance alongside statistical',
        'Look for secondary metrics impacts',
        'Document learnings for future tests'
      ],
      common_mistakes: [
        'Stopping tests too early',
        'Testing multiple variables simultaneously',
        'Insufficient sample sizes',
        'Ignoring external factors'
      ]
    }
    
    res.json({ bestPractices })
  } catch (error) {
    logger.error('Best practices error:', error)
    res.status(500).json({ error: 'Failed to fetch best practices' })
  }
})

// Helper functions
async function generateSubjectVariation(originalEmail, variationNumber, businessInfo) {
  const strategies = [
    'curiosity-driven',
    'urgency-focused',
    'benefit-oriented',
    'question-based',
    'personalized'
  ]
  
  const strategy = strategies[variationNumber - 1] || strategies[0]
  
  const prompt = `
    Create a ${strategy} variation of this email subject line:
    
    Original: "${originalEmail.subject}"
    Business: ${businessInfo?.industry || 'Business'}
    
    Strategy: ${strategy}
    
    Requirements:
    - Maintain core message intent
    - Apply ${strategy} approach
    - Keep under 60 characters
    - Ensure mobile-friendly
    
    Return only the new subject line.
  `

  const response = await aiService.openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: prompt }],
    temperature: 0.8,
    max_tokens: 50
  })

  return {
    subject: response.choices[0].message.content.trim(),
    body: originalEmail.body,
    strategy,
    changes: ['subject_line']
  }
}

async function generateFullEmailVariation(originalEmail, variationNumber, businessInfo) {
  const approaches = [
    'storytelling',
    'data-driven',
    'emotional',
    'minimalist',
    'comprehensive'
  ]
  
  const approach = approaches[variationNumber - 1] || approaches[0]
  
  const prompt = `
    Rewrite this email using a ${approach} approach:
    
    Original Subject: ${originalEmail.subject}
    Original Body: ${originalEmail.body}
    
    Approach: ${approach}
    Business Context: ${businessInfo?.industry || 'Business'}
    
    Maintain the core message and call-to-action while applying the ${approach} style.
    
    Return in format:
    Subject: [new subject]
    Body: [new body]
  `

  const response = await aiService.openai.chat.completions.create({
    model: 'gpt-4',
    messages: [{ role: 'user', content: prompt }],
    temperature: 0.7,
    max_tokens: 600
  })

  const content = response.choices[0].message.content
  const subjectMatch = content.match(/Subject: (.+)/i)
  const bodyMatch = content.match(/Body: ([\s\S]+)/i)

  return {
    subject: subjectMatch ? subjectMatch[1].trim() : originalEmail.subject,
    body: bodyMatch ? bodyMatch[1].trim() : originalEmail.body,
    approach,
    changes: ['subject_line', 'body_content']
  }
}

async function generateCTAVariation(originalEmail, variationNumber, businessInfo) {
  const ctaStyles = [
    'action-oriented',
    'benefit-focused',
    'urgency-driven',
    'social-proof',
    'curiosity-gap'
  ]
  
  const style = ctaStyles[variationNumber - 1] || ctaStyles[0]
  
  // Extract existing CTA from email body
  const ctaRegex = /\b(click|get|start|download|buy|learn|discover|join|sign up)[^.!?]*[.!?]/gi
  const existingCTAs = originalEmail.body.match(ctaRegex) || []
  
  const prompt = `
    Replace the call-to-action in this email with a ${style} version:
    
    Email Body: ${originalEmail.body}
    Existing CTAs: ${existingCTAs.join(', ')}
    
    Style: ${style}
    Business: ${businessInfo?.industry || 'Business'}
    
    Keep the email body the same but replace CTAs with ${style} versions.
    Return the full updated email body.
  `

  const response = await aiService.openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: prompt }],
    temperature: 0.7,
    max_tokens: 500
  })

  return {
    subject: originalEmail.subject,
    body: response.choices[0].message.content.trim(),
    ctaStyle: style,
    changes: ['call_to_action']
  }
}

async function predictPerformance(email) {
  // Use existing AI service performance prediction
  try {
    return await aiService.predictEmailPerformance(email)
  } catch (error) {
    // Fallback simple prediction
    return {
      predictedMetrics: {
        openRate: '22.5%',
        clickRate: '3.2%',
        conversionRate: '1.8%',
        overallScore: 75
      }
    }
  }
}

function generateTestRecommendations(variations, testType) {
  const recommendations = []
  
  // Sort by predicted performance
  const sorted = variations.slice(1).sort((a, b) => 
    (b.predictedPerformance?.predictedMetrics?.overallScore || 0) - 
    (a.predictedPerformance?.predictedMetrics?.overallScore || 0)
  )
  
  recommendations.push(`Test the top 3 performing variations: ${sorted.slice(0, 3).map(v => v.id).join(', ')}`)
  
  if (testType === 'subject_line') {
    recommendations.push('Focus on open rate as primary metric')
    recommendations.push('Run test for 24-48 hours to account for time zone differences')
  } else if (testType === 'full_email') {
    recommendations.push('Monitor both open rate and conversion rate')
    recommendations.push('Run test for at least one week to capture weekly patterns')
  }
  
  recommendations.push('Ensure minimum 1000 recipients per variation for statistical significance')
  
  return recommendations
}

function calculateTestDuration(variationCount) {
  // More variations need longer duration for statistical significance
  const baseDays = 3
  const additionalDays = Math.ceil(variationCount / 2)
  return baseDays + additionalDays
}

function calculateSampleSize(variations) {
  // Simple sample size calculation based on variations
  const baseSize = 1000
  const perVariation = Math.ceil(baseSize / variations.length)
  return {
    totalRecommended: baseSize,
    perVariation,
    minimumTotal: perVariation * variations.length
  }
}

function calculateStatisticalSignificance(results) {
  // Simplified statistical significance calculation
  // In production, use proper statistical tests
  
  const control = results.find(r => r.type === 'control')
  const variations = results.filter(r => r.type === 'variation')
  
  if (!control || variations.length === 0) {
    return { isSignificant: false, confidence: 0 }
  }
  
  const bestVariation = variations.reduce((best, current) => 
    current.conversionRate > best.conversionRate ? current : best
  )
  
  const improvement = (bestVariation.conversionRate - control.conversionRate) / control.conversionRate
  const sampleSize = Math.min(control.sampleSize || 1000, bestVariation.sampleSize || 1000)
  
  // Simplified confidence calculation
  let confidence = 0
  if (improvement > 0.1 && sampleSize > 1000) confidence = 95
  else if (improvement > 0.05 && sampleSize > 500) confidence = 90
  else if (improvement > 0.02 && sampleSize > 300) confidence = 85
  else confidence = Math.max(0, 50 + (improvement * 100) + (sampleSize / 100))
  
  return {
    isSignificant: confidence >= 95,
    confidence: Math.min(99, confidence),
    improvement: improvement * 100
  }
}

function generateTestInsights(results, significance) {
  const insights = []
  
  if (significance.isSignificant) {
    insights.push(`Test achieved ${significance.confidence}% confidence level`)
    insights.push(`Winner shows ${significance.improvement.toFixed(1)}% improvement`)
  } else {
    insights.push('Test did not reach statistical significance')
    insights.push('Consider running longer or with larger sample size')
  }
  
  // Additional insights based on results
  const openRates = results.map(r => r.openRate).filter(Boolean)
  const clickRates = results.map(r => r.clickRate).filter(Boolean)
  
  if (openRates.length > 1) {
    const openRateVariance = Math.max(...openRates) - Math.min(...openRates)
    if (openRateVariance > 5) {
      insights.push('Significant open rate variance detected - subject line impact confirmed')
    }
  }
  
  if (clickRates.length > 1) {
    const clickRateVariance = Math.max(...clickRates) - Math.min(...clickRates)
    if (clickRateVariance > 2) {
      insights.push('Significant click rate variance detected - content/CTA impact confirmed')
    }
  }
  
  return insights
}

function generatePostTestRecommendations(analysis, results) {
  const recommendations = []
  
  if (analysis.statisticalSignificance) {
    recommendations.push(`Implement winning variation: ${analysis.winner.id}`)
    recommendations.push('Apply learnings to future email campaigns')
  } else {
    recommendations.push('Extend test duration or increase sample size')
    recommendations.push('Consider testing more dramatic variations')
  }
  
  recommendations.push('Document results and insights for future reference')
  recommendations.push('Plan follow-up tests based on these learnings')
  
  return recommendations
}

function generateTestId() {
  return 'test_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()
}

export default router