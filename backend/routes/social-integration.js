import express from 'express'
import { auth } from '../middleware/auth.js'
import aiService from '../services/aiService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

// Social media content generation
router.post('/generate-social', auth, async (req, res) => {
  try {
    const { emailContent, platforms, tone = 'professional' } = req.body
    
    if (!emailContent || !platforms || platforms.length === 0) {
      return res.status(400).json({ error: 'Email content and platforms required' })
    }
    
    const socialContent = {}
    
    for (const platform of platforms) {
      try {
        const prompt = `
          Convert this email content into engaging ${platform} content:
          
          Email Subject: ${emailContent.subject}
          Email Body: ${emailContent.body}
          
          Platform: ${platform}
          Tone: ${tone}
          
          Requirements for ${platform}:
          ${platform === 'twitter' ? '- Keep under 280 characters\n- Use relevant hashtags\n- Engaging hook' :
            platform === 'linkedin' ? '- Professional tone\n- 1-3 paragraphs\n- Industry insights\n- Call for engagement' :
            platform === 'facebook' ? '- Conversational tone\n- 1-2 paragraphs\n- Encourage shares and comments' :
            platform === 'instagram' ? '- Visual-first description\n- Relevant hashtags\n- Story-driven content' :
            '- Platform-optimized content'}
          
          Return only the content, no explanation.
        `

        const response = await aiService.openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.8,
          max_tokens: 300
        })

        socialContent[platform] = {
          content: response.choices[0].message.content.trim(),
          platform,
          characterCount: response.choices[0].message.content.trim().length,
          hashtags: extractHashtags(response.choices[0].message.content),
          bestTime: getBestPostingTime(platform)
        }
      } catch (error) {
        logger.error(`Social content generation error for ${platform}:`, error)
        socialContent[platform] = { error: error.message }
      }
    }
    
    res.json({ socialContent })
  } catch (error) {
    logger.error('Social integration error:', error)
    res.status(500).json({ error: 'Social content generation failed' })
  }
})

// Hashtag suggestions
router.post('/suggest-hashtags', auth, async (req, res) => {
  try {
    const { content, platform, industry } = req.body
    
    const prompt = `
      Generate relevant hashtags for this ${platform} content:
      
      Content: ${content}
      Industry: ${industry}
      Platform: ${platform}
      
      Provide 10-15 hashtags that are:
      - Relevant to the content and industry
      - Popular but not overly saturated
      - Mix of broad and niche tags
      - Platform-appropriate
      
      Return as comma-separated list without # symbol.
    `

    const response = await aiService.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 200
    })

    const hashtags = response.choices[0].message.content
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => '#' + tag.replace('#', ''))

    res.json({ hashtags })
  } catch (error) {
    logger.error('Hashtag suggestion error:', error)
    res.status(500).json({ error: 'Hashtag generation failed' })
  }
})

// Social media calendar
router.post('/create-calendar', auth, async (req, res) => {
  try {
    const { emailSequence, platforms, startDate, frequency = 'daily' } = req.body
    
    const calendar = []
    const emails = emailSequence.emails || []
    
    emails.forEach((email, index) => {
      platforms.forEach(platform => {
        const postDate = calculatePostDate(startDate, index, frequency, platform)
        
        calendar.push({
          date: postDate,
          platform,
          content: `Social post based on: ${email.subject}`,
          emailReference: email.subject,
          status: 'scheduled',
          bestTime: getBestPostingTime(platform),
          engagement: predictEngagement(platform, email.subject)
        })
      })
    })
    
    // Sort by date
    calendar.sort((a, b) => new Date(a.date) - new Date(b.date))
    
    res.json({ calendar })
  } catch (error) {
    logger.error('Calendar creation error:', error)
    res.status(500).json({ error: 'Calendar creation failed' })
  }
})

// Helper functions
function extractHashtags(content) {
  const hashtagRegex = /#[\w]+/g
  return content.match(hashtagRegex) || []
}

function getBestPostingTime(platform) {
  const times = {
    twitter: '1:00 PM - 3:00 PM',
    linkedin: '8:00 AM - 10:00 AM',
    facebook: '1:00 PM - 4:00 PM',
    instagram: '11:00 AM - 2:00 PM'
  }
  return times[platform] || '12:00 PM - 2:00 PM'
}

function calculatePostDate(startDate, index, frequency, platform) {
  const start = new Date(startDate)
  const platformDelays = {
    twitter: 0, // Post immediately
    linkedin: 2, // 2 hours later
    facebook: 4, // 4 hours later
    instagram: 6  // 6 hours later
  }
  
  const delay = platformDelays[platform] || 0
  const dayOffset = frequency === 'daily' ? index : index * 3 // Every 3 days if not daily
  
  const postDate = new Date(start)
  postDate.setDate(postDate.getDate() + dayOffset)
  postDate.setHours(postDate.getHours() + delay)
  
  return postDate.toISOString()
}

function predictEngagement(platform, content) {
  // Simple engagement prediction based on content characteristics
  let score = 50 // Base score
  
  if (content.includes('?')) score += 10 // Questions increase engagement
  if (content.length < 100) score += 5 // Shorter content often performs better
  if (/\b(free|new|exclusive)\b/i.test(content)) score += 8 // Power words
  
  const platformMultipliers = {
    twitter: 1.2,
    linkedin: 1.0,
    facebook: 0.9,
    instagram: 1.3
  }
  
  return Math.round(score * (platformMultipliers[platform] || 1))
}

export default router