import express from 'express'
import { auth } from '../middleware/auth.js'
import revenueOptimizationService from '../services/revenueOptimizationService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * Get user's conversion analysis
 * Used for personalized upgrade prompts
 */
router.get('/conversion-analysis', auth, async (req, res) => {
  try {
    const analysis = await revenueOptimizationService.analyzeConversionOpportunity(req.user.id)
    
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not available'
      })
    }
    
    res.json({
      success: true,
      data: analysis
    })
  } catch (error) {
    logger.error('Conversion analysis error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to analyze conversion opportunity'
    })
  }
})

/**
 * Get conversion funnel analytics (admin only)
 */
router.get('/funnel-analytics', auth, async (req, res) => {
  try {
    // Add admin check here in production
    const analytics = await revenueOptimizationService.getConversionFunnelAnalytics()
    
    if (!analytics) {
      return res.status(500).json({
        success: false,
        message: 'Analytics not available'
      })
    }
    
    res.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    logger.error('Funnel analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics'
    })
  }
})

/**
 * Get pricing optimization recommendations (admin only)
 */
router.get('/pricing-optimization', auth, async (req, res) => {
  try {
    // Add admin check here in production
    const recommendations = await revenueOptimizationService.getPricingOptimizationRecommendations()
    
    if (!recommendations) {
      return res.status(500).json({
        success: false,
        message: 'Recommendations not available'
      })
    }
    
    res.json({
      success: true,
      data: recommendations
    })
  } catch (error) {
    logger.error('Pricing optimization error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recommendations'
    })
  }
})

/**
 * Track conversion events
 */
router.post('/track-conversion', auth, async (req, res) => {
  try {
    const { eventType, metadata } = req.body
    
    if (!eventType) {
      return res.status(400).json({
        success: false,
        message: 'Event type is required'
      })
    }
    
    await revenueOptimizationService.trackConversionEvent(req.user.id, eventType, metadata)
    
    res.json({
      success: true,
      message: 'Event tracked successfully'
    })
  } catch (error) {
    logger.error('Conversion tracking error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to track event'
    })
  }
})

/**
 * Get high-priority conversion opportunities (admin only)
 */
router.get('/opportunities', auth, async (req, res) => {
  try {
    // Add admin check here in production
    const opportunities = await revenueOptimizationService.getConversionOpportunities()
    
    res.json({
      success: true,
      data: opportunities
    })
  } catch (error) {
    logger.error('Conversion opportunities error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch opportunities'
    })
  }
})

export default router