import express from 'express';
import { auth } from '../middleware/auth.js';
import teamCollaboration from '../services/teamCollaboration.js';

const router = express.Router();

// Create a new team
router.post('/create', auth, async (req, res) => {
  try {
    const { name, description, settings } = req.body;
    
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Team name is required'
      });
    }

    const result = await teamCollaboration.createTeam(req.user.id, {
      name: name.trim(),
      description: description?.trim() || '',
      settings: settings || {}
    });

    res.status(201).json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Create team error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create team'
    });
  }
});

// Get user's teams
router.get('/my-teams', auth, async (req, res) => {
  try {
    const teams = teamCollaboration.getUserTeams(req.user.id);
    
    res.json({
      success: true,
      data: teams
    });

  } catch (error) {
    console.error('Get user teams error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch teams'
    });
  }
});

// Get team details
router.get('/:teamId', auth, async (req, res) => {
  try {
    const { teamId } = req.params;
    
    const team = teamCollaboration.getTeam(teamId, req.user.id);
    
    res.json({
      success: true,
      data: team
    });

  } catch (error) {
    console.error('Get team error:', error);
    res.status(404).json({
      success: false,
      error: error.message || 'Team not found'
    });
  }
});

// Invite user to team
router.post('/:teamId/invite', auth, async (req, res) => {
  try {
    const { teamId } = req.params;
    const { email, role, message } = req.body;
    
    if (!email || !email.includes('@')) {
      return res.status(400).json({
        success: false,
        error: 'Valid email address is required'
      });
    }

    const result = await teamCollaboration.inviteToTeam(teamId, req.user.id, {
      email: email.trim().toLowerCase(),
      role: role || 'viewer',
      message: message?.trim() || ''
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Invite user error:', error);
    res.status(403).json({
      success: false,
      error: error.message || 'Failed to send invitation'
    });
  }
});

// Accept team invitation
router.post('/invitations/:inviteId/accept', auth, async (req, res) => {
  try {
    const { inviteId } = req.params;
    
    const result = await teamCollaboration.acceptInvitation(inviteId, req.user.id);
    
    res.json({
      success: true,
      data: result,
      message: 'Successfully joined team!'
    });

  } catch (error) {
    console.error('Accept invitation error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to accept invitation'
    });
  }
});

// Share workflow with team
router.post('/:teamId/workflows/share', auth, async (req, res) => {
  try {
    const { teamId } = req.params;
    const { workflowId, permissions, settings } = req.body;
    
    if (!workflowId) {
      return res.status(400).json({
        success: false,
        error: 'Workflow ID is required'
      });
    }

    const result = await teamCollaboration.shareWorkflow(
      workflowId, 
      teamId, 
      req.user.id, 
      { ...permissions, ...settings }
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Share workflow error:', error);
    res.status(403).json({
      success: false,
      error: error.message || 'Failed to share workflow'
    });
  }
});

// Get team workflows
router.get('/:teamId/workflows', auth, async (req, res) => {
  try {
    const { teamId } = req.params;
    
    const workflows = teamCollaboration.getTeamWorkflows(teamId, req.user.id);
    
    res.json({
      success: true,
      data: workflows
    });

  } catch (error) {
    console.error('Get team workflows error:', error);
    res.status(403).json({
      success: false,
      error: error.message || 'Failed to fetch team workflows'
    });
  }
});

// Add workflow comment
router.post('/:teamId/workflows/:workflowId/comments', auth, async (req, res) => {
  try {
    const { teamId, workflowId } = req.params;
    const { content, type, mentions } = req.body;
    
    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Comment content is required'
      });
    }

    const result = await teamCollaboration.addWorkflowComment(
      workflowId, 
      teamId, 
      req.user.id, 
      {
        content: content.trim(),
        type: type || 'general',
        mentions: mentions || []
      }
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Add comment error:', error);
    res.status(403).json({
      success: false,
      error: error.message || 'Failed to add comment'
    });
  }
});

// Get team analytics
router.get('/:teamId/analytics', auth, async (req, res) => {
  try {
    const { teamId } = req.params;
    
    const analytics = teamCollaboration.getTeamAnalytics(teamId, req.user.id);
    
    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get team analytics error:', error);
    res.status(403).json({
      success: false,
      error: error.message || 'Failed to fetch team analytics'
    });
  }
});

// Get available roles
router.get('/roles/available', auth, async (req, res) => {
  try {
    const roles = teamCollaboration.getAvailableRoles();
    
    res.json({
      success: true,
      data: roles
    });

  } catch (error) {
    console.error('Get roles error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch available roles'
    });
  }
});

export default router;