import express from 'express'
import { body, validationResult } from 'express-validator'
import { auth } from '../middleware/auth.js'
import { protect } from '../middleware/auth.js'
import Workflow from '../models/Workflow.js'
import naturalLanguageWorkflowService from '../services/naturalLanguageWorkflowService.js'
import workflowEngine from '../services/workflowEngine.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * NeuroColony Colony Intelligence Workflow API Routes
 * Natural language workflow generation - Superior to n8n's visual-only approach
 */

// Get all workflows for user
router.get('/', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20, status, search } = req.query
    const skip = (page - 1) * limit
    
    // Build query
    const query = { userId: req.user.id }
    if (status) query.status = status
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }
    
    // Fetch workflows from database
    const workflows = await Workflow.find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('createdBy', 'name email')
      .lean()
    
    const total = await Workflow.countDocuments(query)
    
    // Transform workflows to include execution stats
    const workflowsWithStats = await Promise.all(
      workflows.map(async (workflow) => {
        const executions = await db.collection('workflow_executions').countDocuments({
          workflowId: workflow._id,
          status: 'completed'
        })
        
        const lastExecution = await db.collection('workflow_executions')
          .findOne({ workflowId: workflow._id }, { sort: { createdAt: -1 } })
        
        return {
          ...workflow,
          stats: {
            totalExecutions: executions,
            lastExecutedAt: lastExecution?.createdAt,
            successRate: lastExecution ? 
              (await db.collection('workflow_executions').countDocuments({
                workflowId: workflow._id,
                status: 'completed'
              }) / await db.collection('workflow_executions').countDocuments({
                workflowId: workflow._id
              }) * 100).toFixed(1) : 0
          }
        }
      })
    )

    res.json({
      success: true,
      data: workflowsWithStats,
      meta: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    logger.error('Get workflows error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflows',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
})

// Get specific workflow
router.get('/:workflowId', auth, async (req, res) => {
  try {
    const { workflowId } = req.params
    
    // For MVP, return mock workflow data
    // In production, this would query the database
    const mockWorkflow = {
      id: workflowId,
      name: 'Email Marketing Workflow',
      description: 'Comprehensive email marketing automation',
      nodes: [
        {
          id: 'node_1',
          type: 'email-sequence-generator',
          name: 'Email Sequence Generator',
          icon: '📧',
          position: { x: 100, y: 100 },
          inputs: ['businessInfo', 'sequenceSettings'],
          outputs: ['emailSequence', 'aiAnalysis'],
          configuration: {
            enabled: true,
            timeout: 30,
            retries: 1
          }
        }
      ],
      connections: [],
      triggers: [],
      settings: {
        maxConcurrency: 3,
        timeout: 300000
      },
      createdAt: new Date(),
      lastModified: new Date()
    }

    res.json({
      success: true,
      data: mockWorkflow
    })
  } catch (error) {
    logger.error('Get workflow error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflow'
    })
  }
})

// Create new workflow
router.post('/', auth, [
  body('name').trim().isLength({ min: 1, max: 100 }).withMessage('Workflow name is required and must be under 100 characters'),
  body('description').optional().trim().isLength({ max: 500 }).withMessage('Description must be under 500 characters'),
  body('agents').isArray().withMessage('Agents array is required'),
  body('connections').isArray().withMessage('Connections array is required')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { name, description, agents, connections } = req.body

    // Generate workflow ID
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Register workflow with workflow engine
    const workflow = workflowEngine.registerWorkflow(workflowId, {
      name,
      description,
      agents,
      connections,
      author: req.user._id
    })

    logger.info(`Created workflow ${workflowId} for user ${req.user._id}`)

    res.status(201).json({
      success: true,
      data: workflow,
      message: 'Workflow created successfully',
      enhancement: 'Visual workflow builder with n8n-style automation'
    })

  } catch (error) {
    logger.error('Create workflow error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create workflow'
    })
  }
})

// Update workflow
router.put('/:workflowId', auth, [
  body('name').optional().trim().isLength({ min: 1, max: 100 }),
  body('description').optional().trim().isLength({ max: 500 }),
  body('agents').optional().isArray(),
  body('connections').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { workflowId } = req.params
    const updates = req.body

    // For MVP, simulate workflow update
    // In production, this would update the database record
    logger.info(`Updated workflow ${workflowId} for user ${req.user._id}`)

    res.json({
      success: true,
      data: {
        id: workflowId,
        ...updates,
        lastModified: new Date()
      },
      message: 'Workflow updated successfully'
    })

  } catch (error) {
    logger.error('Update workflow error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update workflow'
    })
  }
})

// Execute workflow
router.post('/:workflowId/execute', auth, [
  body('inputs').optional().isObject(),
  body('context').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { workflowId } = req.params
    const { inputs = {}, context = {} } = req.body

    logger.info(`Executing workflow ${workflowId} for user ${req.user._id}`)

    // Add user context
    const executionContext = {
      ...context,
      userId: req.user._id,
      userEmail: req.user.email,
      timestamp: new Date()
    }

    const result = await workflowEngine.executeWorkflow(workflowId, inputs, executionContext)

    res.json({
      success: true,
      data: result,
      message: `Workflow ${workflowId} executed successfully`,
      enhancement: 'Advanced workflow execution with dependency resolution'
    })

  } catch (error) {
    logger.error(`Workflow execution error (${req.params.workflowId}):`, error)
    res.status(500).json({
      success: false,
      message: error.message || 'Workflow execution failed',
      workflowId: req.params.workflowId
    })
  }
})

// Get workflow execution status
router.get('/:workflowId/executions/:executionId', auth, async (req, res) => {
  try {
    const { executionId } = req.params
    
    const status = workflowEngine.getWorkflowStatus(executionId)
    
    if (!status) {
      return res.status(404).json({
        success: false,
        message: 'Execution not found'
      })
    }

    res.json({
      success: true,
      data: status
    })
  } catch (error) {
    logger.error('Get execution status error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch execution status'
    })
  }
})

// Get workflow execution history
router.get('/:workflowId/executions', auth, async (req, res) => {
  try {
    const { workflowId } = req.params
    const { page = 1, limit = 10 } = req.query
    
    const history = workflowEngine.getExecutionHistory(workflowId)
    
    // Paginate results
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + parseInt(limit)
    const paginatedHistory = history.slice(startIndex, endIndex)

    res.json({
      success: true,
      data: paginatedHistory,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: history.length,
        pages: Math.ceil(history.length / limit)
      }
    })
  } catch (error) {
    logger.error('Get execution history error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch execution history'
    })
  }
})

// Cancel workflow execution
router.post('/:workflowId/executions/:executionId/cancel', auth, async (req, res) => {
  try {
    const { executionId } = req.params
    
    await workflowEngine.cancelWorkflow(executionId)

    res.json({
      success: true,
      message: 'Workflow execution cancelled successfully'
    })
  } catch (error) {
    logger.error('Cancel workflow error:', error)
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to cancel workflow execution'
    })
  }
})

// Get workflow analytics
router.get('/:workflowId/analytics', auth, async (req, res) => {
  try {
    const { workflowId } = req.params
    
    const analytics = workflowEngine.getWorkflowAnalytics(workflowId)

    res.json({
      success: true,
      data: {
        workflowId,
        analytics,
        generatedAt: new Date()
      },
      enhancement: 'Advanced workflow performance analytics'
    })
  } catch (error) {
    logger.error('Get workflow analytics error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflow analytics'
    })
  }
})

// Delete workflow
router.delete('/:workflowId', auth, async (req, res) => {
  try {
    const { workflowId } = req.params

    // For MVP, simulate deletion
    // In production, this would remove from database
    logger.info(`Deleted workflow ${workflowId} for user ${req.user._id}`)

    res.json({
      success: true,
      message: 'Workflow deleted successfully'
    })
  } catch (error) {
    logger.error('Delete workflow error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete workflow'
    })
  }
})

// Workflow templates - get popular workflow templates
router.get('/templates/popular', async (req, res) => {
  try {
    const templates = [
      {
        id: 'template_email_onboarding',
        name: 'Email Onboarding Sequence',
        description: 'Welcome new subscribers with a 7-email onboarding sequence',
        category: 'email-marketing',
        difficulty: 'beginner',
        estimatedTime: '15 minutes setup',
        nodes: 3,
        connections: 2,
        usageCount: 1245,
        rating: 4.8,
        preview: '📧 Welcome → 🎯 Optimize → 📊 Track'
      },
      {
        id: 'template_lead_nurturing',
        name: 'Lead Nurturing Campaign',
        description: 'Multi-touch nurturing for qualified leads with segmentation',
        category: 'lead-generation',
        difficulty: 'intermediate',
        estimatedTime: '30 minutes setup',
        nodes: 5,
        connections: 4,
        usageCount: 987,
        rating: 4.9,
        preview: '👥 Segment → ✨ Personalize → 🌱 Nurture → 📈 Convert'
      },
      {
        id: 'template_abandoned_cart',
        name: 'Abandoned Cart Recovery',
        description: 'Win back customers who abandoned their shopping cart',
        category: 'ecommerce',
        difficulty: 'intermediate',
        estimatedTime: '20 minutes setup',
        nodes: 4,
        connections: 3,
        usageCount: 756,
        rating: 4.7,
        preview: '🛒 Detect → 📧 Remind → 💸 Incentivize → 🔄 Recover'
      },
      {
        id: 'template_reengagement',
        name: 'Re-engagement Campaign',
        description: 'Win back inactive subscribers with targeted campaigns',
        category: 'retention',
        difficulty: 'beginner',
        estimatedTime: '10 minutes setup',
        nodes: 3,
        connections: 2,
        usageCount: 654,
        rating: 4.6,
        preview: '🔍 Identify → 🔄 Reengage → ❤️ Reactivate'
      }
    ]

    res.json({
      success: true,
      data: templates,
      meta: {
        total: templates.length,
        categories: ['email-marketing', 'lead-generation', 'ecommerce', 'retention'],
        enhancement: 'Pre-built marketing automation templates'
      }
    })
  } catch (error) {
    logger.error('Get workflow templates error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflow templates'
    })
  }
})

// Create workflow from template
router.post('/templates/:templateId/create', auth, [
  body('name').optional().trim().isLength({ min: 1, max: 100 }),
  body('customization').optional().isObject()
], async (req, res) => {
  try {
    const { templateId } = req.params
    const { name, customization = {} } = req.body

    // Mock template creation
    const workflowId = `workflow_from_template_${Date.now()}`
    
    logger.info(`Created workflow from template ${templateId} for user ${req.user._id}`)

    res.status(201).json({
      success: true,
      data: {
        id: workflowId,
        name: name || `Workflow from ${templateId}`,
        templateId,
        customization,
        createdAt: new Date()
      },
      message: 'Workflow created from template successfully',
      enhancement: 'One-click workflow creation from marketing templates'
    })
  } catch (error) {
    logger.error('Create workflow from template error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create workflow from template'
    })
  }
})

/**
 * Natural Language Workflow Generation Routes
 * Key differentiator from n8n - generate workflows from plain English descriptions
 */

/**
 * POST /api/workflows/generate-from-description
 * Generate workflow from natural language description
 * Example: "Create a workflow that monitors my competitors' pricing and sends me alerts"
 */
router.post('/generate-from-description', protect, [
  body('description')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('name')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Name must be between 3 and 100 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { description, name, category } = req.body
    const userId = req.user._id

    logger.info(`🧠 Generating workflow from description for user ${userId}`)

    // Generate workflow using natural language processing
    const result = await naturalLanguageWorkflowService.generateWorkflowFromDescription(
      description,
      userId,
      { name, category }
    )

    res.status(201).json({
      success: true,
      data: {
        workflow: result.workflow,
        analysis: result.analysis,
        recommendations: result.recommendations
      },
      message: 'Workflow generated successfully from description',
      meta: {
        feature: 'Natural Language Workflow Generation',
        advantage: 'Superior to n8n - no technical knowledge required'
      }
    })
  } catch (error) {
    logger.error('Failed to generate workflow from description:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate workflow from description',
      error: error.message
    })
  }
})

/**
 * GET /api/workflows/templates
 * Get workflow templates with natural language descriptions
 */
router.get('/templates', protect, async (req, res) => {
  try {
    const templates = await Workflow.find({
      isTemplate: true,
      isPublic: true
    }).sort({ name: 1 })

    // Add natural language descriptions for each template
    const enhancedTemplates = templates.map(template => ({
      ...template.toObject(),
      naturalLanguageDescription: generateNaturalLanguageDescription(template),
      useCases: generateUseCases(template.category),
      estimatedSetupTime: estimateSetupTime(template.steps.length),
      businessValue: estimateBusinessValue(template.category)
    }))

    res.json({
      success: true,
      data: {
        templates: enhancedTemplates,
        totalTemplates: templates.length,
        categories: [...new Set(templates.map(t => t.category))]
      }
    })
  } catch (error) {
    logger.error('Failed to fetch workflow templates:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflow templates',
      error: error.message
    })
  }
})

/**
 * POST /api/workflows/:workflowId/activate
 * Activate a workflow for execution
 */
router.post('/:workflowId/activate', protect, async (req, res) => {
  try {
    const { workflowId } = req.params
    const userId = req.user._id

    const workflow = await Workflow.findOne({
      _id: workflowId,
      owner: userId
    })

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      })
    }

    await workflow.activate()

    res.json({
      success: true,
      data: { workflow },
      message: 'Workflow activated successfully'
    })
  } catch (error) {
    logger.error('Failed to activate workflow:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to activate workflow',
      error: error.message
    })
  }
})

/**
 * GET /api/workflows/:workflowId/analytics
 * Get workflow performance analytics
 */
router.get('/:workflowId/analytics', protect, async (req, res) => {
  try {
    const { workflowId } = req.params
    const userId = req.user._id

    const workflow = await Workflow.findOne({
      _id: workflowId,
      owner: userId
    })

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      })
    }

    const analytics = {
      overview: {
        totalExecutions: workflow.analytics.totalExecutions,
        successfulExecutions: workflow.analytics.successfulExecutions,
        successRate: workflow.analytics.successRate,
        averageExecutionTime: workflow.analytics.averageExecutionTime,
        lastExecutionTime: workflow.analytics.lastExecutionTime
      },
      stepAnalytics: workflow.analytics.stepAnalytics,
      colonyMetrics: workflow.colonyMetrics,
      performance: {
        trend: 'improving', // Would calculate from historical data
        bottlenecks: identifyBottlenecks(workflow.analytics.stepAnalytics),
        recommendations: generatePerformanceRecommendations(workflow)
      }
    }

    res.json({
      success: true,
      data: { analytics }
    })
  } catch (error) {
    logger.error('Failed to fetch workflow analytics:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workflow analytics',
      error: error.message
    })
  }
})

/**
 * POST /api/workflows/ai-suggestions
 * Get AI-powered workflow improvement suggestions
 */
router.post('/ai-suggestions', protect, async (req, res) => {
  try {
    const { workflowId, currentPerformance, goals } = req.body
    const userId = req.user._id

    // This would use AI to analyze workflow performance and suggest improvements
    const suggestions = await generateAISuggestions(workflowId, currentPerformance, goals)

    res.json({
      success: true,
      data: { suggestions },
      message: 'AI suggestions generated successfully'
    })
  } catch (error) {
    logger.error('Failed to generate AI suggestions:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI suggestions',
      error: error.message
    })
  }
})

// Helper functions for natural language workflow features
function generateNaturalLanguageDescription(workflow) {
  const stepCount = workflow.steps.length
  const category = workflow.category || 'automation'

  return `This ${category} workflow has ${stepCount} steps that work together to ${workflow.description.toLowerCase()}. Simply activate it and the AI agents will handle the rest.`
}

function generateUseCases(category) {
  const useCases = {
    marketing: [
      'Email campaign automation',
      'Lead nurturing sequences',
      'Social media scheduling',
      'Customer segmentation'
    ],
    sales: [
      'Lead qualification',
      'Follow-up automation',
      'Pipeline management',
      'Proposal generation'
    ],
    support: [
      'Ticket routing',
      'Response automation',
      'Escalation management',
      'Customer satisfaction tracking'
    ]
  }

  return useCases[category] || ['Process automation', 'Task management', 'Data processing']
}

function estimateSetupTime(stepCount) {
  if (stepCount <= 3) return '5-10 minutes'
  if (stepCount <= 6) return '10-20 minutes'
  return '20-30 minutes'
}

function estimateBusinessValue(category) {
  const values = {
    marketing: 'Increase conversion rates by 25-40%',
    sales: 'Reduce sales cycle by 30-50%',
    support: 'Improve response time by 60-80%',
    analytics: 'Better decision making with real-time insights'
  }

  return values[category] || 'Significant time savings and efficiency gains'
}

function identifyBottlenecks(stepAnalytics) {
  if (!stepAnalytics || stepAnalytics.length === 0) return []

  return stepAnalytics
    .filter(step => step.averageTime > 60000) // Steps taking more than 1 minute
    .map(step => ({
      stepId: step.stepId,
      issue: 'High execution time',
      impact: 'Slowing down overall workflow',
      suggestion: 'Consider optimizing this step or running it in parallel'
    }))
}

function generatePerformanceRecommendations(workflow) {
  const recommendations = []

  if (workflow.analytics.successRate < 0.9) {
    recommendations.push({
      type: 'reliability',
      priority: 'high',
      title: 'Improve Success Rate',
      description: 'Add error handling and retry logic to improve reliability'
    })
  }

  if (workflow.steps.length > 5) {
    recommendations.push({
      type: 'performance',
      priority: 'medium',
      title: 'Consider Parallel Execution',
      description: 'Some steps could run in parallel to improve speed'
    })
  }

  return recommendations
}

async function generateAISuggestions(workflowId, currentPerformance, goals) {
  // Mock AI suggestions - in production, this would use AI service
  return [
    {
      type: 'optimization',
      confidence: 0.85,
      title: 'Add Audience Segmentation',
      description: 'Adding audience segmentation before email generation could improve conversion rates by 15-25%',
      implementation: 'Add an Audience Segmentation agent before your Email Sequence agent',
      estimatedImpact: '+20% conversion rate'
    },
    {
      type: 'automation',
      confidence: 0.92,
      title: 'Enable Automatic Scheduling',
      description: 'Set up automatic execution based on optimal send times',
      implementation: 'Configure the Send Time Optimization agent to trigger workflow automatically',
      estimatedImpact: '+10% open rates'
    }
  ]
}

export default router