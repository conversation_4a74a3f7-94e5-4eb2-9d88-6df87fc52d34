import express from 'express'
import { body, validationResult } from 'express-validator'
import { auth } from '../middleware/auth.js'
import aiService from '../services/aiService.js'
import { logger } from '../utils/logger.js'
import ConfigValidator from '../utils/configValidator.js'
import PortValidator from '../utils/portValidator.js'
import mongoose from 'mongoose'
import Redis from 'ioredis'

const router = express.Router()

// Health check endpoint
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Test API is working',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  })
})

// Comprehensive system health check
router.get('/health/system', async (req, res) => {
  const startTime = Date.now()
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    checks: {},
    network: {
      containerMode: !!process.env.DOCKER_ENV,
      mongoHost: process.env.MONGODB_URI,
      redisHost: process.env.REDIS_URL
    }
  }

  try {
    // Enhanced Database health with connection testing
    try {
      const dbState = mongoose.connection.readyState
      const dbStatus = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting'
      }
      
      // Test actual database connectivity
      let dbTestResult = null
      if (dbState === 1) {
        try {
          // Perform a simple query to test DB responsiveness
          await mongoose.connection.db.admin().ping()
          dbTestResult = 'ping_success'
        } catch (pingError) {
          dbTestResult = `ping_failed: ${pingError.message}`
        }
      }
      
      health.checks.database = {
        status: dbState === 1 && dbTestResult === 'ping_success' ? 'healthy' : 'unhealthy',
        state: dbStatus[dbState] || 'unknown',
        host: mongoose.connection.host,
        name: mongoose.connection.name,
        uri: process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/[^@]+@/, '//***:***@') : 'not_configured',
        testResult: dbTestResult,
        readyState: dbState
      }
    } catch (error) {
      health.checks.database = {
        status: 'error',
        error: error.message,
        uri: process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/[^@]+@/, '//***:***@') : 'not_configured'
      }
    }

    // Enhanced Redis health with timeout and retry logic
    try {
      const redis = new Redis(process.env.REDIS_URL, { 
        connectTimeout: 3000,
        lazyConnect: true,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 1
      })
      
      const connectPromise = redis.connect()
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Redis connection timeout')), 3000)
      )
      
      await Promise.race([connectPromise, timeoutPromise])
      const pong = await redis.ping()
      
      health.checks.redis = {
        status: pong === 'PONG' ? 'healthy' : 'unhealthy',
        response: pong,
        url: process.env.REDIS_URL || 'not_configured'
      }
      
      await redis.disconnect()
    } catch (error) {
      health.checks.redis = {
        status: 'error',
        error: error.message,
        url: process.env.REDIS_URL || 'not_configured',
        errorType: error.code || 'unknown'
      }
    }

    // Memory usage
    const memUsage = process.memoryUsage()
    health.checks.memory = {
      status: memUsage.heapUsed < 500 * 1024 * 1024 ? 'healthy' : 'warning', // 500MB threshold
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    }

    // Configuration validation
    const validator = new ConfigValidator()
    const configReport = await validator.validateAll()
    health.checks.configuration = {
      status: configReport.valid ? 'healthy' : 'error',
      errors: configReport.errors,
      warnings: configReport.warnings
    }

    // AI Service health check
    try {
      const aiMode = process.env.AI_MODE || 'auto'
      const openaiKey = process.env.OPENAI_API_KEY
      
      health.checks.ai_service = {
        mode: aiMode,
        openai_configured: !!(openaiKey && !openaiKey.includes('demo') && !openaiKey.includes('dummy')),
        local_ai_available: false, // Will be set by actual test
        status: 'healthy'
      }
      
      // Test if we can generate a simple response
      try {
        const testResult = await aiService.generateEmailSequence(
          { industry: 'Test', productService: 'Test', targetAudience: 'Test', pricePoint: 'Test' },
          { sequenceLength: 1, tone: 'professional', primaryGoal: 'sales' }
        )
        
        if (testResult && testResult.emails && testResult.emails.length > 0) {
          health.checks.ai_service.test_generation = 'success'
          health.checks.ai_service.status = 'healthy'
        } else {
          health.checks.ai_service.test_generation = 'failed'
          health.checks.ai_service.status = 'warning'
        }
      } catch (aiError) {
        health.checks.ai_service.test_generation = `error: ${aiError.message}`
        health.checks.ai_service.status = 'warning' // Still operational in demo mode
      }
    } catch (error) {
      health.checks.ai_service = {
        status: 'error',
        error: error.message
      }
    }

    // Port configuration validation
    try {
      const portValidator = new PortValidator()
      const portReport = portValidator.validatePorts()
      const portSummary = portValidator.getPortSummary()
      
      health.checks.ports = {
        status: portReport.valid ? 'healthy' : 'error',
        summary: portSummary,
        conflicts: portReport.conflicts,
        misconfigurations: portReport.misconfigurations,
        recommendations: portReport.recommendations
      }
    } catch (error) {
      health.checks.ports = {
        status: 'error',
        error: error.message
      }
    }

    // Overall status
    const hasErrors = Object.values(health.checks).some(check => check.status === 'error')
    const hasWarnings = Object.values(health.checks).some(check => check.status === 'warning')
    
    if (hasErrors) {
      health.status = 'unhealthy'
    } else if (hasWarnings) {
      health.status = 'warning'
    }

    health.responseTime = `${Date.now() - startTime}ms`
    
    const statusCode = health.status === 'healthy' ? 200 : health.status === 'warning' ? 200 : 503
    res.status(statusCode).json(health)

  } catch (error) {
    logger.error('Health check failed:', error)
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message,
      responseTime: `${Date.now() - startTime}ms`
    })
  }
})

// Simple test route without database operations
router.post('/generate-demo', auth, [
  body('title').trim().isLength({ min: 1, max: 100 }).withMessage('Title is required and must be under 100 characters'),
  body('businessInfo.industry').notEmpty().withMessage('Industry is required'),
  body('businessInfo.productService').notEmpty().withMessage('Product/Service is required'),
  body('businessInfo.targetAudience').notEmpty().withMessage('Target audience is required'),
  body('businessInfo.pricePoint').notEmpty().withMessage('Price point is required'),
  body('generationSettings.sequenceLength').isInt({ min: 3, max: 14 }).withMessage('Sequence length must be between 3 and 14'),
  body('generationSettings.tone').isIn(['professional', 'casual', 'friendly', 'authoritative', 'conversational']).withMessage('Invalid tone'),
  body('generationSettings.primaryGoal').isIn(['sales', 'nurture', 'onboarding', 'retention', 'upsell']).withMessage('Invalid primary goal')
], async (req, res) => {
  try {
    logger.info('Demo generation started...')
    
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      logger.error('Validation errors:', errors.array())
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    logger.info('Validation passed, generating AI sequence...')
    
    // Generate sequence using AI (without saving to DB)
    const aiResult = await aiService.generateEmailSequence(
      req.body.businessInfo,
      req.body.generationSettings
    )
    
    logger.info('AI generation completed successfully')
    
    // Return result directly (no database save)
    res.status(201).json({
      success: true,
      data: {
        title: req.body.title,
        description: req.body.description,
        businessInfo: req.body.businessInfo,
        generationSettings: req.body.generationSettings,
        emails: aiResult.emails,
        aiAnalysis: aiResult.aiAnalysis,
        user: req.user._id,
        generatedAt: new Date()
      },
      message: 'Demo email sequence generated successfully!'
    })
    
  } catch (error) {
    logger.error('Demo generation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate demo sequence: ' + error.message
    })
  }
})

export default router