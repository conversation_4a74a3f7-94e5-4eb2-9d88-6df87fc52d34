import express from 'express'
import { auth } from '../middleware/auth.js'
import EmailSequence from '../models/EmailSequence.js'
import exportService from '../services/exportService.js'
import { logger } from '../utils/logger.js'
import path from 'path'
import fs from 'fs/promises'

const router = express.Router()

// Export sequence to various formats
router.post('/sequence/:id/:format', auth, async (req, res) => {
  try {
    const { id, format } = req.params
    const options = req.body || {}

    // Get sequence
    const sequence = await EmailSequence.findOne({
      _id: id,
      userId: req.user._id
    })

    if (!sequence) {
      return res.status(404).json({
        success: false,
        message: 'Sequence not found'
      })
    }

    let exportResult
    
    // Export based on format
    switch (format.toLowerCase()) {
      case 'pdf':
        exportResult = await exportService.exportSequenceToPDF(sequence, options)
        break
        
      case 'csv':
        exportResult = await exportService.exportSequenceToCSV(sequence, options)
        break
        
      case 'json':
        exportResult = await exportService.exportSequenceToJSON(sequence, options)
        break
        
      case 'markdown':
      case 'md':
        exportResult = await exportService.exportSequenceToMarkdown(sequence, options)
        break
        
      case 'mailchimp':
      case 'convertkit':
      case 'activecampaign':
        exportResult = await exportService.exportToIntegrationFormat(sequence, format, options)
        break
        
      default:
        return res.status(400).json({
          success: false,
          message: `Unsupported export format: ${format}`
        })
    }

    logger.info(`Sequence exported: ${id} to ${format} by user ${req.user._id}`)

    res.json({
      success: true,
      data: {
        filename: exportResult.filename,
        format: format,
        size: exportResult.size || 0,
        downloadUrl: `/api/export/download/${exportResult.filename}`
      }
    })

  } catch (error) {
    logger.error('Export error:', error)
    res.status(500).json({
      success: false,
      message: `Failed to export sequence: ${error.message}`
    })
  }
})

// Download exported file
router.get('/download/:filename', auth, async (req, res) => {
  try {
    const { filename } = req.params
    
    // Validate filename (prevent directory traversal)
    if (filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      })
    }

    const filepath = path.join(process.env.EXPORT_PATH || './exports', filename)
    
    // Check if file exists
    try {
      await fs.access(filepath)
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'Export file not found'
      })
    }

    // Determine content type based on extension
    const ext = path.extname(filename).toLowerCase()
    let contentType = 'application/octet-stream'
    
    switch (ext) {
      case '.pdf':
        contentType = 'application/pdf'
        break
      case '.csv':
        contentType = 'text/csv'
        break
      case '.json':
        contentType = 'application/json'
        break
      case '.md':
        contentType = 'text/markdown'
        break
    }

    // Send file
    res.setHeader('Content-Type', contentType)
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    
    const fileStream = await fs.open(filepath, 'r')
    const stream = fileStream.createReadStream()
    stream.pipe(res)
    
    stream.on('end', () => {
      fileStream.close()
    })

  } catch (error) {
    logger.error('Download error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to download file'
    })
  }
})

// Get available export formats
router.get('/formats', auth, (req, res) => {
  res.json({
    success: true,
    data: {
      document: [
        {
          format: 'pdf',
          name: 'PDF Document',
          description: 'Professional PDF with formatting',
          icon: 'file-pdf'
        },
        {
          format: 'markdown',
          name: 'Markdown',
          description: 'Plain text with markdown formatting',
          icon: 'file-text'
        }
      ],
      data: [
        {
          format: 'csv',
          name: 'CSV Spreadsheet',
          description: 'Import into Excel or Google Sheets',
          icon: 'file-spreadsheet'
        },
        {
          format: 'json',
          name: 'JSON Data',
          description: 'Raw data for developers',
          icon: 'file-code'
        }
      ],
      integrations: [
        {
          format: 'mailchimp',
          name: 'Mailchimp',
          description: 'Import into Mailchimp campaigns',
          icon: 'mail'
        },
        {
          format: 'convertkit',
          name: 'ConvertKit',
          description: 'Import into ConvertKit sequences',
          icon: 'mail'
        },
        {
          format: 'activecampaign',
          name: 'ActiveCampaign',
          description: 'Import into ActiveCampaign automations',
          icon: 'mail'
        }
      ]
    }
  })
})

// Bulk export multiple sequences
router.post('/bulk', auth, async (req, res) => {
  try {
    const { sequenceIds, format, options = {} } = req.body

    if (!Array.isArray(sequenceIds) || sequenceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No sequences provided'
      })
    }

    // Get sequences
    const sequences = await EmailSequence.find({
      _id: { $in: sequenceIds },
      userId: req.user._id
    })

    if (sequences.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No sequences found'
      })
    }

    const exports = []
    
    // Export each sequence
    for (const sequence of sequences) {
      try {
        let exportResult
        
        switch (format.toLowerCase()) {
          case 'pdf':
            exportResult = await exportService.exportSequenceToPDF(sequence, options)
            break
          case 'csv':
            exportResult = await exportService.exportSequenceToCSV(sequence, options)
            break
          case 'json':
            exportResult = await exportService.exportSequenceToJSON(sequence, options)
            break
          case 'markdown':
            exportResult = await exportService.exportSequenceToMarkdown(sequence, options)
            break
          default:
            continue
        }
        
        exports.push({
          sequenceId: sequence._id,
          sequenceName: sequence.name,
          filename: exportResult.filename,
          size: exportResult.size || 0
        })
        
      } catch (error) {
        logger.error(`Failed to export sequence ${sequence._id}:`, error)
        exports.push({
          sequenceId: sequence._id,
          sequenceName: sequence.name,
          error: error.message
        })
      }
    }

    logger.info(`Bulk export completed: ${exports.length} sequences to ${format}`)

    res.json({
      success: true,
      data: {
        totalRequested: sequenceIds.length,
        totalExported: exports.filter(e => !e.error).length,
        exports: exports
      }
    })

  } catch (error) {
    logger.error('Bulk export error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk export'
    })
  }
})

// Clean up old exports (admin only)
router.post('/cleanup', auth, async (req, res) => {
  try {
    // Check if user is admin (you might want to implement proper admin check)
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      })
    }

    const { daysToKeep = 7 } = req.body
    
    await exportService.cleanupOldExports(daysToKeep)

    res.json({
      success: true,
      message: `Cleaned up exports older than ${daysToKeep} days`
    })

  } catch (error) {
    logger.error('Cleanup error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to clean up exports'
    })
  }
})

export default router