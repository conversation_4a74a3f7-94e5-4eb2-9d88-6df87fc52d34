import express from 'express'
import { body, validationResult } from 'express-validator'
import { auth } from '../middleware/auth.js'
import usageService from '../services/usageService.js'
import User from '../models/User.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

// Get current usage statistics
router.get('/stats', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    await user.checkAndResetPeriod()
    const stats = user.getUsageStats()
    
    res.json({
      success: true,
      data: stats
    })
    
  } catch (error) {
    logger.error('Usage stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get usage statistics'
    })
  }
})

// Get usage history and projections
router.get('/history', auth, async (req, res) => {
  try {
    const months = parseInt(req.query.months) || 6
    const history = await usageService.getUsageHistory(req.user._id, months)
    
    res.json({
      success: true,
      data: history
    })
    
  } catch (error) {
    logger.error('Usage history error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get usage history'
    })
  }
})

// Check if user can generate a sequence
router.get('/check-generation', auth, async (req, res) => {
  try {
    const permission = await usageService.checkGenerationPermission(req.user._id)
    
    res.json({
      success: true,
      data: permission
    })
    
  } catch (error) {
    logger.error('Generation permission check error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to check generation permission'
    })
  }
})

// Give overage consent
router.post('/overage-consent', auth, async (req, res) => {
  try {
    const result = await usageService.giveOverageConsent(req.user._id)
    
    res.json({
      success: true,
      data: result,
      message: 'Overage billing enabled successfully'
    })
    
  } catch (error) {
    logger.error('Overage consent error:', error)
    res.status(400).json({
      success: false,
      message: error.message
    })
  }
})

// Revoke overage consent
router.delete('/overage-consent', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    user.usage.notifications.overageConsentGiven = false
    await user.save()
    
    logger.info(`Overage consent revoked by user: ${req.user._id}`)
    
    res.json({
      success: true,
      message: 'Overage billing disabled',
      data: {
        stats: user.getUsageStats()
      }
    })
    
  } catch (error) {
    logger.error('Overage consent revocation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to disable overage billing'
    })
  }
})

// Process sequence generation (called internally by sequence routes)
router.post('/process-generation', auth, async (req, res) => {
  try {
    const result = await usageService.processSequenceGeneration(req.user._id)
    
    res.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    logger.error('Sequence generation processing error:', error)
    res.status(400).json({
      success: false,
      message: error.message
    })
  }
})

// Get overage charges for current billing period
router.get('/overage-charges', auth, async (req, res) => {
  try {
    const charges = await usageService.calculateMonthlyOverages(req.user._id)
    
    res.json({
      success: true,
      data: charges
    })
    
  } catch (error) {
    logger.error('Overage charges calculation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to calculate overage charges'
    })
  }
})

// Send test usage notification (development only)
router.post('/test-notification', auth, async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({
      success: false,
      message: 'Test endpoints not available in production'
    })
  }
  
  try {
    const { type } = req.body
    const user = await User.findById(req.user._id)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }
    
    await usageService.sendUsageNotification(user, type)
    
    res.json({
      success: true,
      message: `Test ${type} notification sent`
    })
    
  } catch (error) {
    logger.error('Test notification error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to send test notification'
    })
  }
})

export default router