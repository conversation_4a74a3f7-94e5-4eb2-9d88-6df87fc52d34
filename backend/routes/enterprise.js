import express from 'express'
import { auth } from '../middleware/auth.js'
import whiteLabelService from '../services/whiteLabelService.js'
import ssoService from '../services/ssoService.js'
import advancedAnalyticsService from '../services/advancedAnalyticsService.js'

const router = express.Router()

// =============================================================================
// WHITE-LABEL ROUTES
// =============================================================================

// Create white-label organization
router.post('/white-label/organizations', auth, async (req, res) => {
  try {
    const result = await whiteLabelService.createOrganization(req.user.id, req.body)
    
    res.status(201).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Create organization error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create organization'
    })
  }
})

// Get organization configuration
router.get('/white-label/organizations/:orgId/config', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    const { domain } = req.query
    
    const config = whiteLabelService.getOrganizationConfig(orgId, domain)
    
    if (!config) {
      return res.status(404).json({
        success: false,
        error: 'Organization configuration not found'
      })
    }
    
    res.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('Get organization config error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch organization configuration'
    })
  }
})

// Update organization branding
router.put('/white-label/organizations/:orgId/branding', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    
    const result = await whiteLabelService.updateBranding(orgId, req.user.id, req.body)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Update branding error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to update branding'
    })
  }
})

// Set custom domain
router.post('/white-label/organizations/:orgId/domain', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    const { domain, sslConfig } = req.body
    
    const result = await whiteLabelService.setCustomDomain(orgId, req.user.id, domain, sslConfig)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Set custom domain error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to set custom domain'
    })
  }
})

// Create custom theme
router.post('/white-label/organizations/:orgId/themes', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    
    const result = await whiteLabelService.createCustomTheme(orgId, req.user.id, req.body)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Create custom theme error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create custom theme'
    })
  }
})

// Get available themes
router.get('/white-label/organizations/:orgId/themes', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    
    const themes = whiteLabelService.getAvailableThemes(orgId)
    
    res.json({
      success: true,
      data: themes
    })
  } catch (error) {
    console.error('Get themes error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch themes'
    })
  }
})

// =============================================================================
// SSO ROUTES
// =============================================================================

// Configure SSO provider
router.post('/sso/organizations/:orgId/providers', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    
    const result = await ssoService.configureSSOProvider(orgId, req.user.id, req.body)
    
    res.status(201).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Configure SSO provider error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to configure SSO provider'
    })
  }
})

// Get SSO configuration
router.get('/sso/organizations/:orgId/config', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    
    const config = ssoService.getOrganizationSSOConfig(orgId)
    
    res.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('Get SSO config error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch SSO configuration'
    })
  }
})

// Initiate SAML authentication
router.post('/sso/saml/:orgId/auth', async (req, res) => {
  try {
    const { orgId } = req.params
    const { relayState } = req.body
    
    const result = await ssoService.initiateSAMLAuth(orgId, relayState)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Initiate SAML auth error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to initiate SAML authentication'
    })
  }
})

// SAML assertion consumer service
router.post('/sso/saml/acs', async (req, res) => {
  try {
    const { SAMLResponse, RelayState } = req.body
    
    const result = await ssoService.processSAMLAssertion(SAMLResponse, RelayState)
    
    // Redirect with token or error
    if (result.success) {
      res.redirect(`${result.redirectUrl}?token=${result.token}`)
    } else {
      res.redirect(`/login?error=sso_failed`)
    }
  } catch (error) {
    console.error('Process SAML assertion error:', error)
    res.redirect('/login?error=sso_failed')
  }
})

// Initiate OIDC authentication
router.post('/sso/oidc/:orgId/auth', async (req, res) => {
  try {
    const { orgId } = req.params
    const { state } = req.body
    
    const result = await ssoService.initiateOIDCAuth(orgId, state)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Initiate OIDC auth error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to initiate OIDC authentication'
    })
  }
})

// Test SSO configuration
router.post('/sso/configurations/:configId/test', auth, async (req, res) => {
  try {
    const { configId } = req.params
    
    const result = await ssoService.testSSOConfiguration(configId, req.user.id)
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Test SSO configuration error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to test SSO configuration'
    })
  }
})

// Get SSO analytics
router.get('/sso/organizations/:orgId/analytics', auth, async (req, res) => {
  try {
    const { orgId } = req.params
    const { timeRange } = req.query
    
    const analytics = ssoService.getSSOAnalytics(orgId, timeRange)
    
    res.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Get SSO analytics error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch SSO analytics'
    })
  }
})

// =============================================================================
// ADVANCED ANALYTICS ROUTES
// =============================================================================

// Get dashboard data
router.get('/analytics/dashboards/:dashboardId', auth, async (req, res) => {
  try {
    const { dashboardId } = req.params
    const { orgId, timeframe } = req.query
    
    const result = await advancedAnalyticsService.getDashboardData(
      orgId || req.user.orgId, 
      dashboardId, 
      req.user.id, 
      timeframe
    )
    
    res.json({
      success: true,
      data: result.dashboard
    })
  } catch (error) {
    console.error('Get dashboard data error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to fetch dashboard data'
    })
  }
})

// Generate custom report
router.post('/analytics/reports', auth, async (req, res) => {
  try {
    const orgId = req.body.orgId || req.user.orgId
    
    const result = await advancedAnalyticsService.generateCustomReport(
      orgId, 
      req.user.id, 
      req.body
    )
    
    res.status(202).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Generate custom report error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to generate custom report'
    })
  }
})

// Get report status
router.get('/analytics/reports/:reportId/status', auth, async (req, res) => {
  try {
    const { reportId } = req.params
    
    // Mock report status check
    res.json({
      success: true,
      data: {
        reportId,
        status: 'completed',
        downloadUrl: `/api/enterprise/analytics/reports/${reportId}/download`,
        completedAt: new Date()
      }
    })
  } catch (error) {
    console.error('Get report status error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch report status'
    })
  }
})

// Create analytics alert
router.post('/analytics/alerts', auth, async (req, res) => {
  try {
    const orgId = req.body.orgId || req.user.orgId
    
    const result = await advancedAnalyticsService.createAnalyticsAlert(
      orgId, 
      req.user.id, 
      req.body
    )
    
    res.status(201).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Create analytics alert error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create analytics alert'
    })
  }
})

// Create analytics stream
router.post('/analytics/streams', auth, async (req, res) => {
  try {
    const orgId = req.body.orgId || req.user.orgId
    
    const result = await advancedAnalyticsService.createAnalyticsStream(
      orgId, 
      req.user.id, 
      req.body
    )
    
    res.status(201).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Create analytics stream error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create analytics stream'
    })
  }
})

// Get comparative analytics
router.post('/analytics/comparative', auth, async (req, res) => {
  try {
    const orgId = req.body.orgId || req.user.orgId
    
    const result = await advancedAnalyticsService.getComparativeAnalytics(orgId, req.body)
    
    res.json({
      success: true,
      data: result.data
    })
  } catch (error) {
    console.error('Get comparative analytics error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to fetch comparative analytics'
    })
  }
})

// Export analytics data
router.post('/analytics/export', auth, async (req, res) => {
  try {
    const orgId = req.body.orgId || req.user.orgId
    
    const result = await advancedAnalyticsService.exportAnalyticsData(
      orgId, 
      req.user.id, 
      req.body
    )
    
    res.status(202).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Export analytics data error:', error)
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to export analytics data'
    })
  }
})

// Get available dashboards
router.get('/analytics/dashboards', auth, async (req, res) => {
  try {
    const dashboards = [
      {
        id: 'executive-overview',
        name: 'Executive Overview',
        description: 'High-level KPIs and business metrics',
        category: 'executive'
      },
      {
        id: 'marketing-performance',
        name: 'Marketing Performance',
        description: 'Detailed marketing metrics and campaign analysis',
        category: 'marketing'
      },
      {
        id: 'ai-agent-analytics',
        name: 'AI Agent Analytics',
        description: 'Performance metrics for AI agents and workflows',
        category: 'ai'
      },
      {
        id: 'team-collaboration',
        name: 'Team Collaboration',
        description: 'Team productivity and collaboration metrics',
        category: 'team'
      }
    ]
    
    res.json({
      success: true,
      data: dashboards
    })
  } catch (error) {
    console.error('Get dashboards error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboards'
    })
  }
})

// =============================================================================
// ENTERPRISE HEALTH CHECK
// =============================================================================

router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    services: {
      whiteLabelService: 'operational',
      ssoService: 'operational',
      advancedAnalyticsService: 'operational'
    },
    timestamp: new Date()
  })
})

export default router