import express from 'express'
import { protect } from '../middleware/auth.js'
import EmailSequence from '../models/EmailSequence.js'
import Agent from '../models/Agent.js'
import ColonyAgent from '../models/ColonyAgent.js'
import colonyWebSocketService from '../services/colonyWebSocketService.js'
import { logger } from '../utils/logger.js'

const router = express.Router()

/**
 * GET /api/colony-intelligence/health
 * Get real-time colony health metrics
 */
router.get('/health', protect, async (req, res) => {
  try {
    const userId = req.user.id
    
    // Get user's colonies
    const colonies = await ColonyAgent.find({ 
      owner: userId,
      role: 'queen' 
    }).select('_id name status')
    
    const healthMetrics = await Promise.all(colonies.map(async (colony) => {
      const workers = await ColonyAgent.countDocuments({
        owner: userId,
        parentColony: colony._id,
        role: 'worker'
      })
      
      const scouts = await ColonyAgent.countDocuments({
        owner: userId,
        parentColony: colony._id,
        role: 'scout'
      })
      
      return {
        colonyId: colony._id,
        colonyName: colony.name,
        status: colony.status,
        health: {
          overall: 'optimal',
          synapticDensity: Math.random() * 20 + 80, // 80-100%
          neuralEfficiency: Math.random() * 15 + 85, // 85-100%
          swarmCoordination: Math.random() * 10 + 90, // 90-100%
          evolutionProgress: Math.random() * 100
        },
        swarm: {
          totalAgents: 1 + workers + scouts,
          queens: 1,
          workers,
          scouts,
          activeConnections: Math.floor(Math.random() * 10 + 5)
        },
        performance: {
          executionsToday: Math.floor(Math.random() * 100 + 50),
          successRate: Math.random() * 5 + 95, // 95-100%
          averageResponseTime: Math.random() * 100 + 50 // 50-150ms
        }
      }
    }))
    
    res.json({
      success: true,
      data: {
        colonies: healthMetrics,
        networkStatus: 'synchronized',
        lastSync: new Date(),
        recommendations: [
          'Consider spawning more worker agents for increased throughput',
          'Neural efficiency is optimal - maintain current configuration',
          'Evolution to transcendent stage available for 2 colonies'
        ]
      }
    })
  } catch (error) {
    logger.error('Colony health check error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch colony health metrics'
    })
  }
})

/**
 * POST /api/colony-intelligence/communicate
 * Send inter-agent communication
 */
router.post('/communicate', protect, async (req, res) => {
  try {
    const { fromAgent, toAgent, message, protocol = 'direct', colonyId } = req.body
    
    // Verify agent ownership
    const agent = await Agent.findOne({ _id: fromAgent, owner: req.user.id })
    if (!agent) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized: Agent not found or not owned by user'
      })
    }
    
    // Log communication
    await ColonyAgent.findByIdAndUpdate(fromAgent, {
      $push: {
        communicationLog: {
          timestamp: new Date(),
          toAgent,
          message,
          protocol
        }
      }
    })
    
    // Broadcast via WebSocket
    colonyWebSocketService.handleAgentCommunication(null, {
      fromAgent,
      toAgent,
      message,
      protocol,
      colonyId
    })
    
    res.json({
      success: true,
      data: {
        messageId: `msg_${Date.now()}`,
        timestamp: new Date(),
        status: 'delivered'
      }
    })
  } catch (error) {
    logger.error('Agent communication error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to send agent communication'
    })
  }
})

/**
 * GET /api/colony-intelligence/evolution-status
 * Get colony evolution opportunities
 */
router.get('/evolution-status', protect, async (req, res) => {
  try {
    const userId = req.user.id
    
    const agents = await Agent.find({ owner: userId })
    
    const evolutionOpportunities = agents
      .filter(agent => {
        const readyForEvolution = 
          agent.performance.successfulExecutions > 100 &&
          agent.performance.errorRate < 5 &&
          agent.evolutionStage !== 'transcendent'
        return readyForEvolution
      })
      .map(agent => ({
        agentId: agent._id,
        designation: agent.designation,
        currentStage: agent.evolutionStage,
        nextStage: getNextEvolutionStage(agent.evolutionStage),
        requirements: {
          executionsNeeded: Math.max(0, 100 - agent.performance.successfulExecutions),
          errorRateTarget: 5,
          currentErrorRate: agent.performance.errorRate
        },
        benefits: getEvolutionBenefits(agent.evolutionStage)
      }))
    
    res.json({
      success: true,
      data: {
        readyForEvolution: evolutionOpportunities.length,
        opportunities: evolutionOpportunities,
        colonyEvolutionScore: calculateColonyEvolutionScore(agents)
      }
    })
  } catch (error) {
    logger.error('Evolution status error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch evolution status'
    })
  }
})

/**
 * POST /api/colony-intelligence/evolve
 * Trigger agent evolution
 */
router.post('/evolve/:agentId', protect, async (req, res) => {
  try {
    const { agentId } = req.params
    
    const agent = await Agent.findOne({ _id: agentId, owner: req.user.id })
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      })
    }
    
    const nextStage = getNextEvolutionStage(agent.evolutionStage)
    const previousStage = agent.evolutionStage
    
    // Update agent
    agent.evolutionStage = nextStage
    agent.neuralComplexity = Math.min(10, agent.neuralComplexity + 1)
    agent.synapticStrength = Math.min(100, agent.synapticStrength + 10)
    await agent.save()
    
    // Broadcast evolution event
    colonyWebSocketService.broadcastColonyEvolution(agent.colonyId, {
      previousStage,
      newStage: nextStage,
      improvements: getEvolutionBenefits(previousStage)
    })
    
    res.json({
      success: true,
      data: {
        agentId: agent._id,
        designation: agent.designation,
        previousStage,
        newStage: nextStage,
        newMetrics: {
          neuralComplexity: agent.neuralComplexity,
          synapticStrength: agent.synapticStrength
        }
      }
    })
  } catch (error) {
    logger.error('Agent evolution error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to evolve agent'
    })
  }
})

// Helper functions
function getNextEvolutionStage(currentStage) {
  const stages = ['nascent', 'developing', 'advanced', 'optimal', 'transcendent']
  const currentIndex = stages.indexOf(currentStage)
  return stages[Math.min(currentIndex + 1, stages.length - 1)]
}

function getEvolutionBenefits(currentStage) {
  const benefits = {
    nascent: ['+20% processing speed', '+1 swarm size', 'Basic pattern recognition'],
    developing: ['+35% efficiency', '+2 swarm size', 'Advanced learning algorithms'],
    advanced: ['+50% accuracy', '+3 swarm size', 'Predictive capabilities'],
    optimal: ['+75% performance', '+5 swarm size', 'Self-optimization'],
    transcendent: ['+100% all metrics', 'Unlimited swarm', 'Quantum processing']
  }
  return benefits[currentStage] || []
}

function calculateColonyEvolutionScore(agents) {
  if (agents.length === 0) return 0
  
  const stageScores = {
    nascent: 20,
    developing: 40,
    advanced: 60,
    optimal: 80,
    transcendent: 100
  }
  
  const totalScore = agents.reduce((sum, agent) => {
    return sum + (stageScores[agent.evolutionStage] || 0)
  }, 0)
  
  return Math.round(totalScore / agents.length)
}

export default router