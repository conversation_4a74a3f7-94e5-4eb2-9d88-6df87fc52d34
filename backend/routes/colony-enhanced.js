import express from 'express';
import { auth } from '../middleware/auth.js';
import colonyIntelligenceEnhanced from '../services/colonyIntelligenceEnhanced.js';
import workflowExecutionEngine from '../services/workflowExecutionEngine.js';
import agentMarketplaceService from '../services/agentMarketplaceService.js';
import logger from '../utils/logger.js';

const router = express.Router();

// Colony Management Routes

// Create new colony
router.post('/create', auth, async (req, res) => {
  try {
    const { name, description } = req.body;
    const colony = await colonyIntelligenceEnhanced.createColony(
      name,
      description,
      req.user.id
    );
    
    res.json(colony);
  } catch (error) {
    logger.error('Failed to create colony:', error);
    res.status(500).json({ error: error.message });
  }
});

// List user's colonies
router.get('/list', auth, async (req, res) => {
  try {
    const colonies = Array.from(colonyIntelligenceEnhanced.colonies.values())
      .filter(colony => colony.userId === req.user.id);
    
    res.json({ colonies });
  } catch (error) {
    logger.error('Failed to list colonies:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get colony health
router.get('/:colonyId/health', auth, async (req, res) => {
  try {
    const health = await colonyIntelligenceEnhanced.getColonyHealth(req.params.colonyId);
    res.json(health);
  } catch (error) {
    logger.error('Failed to get colony health:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get colony agents
router.get('/:colonyId/agents', auth, async (req, res) => {
  try {
    const colony = colonyIntelligenceEnhanced.colonies.get(req.params.colonyId);
    if (!colony) {
      return res.status(404).json({ error: 'Colony not found' });
    }
    
    const agents = {
      queens: await Promise.all(
        colony.agents.queens.map(id => colonyIntelligenceEnhanced.getAgent(id))
      ),
      workers: await Promise.all(
        colony.agents.workers.map(id => colonyIntelligenceEnhanced.getAgent(id))
      ),
      scouts: await Promise.all(
        colony.agents.scouts.map(id => colonyIntelligenceEnhanced.getAgent(id))
      )
    };
    
    res.json({ agents });
  } catch (error) {
    logger.error('Failed to get colony agents:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get colony executions
router.get('/:colonyId/executions', auth, async (req, res) => {
  try {
    const { limit = 50, offset = 0, status } = req.query;
    
    const ColonyExecution = (await import('../models/ColonyExecution.js')).default;
    const query = { colonyId: req.params.colonyId };
    if (status) query.status = status;
    
    const executions = await ColonyExecution.find(query)
      .sort({ startTime: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset));
    
    res.json({ executions });
  } catch (error) {
    logger.error('Failed to get colony executions:', error);
    res.status(500).json({ error: error.message });
  }
});

// Enable autonomous mode
router.post('/:colonyId/autonomous', auth, async (req, res) => {
  try {
    const config = await colonyIntelligenceEnhanced.enableAutonomousMode(
      req.params.colonyId,
      req.body
    );
    
    res.json({ success: true, config });
  } catch (error) {
    logger.error('Failed to enable autonomous mode:', error);
    res.status(500).json({ error: error.message });
  }
});

// Agent Management Routes

// Create agent
router.post('/:colonyId/agents', auth, async (req, res) => {
  try {
    const agent = await colonyIntelligenceEnhanced.createAgent({
      ...req.body,
      colonyId: req.params.colonyId,
      userId: req.user.id
    });
    
    res.json(agent);
  } catch (error) {
    logger.error('Failed to create agent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Execute agent task
router.post('/agents/:agentId/execute', auth, async (req, res) => {
  try {
    const { task, input, priority = false } = req.body;
    
    const agent = await colonyIntelligenceEnhanced.getAgent(req.params.agentId);
    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    // Queue task based on agent type and priority
    const queue = priority 
      ? colonyIntelligenceEnhanced.queues.priority
      : colonyIntelligenceEnhanced.queues[agent.type] || colonyIntelligenceEnhanced.queues.worker;
    
    const job = await queue.add({
      agentId: agent._id,
      task,
      input,
      userId: req.user.id
    });
    
    res.json({
      jobId: job.id,
      agentId: agent._id,
      status: 'queued'
    });
  } catch (error) {
    logger.error('Failed to execute agent task:', error);
    res.status(500).json({ error: error.message });
  }
});

// Send message between agents
router.post('/agents/:fromAgentId/message', auth, async (req, res) => {
  try {
    const { toAgentId, message, protocol = 'direct' } = req.body;
    
    const result = await colonyIntelligenceEnhanced.sendAgentMessage(
      req.params.fromAgentId,
      toAgentId,
      message,
      protocol
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Failed to send agent message:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get agent recommendations
router.post('/agents/recommend', auth, async (req, res) => {
  try {
    const recommendations = await colonyIntelligenceEnhanced.recommendAgents(req.body);
    res.json({ recommendations });
  } catch (error) {
    logger.error('Failed to get agent recommendations:', error);
    res.status(500).json({ error: error.message });
  }
});

// Workflow Execution Routes

// Execute workflow
router.post('/workflows/:workflowId/execute', auth, async (req, res) => {
  try {
    const result = await workflowExecutionEngine.executeWorkflow(
      req.params.workflowId,
      req.user.id,
      req.body.inputs || {},
      req.body.options || {}
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Failed to execute workflow:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get workflow execution history
router.get('/workflows/:workflowId/history', auth, async (req, res) => {
  try {
    const history = await workflowExecutionEngine.getWorkflowHistory(
      req.params.workflowId,
      req.query
    );
    
    res.json(history);
  } catch (error) {
    logger.error('Failed to get workflow history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get workflow metrics
router.get('/workflows/:workflowId/metrics', auth, async (req, res) => {
  try {
    const metrics = await workflowExecutionEngine.getWorkflowMetrics(
      req.params.workflowId
    );
    
    res.json(metrics);
  } catch (error) {
    logger.error('Failed to get workflow metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Validate workflow
router.post('/workflows/validate', auth, async (req, res) => {
  try {
    const validation = await workflowExecutionEngine.validateWorkflow(req.body);
    res.json(validation);
  } catch (error) {
    logger.error('Failed to validate workflow:', error);
    res.status(500).json({ error: error.message });
  }
});

// Schedule workflow
router.post('/workflows/:workflowId/schedule', auth, async (req, res) => {
  try {
    const schedule = await workflowExecutionEngine.scheduleWorkflow(
      req.params.workflowId,
      req.user.id,
      req.body.schedule,
      req.body.inputs
    );
    
    res.json({ success: true, schedule });
  } catch (error) {
    logger.error('Failed to schedule workflow:', error);
    res.status(500).json({ error: error.message });
  }
});

// Marketplace Routes

// Search marketplace agents
router.get('/marketplace/search', async (req, res) => {
  try {
    const results = await agentMarketplaceService.searchAgents(req.query);
    res.json(results);
  } catch (error) {
    logger.error('Failed to search marketplace:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get featured agents
router.get('/marketplace/featured', async (req, res) => {
  try {
    const featured = await agentMarketplaceService.getFeaturedAgents();
    res.json({ agents: featured });
  } catch (error) {
    logger.error('Failed to get featured agents:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get trending agents
router.get('/marketplace/trending', async (req, res) => {
  try {
    const trending = await agentMarketplaceService.getTrendingAgents();
    res.json({ agents: trending });
  } catch (error) {
    logger.error('Failed to get trending agents:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get agent collections
router.get('/marketplace/collections', async (req, res) => {
  try {
    const collections = await agentMarketplaceService.getCollections();
    res.json(collections);
  } catch (error) {
    logger.error('Failed to get collections:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get agent details
router.get('/marketplace/agents/:agentId', async (req, res) => {
  try {
    const details = await agentMarketplaceService.getAgentDetails(req.params.agentId);
    res.json(details);
  } catch (error) {
    logger.error('Failed to get agent details:', error);
    res.status(500).json({ error: error.message });
  }
});

// Install agent
router.post('/marketplace/agents/:agentId/install', auth, async (req, res) => {
  try {
    const installedAgent = await agentMarketplaceService.installAgent(
      req.params.agentId,
      req.user.id,
      req.body.paymentInfo
    );
    
    res.json({
      success: true,
      agent: installedAgent
    });
  } catch (error) {
    logger.error('Failed to install agent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Publish agent to marketplace
router.post('/marketplace/publish', auth, async (req, res) => {
  try {
    const marketplaceAgent = await agentMarketplaceService.publishAgent(
      req.body.agentId,
      req.user.id,
      req.body.publishInfo
    );
    
    res.json({
      success: true,
      agent: marketplaceAgent
    });
  } catch (error) {
    logger.error('Failed to publish agent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update marketplace agent
router.put('/marketplace/agents/:agentId', auth, async (req, res) => {
  try {
    const updatedAgent = await agentMarketplaceService.updateMarketplaceAgent(
      req.params.agentId,
      req.user.id,
      req.body
    );
    
    res.json({
      success: true,
      agent: updatedAgent
    });
  } catch (error) {
    logger.error('Failed to update marketplace agent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Rate agent
router.post('/marketplace/agents/:agentId/rate', auth, async (req, res) => {
  try {
    const result = await agentMarketplaceService.rateAgent(
      req.params.agentId,
      req.user.id,
      req.body.rating,
      req.body.review
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Failed to rate agent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get creator analytics
router.get('/marketplace/creator/analytics', auth, async (req, res) => {
  try {
    const analytics = await agentMarketplaceService.getCreatorAnalytics(req.user.id);
    res.json(analytics);
  } catch (error) {
    logger.error('Failed to get creator analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get marketplace stats
router.get('/marketplace/stats', async (req, res) => {
  try {
    const stats = agentMarketplaceService.getMarketplaceStats();
    res.json(stats);
  } catch (error) {
    logger.error('Failed to get marketplace stats:', error);
    res.status(500).json({ error: error.message });
  }
});

// System Metrics Routes

// Get colony metrics
router.get('/metrics/colony', auth, async (req, res) => {
  try {
    const metrics = colonyIntelligenceEnhanced.metrics;
    res.json(metrics);
  } catch (error) {
    logger.error('Failed to get colony metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get workflow engine metrics
router.get('/metrics/workflows', auth, async (req, res) => {
  try {
    const metrics = workflowExecutionEngine.getEngineMetrics();
    res.json(metrics);
  } catch (error) {
    logger.error('Failed to get workflow metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get queue status
router.get('/metrics/queues', auth, async (req, res) => {
  try {
    const queueStatus = {};
    
    for (const [name, queue] of Object.entries(colonyIntelligenceEnhanced.queues)) {
      const [waiting, active, completed, failed] = await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount()
      ]);
      
      queueStatus[name] = {
        waiting,
        active,
        completed,
        failed
      };
    }
    
    res.json(queueStatus);
  } catch (error) {
    logger.error('Failed to get queue status:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;