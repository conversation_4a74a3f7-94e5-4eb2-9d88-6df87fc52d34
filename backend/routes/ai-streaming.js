import express from 'express'
import { v4 as uuidv4 } from 'uuid'
import { auth } from '../middleware/auth.js'
import { logger } from '../utils/logger.js'
import streamingAIService from '../services/streamingAIService.js'
import User from '../models/User.js'
import EmailSequence from '../models/EmailSequence.js'

const router = express.Router()

// Active SSE connections
const activeConnections = new Map()

// SSE endpoint for streaming AI generation
router.get('/stream/:streamId', auth, async (req, res) => {
  const { streamId } = req.params
  const userId = req.user._id

  // Set up SSE headers
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'X-Accel-Buffering': 'no' // Disable Nginx buffering
  })

  // Send initial connection message
  res.write(`data: ${JSON.stringify({ 
    type: 'connected', 
    streamId,
    timestamp: new Date() 
  })}\n\n`)

  // Store connection
  activeConnections.set(streamId, {
    res,
    userId,
    startTime: Date.now()
  })

  // Set up event listeners
  const updateHandler = (update) => {
    if (update.streamId === streamId) {
      res.write(`data: ${JSON.stringify(update)}\n\n`)
    }
  }

  streamingAIService.on('stream-update', updateHandler)

  // Handle client disconnect
  req.on('close', () => {
    streamingAIService.removeListener('stream-update', updateHandler)
    activeConnections.delete(streamId)
    streamingAIService.cancelStream(streamId)
    logger.info(`SSE connection closed for stream ${streamId}`)
  })

  // Keep connection alive
  const keepAlive = setInterval(() => {
    res.write(':ping\n\n')
  }, 30000)

  req.on('close', () => {
    clearInterval(keepAlive)
  })
})

// Start streaming generation
router.post('/generate-stream', auth, async (req, res) => {
  try {
    const { businessInfo, settings, provider = 'auto' } = req.body
    const userId = req.user._id

    // Check user permissions and usage
    const user = await User.findById(userId)
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    // Check usage limits
    const canGenerate = await user.canGenerateSequence()
    if (!canGenerate.allowed) {
      return res.status(403).json({
        success: false,
        message: canGenerate.reason,
        usage: canGenerate.usage
      })
    }

    // Create stream ID
    const streamId = uuidv4()

    // Start generation in background
    generateInBackground(streamId, userId, businessInfo, settings, provider)

    // Return stream info immediately
    res.json({
      success: true,
      data: {
        streamId,
        streamUrl: `/api/ai/stream/${streamId}`,
        provider: provider
      }
    })

  } catch (error) {
    logger.error('Stream generation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to start generation'
    })
  }
})

// Background generation function
async function generateInBackground(streamId, userId, businessInfo, settings, provider) {
  try {
    // Generate sequence with streaming
    const result = await streamingAIService.generateStreamingSequence(
      streamId,
      businessInfo,
      settings,
      provider
    )

    // Save the generated sequence
    const sequence = new EmailSequence({
      userId,
      name: `${businessInfo.businessName} - ${settings.sequenceType} Sequence`,
      description: `AI-generated ${settings.sequenceLength}-email ${settings.sequenceType} sequence`,
      businessInfo,
      settings,
      emails: result.sequence,
      metadata: {
        ...result.metadata,
        streamId,
        generatedVia: 'streaming'
      },
      status: 'draft'
    })

    await sequence.save()

    // Update user usage
    const user = await User.findById(userId)
    await user.incrementSequenceCount()

    // Send final success event
    streamingAIService.emit('stream-update', {
      streamId,
      type: 'saved',
      data: {
        sequenceId: sequence._id,
        message: 'Sequence saved successfully'
      }
    })

    logger.info(`Streaming generation completed for user ${userId}, sequence ${sequence._id}`)

  } catch (error) {
    logger.error('Background generation error:', error)
    
    // Send error event
    streamingAIService.emit('stream-update', {
      streamId,
      type: 'error',
      data: {
        status: 'error',
        message: error.message || 'Generation failed'
      }
    })
  }
}

// Cancel streaming generation
router.post('/cancel-stream/:streamId', auth, async (req, res) => {
  try {
    const { streamId } = req.params
    const connection = activeConnections.get(streamId)

    if (!connection || connection.userId.toString() !== req.user._id.toString()) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found or unauthorized'
      })
    }

    // Cancel the stream
    const cancelled = streamingAIService.cancelStream(streamId)

    if (cancelled) {
      res.json({
        success: true,
        message: 'Stream cancelled successfully'
      })
    } else {
      res.status(404).json({
        success: false,
        message: 'Stream not found'
      })
    }

  } catch (error) {
    logger.error('Cancel stream error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to cancel stream'
    })
  }
})

// Get active streams for user
router.get('/active-streams', auth, async (req, res) => {
  try {
    const userId = req.user._id.toString()
    const userStreams = []

    for (const [streamId, connection] of activeConnections.entries()) {
      if (connection.userId.toString() === userId) {
        userStreams.push({
          streamId,
          startTime: connection.startTime,
          duration: Date.now() - connection.startTime
        })
      }
    }

    res.json({
      success: true,
      data: {
        streams: userStreams,
        count: userStreams.length
      }
    })

  } catch (error) {
    logger.error('Get active streams error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get active streams'
    })
  }
})

// Test streaming endpoint
router.get('/test-stream', auth, async (req, res) => {
  // Set up SSE
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive'
  })

  // Send test events
  let progress = 0
  const interval = setInterval(() => {
    progress += 10
    
    res.write(`data: ${JSON.stringify({
      type: 'progress',
      progress,
      message: `Testing... ${progress}%`
    })}\n\n`)

    if (progress >= 100) {
      clearInterval(interval)
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        message: 'Test completed!'
      })}\n\n`)
      res.end()
    }
  }, 500)

  req.on('close', () => {
    clearInterval(interval)
  })
})

export default router