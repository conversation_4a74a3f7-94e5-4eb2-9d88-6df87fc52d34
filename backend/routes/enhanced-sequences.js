import express from 'express';
import EmailSequence from '../models/EmailSequence.js';
import { auth } from '../middleware/auth.js';
import aiService from '../services/aiService.js';
// import usageService from '../services/usageService.js';

const router = express.Router();

// Enhanced sequence generation with professional optimization
router.post('/generate-enhanced', auth, async (req, res) => {
  try {
    const { industry, audience, goal, tone, length, aiModel, customName } = req.body;
    
    // Validate required fields
    if (!industry || !audience || !goal || !tone || !length || !aiModel) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['industry', 'audience', 'goal', 'tone', 'length', 'aiModel']
      });
    }

    // Check usage limits
    const canGenerate = await usageService.canGenerateSequence(req.user.id);
    if (!canGenerate) {
      return res.status(403).json({
        error: 'Usage limit exceeded. Please upgrade your plan or enable overage billing.',
        code: 'USAGE_LIMIT_EXCEEDED'
      });
    }

    // Create enhanced AI prompt with industry-specific context
    const prompt = createEnhancedPrompt({
      industry,
      audience,
      goal,
      tone,
      length
    });

    // Generate sequence with AI
    const startTime = Date.now();
    const content = await aiService.generateSequence(prompt, aiModel);
    const generationTime = Date.now() - startTime;

    // Record usage
    await usageService.recordSequenceGeneration(req.user.id);

    // Create sequence title
    const title = customName || `AI Generated: ${capitalizeFirst(industry)} for ${audience.substring(0, 50)}`;

    // Create enhanced sequence record
    const sequence = new EmailSequence({
      userId: req.user.id,
      title,
      description: `AI-generated ${length}-email sequence for ${goal} using ${tone} tone`,
      industry,
      audience,
      goal,
      tone,
      length: parseInt(length),
      content: {
        generated_content: content,
        ai_model: aiModel,
        prompt,
        generation_time: generationTime,
        generated_at: new Date().toISOString(),
        version: '2.0',
        enhanced_features: [
          'Professional formatting',
          'Industry optimization',
          'Tone consistency',
          'Conversion optimization'
        ]
      },
      status: 'active'
    });

    await sequence.save();

    res.status(201).json({
      sequence,
      generation_time_ms: generationTime,
      performance: {
        backend: 'Node.js Enhanced',
        speed_improvement: 'Professional optimization',
        ai_model: aiModel
      },
      message: 'Enhanced sequence generated successfully with professional optimization'
    });

  } catch (error) {
    console.error('Enhanced sequence generation error:', error);
    res.status(500).json({
      error: 'Failed to generate enhanced sequence',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get sequence analytics
router.get('/analytics', auth, async (req, res) => {
  try {
    const sequences = await EmailSequence.find({ userId: req.user.id });
    
    const analytics = calculateAnalytics(sequences);
    
    res.json({
      analytics,
      generated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch analytics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Helper function to create enhanced AI prompt
function createEnhancedPrompt({ industry, audience, goal, tone, length }) {
  const industryContext = getIndustryContext(industry);
  const toneGuidelines = getToneGuidelines(tone);
  
  return `
You are an expert email marketing strategist specializing in ${capitalizeFirst(industry)}. Create a professional ${length}-email sequence with the following specifications:

INDUSTRY: ${capitalizeFirst(industry)}
${industryContext}

TARGET AUDIENCE: ${audience}

PRIMARY GOAL: ${goal}

TONE & STYLE: ${capitalizeFirst(tone)}
${toneGuidelines}

SEQUENCE REQUIREMENTS:
- Create exactly ${length} emails in the sequence
- Each email should be 150-300 words
- Include compelling subject lines for each email
- Ensure logical flow and progression
- Include clear call-to-actions
- Maintain consistency with the ${tone} tone throughout

FORMAT YOUR RESPONSE AS:
Email 1:
Subject: [Subject Line]
Content: [Email Content]

Email 2:
Subject: [Subject Line] 
Content: [Email Content]

[Continue for all ${length} emails]

Focus on professional quality, industry-specific insights, and conversion optimization.`;
}

// Industry-specific context
function getIndustryContext(industry) {
  const contexts = {
    saas: 'Focus on trial conversions, feature benefits, onboarding best practices, and subscription value propositions.',
    ecommerce: 'Emphasize product benefits, customer reviews, limited-time offers, and shopping cart recovery.',
    consulting: 'Highlight expertise, case studies, trust-building, and consultation booking.',
    education: 'Focus on learning outcomes, course benefits, student success stories, and enrollment drivers.',
    healthcare: 'Emphasize patient care, wellness benefits, safety, and appointment scheduling.',
    finance: 'Focus on security, ROI, financial benefits, and trust-building.',
    marketing: 'Highlight results, case studies, ROI metrics, and service differentiation.'
  };
  
  return contexts[industry] || 'Focus on value proposition, customer benefits, and clear call-to-actions.';
}

// Tone guidelines
function getToneGuidelines(tone) {
  const guidelines = {
    professional: 'Use formal language, industry terminology, and maintain a business-focused approach.',
    friendly: 'Use warm, approachable language with personal touches while remaining professional.',
    casual: 'Use conversational language, contractions, and a relaxed approach.',
    urgent: 'Use action-oriented language, time-sensitive phrases, and direct calls-to-action.',
    educational: 'Use informative language, helpful tips, and focus on teaching and guiding.'
  };
  
  return guidelines[tone] || 'Maintain a balanced, professional tone appropriate for business communication.';
}

// Calculate analytics
function calculateAnalytics(sequences) {
  const analytics = {
    total_sequences: sequences.length,
    by_industry: {},
    by_tone: {},
    by_ai_model: {},
    average_length: 0,
    recent_activity: [],
    popular_prompts: []
  };

  let totalLength = 0;
  const activityMap = {};

  sequences.forEach(seq => {
    // Count by industry
    analytics.by_industry[seq.industry] = (analytics.by_industry[seq.industry] || 0) + 1;
    
    // Count by tone
    analytics.by_tone[seq.tone] = (analytics.by_tone[seq.tone] || 0) + 1;
    
    // Count by AI model
    const aiModel = seq.content?.ai_model || 'gpt-3.5-turbo';
    analytics.by_ai_model[aiModel] = (analytics.by_ai_model[aiModel] || 0) + 1;
    
    // Calculate average length
    totalLength += seq.length || 5;
    
    // Track daily activity
    const date = seq.createdAt.toISOString().split('T')[0];
    activityMap[date] = (activityMap[date] || 0) + 1;
  });

  // Calculate average length
  if (sequences.length > 0) {
    analytics.average_length = totalLength / sequences.length;
  }

  // Convert activity map to array
  analytics.recent_activity = Object.entries(activityMap)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => new Date(b.date) - new Date(a.date))
    .slice(0, 30);

  return analytics;
}

// Helper function to capitalize first letter
function capitalizeFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export default router;