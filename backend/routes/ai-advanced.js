import express from 'express'
import { auth } from '../middleware/auth.js'
import aiService from '../services/aiService.js'
import { logger } from '../utils/logger.js'
import EmailSequence from '../models/EmailSequence.js'

const router = express.Router()

// Multi-language email generation
router.post('/translate', auth, async (req, res) => {
  try {
    const { emailId, targetLanguages } = req.body
    
    // Get email content from database
    const email = await EmailSequence.findOne({
      _id: emailId,
      user: req.user.userId
    })
    
    if (!email) {
      return res.status(404).json({ error: 'Email not found' })
    }
    
    const translations = await aiService.generateMultilingualEmail(
      { subject: email.subject, body: email.body },
      targetLanguages
    )
    
    res.json({ translations })
  } catch (error) {
    logger.error('Translation error:', error)
    res.status(500).json({ error: 'Translation failed' })
  }
})

// Predict email performance
router.post('/predict-performance', auth, async (req, res) => {
  try {
    const { subject, body } = req.body
    
    if (!subject || !body) {
      return res.status(400).json({ error: 'Subject and body required' })
    }
    
    const prediction = await aiService.predictEmailPerformance({ subject, body })
    
    res.json({ prediction })
  } catch (error) {
    logger.error('Performance prediction error:', error)
    res.status(500).json({ error: 'Prediction failed' })
  }
})

// Optimize subject line
router.post('/optimize-subject', auth, async (req, res) => {
  try {
    const { originalSubject, context } = req.body
    
    if (!originalSubject) {
      return res.status(400).json({ error: 'Subject line required' })
    }
    
    const optimization = await aiService.optimizeSubjectLine(originalSubject, context || {})
    
    res.json({ optimization })
  } catch (error) {
    logger.error('Subject optimization error:', error)
    res.status(500).json({ error: 'Optimization failed' })
  }
})

// Analyze competitor email
router.post('/analyze-competitor', auth, async (req, res) => {
  try {
    const { competitorEmail, yourBrand } = req.body
    
    if (!competitorEmail || !competitorEmail.subject || !competitorEmail.body) {
      return res.status(400).json({ error: 'Competitor email subject and body required' })
    }
    
    const analysis = await aiService.analyzeCompetitorEmail(competitorEmail, yourBrand || {})
    
    res.json({ analysis })
  } catch (error) {
    logger.error('Competitor analysis error:', error)
    res.status(500).json({ error: 'Analysis failed' })
  }
})

export default router