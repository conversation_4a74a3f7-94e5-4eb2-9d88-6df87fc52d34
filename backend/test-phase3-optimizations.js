#!/usr/bin/env node

/**
 * Phase 3 Optimizations Test Script
 * Tests all optimization modules without requiring database connection
 */

import { logger } from './utils/logger.js'

async function testOptimizations() {
  logger.info('🧪 Testing Phase 3 System-Level Optimizations...')
  
  const results = {
    systemOptimizer: false,
    networkOptimizer: false,
    cpuOptimizer: false,
    cacheOptimizer: false,
    performanceMonitor: false
  }
  
  // Test System Optimizer
  try {
    const { default: systemOptimizer } = await import('./services/systemOptimizer.js')
    const stats = systemOptimizer.getPerformanceStats()
    results.systemOptimizer = stats && typeof stats === 'object'
    logger.info('✅ System Optimizer: Working')
  } catch (error) {
    logger.error('❌ System Optimizer: Failed', error.message)
  }
  
  // Test Network Optimizer  
  try {
    const { default: networkOptimizer } = await import('./services/networkOptimizer.js')
    const stats = networkOptimizer.getPerformanceStats()
    results.networkOptimizer = stats && typeof stats === 'object'
    logger.info('✅ Network Optimizer: Working')
  } catch (error) {
    logger.error('❌ Network Optimizer: Failed', error.message)
  }
  
  // Test CPU Optimizer
  try {
    const { default: cpuOptimizer } = await import('./services/cpuOptimizer.js')
    await new Promise(resolve => setTimeout(resolve, 1000)) // Allow initialization
    const stats = cpuOptimizer.getPerformanceStats()
    results.cpuOptimizer = stats && typeof stats === 'object'
    logger.info('✅ CPU Optimizer: Working')
  } catch (error) {
    logger.error('❌ CPU Optimizer: Failed', error.message)
  }
  
  // Test Cache Optimizer
  try {
    const { default: hyperOptimizedCacheService } = await import('./services/hyperOptimizedCacheService.js')
    const stats = hyperOptimizedCacheService.getAdvancedStats()
    results.cacheOptimizer = stats && typeof stats === 'object'
    logger.info('✅ Cache Optimizer: Working')
  } catch (error) {
    logger.error('❌ Cache Optimizer: Failed', error.message)
  }
  
  // Test Performance Monitor
  try {
    const { default: performanceMonitor } = await import('./services/performanceMonitor.js')
    await new Promise(resolve => setTimeout(resolve, 2000)) // Allow metrics collection
    const summary = performanceMonitor.getPerformanceSummary()
    results.performanceMonitor = summary && typeof summary === 'object'
    logger.info('✅ Performance Monitor: Working')
  } catch (error) {
    logger.error('❌ Performance Monitor: Failed', error.message)
  }
  
  // Summary
  const working = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  logger.info('🎯 PHASE 3 OPTIMIZATION TEST RESULTS:')
  logger.info('=' .repeat(50))
  logger.info(`Working Components: ${working}/${total}`)
  logger.info(`Success Rate: ${((working/total) * 100).toFixed(1)}%`)
  logger.info('')
  
  Object.entries(results).forEach(([component, working]) => {
    logger.info(`${working ? '✅' : '❌'} ${component}`)
  })
  
  if (working === total) {
    logger.info('')
    logger.info('🚀 ALL PHASE 3 OPTIMIZATIONS WORKING CORRECTLY!')
    logger.info('✅ System is ready for production deployment')
  } else {
    logger.warn('')
    logger.warn('⚠️ Some optimizations need attention')
  }
  
  return results
}

// Run test
testOptimizations()
  .then(results => {
    const allWorking = Object.values(results).every(Boolean)
    process.exit(allWorking ? 0 : 1)
  })
  .catch(error => {
    logger.error('Test failed:', error)
    process.exit(1)
  })