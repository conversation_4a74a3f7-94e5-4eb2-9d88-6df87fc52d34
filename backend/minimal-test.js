console.log('Testing basic imports...')

try {
  console.log('1. Testing dotenv...')
  import('dotenv').then(dotenv => {
    dotenv.config()
    console.log('✅ dotenv works')
    
    console.log('2. Testing mongoose...')
    return import('mongoose')
  }).then(mongoose => {
    console.log('✅ mongoose works')
    
    console.log('3. Testing express...')
    return import('express')
  }).then(express => {
    console.log('✅ express works')
    
    console.log('4. Testing auth routes...')
    return import('./routes/auth.js')
  }).then(() => {
    console.log('✅ auth routes work')
    
    console.log('5. Testing sequence routes...')
    return import('./routes/sequences.js')
  }).then(() => {
    console.log('✅ sequence routes work')
    
    console.log('6. Testing middleware...')
    return import('./middleware/errorHandler.js')
  }).then(() => {
    console.log('✅ All imports successful!')
  }).catch(error => {
    console.error('❌ Import failed:', error)
  })
} catch (error) {
  console.error('❌ Basic import test failed:', error)
}