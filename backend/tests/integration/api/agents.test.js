import request from 'supertest';
import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { Redis } from 'ioredis-mock';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import app from '../../../server.js';
import User from '../../../models/User.js';
import Agent from '../../../models/Agent.js';
import AgentExecution from '../../../models/AgentExecution.js';

describe('Agents API Integration Tests', () => {
  let mongoServer;
  let authToken;
  let testUser;
  let redisClient;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Setup Redis mock
    redisClient = new Redis();
    app.locals.redis = redisClient;

    // Create test user
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      subscription: {
        plan: 'professional',
        status: 'active',
        features: {
          maxAgentExecutions: 1000,
          advancedAgents: true,
        },
      },
    });

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser._id, email: testUser.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    // Seed test agents
    await Agent.create([
      {
        id: 'email-sequence-generator',
        name: 'Email Sequence Generator',
        category: 'email-marketing',
        description: 'Generate AI-powered email sequences',
        version: '1.0.0',
        requiredInputs: ['productName', 'targetAudience', 'sequenceGoal'],
        requiredPlan: 'starter',
      },
      {
        id: 'advanced-segmentation',
        name: 'Advanced Audience Segmentation',
        category: 'analytics',
        description: 'AI-driven audience segmentation',
        version: '1.0.0',
        requiredInputs: ['criteria', 'dataSource'],
        requiredPlan: 'professional',
      },
    ]);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
    await redisClient.quit();
  });

  beforeEach(async () => {
    // Clear executions between tests
    await AgentExecution.deleteMany({});
    await redisClient.flushall();
  });

  describe('GET /api/agents', () => {
    it('should list all available agents', async () => {
      const response = await request(app)
        .get('/api/agents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('email-marketing');
      expect(response.body.data).toHaveProperty('analytics');
      expect(response.body.data['email-marketing']).toHaveLength(1);
      expect(response.body.data['analytics']).toHaveLength(1);
    });

    it('should filter agents by category', async () => {
      const response = await request(app)
        .get('/api/agents?category=email-marketing')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('email-marketing');
      expect(Object.keys(response.body.data)).toHaveLength(1);
    });

    it('should filter agents by user subscription plan', async () => {
      // Create starter user
      const starterUser = await User.create({
        email: '<EMAIL>',
        password: 'TestPassword123!',
        subscription: { plan: 'starter', status: 'active' },
      });

      const starterToken = jwt.sign(
        { userId: starterUser._id },
        process.env.JWT_SECRET || 'test-secret'
      );

      const response = await request(app)
        .get('/api/agents')
        .set('Authorization', `Bearer ${starterToken}`)
        .expect(200);

      // Should not include professional-only agents
      const agents = Object.values(response.body.data).flat();
      const advancedAgent = agents.find(a => a.id === 'advanced-segmentation');
      expect(advancedAgent).toBeUndefined();
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/agents')
        .expect(401);
    });
  });

  describe('GET /api/agents/:id', () => {
    it('should get agent details', async () => {
      const response = await request(app)
        .get('/api/agents/email-sequence-generator')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: 'email-sequence-generator',
        name: 'Email Sequence Generator',
        category: 'email-marketing',
        requiredInputs: ['productName', 'targetAudience', 'sequenceGoal'],
      });
    });

    it('should return 404 for non-existent agent', async () => {
      await request(app)
        .get('/api/agents/non-existent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should include execution statistics', async () => {
      // Create some executions
      await AgentExecution.create([
        {
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'completed',
          executionTime: 2500,
        },
        {
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'completed',
          executionTime: 3000,
        },
        {
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'failed',
          error: 'Test error',
        },
      ]);

      const response = await request(app)
        .get('/api/agents/email-sequence-generator?includeStats=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.statistics).toMatchObject({
        totalExecutions: 3,
        successfulExecutions: 2,
        failedExecutions: 1,
        averageExecutionTime: 2750,
        successRate: expect.closeTo(0.667, 2),
      });
    });
  });

  describe('POST /api/agents/:id/execute', () => {
    it('should execute an agent successfully', async () => {
      const inputs = {
        productName: 'NeuroColony Pro',
        targetAudience: 'Marketing professionals',
        sequenceGoal: 'Product launch campaign',
      };

      const response = await request(app)
        .post('/api/agents/email-sequence-generator/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ inputs })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        executionId: expect.any(String),
        status: 'running',
        agentId: 'email-sequence-generator',
      });

      // Verify execution was created
      const execution = await AgentExecution.findOne({
        executionId: response.body.data.executionId,
      });
      expect(execution).toBeTruthy();
      expect(execution.userId.toString()).toBe(testUser._id.toString());
    });

    it('should validate required inputs', async () => {
      const invalidInputs = {
        productName: 'Test', // Missing required fields
      };

      const response = await request(app)
        .post('/api/agents/email-sequence-generator/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ inputs: invalidInputs })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required inputs');
    });

    it('should enforce rate limits', async () => {
      // Set rate limit in Redis
      await redisClient.set(
        `rate_limit:agent_execution:${testUser._id}`,
        '100',
        'EX',
        3600
      );

      const response = await request(app)
        .post('/api/agents/email-sequence-generator/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ inputs: { productName: 'Test' } })
        .expect(429);

      expect(response.body.error).toContain('Rate limit exceeded');
    });

    it('should enforce subscription limits', async () => {
      // Update user to have no executions left
      testUser.subscription.usage = {
        agentExecutions: 1000,
        monthlyLimit: 1000,
      };
      await testUser.save();

      const response = await request(app)
        .post('/api/agents/email-sequence-generator/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          inputs: {
            productName: 'Test',
            targetAudience: 'Test',
            sequenceGoal: 'Test',
          },
        })
        .expect(403);

      expect(response.body.error).toContain('execution limit');
    });

    it('should handle async execution with callback', async () => {
      const callbackUrl = 'https://webhook.site/test-callback';
      const inputs = {
        productName: 'Test',
        targetAudience: 'Test',
        sequenceGoal: 'Test',
      };

      const response = await request(app)
        .post('/api/agents/email-sequence-generator/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ inputs, callbackUrl })
        .expect(200);

      expect(response.body.data.callbackUrl).toBe(callbackUrl);
      
      // Verify callback configuration was stored
      const execution = await AgentExecution.findOne({
        executionId: response.body.data.executionId,
      });
      expect(execution.callbackUrl).toBe(callbackUrl);
    });
  });

  describe('GET /api/agents/executions/running', () => {
    it('should list running executions for user', async () => {
      // Create test executions
      await AgentExecution.create([
        {
          executionId: 'exec-1',
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'running',
          progress: 50,
        },
        {
          executionId: 'exec-2',
          agentId: 'advanced-segmentation',
          userId: testUser._id,
          status: 'running',
          progress: 75,
        },
        {
          executionId: 'exec-3',
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'completed',
        },
      ]);

      const response = await request(app)
        .get('/api/agents/executions/running')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveLength(2);
      expect(response.body.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            executionId: 'exec-1',
            status: 'running',
            progress: 50,
          }),
          expect.objectContaining({
            executionId: 'exec-2',
            status: 'running',
            progress: 75,
          }),
        ])
      );
    });

    it('should not show other users executions', async () => {
      // Create another user
      const otherUser = await User.create({
        email: '<EMAIL>',
        password: 'TestPassword123!',
      });

      // Create executions for both users
      await AgentExecution.create([
        {
          executionId: 'user1-exec',
          agentId: 'email-sequence-generator',
          userId: testUser._id,
          status: 'running',
        },
        {
          executionId: 'user2-exec',
          agentId: 'email-sequence-generator',
          userId: otherUser._id,
          status: 'running',
        },
      ]);

      const response = await request(app)
        .get('/api/agents/executions/running')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].executionId).toBe('user1-exec');
    });
  });

  describe('POST /api/agents/quick/:action', () => {
    it('should execute quick email sequence generation', async () => {
      const response = await request(app)
        .post('/api/agents/quick/email-sequence')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productName: 'Quick Test Product',
          targetAudience: 'Developers',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('executionId');
      expect(response.body.data.agentId).toBe('email-sequence-generator');
    });

    it('should execute quick subject optimization', async () => {
      const response = await request(app)
        .post('/api/agents/quick/optimize-subject')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          subject: 'Check out our new product',
          context: 'product launch',
        })
        .expect(200);

      expect(response.body.data.agentId).toBe('subject-line-optimizer');
    });

    it('should execute quick audience segmentation', async () => {
      const response = await request(app)
        .post('/api/agents/quick/segment-audience')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          audienceData: [
            { email: '<EMAIL>', engagement: 'high' },
            { email: '<EMAIL>', engagement: 'low' },
          ],
          criteria: 'engagement-based',
        })
        .expect(200);

      expect(response.body.data.agentId).toBe('audience-segmentation');
    });
  });

  describe('Agent Chain Execution', () => {
    it('should execute agent chain successfully', async () => {
      const chainConfig = {
        name: 'Complete Email Campaign',
        agents: [
          {
            id: 'audience-segmentation',
            inputs: {
              criteria: 'behavior-based',
              dataSource: 'user-engagement',
            },
          },
          {
            id: 'email-sequence-generator',
            inputs: {
              productName: 'Test Product',
              targetAudience: '${agents[0].output.segments[0].name}',
              sequenceGoal: 'nurture-campaign',
            },
          },
        ],
      };

      const response = await request(app)
        .post('/api/agents/chain/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .send(chainConfig)
        .expect(200);

      expect(response.body.data).toMatchObject({
        chainId: expect.any(String),
        status: 'running',
        agents: expect.arrayContaining([
          expect.objectContaining({
            agentId: 'audience-segmentation',
            status: 'pending',
          }),
          expect.objectContaining({
            agentId: 'email-sequence-generator',
            status: 'pending',
          }),
        ]),
      });
    });
  });

  describe('WebSocket Agent Updates', () => {
    it('should send real-time execution updates via WebSocket', async (done) => {
      const io = require('socket.io-client');
      const socket = io(`http://localhost:${process.env.PORT || 5000}`, {
        auth: { token: authToken },
      });

      socket.on('connect', async () => {
        // Execute an agent
        const response = await request(app)
          .post('/api/agents/email-sequence-generator/execute')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            inputs: {
              productName: 'WebSocket Test',
              targetAudience: 'Test',
              sequenceGoal: 'Test',
            },
          });

        const executionId = response.body.data.executionId;

        // Subscribe to execution updates
        socket.emit('subscribe:execution', executionId);
      });

      socket.on('execution:progress', (data) => {
        expect(data).toMatchObject({
          executionId: expect.any(String),
          progress: expect.any(Number),
          status: expect.any(String),
        });
      });

      socket.on('execution:completed', (data) => {
        expect(data).toMatchObject({
          executionId: expect.any(String),
          status: 'completed',
          result: expect.any(Object),
        });
        socket.close();
        done();
      });
    });
  });
});