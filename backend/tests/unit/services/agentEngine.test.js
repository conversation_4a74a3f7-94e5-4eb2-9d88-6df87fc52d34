import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { AgentEngine } from '../../../services/agentEngine.js';
import { aiService } from '../../../services/aiService.js';
import { Redis } from 'ioredis';
import { EventEmitter } from 'events';

// Mock dependencies
jest.mock('../../../services/aiService.js');
jest.mock('ioredis');

describe('AgentEngine Service', () => {
  let agentEngine;
  let mockRedis;
  let mockEventEmitter;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock Redis
    mockRedis = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      expire: jest.fn(),
      publish: jest.fn(),
      subscribe: jest.fn(),
      on: jest.fn(),
    };
    Redis.mockImplementation(() => mockRedis);
    
    // Mock EventEmitter
    mockEventEmitter = new EventEmitter();
    
    // Initialize AgentEngine
    agentEngine = new AgentEngine({
      redis: mockRedis,
      eventEmitter: mockEventEmitter,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('executeAgent', () => {
    it('should execute an agent successfully', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = {
        productName: 'Test Product',
        targetAudience: 'Developers',
        sequenceGoal: 'Product launch',
      };
      const userId = 'user123';
      
      const expectedResult = {
        sequences: [
          {
            subject: 'Introducing Test Product',
            content: 'Email content here',
            timing: 'Day 1',
          },
        ],
      };
      
      aiService.generateEmailSequence.mockResolvedValue(expectedResult);
      
      // Act
      const result = await agentEngine.executeAgent(agentId, inputs, userId);
      
      // Assert
      expect(result).toMatchObject({
        agentId,
        status: 'completed',
        result: expectedResult,
        executionTime: expect.any(Number),
      });
      expect(aiService.generateEmailSequence).toHaveBeenCalledWith(inputs);
      expect(mockRedis.publish).toHaveBeenCalledWith(
        'agent:execution:completed',
        expect.stringContaining(agentId)
      );
    });

    it('should handle agent execution failure', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = { productName: 'Test' };
      const userId = 'user123';
      const error = new Error('AI service unavailable');
      
      aiService.generateEmailSequence.mockRejectedValue(error);
      
      // Act & Assert
      await expect(agentEngine.executeAgent(agentId, inputs, userId))
        .rejects.toThrow('AI service unavailable');
      
      expect(mockRedis.publish).toHaveBeenCalledWith(
        'agent:execution:failed',
        expect.stringContaining(agentId)
      );
    });

    it('should validate required inputs', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = {}; // Missing required fields
      const userId = 'user123';
      
      // Act & Assert
      await expect(agentEngine.executeAgent(agentId, inputs, userId))
        .rejects.toThrow('Missing required input');
    });

    it('should enforce rate limits', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = { productName: 'Test' };
      const userId = 'user123';
      
      // Mock rate limit exceeded
      mockRedis.get.mockResolvedValue('100'); // Current count
      
      // Act & Assert
      await expect(agentEngine.executeAgent(agentId, inputs, userId))
        .rejects.toThrow('Rate limit exceeded');
    });

    it('should cache agent results', async () => {
      // Arrange
      const agentId = 'subject-line-optimizer';
      const inputs = { subject: 'Test Subject' };
      const userId = 'user123';
      const cachedResult = {
        variations: ['Subject 1', 'Subject 2'],
        scores: [0.8, 0.9],
      };
      
      // Mock cache hit
      mockRedis.get.mockResolvedValue(JSON.stringify(cachedResult));
      
      // Act
      const result = await agentEngine.executeAgent(agentId, inputs, userId);
      
      // Assert
      expect(result.result).toEqual(cachedResult);
      expect(result.fromCache).toBe(true);
      expect(aiService.generateSubjectVariations).not.toHaveBeenCalled();
    });
  });

  describe('getAgentStatus', () => {
    it('should return agent execution status', async () => {
      // Arrange
      const executionId = 'exec123';
      const status = {
        id: executionId,
        status: 'running',
        progress: 75,
        startTime: Date.now(),
      };
      
      mockRedis.get.mockResolvedValue(JSON.stringify(status));
      
      // Act
      const result = await agentEngine.getAgentStatus(executionId);
      
      // Assert
      expect(result).toEqual(status);
      expect(mockRedis.get).toHaveBeenCalledWith(`agent:execution:${executionId}`);
    });

    it('should return null for non-existent execution', async () => {
      // Arrange
      const executionId = 'nonexistent';
      mockRedis.get.mockResolvedValue(null);
      
      // Act
      const result = await agentEngine.getAgentStatus(executionId);
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('cancelAgentExecution', () => {
    it('should cancel a running agent execution', async () => {
      // Arrange
      const executionId = 'exec123';
      const status = {
        id: executionId,
        status: 'running',
        agentId: 'email-sequence-generator',
      };
      
      mockRedis.get.mockResolvedValue(JSON.stringify(status));
      
      // Act
      const result = await agentEngine.cancelAgentExecution(executionId);
      
      // Assert
      expect(result).toBe(true);
      expect(mockRedis.set).toHaveBeenCalledWith(
        `agent:execution:${executionId}`,
        expect.stringContaining('"status":"cancelled"')
      );
      expect(mockRedis.publish).toHaveBeenCalledWith(
        'agent:execution:cancelled',
        expect.stringContaining(executionId)
      );
    });

    it('should not cancel completed executions', async () => {
      // Arrange
      const executionId = 'exec123';
      const status = {
        id: executionId,
        status: 'completed',
      };
      
      mockRedis.get.mockResolvedValue(JSON.stringify(status));
      
      // Act
      const result = await agentEngine.cancelAgentExecution(executionId);
      
      // Assert
      expect(result).toBe(false);
      expect(mockRedis.set).not.toHaveBeenCalled();
    });
  });

  describe('getAgentMetrics', () => {
    it('should return agent performance metrics', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const metrics = {
        totalExecutions: 150,
        successfulExecutions: 145,
        failedExecutions: 5,
        averageExecutionTime: 2500,
        successRate: 0.967,
      };
      
      mockRedis.get.mockResolvedValue(JSON.stringify(metrics));
      
      // Act
      const result = await agentEngine.getAgentMetrics(agentId);
      
      // Assert
      expect(result).toEqual(metrics);
      expect(mockRedis.get).toHaveBeenCalledWith(`agent:metrics:${agentId}`);
    });
  });

  describe('listAvailableAgents', () => {
    it('should return categorized list of agents', () => {
      // Act
      const agents = agentEngine.listAvailableAgents();
      
      // Assert
      expect(agents).toHaveProperty('email-marketing');
      expect(agents).toHaveProperty('analytics');
      expect(agents).toHaveProperty('optimization');
      expect(agents).toHaveProperty('automation');
      
      expect(agents['email-marketing']).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'email-sequence-generator',
            name: 'Email Sequence Generator',
          }),
        ])
      );
    });

    it('should filter agents by category', () => {
      // Act
      const agents = agentEngine.listAvailableAgents('email-marketing');
      
      // Assert
      expect(Object.keys(agents)).toEqual(['email-marketing']);
      expect(agents['email-marketing'].length).toBeGreaterThan(0);
    });
  });

  describe('Agent Chain Execution', () => {
    it('should execute agent chains sequentially', async () => {
      // Arrange
      const chainConfig = {
        agents: [
          {
            id: 'audience-segmentation',
            inputs: { criteria: 'engagement' },
          },
          {
            id: 'email-sequence-generator',
            inputs: { useSegmentation: true },
          },
          {
            id: 'send-time-optimizer',
            inputs: { timezone: 'UTC' },
          },
        ],
      };
      
      const userId = 'user123';
      
      // Mock sequential execution results
      const segmentationResult = { segments: ['high', 'medium', 'low'] };
      const sequenceResult = { sequences: [] };
      const timingResult = { optimalTimes: [] };
      
      aiService.performSegmentation.mockResolvedValue(segmentationResult);
      aiService.generateEmailSequence.mockResolvedValue(sequenceResult);
      aiService.optimizeSendTime.mockResolvedValue(timingResult);
      
      // Act
      const result = await agentEngine.executeAgentChain(chainConfig, userId);
      
      // Assert
      expect(result).toHaveLength(3);
      expect(result[0].result).toEqual(segmentationResult);
      expect(result[1].result).toEqual(sequenceResult);
      expect(result[2].result).toEqual(timingResult);
    });

    it('should pass outputs between chain steps', async () => {
      // Arrange
      const chainConfig = {
        agents: [
          {
            id: 'subject-line-optimizer',
            inputs: { subject: 'Original' },
          },
          {
            id: 'content-personalizer',
            inputs: { useOptimizedSubject: true },
            inputMapping: {
              subject: '$.previous.variations[0]',
            },
          },
        ],
      };
      
      const subjectResult = { variations: ['Optimized Subject'] };
      const contentResult = { personalizedContent: 'Content' };
      
      aiService.generateSubjectVariations.mockResolvedValue(subjectResult);
      aiService.personalizeContent.mockResolvedValue(contentResult);
      
      // Act
      await agentEngine.executeAgentChain(chainConfig, 'user123');
      
      // Assert
      expect(aiService.personalizeContent).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'Optimized Subject',
          useOptimizedSubject: true,
        })
      );
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should retry failed agent executions', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = { productName: 'Test' };
      const userId = 'user123';
      
      // Fail twice, then succeed
      aiService.generateEmailSequence
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({ sequences: [] });
      
      // Act
      const result = await agentEngine.executeAgent(agentId, inputs, userId, {
        maxRetries: 3,
        retryDelay: 100,
      });
      
      // Assert
      expect(result.status).toBe('completed');
      expect(aiService.generateEmailSequence).toHaveBeenCalledTimes(3);
    });

    it('should handle circuit breaker activation', async () => {
      // Arrange
      const agentId = 'email-sequence-generator';
      const inputs = { productName: 'Test' };
      const userId = 'user123';
      
      // Simulate multiple failures to trip circuit breaker
      for (let i = 0; i < 5; i++) {
        aiService.generateEmailSequence.mockRejectedValueOnce(
          new Error('Service unavailable')
        );
        try {
          await agentEngine.executeAgent(agentId, inputs, userId);
        } catch (e) {
          // Expected failures
        }
      }
      
      // Act & Assert
      await expect(agentEngine.executeAgent(agentId, inputs, userId))
        .rejects.toThrow('Circuit breaker is open');
    });
  });
});