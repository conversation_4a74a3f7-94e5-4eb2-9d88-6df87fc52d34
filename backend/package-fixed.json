{"name": "neurocolony-backend", "version": "2.0.0", "description": "NeuroColony Backend API with World-Class Developer Experience - Billion User Scale", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "start-cluster": "ENABLE_CLUSTER=true node cluster.js", "start-production": "NODE_ENV=production node cluster.js", "start-debug": "node debug-server.js", "start-enhanced": "node server-with-monitoring.js", "start-monitoring": "node monitoring/index.js", "dashboard": "node monitoring/monitoringDashboard.js", "validate": "node monitoring/systemValidator.js", "build": "echo 'Backend build complete'", "test": "vitest", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "vitest run tests/e2e", "test:contract": "npm run pact:test && npm run pact:publish", "test:chaos": "node tests/chaos/run-chaos-tests.js", "test:coverage": "vitest run --coverage", "test:performance": "node scripts/performance-profiler.js", "test:health": "node health-test.js", "test:monitoring": "node monitoring/test.js", "test:smoke": "npm run test:health && npm run test:monitoring", "pact:test": "vitest run tests/contract", "pact:publish": "pact-broker publish pacts --broker-base-url=$PACT_BROKER_URL", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "format": "prettier --write \"src/**/*.js\" \"tests/**/*.js\"", "format:check": "prettier --check \"src/**/*.js\" \"tests/**/*.js\"", "type-check": "echo 'Type checking (would run TypeScript compiler)'", "docs:generate": "node scripts/generate-docs.js", "docs:serve": "npx swagger-ui-serve docs/api/openapi.json", "docs:build": "npm run docs:generate && echo 'Documentation built'", "setup:dev": "node scripts/setup-dev-environment.js", "validate:setup": "node scripts/validate-setup.js", "profile": "node scripts/performance-profiler.js", "profile:start": "node scripts/performance-profiler.js start", "profile:analyze": "node scripts/performance-profiler.js analyze", "security:audit": "npm audit && echo 'Security audit complete'", "security:scan": "echo 'Security scan (would run security tools)'", "docker:build": "docker build -t neurocolony/backend .", "docker:run": "docker run -p 5000:5000 neurocolony/backend", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "k8s:deploy": "kubectl apply -f deployment/", "helm:install": "helm install neurocolony deployment/helm/neurocolony", "helm:upgrade": "helm upgrade neurocolony deployment/helm/neurocolony", "monitoring:start": "docker-compose -f docker-compose.monitoring.yml up -d", "monitoring:stop": "docker-compose -f docker-compose.monitoring.yml down", "db:migrate": "echo 'Database migration (would run migrations)'", "db:seed": "echo 'Database seeding (would run seeders)'", "cache:clear": "echo 'Cache clear (would clear Redis cache)'", "logs:view": "tail -f logs/combined.log", "logs:error": "tail -f logs/error.log", "health:check": "curl -f http://localhost:5000/health || exit 1", "wait-for-services": "node scripts/wait-for-services.js", "chaos:run": "node tests/chaos/run-chaos-tests.js", "chaos:schedule": "echo 'Chaos engineering scheduled'", "benchmark": "node scripts/benchmark.js", "load:test": "echo 'Load testing (would run k6 or artillery)'", "deploy:staging": "echo 'Deploy to staging'", "deploy:production": "echo 'Deploy to production'", "rollback": "echo 'Rollback deployment'", "backup": "echo 'Backup database'", "restore": "echo 'Restore database'", "deps:check": "npm outdated", "deps:audit": "npm audit", "deps:update": "npm update", "deps:fix": "npm audit fix"}, "dependencies": {"argon2": "^0.43.0", "axios": "^1.7.9", "compression": "^1.7.5", "connect-redis": "^9.0.0", "cors": "^2.8.5", "dotenv": "^16.6.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^7.2.0", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "mongoose": "^8.16.1", "nodemailer": "^6.10.1", "openai": "^4.104.0", "opossum": "^9.0.0", "rate-limit-redis": "^4.2.1", "redis": "^4.7.1", "socket.io": "^4.8.1", "stripe": "^14.25.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "@opentelemetry/sdk-node": "^0.202.0", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/semantic-conventions": "^1.34.0", "prom-client": "^15.1.3", "chalk": "^5.4.1", "inquirer": "^12.6.3"}, "devDependencies": {"nodemon": "^3.1.10", "vitest": "^2.1.8", "supertest": "^7.0.0", "@pact-foundation/pact": "^13.1.3", "fast-check": "^3.23.1", "testcontainers": "^11.1.1", "@testing-library/jest-dom": "^6.6.3", "msw": "^2.7.0", "eslint": "^9.17.0", "@typescript-eslint/parser": "^8.20.0", "@typescript-eslint/eslint-plugin": "^8.20.0", "prettier": "^3.4.2", "husky": "^9.1.7", "lint-staged": "^15.3.0", "concurrently": "^9.1.0", "@redocly/openapi-cli": "^1.25.15", "typedoc": "^0.27.6", "@microsoft/api-extractor": "^7.48.0", "@vitest/coverage-v8": "^2.1.8", "c8": "^10.1.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "keywords": ["neurocolony", "backend", "api", "billion-user-scale", "developer-experience", "clean-architecture", "monitoring", "debugging", "testing", "cicd", "performance", "ultra-debug-god-mode", "world-class-dx"], "author": "NeuroColony Engineering Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/neurocolony/backend"}, "bugs": {"url": "https://github.com/neurocolony/backend/issues"}, "homepage": "https://neurocolony.com", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}