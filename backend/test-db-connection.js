import mongoose from 'mongoose'
import dotenv from 'dotenv'

dotenv.config()

console.log('🔄 Testing MongoDB connection...')
console.log('MongoDB URI:', process.env.MONGODB_URI)

async function testConnection() {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    console.log('✅ MongoDB connected successfully!')
    
    // Test a simple operation
    const collections = await mongoose.connection.db.listCollections().toArray()
    console.log('📊 Available collections:', collections.map(c => c.name))
    
    // Create a test collection
    await mongoose.connection.db.collection('test').insertOne({ test: true, timestamp: new Date() })
    console.log('✅ Test write operation successful!')
    
    await mongoose.disconnect()
    console.log('✅ MongoDB disconnected successfully!')
    process.exit(0)
  } catch (error) {
    console.error('❌ MongoDB connection error:', error)
    process.exit(1)
  }
}

testConnection()