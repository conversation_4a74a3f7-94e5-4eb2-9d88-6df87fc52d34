/**
 * NeuroColony Unified Production Server
 * Consolidates all server variations into a single, configurable entry point
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const winston = require('winston');
const connectDB = require('./config/database');
const { configureRedis } = require('./config/redis');
const { initializeMetrics } = require('./config/metrics');
const errorHandler = require('./middleware/errorHandler');
const { featureFlags } = require('./config/features');

// Initialize Express app
const app = express();

// Logger configuration
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'neurocolony-api' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Global error handlers
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Middleware configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(compression());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(mongoSanitize());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Request logging
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('user-agent')
    });
  });
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API Routes - Consolidated and organized
const setupRoutes = () => {
  // Core routes
  app.use('/api/auth', require('./routes/unified/auth.routes'));
  app.use('/api/users', require('./routes/unified/users.routes'));
  
  // Business logic routes
  app.use('/api/sequences', require('./routes/unified/sequences.routes'));
  app.use('/api/agents', require('./routes/unified/agents.routes'));
  app.use('/api/workflows', require('./routes/unified/workflows.routes'));
  
  // Integration routes
  app.use('/api/integrations', require('./routes/unified/integrations.routes'));
  app.use('/api/billing', require('./routes/unified/billing.routes'));
  
  // Admin and analytics routes
  if (featureFlags.analytics) {
    app.use('/api/analytics', require('./routes/unified/analytics.routes'));
  }
  
  if (featureFlags.admin) {
    app.use('/api/admin', require('./routes/unified/admin.routes'));
  }
  
  // AI routes (conditional based on features)
  if (featureFlags.aiEngine) {
    app.use('/api/ai', require('./routes/unified/ai.routes'));
  }
  
  // Colony features (if enabled)
  if (featureFlags.colonyFeatures) {
    app.use('/api/colony', require('./routes/unified/colony.routes'));
  }
};

// Error handling middleware (must be last)
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString()
  });
});

// Server initialization
const startServer = async () => {
  try {
    // Connect to MongoDB
    await connectDB();
    logger.info('MongoDB connected successfully');
    
    // Initialize Redis
    if (featureFlags.caching) {
      await configureRedis();
      logger.info('Redis connected successfully');
    }
    
    // Initialize metrics
    if (featureFlags.metrics) {
      initializeMetrics(app);
      logger.info('Metrics initialized');
    }
    
    // Setup routes
    setupRoutes();
    logger.info('Routes configured');
    
    // Start server
    const PORT = process.env.PORT || 5000;
    const server = app.listen(PORT, () => {
      logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
      logger.info(`Feature flags: ${JSON.stringify(featureFlags)}`);
    });
    
    // Graceful shutdown
    const gracefulShutdown = async (signal) => {
      logger.info(`${signal} received, starting graceful shutdown`);
      
      server.close(() => {
        logger.info('HTTP server closed');
      });
      
      // Close database connections
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed');
        
        if (featureFlags.caching && global.redisClient) {
          await global.redisClient.quit();
          logger.info('Redis connection closed');
        }
        
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };
    
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;