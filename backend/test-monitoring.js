// ====================================================================
// ULTRA DEBUG GOD MODE - PHASE 4: PREVENTIVE HARDENING
// Monitoring System Test Suite
// ====================================================================

import monitoringSystem from './monitoring/index.js';
import axios from 'axios';
import { setTimeout } from 'timers/promises';

class MonitoringTester {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
    }

    async runTest(name, testFunction) {
        this.testResults.total++;
        console.log(`🧪 Testing: ${name}`);
        
        try {
            const result = await testFunction();
            
            if (result) {
                this.testResults.passed++;
                console.log(`✅ PASS: ${name}`);
                this.testResults.details.push({ name, status: 'PASS', result });
            } else {
                this.testResults.failed++;
                console.log(`❌ FAIL: ${name}`);
                this.testResults.details.push({ name, status: 'FAIL', error: 'Test returned false' });
            }
        } catch (error) {
            this.testResults.failed++;
            console.log(`❌ ERROR: ${name} - ${error.message}`);
            this.testResults.details.push({ name, status: 'ERROR', error: error.message });
        }
    }

    async runAllTests() {
        console.log('🔥 Starting Ultra Debug God Mode Monitoring Test Suite');
        console.log('='.repeat(70));

        // Initialize monitoring system for testing
        await this.runTest('Initialize Monitoring System', async () => {
            await monitoringSystem.initialize({
                enableDashboard: true,
                dashboard: { port: 5002 }, // Use different port for testing
                healthMonitor: { checkInterval: 5000 }, // Faster for testing
                selfHealer: { healthCheckInterval: 10000 }
            });
            return monitoringSystem.isRunning;
        });

        // Test Health Monitor
        await this.runTest('Health Monitor Initialization', async () => {
            const healthMonitor = monitoringSystem.getHealthMonitor();
            return healthMonitor !== null;
        });

        await this.runTest('Health Monitor Status Check', async () => {
            const healthMonitor = monitoringSystem.getHealthMonitor();
            if (!healthMonitor) return false;
            
            const status = healthMonitor.getStatus();
            return status && typeof status.status === 'string';
        });

        // Test Alert System
        await this.runTest('Alert System Initialization', async () => {
            const alertSystem = monitoringSystem.getAlertSystem();
            return alertSystem !== null;
        });

        await this.runTest('Alert System Configuration', async () => {
            const alertSystem = monitoringSystem.getAlertSystem();
            if (!alertSystem) return false;
            
            const config = alertSystem.getConfig();
            return config && typeof config === 'object';
        });

        // Test Circuit Breakers
        await this.runTest('Circuit Breaker Manager', async () => {
            const manager = monitoringSystem.getCircuitBreakerManager();
            return manager !== null;
        });

        await this.runTest('Circuit Breaker Creation', async () => {
            const manager = monitoringSystem.getCircuitBreakerManager();
            if (!manager) return false;
            
            const testBreaker = manager.create('test-service', {
                failureThreshold: 3,
                timeout: 5000
            });
            
            return testBreaker && testBreaker.name === 'test-service';
        });

        await this.runTest('Circuit Breaker Execution', async () => {
            const manager = monitoringSystem.getCircuitBreakerManager();
            if (!manager) return false;
            
            const testBreaker = manager.get('test-service');
            if (!testBreaker) return false;
            
            const result = await testBreaker.execute(async () => {
                return 'success';
            });
            
            return result === 'success';
        });

        // Test Self Healer
        await this.runTest('Self Healer Initialization', async () => {
            const selfHealer = monitoringSystem.getSelfHealer();
            return selfHealer !== null;
        });

        await this.runTest('Self Healer Statistics', async () => {
            const selfHealer = monitoringSystem.getSelfHealer();
            if (!selfHealer) return false;
            
            const stats = selfHealer.getHealingStats();
            return stats && typeof stats.totalActions === 'number';
        });

        // Test Logger
        await this.runTest('Production Logger Initialization', async () => {
            const logger = monitoringSystem.getLogger();
            return logger !== null;
        });

        await this.runTest('Logger Functionality', async () => {
            const logger = monitoringSystem.getLogger();
            if (!logger) return false;
            
            logger.info('Test log message', { test: true });
            
            const stats = logger.getLogStats();
            return stats && typeof stats === 'object';
        });

        // Test System Validator
        await this.runTest('System Validator Initialization', async () => {
            const validator = monitoringSystem.getSystemValidator();
            return validator !== null;
        });

        await this.runTest('System Validation Execution', async () => {
            const validator = monitoringSystem.getSystemValidator();
            if (!validator) return false;
            
            const result = await validator.validateSystem();
            return result && result.overall && result.timestamp;
        });

        // Test Dashboard
        await this.runTest('Dashboard Initialization', async () => {
            const dashboard = monitoringSystem.getDashboard();
            return dashboard !== null;
        });

        await this.runTest('Dashboard API Endpoint', async () => {
            // Wait a bit for dashboard to start
            await setTimeout(2000);
            
            try {
                const response = await axios.get('http://localhost:5002/api/status', {
                    timeout: 5000
                });
                return response.status === 200;
            } catch (error) {
                console.log(`Dashboard API test failed: ${error.message}`);
                return false;
            }
        });

        // Integration Tests
        await this.runTest('Alert Integration Test', async () => {
            const alertSystem = monitoringSystem.getAlertSystem();
            if (!alertSystem) return false;
            
            const testAlert = {
                type: 'integration_test',
                message: 'This is a test alert for integration testing',
                severity: 'info',
                timestamp: new Date()
            };
            
            const result = await alertSystem.sendAlert(testAlert);
            return result; // May be false if no alert channels configured, but function should work
        });

        await this.runTest('Circuit Breaker Failure Simulation', async () => {
            const manager = monitoringSystem.getCircuitBreakerManager();
            if (!manager) return false;
            
            const testBreaker = manager.get('test-service');
            if (!testBreaker) return false;
            
            // Simulate failures to trigger circuit breaker
            for (let i = 0; i < 5; i++) {
                try {
                    await testBreaker.execute(async () => {
                        throw new Error('Simulated failure');
                    });
                } catch (error) {
                    // Expected to fail
                }
            }
            
            // Circuit should now be open
            const stats = testBreaker.getStats();
            return stats.state === 'OPEN';
        });

        await this.runTest('Health Monitor Metrics Collection', async () => {
            const healthMonitor = monitoringSystem.getHealthMonitor();
            if (!healthMonitor) return false;
            
            // Trigger a health check
            await healthMonitor.performHealthCheck();
            
            const status = healthMonitor.getStatus();
            return status && status.system && typeof status.system.cpu === 'number';
        });

        // Performance Tests
        await this.runTest('Monitoring System Performance', async () => {
            const startTime = Date.now();
            const iterations = 100;
            
            for (let i = 0; i < iterations; i++) {
                const healthMonitor = monitoringSystem.getHealthMonitor();
                if (healthMonitor) {
                    healthMonitor.getStatus();
                }
            }
            
            const endTime = Date.now();
            const avgTime = (endTime - startTime) / iterations;
            
            console.log(`Average status check time: ${avgTime.toFixed(2)}ms`);
            return avgTime < 10; // Should be very fast
        });

        // Cleanup Tests
        await this.runTest('Monitoring System Shutdown', async () => {
            await monitoringSystem.stop();
            return !monitoringSystem.isRunning;
        });

        // Display Results
        this.displayResults();
    }

    displayResults() {
        console.log('\n' + '='.repeat(70));
        console.log('🏆 ULTRA DEBUG GOD MODE MONITORING TEST RESULTS');
        console.log('='.repeat(70));
        
        const passRate = ((this.testResults.passed / this.testResults.total) * 100).toFixed(1);
        
        console.log(`📊 Total Tests: ${this.testResults.total}`);
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`📈 Pass Rate: ${passRate}%`);
        
        if (this.testResults.failed > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults.details
                .filter(test => test.status !== 'PASS')
                .forEach(test => {
                    console.log(`   • ${test.name}: ${test.error || 'Failed'}`);
                });
        }
        
        console.log('\n' + '='.repeat(70));
        
        if (passRate >= 90) {
            console.log('🎉 EXCELLENT! Monitoring system is working perfectly!');
        } else if (passRate >= 80) {
            console.log('👍 GOOD! Most monitoring features are working correctly.');
        } else if (passRate >= 70) {
            console.log('⚠️ WARNING! Some monitoring features need attention.');
        } else {
            console.log('🚨 CRITICAL! Monitoring system has significant issues.');
        }
        
        console.log('='.repeat(70));
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new MonitoringTester();
    
    tester.runAllTests()
        .then(() => {
            console.log('\n🎯 Monitoring system testing complete!');
            process.exit(tester.testResults.failed > 0 ? 1 : 0);
        })
        .catch((error) => {
            console.error('💥 Test suite failed:', error);
            process.exit(1);
        });
}

export default MonitoringTester;