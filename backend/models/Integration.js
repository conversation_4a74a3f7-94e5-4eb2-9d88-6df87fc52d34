import mongoose from 'mongoose'
import crypto from 'crypto'
import encryptionService from '../services/security/encryptionService.js'

/**
 * NeuroColony Integration Model - Multi-Channel Platform Connections
 * Unified interface for all major marketing platforms
 * Superior marketing-first approach with secure credential management
 */

const integrationSchema = new mongoose.Schema({
  // Basic Integration Information
  name: {
    type: String,
    required: true,
    trim: true
  },
  platform: {
    type: String,
    required: true,
    enum: [
      'mailchimp',
      'convertkit',
      'hubspot',
      'salesforce',
      'google_analytics',
      'facebook_ads',
      'linkedin_ads',
      'twitter_api',
      'instagram_api',
      'zapier',
      'stripe',
      'shopify',
      'wordpress',
      'slack',
      'discord',
      'airtable',
      'notion',
      'calendly',
      'zoom'
    ],
    index: true
  },
  
  // Connection Status
  status: {
    type: String,
    enum: ['connected', 'disconnected', 'error', 'pending'],
    default: 'pending',
    index: true
  },
  
  // Encrypted Credentials
  credentials: {
    apiKey: String,
    apiSecret: String,
    accessToken: String,
    refreshToken: String,
    clientId: String,
    clientSecret: String,
    webhookUrl: String,
    customFields: mongoose.Schema.Types.Mixed
  },
  
  // Platform-Specific Configuration
  config: {
    baseUrl: String,
    version: String,
    scopes: [String],
    webhookEvents: [String],
    rateLimits: {
      requestsPerMinute: Number,
      requestsPerHour: Number,
      requestsPerDay: Number
    },
    features: [String],
    customSettings: mongoose.Schema.Types.Mixed
  },
  
  // Integration Capabilities
  capabilities: [{
    name: String,
    description: String,
    endpoint: String,
    method: String,
    inputSchema: mongoose.Schema.Types.Mixed,
    outputSchema: mongoose.Schema.Types.Mixed,
    rateLimited: { type: Boolean, default: true }
  }],
  
  // Usage Statistics
  usage: {
    totalRequests: { type: Number, default: 0 },
    successfulRequests: { type: Number, default: 0 },
    failedRequests: { type: Number, default: 0 },
    lastRequestTime: Date,
    monthlyUsage: { type: Number, default: 0 },
    quotaLimit: Number,
    quotaUsed: { type: Number, default: 0 }
  },
  
  // Health Monitoring
  health: {
    isHealthy: { type: Boolean, default: true },
    lastHealthCheck: Date,
    responseTime: Number,
    errorRate: { type: Number, default: 0 },
    uptime: { type: Number, default: 100 }
  },
  
  // Webhook Configuration
  webhooks: [{
    event: String,
    url: String,
    secret: String,
    active: { type: Boolean, default: true },
    lastTriggered: Date,
    totalTriggers: { type: Number, default: 0 }
  }],
  
  // Data Mapping
  fieldMappings: [{
    localField: String,
    remoteField: String,
    transformation: String,
    required: { type: Boolean, default: false }
  }],
  
  // Ownership & Access
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  },
  
  // Integration Metadata
  metadata: {
    connectedAt: Date,
    lastSyncTime: Date,
    syncFrequency: String,
    autoSync: { type: Boolean, default: false },
    tags: [String],
    notes: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
integrationSchema.index({ owner: 1, platform: 1 })
integrationSchema.index({ status: 1, platform: 1 })
integrationSchema.index({ 'health.isHealthy': 1 })
integrationSchema.index({ createdAt: -1 })

// Virtual for success rate
integrationSchema.virtual('successRate').get(function() {
  if (this.usage.totalRequests === 0) return 100
  return (this.usage.successfulRequests / this.usage.totalRequests) * 100
})

// Virtual for quota usage percentage
integrationSchema.virtual('quotaUsagePercent').get(function() {
  if (!this.usage.quotaLimit) return 0
  return (this.usage.quotaUsed / this.usage.quotaLimit) * 100
})

// Methods for credential encryption/decryption using secure encryption service
integrationSchema.methods.encryptCredentials = async function(credentials) {
  const context = `integration-${this.platform}-${this._id}`
  const fields = ['apiKey', 'apiSecret', 'accessToken', 'refreshToken', 'clientId', 'clientSecret', 'webhookUrl']
  
  const encrypted = {}
  for (const [key, value] of Object.entries(credentials)) {
    if (value && typeof value === 'string' && fields.includes(key)) {
      // Encrypt sensitive fields
      const result = await encryptionService.encrypt(value, context, {
        integrationId: this._id.toString(),
        platform: this.platform,
        field: key
      })
      encrypted[key] = result.encrypted
    } else {
      // Keep non-sensitive fields as-is
      encrypted[key] = value
    }
  }
  
  this.credentials = encrypted
  return this
}

integrationSchema.methods.decryptCredentials = async function() {
  const context = `integration-${this.platform}-${this._id}`
  const fields = ['apiKey', 'apiSecret', 'accessToken', 'refreshToken', 'clientId', 'clientSecret', 'webhookUrl']
  
  const decrypted = {}
  for (const [key, value] of Object.entries(this.credentials || {})) {
    if (value && typeof value === 'string' && fields.includes(key)) {
      try {
        // Decrypt sensitive fields
        decrypted[key] = await encryptionService.decrypt(value, context, {
          integrationId: this._id.toString(),
          platform: this.platform,
          field: key
        })
      } catch (error) {
        console.error(`Failed to decrypt ${key} for integration ${this._id}:`, error.message)
        decrypted[key] = null // Return null for failed decryption
      }
    } else {
      // Keep non-sensitive fields as-is
      decrypted[key] = value
    }
  }
  
  return decrypted
}

// Methods for health monitoring
integrationSchema.methods.updateHealth = function(responseTime, success = true) {
  this.health.lastHealthCheck = new Date()
  this.health.responseTime = responseTime
  
  if (success) {
    this.health.isHealthy = true
    this.health.uptime = Math.min(100, this.health.uptime + 0.1)
  } else {
    this.health.errorRate += 1
    if (this.health.errorRate > 10) {
      this.health.isHealthy = false
    }
    this.health.uptime = Math.max(0, this.health.uptime - 1)
  }
  
  return this.save()
}

// Methods for usage tracking
integrationSchema.methods.recordRequest = function(success = true) {
  this.usage.totalRequests += 1
  this.usage.lastRequestTime = new Date()
  
  if (success) {
    this.usage.successfulRequests += 1
  } else {
    this.usage.failedRequests += 1
  }
  
  // Update monthly usage (simplified)
  const currentMonth = new Date().getMonth()
  if (!this.usage.lastMonthTracked || this.usage.lastMonthTracked !== currentMonth) {
    this.usage.monthlyUsage = 1
    this.usage.lastMonthTracked = currentMonth
  } else {
    this.usage.monthlyUsage += 1
  }
  
  return this.save()
}

// Static methods for integration management
integrationSchema.statics.getByPlatform = function(platform, userId) {
  return this.findOne({ platform, owner: userId, status: 'connected' })
}

integrationSchema.statics.getHealthyIntegrations = function(userId) {
  return this.find({
    owner: userId,
    status: 'connected',
    'health.isHealthy': true
  })
}

integrationSchema.statics.getPlatformStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$platform',
        total: { $sum: 1 },
        connected: {
          $sum: { $cond: [{ $eq: ['$status', 'connected'] }, 1, 0] }
        },
        avgSuccessRate: { $avg: '$usage.successfulRequests' }
      }
    },
    { $sort: { total: -1 } }
  ])
}

export default mongoose.model('Integration', integrationSchema)
