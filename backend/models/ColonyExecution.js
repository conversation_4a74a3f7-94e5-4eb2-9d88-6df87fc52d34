import mongoose from 'mongoose'

/**
 * NeuroColony Execution Model - Agent Execution Tracking & Inter-Agent Communication
 * Tracks all agent executions, signals, and colony intelligence coordination
 */

const colonyExecutionSchema = new mongoose.Schema({
  // Execution Identification
  executionId: {
    type: String,
    required: true,
    unique: true,
    default: () => `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },
  
  // Agent Information
  agent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyAgent',
    required: true,
    index: true
  },
  agentType: {
    type: String,
    enum: ['queen', 'worker', 'scout'],
    required: true
  },
  
  // Execution Context
  workflow: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyWorkflow'
  },
  parentExecution: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyExecution'
  },
  childExecutions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyExecution'
  }],
  
  // Execution Data
  inputData: mongoose.Schema.Types.Mixed,
  outputData: mongoose.Schema.Types.Mixed,
  context: mongoose.Schema.Types.Mixed,
  
  // Execution Status
  status: {
    type: String,
    enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'retrying'],
    default: 'pending',
    index: true
  },
  
  // Timing Information
  scheduledAt: Date,
  startedAt: Date,
  completedAt: Date,
  executionTime: Number, // milliseconds
  
  // Error Handling
  error: {
    message: String,
    stack: String,
    code: String,
    retryCount: { type: Number, default: 0 },
    maxRetries: { type: Number, default: 3 }
  },
  
  // Colony Intelligence Signals
  signalType: {
    type: String,
    enum: [
      'execution_start',
      'execution_complete',
      'execution_failed',
      'data_request',
      'data_response',
      'trigger_downstream',
      'colony_alert',
      'performance_update'
    ]
  },
  sourceAgent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyAgent'
  },
  targetAgents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyAgent'
  }],
  
  // Performance Metrics
  performance: {
    cpuUsage: Number,
    memoryUsage: Number,
    apiCalls: Number,
    dataProcessed: Number,
    tokensUsed: Number,
    cost: Number
  },
  
  // Marketing Metrics (for marketing agents)
  marketingResults: {
    emailsSent: { type: Number, default: 0 },
    emailsDelivered: { type: Number, default: 0 },
    emailsOpened: { type: Number, default: 0 },
    emailsClicked: { type: Number, default: 0 },
    conversions: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    leadsCaptured: { type: Number, default: 0 },
    socialEngagement: { type: Number, default: 0 }
  },
  
  // Logs and Debug Information
  logs: [{
    timestamp: { type: Date, default: Date.now },
    level: {
      type: String,
      enum: ['debug', 'info', 'warn', 'error'],
      default: 'info'
    },
    message: String,
    data: mongoose.Schema.Types.Mixed
  }],
  
  // User Context
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Execution Environment
  environment: {
    nodeVersion: String,
    platform: String,
    serverInstance: String,
    region: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
colonyExecutionSchema.index({ agent: 1, status: 1 })
colonyExecutionSchema.index({ user: 1, createdAt: -1 })
colonyExecutionSchema.index({ workflow: 1, createdAt: -1 })
colonyExecutionSchema.index({ status: 1, scheduledAt: 1 })
colonyExecutionSchema.index({ signalType: 1, sourceAgent: 1 })

// Virtual for execution duration
colonyExecutionSchema.virtual('duration').get(function() {
  if (this.startedAt && this.completedAt) {
    return this.completedAt - this.startedAt
  }
  return null
})

// Virtual for success status
colonyExecutionSchema.virtual('isSuccessful').get(function() {
  return this.status === 'completed'
})

// Methods for execution management
colonyExecutionSchema.methods.addLog = function(level, message, data = null) {
  this.logs.push({
    timestamp: new Date(),
    level,
    message,
    data
  })
  return this.save()
}

colonyExecutionSchema.methods.markStarted = function() {
  this.status = 'running'
  this.startedAt = new Date()
  return this.save()
}

colonyExecutionSchema.methods.markCompleted = function(outputData = null) {
  this.status = 'completed'
  this.completedAt = new Date()
  this.executionTime = this.completedAt - this.startedAt
  if (outputData) {
    this.outputData = outputData
  }
  return this.save()
}

colonyExecutionSchema.methods.markFailed = function(error) {
  this.status = 'failed'
  this.completedAt = new Date()
  this.error = {
    message: error.message,
    stack: error.stack,
    code: error.code || 'UNKNOWN_ERROR',
    retryCount: this.error?.retryCount || 0
  }
  return this.save()
}

// Static methods for colony intelligence
colonyExecutionSchema.statics.getActiveExecutions = function(userId) {
  return this.find({
    user: userId,
    status: { $in: ['pending', 'running', 'retrying'] }
  }).populate('agent', 'name agentType category')
}

colonyExecutionSchema.statics.getExecutionHistory = function(userId, limit = 50) {
  return this.find({ user: userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('agent', 'name agentType category')
}

colonyExecutionSchema.statics.getColonyPerformance = function(userId, timeframe = '24h') {
  const since = new Date()
  switch (timeframe) {
    case '1h':
      since.setHours(since.getHours() - 1)
      break
    case '24h':
      since.setDate(since.getDate() - 1)
      break
    case '7d':
      since.setDate(since.getDate() - 7)
      break
    case '30d':
      since.setDate(since.getDate() - 30)
      break
  }
  
  return this.aggregate([
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: since }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgExecutionTime: { $avg: '$executionTime' },
        totalCost: { $sum: '$performance.cost' }
      }
    }
  ])
}

export default mongoose.model('ColonyExecution', colonyExecutionSchema)
