import mongoose from 'mongoose'

/**
 * NeuroColony Agent Model - Colony Intelligence Architecture
 * Implements Queen/Worker/Scout agent hierarchy with ant-inspired design
 * Surpasses n8n with autonomous collaboration and marketing-first approach
 */

const colonyAgentSchema = new mongoose.Schema({
  // Basic Agent Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  
  // Colony Intelligence Hierarchy
  agentType: {
    type: String,
    enum: ['queen', 'worker', 'scout'],
    required: true,
    index: true
  },
  
  // Agent Classification
  category: {
    type: String,
    enum: [
      'email_marketing',
      'social_media',
      'content_creation',
      'analytics_reporting',
      'lead_generation',
      'customer_service',
      'sales_automation',
      'integration_hub',
      'ai_orchestration'
    ],
    required: true,
    index: true
  },
  
  // Agent Capabilities & Configuration
  capabilities: [{
    name: String,
    description: String,
    inputSchema: mongoose.Schema.Types.Mixed,
    outputSchema: mongoose.Schema.Types.Mixed
  }],
  
  // Colony Relationships
  parentQueen: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyAgent',
    index: true
  },
  subordinateAgents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ColonyAgent'
  }],
  collaboratingAgents: [{
    agent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ColonyAgent'
    },
    relationship: {
      type: String,
      enum: ['data_provider', 'data_consumer', 'trigger_source', 'trigger_target']
    }
  }],
  
  // Execution Configuration
  executionConfig: {
    triggers: [{
      type: {
        type: String,
        enum: ['schedule', 'webhook', 'agent_signal', 'manual', 'condition']
      },
      config: mongoose.Schema.Types.Mixed
    }],
    conditions: [{
      field: String,
      operator: String,
      value: mongoose.Schema.Types.Mixed
    }],
    actions: [{
      type: String,
      config: mongoose.Schema.Types.Mixed
    }],
    errorHandling: {
      retryCount: { type: Number, default: 3 },
      retryDelay: { type: Number, default: 5000 },
      fallbackAction: String
    }
  },
  
  // AI Integration
  aiConfig: {
    provider: {
      type: String,
      enum: ['claude-4', 'gpt-4', 'local-ai', 'multi-ai'],
      default: 'multi-ai'
    },
    model: String,
    systemPrompt: String,
    temperature: { type: Number, default: 0.7 },
    maxTokens: { type: Number, default: 2000 },
    marketingContext: {
      industry: String,
      targetAudience: String,
      brandVoice: String,
      objectives: [String]
    }
  },
  
  // Integration Connections
  integrations: [{
    platform: String,
    config: mongoose.Schema.Types.Mixed,
    credentials: mongoose.Schema.Types.Mixed, // Encrypted
    status: {
      type: String,
      enum: ['connected', 'disconnected', 'error'],
      default: 'disconnected'
    }
  }],
  
  // Performance & Analytics
  performance: {
    totalExecutions: { type: Number, default: 0 },
    successfulExecutions: { type: Number, default: 0 },
    failedExecutions: { type: Number, default: 0 },
    averageExecutionTime: { type: Number, default: 0 },
    lastExecutionTime: Date,
    successRate: { type: Number, default: 0 }
  },
  
  // Marketing Metrics (for marketing-focused agents)
  marketingMetrics: {
    emailsSent: { type: Number, default: 0 },
    emailsOpened: { type: Number, default: 0 },
    emailsClicked: { type: Number, default: 0 },
    conversions: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    roi: { type: Number, default: 0 }
  },
  
  // Ownership & Access
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  },
  
  // Agent Status
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'error', 'archived'],
    default: 'draft',
    index: true
  },
  
  // Marketplace Information
  marketplace: {
    isPublic: { type: Boolean, default: false },
    price: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    rating: { type: Number, default: 0 },
    reviews: [{
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      rating: Number,
      comment: String,
      createdAt: { type: Date, default: Date.now }
    }],
    tags: [String],
    featured: { type: Boolean, default: false }
  },
  
  // Version Control
  version: { type: String, default: '1.0.0' },
  changelog: [{
    version: String,
    changes: String,
    date: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
colonyAgentSchema.index({ owner: 1, status: 1 })
colonyAgentSchema.index({ agentType: 1, category: 1 })
colonyAgentSchema.index({ 'marketplace.isPublic': 1, 'marketplace.featured': 1 })
colonyAgentSchema.index({ createdAt: -1 })

// Virtual for success rate calculation
colonyAgentSchema.virtual('calculatedSuccessRate').get(function() {
  if (this.performance.totalExecutions === 0) return 0
  return (this.performance.successfulExecutions / this.performance.totalExecutions) * 100
})

// Methods for colony intelligence
colonyAgentSchema.methods.sendSignalToColony = async function(signal, data) {
  // Implementation for inter-agent communication
  const ColonyExecution = mongoose.model('ColonyExecution')

  return await ColonyExecution.create({
    sourceAgent: this._id,
    signalType: signal,
    data: data,
    timestamp: new Date()
  })
}

colonyAgentSchema.methods.executeWithColony = async function(inputData, context = {}) {
  // Enhanced execution with colony intelligence
  const execution = {
    agent: this._id,
    inputData,
    context,
    startTime: new Date(),
    status: 'running'
  }

  try {
    // Pre-execution: Notify parent Queen if exists
    if (this.parentQueen) {
      await this.sendSignalToColony('execution_start', { execution })
    }

    // Execute agent logic based on type
    let result
    switch (this.agentType) {
      case 'queen':
        result = await this.executeQueenLogic(inputData, context)
        break
      case 'worker':
        result = await this.executeWorkerLogic(inputData, context)
        break
      case 'scout':
        result = await this.executeScoutLogic(inputData, context)
        break
    }

    // Post-execution: Update performance metrics
    this.performance.totalExecutions += 1
    this.performance.successfulExecutions += 1
    this.performance.lastExecutionTime = new Date()
    this.performance.successRate = this.calculatedSuccessRate

    await this.save()

    return {
      success: true,
      result,
      executionTime: new Date() - execution.startTime
    }

  } catch (error) {
    this.performance.totalExecutions += 1
    this.performance.failedExecutions += 1
    await this.save()

    throw error
  }
}

// Static methods for colony management
colonyAgentSchema.statics.findByColonyHierarchy = function(queenId) {
  return this.find({
    $or: [
      { _id: queenId },
      { parentQueen: queenId }
    ]
  }).populate('parentQueen subordinateAgents')
}

colonyAgentSchema.statics.getMarketplaceFeatured = function() {
  return this.find({
    'marketplace.isPublic': true,
    'marketplace.featured': true,
    status: 'active'
  }).sort({ 'marketplace.downloads': -1, 'marketplace.rating': -1 })
}

export default mongoose.model('ColonyAgent', colonyAgentSchema)
