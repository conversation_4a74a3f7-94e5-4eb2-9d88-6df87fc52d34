import mongoose from 'mongoose'

// Workflow Status Types
const WORKFLOW_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
}

// Step Types
const STEP_TYPES = {
  AGENT: 'agent',           // Execute an agent
  CONDITION: 'condition',   // Conditional logic
  DELAY: 'delay',          // Wait/delay step
  WEBHOOK: 'webhook',      // External webhook call
  TRANSFORM: 'transform',  // Data transformation
  SPLIT: 'split',         // Split into parallel paths
  MERGE: 'merge'          // Merge parallel paths
}

// Trigger Types
const WORKFLOW_TRIGGERS = {
  MANUAL: 'manual',
  SCHEDULE: 'schedule',
  WEBHOOK: 'webhook',
  EMAIL: 'email',
  API: 'api',
  EVENT: 'event'
}

const workflowSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  
  // Visual Metadata
  icon: String,
  color: String,
  tags: [String],
  category: {
    type: String,
    enum: ['marketing', 'sales', 'support', 'analytics', 'content', 'automation', 'integration']
  },
  
  // Workflow Definition
  steps: [{
    id: {
      type: String,
      required: true
    },
    name: String,
    type: {
      type: String,
      enum: Object.values(STEP_TYPES),
      required: true
    },
    
    // Position for visual editor
    position: {
      x: Number,
      y: Number
    },
    
    // Step Configuration
    config: {
      // For agent steps
      agentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Agent'
      },
      agentConfig: mongoose.Schema.Types.Mixed,
      
      // For condition steps
      condition: {
        expression: String,
        operator: {
          type: String,
          enum: ['equals', 'not_equals', 'contains', 'greater_than', 'less_than', 'exists', 'custom']
        },
        value: mongoose.Schema.Types.Mixed
      },
      
      // For delay steps
      delay: {
        duration: Number, // milliseconds
        unit: {
          type: String,
          enum: ['seconds', 'minutes', 'hours', 'days']
        }
      },
      
      // For webhook steps
      webhook: {
        url: String,
        method: String,
        headers: mongoose.Schema.Types.Mixed,
        body: mongoose.Schema.Types.Mixed,
        authentication: mongoose.Schema.Types.Mixed
      },
      
      // For transform steps
      transform: {
        script: String,
        language: {
          type: String,
          enum: ['javascript', 'python', 'jq'],
          default: 'javascript'
        }
      }
    },
    
    // Input/Output Mapping
    inputs: [{
      name: String,
      source: String, // step_id.output_name or workflow.input_name
      transformation: String
    }],
    outputs: [{
      name: String,
      type: String,
      description: String
    }],
    
    // Error Handling
    errorHandling: {
      strategy: {
        type: String,
        enum: ['fail', 'continue', 'retry', 'fallback'],
        default: 'fail'
      },
      retries: {
        maxAttempts: { type: Number, default: 3 },
        backoff: {
          type: String,
          enum: ['linear', 'exponential'],
          default: 'exponential'
        }
      },
      fallbackStep: String
    },
    
    // Conditional Execution
    conditions: [{
      expression: String,
      required: Boolean
    }]
  }],
  
  // Step Connections (Ant Trails)
  connections: [{
    from: String, // step id
    to: String,   // step id
    condition: String, // optional condition
    label: String,
    style: {
      color: String,
      thickness: Number,
      pattern: {
        type: String,
        enum: ['solid', 'dashed', 'dotted']
      }
    }
  }],
  
  // Workflow Triggers
  triggers: [{
    type: {
      type: String,
      enum: Object.values(WORKFLOW_TRIGGERS),
      required: true
    },
    config: {
      // Schedule trigger
      schedule: {
        cron: String,
        timezone: String,
        enabled: Boolean
      },
      
      // Webhook trigger
      webhook: {
        url: String,
        secret: String,
        method: String
      },
      
      // Email trigger
      email: {
        address: String,
        subject: String,
        filters: [String]
      },
      
      // Event trigger
      event: {
        source: String,
        eventType: String,
        filters: mongoose.Schema.Types.Mixed
      }
    },
    enabled: {
      type: Boolean,
      default: true
    }
  }],
  
  // Workflow Inputs/Outputs
  inputs: [{
    name: String,
    type: String,
    required: Boolean,
    default: mongoose.Schema.Types.Mixed,
    description: String
  }],
  outputs: [{
    name: String,
    type: String,
    description: String
  }],
  
  // Execution Settings
  settings: {
    timeout: { type: Number, default: 3600000 }, // 1 hour in ms
    maxConcurrentExecutions: { type: Number, default: 10 },
    retryPolicy: {
      enabled: Boolean,
      maxRetries: Number,
      backoffStrategy: String
    },
    notifications: {
      onSuccess: Boolean,
      onFailure: Boolean,
      channels: [String] // email, slack, webhook
    }
  },
  
  // Performance & Analytics
  analytics: {
    totalExecutions: { type: Number, default: 0 },
    successfulExecutions: { type: Number, default: 0 },
    averageExecutionTime: { type: Number, default: 0 },
    lastExecutionTime: Date,
    successRate: { type: Number, default: 0 },
    
    // Step-level analytics
    stepAnalytics: [{
      stepId: String,
      executions: Number,
      averageTime: Number,
      successRate: Number,
      errorRate: Number
    }]
  },
  
  // Colony Intelligence
  colonyMetrics: {
    agentCollaboration: [{
      agentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Agent'
      },
      interactions: Number,
      successRate: Number,
      averageResponseTime: Number
    }],
    communicationPatterns: [{
      fromAgent: String,
      toAgent: String,
      frequency: Number,
      protocol: String
    }],
    resourceUtilization: {
      cpu: Number,
      memory: Number,
      network: Number
    }
  },
  
  // Status & Lifecycle
  status: {
    type: String,
    enum: Object.values(WORKFLOW_STATUS),
    default: 'draft'
  },
  
  // Ownership & Access
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  organization: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization'
  },
  
  // Sharing & Templates
  isPublic: {
    type: Boolean,
    default: false
  },
  isTemplate: {
    type: Boolean,
    default: false
  },
  templateCategory: String,
  
  // Subscription & Billing
  subscriptionTier: {
    type: String,
    enum: ['free', 'pro', 'business', 'enterprise'],
    default: 'free'
  },
  
  // Versioning
  version: {
    type: String,
    default: '1.0.0'
  },
  changelog: [{
    version: String,
    changes: [String],
    date: { type: Date, default: Date.now }
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastExecutedAt: Date
})

// Indexes
workflowSchema.index({ owner: 1, status: 1 })
workflowSchema.index({ isPublic: 1, isTemplate: 1 })
workflowSchema.index({ category: 1, tags: 1 })
workflowSchema.index({ subscriptionTier: 1 })

// Update timestamp on save
workflowSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

// Methods
workflowSchema.methods.activate = function() {
  this.status = 'active'
  return this.save()
}

workflowSchema.methods.pause = function() {
  this.status = 'paused'
  return this.save()
}

workflowSchema.methods.updateAnalytics = function(executionData) {
  this.analytics.totalExecutions += 1
  if (executionData.success) {
    this.analytics.successfulExecutions += 1
  }
  this.analytics.successRate = this.analytics.successfulExecutions / this.analytics.totalExecutions
  this.analytics.lastExecutionTime = new Date()
  
  // Update average execution time
  if (executionData.duration) {
    const currentAvg = this.analytics.averageExecutionTime || 0
    const totalExecs = this.analytics.totalExecutions
    this.analytics.averageExecutionTime = 
      (currentAvg * (totalExecs - 1) + executionData.duration) / totalExecs
  }
  
  return this.save()
}

// Export constants
export { WORKFLOW_STATUS, STEP_TYPES, WORKFLOW_TRIGGERS }

const Workflow = mongoose.model('Workflow', workflowSchema)
export default Workflow
