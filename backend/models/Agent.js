import mongoose from 'mongoose'

// Agent Types in the Colony Hierarchy
const AGENT_TYPES = {
  QUEEN: 'queen',      // Master orchestrators
  WORKER: 'worker',    // Specialized task agents
  SCOUT: 'scout'       // Discovery and monitoring agents
}

// Agent Specializations (different "species")
const AGENT_SPECIALIZATIONS = {
  // Email Marketing Specialists
  EMAIL_SEQUENCE: 'email_sequence',
  EMAIL_OPTIMIZATION: 'email_optimization',
  SUBJECT_LINE_TESTING: 'subject_line_testing',
  SEND_TIME_OPTIMIZATION: 'send_time_optimization',
  
  // Social Media Specialists
  SOCIAL_CONTENT: 'social_content',
  SOCIAL_SCHEDULING: 'social_scheduling',
  SOCIAL_ENGAGEMENT: 'social_engagement',
  HASHTAG_RESEARCH: 'hashtag_research',
  
  // Data Processing Specialists
  DATA_ENRICHMENT: 'data_enrichment',
  LEAD_SCORING: 'lead_scoring',
  AUDIENCE_SEGMENTATION: 'audience_segmentation',
  ANALYTICS_REPORTING: 'analytics_reporting',
  
  // Web & API Specialists
  WEB_SCRAPING: 'web_scraping',
  API_INTEGRATION: 'api_integration',
  WEBHOOK_PROCESSING: 'webhook_processing',
  DATABASE_SYNC: 'database_sync',
  
  // Content Creation Specialists
  CONTENT_GENERATION: 'content_generation',
  IMAGE_GENERATION: 'image_generation',
  VIDEO_PROCESSING: 'video_processing',
  COPYWRITING: 'copywriting',
  
  // Monitoring & Intelligence Specialists
  COMPETITOR_MONITORING: 'competitor_monitoring',
  PRICE_TRACKING: 'price_tracking',
  SENTIMENT_ANALYSIS: 'sentiment_analysis',
  TREND_DETECTION: 'trend_detection',
  
  // Orchestration Specialists (Queen Agents)
  WORKFLOW_ORCHESTRATOR: 'workflow_orchestrator',
  CAMPAIGN_MANAGER: 'campaign_manager',
  RESOURCE_ALLOCATOR: 'resource_allocator',
  PERFORMANCE_OPTIMIZER: 'performance_optimizer'
}

// Agent Communication Protocols
const COMMUNICATION_PROTOCOLS = {
  DIRECT: 'direct',           // Direct agent-to-agent communication
  BROADCAST: 'broadcast',     // One-to-many communication
  QUEUE: 'queue',            // Asynchronous task queuing
  EVENT: 'event',            // Event-driven communication
  PIPELINE: 'pipeline'        // Sequential processing pipeline
}

const agentSchema = new mongoose.Schema({
  // Basic Agent Identity
  designation: {
    type: String,
    required: true,
    trim: true,
    alias: 'name' // Backward compatibility
  },
  purpose: {
    type: String,
    required: true,
    alias: 'description' // Backward compatibility
  },
  
  // Colony Hierarchy
  colonyType: {
    type: String,
    enum: ['communication', 'intelligence', 'evolution', 'automation'],
    required: true
  },
  specialization: {
    type: String,
    enum: Object.values(AGENT_SPECIALIZATIONS),
    required: true
  },
  
  // Neural Network Metrics
  neuralComplexity: {
    type: Number,
    min: 1,
    max: 10,
    default: 5,
    description: 'Algorithm complexity rating (1-10)'
  },
  swarmSize: {
    type: Number,
    default: 3,
    description: 'Number of sub-agents in the swarm'
  },
  synapticStrength: {
    type: Number,
    min: 0,
    max: 100,
    default: 75,
    description: 'Connection quality percentage'
  },
  evolutionStage: {
    type: String,
    enum: ['nascent', 'developing', 'advanced', 'optimal', 'transcendent'],
    default: 'developing',
    description: 'Current optimization level'
  },
  
  // Agent Capabilities
  capabilities: [{
    name: String,
    description: String,
    inputSchema: mongoose.Schema.Types.Mixed,
    outputSchema: mongoose.Schema.Types.Mixed,
    complexity: {
      type: String,
      enum: ['simple', 'moderate', 'complex', 'expert'],
      default: 'simple'
    }
  }],
  
  // Colony Relationships
  parentAgents: [{
    agentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Agent'
    },
    relationship: {
      type: String,
      enum: ['supervisor', 'coordinator', 'collaborator']
    }
  }],
  childAgents: [{
    agentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Agent'
    },
    relationship: {
      type: String,
      enum: ['subordinate', 'delegate', 'specialist']
    }
  }],
  
  // Communication Configuration
  communicationProtocols: [{
    type: String,
    enum: Object.values(COMMUNICATION_PROTOCOLS)
  }],
  messageFormats: [{
    protocol: String,
    format: mongoose.Schema.Types.Mixed
  }],
  
  // Execution Configuration
  executionConfig: {
    runtime: {
      type: String,
      enum: ['nodejs', 'python', 'docker', 'serverless'],
      default: 'nodejs'
    },
    resources: {
      cpu: { type: Number, default: 1 },
      memory: { type: Number, default: 512 }, // MB
      timeout: { type: Number, default: 300 } // seconds
    },
    scaling: {
      minInstances: { type: Number, default: 0 },
      maxInstances: { type: Number, default: 10 },
      autoScale: { type: Boolean, default: true }
    }
  },
  
  // AI Model Configuration
  aiConfig: {
    primaryModel: {
      type: String,
      enum: ['gpt-4', 'claude-4', 'local-ai', 'custom'],
      default: 'gpt-4'
    },
    fallbackModels: [String],
    temperature: { type: Number, default: 0.7 },
    maxTokens: { type: Number, default: 2000 },
    customPrompts: [{
      scenario: String,
      prompt: String,
      examples: [mongoose.Schema.Types.Mixed]
    }]
  },
  
  // Integration Points
  integrations: [{
    platform: String,
    type: {
      type: String,
      enum: ['api', 'webhook', 'database', 'file', 'email', 'social']
    },
    config: mongoose.Schema.Types.Mixed,
    credentials: {
      encrypted: String,
      keyId: String
    }
  }],
  
  // Performance Metrics
  performance: {
    totalExecutions: { type: Number, default: 0 },
    successfulExecutions: { type: Number, default: 0 },
    averageExecutionTime: { type: Number, default: 0 },
    lastExecutionTime: Date,
    errorRate: { type: Number, default: 0 },
    resourceUtilization: {
      cpu: { type: Number, default: 0 },
      memory: { type: Number, default: 0 }
    }
  },
  
  // Subscription & Billing
  subscriptionTier: {
    type: String,
    enum: ['free', 'pro', 'business', 'enterprise'],
    default: 'free'
  },
  computeCost: {
    perExecution: { type: Number, default: 0 },
    perMinute: { type: Number, default: 0 }
  },
  
  // Ownership & Access
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  isBlueprint: {
    type: Boolean,
    default: false,
    alias: 'isTemplate' // Backward compatibility
  },
  tags: [String],
  
  // Versioning
  version: {
    type: String,
    default: '1.0.0'
  },
  changelog: [{
    version: String,
    changes: [String],
    date: { type: Date, default: Date.now }
  }],
  
  // Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'deprecated'],
    default: 'active'
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastActiveAt: Date
})

// Indexes for performance
agentSchema.index({ owner: 1, status: 1 })
agentSchema.index({ colonyType: 1, specialization: 1 })
agentSchema.index({ isPublic: 1, isBlueprint: 1 })
agentSchema.index({ subscriptionTier: 1 })
agentSchema.index({ tags: 1 })
agentSchema.index({ neuralComplexity: 1 })
agentSchema.index({ evolutionStage: 1 })

// Update timestamp on save
agentSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

// Export constants for use in other modules
export { AGENT_TYPES, AGENT_SPECIALIZATIONS, COMMUNICATION_PROTOCOLS }

const Agent = mongoose.model('Agent', agentSchema)
export default Agent
