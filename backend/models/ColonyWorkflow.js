import mongoose from 'mongoose'

/**
 * NeuroColony Workflow Model - Multi-Agent Marketing Automation Workflows
 * N8N-style visual workflows with colony intelligence and marketing focus
 */

const colonyWorkflowSchema = new mongoose.Schema({
  // Basic Workflow Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  
  // Workflow Configuration
  nodes: [{
    id: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['trigger', 'agent', 'condition', 'action', 'integration'],
      required: true
    },
    agentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ColonyAgent'
    },
    position: {
      x: Number,
      y: Number
    },
    config: mongoose.Schema.Types.Mixed,
    label: String
  }],
  
  connections: [{
    source: {
      nodeId: String,
      outputPort: String
    },
    target: {
      nodeId: String,
      inputPort: String
    },
    condition: mongoose.Schema.Types.Mixed // Optional condition for connection
  }],
  
  // Workflow Triggers
  triggers: [{
    type: {
      type: String,
      enum: ['schedule', 'webhook', 'manual', 'event', 'condition'],
      required: true
    },
    config: mongoose.Schema.Types.Mixed,
    active: { type: Boolean, default: true }
  }],
  
  // Workflow Status & Control
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'error', 'archived'],
    default: 'draft',
    index: true
  },
  
  // Execution Settings
  executionSettings: {
    maxConcurrentExecutions: { type: Number, default: 1 },
    timeout: { type: Number, default: 300000 }, // 5 minutes
    retryPolicy: {
      enabled: { type: Boolean, default: true },
      maxRetries: { type: Number, default: 3 },
      retryDelay: { type: Number, default: 5000 }
    },
    errorHandling: {
      continueOnError: { type: Boolean, default: false },
      notifyOnError: { type: Boolean, default: true }
    }
  },
  
  // Marketing Context
  marketingContext: {
    campaign: String,
    industry: String,
    targetAudience: String,
    objectives: [String],
    kpis: [{
      name: String,
      target: Number,
      current: { type: Number, default: 0 }
    }]
  },
  
  // Performance & Analytics
  performance: {
    totalExecutions: { type: Number, default: 0 },
    successfulExecutions: { type: Number, default: 0 },
    failedExecutions: { type: Number, default: 0 },
    averageExecutionTime: { type: Number, default: 0 },
    lastExecutionTime: Date,
    successRate: { type: Number, default: 0 }
  },
  
  // Marketing Results
  marketingResults: {
    totalEmailsSent: { type: Number, default: 0 },
    totalEmailsOpened: { type: Number, default: 0 },
    totalEmailsClicked: { type: Number, default: 0 },
    totalConversions: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    totalLeadsCaptured: { type: Number, default: 0 },
    roi: { type: Number, default: 0 }
  },
  
  // Ownership & Access
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  },
  collaborators: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['viewer', 'editor', 'admin'],
      default: 'viewer'
    }
  }],
  
  // Template & Marketplace
  template: {
    isTemplate: { type: Boolean, default: false },
    category: String,
    tags: [String],
    isPublic: { type: Boolean, default: false },
    downloads: { type: Number, default: 0 },
    rating: { type: Number, default: 0 },
    featured: { type: Boolean, default: false }
  },
  
  // Version Control
  version: { type: String, default: '1.0.0' },
  changelog: [{
    version: String,
    changes: String,
    date: { type: Date, default: Date.now }
  }],
  
  // Execution History (last 10 executions)
  recentExecutions: [{
    executionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ColonyExecution'
    },
    status: String,
    startTime: Date,
    endTime: Date,
    duration: Number
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
colonyWorkflowSchema.index({ owner: 1, status: 1 })
colonyWorkflowSchema.index({ 'template.isPublic': 1, 'template.featured': 1 })
colonyWorkflowSchema.index({ createdAt: -1 })
colonyWorkflowSchema.index({ 'triggers.type': 1, status: 1 })

// Virtual for success rate calculation
colonyWorkflowSchema.virtual('calculatedSuccessRate').get(function() {
  if (this.performance.totalExecutions === 0) return 0
  return (this.performance.successfulExecutions / this.performance.totalExecutions) * 100
})

// Virtual for ROI calculation
colonyWorkflowSchema.virtual('calculatedROI').get(function() {
  const revenue = this.marketingResults.totalRevenue
  const cost = this.performance.totalExecutions * 0.01 // Estimated cost per execution
  if (cost === 0) return 0
  return ((revenue - cost) / cost) * 100
})

// Methods for workflow management
colonyWorkflowSchema.methods.execute = async function(inputData = {}, context = {}) {
  const ColonyExecution = mongoose.model('ColonyExecution')
  
  // Create workflow execution record
  const execution = await ColonyExecution.create({
    workflow: this._id,
    agent: null, // Workflow-level execution
    agentType: 'queen', // Workflows act as Queen agents
    inputData,
    context: {
      ...context,
      workflowId: this._id,
      workflowName: this.name
    },
    user: this.owner,
    status: 'pending'
  })
  
  try {
    await execution.markStarted()
    
    // Execute workflow nodes in order
    const result = await this.executeNodes(execution, inputData, context)
    
    await execution.markCompleted(result)
    
    // Update workflow performance
    this.performance.totalExecutions += 1
    this.performance.successfulExecutions += 1
    this.performance.lastExecutionTime = new Date()
    this.performance.successRate = this.calculatedSuccessRate
    
    // Add to recent executions
    this.recentExecutions.unshift({
      executionId: execution._id,
      status: 'completed',
      startTime: execution.startedAt,
      endTime: execution.completedAt,
      duration: execution.executionTime
    })
    
    // Keep only last 10 executions
    if (this.recentExecutions.length > 10) {
      this.recentExecutions = this.recentExecutions.slice(0, 10)
    }
    
    await this.save()
    
    return {
      success: true,
      executionId: execution._id,
      result
    }
    
  } catch (error) {
    await execution.markFailed(error)
    
    this.performance.totalExecutions += 1
    this.performance.failedExecutions += 1
    await this.save()
    
    throw error
  }
}

colonyWorkflowSchema.methods.executeNodes = async function(execution, inputData, context) {
  // Implementation for executing workflow nodes
  // This would contain the logic for traversing the workflow graph
  // and executing each node based on connections and conditions
  
  const results = {}
  
  // Find trigger nodes
  const triggerNodes = this.nodes.filter(node => node.type === 'trigger')
  
  for (const node of triggerNodes) {
    // Execute each trigger node and follow connections
    results[node.id] = await this.executeNode(node, inputData, context, execution)
  }
  
  return results
}

colonyWorkflowSchema.methods.executeNode = async function(node, inputData, context, execution) {
  // Node execution logic would be implemented here
  // This is a simplified version
  
  switch (node.type) {
    case 'agent':
      if (node.agentId) {
        const ColonyAgent = mongoose.model('ColonyAgent')
        const agent = await ColonyAgent.findById(node.agentId)
        if (agent) {
          return await agent.executeWithColony(inputData, context)
        }
      }
      break
    case 'condition':
      // Evaluate condition logic
      return this.evaluateCondition(node.config, inputData, context)
    case 'action':
      // Execute action
      return this.executeAction(node.config, inputData, context)
    default:
      return { success: true, message: `Node ${node.type} executed` }
  }
}

// Static methods
colonyWorkflowSchema.statics.getTemplates = function() {
  return this.find({
    'template.isTemplate': true,
    'template.isPublic': true,
    status: 'active'
  }).sort({ 'template.downloads': -1, 'template.rating': -1 })
}

export default mongoose.model('ColonyWorkflow', colonyWorkflowSchema)
