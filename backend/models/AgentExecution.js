import mongoose from 'mongoose'

// Execution Status Types
const EXECUTION_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  TIMEOUT: 'timeout'
}

// Execution Trigger Types
const TRIGGER_TYPES = {
  MANUAL: 'manual',           // User-initiated
  SCHEDULED: 'scheduled',     // Time-based trigger
  WEBHOOK: 'webhook',         // External API trigger
  EVENT: 'event',            // Internal event trigger
  AGENT: 'agent',            // Triggered by another agent
  WORKFLOW: 'workflow'        // Part of a workflow
}

const agentExecutionSchema = new mongoose.Schema({
  // Execution Identity
  executionId: {
    type: String,
    required: true,
    unique: true,
    default: () => `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },
  
  // Agent Reference
  agent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Agent',
    required: true
  },
  agentSnapshot: {
    // Store agent config at execution time for reproducibility
    name: String,
    version: String,
    specialization: String,
    aiConfig: mongoose.Schema.Types.Mixed,
    executionConfig: mongoose.Schema.Types.Mixed
  },
  
  // Execution Context
  trigger: {
    type: {
      type: String,
      enum: Object.values(TRIGGER_TYPES),
      required: true
    },
    source: String, // ID of triggering entity
    metadata: mongoose.Schema.Types.Mixed
  },
  
  // Input/Output Data
  input: {
    data: mongoose.Schema.Types.Mixed,
    schema: mongoose.Schema.Types.Mixed,
    validation: {
      isValid: Boolean,
      errors: [String]
    }
  },
  output: {
    data: mongoose.Schema.Types.Mixed,
    schema: mongoose.Schema.Types.Mixed,
    metadata: {
      tokensUsed: Number,
      modelUsed: String,
      processingTime: Number
    }
  },
  
  // Execution Status
  status: {
    type: String,
    enum: Object.values(EXECUTION_STATUS),
    default: 'pending'
  },
  progress: {
    percentage: { type: Number, default: 0 },
    currentStep: String,
    totalSteps: Number,
    completedSteps: Number
  },
  
  // Timing Information
  timing: {
    queuedAt: { type: Date, default: Date.now },
    startedAt: Date,
    completedAt: Date,
    duration: Number, // milliseconds
    timeout: Number   // milliseconds
  },
  
  // Resource Usage
  resources: {
    cpu: {
      peak: Number,
      average: Number,
      total: Number // CPU-seconds
    },
    memory: {
      peak: Number,
      average: Number
    },
    network: {
      bytesIn: Number,
      bytesOut: Number,
      requests: Number
    },
    storage: {
      bytesRead: Number,
      bytesWritten: Number
    }
  },
  
  // Error Handling
  error: {
    type: String,
    message: String,
    stack: String,
    code: String,
    retryable: Boolean
  },
  retries: {
    count: { type: Number, default: 0 },
    maxRetries: { type: Number, default: 3 },
    backoffStrategy: {
      type: String,
      enum: ['linear', 'exponential', 'fixed'],
      default: 'exponential'
    },
    history: [{
      attempt: Number,
      timestamp: Date,
      error: String,
      nextRetryAt: Date
    }]
  },
  
  // Colony Communication
  communications: [{
    timestamp: Date,
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    protocol: String,
    targetAgent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Agent'
    },
    message: mongoose.Schema.Types.Mixed,
    acknowledged: Boolean
  }],
  
  // Workflow Context (if part of a workflow)
  workflow: {
    workflowId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Workflow'
    },
    stepId: String,
    stepIndex: Number,
    dependencies: [{
      stepId: String,
      executionId: String,
      status: String
    }],
    nextSteps: [{
      stepId: String,
      condition: String,
      triggered: Boolean
    }]
  },
  
  // Billing & Cost Tracking
  billing: {
    computeUnits: Number,
    cost: {
      compute: Number,
      ai: Number,
      storage: Number,
      network: Number,
      total: Number
    },
    subscriptionTier: String,
    isOverage: Boolean
  },
  
  // Quality Metrics
  quality: {
    outputScore: Number, // 0-1 quality score
    userRating: Number,  // 1-5 user rating
    feedback: String,
    flagged: Boolean,
    flagReason: String
  },
  
  // Debugging & Monitoring
  logs: [{
    timestamp: Date,
    level: {
      type: String,
      enum: ['debug', 'info', 'warn', 'error']
    },
    message: String,
    metadata: mongoose.Schema.Types.Mixed
  }],
  traces: [{
    timestamp: Date,
    operation: String,
    duration: Number,
    metadata: mongoose.Schema.Types.Mixed
  }],
  
  // User Context
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  organization: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization'
  },
  
  // Environment
  environment: {
    type: String,
    enum: ['development', 'staging', 'production'],
    default: 'production'
  },
  region: String,
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
})

// Indexes for performance
agentExecutionSchema.index({ agent: 1, status: 1 })
agentExecutionSchema.index({ user: 1, createdAt: -1 })
agentExecutionSchema.index({ status: 1, 'timing.queuedAt': 1 })
agentExecutionSchema.index({ 'workflow.workflowId': 1, 'workflow.stepIndex': 1 })
agentExecutionSchema.index({ executionId: 1 }, { unique: true })
agentExecutionSchema.index({ 'trigger.type': 1, createdAt: -1 })

// Update timestamp on save
agentExecutionSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

// Calculate duration when execution completes
agentExecutionSchema.pre('save', function(next) {
  if (this.timing.startedAt && this.timing.completedAt && !this.timing.duration) {
    this.timing.duration = this.timing.completedAt - this.timing.startedAt
  }
  next()
})

// Methods
agentExecutionSchema.methods.markStarted = function() {
  this.status = 'running'
  this.timing.startedAt = new Date()
  this.progress.percentage = 0
  return this.save()
}

agentExecutionSchema.methods.markCompleted = function(output) {
  this.status = 'completed'
  this.timing.completedAt = new Date()
  this.progress.percentage = 100
  if (output) {
    this.output.data = output
  }
  return this.save()
}

agentExecutionSchema.methods.markFailed = function(error) {
  this.status = 'failed'
  this.timing.completedAt = new Date()
  this.error = {
    type: error.name || 'Error',
    message: error.message,
    stack: error.stack,
    code: error.code,
    retryable: error.retryable || false
  }
  return this.save()
}

agentExecutionSchema.methods.updateProgress = function(percentage, currentStep) {
  this.progress.percentage = Math.min(100, Math.max(0, percentage))
  if (currentStep) {
    this.progress.currentStep = currentStep
  }
  return this.save()
}

// Export constants
export { EXECUTION_STATUS, TRIGGER_TYPES }

const AgentExecution = mongoose.model('AgentExecution', agentExecutionSchema)
export default AgentExecution
