import mongoose from 'mongoose'

// Performance optimization: Enable schema optimization
mongoose.set('bufferCommands', false)

const emailSchema = new mongoose.Schema({
  subject: {
    type: String,
    required: true,
    trim: true
  },
  body: {
    type: String,
    required: true
  },
  dayDelay: {
    type: Number,
    required: true,
    min: 0
  },
  psychologyTriggers: [{
    type: String,
    enum: ['scarcity', 'urgency', 'social_proof', 'authority', 'reciprocity', 'commitment', 'liking', 'anchoring', 'loss_aversion', 'curiosity', 'trust']
  }],
  emotionalState: {
    type: String,
    enum: ['curiosity', 'concern', 'hope', 'trust', 'urgency', 'confidence', 'commitment']
  },
  nextEmotionalState: {
    type: String,
    enum: ['curiosity', 'concern', 'hope', 'trust', 'urgency', 'confidence', 'commitment']
  },
  framework: {
    type: String,
    enum: ['AIDA', 'PAS', 'StoryBrand', 'PASTOR', 'BeforeAfterBridge', 'HookStoryOffer']
  },
  microCommitment: String,
  objectionHandling: [String],
  authorityBuilders: [String],
  riskReversals: [String],
  conversionScore: {
    type: Number,
    min: 0,
    max: 100
  },
  subjectLineVariations: [{
    type: String,
    trim: true
  }],
  
  // A/B Testing
  abTesting: {
    enabled: {
      type: Boolean,
      default: false
    },
    testType: {
      type: String,
      enum: ['subject_line', 'email_content', 'send_time'],
      default: 'subject_line'
    },
    variants: [{
      name: String,
      content: String, // Subject line or email body
      sentCount: { type: Number, default: 0 },
      openCount: { type: Number, default: 0 },
      clickCount: { type: Number, default: 0 },
      conversionCount: { type: Number, default: 0 }
    }],
    trafficSplit: {
      type: Number,
      min: 10,
      max: 50,
      default: 50 // Percentage for variant B
    },
    winnerDetermined: {
      type: Boolean,
      default: false
    },
    winningVariant: String,
    testStartDate: Date,
    testEndDate: Date,
    significanceLevel: {
      type: Number,
      default: 95 // 95% confidence level
    }
  }
})

const emailSequenceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  // Business Information
  businessInfo: {
    industry: {
      type: String,
      required: true
    },
    productService: {
      type: String,
      required: true
    },
    targetAudience: {
      type: String,
      required: true
    },
    pricePoint: {
      type: String,
      required: true
    },
    uniqueSellingProposition: String,
    mainBenefit: String,
    painPoint: String
  },
  
  // AI Generation Settings
  generationSettings: {
    sequenceLength: {
      type: Number,
      required: true,
      min: 3,
      max: 14,
      default: 7
    },
    tone: {
      type: String,
      enum: ['professional', 'casual', 'friendly', 'authoritative', 'conversational'],
      default: 'professional'
    },
    primaryGoal: {
      type: String,
      enum: ['sales', 'nurture', 'onboarding', 'retention', 'upsell'],
      default: 'sales'
    },
    includeCTA: {
      type: Boolean,
      default: true
    },
    includePersonalization: {
      type: Boolean,
      default: true
    }
  },
  
  // Generated Emails
  emails: [emailSchema],
  
  // Performance Metrics (Enhanced with Colony Intelligence)
  performance: {
    // Traditional metrics
    totalSent: {
      type: Number,
      default: 0
    },
    totalOpened: {
      type: Number,
      default: 0
    },
    totalClicked: {
      type: Number,
      default: 0
    },
    totalConverted: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    },
    openRate: {
      type: Number,
      default: 0
    },
    clickRate: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    },
    // Colony Intelligence metrics (NEW)
    activations: {
      type: Number,
      default: 0,
      description: 'Number of times agent was activated'
    },
    synapticStrength: {
      type: Number,
      min: 0,
      max: 100,
      default: 75,
      description: 'Neural connection efficiency'
    },
    colonyContribution: {
      type: Number,
      default: 0,
      description: 'Contribution score to overall colony performance'
    },
    evolutionProgress: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
      description: 'Progress towards next evolution stage'
    }
  },
  
  // AI Analysis - Enhanced with Advanced Psychology
  aiAnalysis: {
    overallScore: {
      type: Number,
      min: 0,
      max: 100
    },
    psychologyDepth: {
      type: Number,
      min: 0,
      max: 100
    },
    emotionalJourney: {
      type: Number,
      min: 0,
      max: 100
    },
    conversionOptimization: {
      type: Number,
      min: 0,
      max: 100
    },
    industryAlignment: {
      type: Number,
      min: 0,
      max: 100
    },
    audienceResonance: {
      type: Number,
      min: 0,
      max: 100
    },
    strengths: [String],
    improvements: [String],
    predictedConversionRate: {
      type: Number,
      min: 0,
      max: 100
    },
    competitorComparison: String,
    competitorAdvantage: String,
    psychologyBreakdown: {
      cognitiveBiases: [String],
      emotionalTriggers: [String],
      persuasionSequence: String
    }
  },
  
  // Integration Settings
  integrations: {
    platform: {
      type: String,
      enum: ['mailchimp', 'convertkit', 'activecampaign', 'sendinblue', 'constant_contact', 'other']
    },
    listId: String,
    campaignId: String,
    lastSynced: Date
  },
  
  // Status and Metadata
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed', 'archived'],
    default: 'draft'
  },
  tags: [String],
  isTemplate: {
    type: Boolean,
    default: false
  },
  templateCategory: String,
  isPublic: {
    type: Boolean,
    default: false
  },
  
  // Neural Network Metrics (NEW)
  neuralComplexity: {
    type: Number,
    min: 1,
    max: 10,
    default: 5,
    description: 'Algorithm complexity rating (1-10)'
  },
  swarmSize: {
    type: Number,
    default: 3,
    description: 'Number of sub-agents in the swarm'
  },
  synapticStrength: {
    type: Number,
    min: 0,
    max: 100,
    default: 75,
    description: 'Connection quality percentage'
  },
  evolutionStage: {
    type: String,
    enum: ['nascent', 'developing', 'advanced', 'optimal', 'transcendent'],
    default: 'developing',
    description: 'Current optimization level'
  },
  
  // Generation metadata
  generatedAt: {
    type: Date,
    default: Date.now
  },
  generationTime: Number, // in milliseconds
  aiModel: {
    type: String,
    default: 'gpt-4'
  },
  version: {
    type: Number,
    default: 1
  },
  
  // Billing information
  billing: {
    isOverage: {
      type: Boolean,
      default: false
    },
    overageCharge: {
      type: Number,
      default: 0
    },
    billingPeriod: {
      type: String,
      default: function() {
        const now = new Date()
        return `${now.getFullYear()}-${now.getMonth() + 1}`
      }
    }
  }
}, {
  timestamps: true
})

// Indexes for performance
emailSequenceSchema.index({ user: 1, createdAt: -1 })
emailSequenceSchema.index({ 'businessInfo.industry': 1 })
emailSequenceSchema.index({ status: 1 })
emailSequenceSchema.index({ isTemplate: 1, isPublic: 1 })
emailSequenceSchema.index({ tags: 1 })

// Virtual for sequence length
emailSequenceSchema.virtual('emailCount').get(function() {
  return this.emails.length
})

// Method to calculate overall performance
emailSequenceSchema.methods.calculatePerformance = function() {
  if (this.performance.totalSent === 0) return
  
  this.performance.openRate = (this.performance.totalOpened / this.performance.totalSent) * 100
  this.performance.clickRate = (this.performance.totalClicked / this.performance.totalSent) * 100
  this.performance.conversionRate = (this.performance.totalConverted / this.performance.totalSent) * 100
}

// Method to get next email in sequence
emailSequenceSchema.methods.getNextEmail = function(daysSinceStart) {
  return this.emails.find(email => email.dayDelay === daysSinceStart)
}

// Method to export for email platform
emailSequenceSchema.methods.exportForPlatform = function(platform) {
  const baseExport = {
    title: this.title,
    description: this.description,
    emails: this.emails.map(email => ({
      subject: email.subject,
      body: email.body,
      delay: email.dayDelay
    }))
  }
  
  // Platform-specific formatting
  switch (platform) {
    case 'mailchimp':
      return {
        ...baseExport,
        type: 'automation',
        settings: {
          delay_type: 'day',
          delay_amount: 1
        }
      }
    case 'convertkit':
      return {
        ...baseExport,
        type: 'sequence',
        trigger: 'subscription'
      }
    default:
      return baseExport
  }
}

// Static method to get popular agent blueprints
emailSequenceSchema.statics.getPopularAgentBlueprints = function() {
  return this.find({
    isTemplate: true,
    isPublic: true
  })
  .sort({ 'performance.synapticStrength': -1, 'performance.conversionRate': -1 })
  .limit(10)
  .lean()
  .then(blueprints => blueprints.map(bp => ({
    ...bp,
    designation: bp.title,
    colonyType: bp.businessInfo?.industry || 'general',
    neuralComplexity: Math.floor(Math.random() * 5 + 3),
    synapticStrength: bp.performance?.synapticStrength || Math.floor(Math.random() * 30 + 70),
    evolutionStage: 'advanced'
  })))
}

// Static method to get agent blueprints by colony type
emailSequenceSchema.statics.getAgentBlueprintsByColonyType = function(colonyType) {
  // Map colony types to industries for backward compatibility
  const colonyToIndustryMap = {
    'communication': ['marketing', 'email', 'social'],
    'intelligence': ['analytics', 'data', 'research'],
    'evolution': ['optimization', 'growth', 'conversion'],
    'automation': ['workflow', 'process', 'integration']
  }
  
  const industries = colonyToIndustryMap[colonyType] || [colonyType]
  
  return this.find({
    isTemplate: true,
    isPublic: true,
    'businessInfo.industry': { $in: industries }
  })
  .sort({ 'performance.synapticStrength': -1, 'performance.conversionRate': -1 })
  .lean()
  .then(blueprints => blueprints.map(bp => ({
    ...bp,
    designation: bp.title,
    colonyType: colonyType,
    neuralComplexity: Math.floor(Math.random() * 5 + 3),
    synapticStrength: bp.performance?.synapticStrength || Math.floor(Math.random() * 30 + 70),
    swarmSize: Math.floor(Math.random() * 5 + 2),
    evolutionStage: 'advanced'
  })))
}

// A/B Testing Methods
emailSequenceSchema.methods.startABTest = function(testType, variants, trafficSplit) {
  this.emails.forEach(email => {
    email.abTesting = {
      enabled: true,
      testType,
      variants: variants.map(variant => ({
        name: variant.name,
        content: variant.content,
        sentCount: 0,
        openCount: 0,
        clickCount: 0,
        conversionCount: 0
      })),
      trafficSplit,
      winnerDetermined: false,
      testStartDate: new Date(),
      significanceLevel: 95
    }
  })
}

emailSequenceSchema.methods.determineABTestWinner = function(emailIndex) {
  const email = this.emails[emailIndex]
  if (!email?.abTesting?.enabled || email.abTesting.winnerDetermined) return null
  
  const variants = email.abTesting.variants
  if (variants.length < 2) return null
  
  // Calculate conversion rates
  const variantA = variants[0]
  const variantB = variants[1]
  
  const conversionRateA = variantA.sentCount > 0 ? (variantA.conversionCount / variantA.sentCount) * 100 : 0
  const conversionRateB = variantB.sentCount > 0 ? (variantB.conversionCount / variantB.sentCount) * 100 : 0
  
  // Simple statistical significance check (basic implementation)
  const minSampleSize = 100
  const totalSent = variantA.sentCount + variantB.sentCount
  
  if (totalSent >= minSampleSize) {
    const winner = conversionRateA > conversionRateB ? variantA.name : variantB.name
    const winningRate = Math.max(conversionRateA, conversionRateB)
    const losingRate = Math.min(conversionRateA, conversionRateB)
    const improvement = winningRate > 0 ? ((winningRate - losingRate) / losingRate * 100).toFixed(1) : 0
    
    email.abTesting.winnerDetermined = true
    email.abTesting.winningVariant = winner
    email.abTesting.testEndDate = new Date()
    
    return {
      winner,
      winningRate: winningRate.toFixed(1),
      improvement,
      sampleSize: totalSent,
      significant: improvement > 5 // Simple threshold
    }
  }
  
  return null
}

emailSequenceSchema.methods.getABTestResults = function() {
  return this.emails.map((email, index) => {
    if (!email.abTesting?.enabled) return null
    
    const variants = email.abTesting.variants.map(variant => ({
      name: variant.name,
      content: variant.content,
      sentCount: variant.sentCount,
      openRate: variant.sentCount > 0 ? ((variant.openCount / variant.sentCount) * 100).toFixed(1) : '0.0',
      clickRate: variant.sentCount > 0 ? ((variant.clickCount / variant.sentCount) * 100).toFixed(1) : '0.0',
      conversionRate: variant.sentCount > 0 ? ((variant.conversionCount / variant.sentCount) * 100).toFixed(1) : '0.0'
    }))
    
    return {
      emailIndex: index,
      emailSubject: email.subject,
      testType: email.abTesting.testType,
      variants,
      trafficSplit: email.abTesting.trafficSplit,
      winnerDetermined: email.abTesting.winnerDetermined,
      winningVariant: email.abTesting.winningVariant,
      testStartDate: email.abTesting.testStartDate,
      testEndDate: email.abTesting.testEndDate
    }
  }).filter(result => result !== null)
}

// Performance optimizations: Add compound indexes for common query patterns
// Removed duplicate - already defined at line 313
emailSequenceSchema.index({ user: 1, status: 1, createdAt: -1 }) // User sequences by status
emailSequenceSchema.index({ user: 1, 'businessInfo.industry': 1 }) // User sequences by industry
emailSequenceSchema.index({ 'businessInfo.industry': 1, createdAt: -1 }) // Industry analytics
emailSequenceSchema.index({ 'generationSettings.primaryGoal': 1, createdAt: -1 }) // Goal analytics
emailSequenceSchema.index({ user: 1, 'aiAnalysis.overallScore': -1 }) // Best sequences
emailSequenceSchema.index({ createdAt: -1, 'aiAnalysis.overallScore': -1 }) // Global best sequences
emailSequenceSchema.index({ user: 1, generatedAt: -1 }) // User sequences by generation date

// Text search index for sequence content
emailSequenceSchema.index({ 
  title: 'text', 
  description: 'text', 
  'businessInfo.industry': 'text',
  'businessInfo.productService': 'text'
})

// Performance optimization: Add lean query methods
emailSequenceSchema.statics.findUserSequencesLean = function(userId, limit = 10) {
  return this.find({ user: userId })
    .select('title createdAt emails.subject businessInfo.industry aiAnalysis.overallScore')
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean()
}

emailSequenceSchema.statics.findDashboardStatsLean = function(userId) {
  return this.find({ user: userId })
    .select('emails createdAt businessInfo.industry generationSettings.primaryGoal aiAnalysis')
    .lean()
}

// Add performance-optimized virtual fields
emailSequenceSchema.virtual('emailCount').get(function() {
  return this.emails ? this.emails.length : 0
})

emailSequenceSchema.virtual('avgScore').get(function() {
  return this.aiAnalysis?.overallScore || 0
})

// Performance: Ensure virtuals are included in JSON
emailSequenceSchema.set('toJSON', { virtuals: true })
emailSequenceSchema.set('toObject', { virtuals: true })

export default mongoose.model('EmailSequence', emailSequenceSchema)