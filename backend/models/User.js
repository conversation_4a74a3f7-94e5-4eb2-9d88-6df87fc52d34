import mongoose from 'mongoose'
import argon2 from 'argon2'

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  subscription: {
    type: {
      type: String,
      enum: ['free', 'pro', 'business', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'canceled', 'past_due', 'trialing', 'cancelled'],
      default: 'active'
    },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    paypalSubscriptionId: String,
    paymentProvider: {
      type: String,
      enum: ['stripe', 'paypal'],
      default: 'stripe'
    },
    currentPeriodEnd: Date,
    trialEnd: Date
  },
  monthlyUsage: {
    type: Number,
    default: 0
  },
  lastPaymentDate: Date,
  lastPaymentAmount: Number,
  paymentFailureCount: {
    type: Number,
    default: 0
  },
  usage: {
    currentPeriod: {
      sequencesGenerated: {
        type: Number,
        default: 0
      },
      overageSequences: {
        type: Number,
        default: 0
      },
      overageCharges: {
        type: Number,
        default: 0
      },
      startDate: {
        type: Date,
        default: Date.now
      },
      endDate: {
        type: Date,
        default: function() {
          const now = new Date()
          return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate())
        }
      }
    },
    notifications: {
      usage80Sent: {
        type: Boolean,
        default: false
      },
      usage95Sent: {
        type: Boolean,
        default: false
      },
      overageConsentGiven: {
        type: Boolean,
        default: false
      },
      lastNotificationDate: Date
    },
    history: [{
      period: String,
      sequencesGenerated: Number,
      overageSequences: Number,
      overageCharges: Number,
      createdAt: { type: Date, default: Date.now }
    }]
  },
  profile: {
    company: String,
    industry: String,
    website: String,
    phone: String
  },
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    marketingEmails: {
      type: Boolean,
      default: false
    }
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date,
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
})

// Indexes for performance
userSchema.index({ email: 1 }, { unique: true })
userSchema.index({ 'subscription.stripeCustomerId': 1 })

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  try {
    this.password = await argon2.hash(this.password, {
      type: argon2.argon2id,
      memoryCost: 2 ** 16, // 64 MB
      timeCost: 3,
      parallelism: 1
    })
    next()
  } catch (error) {
    next(error)
  }
})

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await argon2.verify(this.password, candidatePassword)
}

// Get subscription limits and overage rates
userSchema.methods.getSubscriptionLimits = function() {
  const limits = {
    free: {
      sequences: 5,
      emailsPerSequence: 5,
      templates: 3,
      overageRate: null // No overage for free tier
    },
    pro: {
      sequences: 75,
      emailsPerSequence: 15,
      templates: 20,
      overageRate: 3 // $3 per additional sequence
    },
    business: {
      sequences: 200,
      emailsPerSequence: 25,
      templates: -1, // unlimited
      overageRate: 3 // $3 per additional sequence
    }
  }
  
  return limits[this.subscription.type] || limits.free
}

// Check if billing period needs reset
userSchema.methods.checkAndResetPeriod = function() {
  const now = new Date()
  const periodEnd = new Date(this.usage.currentPeriod.endDate)
  
  if (now > periodEnd) {
    // Archive current period to history
    this.usage.history.push({
      period: `${periodEnd.getFullYear()}-${periodEnd.getMonth() + 1}`,
      sequencesGenerated: this.usage.currentPeriod.sequencesGenerated,
      overageSequences: this.usage.currentPeriod.overageSequences,
      overageCharges: this.usage.currentPeriod.overageCharges
    })
    
    // Reset current period
    this.usage.currentPeriod.sequencesGenerated = 0
    this.usage.currentPeriod.overageSequences = 0
    this.usage.currentPeriod.overageCharges = 0
    this.usage.currentPeriod.startDate = now
    this.usage.currentPeriod.endDate = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate())
    
    // Reset notifications
    this.usage.notifications.usage80Sent = false
    this.usage.notifications.usage95Sent = false
    
    return this.save()
  }
  
  return Promise.resolve(this)
}

// Check if user can generate sequences (with overage support)
userSchema.methods.canGenerateSequence = function() {
  const limits = this.getSubscriptionLimits()
  
  // Free tier has hard limits
  if (this.subscription.type === 'free') {
    return this.usage.currentPeriod.sequencesGenerated < limits.sequences
  }
  
  // Pro and Business can go into overage if consent given
  if (this.usage.currentPeriod.sequencesGenerated >= limits.sequences) {
    return this.usage.notifications.overageConsentGiven
  }
  
  return true
}

// Calculate usage statistics
userSchema.methods.getUsageStats = function() {
  const limits = this.getSubscriptionLimits()
  const generated = this.usage.currentPeriod.sequencesGenerated
  const overage = this.usage.currentPeriod.overageSequences
  const baseLimit = limits.sequences
  
  let usagePercentage = 0
  let status = 'normal'
  
  if (baseLimit > 0) {
    usagePercentage = Math.min((generated / baseLimit) * 100, 100)
    
    if (usagePercentage >= 95) {
      status = 'critical'
    } else if (usagePercentage >= 80) {
      status = 'warning'
    }
  }
  
  return {
    sequencesGenerated: generated,
    sequencesLimit: baseLimit,
    overageSequences: overage,
    overageCharges: this.usage.currentPeriod.overageCharges,
    usagePercentage: Math.round(usagePercentage),
    status,
    periodEnd: this.usage.currentPeriod.endDate,
    canGoOverage: limits.overageRate !== null,
    overageRate: limits.overageRate
  }
}

// Increment usage with overage handling
userSchema.methods.incrementUsage = async function() {
  await this.checkAndResetPeriod()
  
  const limits = this.getSubscriptionLimits()
  const beforeGenerated = this.usage.currentPeriod.sequencesGenerated
  
  this.usage.currentPeriod.sequencesGenerated += 1
  
  // Check if this pushes us into overage
  if (beforeGenerated >= limits.sequences && limits.overageRate) {
    this.usage.currentPeriod.overageSequences += 1
    this.usage.currentPeriod.overageCharges += limits.overageRate
  }
  
  return this.save()
}

// Check if usage notifications should be sent
userSchema.methods.shouldSendUsageNotification = function() {
  const stats = this.getUsageStats()
  const notifications = []
  
  if (stats.usagePercentage >= 80 && !this.usage.notifications.usage80Sent) {
    notifications.push('usage_80')
  }
  
  if (stats.usagePercentage >= 95 && !this.usage.notifications.usage95Sent) {
    notifications.push('usage_95')
  }
  
  // If at limit and can go into overage but hasn't consented
  if (stats.sequencesGenerated >= stats.sequencesLimit && 
      stats.canGoOverage && 
      !this.usage.notifications.overageConsentGiven) {
    notifications.push('overage_consent')
  }
  
  return notifications
}

// Mark notification as sent
userSchema.methods.markNotificationSent = function(type) {
  if (type === 'usage_80') {
    this.usage.notifications.usage80Sent = true
  } else if (type === 'usage_95') {
    this.usage.notifications.usage95Sent = true
  }
  
  this.usage.notifications.lastNotificationDate = new Date()
  return this.save()
}

// Give overage consent
userSchema.methods.giveOverageConsent = function() {
  this.usage.notifications.overageConsentGiven = true
  return this.save()
}

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return this.name
})

// Remove sensitive data when converting to JSON
userSchema.methods.toJSON = function() {
  const user = this.toObject()
  delete user.password
  delete user.emailVerificationToken
  delete user.passwordResetToken
  delete user.passwordResetExpires
  return user
}

export default mongoose.model('User', userSchema)