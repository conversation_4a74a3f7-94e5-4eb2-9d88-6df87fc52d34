import mongoose from 'mongoose'

const subscriptionSchema = new mongoose.Schema({
  plan: {
    type: String,
    enum: ['free', 'pro', 'business', 'enterprise'],
    default: 'free'
  },
  status: {
    type: String,
    enum: ['active', 'canceled', 'past_due', 'trialing'],
    default: 'active'
  },
  stripeCustomerId: String,
  stripeSubscriptionId: String,
  currentPeriodStart: Date,
  currentPeriodEnd: Date,
  cancelAtPeriodEnd: { type: Boolean, default: false },
  trialEnd: Date,
  price: { type: Number, default: 0 },
  billingInterval: {
    type: String,
    enum: ['month', 'year'],
    default: 'month'
  }
})

const usageSchema = new mongoose.Schema({
  currentPeriod: {
    sequencesGenerated: { type: Number, default: 0 },
    emailsSent: { type: Number, default: 0 },
    abTestsCreated: { type: Number, default: 0 },
    translationsGenerated: { type: Number, default: 0 },
    socialPostsCreated: { type: Number, default: 0 },
    overageSequences: { type: Number, default: 0 },
    overageCharges: { type: Number, default: 0 },
    startDate: { type: Date, default: Date.now },
    endDate: Date
  },
  totalLifetime: {
    sequencesGenerated: { type: Number, default: 0 },
    emailsSent: { type: Number, default: 0 },
    loginDays: { type: Number, default: 0 },
    features: { type: Map, of: Number, default: {} }
  },
  limits: {
    sequences: { type: Number, default: 5 },
    emails: { type: Number, default: 50 },
    abTests: { type: Number, default: 1 },
    translations: { type: Number, default: 5 },
    socialPosts: { type: Number, default: 10 }
  }
})

const onboardingSchema = new mongoose.Schema({
  completed: { type: Boolean, default: false },
  currentStep: { type: Number, default: 1 },
  stepsCompleted: [Number],
  industry: String,
  businessType: String,
  emailVolume: String,
  goals: [String],
  completedAt: Date,
  demoSequenceCreated: { type: Boolean, default: false }
})

const behaviorSchema = new mongoose.Schema({
  lastLogin: Date,
  loginCount: { type: Number, default: 0 },
  loginStreak: { type: Number, default: 0 },
  lastActiveFeature: String,
  featureUsage: { type: Map, of: Number, default: {} },
  conversionTriggers: [{
    type: String,
    triggeredAt: Date,
    action: String,
    result: String
  }],
  engagementScore: { type: Number, default: 0 },
  churnRisk: { type: Number, default: 0 },
  upgradeReadiness: { type: Number, default: 0 }
})

const teamSchema = new mongoose.Schema({
  name: String,
  role: {
    type: String,
    enum: ['owner', 'admin', 'editor', 'viewer'],
    default: 'owner'
  },
  members: [{
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    email: String,
    role: String,
    invitedAt: Date,
    joinedAt: Date,
    status: {
      type: String,
      enum: ['pending', 'active', 'suspended'],
      default: 'pending'
    }
  }],
  settings: {
    sharedSequences: { type: Boolean, default: true },
    sharedTemplates: { type: Boolean, default: true },
    sharedAnalytics: { type: Boolean, default: false }
  }
})

const preferencesSchema = new mongoose.Schema({
  theme: {
    type: String,
    enum: ['light', 'dark', 'system'],
    default: 'system'
  },
  language: {
    type: String,
    default: 'en'
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  notifications: {
    email: { type: Boolean, default: true },
    browser: { type: Boolean, default: true },
    marketing: { type: Boolean, default: false },
    updates: { type: Boolean, default: true }
  },
  aiPersonality: {
    type: String,
    enum: ['professional', 'creative', 'casual', 'luxury'],
    default: 'professional'
  },
  defaultTone: {
    type: String,
    enum: ['professional', 'friendly', 'casual', 'urgent', 'luxury', 'playful'],
    default: 'professional'
  }
})

const analyticsSchema = new mongoose.Schema({
  sequences: [{
    sequenceId: mongoose.Schema.Types.ObjectId,
    sent: { type: Number, default: 0 },
    opened: { type: Number, default: 0 },
    clicked: { type: Number, default: 0 },
    converted: { type: Number, default: 0 },
    unsubscribed: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    lastUpdate: { type: Date, default: Date.now }
  }],
  campaigns: [{
    name: String,
    sequences: [mongoose.Schema.Types.ObjectId],
    totalSent: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    roi: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now }
  }],
  performance: {
    avgOpenRate: { type: Number, default: 0 },
    avgClickRate: { type: Number, default: 0 },
    avgConversionRate: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    bestPerformingSequence: mongoose.Schema.Types.ObjectId
  }
})

const completeUserSchema = new mongoose.Schema({
  // Basic Info
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  
  // Account Status
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Subscription & Billing
  subscription: subscriptionSchema,
  
  // Usage Tracking
  usage: usageSchema,
  
  // Onboarding
  onboarding: onboardingSchema,
  
  // Behavior Analytics
  behavior: behaviorSchema,
  
  // Team Management
  team: teamSchema,
  
  // User Preferences
  preferences: preferencesSchema,
  
  // Analytics Data
  analytics: analyticsSchema,
  
  // API Access
  apiKey: String,
  apiKeyLastUsed: Date,
  webhookUrl: String,
  
  // Referrals
  referralCode: String,
  referredBy: mongoose.Schema.Types.ObjectId,
  referrals: [{
    userId: mongoose.Schema.Types.ObjectId,
    reward: Number,
    status: String,
    createdAt: Date
  }],
  
  // Admin Notes
  internalNotes: String,
  tags: [String],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastLoginAt: Date,
  deletedAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
completeUserSchema.index({ email: 1 })
completeUserSchema.index({ 'subscription.stripeCustomerId': 1 })
completeUserSchema.index({ 'subscription.plan': 1 })
completeUserSchema.index({ createdAt: -1 })
completeUserSchema.index({ 'behavior.lastLogin': -1 })

// Virtual for full name
completeUserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`
})

// Virtual for plan limits
completeUserSchema.virtual('planLimits').get(function() {
  const limits = {
    free: {
      sequences: 5,
      emails: 100,
      abTests: 1,
      translations: 3,
      socialPosts: 5,
      teamMembers: 1,
      support: 'community'
    },
    pro: {
      sequences: 75,
      emails: 5000,
      abTests: 10,
      translations: 50,
      socialPosts: 100,
      teamMembers: 3,
      support: 'email'
    },
    business: {
      sequences: 500,
      emails: 50000,
      abTests: 100,
      translations: 500,
      socialPosts: 1000,
      teamMembers: 25,
      support: 'priority'
    },
    enterprise: {
      sequences: -1, // unlimited
      emails: -1,
      abTests: -1,
      translations: -1,
      socialPosts: -1,
      teamMembers: -1,
      support: 'dedicated'
    }
  }
  
  return limits[this.subscription.plan] || limits.free
})

// Instance method to check if user can perform action
completeUserSchema.methods.canUse = function(feature) {
  const limits = this.planLimits
  const usage = this.usage.currentPeriod
  
  switch (feature) {
    case 'sequence':
      return limits.sequences === -1 || usage.sequencesGenerated < limits.sequences
    case 'email':
      return limits.emails === -1 || usage.emailsSent < limits.emails
    case 'abTest':
      return limits.abTests === -1 || usage.abTestsCreated < limits.abTests
    case 'translation':
      return limits.translations === -1 || usage.translationsGenerated < limits.translations
    case 'socialPost':
      return limits.socialPosts === -1 || usage.socialPostsCreated < limits.socialPosts
    default:
      return false
  }
}

// Instance method to record usage
completeUserSchema.methods.recordUsage = function(feature, amount = 1) {
  const usage = this.usage.currentPeriod
  const totalUsage = this.usage.totalLifetime
  
  switch (feature) {
    case 'sequence':
      usage.sequencesGenerated += amount
      totalUsage.sequencesGenerated += amount
      break
    case 'email':
      usage.emailsSent += amount
      totalUsage.emailsSent += amount
      break
    case 'abTest':
      usage.abTestsCreated += amount
      break
    case 'translation':
      usage.translationsGenerated += amount
      break
    case 'socialPost':
      usage.socialPostsCreated += amount
      break
  }
  
  return this.save()
}

// Instance method to calculate engagement score
completeUserSchema.methods.calculateEngagementScore = function() {
  const behavior = this.behavior
  const usage = this.usage.totalLifetime
  
  let score = 0
  
  // Login frequency (40% weight)
  if (behavior.loginCount > 30) score += 40
  else if (behavior.loginCount > 10) score += 30
  else if (behavior.loginCount > 3) score += 20
  else score += 10
  
  // Feature usage diversity (30% weight)
  const featuresUsed = Object.keys(behavior.featureUsage).length
  if (featuresUsed > 8) score += 30
  else if (featuresUsed > 5) score += 25
  else if (featuresUsed > 3) score += 20
  else score += 10
  
  // Content creation (20% weight)
  if (usage.sequencesGenerated > 20) score += 20
  else if (usage.sequencesGenerated > 10) score += 15
  else if (usage.sequencesGenerated > 3) score += 10
  else score += 5
  
  // Recency (10% weight)
  const daysSinceLogin = Math.floor((Date.now() - behavior.lastLogin) / (1000 * 60 * 60 * 24))
  if (daysSinceLogin < 1) score += 10
  else if (daysSinceLogin < 7) score += 8
  else if (daysSinceLogin < 30) score += 5
  else score += 0
  
  this.behavior.engagementScore = score
  return score
}

// Pre-save middleware
completeUserSchema.pre('save', function(next) {
  this.updatedAt = Date.now()
  
  // Calculate engagement score
  this.calculateEngagementScore()
  
  next()
})

// Static method to find users ready for upgrade
completeUserSchema.statics.findUpgradeOpportunities = function() {
  return this.find({
    'subscription.plan': 'free',
    'behavior.engagementScore': { $gte: 60 },
    'usage.currentPeriod.sequencesGenerated': { $gte: 4 } // Close to limit
  })
}

export default mongoose.model('CompleteUser', completeUserSchema)