import mongoose from 'mongoose'

const simpleSequenceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  emails: [{
    subject: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    emailNumber: {
      type: Number,
      required: true
    }
  }],
  tone: {
    type: String,
    enum: ['professional', 'casual', 'friendly', 'urgent'],
    default: 'professional'
  },
  industry: {
    type: String,
    default: 'general'
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed'],
    default: 'draft'
  },
  aiModel: {
    type: String,
    default: 'template'
  }
}, {
  timestamps: true
})

// Index for performance
simpleSequenceSchema.index({ user: 1, createdAt: -1 })

const SimpleSequence = mongoose.model('SimpleSequence', simpleSequenceSchema)

export default SimpleSequence