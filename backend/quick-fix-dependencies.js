#!/usr/bin/env node

/**
 * 🚨 EMERGENCY DEPENDENCY REPAIR
 * Quick fix to get the system operational
 */

import { execSync } from 'child_process'
import { writeFileSync } from 'fs'

console.log('🔧 EMERGENCY DEPENDENCY REPAIR - Installing core packages only...')

const corePackages = [
  // Essential runtime dependencies
  'express@^4.21.2',
  'mongoose@^8.16.1', 
  'redis@^4.7.1',
  'axios@^1.7.9',
  'dotenv@^16.6.0',
  'winston@^3.17.0',
  'jsonwebtoken@^9.0.2',
  'helmet@^7.2.0',
  'cors@^2.8.5',
  'compression@^1.7.5',
  'nodemailer@^6.10.1',
  'stripe@^14.25.0',
  'openai@^4.104.0'
]

const devPackages = [
  'nodemon@^3.1.10'
]

try {
  // Install core runtime packages
  console.log('📦 Installing core runtime packages...')
  for (const pkg of corePackages) {
    console.log(`   Installing ${pkg}...`)
    execSync(`npm install --save ${pkg}`, { stdio: 'pipe' })
  }

  // Install essential dev packages  
  console.log('🛠️ Installing essential dev packages...')
  for (const pkg of devPackages) {
    console.log(`   Installing ${pkg}...`)
    execSync(`npm install --save-dev ${pkg}`, { stdio: 'pipe' })
  }

  console.log('✅ Core dependencies installed successfully!')
  console.log('🚀 System should now be operational for basic functionality')
  
} catch (error) {
  console.error('❌ Installation failed:', error.message)
  process.exit(1)
}