{"name": "neurocolony-backend", "version": "2.0.0", "description": "NeuroColony Backend API - Minimal Working Version", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "echo 'Tests will be added after dependency resolution'", "deps:check": "npm outdated", "deps:audit": "npm audit", "deps:update": "npm update"}, "dependencies": {"@anthropic-ai/sdk": "^0.55.0", "@aws-sdk/client-s3": "^3.839.0", "@google/generative-ai": "^0.24.1", "@tensorflow/tfjs-node": "^4.22.0", "argon2": "^0.43.0", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "compromise": "^14.10.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^7.1.0", "html-pdf": "^3.0.1", "ioredis": "^5.6.1", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mongoose": "^8.0.0", "multer": "^2.0.1", "natural": "^6.12.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.4", "openai": "^5.8.2", "pdfkit": "^0.17.1", "rate-limit-redis": "^4.2.1", "redis": "^5.5.6", "socket.io": "^4.8.1", "stripe": "^18.2.1", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["neurocolony", "backend", "api"], "author": "NeuroColony Engineering Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}