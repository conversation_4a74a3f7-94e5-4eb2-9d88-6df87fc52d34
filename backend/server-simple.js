import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import mongoose from 'mongoose'
import dotenv from 'dotenv'
import { createServer } from 'http'

// Import basic routes only
import authRoutes from './routes/auth.js'
import sequenceRoutes from './routes/sequences-simple.js'
import paymentRoutes from './routes/payment.js'
import premiumAIRoutes from './routes/premiumAI.js'
import contactRoutes from './routes/contact.js'
import emailIntelligenceRoutes from './routes/email-intelligence.js'
import agentRoutes from './routes/agents.js'
import colonyIntelligenceRoutes from './routes/colony-intelligence.js'

// Import basic middleware
import { errorHandler } from './middleware/errorHandler.js'

// Import WebSocket service
import colonyWebSocketService from './services/colonyWebSocketService.js'

dotenv.config()

const app = express()
const server = createServer(app)
const PORT = process.env.PORT || 5002

// Basic middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3003', 
    'http://localhost:3010',
    'http://localhost:80',
    process.env.FRONTEND_URL || 'http://localhost:3010'
  ],
  credentials: true
}))
app.use(helmet())
app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000 // limit each IP to 1000 requests per windowMs
})
app.use(limiter)

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'NeuroColony Backend',
    version: '2.0.0'
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/sequences', sequenceRoutes)
app.use('/api/payment', paymentRoutes)
app.use('/api/premium-ai', premiumAIRoutes)
app.use('/api/contact', contactRoutes)
app.use('/api/email-intelligence', emailIntelligenceRoutes)
app.use('/api/agents', agentRoutes)
app.use('/api/colony-intelligence', colonyIntelligenceRoutes)

// Error handling
app.use(errorHandler)

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    console.log('✅ MongoDB connected successfully')
    console.log(`📊 Connected to database: ${mongoose.connection.name}`)
  } catch (error) {
    console.error('❌ MongoDB connection error:', error)
    process.exit(1)
  }
}

// Start server
const startServer = async () => {
  await connectDB()
  
  // Initialize WebSocket service
  colonyWebSocketService.initialize(server)
  
  server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 NeuroColony Backend running on port ${PORT}`)
    console.log(`📡 Health check: http://localhost:${PORT}/health`)
    console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL}`)
    console.log(`🔌 WebSocket service initialized for real-time colony updates`)
  })
}

startServer().catch((error) => {
  console.error('Failed to start server:', error)
  process.exit(1)
})