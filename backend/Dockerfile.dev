# Development Dockerfile for Backend
FROM node:18-alpine

# Install development tools and TensorFlow dependencies
RUN apk add --no-cache \
    curl \
    git \
    python3 \
    make \
    g++ \
    gcompat \
    libc6-compat \
    linux-headers

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies including dev dependencies
RUN npm install

# Install nodemon globally for development
RUN npm install -g nodemon

# Copy source code (will be mounted as volume in docker-compose)
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port
EXPOSE 5002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:5002/health || exit 1

# Default development command (can be overridden in docker-compose)
CMD ["nodemon", "server-simple.js"]