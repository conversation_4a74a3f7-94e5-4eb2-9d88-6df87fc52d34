/**
 * Optimized Server with Phase 3 System-Level Optimizations
 * Features: All optimizations integrated for maximum performance
 * Target: 2000+ req/s, 500ms response time, 40% memory reduction, 60% CPU efficiency
 */

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import mongoose from 'mongoose'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import cluster from 'cluster'
import os from 'os'

// Import all Phase 3 optimizers
import systemOptimizer from './services/systemOptimizer.js'
import databaseOptimizer from './services/databaseOptimizer.js'
import networkOptimizer from './services/networkOptimizer.js'
import cpuOptimizer from './services/cpuOptimizer.js'
import performanceMonitor from './services/performanceMonitor.js'
import hyperOptimizedCacheService from './services/hyperOptimizedCacheService.js'

// Import routes
import authRoutes from './routes/auth.js'
import sequenceRoutes from './routes/sequences.js'
import paymentRoutes from './routes/payments-simple.js'
import userRoutes from './routes/users.js'
import testRoutes from './routes/test.js'
import usageRoutes from './routes/usage.js'
import performanceRoutes from './routes/performance.js'

// Import middleware
import { errorHandler } from './middleware/errorHandler.js'
import { logger } from './utils/logger.js'
import { ConfigValidator } from './utils/configValidator.js'
import { queryMonitor } from './middleware/queryMonitor.js'
import { sessionManager } from './middleware/sessionManager.js'
import { distributedRateLimiter } from './middleware/distributedRateLimit.js'

dotenv.config()

// Cluster setup for production
if (cluster.isMaster && process.env.NODE_ENV === 'production') {
  const numWorkers = Math.min(os.cpus().length, 4)
  
  logger.info(`🔥 Master process ${process.pid} starting ${numWorkers} workers`)
  
  for (let i = 0; i < numWorkers; i++) {
    cluster.fork()
  }
  
  cluster.on('exit', (worker, code, signal) => {
    logger.warn(`Worker ${worker.process.pid} died. Restarting...`)
    cluster.fork()
  })
  
} else {
  // Worker process or development mode
  startOptimizedServer()
}

async function startOptimizedServer() {
  try {
    // Initialize all Phase 3 optimizations
    await initializeOptimizations()
    
    // Create optimized Express app
    const app = await createOptimizedApp()
    
    // Start server
    const PORT = process.env.PORT || 5000
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Optimized server running on port ${PORT}`)
      logger.info(`Environment: ${process.env.NODE_ENV}`)
      logger.info(`Process: ${process.pid}`)
    })
    
    // Optimize server settings
    server.keepAliveTimeout = 65000
    server.headersTimeout = 66000
    server.requestTimeout = 120000
    server.timeout = 120000
    
    // Graceful shutdown handler
    setupGracefulShutdown(server)
    
    return server
    
  } catch (error) {
    logger.error('Failed to start optimized server:', error)
    process.exit(1)
  }
}

/**
 * Initialize all Phase 3 optimizations
 */
async function initializeOptimizations() {
  logger.info('🔧 Initializing Phase 3 optimizations...')
  
  try {
    // Initialize system optimizer (includes all sub-optimizations)
    await systemOptimizer.applySystemOptimizations()
    
    // Initialize database optimizer
    await databaseOptimizer.initializeOptimizations()
    
    // Initialize network optimizer
    networkOptimizer.initializeOptimizations()
    
    // Initialize CPU optimizer
    await cpuOptimizer.initializeOptimizations()
    
    // Performance monitor is auto-initialized as singleton
    logger.info('📊 Performance monitoring active')
    
    // Cache service is auto-initialized as singleton
    logger.info('🔥 Hyper-optimized cache service active')
    
    logger.info('✅ All Phase 3 optimizations initialized successfully')
    
  } catch (error) {
    logger.error('Failed to initialize optimizations:', error)
    throw error
  }
}

/**
 * Create optimized Express application
 */
async function createOptimizedApp() {
  const app = express()
  
  // Validate configuration
  const validator = new ConfigValidator()
  const autoFixes = validator.autoFix()
  if (autoFixes.length > 0) {
    logger.info('🔧 Auto-fixed configuration issues:', autoFixes)
  }
  
  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"]
      }
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }))
  
  // Network optimization middleware
  const networkMiddleware = networkOptimizer.createOptimizedMiddleware()
  networkMiddleware.forEach(middleware => app.use(middleware))
  
  // Enhanced compression with Brotli support
  app.use(compression({
    filter: (req, res) => {
      if (req.headers['x-no-compression']) return false
      return compression.filter(req, res)
    },
    level: 6,
    threshold: 1024,
    chunkSize: 1024,
    windowBits: 15,
    memLevel: 8,
    brotliOptions: {
      level: 4,
      chunkSize: 1024
    }
  }))
  
  // Distributed rate limiting with Redis
  const rateLimiters = distributedRateLimiter.getAllLimiters()
  app.use('/api/auth/', rateLimiters.auth)
  app.use('/api/sequences/generate', rateLimiters.ai)
  app.use('/api/payments/', rateLimiters.payment)
  app.use('/api/', rateLimiters.burst)
  app.use('/api/', rateLimiters.api)
  
  // CORS configuration
  app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  }))
  
  // Session management with Redis
  const sessionMiddleware = sessionManager.createSessionMiddleware()
  app.use(sessionMiddleware)
  
  // Body parsing with optimized limits
  app.use(express.json({ 
    limit: '10mb',
    type: 'application/json'
  }))
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb',
    parameterLimit: 1000
  }))
  
  // Performance monitoring middleware
  app.use((req, res, next) => {
    req.startTime = Date.now()
    
    res.on('finish', () => {
      const responseTime = Date.now() - req.startTime
      
      // Track metrics
      performanceMonitor.emit('requestCompleted', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        responseTime,
        contentLength: res.get('Content-Length') || 0
      })
    })
    
    next()
  })
  
  // Connect to MongoDB with optimized settings
  await connectToDatabase()
  
  // Load routes
  setupRoutes(app)
  
  // Performance monitoring endpoints
  setupPerformanceEndpoints(app)
  
  // Error handling middleware
  app.use(errorHandler)
  
  return app
}

/**
 * Connect to MongoDB with all optimizations
 */
async function connectToDatabase() {
  logger.info('🔄 Connecting to MongoDB with optimizations...')
  
  const mongooseOptions = {
    // Connection pool optimization (Phase 3)
    maxPoolSize: 50,
    minPoolSize: 10,
    maxIdleTimeMS: 10000,
    waitQueueTimeoutMS: 2000,
    serverSelectionTimeoutMS: 2000,
    heartbeatFrequencyMS: 5000,
    
    // Performance settings
    bufferCommands: false,
    bufferMaxEntries: 0,
    
    // Read/Write optimization
    readPreference: 'primaryPreferred',
    readConcern: { level: 'local' },
    writeConcern: { w: 1, j: false },
    
    // Retry settings
    retryWrites: true,
    retryReads: true,
    
    // Compression (Phase 3)
    compressors: ['zstd', 'zlib'],
    zlibCompressionLevel: 6,
    
    // Connection optimization
    maxConnecting: 10,
    socketTimeoutMS: 45000
  }
  
  try {
    const uri = process.env.MONGODB_URI.replace('localhost', '127.0.0.1')
    await mongoose.connect(uri, mongooseOptions)
    
    logger.info('✅ Connected to MongoDB with Phase 3 optimizations')
    global.dbConnected = true
    
  } catch (error) {
    logger.warn('⚠️ MongoDB connection failed, starting in degraded mode:', error.message)
    global.dbConnected = false
  }
}

/**
 * Setup application routes
 */
function setupRoutes(app) {
  logger.info('🔄 Loading optimized routes...')
  
  try {
    app.use('/api/auth', authRoutes)
    app.use('/api/sequences', sequenceRoutes)
    app.use('/api/payments', paymentRoutes)
    app.use('/api/users', userRoutes)
    app.use('/api/usage', usageRoutes)
    app.use('/api/test', testRoutes)
    app.use('/api/performance', performanceRoutes)
    
    // Health check endpoints
    app.get('/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        database: global.dbConnected ? 'connected' : 'degraded',
        uptime: process.uptime(),
        process: process.pid,
        optimizations: 'Phase 3 Active'
      })
    })
    
    app.get('/api/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        database: global.dbConnected ? 'connected' : 'degraded',
        uptime: process.uptime(),
        process: process.pid,
        optimizations: 'Phase 3 Active'
      })
    })
    
    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
      })
    })
    
    logger.info('✅ All routes loaded successfully')
    
  } catch (error) {
    logger.error('❌ Route loading failed:', error)
    throw error
  }
}

/**
 * Setup performance monitoring endpoints
 */
function setupPerformanceEndpoints(app) {
  // Real-time performance metrics
  app.get('/api/performance/metrics', (req, res) => {
    const metrics = performanceMonitor.getPerformanceSummary()
    res.json({
      success: true,
      data: metrics,
      timestamp: Date.now()
    })
  })
  
  // System optimization stats
  app.get('/api/performance/optimizations', (req, res) => {
    const stats = {
      system: systemOptimizer.getPerformanceStats(),
      database: databaseOptimizer.getPerformanceStats(),
      network: networkOptimizer.getPerformanceStats(),
      cpu: cpuOptimizer.getPerformanceStats(),
      cache: hyperOptimizedCacheService.getAdvancedStats()
    }
    
    res.json({
      success: true,
      data: stats,
      timestamp: Date.now()
    })
  })
  
  // Force garbage collection (development only)
  if (process.env.NODE_ENV !== 'production') {
    app.post('/api/performance/gc', (req, res) => {
      const gcResult = systemOptimizer.forceGarbageCollection()
      res.json({
        success: true,
        data: gcResult,
        message: 'Garbage collection completed'
      })
    })
  }
  
  // Performance recommendations
  app.get('/api/performance/recommendations', (req, res) => {
    const summary = performanceMonitor.getPerformanceSummary()
    res.json({
      success: true,
      data: {
        recommendations: summary.recommendations || [],
        alerts: summary.alerts || [],
        overallScore: summary.overallScore
      }
    })
  })
  
  // Historical performance data
  app.get('/api/performance/history', (req, res) => {
    const timeRange = parseInt(req.query.timeRange) || 3600000 // 1 hour default
    const historical = performanceMonitor.getHistoricalData(timeRange)
    
    res.json({
      success: true,
      data: historical,
      timeRange,
      count: historical.length
    })
  })
  
  logger.info('✅ Performance monitoring endpoints configured')
}

/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown(server) {
  const shutdown = async (signal) => {
    logger.info(`🔄 Received ${signal}, starting graceful shutdown...`)
    
    try {
      // Stop accepting new requests
      server.close(async () => {
        logger.info('🔄 HTTP server closed')
        
        try {
          // Shutdown all optimizers
          await Promise.all([
            systemOptimizer.shutdown?.(),
            databaseOptimizer.runMaintenance?.(),
            networkOptimizer.shutdown?.(),
            cpuOptimizer.shutdown?.(),
            performanceMonitor.shutdown?.()
          ].filter(Boolean))
          
          // Close database connection
          if (global.dbConnected) {
            await mongoose.connection.close()
            logger.info('🔄 Database connection closed')
          }
          
          logger.info('✅ Graceful shutdown completed')
          process.exit(0)
          
        } catch (error) {
          logger.error('Error during shutdown:', error)
          process.exit(1)
        }
      })
      
      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout')
        process.exit(1)
      }, 30000)
      
    } catch (error) {
      logger.error('Shutdown error:', error)
      process.exit(1)
    }
  }
  
  // Handle shutdown signals
  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGINT', () => shutdown('SIGINT'))
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error)
    shutdown('UNCAUGHT_EXCEPTION')
  })
  
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection at:', promise, 'reason:', reason)
    shutdown('UNHANDLED_REJECTION')
  })
}

export default startOptimizedServer