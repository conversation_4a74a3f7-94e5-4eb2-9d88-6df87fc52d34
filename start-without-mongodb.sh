#!/bin/bash

# Start NeuroColony without MongoDB (degraded mode)
echo "🧠 Starting NeuroColony in degraded mode (without MongoDB)"
echo "========================================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Kill existing processes on ports
echo -e "${BLUE}Clearing ports...${NC}"
lsof -ti:5002 | xargs kill -9 2>/dev/null
lsof -ti:3004 | xargs kill -9 2>/dev/null
sleep 1

# Update backend .env to enable fallback mode
echo -e "${BLUE}Configuring backend for degraded mode...${NC}"
cd backend
if [ -f .env ]; then
    # Ensure DATABASE_FALLBACK is true
    if ! grep -q "DATABASE_FALLBACK=true" .env; then
        echo "DATABASE_FALLBACK=true" >> .env
    fi
fi

# Start backend
echo -e "${BLUE}Starting backend on port 5002...${NC}"
export PORT=5002
export DATABASE_FALLBACK=true
export DEMO_MODE=true
npm start &
BACKEND_PID=$!

echo -e "${YELLOW}Waiting for backend to start...${NC}"
sleep 5

# Start frontend
cd ../frontend
echo -e "${BLUE}Starting frontend on port 3004...${NC}"
npm run dev &
FRONTEND_PID=$!

echo -e "${YELLOW}Waiting for frontend to start...${NC}"
sleep 5

echo ""
echo -e "${GREEN}🎉 NeuroColony is starting in degraded mode!${NC}"
echo ""
echo -e "${BLUE}📱 Access URLs:${NC}"
echo -e "   🌐 Frontend: ${GREEN}http://localhost:3004${NC}"
echo -e "   🔧 Backend API: ${GREEN}http://localhost:5002/api${NC}"
echo -e "   🧪 Test endpoint: ${GREEN}http://localhost:5002/api/test${NC}"
echo ""
echo -e "${YELLOW}⚠️  Note: Running without database - some features may be limited${NC}"
echo ""
echo "Press Ctrl+C to stop all services"

# Trap cleanup
trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT TERM

# Keep running
wait