version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: neurocolony-postgres
    environment:
      POSTGRES_DB: neurocolony
      POSTGRES_USER: neurocolony
      POSTGRES_PASSWORD: neurocolony123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5435:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend-go/migrations:/docker-entrypoint-initdb.d
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U neurocolony -d neurocolony"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: neurocolony-redis
    ports:
      - "6382:6379"
    volumes:
      - redis_data:/data
    networks:
      - neurocolony-network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # MongoDB Database (Production)
  mongodb:
    image: mongo:6.0
    container_name: neurocolony-mongodb-prod
    environment:
      - MONGO_INITDB_DATABASE=neurocolony_production
    ports:
      - "27020:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - neurocolony-network
    restart: unless-stopped
    command: mongod --bind_ip_all --noauth

  # Node.js Backend (Production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: neurocolony-backend-prod
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/neurocolony_production
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - EMAIL_SERVICE_API_KEY=${EMAIL_SERVICE_API_KEY}
      - DOMAIN=neurocolony.com
    ports:
      - "5002:5000"
    depends_on:
      - mongodb
      - redis
    networks:
      - neurocolony-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Local AI Service (Ollama) - Currently disabled for deployment
  # Uncomment when AI integration is needed
  # ollama:
  #   image: ollama/ollama:latest
  #   container_name: neurocolony-ollama-prod
  #   ports:
  #     - "11435:11434"
  #   volumes:
  #     - ollama_data:/root/.ollama
  #   networks:
  #     - neurocolony-network
  #   restart: unless-stopped

  # React Frontend (Production)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: neurocolony-frontend-prod
    environment:
      - VITE_API_URL=http://localhost:5002/api
      - VITE_APP_NAME=NeuroColony
      - VITE_APP_VERSION=2.0.0
      - NODE_ENV=production
    ports:
      - "3010:3000"
    depends_on:
      - backend
    networks:
      - neurocolony-network
    restart: unless-stopped

  # Nginx Reverse Proxy (Disabled for deployment - port 80 in use)
  # nginx:
  #   image: nginx:alpine
  #   container_name: neurocolony-nginx
  #   ports:
  #     - "8080:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf
  #     - ./nginx/ssl:/etc/nginx/ssl
  #   depends_on:
  #     - backend
  #     - frontend
  #   networks:
  #     - neurocolony-network
  #   restart: unless-stopped

volumes:
  postgres_data:
  mongodb_data:
  ollama_data:
  redis_data:
  frontend_node_modules:

networks:
  neurocolony-network:
    driver: bridge