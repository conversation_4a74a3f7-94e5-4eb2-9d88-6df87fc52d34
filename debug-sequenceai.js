#!/usr/bin/env node

/**
 * NeuroColony v2.0 Ultra Debug & Validation Tool
 * Comprehensive system check and error detection
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class NeuroColonyDebugger {
  constructor() {
    this.errors = []
    this.warnings = []
    this.info = []
    this.startTime = Date.now()
  }

  log(level, message, details = null) {
    const timestamp = new Date().toISOString()
    const entry = { timestamp, level, message, details }
    
    if (level === 'error') this.errors.push(entry)
    else if (level === 'warning') this.warnings.push(entry)
    else this.info.push(entry)
    
    const colors = {
      error: '\x1b[31m',    // Red
      warning: '\x1b[33m',  // Yellow
      info: '\x1b[36m',     // Cyan
      success: '\x1b[32m',  // Green
      reset: '\x1b[0m'      // Reset
    }
    
    console.log(`${colors[level]}[${level.toUpperCase()}] ${message}${colors.reset}`)
    if (details) console.log(`  ${JSON.stringify(details, null, 2)}`)
  }

  checkFileExists(filePath, required = true) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (!exists) {
      this.log(required ? 'error' : 'warning', `Missing file: ${filePath}`)
      return false
    } else {
      this.log('success', `✓ Found: ${filePath}`)
      return true
    }
  }

  checkDirectoryStructure() {
    this.log('info', '🔍 Checking directory structure...')
    
    const requiredDirs = [
      'backend',
      'frontend',
      'backend/routes',
      'backend/services',
      'backend/models',
      'frontend/src',
      'frontend/src/components',
      'frontend/src/components/premium',
      'frontend/src/contexts',
      'frontend/src/styles'
    ]
    
    for (const dir of requiredDirs) {
      const fullPath = path.join(__dirname, dir)
      if (!fs.existsSync(fullPath)) {
        this.log('error', `Missing directory: ${dir}`)
      } else {
        this.log('success', `✓ Directory exists: ${dir}`)
      }
    }
  }

  checkBackendFiles() {
    this.log('info', '🔍 Checking backend files...')
    
    const backendFiles = [
      'backend/server.js',
      'backend/package.json',
      'backend/routes/ai-advanced.js',
      'backend/routes/social-integration.js',
      'backend/routes/ab-testing.js',
      'backend/services/aiService.js'
    ]
    
    for (const file of backendFiles) {
      this.checkFileExists(file, true)
    }
  }

  checkFrontendFiles() {
    this.log('info', '🔍 Checking frontend files...')
    
    const frontendFiles = [
      'frontend/src/App.jsx',
      'frontend/src/components/premium/UltraDashboard.jsx',
      'frontend/src/components/premium/UltraEmailGenerator.jsx',
      'frontend/src/components/premium/ThemeToggle.jsx',
      'frontend/src/contexts/ThemeContext.jsx',
      'frontend/src/styles/premium-theme.css'
    ]
    
    for (const file of frontendFiles) {
      this.checkFileExists(file, true)
    }
  }

  checkPackageJsonDependencies() {
    this.log('info', '🔍 Checking package.json dependencies...')
    
    try {
      const backendPackage = JSON.parse(
        fs.readFileSync(path.join(__dirname, 'backend/package.json'), 'utf8')
      )
      
      const requiredDeps = ['brain.js', 'natural', 'openai', 'express', 'mongoose']
      
      for (const dep of requiredDeps) {
        if (backendPackage.dependencies[dep]) {
          this.log('success', `✓ Backend dependency: ${dep}`)
        } else {
          this.log('error', `Missing backend dependency: ${dep}`)
        }
      }
    } catch (error) {
      this.log('error', 'Could not read backend package.json', error.message)
    }
  }

  checkDockerFiles() {
    this.log('info', '🔍 Checking Docker configuration...')
    
    const dockerFiles = [
      'docker-compose.yml',
      'backend/Dockerfile',
      'frontend/Dockerfile'
    ]
    
    for (const file of dockerFiles) {
      this.checkFileExists(file, false)
    }
  }

  validateJavaScriptSyntax() {
    this.log('info', '🔍 Validating JavaScript syntax...')
    
    const jsFiles = [
      'backend/server.js',
      'backend/routes/ai-advanced.js',
      'frontend/src/App.jsx'
    ]
    
    for (const file of jsFiles) {
      try {
        const fullPath = path.join(__dirname, file)
        if (fs.existsSync(fullPath)) {
          const content = fs.readFileSync(fullPath, 'utf8')
          
          // Basic syntax validation
          if (content.includes('import') && !content.includes('export')) {
            this.log('warning', `File ${file} has imports but no exports`)
          }
          
          // Check for common issues
          const lines = content.split('\n')
          lines.forEach((line, index) => {
            if (line.includes('console.log') && !line.includes('//')) {
              this.log('warning', `Debug console.log found in ${file}:${index + 1}`)
            }
          })
          
          this.log('success', `✓ Syntax OK: ${file}`)
        }
      } catch (error) {
        this.log('error', `Syntax error in ${file}`, error.message)
      }
    }
  }

  checkEnvironmentVariables() {
    this.log('info', '🔍 Checking environment configuration...')
    
    const envFile = path.join(__dirname, 'backend/.env')
    if (fs.existsSync(envFile)) {
      try {
        const envContent = fs.readFileSync(envFile, 'utf8')
        const envVars = [
          'MONGODB_URI',
          'JWT_SECRET',
          'OPENAI_API_KEY',
          'STRIPE_SECRET_KEY'
        ]
        
        for (const envVar of envVars) {
          if (envContent.includes(envVar)) {
            this.log('success', `✓ Environment variable: ${envVar}`)
          } else {
            this.log('warning', `Missing environment variable: ${envVar}`)
          }
        }
      } catch (error) {
        this.log('error', 'Could not read .env file', error.message)
      }
    } else {
      this.log('warning', 'No .env file found in backend directory')
    }
  }

  checkMemoryUsage() {
    this.log('info', '🔍 Checking system resources...')
    
    const used = process.memoryUsage()
    const memoryInfo = {
      rss: `${Math.round(used.rss / 1024 / 1024 * 100) / 100} MB`,
      heapTotal: `${Math.round(used.heapTotal / 1024 / 1024 * 100) / 100} MB`,
      heapUsed: `${Math.round(used.heapUsed / 1024 / 1024 * 100) / 100} MB`,
      external: `${Math.round(used.external / 1024 / 1024 * 100) / 100} MB`
    }
    
    this.log('info', 'Memory usage:', memoryInfo)
    
    if (used.heapUsed / used.heapTotal > 0.9) {
      this.log('warning', 'High memory usage detected')
    }
  }

  generateReport() {
    const duration = Date.now() - this.startTime
    
    console.log('\n' + '='.repeat(60))
    console.log('🚀 SEQUENCEAI v2.0 ULTRA DEBUG REPORT')
    console.log('='.repeat(60))
    
    console.log(`\n⏱️  Duration: ${duration}ms`)
    console.log(`✅ Info: ${this.info.length}`)
    console.log(`⚠️  Warnings: ${this.warnings.length}`)
    console.log(`❌ Errors: ${this.errors.length}`)
    
    if (this.errors.length > 0) {
      console.log('\n❌ CRITICAL ERRORS:')
      this.errors.forEach(error => {
        console.log(`  • ${error.message}`)
      })
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:')
      this.warnings.forEach(warning => {
        console.log(`  • ${warning.message}`)
      })
    }
    
    console.log('\n🏆 OVERALL STATUS:')
    if (this.errors.length === 0 && this.warnings.length < 3) {
      console.log('✅ EXCELLENT - NeuroColony v2.0 Ultra is ready for production!')
    } else if (this.errors.length === 0) {
      console.log('⚠️  GOOD - Minor issues detected, but system is functional')
    } else {
      console.log('❌ NEEDS ATTENTION - Critical errors must be fixed')
    }
    
    console.log('\n🎯 NEXT STEPS:')
    console.log('  1. Fix any critical errors listed above')
    console.log('  2. Address warnings for optimal performance')
    console.log('  3. Run: docker-compose up --build')
    console.log('  4. Access: http://localhost:3001')
    console.log('  5. Test ultra routes: /dashboard-ultra, /generator-ultra')
    
    console.log('\n' + '='.repeat(60))
    
    // Save detailed report
    const reportPath = path.join(__dirname, 'debug-report.json')
    const detailedReport = {
      timestamp: new Date().toISOString(),
      duration,
      summary: {
        info: this.info.length,
        warnings: this.warnings.length,
        errors: this.errors.length
      },
      details: {
        info: this.info,
        warnings: this.warnings,
        errors: this.errors
      }
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(detailedReport, null, 2))
    console.log(`📄 Detailed report saved: ${reportPath}`)
  }

  async run() {
    console.log('🚀 Starting NeuroColony v2.0 Ultra Debug & Validation...\n')
    
    this.checkDirectoryStructure()
    this.checkBackendFiles()
    this.checkFrontendFiles()
    this.checkPackageJsonDependencies()
    this.checkDockerFiles()
    this.validateJavaScriptSyntax()
    this.checkEnvironmentVariables()
    this.checkMemoryUsage()
    
    this.generateReport()
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const debugTool = new NeuroColonyDebugger()
  debugTool.run().catch(console.error)
}

export default NeuroColonyDebugger