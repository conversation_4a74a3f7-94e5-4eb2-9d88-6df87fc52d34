#!/bin/bash

echo "🚀 SequenceAI Railway Deployment Script"
echo "======================================="

echo "📦 Installing Railway CLI..."
npm install -g @railway/cli

echo "🔐 Login to Railway..."
echo "This will open your browser for authentication"
railway login

echo "🆕 Creating new Railway project..."
railway new

echo "🔧 Deploying backend and database..."
railway up

echo "📊 Setting up databases..."
echo "Adding MongoDB..."
railway add mongodb
echo "Adding Redis..."
railway add redis

echo "🔑 Setting environment variables..."
echo "Please set these in the Railway dashboard:"
echo "- JWT_SECRET=your-jwt-secret-here"
echo "- STRIPE_SECRET_KEY=sk_test_your-stripe-key"
echo "- STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret"
echo "- PAYPAL_CLIENT_ID=your-paypal-client-id"
echo "- PAYPAL_CLIENT_SECRET=your-paypal-secret"
echo "- SMTP_HOST=smtp.gmail.com"
echo "- SMTP_USER=<EMAIL>"
echo "- SMTP_PASS=your-app-password"

echo "✅ Railway deployment initiated!"
echo "📋 Next steps:"
echo "1. Go to railway.app/dashboard"
echo "2. Configure environment variables"
echo "3. Get your Railway app URL"
echo "4. Update frontend VITE_API_URL to point to Railway"