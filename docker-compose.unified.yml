version: '3.8'

services:
  # API Gateway (Kong)
  gateway:
    image: kong:3.4-alpine
    container_name: neurocolony-gateway
    environment:
      KONG_DATABASE: 'off'
      KONG_DECLARATIVE_CONFIG: /kong/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: '0.0.0.0:8001'
    ports:
      - "8000:8000"  # Proxy port
      - "8001:8001"  # Admin API
    volumes:
      - ./infrastructure/kong/kong.yml:/kong/kong.yml:ro
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: neurocolony-frontend
    environment:
      - VITE_API_URL=http://localhost:8000/api
      - NODE_ENV=production
    ports:
      - "3000:80"
    networks:
      - neurocolony-network
    depends_on:
      - gateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Core API Service
  api-service:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: neurocolony-api
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/neurocolony
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_NAME=api-service
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - neurocolony-network
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Engine Service
  ai-engine:
    build:
      context: ./services/ai-engine
      dockerfile: Dockerfile
    container_name: neurocolony-ai-engine
    environment:
      - NODE_ENV=production
      - PORT=3002
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - SERVICE_NAME=ai-engine
    networks:
      - neurocolony-network
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Engine Service
  workflow-engine:
    build:
      context: ./services/workflow-engine
      dockerfile: Dockerfile
    container_name: neurocolony-workflow
    environment:
      - NODE_ENV=production
      - PORT=3003
      - DATABASE_URL=postgres://postgres:${POSTGRES_PASSWORD}@postgres:5432/workflows
      - REDIS_URL=redis://redis:6379
      - SERVICE_NAME=workflow-engine
    networks:
      - neurocolony-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Engine Service
  analytics-engine:
    build:
      context: ./services/analytics-engine
      dockerfile: Dockerfile
    container_name: neurocolony-analytics
    environment:
      - PYTHON_ENV=production
      - PORT=3004
      - CLICKHOUSE_URL=clickhouse://clickhouse:8123/analytics
      - REDIS_URL=redis://redis:6379
      - SERVICE_NAME=analytics-engine
    networks:
      - neurocolony-network
    depends_on:
      clickhouse:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB
  mongo:
    image: mongo:7.0
    container_name: neurocolony-mongo
    environment:
      - MONGO_INITDB_DATABASE=neurocolony
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-changeme}
    volumes:
      - mongo_data:/data/db
      - ./infrastructure/mongo/init.js:/docker-entrypoint-initdb.d/init.js:ro
    ports:
      - "27017:27017"
    networks:
      - neurocolony-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: neurocolony-postgres
    environment:
      - POSTGRES_DB=neurocolony
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-changeme}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis
  redis:
    image: redis:7-alpine
    container_name: neurocolony-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-changeme}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ClickHouse for Analytics
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: neurocolony-clickhouse
    environment:
      - CLICKHOUSE_DB=analytics
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-changeme}
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./infrastructure/clickhouse/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native interface
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "clickhouse-client", "-q", "SELECT 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx (Load Balancer for multiple API instances)
  nginx:
    image: nginx:alpine
    container_name: neurocolony-nginx
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "443:443"
      - "80:80"
    networks:
      - neurocolony-network
    depends_on:
      - gateway
      - frontend
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: neurocolony-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: neurocolony-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-changeme}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3001:3000"
    networks:
      - neurocolony-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger for Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: neurocolony-jaeger
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"  # Jaeger UI
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - neurocolony-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:14269/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongo_data:
  postgres_data:
  redis_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:

networks:
  neurocolony-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16