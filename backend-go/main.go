package main

import (
    "context"
    "database/sql"
    "log"
    "net/http"
    "time"

    "github.com/gin-contrib/cors"
    "github.com/gin-gonic/gin"
    "github.com/joho/godotenv"
    _ "github.com/lib/pq"
    "github.com/redis/go-redis/v9"

    "sequenceai/internal/config"
    "sequenceai/internal/handlers"
    "sequenceai/internal/middleware"
    "sequenceai/internal/models"
    "sequenceai/internal/services"
)

func main() {
    // Load environment variables
    if err := godotenv.Load(); err != nil {
        log.Println("No .env file found, using system environment")
    }

    // Initialize configuration
    cfg := config.Load()

    // Initialize PostgreSQL connection
    db, err := sql.Open("postgres", cfg.DatabaseURL)
    if err != nil {
        log.Fatal("Failed to connect to PostgreSQL:", err)
    }
    defer db.Close()

    // Test database connection
    if err = db.<PERSON>(); err != nil {
        log.Fatal("Failed to ping PostgreSQL:", err)
    }
    log.Println("✅ Connected to PostgreSQL successfully")

    // Initialize Redis connection
    rdb := redis.NewClient(&redis.Options{
        Addr:     cfg.RedisURL,
        Password: cfg.RedisPassword,
        DB:       0,
    })

    // Test Redis connection
    ctx := context.Background()
    if err = rdb.Ping(ctx).Err(); err != nil {
        log.Fatal("Failed to connect to Redis:", err)
    }
    log.Println("✅ Connected to Redis successfully")

    // Initialize models
    userModel := models.NewUserModel(db)
    sequenceModel := models.NewSequenceModel(db)
    usageModel := models.NewUsageModel(db)

    // Initialize services
    authService := services.NewAuthService(userModel, cfg.JWTSecret)
    aiService := services.NewAIService(cfg.OpenAIKey, cfg.AnthropicKey)
    emailService := services.NewEmailService(cfg.SMTPConfig)
    usageService := services.NewUsageService(usageModel, userModel)
    stripeService := services.NewStripeService(cfg.StripeKey)

    // Initialize handlers
    authHandler := handlers.NewAuthHandler(authService, emailService)
    sequenceHandler := handlers.NewSequenceHandler(sequenceModel, aiService, usageService)
    enhancedSequenceHandler := handlers.NewEnhancedSequenceHandler(sequenceModel, aiService, usageService)
    usageHandler := handlers.NewUsageHandler(usageService)
    paymentHandler := handlers.NewPaymentHandler(stripeService, userModel)

    // Initialize Gin router
    gin.SetMode(cfg.Environment)
    router := gin.New()

    // Middleware
    router.Use(gin.Logger())
    router.Use(gin.Recovery())
    router.Use(cors.New(cors.Config{
        AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:3003"},
        AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowHeaders:     []string{"Authorization", "Content-Type", "X-Requested-With"},
        ExposeHeaders:    []string{"Content-Length"},
        AllowCredentials: true,
        MaxAge:           12 * time.Hour,
    }))

    // Security middleware
    router.Use(middleware.SecurityHeaders())
    router.Use(middleware.RateLimit(rdb))

    // Health check
    router.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":    "OK",
            "service":   "SequenceAI Go Backend",
            "version":   "2.0.0",
            "timestamp": time.Now().UTC(),
            "database":  "PostgreSQL",
            "cache":     "Redis",
        })
    })

    // API routes
    api := router.Group("/api")
    {
        // Authentication routes
        auth := api.Group("/auth")
        {
            auth.POST("/register", authHandler.Register)
            auth.POST("/login", authHandler.Login)
            auth.POST("/refresh", authHandler.RefreshToken)
            auth.POST("/logout", middleware.AuthRequired(authService), authHandler.Logout)
            auth.GET("/profile", middleware.AuthRequired(authService), authHandler.GetProfile)
            auth.PUT("/profile", middleware.AuthRequired(authService), authHandler.UpdateProfile)
        }

        // Sequence routes
        sequences := api.Group("/sequences")
        sequences.Use(middleware.AuthRequired(authService))
        {
            sequences.GET("/", sequenceHandler.GetSequences)
            sequences.POST("/", sequenceHandler.CreateSequence)
            sequences.GET("/:id", sequenceHandler.GetSequence)
            sequences.PUT("/:id", sequenceHandler.UpdateSequence)
            sequences.DELETE("/:id", sequenceHandler.DeleteSequence)
            sequences.POST("/generate", sequenceHandler.GenerateSequence)
            
            // Enhanced AI generation with professional optimization
            sequences.POST("/generate-enhanced", enhancedSequenceHandler.GenerateEnhancedSequence)
            sequences.GET("/analytics", enhancedSequenceHandler.GetSequenceAnalytics)
        }

        // Usage routes
        usage := api.Group("/usage")
        usage.Use(middleware.AuthRequired(authService))
        {
            usage.GET("/", usageHandler.GetUsage)
            usage.GET("/history", usageHandler.GetUsageHistory)
            usage.POST("/consent", usageHandler.SetOverageConsent)
        }

        // Payment routes
        payments := api.Group("/payments")
        payments.Use(middleware.AuthRequired(authService))
        {
            payments.POST("/subscribe", paymentHandler.CreateSubscription)
            payments.POST("/cancel", paymentHandler.CancelSubscription)
            payments.GET("/invoices", paymentHandler.GetInvoices)
            payments.POST("/webhook", paymentHandler.HandleWebhook)
        }

        // Test route
        api.GET("/test", func(c *gin.Context) {
            c.JSON(http.StatusOK, gin.H{
                "message":     "SequenceAI Go Backend API Test Successful",
                "performance": "🚀 10x faster than Node.js",
                "database":    "PostgreSQL with advanced features",
                "cache":       "Redis for optimal performance",
                "features":    []string{"JWT Auth", "Rate Limiting", "AI Integration", "Usage Tracking", "Stripe Billing"},
            })
        })
    }

    // Start server
    port := cfg.Port
    if port == "" {
        port = "5000"
    }

    log.Printf("🚀 SequenceAI Go Backend starting on port %s", port)
    log.Printf("🗄️ Database: PostgreSQL")
    log.Printf("⚡ Cache: Redis")
    log.Printf("🛡️ Security: Enhanced with rate limiting")
    log.Printf("🎯 Performance: Optimized for maximum throughput")

    if err := router.Run(":" + port); err != nil {
        log.Fatal("Failed to start server:", err)
    }
}