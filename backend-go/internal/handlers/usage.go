package handlers

import (
    "net/http"

    "github.com/gin-gonic/gin"
    "sequenceai/internal/services"
)

type UsageHandler struct {
    usageService *services.UsageService
}

func NewUsageHandler(usageService *services.UsageService) *UsageHandler {
    return &UsageHandler{
        usageService: usageService,
    }
}

func (h *UsageHandler) GetUsage(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    usage, err := h.usageService.GetCurrentUsage(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch usage"})
        return
    }

    status, percentage, err := h.usageService.GetUsageStatus(userID.(int))
    if err != nil {
        c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to calculate usage status"})
        return
    }

    canGenerate, err := h.usageService.CanGenerateSequence(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check generation capability"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "usage":       usage,
        "status":      status,
        "percentage":  percentage,
        "can_generate": canGenerate,
    })
}

func (h *UsageHandler) GetUsageHistory(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    history, err := h.usageService.GetUsageHistory(userID.(int), 12) // Last 12 months
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch usage history"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"history": history})
}

func (h *UsageHandler) SetOverageConsent(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req struct {
        Consent bool `json:"consent"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.usageService.SetOverageConsent(userID.(int), req.Consent); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update overage consent"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "message": "Overage consent updated successfully",
        "consent": req.Consent,
    })
}