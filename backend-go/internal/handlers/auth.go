package handlers

import (
    "net/http"

    "github.com/gin-gonic/gin"
    "github.com/go-playground/validator/v10"
    "sequenceai/internal/services"
)

type AuthHandler struct {
    authService  *services.AuthService
    emailService *services.EmailService
    validator    *validator.Validate
}

type RegisterRequest struct {
    Name     string `json:"name" validate:"required,min=2,max=100"`
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8,max=100"`
}

type LoginRequest struct {
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required"`
}

type AuthResponse struct {
    User  interface{} `json:"user"`
    Token string      `json:"token"`
}

func NewAuthHandler(authService *services.AuthService, emailService *services.EmailService) *AuthHandler {
    return &AuthHandler{
        authService:  authService,
        emailService: emailService,
        validator:    validator.New(),
    }
}

func (h *<PERSON>th<PERSON>andler) Register(c *gin.Context) {
    var req RegisterRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
        return
    }

    user, err := h.authService.Register(req.Name, req.Email, req.Password)
    if err != nil {
        if err.Error() == "user already exists" {
            c.JSON(http.StatusConflict, gin.H{"error": "User already exists"})
            return
        }
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
        return
    }

    token, err := h.authService.GenerateToken(user)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
        return
    }

    // Send welcome email (non-blocking)
    go h.emailService.SendWelcomeEmail(user.Email, user.Name)

    c.JSON(http.StatusCreated, AuthResponse{
        User:  user,
        Token: token,
    })
}

func (h *AuthHandler) Login(c *gin.Context) {
    var req LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed"})
        return
    }

    user, token, err := h.authService.Login(req.Email, req.Password)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
        return
    }

    c.JSON(http.StatusOK, AuthResponse{
        User:  user,
        Token: token,
    })
}

func (h *AuthHandler) RefreshToken(c *gin.Context) {
    authHeader := c.GetHeader("Authorization")
    if authHeader == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Authorization header required"})
        return
    }

    // Extract token (assuming "Bearer <token>" format)
    if len(authHeader) < 8 || authHeader[:7] != "Bearer " {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid authorization header format"})
        return
    }

    oldToken := authHeader[7:]
    claims, err := h.authService.ValidateToken(oldToken)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
        return
    }

    user, err := h.authService.GetUserByID(claims.UserID)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    newToken, err := h.authService.GenerateToken(user)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
        return
    }

    c.JSON(http.StatusOK, AuthResponse{
        User:  user,
        Token: newToken,
    })
}

func (h *AuthHandler) Logout(c *gin.Context) {
    // In a stateless JWT system, logout is handled client-side by removing the token
    // In a more sophisticated system, you might want to blacklist the token
    c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

func (h *AuthHandler) GetProfile(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    user, err := h.authService.GetUserByID(userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"user": user})
}

func (h *AuthHandler) UpdateProfile(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req struct {
        Name string `json:"name" validate:"required,min=2,max=100"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed"})
        return
    }

    user, err := h.authService.GetUserByID(userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    user.Name = req.Name
    // Here you would call a user update method
    // userModel.Update(user)

    c.JSON(http.StatusOK, gin.H{
        "message": "Profile updated successfully",
        "user":    user,
    })
}