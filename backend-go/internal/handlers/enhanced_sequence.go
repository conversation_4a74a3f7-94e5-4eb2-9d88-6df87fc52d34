package handlers

import (
    "fmt"
    "net/http"
    "strconv"
    "strings"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/go-playground/validator/v10"
    "sequenceai/internal/models"
    "sequenceai/internal/services"
)

type EnhancedSequenceHandler struct {
    sequenceModel *models.SequenceModel
    aiService     *services.AIService
    usageService  *services.UsageService
    validator     *validator.Validate
}

type EnhancedGenerateRequest struct {
    Industry    string `json:"industry" validate:"required,max=100"`
    Audience    string `json:"audience" validate:"required,max=500"`
    Goal        string `json:"goal" validate:"required,max=500"`
    Tone        string `json:"tone" validate:"required,max=50"`
    Length      int    `json:"length" validate:"required,min=3,max=10"`
    AIModel     string `json:"aiModel" validate:"required"`
    CustomName  string `json:"customName,omitempty" validate:"max=255"`
}

type SequenceAnalytics struct {
    TotalSequences    int                    `json:"total_sequences"`
    ByIndustry       map[string]int         `json:"by_industry"`
    ByTone           map[string]int         `json:"by_tone"`
    ByAIModel        map[string]int         `json:"by_ai_model"`
    AverageLength    float64                `json:"average_length"`
    RecentActivity   []SequenceActivity     `json:"recent_activity"`
    PopularPrompts   []string               `json:"popular_prompts"`
}

type SequenceActivity struct {
    Date  string `json:"date"`
    Count int    `json:"count"`
}

func NewEnhancedSequenceHandler(sequenceModel *models.SequenceModel, aiService *services.AIService, usageService *services.UsageService) *EnhancedSequenceHandler {
    return &EnhancedSequenceHandler{
        sequenceModel: sequenceModel,
        aiService:     aiService,
        usageService:  usageService,
        validator:     validator.New(),
    }
}

func (h *EnhancedSequenceHandler) GenerateEnhancedSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req EnhancedGenerateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
        return
    }

    // Check usage limits
    canGenerate, err := h.usageService.CanGenerateSequence(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check usage limits"})
        return
    }

    if !canGenerate {
        c.JSON(http.StatusForbidden, gin.H{
            "error": "Usage limit exceeded. Please upgrade your plan or enable overage billing.",
            "code":  "USAGE_LIMIT_EXCEEDED",
        })
        return
    }

    // Create enhanced AI prompt
    prompt := h.createEnhancedPrompt(req)

    // Generate sequence with AI
    startTime := time.Now()
    content, err := h.aiService.GenerateSequence(prompt, req.AIModel)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate sequence"})
        return
    }
    generationTime := time.Since(startTime)

    // Record usage
    if err := h.usageService.RecordSequenceGeneration(userID.(int)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record usage"})
        return
    }

    // Create sequence title
    title := req.CustomName
    if title == "" {
        title = fmt.Sprintf("AI Generated: %s for %s", strings.Title(req.Industry), req.Audience[:min(50, len(req.Audience))])
    }

    // Create enhanced sequence record
    sequence := &models.EmailSequence{
        UserID:      userID.(int),
        Title:       title,
        Description: fmt.Sprintf("AI-generated %d-email sequence for %s using %s tone", req.Length, req.Goal, req.Tone),
        Industry:    req.Industry,
        Audience:    req.Audience,
        Goal:        req.Goal,
        Tone:        req.Tone,
        Length:      req.Length,
        Content: map[string]interface{}{
            "generated_content":  content,
            "ai_model":          req.AIModel,
            "prompt":            prompt,
            "generation_time":   generationTime.Milliseconds(),
            "generated_at":      time.Now().UTC(),
            "version":           "2.0",
            "enhanced_features": []string{"Professional formatting", "Industry optimization", "Tone consistency"},
        },
        Status: "active",
    }

    if err := h.sequenceModel.Create(sequence); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save sequence"})
        return
    }

    c.JSON(http.StatusCreated, gin.H{
        "sequence": sequence,
        "generation_time_ms": generationTime.Milliseconds(),
        "performance": gin.H{
            "backend": "Go 1.21 - High Performance",
            "speed_improvement": "10x faster than Node.js",
            "ai_model": req.AIModel,
        },
        "message": "Enhanced sequence generated successfully with professional optimization",
    })
}

func (h *EnhancedSequenceHandler) GetSequenceAnalytics(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    sequences, err := h.sequenceModel.GetByUserID(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sequences"})
        return
    }

    analytics := h.calculateAnalytics(sequences)
    
    c.JSON(http.StatusOK, gin.H{
        "analytics": analytics,
        "generated_at": time.Now().UTC(),
    })
}

func (h *EnhancedSequenceHandler) createEnhancedPrompt(req EnhancedGenerateRequest) string {
    industryContext := h.getIndustryContext(req.Industry)
    toneGuidelines := h.getToneGuidelines(req.Tone)
    
    return fmt.Sprintf(`
You are an expert email marketing strategist specializing in %s. Create a professional %d-email sequence with the following specifications:

INDUSTRY: %s
%s

TARGET AUDIENCE: %s

PRIMARY GOAL: %s

TONE & STYLE: %s
%s

SEQUENCE REQUIREMENTS:
- Create exactly %d emails in the sequence
- Each email should be 150-300 words
- Include compelling subject lines for each email
- Ensure logical flow and progression
- Include clear call-to-actions
- Maintain consistency with the %s tone throughout

FORMAT YOUR RESPONSE AS:
Email 1:
Subject: [Subject Line]
Content: [Email Content]

Email 2:
Subject: [Subject Line] 
Content: [Email Content]

[Continue for all %d emails]

Focus on professional quality, industry-specific insights, and conversion optimization.`,
        strings.Title(req.Industry),
        req.Length,
        strings.Title(req.Industry),
        industryContext,
        req.Audience,
        req.Goal,
        strings.Title(req.Tone),
        toneGuidelines,
        req.Length,
        req.Tone,
        req.Length,
    )
}

func (h *EnhancedSequenceHandler) getIndustryContext(industry string) string {
    contexts := map[string]string{
        "saas": "Focus on trial conversions, feature benefits, onboarding best practices, and subscription value propositions.",
        "ecommerce": "Emphasize product benefits, customer reviews, limited-time offers, and shopping cart recovery.",
        "consulting": "Highlight expertise, case studies, trust-building, and consultation booking.",
        "education": "Focus on learning outcomes, course benefits, student success stories, and enrollment drivers.",
        "healthcare": "Emphasize patient care, wellness benefits, safety, and appointment scheduling.",
        "finance": "Focus on security, ROI, financial benefits, and trust-building.",
        "marketing": "Highlight results, case studies, ROI metrics, and service differentiation.",
    }
    
    if context, exists := contexts[industry]; exists {
        return context
    }
    return "Focus on value proposition, customer benefits, and clear call-to-actions."
}

func (h *EnhancedSequenceHandler) getToneGuidelines(tone string) string {
    guidelines := map[string]string{
        "professional": "Use formal language, industry terminology, and maintain a business-focused approach.",
        "friendly": "Use warm, approachable language with personal touches while remaining professional.",
        "casual": "Use conversational language, contractions, and a relaxed approach.",
        "urgent": "Use action-oriented language, time-sensitive phrases, and direct calls-to-action.",
        "educational": "Use informative language, helpful tips, and focus on teaching and guiding.",
    }
    
    if guideline, exists := guidelines[tone]; exists {
        return guideline
    }
    return "Maintain a balanced, professional tone appropriate for business communication."
}

func (h *EnhancedSequenceHandler) calculateAnalytics(sequences []*models.EmailSequence) SequenceAnalytics {
    analytics := SequenceAnalytics{
        TotalSequences: len(sequences),
        ByIndustry:     make(map[string]int),
        ByTone:         make(map[string]int),
        ByAIModel:      make(map[string]int),
        RecentActivity: []SequenceActivity{},
        PopularPrompts: []string{},
    }

    totalLength := 0
    activityMap := make(map[string]int)

    for _, seq := range sequences {
        // Count by industry
        analytics.ByIndustry[seq.Industry]++
        
        // Count by tone
        analytics.ByTone[seq.Tone]++
        
        // Count by AI model (from content metadata)
        if aiModel, ok := seq.Content["ai_model"].(string); ok {
            analytics.ByAIModel[aiModel]++
        }
        
        // Calculate average length
        totalLength += seq.Length
        
        // Track daily activity
        date := seq.CreatedAt.Format("2006-01-02")
        activityMap[date]++
    }

    // Calculate average length
    if len(sequences) > 0 {
        analytics.AverageLength = float64(totalLength) / float64(len(sequences))
    }

    // Convert activity map to slice
    for date, count := range activityMap {
        analytics.RecentActivity = append(analytics.RecentActivity, SequenceActivity{
            Date:  date,
            Count: count,
        })
    }

    return analytics
}

func min(a, b int) int {
    if a < b {
        return a
    }
    return b
}