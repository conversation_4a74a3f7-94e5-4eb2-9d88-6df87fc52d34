package handlers

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/go-playground/validator/v10"
    "sequenceai/internal/models"
    "sequenceai/internal/services"
)

type SequenceHandler struct {
    sequenceModel *models.SequenceModel
    aiService     *services.AIService
    usageService  *services.UsageService
    validator     *validator.Validate
}

type CreateSequenceRequest struct {
    Title       string `json:"title" validate:"required,min=2,max=255"`
    Description string `json:"description" validate:"max=1000"`
    Industry    string `json:"industry" validate:"required,max=100"`
    Audience    string `json:"audience" validate:"required,max=100"`
    Goal        string `json:"goal" validate:"required,max=100"`
    Tone        string `json:"tone" validate:"required,max=50"`
    Length      int    `json:"length" validate:"required,min=1,max=20"`
}

type GenerateSequenceRequest struct {
    Industry  string `json:"industry" validate:"required,max=100"`
    Audience  string `json:"audience" validate:"required,max=100"`
    Goal      string `json:"goal" validate:"required,max=100"`
    Tone      string `json:"tone" validate:"required,max=50"`
    Length    int    `json:"length" validate:"required,min=1,max=20"`
    AIModel   string `json:"ai_model" validate:"required"`
}

func NewSequenceHandler(sequenceModel *models.SequenceModel, aiService *services.AIService, usageService *services.UsageService) *SequenceHandler {
    return &SequenceHandler{
        sequenceModel: sequenceModel,
        aiService:     aiService,
        usageService:  usageService,
        validator:     validator.New(),
    }
}

func (h *SequenceHandler) GetSequences(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    sequences, err := h.sequenceModel.GetByUserID(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sequences"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"sequences": sequences})
}

func (h *SequenceHandler) GetSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    idParam := c.Param("id")
    id, err := strconv.Atoi(idParam)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sequence ID"})
        return
    }

    sequence, err := h.sequenceModel.GetByID(id, userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Sequence not found"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"sequence": sequence})
}

func (h *SequenceHandler) CreateSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req CreateSequenceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
        return
    }

    sequence := &models.EmailSequence{
        UserID:      userID.(int),
        Title:       req.Title,
        Description: req.Description,
        Industry:    req.Industry,
        Audience:    req.Audience,
        Goal:        req.Goal,
        Tone:        req.Tone,
        Length:      req.Length,
        Content:     make(map[string]interface{}),
    }

    if err := h.sequenceModel.Create(sequence); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create sequence"})
        return
    }

    c.JSON(http.StatusCreated, gin.H{"sequence": sequence})
}

func (h *SequenceHandler) UpdateSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    idParam := c.Param("id")
    id, err := strconv.Atoi(idParam)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sequence ID"})
        return
    }

    sequence, err := h.sequenceModel.GetByID(id, userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Sequence not found"})
        return
    }

    var req CreateSequenceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
        return
    }

    sequence.Title = req.Title
    sequence.Description = req.Description
    sequence.Industry = req.Industry
    sequence.Audience = req.Audience
    sequence.Goal = req.Goal
    sequence.Tone = req.Tone
    sequence.Length = req.Length

    if err := h.sequenceModel.Update(sequence); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update sequence"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"sequence": sequence})
}

func (h *SequenceHandler) DeleteSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    idParam := c.Param("id")
    id, err := strconv.Atoi(idParam)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sequence ID"})
        return
    }

    if err := h.sequenceModel.Delete(id, userID.(int)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete sequence"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Sequence deleted successfully"})
}

func (h *SequenceHandler) GenerateSequence(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req GenerateSequenceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    if err := h.validator.Struct(req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
        return
    }

    // Check usage limits
    canGenerate, err := h.usageService.CanGenerateSequence(userID.(int))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check usage limits"})
        return
    }

    if !canGenerate {
        c.JSON(http.StatusForbidden, gin.H{
            "error": "Usage limit exceeded. Please upgrade your plan or enable overage billing.",
            "code":  "USAGE_LIMIT_EXCEEDED",
        })
        return
    }

    // Generate AI prompt
    prompt := h.createPrompt(req)

    // Generate sequence with AI
    content, err := h.aiService.GenerateSequence(prompt, req.AIModel)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate sequence"})
        return
    }

    // Record usage
    if err := h.usageService.RecordSequenceGeneration(userID.(int)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record usage"})
        return
    }

    // Create sequence record
    sequence := &models.EmailSequence{
        UserID:      userID.(int),
        Title:       "AI Generated: " + req.Industry + " for " + req.Audience,
        Description: "AI-generated email sequence for " + req.Goal,
        Industry:    req.Industry,
        Audience:    req.Audience,
        Goal:        req.Goal,
        Tone:        req.Tone,
        Length:      req.Length,
        Content: map[string]interface{}{
            "generated_content": content,
            "ai_model":          req.AIModel,
            "prompt":            prompt,
        },
        Status: "active",
    }

    if err := h.sequenceModel.Create(sequence); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save sequence"})
        return
    }

    c.JSON(http.StatusCreated, gin.H{
        "sequence": sequence,
        "message":  "Sequence generated successfully",
    })
}

func (h *SequenceHandler) createPrompt(req GenerateSequenceRequest) string {
    return "Create a " + strconv.Itoa(req.Length) + "-email sequence for the " + req.Industry + 
           " industry targeting " + req.Audience + " with the goal of " + req.Goal + 
           ". Use a " + req.Tone + " tone. Include subject lines and email content for each email in the sequence."
}