package handlers

import (
    "net/http"

    "github.com/gin-gonic/gin"
    "sequenceai/internal/models"
    "sequenceai/internal/services"
)

type PaymentHandler struct {
    stripeService *services.StripeService
    userModel     *models.UserModel
}

func NewPaymentHandler(stripeService *services.StripeService, userModel *models.UserModel) *PaymentHandler {
    return &PaymentHandler{
        stripeService: stripeService,
        userModel:     userModel,
    }
}

func (h *PaymentHandler) CreateSubscription(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    var req struct {
        PlanID string `json:"plan_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
        return
    }

    subscription, err := h.stripeService.CreateSubscription(userID.(int), req.PlanID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
        return
    }

    c.JSON(http.StatusCreated, gin.H{
        "subscription": subscription,
        "message":      "Subscription created successfully",
    })
}

func (h *PaymentHandler) CancelSubscription(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    user, err := h.userModel.GetByID(userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    if user.SubscriptionID == nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription"})
        return
    }

    if err := h.stripeService.CancelSubscription(*user.SubscriptionID); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel subscription"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Subscription canceled successfully"})
}

func (h *PaymentHandler) GetInvoices(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }

    user, err := h.userModel.GetByID(userID.(int))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    if user.StripeCustomerID == nil {
        c.JSON(http.StatusOK, gin.H{"invoices": []interface{}{}})
        return
    }

    invoices, err := h.stripeService.GetInvoices(*user.StripeCustomerID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch invoices"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"invoices": invoices})
}

func (h *PaymentHandler) HandleWebhook(c *gin.Context) {
    payload, err := c.GetRawData()
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read payload"})
        return
    }

    signature := c.GetHeader("Stripe-Signature")

    if err := h.stripeService.HandleWebhook(payload, signature); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}