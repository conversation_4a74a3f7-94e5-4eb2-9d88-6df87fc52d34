package services

import (
    "fmt"
    "log"
    "sequenceai/internal/config"
)

type EmailService struct {
    smtpConfig config.SMTPConfig
}

func NewEmailService(smtpConfig config.SMTPConfig) *EmailService {
    return &EmailService{
        smtpConfig: smtpConfig,
    }
}

func (s *EmailService) SendWelcomeEmail(email, name string) error {
    // For now, just log the email send attempt
    // In production, you would implement actual SMTP sending
    log.Printf("📧 Sending welcome email to %s (%s)", email, name)
    
    subject := "Welcome to SequenceAI!"
    body := fmt.Sprintf(`
Dear %s,

Welcome to SequenceAI! We're excited to have you on board.

Your account has been successfully created and you can now start generating high-converting email sequences with our advanced AI models.

Getting Started:
1. Log in to your dashboard
2. Create your first email sequence
3. Customize your content with our AI tools

If you have any questions, feel free to reach out to our support team.

Best regards,
The SequenceAI Team

---
This email was sent to: %s
`, name, email)

    log.Printf("📧 Email Subject: %s", subject)
    log.Printf("📧 Email Body: %s", body)
    
    return nil
}

func (s *EmailService) SendUsageWarning(email, name string, usagePercent int) error {
    log.Printf("📧 Sending usage warning email to %s (%s) - %d%% usage", email, name, usagePercent)
    return nil
}

func (s *EmailService) SendOverageNotification(email, name string, overageAmount float64) error {
    log.Printf("📧 Sending overage notification email to %s (%s) - $%.2f overage", email, name, overageAmount)
    return nil
}