package services

import (
    "database/sql"
    "errors"
    "time"

    "github.com/golang-jwt/jwt/v5"
    "sequenceai/internal/models"
)

type AuthService struct {
    userModel *models.UserModel
    jwtSecret string
}

type Claims struct {
    UserID int    `json:"user_id"`
    Email  string `json:"email"`
    Plan   string `json:"plan"`
    jwt.RegisteredClaims
}

func NewAuthService(userModel *models.UserModel, jwtSecret string) *AuthService {
    return &AuthService{
        userModel: userModel,
        jwtSecret: jwtSecret,
    }
}

func (s *AuthService) Register(name, email, password string) (*models.User, error) {
    // Check if user already exists
    existingUser, err := s.userModel.GetByEmail(email)
    if err != nil && err != sql.ErrNoRows {
        return nil, err
    }
    if existingUser != nil {
        return nil, errors.New("user already exists")
    }

    // Create new user
    user := &models.User{
        Name:     name,
        Email:    email,
        Password: password,
    }

    if err := s.userModel.Create(user); err != nil {
        return nil, err
    }

    // Clear password before returning
    user.Password = ""
    return user, nil
}

func (s *AuthService) Login(email, password string) (*models.User, string, error) {
    user, err := s.userModel.GetByEmail(email)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, "", errors.New("invalid credentials")
        }
        return nil, "", err
    }

    if !s.userModel.ValidatePassword(user, password) {
        return nil, "", errors.New("invalid credentials")
    }

    token, err := s.GenerateToken(user)
    if err != nil {
        return nil, "", err
    }

    // Clear password before returning
    user.Password = ""
    return user, token, nil
}

func (s *AuthService) GenerateToken(user *models.User) (string, error) {
    claims := &Claims{
        UserID: user.ID,
        Email:  user.Email,
        Plan:   user.Plan,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            NotBefore: jwt.NewNumericDate(time.Now()),
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.jwtSecret))
}

func (s *AuthService) ValidateToken(tokenString string) (*Claims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(s.jwtSecret), nil
    })

    if err != nil {
        return nil, err
    }

    if claims, ok := token.Claims.(*Claims); ok && token.Valid {
        return claims, nil
    }

    return nil, errors.New("invalid token")
}

func (s *AuthService) GetUserByID(id int) (*models.User, error) {
    user, err := s.userModel.GetByID(id)
    if err != nil {
        return nil, err
    }

    // Clear password before returning
    user.Password = ""
    return user, nil
}