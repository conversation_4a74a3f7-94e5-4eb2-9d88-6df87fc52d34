package services

import (
    "errors"
    "sequenceai/internal/models"
)

type UsageService struct {
    usageModel *models.UsageModel
    userModel  *models.UserModel
}

func NewUsageService(usageModel *models.UsageModel, userModel *models.UserModel) *UsageService {
    return &UsageService{
        usageModel: usageModel,
        userModel:  userModel,
    }
}

func (s *UsageService) GetCurrentUsage(userID int) (*models.Usage, error) {
    return s.usageModel.GetCurrentUsage(userID)
}

func (s *UsageService) CanGenerateSequence(userID int) (bool, error) {
    usage, err := s.usageModel.GetCurrentUsage(userID)
    if err != nil {
        return false, err
    }

    // Check if user is within regular limit
    if usage.SequencesUsed < usage.SequencesLimit {
        return true, nil
    }

    // Check if user has overage consent
    if usage.OverageConsent {
        return true, nil
    }

    return false, nil
}

func (s *UsageService) RecordSequenceGeneration(userID int) error {
    usage, err := s.usageModel.GetCurrentUsage(userID)
    if err != nil {
        return err
    }

    if usage.SequencesUsed < usage.SequencesLimit {
        // Within regular limit
        return s.usageModel.IncrementUsage(userID)
    } else if usage.OverageConsent {
        // Using overage
        return s.usageModel.IncrementOverage(userID)
    }

    return errors.New("usage limit exceeded and no overage consent")
}

func (s *UsageService) SetOverageConsent(userID int, consent bool) error {
    return s.usageModel.SetOverageConsent(userID, consent)
}

func (s *UsageService) GetUsageHistory(userID int, limit int) ([]*models.Usage, error) {
    return s.usageModel.GetUsageHistory(userID, limit)
}

func (s *UsageService) GetUsageStatus(userID int) (string, float64, error) {
    usage, err := s.usageModel.GetCurrentUsage(userID)
    if err != nil {
        return "", 0.0, err
    }

    percentage := (float64(usage.SequencesUsed) / float64(usage.SequencesLimit)) * 100

    var status string
    switch {
    case percentage < 80:
        status = "normal"
    case percentage < 95:
        status = "warning"
    default:
        status = "critical"
    }

    return status, percentage, nil
}