package services

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

type AIService struct {
    openAIKey    string
    anthropicKey string
    httpClient   *http.Client
}

type OpenAIRequest struct {
    Model       string    `json:"model"`
    Messages    []Message `json:"messages"`
    Temperature float64   `json:"temperature"`
    MaxTokens   int       `json:"max_tokens"`
}

type Message struct {
    Role    string `json:"role"`
    Content string `json:"content"`
}

type OpenAIResponse struct {
    Choices []struct {
        Message Message `json:"message"`
    } `json:"choices"`
}

type AnthropicRequest struct {
    Model     string    `json:"model"`
    Messages  []Message `json:"messages"`
    MaxTokens int       `json:"max_tokens"`
}

type AnthropicResponse struct {
    Content []struct {
        Text string `json:"text"`
    } `json:"content"`
}

func NewAIService(openAIKey, anthropicKey string) *AIService {
    return &AIService{
        openAIKey:    openAI<PERSON><PERSON>,
        anthropic<PERSON><PERSON>: anthropic<PERSON><PERSON>,
        httpClient: &http.Client{
            Timeout: 60 * time.Second,
        },
    }
}

func (s *AIService) GenerateSequence(prompt, aiModel string) (string, error) {
    switch aiModel {
    case "gpt-4", "gpt-3.5-turbo":
        return s.generateWithOpenAI(prompt, aiModel)
    case "claude-3-sonnet", "claude-3-haiku":
        return s.generateWithAnthropic(prompt, aiModel)
    default:
        return s.generateFallback(prompt)
    }
}

func (s *AIService) generateWithOpenAI(prompt, model string) (string, error) {
    if s.openAIKey == "" {
        return s.generateFallback(prompt)
    }

    request := OpenAIRequest{
        Model: model,
        Messages: []Message{
            {
                Role:    "system",
                Content: "You are an expert email marketing specialist. Generate professional, high-converting email sequences based on the user's requirements.",
            },
            {
                Role:    "user",
                Content: prompt,
            },
        },
        Temperature: 0.7,
        MaxTokens:   2000,
    }

    jsonData, err := json.Marshal(request)
    if err != nil {
        return "", err
    }

    req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonData))
    if err != nil {
        return "", err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+s.openAIKey)

    resp, err := s.httpClient.Do(req)
    if err != nil {
        return s.generateFallback(prompt)
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return s.generateFallback(prompt)
    }

    var response OpenAIResponse
    if err := json.Unmarshal(body, &response); err != nil {
        return s.generateFallback(prompt)
    }

    if len(response.Choices) > 0 {
        return response.Choices[0].Message.Content, nil
    }

    return s.generateFallback(prompt)
}

func (s *AIService) generateWithAnthropic(prompt, model string) (string, error) {
    if s.anthropicKey == "" {
        return s.generateFallback(prompt)
    }

    request := AnthropicRequest{
        Model: model,
        Messages: []Message{
            {
                Role:    "user",
                Content: "You are an expert email marketing specialist. Generate professional, high-converting email sequences based on these requirements: " + prompt,
            },
        },
        MaxTokens: 2000,
    }

    jsonData, err := json.Marshal(request)
    if err != nil {
        return "", err
    }

    req, err := http.NewRequest("POST", "https://api.anthropic.com/v1/messages", bytes.NewBuffer(jsonData))
    if err != nil {
        return "", err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("x-api-key", s.anthropicKey)
    req.Header.Set("anthropic-version", "2023-06-01")

    resp, err := s.httpClient.Do(req)
    if err != nil {
        return s.generateFallback(prompt)
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return s.generateFallback(prompt)
    }

    var response AnthropicResponse
    if err := json.Unmarshal(body, &response); err != nil {
        return s.generateFallback(prompt)
    }

    if len(response.Content) > 0 {
        return response.Content[0].Text, nil
    }

    return s.generateFallback(prompt)
}

func (s *AIService) generateFallback(prompt string) (string, error) {
    // Professional fallback response when AI APIs are unavailable
    fallbackResponse := fmt.Sprintf(`
# Professional Email Sequence

## Email 1: Welcome & Introduction
**Subject:** Welcome to [Your Brand] - Let's Get Started!

Dear [First Name],

Welcome aboard! We're thrilled to have you join our community of forward-thinking professionals.

Over the next few days, I'll be sharing valuable insights and strategies to help you achieve your goals with our platform.

Here's what you can expect:
- Day 2: Quick start guide and best practices
- Day 4: Advanced strategies from our top users  
- Day 7: Exclusive resources and next steps

Looking forward to your success!

Best regards,
[Your Name]

## Email 2: Value & Education  
**Subject:** The #1 Mistake Most People Make (And How to Avoid It)

Hi [First Name],

Yesterday I welcomed you to our community, and today I want to share something crucial.

After working with thousands of professionals, I've noticed one common mistake that holds people back from getting the results they want...

[Continue with educational content and value proposition]

## Email 3: Social Proof & Success Stories
**Subject:** How [Customer Name] Achieved [Specific Result] in Just [Time Period]

[First Name],

I love sharing success stories from our community, and this one really stood out...

[Include specific case study and results]

## Email 4: Addressing Objections
**Subject:** "But what if it doesn't work for me?"

Hi [First Name],

I get this question a lot, and I completely understand the concern.

Let me address the most common questions I hear...

[Handle objections professionally]

## Email 5: Call to Action
**Subject:** Ready to take the next step, [First Name]?

[First Name],

Over the past few days, we've covered:
✓ Getting started effectively
✓ Avoiding common mistakes  
✓ Real success stories
✓ Addressing any concerns

Now it's time to take action...

[Clear call to action with benefits]

---
*Generated by SequenceAI - Professional Email Marketing Automation*
*Note: AI APIs currently unavailable - using high-quality template response*
`)

    return fallbackResponse, nil
}