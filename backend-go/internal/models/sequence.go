package models

import (
    "database/sql"
    "encoding/json"
    "time"
)

type EmailSequence struct {
    ID          int                    `json:"id" db:"id"`
    UserID      int                    `json:"user_id" db:"user_id"`
    Title       string                 `json:"title" db:"title"`
    Description string                 `json:"description" db:"description"`
    Industry    string                 `json:"industry" db:"industry"`
    Audience    string                 `json:"audience" db:"audience"`
    Goal        string                 `json:"goal" db:"goal"`
    Tone        string                 `json:"tone" db:"tone"`
    Length      int                    `json:"length" db:"length"`
    Content     map[string]interface{} `json:"content" db:"content"`
    Status      string                 `json:"status" db:"status"`
    CreatedAt   time.Time              `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at" db:"updated_at"`
}

type SequenceModel struct {
    DB *sql.DB
}

func NewSequenceModel(db *sql.DB) *SequenceModel {
    return &SequenceModel{DB: db}
}

func (m *SequenceModel) Create(sequence *EmailSequence) error {
    contentBytes, err := json.Marshal(sequence.Content)
    if err != nil {
        return err
    }

    query := `
        INSERT INTO email_sequences (user_id, title, description, industry, audience, goal, tone, length, content, status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING id`

    now := time.Now()
    err = m.DB.QueryRow(
        query,
        sequence.UserID,
        sequence.Title,
        sequence.Description,
        sequence.Industry,
        sequence.Audience,
        sequence.Goal,
        sequence.Tone,
        sequence.Length,
        contentBytes,
        "draft",
        now,
        now,
    ).Scan(&sequence.ID)

    if err != nil {
        return err
    }

    sequence.CreatedAt = now
    sequence.UpdatedAt = now
    sequence.Status = "draft"

    return nil
}

func (m *SequenceModel) GetByUserID(userID int) ([]*EmailSequence, error) {
    query := `
        SELECT id, user_id, title, description, industry, audience, goal, tone, length, content, status, created_at, updated_at
        FROM email_sequences 
        WHERE user_id = $1 
        ORDER BY created_at DESC`

    rows, err := m.DB.Query(query, userID)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var sequences []*EmailSequence
    for rows.Next() {
        sequence := &EmailSequence{}
        var contentBytes []byte

        err := rows.Scan(
            &sequence.ID,
            &sequence.UserID,
            &sequence.Title,
            &sequence.Description,
            &sequence.Industry,
            &sequence.Audience,
            &sequence.Goal,
            &sequence.Tone,
            &sequence.Length,
            &contentBytes,
            &sequence.Status,
            &sequence.CreatedAt,
            &sequence.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }

        if err = json.Unmarshal(contentBytes, &sequence.Content); err != nil {
            return nil, err
        }

        sequences = append(sequences, sequence)
    }

    return sequences, nil
}

func (m *SequenceModel) GetByID(id int, userID int) (*EmailSequence, error) {
    sequence := &EmailSequence{}
    var contentBytes []byte

    query := `
        SELECT id, user_id, title, description, industry, audience, goal, tone, length, content, status, created_at, updated_at
        FROM email_sequences 
        WHERE id = $1 AND user_id = $2`

    err := m.DB.QueryRow(query, id, userID).Scan(
        &sequence.ID,
        &sequence.UserID,
        &sequence.Title,
        &sequence.Description,
        &sequence.Industry,
        &sequence.Audience,
        &sequence.Goal,
        &sequence.Tone,
        &sequence.Length,
        &contentBytes,
        &sequence.Status,
        &sequence.CreatedAt,
        &sequence.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    if err = json.Unmarshal(contentBytes, &sequence.Content); err != nil {
        return nil, err
    }

    return sequence, nil
}

func (m *SequenceModel) Update(sequence *EmailSequence) error {
    contentBytes, err := json.Marshal(sequence.Content)
    if err != nil {
        return err
    }

    query := `
        UPDATE email_sequences 
        SET title = $1, description = $2, industry = $3, audience = $4, goal = $5, 
            tone = $6, length = $7, content = $8, status = $9, updated_at = $10
        WHERE id = $11 AND user_id = $12`

    _, err = m.DB.Exec(
        query,
        sequence.Title,
        sequence.Description,
        sequence.Industry,
        sequence.Audience,
        sequence.Goal,
        sequence.Tone,
        sequence.Length,
        contentBytes,
        sequence.Status,
        time.Now(),
        sequence.ID,
        sequence.UserID,
    )

    return err
}

func (m *SequenceModel) Delete(id int, userID int) error {
    query := `DELETE FROM email_sequences WHERE id = $1 AND user_id = $2`
    _, err := m.DB.Exec(query, id, userID)
    return err
}

func (m *SequenceModel) CountByUserID(userID int) (int, error) {
    var count int
    query := `SELECT COUNT(*) FROM email_sequences WHERE user_id = $1`
    err := m.DB.QueryRow(query, userID).Scan(&count)
    return count, err
}