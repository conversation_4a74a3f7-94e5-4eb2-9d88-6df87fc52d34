package models

import (
    "database/sql"
    "time"

    "golang.org/x/crypto/bcrypt"
)

type User struct {
    ID                int       `json:"id" db:"id"`
    Name              string    `json:"name" db:"name"`
    Email             string    `json:"email" db:"email"`
    Password          string    `json:"-" db:"password_hash"`
    Plan              string    `json:"plan" db:"plan"`
    StripeCustomerID  *string   `json:"stripe_customer_id" db:"stripe_customer_id"`
    SubscriptionID    *string   `json:"subscription_id" db:"subscription_id"`
    SubscriptionStatus string   `json:"subscription_status" db:"subscription_status"`
    CreatedAt         time.Time `json:"created_at" db:"created_at"`
    UpdatedAt         time.Time `json:"updated_at" db:"updated_at"`
}

type UserModel struct {
    DB *sql.DB
}

func NewUserModel(db *sql.DB) *UserModel {
    return &UserModel{DB: db}
}

func (m *UserModel) Create(user *User) error {
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
    if err != nil {
        return err
    }

    query := `
        INSERT INTO users (name, email, password_hash, plan, subscription_status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id`

    now := time.Now()
    err = m.DB.QueryRow(
        query,
        user.Name,
        user.Email,
        string(hashedPassword),
        "free",
        "inactive",
        now,
        now,
    ).Scan(&user.ID)

    if err != nil {
        return err
    }

    user.CreatedAt = now
    user.UpdatedAt = now
    user.Plan = "free"
    user.SubscriptionStatus = "inactive"

    return nil
}

func (m *UserModel) GetByEmail(email string) (*User, error) {
    user := &User{}
    query := `
        SELECT id, name, email, password_hash, plan, stripe_customer_id, 
               subscription_id, subscription_status, created_at, updated_at
        FROM users 
        WHERE email = $1`

    err := m.DB.QueryRow(query, email).Scan(
        &user.ID,
        &user.Name,
        &user.Email,
        &user.Password,
        &user.Plan,
        &user.StripeCustomerID,
        &user.SubscriptionID,
        &user.SubscriptionStatus,
        &user.CreatedAt,
        &user.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    return user, nil
}

func (m *UserModel) GetByID(id int) (*User, error) {
    user := &User{}
    query := `
        SELECT id, name, email, password_hash, plan, stripe_customer_id, 
               subscription_id, subscription_status, created_at, updated_at
        FROM users 
        WHERE id = $1`

    err := m.DB.QueryRow(query, id).Scan(
        &user.ID,
        &user.Name,
        &user.Email,
        &user.Password,
        &user.Plan,
        &user.StripeCustomerID,
        &user.SubscriptionID,
        &user.SubscriptionStatus,
        &user.CreatedAt,
        &user.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    return user, nil
}

func (m *UserModel) Update(user *User) error {
    query := `
        UPDATE users 
        SET name = $1, plan = $2, stripe_customer_id = $3, 
            subscription_id = $4, subscription_status = $5, updated_at = $6
        WHERE id = $7`

    _, err := m.DB.Exec(
        query,
        user.Name,
        user.Plan,
        user.StripeCustomerID,
        user.SubscriptionID,
        user.SubscriptionStatus,
        time.Now(),
        user.ID,
    )

    return err
}

func (m *UserModel) ValidatePassword(user *User, password string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
    return err == nil
}