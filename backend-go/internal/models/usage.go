package models

import (
    "database/sql"
    "time"
)

type Usage struct {
    ID                 int       `json:"id" db:"id"`
    UserID             int       `json:"user_id" db:"user_id"`
    Period             string    `json:"period" db:"period"`
    SequencesUsed      int       `json:"sequences_used" db:"sequences_used"`
    SequencesLimit     int       `json:"sequences_limit" db:"sequences_limit"`
    OverageSequences   int       `json:"overage_sequences" db:"overage_sequences"`
    OverageCharges     float64   `json:"overage_charges" db:"overage_charges"`
    OverageConsent     bool      `json:"overage_consent" db:"overage_consent"`
    NotificationsSent  int       `json:"notifications_sent" db:"notifications_sent"`
    CreatedAt          time.Time `json:"created_at" db:"created_at"`
    UpdatedAt          time.Time `json:"updated_at" db:"updated_at"`
}

type UsageModel struct {
    DB *sql.DB
}

func NewUsageModel(db *sql.DB) *UsageModel {
    return &UsageModel{DB: db}
}

func (m *UsageModel) GetCurrentUsage(userID int) (*Usage, error) {
    now := time.Now()
    period := now.Format("2006-01")

    usage := &Usage{}
    query := `
        SELECT id, user_id, period, sequences_used, sequences_limit, overage_sequences, 
               overage_charges, overage_consent, notifications_sent, created_at, updated_at
        FROM usage 
        WHERE user_id = $1 AND period = $2`

    err := m.DB.QueryRow(query, userID, period).Scan(
        &usage.ID,
        &usage.UserID,
        &usage.Period,
        &usage.SequencesUsed,
        &usage.SequencesLimit,
        &usage.OverageSequences,
        &usage.OverageCharges,
        &usage.OverageConsent,
        &usage.NotificationsSent,
        &usage.CreatedAt,
        &usage.UpdatedAt,
    )

    if err == sql.ErrNoRows {
        // Create new usage record for current period
        return m.CreateUsageRecord(userID, period)
    }

    if err != nil {
        return nil, err
    }

    return usage, nil
}

func (m *UsageModel) CreateUsageRecord(userID int, period string) (*Usage, error) {
    // Default limits based on plan (would need to get from user record)
    sequencesLimit := 5 // Free plan default

    usage := &Usage{
        UserID:            userID,
        Period:            period,
        SequencesUsed:     0,
        SequencesLimit:    sequencesLimit,
        OverageSequences:  0,
        OverageCharges:    0.0,
        OverageConsent:    false,
        NotificationsSent: 0,
    }

    query := `
        INSERT INTO usage (user_id, period, sequences_used, sequences_limit, overage_sequences, 
                          overage_charges, overage_consent, notifications_sent, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id`

    now := time.Now()
    err := m.DB.QueryRow(
        query,
        usage.UserID,
        usage.Period,
        usage.SequencesUsed,
        usage.SequencesLimit,
        usage.OverageSequences,
        usage.OverageCharges,
        usage.OverageConsent,
        usage.NotificationsSent,
        now,
        now,
    ).Scan(&usage.ID)

    if err != nil {
        return nil, err
    }

    usage.CreatedAt = now
    usage.UpdatedAt = now

    return usage, nil
}

func (m *UsageModel) IncrementUsage(userID int) error {
    now := time.Now()
    period := now.Format("2006-01")

    query := `
        UPDATE usage 
        SET sequences_used = sequences_used + 1, updated_at = $1
        WHERE user_id = $2 AND period = $3`

    _, err := m.DB.Exec(query, now, userID, period)
    return err
}

func (m *UsageModel) IncrementOverage(userID int) error {
    now := time.Now()
    period := now.Format("2006-01")

    query := `
        UPDATE usage 
        SET overage_sequences = overage_sequences + 1, 
            overage_charges = overage_charges + 3.0, 
            updated_at = $1
        WHERE user_id = $2 AND period = $3`

    _, err := m.DB.Exec(query, now, userID, period)
    return err
}

func (m *UsageModel) SetOverageConsent(userID int, consent bool) error {
    now := time.Now()
    period := now.Format("2006-01")

    query := `
        UPDATE usage 
        SET overage_consent = $1, updated_at = $2
        WHERE user_id = $3 AND period = $4`

    _, err := m.DB.Exec(query, consent, now, userID, period)
    return err
}

func (m *UsageModel) GetUsageHistory(userID int, limit int) ([]*Usage, error) {
    query := `
        SELECT id, user_id, period, sequences_used, sequences_limit, overage_sequences, 
               overage_charges, overage_consent, notifications_sent, created_at, updated_at
        FROM usage 
        WHERE user_id = $1 
        ORDER BY period DESC 
        LIMIT $2`

    rows, err := m.DB.Query(query, userID, limit)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var usages []*Usage
    for rows.Next() {
        usage := &Usage{}
        err := rows.Scan(
            &usage.ID,
            &usage.UserID,
            &usage.Period,
            &usage.SequencesUsed,
            &usage.SequencesLimit,
            &usage.OverageSequences,
            &usage.OverageCharges,
            &usage.OverageConsent,
            &usage.NotificationsSent,
            &usage.CreatedAt,
            &usage.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }
        usages = append(usages, usage)
    }

    return usages, nil
}