package config

import (
    "os"
    "strconv"
)

type Config struct {
    Environment     string
    Port            string
    DatabaseURL     string
    RedisURL        string
    RedisPassword   string
    JWTSecret       string
    OpenAIKey       string
    AnthropicKey    string
    StripeKey       string
    SMTPConfig      SMTPConfig
}

type SMTPConfig struct {
    Host     string
    Port     int
    Username string
    Password string
    From     string
}

func Load() *Config {
    port, _ := strconv.Atoi(getEnv("SMTP_PORT", "587"))

    return &Config{
        Environment:   getEnv("GIN_MODE", "debug"),
        Port:          getEnv("PORT", "5000"),
        DatabaseURL:   getEnv("DATABASE_URL", "postgres://sequenceai:sequenceai123@localhost:5432/sequenceai?sslmode=disable"),
        RedisURL:      getEnv("REDIS_URL", "localhost:6379"),
        RedisPassword: getEnv("REDIS_PASSWORD", ""),
        JWTSecret:     getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-in-production"),
        OpenAIKey:     getEnv("OPENAI_API_KEY", ""),
        AnthropicKey:  getEnv("ANTHROPIC_API_KEY", ""),
        StripeKey:     getEnv("STRIPE_SECRET_KEY", ""),
        SMTPConfig: SMTPConfig{
            Host:     getEnv("SMTP_HOST", "smtp.gmail.com"),
            Port:     port,
            Username: getEnv("SMTP_USERNAME", ""),
            Password: getEnv("SMTP_PASSWORD", ""),
            From:     getEnv("SMTP_FROM", "<EMAIL>"),
        },
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}