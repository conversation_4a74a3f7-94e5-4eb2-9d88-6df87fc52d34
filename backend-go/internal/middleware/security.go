package middleware

import (
    "context"
    "fmt"
    "net/http"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/redis/go-redis/v9"
)

// SecurityHeaders adds security headers to all responses
func SecurityHeaders() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Content Security Policy
        c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'")
        
        // HSTS (HTTP Strict Transport Security)
        c.<PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        
        // Prevent clickjacking
        c.Header("X-Frame-Options", "DENY")
        
        // XSS Protection
        c.Header("X-XSS-Protection", "1; mode=block")
        
        // Content type sniffing protection
        c.<PERSON>er("X-Content-Type-Options", "nosniff")
        
        // Referrer Policy
        c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
        
        // Permissions Policy
        c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

        c.Next()
    }
}

// RateLimit implements Redis-based rate limiting
func RateLimit(rdb *redis.Client) gin.HandlerFunc {
    return func(c *gin.Context) {
        if rdb == nil {
            // If Redis is not available, skip rate limiting
            c.Next()
            return
        }

        // Get client IP
        clientIP := c.ClientIP()
        
        // Different limits for different endpoints
        var limit int
        var window time.Duration
        
        switch {
        case c.Request.URL.Path == "/api/auth/login":
            limit = 5  // 5 login attempts per 15 minutes
            window = 15 * time.Minute
        case c.Request.URL.Path == "/api/auth/register":
            limit = 3  // 3 registration attempts per hour
            window = time.Hour
        default:
            limit = 100 // 100 general API requests per 15 minutes
            window = 15 * time.Minute
        }

        // Create Redis key
        key := fmt.Sprintf("rate_limit:%s:%s", c.Request.URL.Path, clientIP)
        
        ctx := context.Background()
        
        // Get current count
        current, err := rdb.Get(ctx, key).Int()
        if err != nil && err != redis.Nil {
            // If Redis error, allow request but log it
            c.Next()
            return
        }

        if current >= limit {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": int(window.Seconds()),
            })
            c.Abort()
            return
        }

        // Increment counter
        pipe := rdb.Pipeline()
        pipe.Incr(ctx, key)
        if current == 0 {
            // Set expiration only on first request
            pipe.Expire(ctx, key, window)
        }
        _, err = pipe.Exec(ctx)
        if err != nil {
            // If Redis error, allow request but log it
            c.Next()
            return
        }

        // Add rate limit headers
        c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", limit))
        c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", limit-current-1))
        c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", time.Now().Add(window).Unix()))

        c.Next()
    }
}

// InputValidation middleware for basic input sanitization
func InputValidation() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Check Content-Length to prevent large payloads
        if c.Request.ContentLength > 10*1024*1024 { // 10MB limit
            c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "Request payload too large"})
            c.Abort()
            return
        }

        // Check for common malicious patterns in URL
        if containsMaliciousPatterns(c.Request.URL.Path) {
            c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
            c.Abort()
            return
        }

        c.Next()
    }
}

func containsMaliciousPatterns(input string) bool {
    patterns := []string{
        "../", "..\\", // Path traversal
        "<script", "</script>", // XSS
        "javascript:", "vbscript:", // Script injection
        "DROP TABLE", "DELETE FROM", "INSERT INTO", // SQL injection patterns
        "UNION SELECT", "OR 1=1", "' OR '1'='1",
    }
    
    for _, pattern := range patterns {
        if contains(input, pattern) {
            return true
        }
    }
    return false
}

func contains(s, substr string) bool {
    return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && 
        (hasPrefix(s, substr) || contains(s[1:], substr)))
}

func hasPrefix(s, prefix string) bool {
    return len(s) >= len(prefix) && s[0:len(prefix)] == prefix
}