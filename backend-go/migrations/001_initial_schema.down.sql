-- Drop triggers
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_email_sequences_updated_at ON email_sequences;
DROP TRIGGER IF EXISTS update_usage_updated_at ON usage;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS cleanup_expired_sessions();

-- Drop indexes
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_stripe_customer_id;
DROP INDEX IF EXISTS idx_email_sequences_user_id;
DROP INDEX IF EXISTS idx_email_sequences_created_at;
DROP INDEX IF EXISTS idx_email_sequences_search;
DROP INDEX IF EXISTS idx_usage_user_id_period;
DROP INDEX IF EXISTS idx_sessions_user_id;
DROP INDEX IF EXISTS idx_sessions_expires_at;

-- Drop tables
DROP TABLE IF EXISTS sessions;
DROP TABLE IF EXISTS usage;
DROP TABLE IF EXISTS email_sequences;
DROP TABLE IF EXISTS users;

-- Drop extensions
DROP EXTENSION IF EXISTS "uuid-ossp";