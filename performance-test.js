#!/usr/bin/env node

/**
 * NeuroColony Performance Testing Suite
 * Comprehensive benchmarking and load testing for all optimizations
 */

import { performance } from 'perf_hooks'
import fetch from 'node-fetch'
import { spawn } from 'child_process'
import fs from 'fs/promises'
import path from 'path'

class PerformanceTester {
  constructor() {
    this.baseUrl = 'http://localhost:5000'
    this.testResults = []
    this.config = {
      warmupRequests: 10,
      testRequests: 100,
      concurrentUsers: 10,
      testTimeout: 300000 // 5 minutes
    }
  }

  /**
   * Run complete performance test suite
   */
  async runCompleteSuite() {
    console.log('🚀 Starting NeuroColony Performance Test Suite')
    console.log('=' .repeat(60))

    const startTime = performance.now()

    try {
      // Test 1: API Response Times
      await this.testAPIPerformance()

      // Test 2: Database Query Performance
      await this.testDatabasePerformance()

      // Test 3: Cache Performance
      await this.testCachePerformance()

      // Test 4: AI Generation Performance
      await this.testAIGenerationPerformance()

      // Test 5: Memory Usage
      await this.testMemoryUsage()

      // Test 6: Concurrent User Load
      await this.testConcurrentLoad()

      // Test 7: End-to-End Performance
      await this.testEndToEndPerformance()

      const totalTime = performance.now() - startTime
      
      // Generate comprehensive report
      await this.generatePerformanceReport(totalTime)

    } catch (error) {
      console.error('❌ Performance test suite failed:', error)
      process.exit(1)
    }
  }

  /**
   * Test API response times
   */
  async testAPIPerformance() {
    console.log('\n📊 Testing API Performance...')
    
    const endpoints = [
      { path: '/api/health', method: 'GET', name: 'Health Check' },
      { path: '/api/test/health/system', method: 'GET', name: 'System Health' },
      { path: '/api/auth/me', method: 'GET', name: 'User Profile', auth: true }
    ]

    const apiResults = []

    for (const endpoint of endpoints) {
      console.log(`  Testing ${endpoint.name}...`)
      
      const times = []
      
      // Warmup
      await this.makeRequest(endpoint.path, endpoint.method)
      
      // Actual tests
      for (let i = 0; i < 50; i++) {
        const start = performance.now()
        
        try {
          const response = await this.makeRequest(endpoint.path, endpoint.method, endpoint.auth)
          const duration = performance.now() - start
          
          if (response.ok) {
            times.push(duration)
          }
        } catch (error) {
          console.warn(`    Request failed: ${error.message}`)
        }
      }

      const stats = this.calculateStats(times)
      apiResults.push({
        endpoint: endpoint.name,
        ...stats
      })

      console.log(`    Avg: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms, P99: ${stats.p99.toFixed(2)}ms`)
    }

    this.testResults.push({
      category: 'API Performance',
      results: apiResults,
      passed: apiResults.every(r => r.avg < 200) // Under 200ms average
    })
  }

  /**
   * Test database query performance
   */
  async testDatabasePerformance() {
    console.log('\n🗄️  Testing Database Performance...')

    const dbTests = [
      {
        name: 'User Sequences Query',
        simulate: () => this.simulateDatabaseQuery('EmailSequence.find', 50)
      },
      {
        name: 'Dashboard Analytics',
        simulate: () => this.simulateDatabaseQuery('EmailSequence.aggregate', 200)
      },
      {
        name: 'User Profile Lookup',
        simulate: () => this.simulateDatabaseQuery('User.findById', 20)
      }
    ]

    const dbResults = []

    for (const test of dbTests) {
      console.log(`  Testing ${test.name}...`)
      
      const times = []
      
      for (let i = 0; i < 20; i++) {
        const start = performance.now()
        await test.simulate()
        const duration = performance.now() - start
        times.push(duration)
      }

      const stats = this.calculateStats(times)
      dbResults.push({
        query: test.name,
        ...stats
      })

      console.log(`    Avg: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms`)
    }

    this.testResults.push({
      category: 'Database Performance',
      results: dbResults,
      passed: dbResults.every(r => r.avg < 100) // Under 100ms average
    })
  }

  /**
   * Test cache performance
   */
  async testCachePerformance() {
    console.log('\n⚡ Testing Cache Performance...')

    const cacheTests = [
      {
        name: 'Cache Hit Performance',
        test: async () => {
          // First request (cache miss)
          const start1 = performance.now()
          await this.makeRequest('/api/sequences?limit=10', 'GET', true)
          const miss = performance.now() - start1

          // Second request (cache hit)
          const start2 = performance.now()
          await this.makeRequest('/api/sequences?limit=10', 'GET', true)
          const hit = performance.now() - start2

          return { miss, hit, improvement: miss / hit }
        }
      }
    ]

    const cacheResults = []

    for (const test of cacheTests) {
      console.log(`  Testing ${test.name}...`)
      
      try {
        const result = await test.test()
        cacheResults.push({
          test: test.name,
          cacheMiss: result.miss,
          cacheHit: result.hit,
          improvement: result.improvement
        })

        console.log(`    Cache Miss: ${result.miss.toFixed(2)}ms, Cache Hit: ${result.hit.toFixed(2)}ms`)
        console.log(`    Improvement: ${result.improvement.toFixed(1)}x faster`)
      } catch (error) {
        console.warn(`    Cache test failed: ${error.message}`)
      }
    }

    this.testResults.push({
      category: 'Cache Performance',
      results: cacheResults,
      passed: cacheResults.every(r => r.improvement > 2) // At least 2x improvement
    })
  }

  /**
   * Test AI generation performance
   */
  async testAIGenerationPerformance() {
    console.log('\n🧠 Testing AI Generation Performance...')

    const aiTestPayload = {
      title: 'Performance Test Sequence',
      businessInfo: {
        industry: 'technology',
        productService: 'SaaS platform',
        targetAudience: 'small businesses',
        pricePoint: '$99/month'
      },
      generationSettings: {
        sequenceLength: 5,
        tone: 'professional',
        primaryGoal: 'sales'
      }
    }

    console.log('  Testing AI sequence generation...')

    const times = []
    let cacheHits = 0
    let cacheMisses = 0

    // Test with same payload to measure cache effectiveness
    for (let i = 0; i < 10; i++) {
      const start = performance.now()
      
      try {
        const response = await this.makeRequest('/api/sequences', 'POST', true, aiTestPayload)
        const duration = performance.now() - start
        times.push(duration)

        if (response.ok) {
          const data = await response.json()
          if (data.data._performance?.cached) {
            cacheHits++
          } else {
            cacheMisses++
          }
        }
      } catch (error) {
        console.warn(`    AI generation test failed: ${error.message}`)
      }

      // Add delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    const stats = this.calculateStats(times)
    
    this.testResults.push({
      category: 'AI Generation Performance',
      results: [{
        test: 'AI Sequence Generation',
        ...stats,
        cacheHits,
        cacheMisses,
        cacheHitRate: cacheHits / (cacheHits + cacheMisses)
      }],
      passed: stats.avg < 10000 // Under 10 seconds average
    })

    console.log(`    Avg: ${stats.avg.toFixed(0)}ms, Cache Hit Rate: ${(cacheHits / (cacheHits + cacheMisses) * 100).toFixed(1)}%`)
  }

  /**
   * Test memory usage
   */
  async testMemoryUsage() {
    console.log('\n💾 Testing Memory Usage...')

    const memoryBaseline = process.memoryUsage()
    console.log(`  Baseline Memory: ${Math.round(memoryBaseline.heapUsed / 1024 / 1024)}MB`)

    // Simulate memory-intensive operations
    const operations = [
      () => this.simulateSequenceGeneration(10),
      () => this.simulateDashboardLoading(5),
      () => this.simulateUserOperations(20)
    ]

    const memoryResults = []

    for (let i = 0; i < operations.length; i++) {
      const beforeMem = process.memoryUsage().heapUsed
      
      await operations[i]()
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const afterMem = process.memoryUsage().heapUsed
      const memoryIncrease = afterMem - beforeMem

      memoryResults.push({
        operation: `Operation ${i + 1}`,
        memoryIncrease: Math.round(memoryIncrease / 1024 / 1024),
        totalMemory: Math.round(afterMem / 1024 / 1024)
      })

      console.log(`    Operation ${i + 1}: +${Math.round(memoryIncrease / 1024 / 1024)}MB`)
    }

    this.testResults.push({
      category: 'Memory Usage',
      results: memoryResults,
      passed: memoryResults.every(r => r.memoryIncrease < 50) // Under 50MB increase per operation
    })
  }

  /**
   * Test concurrent user load
   */
  async testConcurrentLoad() {
    console.log('\n👥 Testing Concurrent User Load...')

    const concurrentUsers = 20
    const requestsPerUser = 10

    console.log(`  Simulating ${concurrentUsers} concurrent users, ${requestsPerUser} requests each...`)

    const userPromises = []
    const allTimes = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateUser(user, requestsPerUser)
      userPromises.push(userPromise)
    }

    const startTime = performance.now()
    const results = await Promise.allSettled(userPromises)
    const totalTime = performance.now() - startTime

    // Collect all response times
    results.forEach(result => {
      if (result.status === 'fulfilled') {
        allTimes.push(...result.value)
      }
    })

    const stats = this.calculateStats(allTimes)
    const throughput = (concurrentUsers * requestsPerUser) / (totalTime / 1000)

    this.testResults.push({
      category: 'Concurrent Load',
      results: [{
        concurrentUsers,
        requestsPerUser,
        totalRequests: concurrentUsers * requestsPerUser,
        totalTime: totalTime.toFixed(0),
        throughput: throughput.toFixed(1),
        ...stats
      }],
      passed: stats.avg < 1000 && throughput > 10 // Under 1s average and >10 req/s
    })

    console.log(`    Total Time: ${totalTime.toFixed(0)}ms, Throughput: ${throughput.toFixed(1)} req/s`)
    console.log(`    Avg Response: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms`)
  }

  /**
   * Test end-to-end performance
   */
  async testEndToEndPerformance() {
    console.log('\n🎯 Testing End-to-End Performance...')

    const e2eScenarios = [
      {
        name: 'User Registration to First Sequence',
        steps: [
          () => this.makeRequest('/api/auth/register', 'POST', false, {
            email: `test${Date.now()}@test.com`,
            password: 'testpass123',
            firstName: 'Test',
            lastName: 'User'
          }),
          () => this.makeRequest('/api/sequences', 'POST', true, {
            title: 'E2E Test Sequence',
            businessInfo: {
              industry: 'technology',
              productService: 'AI platform',
              targetAudience: 'developers',
              pricePoint: '$49/month'
            },
            generationSettings: {
              sequenceLength: 3,
              tone: 'professional',
              primaryGoal: 'sales'
            }
          })
        ]
      }
    ]

    const e2eResults = []

    for (const scenario of e2eScenarios) {
      console.log(`  Testing ${scenario.name}...`)
      
      const start = performance.now()
      
      try {
        for (const step of scenario.steps) {
          await step()
        }
        
        const duration = performance.now() - start
        e2eResults.push({
          scenario: scenario.name,
          duration,
          steps: scenario.steps.length
        })

        console.log(`    Completed in ${duration.toFixed(0)}ms`)
      } catch (error) {
        console.warn(`    E2E test failed: ${error.message}`)
      }
    }

    this.testResults.push({
      category: 'End-to-End Performance',
      results: e2eResults,
      passed: e2eResults.every(r => r.duration < 15000) // Under 15 seconds
    })
  }

  /**
   * Make HTTP request
   */
  async makeRequest(path, method = 'GET', auth = false, body = null) {
    const url = `${this.baseUrl}${path}`
    const headers = { 'Content-Type': 'application/json' }
    
    if (auth) {
      headers['Authorization'] = 'Bearer dummy-test-token'
    }

    const options = {
      method,
      headers,
      body: body ? JSON.stringify(body) : null
    }

    return fetch(url, options)
  }

  /**
   * Simulate database query
   */
  async simulateDatabaseQuery(query, baseDelay) {
    // Simulate database query delay
    const delay = baseDelay + Math.random() * 50
    await new Promise(resolve => setTimeout(resolve, delay))
  }

  /**
   * Simulate user behavior
   */
  async simulateUser(userId, requestCount) {
    const times = []
    
    const endpoints = [
      '/api/health',
      '/api/sequences?limit=5',
      '/api/sequences/analytics/dashboard'
    ]

    for (let i = 0; i < requestCount; i++) {
      const endpoint = endpoints[i % endpoints.length]
      const start = performance.now()
      
      try {
        await this.makeRequest(endpoint, 'GET', true)
        const duration = performance.now() - start
        times.push(duration)
      } catch (error) {
        // Ignore errors in load test
      }

      // Random delay between requests
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100))
    }

    return times
  }

  /**
   * Simulate sequence generation
   */
  async simulateSequenceGeneration(count) {
    for (let i = 0; i < count; i++) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  /**
   * Simulate dashboard loading
   */
  async simulateDashboardLoading(count) {
    for (let i = 0; i < count; i++) {
      await new Promise(resolve => setTimeout(resolve, 50))
    }
  }

  /**
   * Simulate user operations
   */
  async simulateUserOperations(count) {
    for (let i = 0; i < count; i++) {
      await new Promise(resolve => setTimeout(resolve, 25))
    }
  }

  /**
   * Calculate statistics from timing array
   */
  calculateStats(times) {
    if (times.length === 0) {
      return { avg: 0, min: 0, max: 0, p50: 0, p95: 0, p99: 0 }
    }

    const sorted = [...times].sort((a, b) => a - b)
    
    return {
      avg: times.reduce((sum, time) => sum + time, 0) / times.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    }
  }

  /**
   * Generate comprehensive performance report
   */
  async generatePerformanceReport(totalTime) {
    console.log('\n📋 Generating Performance Report...')

    const report = {
      timestamp: new Date().toISOString(),
      totalTestTime: totalTime,
      summary: {
        testsRun: this.testResults.length,
        testsPassed: this.testResults.filter(r => r.passed).length,
        testsFailed: this.testResults.filter(r => !r.passed).length
      },
      results: this.testResults,
      recommendations: this.generateRecommendations()
    }

    // Save to file
    const reportPath = path.join(process.cwd(), `performance-report-${Date.now()}.json`)
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))

    // Print summary
    console.log('\n' + '='.repeat(60))
    console.log('📊 PERFORMANCE TEST RESULTS')
    console.log('='.repeat(60))
    
    console.log(`Total Test Time: ${(totalTime / 1000).toFixed(1)}s`)
    console.log(`Tests Passed: ${report.summary.testsPassed}/${report.summary.testsRun}`)
    
    if (report.summary.testsFailed > 0) {
      console.log('❌ Some tests failed:')
      this.testResults.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.category}`)
      })
    } else {
      console.log('✅ All performance tests passed!')
    }

    console.log(`\nDetailed report saved to: ${reportPath}`)

    // Print recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 Performance Recommendations:')
      report.recommendations.forEach((rec, i) => {
        console.log(`  ${i + 1}. ${rec}`)
      })
    }
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = []

    // Check API performance
    const apiResults = this.testResults.find(r => r.category === 'API Performance')
    if (apiResults && !apiResults.passed) {
      recommendations.push('Optimize API response times - some endpoints are slower than 200ms')
    }

    // Check cache effectiveness
    const cacheResults = this.testResults.find(r => r.category === 'Cache Performance')
    if (cacheResults && !cacheResults.passed) {
      recommendations.push('Improve cache hit rates - cache is not providing sufficient performance improvement')
    }

    // Check memory usage
    const memoryResults = this.testResults.find(r => r.category === 'Memory Usage')
    if (memoryResults && !memoryResults.passed) {
      recommendations.push('Optimize memory usage - operations are consuming too much memory')
    }

    // Check concurrent load
    const loadResults = this.testResults.find(r => r.category === 'Concurrent Load')
    if (loadResults && !loadResults.passed) {
      recommendations.push('Improve concurrent user handling - system struggles under load')
    }

    return recommendations
  }
}

// Run the performance test suite
const tester = new PerformanceTester()
tester.runCompleteSuite().catch(console.error)