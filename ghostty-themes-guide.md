# Ghostty Terminal Configuration Guide
## Complete Theme Collection & Customization Manual

### Table of Contents
1. [Master Configuration Plan](#master-configuration-plan)
2. [Installation & Setup](#installation--setup)
3. [Theme Collection](#theme-collection)
4. [Configuration Files](#configuration-files)
5. [Implementation Guide](#implementation-guide)
6. [Customization Tips](#customization-tips)
7. [Troubleshooting](#troubleshooting)

---

## Master Configuration Plan

### Directory Structure
```
~/.config/ghostty/
├── config                          # Main configuration file
├── themes/                         # Custom theme directory
│   ├── cyberpunk-matrix.conf      # Matrix-style hacker theme
│   ├── military-tactical.conf     # WW2 missile control aesthetic
│   ├── synthwave-neon.conf        # Anime-inspired synthwave
│   ├── cyberpunk-anime.conf       # Cyberpunk anime theme
│   ├── minimal-dev.conf           # Modern minimalist developer
│   ├── retro-amber.conf           # Vintage CRT amber
│   ├── retro-ibm.conf             # Classic IBM terminal
│   ├── high-contrast.conf         # Accessibility theme
│   ├── custom-brand.conf          # Custom branded theme
│   └── tokyo-night.conf           # Popular developer theme
└── backups/                       # Configuration backups
    └── config.backup.YYYY-MM-DD
```

### Theme Management Strategy

#### 1. Base Configuration Approach
- **Main Config**: Contains universal settings (fonts, keybinds, window behavior)
- **Theme Files**: Focus purely on colors and visual aesthetics
- **Modular Design**: Easy switching between themes without losing personal settings

#### 2. Theme Categories
- **Hacker/Cyberpunk**: Matrix-style, terminal aesthetics
- **Military/Tactical**: Command center, radar-inspired themes
- **Anime/Synthwave**: Neon colors, retro-futuristic aesthetics
- **Developer/Minimalist**: Clean, productivity-focused themes
- **Retro/Vintage**: CRT monitor, classic computing aesthetics
- **Accessibility**: High contrast, colorblind-friendly themes
- **Custom/Branded**: Personalized color schemes

#### 3. Font Recommendations by Theme Category
- **Cyberpunk/Hacker**: JetBrains Mono, Fira Code (with ligatures)
- **Military/Tactical**: Consolas, Cascadia Code
- **Anime/Synthwave**: Iosevka, Victor Mono
- **Developer/Minimalist**: SF Mono, Source Code Pro
- **Retro/Vintage**: IBM Plex Mono, Courier Prime
- **Accessibility**: Atkinson Hyperlegible, OpenDyslexic Mono

---

## Installation & Setup

### Prerequisites
1. **Ghostty Terminal** installed and working
2. **Font Installation** (recommended fonts for each theme)
3. **Configuration Directory** setup

### Initial Setup Steps

#### 1. Create Configuration Directory
```bash
# Create the main config directory
mkdir -p ~/.config/ghostty/themes
mkdir -p ~/.config/ghostty/backups

# Backup existing configuration (if any)
if [ -f ~/.config/ghostty/config ]; then
    cp ~/.config/ghostty/config ~/.config/ghostty/backups/config.backup.$(date +%Y-%m-%d)
fi
```

#### 2. Install Recommended Fonts
```bash
# On macOS with Homebrew
brew install font-jetbrains-mono font-fira-code font-cascadia-code font-iosevka

# On Ubuntu/Debian
sudo apt install fonts-jetbrains-mono fonts-firacode fonts-cascadia-code

# On Arch Linux
sudo pacman -S ttf-jetbrains-mono ttf-fira-code ttf-cascadia-code ttf-iosevka
```

#### 3. Base Configuration Setup
Create a base configuration file that works with all themes:

```bash
# Create base config
cat > ~/.config/ghostty/config << 'EOF'
# Ghostty Base Configuration
# This file contains universal settings that work with all themes

# Font Configuration
font-family = "JetBrains Mono"
font-size = 14
font-feature = +calt +liga +dlig

# Window Settings
window-decoration = true
window-padding-x = 8
window-padding-y = 8
window-inherit-working-directory = true
window-inherit-font-size = true

# Terminal Behavior
scrollback-limit = 100000
cursor-style = block
cursor-style-blink = true
mouse-hide-while-typing = true
copy-on-select = false

# Shell Integration
shell-integration = detect
shell-integration-features = cursor,sudo,title

# Theme Selection (change this line to switch themes)
theme = cyberpunk-matrix

# Keybindings
keybind = ctrl+shift+c=copy_to_clipboard
keybind = ctrl+shift+v=paste_from_clipboard
keybind = ctrl+shift+t=new_tab
keybind = ctrl+shift+w=close_surface
keybind = ctrl+shift+n=new_window
keybind = ctrl+shift+plus=increase_font_size:1
keybind = ctrl+shift+minus=decrease_font_size:1
keybind = ctrl+shift+zero=reset_font_size
EOF
```

---

## Theme Collection

### Overview
This collection includes 10 carefully crafted themes covering different aesthetics and use cases. Each theme is designed to be visually striking while maintaining excellent readability and usability.

### Theme Categories Summary

| Theme Name | Category | Primary Colors | Best For |
|------------|----------|----------------|----------|
| Cyberpunk Matrix | Hacker/Cyberpunk | Green on Black | Terminal work, coding |
| Military Tactical | Military/Command | Amber/Orange on Dark | System administration |
| Synthwave Neon | Anime/Retro | Pink/Cyan/Purple | Creative work |
| Cyberpunk Anime | Anime/Cyberpunk | Blue/Pink/Cyan | Development, gaming |
| Minimal Dev | Developer/Clean | Subtle grays/blues | Professional work |
| Retro Amber | Vintage/CRT | Amber on Black | Nostalgic computing |
| Retro IBM | Classic/Vintage | Green on Black | Traditional terminal |
| High Contrast | Accessibility | Black/White/Yellow | Visual accessibility |
| Custom Brand | Personalized | Customizable | Brand consistency |
| Tokyo Night | Popular/Modern | Purple/Blue/Pink | General development |

### Detailed Theme Descriptions

#### 1. Cyberpunk Matrix Theme
**Aesthetic**: Classic hacker terminal, Matrix movie inspired
**Colors**: Bright green text on deep black background
**Mood**: Mysterious, focused, cyberpunk
**Best For**: Terminal-heavy work, system administration, coding

#### 2. Military Tactical Theme
**Aesthetic**: WW2 missile control room, radar displays
**Colors**: Amber/orange text on dark military green
**Mood**: Professional, tactical, command center
**Best For**: System monitoring, DevOps work, server management

#### 3. Synthwave Neon Theme
**Aesthetic**: 80s synthwave, neon lights, retro-futuristic
**Colors**: Hot pink, cyan, purple gradients
**Mood**: Creative, energetic, nostalgic
**Best For**: Creative coding, design work, evening sessions

#### 4. Cyberpunk Anime Theme
**Aesthetic**: Ghost in the Shell, cyberpunk anime
**Colors**: Electric blue, hot pink, cyan accents
**Mood**: Futuristic, dynamic, high-tech
**Best For**: Game development, creative projects

#### 5. Minimal Dev Theme
**Aesthetic**: Clean, modern, professional
**Colors**: Subtle grays, soft blues, minimal contrast
**Mood**: Calm, focused, professional
**Best For**: Long coding sessions, professional environments

#### 6. Retro Amber Theme
**Aesthetic**: Vintage CRT monitor, 1980s computing
**Colors**: Warm amber text on black background
**Mood**: Nostalgic, warm, classic
**Best For**: Writing, documentation, retro computing

#### 7. Retro IBM Theme
**Aesthetic**: Classic IBM terminals, mainframe computing
**Colors**: Bright green on black, classic terminal
**Mood**: Traditional, reliable, professional
**Best For**: System administration, traditional Unix work

#### 8. High Contrast Theme
**Aesthetic**: Maximum readability, accessibility focused
**Colors**: Pure black/white with yellow highlights
**Mood**: Clear, accessible, functional
**Best For**: Visual impairments, bright environments

#### 9. Custom Brand Theme
**Aesthetic**: Customizable for personal/company branding
**Colors**: Template with placeholder values
**Mood**: Professional, branded, consistent
**Best For**: Corporate environments, personal branding

#### 10. Tokyo Night Theme
**Aesthetic**: Popular developer theme, modern dark
**Colors**: Deep purple background with blue/pink accents
**Mood**: Modern, popular, developer-friendly
**Best For**: General development work, popular choice

---

## Configuration Files

### Theme File Format
Each theme file follows Ghostty's configuration syntax and focuses on color settings. Here are the complete configuration files for each theme:

### 1. Cyberpunk Matrix Theme
**File**: `~/.config/ghostty/themes/cyberpunk-matrix.conf`

```conf
# Cyberpunk Matrix Theme
# Classic hacker terminal aesthetic inspired by The Matrix

# Core Colors
background = #000000
foreground = #00ff00
cursor-color = #00ff00
cursor-text = #000000

# Selection Colors
selection-background = #003300
selection-foreground = #00ff00

# 16-Color Palette (ANSI Colors)
palette = 0=#000000   # Black
palette = 1=#ff0000   # Red
palette = 2=#00ff00   # Green (Matrix green)
palette = 3=#ffff00   # Yellow
palette = 4=#0000ff   # Blue
palette = 5=#ff00ff   # Magenta
palette = 6=#00ffff   # Cyan
palette = 7=#c0c0c0   # Light Gray
palette = 8=#808080   # Dark Gray
palette = 9=#ff0000   # Bright Red
palette = 10=#00ff41  # Bright Green (Matrix bright)
palette = 11=#ffff00  # Bright Yellow
palette = 12=#0066ff  # Bright Blue
palette = 13=#ff00ff  # Bright Magenta
palette = 14=#00ffff  # Bright Cyan
palette = 15=#ffffff  # White

# Font Settings (optimized for Matrix aesthetic)
font-family = "JetBrains Mono"
font-size = 14
font-feature = +calt

# Visual Effects
background-opacity = 0.95
cursor-style = block
cursor-style-blink = true
```

### 2. Military Tactical Theme
**File**: `~/.config/ghostty/themes/military-tactical.conf`

```conf
# Military Tactical Theme
# WW2 missile control room / radar display aesthetic

# Core Colors
background = #0a0f0a
foreground = #ff8c00
cursor-color = #ffaa00
cursor-text = #0a0f0a

# Selection Colors
selection-background = #2d4a2d
selection-foreground = #ff8c00

# 16-Color Palette (Military/Tactical Colors)
palette = 0=#0a0f0a   # Deep Military Green
palette = 1=#cc3300   # Alert Red
palette = 2=#66cc00   # Status Green
palette = 3=#ff8c00   # Amber Warning
palette = 4=#3366cc   # Tactical Blue
palette = 5=#cc6600   # Orange Alert
palette = 6=#00cccc   # Cyan Info
palette = 7=#cccccc   # Light Gray
palette = 8=#666666   # Dark Gray
palette = 9=#ff4444   # Bright Alert Red
palette = 10=#88ff00  # Bright Status Green
palette = 11=#ffaa00  # Bright Amber
palette = 12=#4488ff  # Bright Tactical Blue
palette = 13=#ff8800  # Bright Orange
palette = 14=#44ffff  # Bright Cyan
palette = 15=#ffffff  # White

# Font Settings
font-family = "Cascadia Code"
font-size = 13
font-feature = +calt

# Visual Effects
background-opacity = 0.98
cursor-style = block
cursor-style-blink = false
```

### 3. Synthwave Neon Theme
**File**: `~/.config/ghostty/themes/synthwave-neon.conf`

```conf
# Synthwave Neon Theme
# 80s synthwave aesthetic with neon colors

# Core Colors
background = #1a0d1a
foreground = #ff00ff
cursor-color = #00ffff
cursor-text = #1a0d1a

# Selection Colors
selection-background = #4d1a4d
selection-foreground = #ff00ff

# 16-Color Palette (Synthwave/Neon Colors)
palette = 0=#1a0d1a   # Deep Purple Black
palette = 1=#ff0080   # Hot Pink
palette = 2=#00ff80   # Neon Green
palette = 3=#ffff00   # Electric Yellow
palette = 4=#0080ff   # Electric Blue
palette = 5=#ff00ff   # Magenta Neon
palette = 6=#00ffff   # Cyan Neon
palette = 7=#cccccc   # Light Gray
palette = 8=#666666   # Dark Gray
palette = 9=#ff44aa   # Bright Hot Pink
palette = 10=#44ff88  # Bright Neon Green
palette = 11=#ffff44  # Bright Electric Yellow
palette = 12=#44aaff  # Bright Electric Blue
palette = 13=#ff44ff  # Bright Magenta
palette = 14=#44ffff  # Bright Cyan
palette = 15=#ffffff  # Pure White

# Font Settings
font-family = "Iosevka"
font-size = 14
font-feature = +calt +liga

# Visual Effects
background-opacity = 0.92
background-blur = 15
cursor-style = bar
cursor-style-blink = true
```

### 4. Cyberpunk Anime Theme
**File**: `~/.config/ghostty/themes/cyberpunk-anime.conf`

```conf
# Cyberpunk Anime Theme
# Ghost in the Shell / cyberpunk anime inspired

# Core Colors
background = #0d1117
foreground = #58a6ff
cursor-color = #ff7b72
cursor-text = #0d1117

# Selection Colors
selection-background = #264f78
selection-foreground = #58a6ff

# 16-Color Palette (Cyberpunk Anime Colors)
palette = 0=#0d1117   # Deep Space Blue
palette = 1=#ff7b72   # Cyber Red
palette = 2=#7ee787   # Matrix Green
palette = 3=#f9e2af   # Electric Yellow
palette = 4=#58a6ff   # Cyber Blue
palette = 5=#bc8cff   # Neon Purple
palette = 6=#39c5cf   # Cyber Cyan
palette = 7=#b1bac4   # Light Gray
palette = 8=#6e7681   # Dark Gray
palette = 9=#ffa198   # Bright Cyber Red
palette = 10=#56d364  # Bright Matrix Green
palette = 11=#e3b341  # Bright Electric Yellow
palette = 12=#79c0ff  # Bright Cyber Blue
palette = 13=#d2a8ff  # Bright Neon Purple
palette = 14=#56d4dd  # Bright Cyber Cyan
palette = 15=#f0f6fc  # Pure White

# Font Settings
font-family = "Victor Mono"
font-size = 14
font-feature = +calt +liga +dlig

# Visual Effects
background-opacity = 0.94
cursor-style = block
cursor-style-blink = true
```

### 5. Minimal Dev Theme
**File**: `~/.config/ghostty/themes/minimal-dev.conf`

```conf
# Minimal Dev Theme
# Clean, modern, professional developer theme

# Core Colors
background = #fafafa
foreground = #383a42
cursor-color = #526fff
cursor-text = #fafafa

# Selection Colors
selection-background = #e5e5e6
selection-foreground = #383a42

# 16-Color Palette (Minimal/Clean Colors)
palette = 0=#fafafa   # Pure White Background
palette = 1=#e45649   # Soft Red
palette = 2=#50a14f   # Soft Green
palette = 3=#c18401   # Soft Yellow
palette = 4=#4078f2   # Soft Blue
palette = 5=#a626a4   # Soft Purple
palette = 6=#0184bc   # Soft Cyan
palette = 7=#383a42   # Dark Gray
palette = 8=#a0a1a7   # Medium Gray
palette = 9=#e45649   # Bright Soft Red
palette = 10=#50a14f  # Bright Soft Green
palette = 11=#c18401  # Bright Soft Yellow
palette = 12=#4078f2  # Bright Soft Blue
palette = 13=#a626a4  # Bright Soft Purple
palette = 14=#0184bc  # Bright Soft Cyan
palette = 15=#090a0b  # Near Black

# Font Settings
font-family = "SF Mono"
font-size = 13
font-feature = +calt

# Visual Effects
background-opacity = 1.0
cursor-style = bar
cursor-style-blink = false
```

### 6. Retro Amber Theme
**File**: `~/.config/ghostty/themes/retro-amber.conf`

```conf
# Retro Amber Theme
# Vintage CRT monitor aesthetic with warm amber glow

# Core Colors
background = #000000
foreground = #ffb000
cursor-color = #ffb000
cursor-text = #000000

# Selection Colors
selection-background = #332200
selection-foreground = #ffb000

# 16-Color Palette (Amber/Vintage Colors)
palette = 0=#000000   # Pure Black
palette = 1=#cc6600   # Dark Orange
palette = 2=#ffb000   # Amber
palette = 3=#ffcc00   # Bright Amber
palette = 4=#996600   # Dark Brown
palette = 5=#cc9900   # Golden Brown
palette = 6=#ffcc66   # Light Amber
palette = 7=#ccaa66   # Warm Gray
palette = 8=#664400   # Dark Amber
palette = 9=#ff8800   # Bright Orange
palette = 10=#ffcc00  # Bright Amber
palette = 11=#ffdd44  # Very Bright Amber
palette = 12=#cc9966  # Light Brown
palette = 13=#ffbb33  # Golden Amber
palette = 14=#ffdd88  # Very Light Amber
palette = 15=#ffffcc  # Warm White

# Font Settings
font-family = "IBM Plex Mono"
font-size = 14
font-feature = +calt

# Visual Effects
background-opacity = 1.0
cursor-style = block
cursor-style-blink = true
```

### 7. Retro IBM Theme
**File**: `~/.config/ghostty/themes/retro-ibm.conf`

```conf
# Retro IBM Theme
# Classic IBM terminal / mainframe computing aesthetic

# Core Colors
background = #000000
foreground = #00aa00
cursor-color = #00aa00
cursor-text = #000000

# Selection Colors
selection-background = #002200
selection-foreground = #00aa00

# 16-Color Palette (Classic IBM Colors)
palette = 0=#000000   # Black
palette = 1=#aa0000   # Dark Red
palette = 2=#00aa00   # IBM Green
palette = 3=#aa5500   # Brown
palette = 4=#0000aa   # Dark Blue
palette = 5=#aa00aa   # Dark Magenta
palette = 6=#00aaaa   # Dark Cyan
palette = 7=#aaaaaa   # Light Gray
palette = 8=#555555   # Dark Gray
palette = 9=#ff5555   # Bright Red
palette = 10=#55ff55  # Bright Green
palette = 11=#ffff55  # Bright Yellow
palette = 12=#5555ff  # Bright Blue
palette = 13=#ff55ff  # Bright Magenta
palette = 14=#55ffff  # Bright Cyan
palette = 15=#ffffff  # White

# Font Settings
font-family = "Courier Prime"
font-size = 14

# Visual Effects
background-opacity = 1.0
cursor-style = block
cursor-style-blink = false
```

### 8. High Contrast Theme
**File**: `~/.config/ghostty/themes/high-contrast.conf`

```conf
# High Contrast Theme
# Maximum readability and accessibility

# Core Colors
background = #000000
foreground = #ffffff
cursor-color = #ffff00
cursor-text = #000000

# Selection Colors
selection-background = #ffffff
selection-foreground = #000000

# 16-Color Palette (High Contrast Colors)
palette = 0=#000000   # Pure Black
palette = 1=#ff0000   # Pure Red
palette = 2=#00ff00   # Pure Green
palette = 3=#ffff00   # Pure Yellow
palette = 4=#0000ff   # Pure Blue
palette = 5=#ff00ff   # Pure Magenta
palette = 6=#00ffff   # Pure Cyan
palette = 7=#ffffff   # Pure White
palette = 8=#808080   # Medium Gray
palette = 9=#ff8080   # Light Red
palette = 10=#80ff80  # Light Green
palette = 11=#ffff80  # Light Yellow
palette = 12=#8080ff  # Light Blue
palette = 13=#ff80ff  # Light Magenta
palette = 14=#80ffff  # Light Cyan
palette = 15=#ffffff  # Pure White

# Font Settings
font-family = "Atkinson Hyperlegible"
font-size = 15
font-thicken = true

# Visual Effects
background-opacity = 1.0
cursor-style = block
cursor-style-blink = true
minimum-contrast = 7.0
```

### 9. Custom Brand Theme (Template)
**File**: `~/.config/ghostty/themes/custom-brand.conf`

```conf
# Custom Brand Theme Template
# Customize these colors for your personal/company branding

# Core Colors (Replace with your brand colors)
background = #1e1e2e    # Your primary dark color
foreground = #cdd6f4    # Your primary text color
cursor-color = #f38ba8  # Your accent color
cursor-text = #1e1e2e

# Selection Colors
selection-background = #585b70
selection-foreground = #cdd6f4

# 16-Color Palette (Customize these)
palette = 0=#45475a    # Dark shade
palette = 1=#f38ba8    # Brand red/pink
palette = 2=#a6e3a1    # Brand green
palette = 3=#f9e2af    # Brand yellow
palette = 4=#89b4fa    # Brand blue
palette = 5=#cba6f7    # Brand purple
palette = 6=#94e2d5    # Brand cyan
palette = 7=#bac2de    # Light gray
palette = 8=#585b70    # Medium gray
palette = 9=#f38ba8    # Bright brand red/pink
palette = 10=#a6e3a1   # Bright brand green
palette = 11=#f9e2af   # Bright brand yellow
palette = 12=#89b4fa   # Bright brand blue
palette = 13=#cba6f7   # Bright brand purple
palette = 14=#94e2d5   # Bright brand cyan
palette = 15=#a6adc8   # Light text

# Font Settings
font-family = "Source Code Pro"
font-size = 14
font-feature = +calt

# Visual Effects
background-opacity = 0.98
cursor-style = bar
cursor-style-blink = false
```

### 10. Tokyo Night Theme
**File**: `~/.config/ghostty/themes/tokyo-night.conf`

```conf
# Tokyo Night Theme
# Popular modern developer theme with deep purple background

# Core Colors
background = #1a1b26
foreground = #c0caf5
cursor-color = #c0caf5
cursor-text = #1a1b26

# Selection Colors
selection-background = #33467c
selection-foreground = #c0caf5

# 16-Color Palette (Tokyo Night Colors)
palette = 0=#15161e    # Darker background
palette = 1=#f7768e    # Red
palette = 2=#9ece6a    # Green
palette = 3=#e0af68    # Yellow
palette = 4=#7aa2f7    # Blue
palette = 5=#bb9af7    # Purple
palette = 6=#7dcfff    # Cyan
palette = 7=#a9b1d6    # Light gray
palette = 8=#414868    # Dark gray
palette = 9=#f7768e    # Bright red
palette = 10=#9ece6a   # Bright green
palette = 11=#e0af68   # Bright yellow
palette = 12=#7aa2f7   # Bright blue
palette = 13=#bb9af7   # Bright purple
palette = 14=#7dcfff   # Bright cyan
palette = 15=#c0caf5   # White

# Font Settings
font-family = "JetBrains Mono"
font-size = 14
font-feature = +calt +liga

# Visual Effects
background-opacity = 0.96
cursor-style = block
cursor-style-blink = false
```

---

## Implementation Guide

### Step-by-Step Installation

#### Step 1: Backup Current Configuration
```bash
# Create backup directory if it doesn't exist
mkdir -p ~/.config/ghostty/backups

# Backup current config with timestamp
if [ -f ~/.config/ghostty/config ]; then
    cp ~/.config/ghostty/config ~/.config/ghostty/backups/config.backup.$(date +%Y-%m-%d-%H%M%S)
    echo "✅ Configuration backed up successfully"
else
    echo "ℹ️  No existing configuration found"
fi
```

#### Step 2: Create Theme Directory
```bash
# Create themes directory
mkdir -p ~/.config/ghostty/themes
echo "✅ Theme directory created"
```

#### Step 3: Install Theme Files
You can install themes individually or all at once:

**Option A: Install All Themes**
```bash
# Navigate to your themes directory
cd ~/.config/ghostty/themes

# Create all theme files (copy the content from the Configuration Files section above)
# Or download from a repository if available
```

**Option B: Install Individual Themes**
```bash
# Example: Install just the Cyberpunk Matrix theme
cat > ~/.config/ghostty/themes/cyberpunk-matrix.conf << 'EOF'
# [Copy the Cyberpunk Matrix theme content from above]
EOF
```

#### Step 4: Update Main Configuration
```bash
# Edit your main config file
nano ~/.config/ghostty/config

# Add or modify the theme line:
theme = cyberpunk-matrix
```

#### Step 5: Test Configuration
```bash
# Test the configuration for syntax errors
ghostty --config-file ~/.config/ghostty/config --validate

# If validation passes, reload Ghostty
# Use Ctrl+Shift+R or restart the application
```

### Theme Switching Methods

#### Method 1: Edit Configuration File
```bash
# Edit main config
nano ~/.config/ghostty/config

# Change the theme line:
theme = synthwave-neon

# Reload configuration (Ctrl+Shift+R in Ghostty)
```

#### Method 2: Command Line Override
```bash
# Launch Ghostty with a specific theme
ghostty --theme tokyo-night

# Launch with custom theme file
ghostty --theme ~/.config/ghostty/themes/military-tactical.conf
```

#### Method 3: Multiple Configuration Files
```bash
# Create separate config files for different setups
cp ~/.config/ghostty/config ~/.config/ghostty/config-cyberpunk
cp ~/.config/ghostty/config ~/.config/ghostty/config-minimal

# Edit each config file with different themes
# Launch with specific config:
ghostty --config-file ~/.config/ghostty/config-cyberpunk
```

### Font Installation Guide

#### Installing Recommended Fonts

**macOS (using Homebrew)**
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install fonts
brew tap homebrew/cask-fonts
brew install --cask font-jetbrains-mono
brew install --cask font-fira-code
brew install --cask font-cascadia-code
brew install --cask font-iosevka
brew install --cask font-victor-mono
brew install --cask font-ibm-plex-mono
brew install --cask font-source-code-pro
```

**Ubuntu/Debian**
```bash
# Update package list
sudo apt update

# Install fonts
sudo apt install fonts-jetbrains-mono fonts-firacode fonts-cascadia-code
sudo apt install fonts-ibm-plex fonts-source-code-pro

# For additional fonts, download manually:
# Iosevka: https://github.com/be5invis/Iosevka/releases
# Victor Mono: https://rubjo.github.io/victor-mono/
```

**Arch Linux**
```bash
# Install from official repositories
sudo pacman -S ttf-jetbrains-mono ttf-fira-code ttf-cascadia-code
sudo pacman -S ttf-ibm-plex ttf-source-code-pro

# Install from AUR for additional fonts
yay -S ttf-iosevka ttf-victor-mono
```

**Manual Installation**
```bash
# Create fonts directory
mkdir -p ~/.local/share/fonts

# Download and extract font files to this directory
# Then refresh font cache
fc-cache -fv
```

### Creating Custom Variations

#### Customizing Existing Themes
```bash
# Copy an existing theme as a starting point
cp ~/.config/ghostty/themes/cyberpunk-matrix.conf ~/.config/ghostty/themes/my-custom-matrix.conf

# Edit the new theme file
nano ~/.config/ghostty/themes/my-custom-matrix.conf

# Modify colors, fonts, or other settings as desired
```

#### Color Customization Tips
```conf
# Adjust background opacity for transparency effects
background-opacity = 0.85

# Add blur effect (macOS and some Linux environments)
background-blur = 20

# Modify cursor appearance
cursor-style = bar          # Options: block, bar, underline, block_hollow
cursor-style-blink = true   # Enable/disable cursor blinking
cursor-opacity = 0.8        # Cursor transparency

# Fine-tune selection colors
selection-invert-fg-bg = true  # Invert colors for selection
```

#### Font Customization
```conf
# Enable font ligatures for programming
font-feature = +calt +liga +dlig

# Adjust font weight for better readability
font-variation = wght=400

# Use different fonts for different styles
font-family-bold = "JetBrains Mono Bold"
font-family-italic = "JetBrains Mono Italic"

# Adjust font rendering
font-thicken = true
font-thicken-strength = 128
```

---

## Customization Tips

### Advanced Color Techniques

#### 1. Color Psychology for Different Work Types
- **Coding/Development**: Cool blues and greens reduce eye strain
- **Writing/Documentation**: Warm colors like amber improve focus
- **System Administration**: High contrast colors for quick error spotting
- **Creative Work**: Vibrant colors can boost creativity

#### 2. Accessibility Considerations
```conf
# High contrast for visual impairments
minimum-contrast = 7.0

# Larger cursor for better visibility
adjust-cursor-height = 2
adjust-cursor-thickness = 2

# Thicker fonts for better readability
font-thicken = true
font-thicken-strength = 200
```

#### 3. Environment-Specific Themes
```conf
# Bright environment theme (higher contrast)
background = #ffffff
foreground = #000000
minimum-contrast = 4.5

# Dark environment theme (reduced brightness)
background = #000000
foreground = #808080
background-opacity = 0.9
```

### Performance Optimization

#### 1. Reduce Visual Effects for Better Performance
```conf
# Disable transparency for better performance
background-opacity = 1.0
background-blur = false

# Disable cursor blinking to reduce redraws
cursor-style-blink = false

# Optimize scrollback for memory usage
scrollback-limit = 50000
```

#### 2. Font Optimization
```conf
# Disable ligatures if causing performance issues
font-feature = -calt -liga

# Use system fonts for better performance
font-family = "monospace"  # Uses system default monospace font
```

### Multi-Monitor Setup

#### Different Themes for Different Monitors
```bash
# Create monitor-specific configs
cp ~/.config/ghostty/config ~/.config/ghostty/config-main-monitor
cp ~/.config/ghostty/config ~/.config/ghostty/config-secondary-monitor

# Edit each config with appropriate themes
# Main monitor (bright): minimal-dev theme
# Secondary monitor (dark): cyberpunk-matrix theme

# Launch with specific configs:
ghostty --config-file ~/.config/ghostty/config-main-monitor &
ghostty --config-file ~/.config/ghostty/config-secondary-monitor &
```

### Workflow-Specific Customizations

#### 1. Development Workflow
```conf
# Theme: minimal-dev or tokyo-night
theme = minimal-dev

# Larger font for long coding sessions
font-size = 15

# Enable programming ligatures
font-feature = +calt +liga +dlig

# Comfortable cursor
cursor-style = bar
cursor-style-blink = false
```

#### 2. System Administration
```conf
# Theme: military-tactical or high-contrast
theme = military-tactical

# Smaller font to see more information
font-size = 12

# Block cursor for better visibility
cursor-style = block
cursor-style-blink = true

# High scrollback for log analysis
scrollback-limit = 200000
```

#### 3. Creative/Design Work
```conf
# Theme: synthwave-neon or cyberpunk-anime
theme = synthwave-neon

# Stylish fonts with ligatures
font-family = "Victor Mono"
font-feature = +calt +liga +dlig

# Transparency for layering
background-opacity = 0.85
background-blur = 15
```

### Seasonal/Time-Based Themes

#### Automatic Light/Dark Mode Switching
```conf
# Use different themes for light and dark mode
theme = light:minimal-dev,dark:tokyo-night

# This automatically switches based on system appearance
```

#### Manual Time-Based Switching
```bash
#!/bin/bash
# Script: ~/.local/bin/ghostty-theme-switcher.sh

HOUR=$(date +%H)
CONFIG_FILE="$HOME/.config/ghostty/config"

if [ $HOUR -ge 6 ] && [ $HOUR -lt 18 ]; then
    # Daytime theme (6 AM - 6 PM)
    sed -i 's/^theme = .*/theme = minimal-dev/' "$CONFIG_FILE"
else
    # Nighttime theme (6 PM - 6 AM)
    sed -i 's/^theme = .*/theme = tokyo-night/' "$CONFIG_FILE"
fi

# Reload Ghostty configuration
pkill -USR1 ghostty 2>/dev/null || true
```

### Color Palette Generators

#### Creating Custom Palettes
```bash
# Use online tools to generate color palettes:
# - coolors.co
# - paletton.com
# - adobe.com/products/color.html

# Convert hex colors to Ghostty format:
# Example: #ff6b6b becomes palette = 1=#ff6b6b
```

#### Testing Color Combinations
```bash
# Create a test script to preview colors
cat > ~/.local/bin/test-ghostty-colors.sh << 'EOF'
#!/bin/bash
echo -e "\033[30mBlack (0)\033[0m"
echo -e "\033[31mRed (1)\033[0m"
echo -e "\033[32mGreen (2)\033[0m"
echo -e "\033[33mYellow (3)\033[0m"
echo -e "\033[34mBlue (4)\033[0m"
echo -e "\033[35mMagenta (5)\033[0m"
echo -e "\033[36mCyan (6)\033[0m"
echo -e "\033[37mWhite (7)\033[0m"
echo -e "\033[90mBright Black (8)\033[0m"
echo -e "\033[91mBright Red (9)\033[0m"
echo -e "\033[92mBright Green (10)\033[0m"
echo -e "\033[93mBright Yellow (11)\033[0m"
echo -e "\033[94mBright Blue (12)\033[0m"
echo -e "\033[95mBright Magenta (13)\033[0m"
echo -e "\033[96mBright Cyan (14)\033[0m"
echo -e "\033[97mBright White (15)\033[0m"
EOF

chmod +x ~/.local/bin/test-ghostty-colors.sh
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Theme Not Loading
**Problem**: Theme doesn't change after editing configuration

**Solutions**:
```bash
# Check if theme file exists
ls -la ~/.config/ghostty/themes/

# Validate configuration syntax
ghostty --config-file ~/.config/ghostty/config --validate

# Check for typos in theme name
grep "theme =" ~/.config/ghostty/config

# Reload configuration
# In Ghostty: Ctrl+Shift+R (or Cmd+Shift+R on macOS)
# Or restart Ghostty completely
```

#### 2. Font Not Displaying Correctly
**Problem**: Font appears different than expected or has missing characters

**Solutions**:
```bash
# List available fonts
ghostty +list-fonts | grep -i "jetbrains"

# Check if font is installed
fc-list | grep -i "jetbrains"

# Install missing fonts (Ubuntu/Debian)
sudo apt install fonts-jetbrains-mono

# Clear font cache
fc-cache -fv

# Test with system default font
# Temporarily change font-family to "monospace"
```

#### 3. Colors Look Different Than Expected
**Problem**: Colors appear washed out or incorrect

**Solutions**:
```bash
# Check color space settings
# In config file, try:
window-colorspace = srgb

# Adjust alpha blending
alpha-blending = native

# Check background opacity
background-opacity = 1.0

# Verify terminal color support
echo $COLORTERM
echo $TERM
```

#### 4. Performance Issues
**Problem**: Ghostty feels slow or laggy with certain themes

**Solutions**:
```conf
# Disable transparency effects
background-opacity = 1.0
background-blur = false

# Reduce scrollback buffer
scrollback-limit = 10000

# Disable font ligatures
font-feature = -calt -liga

# Use simpler cursor
cursor-style = block
cursor-style-blink = false

# Disable synthetic font styles
font-synthetic-style = false
```

#### 5. Theme Switching Not Working
**Problem**: Cannot switch between themes easily

**Solutions**:
```bash
# Create theme switching script
cat > ~/.local/bin/ghostty-switch-theme.sh << 'EOF'
#!/bin/bash
THEME=$1
CONFIG_FILE="$HOME/.config/ghostty/config"

if [ -z "$THEME" ]; then
    echo "Usage: $0 <theme-name>"
    echo "Available themes:"
    ls ~/.config/ghostty/themes/ | sed 's/.conf$//'
    exit 1
fi

# Check if theme exists
if [ ! -f "$HOME/.config/ghostty/themes/$THEME.conf" ]; then
    echo "Theme '$THEME' not found!"
    exit 1
fi

# Update config file
sed -i "s/^theme = .*/theme = $THEME/" "$CONFIG_FILE"
echo "Switched to theme: $THEME"
echo "Reload Ghostty to see changes (Ctrl+Shift+R)"
EOF

chmod +x ~/.local/bin/ghostty-switch-theme.sh

# Usage: ghostty-switch-theme.sh cyberpunk-matrix
```

#### 6. Configuration File Errors
**Problem**: Ghostty won't start due to configuration errors

**Solutions**:
```bash
# Backup current config
cp ~/.config/ghostty/config ~/.config/ghostty/config.broken

# Start with minimal config
cat > ~/.config/ghostty/config << 'EOF'
# Minimal working configuration
font-family = "monospace"
font-size = 12
theme = default
EOF

# Test configuration
ghostty --config-file ~/.config/ghostty/config --validate

# Gradually add settings back to identify the problem
```

### Debugging Tools

#### 1. Configuration Validation
```bash
# Validate current configuration
ghostty --config-file ~/.config/ghostty/config --validate

# Test with specific theme
ghostty --theme cyberpunk-matrix --validate

# Check configuration loading order
ghostty --config-file ~/.config/ghostty/config --debug-config
```

#### 2. Font Debugging
```bash
# List all available fonts
ghostty +list-fonts

# Check specific font family
ghostty +list-fonts | grep -i "jetbrains"

# Test font rendering
echo "Testing font: JetBrains Mono 0O il1 != == <= >= -> <-"
```

#### 3. Color Testing
```bash
# Run the color test script
~/.local/bin/test-ghostty-colors.sh

# Test 256-color support
for i in {0..255}; do
    printf "\033[48;5;%sm%3d\033[0m " "$i" "$i"
    if (( i == 15 )) || (( i > 15 )) && (( (i-15) % 6 == 0 )); then
        printf "\n"
    fi
done
```

### Getting Help

#### 1. Community Resources
- **Ghostty Discord**: https://discord.gg/ghostty
- **GitHub Issues**: https://github.com/ghostty-org/ghostty/issues
- **Documentation**: https://ghostty.org/docs

#### 2. Reporting Issues
When reporting theme-related issues, include:
```bash
# System information
uname -a
ghostty --version

# Configuration file
cat ~/.config/ghostty/config

# Theme file (if custom)
cat ~/.config/ghostty/themes/your-theme.conf

# Font information
fc-list | grep -i "your-font-name"

# Terminal environment
echo "TERM: $TERM"
echo "COLORTERM: $COLORTERM"
```

#### 3. Creating Minimal Reproduction
```bash
# Create minimal config that reproduces the issue
cat > /tmp/minimal-ghostty-config << 'EOF'
font-family = "JetBrains Mono"
font-size = 14
theme = problematic-theme
EOF

# Test with minimal config
ghostty --config-file /tmp/minimal-ghostty-config
```

---

## Conclusion

This comprehensive guide provides everything you need to customize Ghostty terminal with beautiful, functional themes. The 10 curated themes cover a wide range of aesthetics and use cases, from cyberpunk hacker terminals to professional development environments.

### Key Takeaways

1. **Modular Approach**: Keep your base configuration separate from theme files for easy switching
2. **Font Matters**: Choose appropriate fonts for each theme to enhance the aesthetic
3. **Accessibility**: Consider readability and contrast, especially for long work sessions
4. **Performance**: Balance visual effects with terminal performance
5. **Customization**: Don't hesitate to modify themes to suit your specific needs

### Next Steps

1. **Install** your preferred themes using the provided configuration files
2. **Experiment** with different themes for different types of work
3. **Customize** existing themes to create your perfect terminal environment
4. **Share** your custom themes with the community
5. **Stay Updated** with new themes and Ghostty features

### Contributing

If you create interesting theme variations or improvements, consider:
- Sharing them on GitHub
- Contributing to the Ghostty community
- Submitting themes to the iterm2-color-schemes project

Happy terminal customization! 🚀
