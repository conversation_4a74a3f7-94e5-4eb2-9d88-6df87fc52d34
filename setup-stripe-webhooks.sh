#!/bin/bash

echo "🎯 NeuroColony Stripe Webhook Setup Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo -e "${RED}❌ Stripe CLI not found. Installing...${NC}"
    
    # Download and install Stripe CLI
    cd /tmp
    wget -q https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.4_linux_x86_64.tar.gz
    tar -xzf stripe_1.19.4_linux_x86_64.tar.gz
    sudo mv stripe /usr/local/bin/
    
    echo -e "${GREEN}✅ Stripe CLI installed${NC}"
else
    echo -e "${GREEN}✅ Stripe CLI found${NC}"
fi

# Check if user is logged in to Stripe
if ! stripe --version &> /dev/null; then
    echo -e "${YELLOW}⚠️ Please run 'stripe login' to authenticate with Stripe${NC}"
    echo "This will open your browser to connect your Stripe account."
    read -p "Press Enter after you've completed the login..."
fi

echo ""
echo -e "${BLUE}🔧 Setting up webhook forwarding...${NC}"
echo ""
echo "The following command will start webhook forwarding with the EXACT events NeuroColony needs:"
echo ""
echo -e "${YELLOW}stripe listen --forward-to localhost:5000/api/webhooks/stripe --events customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed${NC}"
echo ""
echo -e "${RED}IMPORTANT:${NC} Copy the webhook signing secret (whsec_...) that appears!"
echo ""
echo "Required Environment Variables to update in backend/.env:"
echo -e "${GREEN}STRIPE_SECRET_KEY=${NC}sk_test_YOUR_SECRET_KEY"
echo -e "${GREEN}STRIPE_WEBHOOK_SECRET=${NC}whsec_YOUR_WEBHOOK_SECRET"
echo ""
echo "Required Environment Variables for frontend/.env.local:"
echo -e "${GREEN}VITE_STRIPE_PUBLISHABLE_KEY=${NC}pk_test_YOUR_PUBLISHABLE_KEY"
echo ""

read -p "Press Enter to start webhook forwarding (Ctrl+C to stop)..."

# Start webhook forwarding with exact events NeuroColony needs
stripe listen \
  --forward-to localhost:5000/api/webhooks/stripe \
  --events customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed