#!/bin/bash

echo "🚀 SequenceAI Frontend Deployment Script"
echo "========================================"

# Check if we're in the right directory
if [ ! -d "frontend" ]; then
    echo "❌ Error: frontend directory not found. Run this script from the project root."
    exit 1
fi

echo "📦 Installing Vercel CLI..."
npm install -g vercel

echo "🔧 Building frontend..."
cd frontend
npm install
npm run build

echo "🌐 Deploying to Vercel..."
echo "Please follow the prompts:"
echo "- When asked 'Set up and deploy?', press Y"
echo "- When asked 'Which scope?', select your account"
echo "- When asked 'Link to existing project?', press N for new project"
echo "- When asked 'What's your project's name?', enter your domain name (e.g., flowsequence)"
echo "- When asked 'In which directory is your code located?', press Enter"

vercel --prod

echo "✅ Frontend deployment complete!"
echo "📋 Next steps:"
echo "1. Note the deployment URL Vercel provides"
echo "2. Update your backend CORS settings with this URL"
echo "3. Point your domain to this Vercel deployment"

cd ../