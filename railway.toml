[build]
builder = "NIXPACKS"

[[services]]
name = "neurocolony-backend"
source = "backend"

[services.variables]
PORT = "5000"
NODE_ENV = "production"
MONGODB_URI = "${{MongoDB.DATABASE_URL}}"
REDIS_URL = "${{Redis.REDIS_URL}}"

[[services]]
name = "neurocolony-frontend"
source = "frontend"

[services.variables]
VITE_API_URL = "${{neurocolony-backend.RAILWAY_PUBLIC_DOMAIN}}/api"

[[services]]
name = "mongodb"
image = "mongo:6"

[[services]]
name = "redis"
image = "redis:7"