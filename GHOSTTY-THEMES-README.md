# Ghostty Terminal Configuration Guide
## Complete Theme Collection & Customization Manual

A comprehensive collection of 10 carefully crafted terminal themes for Ghostty, covering everything from cyberpunk aesthetics to professional development environments.

## 🎨 Theme Collection

| Theme | Aesthetic | Colors | Best For |
|-------|-----------|--------|----------|
| **Cyberpunk Matrix** | Hacker/Matrix | Green on Black | Terminal work, coding |
| **Military Tactical** | Command Center | Amber/Orange on Dark | System administration |
| **Synthwave Neon** | 80s Retro | Pink/Cyan/Purple | Creative work |
| **Cyberpunk Anime** | Futuristic | Blue/Pink/Cyan | Development, gaming |
| **Minimal Dev** | Professional | Subtle grays/blues | Professional work |
| **Retro Amber** | Vintage CRT | Amber on Black | Nostalgic computing |
| **Retro IBM** | Classic Terminal | Green on Black | Traditional Unix work |
| **High Contrast** | Accessibility | Black/White/Yellow | Visual accessibility |
| **Custom Brand** | Template | Customizable | Brand consistency |
| **Tokyo Night** | Modern Developer | Purple/Blue/Pink | General development |

## 🚀 Quick Start

### Option 1: Automatic Installation
```bash
# Download and run the installation script
chmod +x install-ghostty-themes.sh
./install-ghostty-themes.sh
```

### Option 2: Manual Installation
```bash
# 1. Create directories
mkdir -p ~/.config/ghostty/{themes,backups}

# 2. Copy theme files from the guide
# 3. Update your config:
echo "theme = cyberpunk-matrix" >> ~/.config/ghostty/config

# 4. Reload Ghostty (Ctrl+Shift+R)
```

## 📁 Files Included

- **`ghostty-themes-guide.md`** - Complete configuration guide with all theme files
- **`ghostty-quick-reference.md`** - Quick reference card for common tasks
- **`ghostty-theme-manager.sh`** - Interactive theme management script
- **`install-ghostty-themes.sh`** - Automated installation script

## 🛠️ Theme Manager

Use the included theme manager for easy theme switching:

```bash
# List available themes
./ghostty-theme-manager.sh list

# Switch to a theme
./ghostty-theme-manager.sh switch cyberpunk-matrix

# Preview theme colors
./ghostty-theme-manager.sh preview tokyo-night

# Install new theme
./ghostty-theme-manager.sh install my-theme.conf

# Show help
./ghostty-theme-manager.sh help
```

## 🎯 Features

### ✨ Visual Aesthetics
- **10 distinct themes** covering different aesthetics and use cases
- **Carefully chosen color palettes** for optimal readability and visual appeal
- **Font recommendations** optimized for each theme
- **Transparency and blur effects** where appropriate

### 🔧 Functionality
- **Modular design** - easy to switch between themes
- **Performance optimized** - options for different performance needs
- **Accessibility focused** - high contrast options available
- **Customization friendly** - easy to modify and create variations

### 📚 Documentation
- **Complete installation guide** with step-by-step instructions
- **Troubleshooting section** for common issues
- **Customization tips** for advanced users
- **Quick reference** for daily use

## 🎨 Theme Previews

### Cyberpunk Matrix
```
Background: #000000 (Pure Black)
Foreground: #00ff00 (Matrix Green)
Font: JetBrains Mono
Style: Classic hacker terminal aesthetic
```

### Military Tactical
```
Background: #0a0f0a (Military Green)
Foreground: #ff8c00 (Amber Warning)
Font: Cascadia Code
Style: Command center/radar display
```

### Synthwave Neon
```
Background: #1a0d1a (Deep Purple)
Foreground: #ff00ff (Neon Magenta)
Font: Iosevka
Style: 80s synthwave aesthetic
```

### Tokyo Night
```
Background: #1a1b26 (Deep Blue)
Foreground: #c0caf5 (Light Blue)
Font: JetBrains Mono
Style: Popular modern developer theme
```

## 🔤 Font Recommendations

### Primary Fonts
- **JetBrains Mono** - Excellent for most themes, great ligatures
- **Fira Code** - Popular choice with extensive ligature support
- **Cascadia Code** - Microsoft's developer font
- **Iosevka** - Narrow, space-efficient font

### Installation
```bash
# macOS (Homebrew)
brew install --cask font-jetbrains-mono font-fira-code font-cascadia-code font-iosevka

# Ubuntu/Debian
sudo apt install fonts-jetbrains-mono fonts-firacode fonts-cascadia-code

# Arch Linux
sudo pacman -S ttf-jetbrains-mono ttf-fira-code ttf-cascadia-code ttf-iosevka
```

## ⚙️ Configuration Structure

```
~/.config/ghostty/
├── config                          # Main configuration
├── themes/                         # Theme files
│   ├── cyberpunk-matrix.conf
│   ├── military-tactical.conf
│   ├── synthwave-neon.conf
│   └── ...
└── backups/                        # Configuration backups
    └── config.backup.YYYY-MM-DD
```

## 🔧 Customization

### Quick Theme Switch
```bash
# Edit main config
sed -i 's/^theme = .*/theme = tokyo-night/' ~/.config/ghostty/config

# Reload Ghostty (Ctrl+Shift+R)
```

### Create Custom Theme
```bash
# Copy existing theme as starting point
cp ~/.config/ghostty/themes/cyberpunk-matrix.conf ~/.config/ghostty/themes/my-theme.conf

# Edit colors and settings
nano ~/.config/ghostty/themes/my-theme.conf
```

## 🐛 Troubleshooting

### Theme Not Loading
1. Check theme file exists: `ls ~/.config/ghostty/themes/`
2. Validate config: `ghostty --validate`
3. Reload configuration: `Ctrl+Shift+R`

### Font Issues
1. List available fonts: `ghostty +list-fonts`
2. Install missing fonts
3. Clear font cache: `fc-cache -fv`

### Performance Issues
1. Disable transparency: `background-opacity = 1.0`
2. Disable blur: `background-blur = false`
3. Reduce scrollback: `scrollback-limit = 50000`

## 📖 Documentation

- **Complete Guide**: `ghostty-themes-guide.md` - Full documentation with all configuration files
- **Quick Reference**: `ghostty-quick-reference.md` - Essential commands and settings
- **Theme Manager**: `ghostty-theme-manager.sh` - Interactive management script

## 🤝 Contributing

1. **Create new themes** using the provided templates
2. **Share improvements** to existing themes
3. **Report issues** or suggest enhancements
4. **Submit font recommendations** for different themes

## 📄 License

This collection is provided as-is for the Ghostty terminal community. Feel free to modify, share, and improve upon these themes.

## 🙏 Acknowledgments

- **Ghostty Terminal** - For creating an excellent terminal emulator
- **iTerm2 Color Schemes** - Source of inspiration for many color palettes
- **Font Developers** - For creating beautiful monospace fonts
- **Community** - For feedback and suggestions

---

**Happy terminal customization!** 🚀✨
