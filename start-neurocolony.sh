#!/bin/bash

# NeuroColony AI Agent Platform - Startup Script
# This script helps start the development servers with proper configuration

echo "🐜 NeuroColony AI Agent Platform - Startup Script"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js found: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org${NC}"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm found: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm not found. Please install npm${NC}"
    exit 1
fi

# Check MongoDB
if command_exists mongod; then
    echo -e "${GREEN}✅ MongoDB found${NC}"
else
    echo -e "${YELLOW}⚠️ MongoDB not found. Please install MongoDB or ensure it's running${NC}"
fi

# Check if ports are available
echo -e "${BLUE}🔍 Checking ports...${NC}"

if port_in_use 3004; then
    echo -e "${YELLOW}⚠️ Port 3004 is in use. Frontend may not start properly.${NC}"
else
    echo -e "${GREEN}✅ Port 3004 is available${NC}"
fi

if port_in_use 5002; then
    echo -e "${YELLOW}⚠️ Port 5002 is in use. Backend may not start properly.${NC}"
else
    echo -e "${GREEN}✅ Port 5002 is available${NC}"
fi

# Check if .env file exists
echo -e "${BLUE}🔍 Checking configuration...${NC}"

if [ -f "backend/.env" ]; then
    echo -e "${GREEN}✅ Backend .env file found${NC}"
else
    echo -e "${YELLOW}⚠️ Backend .env file not found. Creating from template...${NC}"
    cp backend/.env.example backend/.env
    echo -e "${GREEN}✅ Created backend/.env from template${NC}"
fi

# Check if node_modules exist
echo -e "${BLUE}🔍 Checking dependencies...${NC}"

if [ -d "backend/node_modules" ]; then
    echo -e "${GREEN}✅ Backend dependencies found${NC}"
else
    echo -e "${YELLOW}⚠️ Backend dependencies not found. Installing...${NC}"
    cd backend && npm install && cd ..
    echo -e "${GREEN}✅ Backend dependencies installed${NC}"
fi

if [ -d "frontend/node_modules" ]; then
    echo -e "${GREEN}✅ Frontend dependencies found${NC}"
else
    echo -e "${YELLOW}⚠️ Frontend dependencies not found. Installing...${NC}"
    cd frontend && npm install && cd ..
    echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
fi

# Create necessary directories
echo -e "${BLUE}🔍 Creating necessary directories...${NC}"
mkdir -p backend/logs backend/uploads
echo -e "${GREEN}✅ Directories created${NC}"

echo ""
echo -e "${GREEN}🚀 Starting NeuroColony AI Agent Platform...${NC}"
echo ""

# Function to start backend
start_backend() {
    echo -e "${BLUE}🔧 Starting Backend Server (Port 5002)...${NC}"
    cd backend
    DATABASE_FALLBACK=true DEMO_MODE=true npm run dev &
    BACKEND_PID=$!
    cd ..
    echo -e "${GREEN}✅ Backend server started (PID: $BACKEND_PID)${NC}"
}

# Function to start frontend
start_frontend() {
    echo -e "${BLUE}🎨 Starting Frontend Server (Port 3004)...${NC}"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    echo -e "${GREEN}✅ Frontend server started (PID: $FRONTEND_PID)${NC}"
}

# Start servers
start_backend
sleep 3
start_frontend

echo ""
echo -e "${GREEN}🎉 NeuroColony AI Agent Platform is starting up!${NC}"
echo ""
echo -e "${BLUE}📱 Access URLs:${NC}"
echo -e "   🌐 Main Platform: ${GREEN}http://localhost:3004${NC}"
echo -e "   🐜 Colony Dashboard: ${GREEN}http://localhost:3004/colony-dashboard${NC}"
echo -e "   🔗 Integration Hub: ${GREEN}http://localhost:3004/integration-hub${NC}"
echo -e "   🤖 Agent Marketplace: ${GREEN}http://localhost:3004/agent-marketplace${NC}"
echo -e "   🎨 Workflow Builder: ${GREEN}http://localhost:3004/agent-builder${NC}"
echo -e "   🔧 Backend API: ${GREEN}http://localhost:5002/api${NC}"
echo ""
echo -e "${YELLOW}⏳ Please wait 10-15 seconds for servers to fully start...${NC}"
echo ""
echo -e "${BLUE}💡 Tips:${NC}"
echo -e "   • Press Ctrl+C to stop both servers"
echo -e "   • Check browser console for any errors"
echo -e "   • Ensure MongoDB is running for full functionality"
echo ""

# Wait for user to stop
trap 'echo -e "\n${YELLOW}🛑 Stopping servers...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT

# Keep script running
wait
