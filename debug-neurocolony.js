#!/usr/bin/env node

import { spawn } from 'child_process';
import { createServer } from 'net';
import axios from 'axios';

console.log('🧠 NeuroColony Debug & Fix Script');
console.log('==================================\n');

// Check if port is available
function checkPort(port) {
  return new Promise((resolve) => {
    const server = createServer();
    server.unref();
    server.on('error', () => resolve(false));
    server.listen(port, () => {
      server.close(() => resolve(true));
    });
  });
}

// Kill process on port
function killPort(port) {
  return new Promise((resolve) => {
    const kill = spawn('lsof', ['-ti', `:${port}`]);
    let pid = '';
    
    kill.stdout.on('data', (data) => {
      pid = data.toString().trim();
    });
    
    kill.on('close', (code) => {
      if (pid) {
        console.log(`⚠️  Killing process ${pid} on port ${port}`);
        spawn('kill', ['-9', pid]).on('close', () => resolve());
      } else {
        resolve();
      }
    });
  });
}

// Check MongoDB connection
async function checkMongoDB() {
  try {
    const { default: mongoose } = await import('mongoose');
    console.log('🔍 Checking MongoDB connection...');
    
    // Try to connect with same settings as the app
    await mongoose.connect('mongodb://127.0.0.1:27017/neurocolony', {
      serverSelectionTimeoutMS: 5000,
    });
    
    console.log('✅ MongoDB is accessible');
    await mongoose.disconnect();
    return true;
  } catch (error) {
    console.log('❌ MongoDB connection failed:', error.message);
    return false;
  }
}

// Test backend API
async function testBackendAPI(port) {
  try {
    const response = await axios.get(`http://localhost:${port}/api/test`, {
      timeout: 5000
    });
    console.log(`✅ Backend API is responding on port ${port}`);
    return true;
  } catch (error) {
    console.log(`❌ Backend API not responding on port ${port}`);
    return false;
  }
}

// Main debug flow
async function debug() {
  console.log('1️⃣  Checking services status...\n');
  
  // Check MongoDB
  const mongoOk = await checkMongoDB();
  if (!mongoOk) {
    console.log('\n💡 MongoDB Fix: Start MongoDB with:');
    console.log('   sudo systemctl start mongod');
    console.log('   OR');
    console.log('   mongod --dbpath /data/db\n');
  }
  
  // Check ports
  console.log('\n2️⃣  Checking port availability...\n');
  
  const ports = [
    { port: 3000, name: 'Frontend (standard)' },
    { port: 3004, name: 'Frontend (debug)' },
    { port: 5000, name: 'Backend (standard)' },
    { port: 5002, name: 'Backend (debug)' }
  ];
  
  for (const { port, name } of ports) {
    const available = await checkPort(port);
    if (available) {
      console.log(`✅ Port ${port} (${name}) is available`);
    } else {
      console.log(`⚠️  Port ${port} (${name}) is in use`);
      await killPort(port);
      console.log(`✅ Port ${port} cleared`);
    }
  }
  
  // Test if backend is already running
  console.log('\n3️⃣  Testing existing services...\n');
  
  const backend5000 = await testBackendAPI(5000);
  const backend5002 = await testBackendAPI(5002);
  
  if (!backend5000 && !backend5002) {
    console.log('\n4️⃣  Starting services...\n');
    
    // Start backend
    console.log('🚀 Starting backend on port 5002...');
    const backend = spawn('npm', ['start'], {
      cwd: './backend',
      env: { ...process.env, PORT: '5002' },
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    backend.stdout.on('data', (data) => {
      console.log(`[Backend] ${data.toString().trim()}`);
    });
    
    backend.stderr.on('data', (data) => {
      console.log(`[Backend Error] ${data.toString().trim()}`);
    });
    
    // Wait for backend to start
    console.log('⏳ Waiting for backend to initialize...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Start frontend
    console.log('\n🚀 Starting frontend on port 3004...');
    const frontend = spawn('npm', ['run', 'dev'], {
      cwd: './frontend',
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    frontend.stdout.on('data', (data) => {
      console.log(`[Frontend] ${data.toString().trim()}`);
    });
    
    frontend.stderr.on('data', (data) => {
      console.log(`[Frontend Error] ${data.toString().trim()}`);
    });
    
    console.log('\n✅ Services started!');
    console.log('\n📱 Access URLs:');
    console.log('   🌐 Frontend: http://localhost:3004');
    console.log('   🔧 Backend API: http://localhost:5002/api');
    console.log('\nPress Ctrl+C to stop all services\n');
    
    // Handle cleanup
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping services...');
      backend.kill();
      frontend.kill();
      process.exit(0);
    });
    
    // Keep running
    await new Promise(() => {});
  } else {
    console.log('\n✅ Backend is already running!');
    console.log('\n📱 Access URLs:');
    if (backend5000) console.log('   🔧 Backend API: http://localhost:5000/api');
    if (backend5002) console.log('   🔧 Backend API: http://localhost:5002/api');
  }
}

// Run debug
debug().catch(console.error);