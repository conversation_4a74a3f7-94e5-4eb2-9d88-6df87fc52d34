#!/bin/bash

echo "🚀 SequenceAI Production Launch Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Do not run this script as root!"
    exit 1
fi

print_info "Starting SequenceAI production deployment..."

# 1. Environment Setup
print_info "1. Setting up environment..."

# Check if .env exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
    print_warning "Please configure your .env file with production values"
    exit 1
fi

# Load environment variables
set -a
source .env
set +a

print_status "Environment loaded"

# 2. Database Setup
print_info "2. Setting up databases..."

# Start MongoDB and Redis containers
if command -v docker &> /dev/null; then
    print_info "Starting database containers..."
    docker-compose up -d mongodb redis
    sleep 10
    print_status "Database containers started"
else
    print_warning "Docker not found. Make sure MongoDB and Redis are running manually"
fi

# 3. Backend Setup
print_info "3. Setting up backend..."

cd backend

# Install dependencies
print_info "Installing backend dependencies..."
npm install

# Run database migrations if any
if [ -f "migrations/migrate.js" ]; then
    print_info "Running database migrations..."
    node migrations/migrate.js
fi

print_status "Backend setup complete"

# 4. Frontend Setup
print_info "4. Setting up frontend..."

cd ../frontend

# Install dependencies
print_info "Installing frontend dependencies..."
npm install

# Build production frontend
print_info "Building production frontend..."
npm run build

print_status "Frontend build complete"

cd ..

# 5. Production Deployment
print_info "5. Deploying to production..."

# Option 1: Docker deployment
if [ "$DEPLOY_METHOD" = "docker" ]; then
    print_info "Deploying with Docker..."
    docker-compose -f docker-compose.prod.yml up -d --build
    print_status "Docker deployment complete"

# Option 2: PM2 deployment
elif [ "$DEPLOY_METHOD" = "pm2" ]; then
    print_info "Deploying with PM2..."
    
    # Install PM2 globally if not exists
    if ! command -v pm2 &> /dev/null; then
        print_info "Installing PM2..."
        npm install -g pm2
    fi
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'sequenceai-backend',
    script: './backend/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: ${PORT:-5000}
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true
  }]
}
EOF
    
    # Start with PM2
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    print_status "PM2 deployment complete"

# Option 3: Manual deployment
else
    print_info "Starting manual deployment..."
    print_warning "Starting backend in background..."
    cd backend && npm start &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    print_info "Backend started with PID: $BACKEND_PID"
fi

# 6. Health Checks
print_info "6. Running health checks..."

sleep 5

# Check backend health
BACKEND_URL="http://localhost:${PORT:-5000}/api/health"
if curl -s "$BACKEND_URL" > /dev/null; then
    print_status "Backend health check passed"
else
    print_error "Backend health check failed"
    exit 1
fi

# Check database connectivity
if curl -s "$BACKEND_URL" | grep -q "database.*connected"; then
    print_status "Database connectivity check passed"
else
    print_warning "Database might not be connected properly"
fi

# 7. Performance Optimization
print_info "7. Applying performance optimizations..."

# Enable gzip compression in nginx if available
if command -v nginx &> /dev/null; then
    print_info "Nginx detected - configuring compression..."
    # Nginx configuration would go here
fi

# Set up log rotation
print_info "Setting up log rotation..."
mkdir -p logs

# Create logrotate configuration
sudo tee /etc/logrotate.d/sequenceai > /dev/null << EOF
/home/<USER>/SequenceAI/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        systemctl reload sequenceai || true
    endscript
}
EOF

print_status "Log rotation configured"

# 8. Security Setup
print_info "8. Applying security configurations..."

# Set proper file permissions
chmod 600 .env
chmod -R 755 backend/
chmod -R 755 frontend/dist/

# Create firewall rules if ufw is available
if command -v ufw &> /dev/null; then
    print_info "Configuring firewall..."
    sudo ufw allow ${PORT:-5000}/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    print_status "Firewall configured"
fi

# 9. Monitoring Setup
print_info "9. Setting up monitoring..."

# Create monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    if ! curl -s http://localhost:5000/api/health > /dev/null; then
        echo "$(date): Health check failed - restarting service"
        pm2 restart sequenceai-backend || echo "Failed to restart"
    fi
    sleep 60
done
EOF

chmod +x monitor.sh

print_status "Monitoring script created"

# 10. Final Status
print_info "10. Deployment complete!"

echo ""
echo "🎉 SequenceAI Production Deployment Summary"
echo "=========================================="
print_status "Backend: Running on port ${PORT:-5000}"
print_status "Database: MongoDB and Redis containers active"
print_status "Frontend: Built and ready for serving"
print_status "Logs: Available in ./logs/ directory"
print_status "Monitoring: Health check script created"

echo ""
echo "🔗 Access URLs:"
echo "   Backend API: http://localhost:${PORT:-5000}/api"
echo "   Health Check: http://localhost:${PORT:-5000}/api/health"
echo "   Frontend: Serve from ./frontend/dist/"

echo ""
echo "📋 Next Steps:"
echo "   1. Configure your web server (Nginx/Apache) to serve frontend"
echo "   2. Set up SSL certificates for HTTPS"
echo "   3. Configure domain name and DNS"
echo "   4. Set up automated backups"
echo "   5. Configure monitoring alerts"

echo ""
print_warning "Remember to:"
echo "   - Update your .env file with production values"
echo "   - Configure Stripe webhooks for payments"
echo "   - Set up email SMTP settings"
echo "   - Configure your domain's CORS settings"

echo ""
print_status "SequenceAI is now running in production! 🚀"