# 🚀 NeuroColony Production Deployment Guide

## 🎯 **Recommended Deployment Options**

### **Option 1: DigitalOcean App Platform (EASIEST)**
- ✅ **Zero DevOps** - Fully managed
- ✅ **Auto SSL** - Free certificates
- ✅ **Auto Scaling** - Handles traffic spikes
- ✅ **GitHub Integration** - Deploy on push
- ✅ **Built-in Database** - Managed MongoDB
- 💰 **Cost**: ~$30-60/month

### **Option 2: Railway (DEVELOPER FRIENDLY)**
- ✅ **Git-based Deploy** - Connect repository
- ✅ **Environment Variables** - Easy configuration
- ✅ **Custom Domains** - Simple DNS setup
- ✅ **Database Included** - PostgreSQL/MongoDB
- 💰 **Cost**: ~$20-40/month

### **Option 3: Vercel + PlanetScale (MODERN STACK)**
- ✅ **Frontend**: Vercel (best React performance)
- ✅ **Backend**: Railway or DigitalOcean
- ✅ **Database**: PlanetScale (serverless MySQL)
- ✅ **Global CDN** - Lightning fast
- 💰 **Cost**: ~$25-50/month

### **Option 4: Traditional VPS (MOST CONTROL)**
- ✅ **Full Control** - Root access
- ✅ **Docker Compose** - Easy container management
- ✅ **Custom Configuration** - Any setup you want
- ⚠️ **Requires DevOps** - Manual SSL, updates, monitoring
- 💰 **Cost**: ~$10-25/month

## 🎯 **My Recommendation: DigitalOcean App Platform**

Perfect for NeuroColony because:
- ✅ **Handles Docker containers** (NeuroColony is already containerized)
- ✅ **Managed MongoDB** (no database setup needed)
- ✅ **Auto SSL + Custom Domain** (just point DNS)
- ✅ **Built-in CI/CD** (deploy from GitHub)
- ✅ **Environment Variables** (easy config management)

## 📋 **Domain Requirements**

### **What You'll Need:**
1. **Domain Name** (e.g., neurocolony.ai, yourname.com)
2. **DNS Management** (usually included with domain)
3. **SSL Certificate** (free with most platforms)

### **DNS Records to Configure:**
```
A     @     [platform-ip-address]
CNAME www   your-app.platform.com
CNAME api   your-app.platform.com
```

## ⚙️ **Pre-Deployment Checklist**

Before deploying, ensure:

### **Environment Variables Ready:**
- ✅ `STRIPE_SECRET_KEY` (your real key)
- ✅ `STRIPE_WEBHOOK_SECRET` (from production webhook)
- ✅ `OPENAI_API_KEY` (for AI features)
- ✅ `JWT_SECRET` (generate new production secret)
- ✅ `MONGODB_URI` (production database)
- ✅ `FRONTEND_URL` (your domain)

### **Stripe Configuration:**
- ✅ **Production Webhook** pointing to yourdomain.com/api/webhooks/stripe
- ✅ **Live API Keys** (not test keys)
- ✅ **Products Created** in Stripe dashboard

### **Build Verification:**
- ✅ Frontend builds successfully (`npm run build`)
- ✅ Backend starts without errors
- ✅ Database connection works

## 🔧 **Platform-Specific Setup**

### **DigitalOcean App Platform Setup:**

1. **Create App from GitHub:**
   - Connect your NeuroColony repository
   - Auto-detect as Node.js app

2. **Configure Components:**
   ```yaml
   # Frontend (React)
   name: neurocolony-frontend
   source_dir: /frontend
   build_command: npm run build
   run_command: npm run preview
   environment_slug: node-js
   instance_count: 1
   instance_size_slug: basic-xxs

   # Backend (Express)
   name: neurocolony-backend  
   source_dir: /backend
   build_command: npm install
   run_command: npm start
   environment_slug: node-js
   instance_count: 1
   instance_size_slug: basic-xxs
   
   # Database (MongoDB)
   name: neurocolony-db
   engine: mongodb
   version: "5"
   size: basic
   ```

3. **Environment Variables:**
   - Add all production environment variables
   - Use DigitalOcean's secure variable storage

4. **Custom Domain:**
   - Add your domain in DigitalOcean dashboard
   - Update DNS to point to DigitalOcean

### **Railway Setup:**

1. **Deploy Backend:**
   ```bash
   railway login
   railway new neurocolony-backend
   railway add
   ```

2. **Deploy Frontend:**
   ```bash
   railway new neurocolony-frontend
   railway add
   ```

3. **Environment Variables:**
   - Set via Railway dashboard or CLI
   - Railway automatically provisions databases

## 🌐 **DNS Configuration Examples**

### **Cloudflare (Recommended):**
```
Type  Name  Content                    TTL
A     @     your-app-ip-address      Auto
CNAME www   your-app.platform.com    Auto
CNAME api   your-app.platform.com    Auto
```

### **Domain Registrar DNS:**
```
Host  Type  Value                     TTL
@     A     your-app-ip-address      3600
www   CNAME your-app.platform.com   3600
api   CNAME your-app.platform.com   3600
```

## 🔒 **SSL Certificate Setup**

### **Automatic (Recommended):**
- ✅ **DigitalOcean**: Auto SSL with Let's Encrypt
- ✅ **Railway**: Auto SSL included
- ✅ **Vercel**: Auto SSL included

### **Manual (if needed):**
```bash
# Using certbot
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com
```

## 📊 **Production Environment Variables**

Create `/home/<USER>/SequenceAI/production.env`:

```env
# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/neurocolony
REDIS_URL=redis://production-redis:6379

# Security
JWT_SECRET=your-super-secure-production-jwt-secret-256-bits
INTEGRATION_ENCRYPTION_KEY=production-encryption-key-32-chars

# Stripe (LIVE KEYS)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_PRODUCTION_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY

# OpenAI
OPENAI_API_KEY=sk-YOUR_PRODUCTION_OPENAI_KEY

# URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://yourdomain.com/api

# Email
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Production Settings
NODE_ENV=production
PORT=5000
DEMO_MODE=false
```

## 🎯 **Quick Start Deployment**

### **If you want me to set this up:**

**Tell me:**
1. **What domain** you're planning to use
2. **Which platform** you prefer (I recommend DigitalOcean)
3. **If you have GitHub** repository ready

**I can create:**
- ✅ Platform-specific deployment configs
- ✅ Docker compose files
- ✅ GitHub Actions for CI/CD
- ✅ Environment variable templates
- ✅ DNS configuration guides

## 💡 **Next Steps**

1. **Choose deployment platform**
2. **Get your domain**
3. **I'll configure everything for you**
4. **Deploy and test**
5. **Update Stripe webhook to production URL**
6. **Go live!**

**Ready to pick a platform and domain?**