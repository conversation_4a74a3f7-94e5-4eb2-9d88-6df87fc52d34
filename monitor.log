2025-06-25 09:46:40 [0;34m🔍 Starting SequenceAI system monitor...[0m
2025-06-25 09:46:40 [0;32m✅ MongoDB (port 27018) is running[0m
2025-06-25 09:46:40 [0;32m✅ <PERSON><PERSON> (port 6380) is running[0m
2025-06-25 09:46:40 [0;32m✅ Backend API (port 5000) is running[0m
2025-06-25 09:46:40 [0;32m✅ Frontend (port 3001) is running[0m
2025-06-25 09:46:40 [0;32m✅ <PERSON>inx (port 8080) is running[0m
2025-06-25 09:46:40 [0;32m✅ Backend Health HTTP check passed[0m
2025-06-25 09:46:40 [0;32m✅ Frontend HTTP check passed[0m
2025-06-25 09:46:40 [0;32m✅ Nginx Proxy HTTP check passed[0m
2025-06-25 09:46:40 [0;31m❌ System health check: UNHEALTHY[0m
2025-06-25 09:46:40 [0;31m🚨 Some SequenceAI services have issues![0m
2025-06-26 07:30:59 [0;34m🔍 Starting SequenceAI system monitor...[0m
2025-06-26 07:30:59 [0;32m✅ MongoDB (port 27018) is running[0m
2025-06-26 07:30:59 [0;32m✅ Redis (port 6380) is running[0m
2025-06-26 07:30:59 [0;32m✅ Backend API (port 5000) is running[0m
2025-06-26 07:30:59 [0;32m✅ Frontend (port 3001) is running[0m
2025-06-26 07:30:59 [0;32m✅ Nginx (port 8080) is running[0m
2025-06-26 07:30:59 [0;32m✅ Backend Health HTTP check passed[0m
2025-06-26 07:30:59 [0;32m✅ Frontend HTTP check passed[0m
2025-06-26 07:30:59 [0;32m✅ Nginx Proxy HTTP check passed[0m
2025-06-26 07:30:59 [0;31m❌ System health check: UNHEALTHY[0m
2025-06-26 07:30:59 [0;31m🚨 Some SequenceAI services have issues![0m
