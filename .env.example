# Environment Configuration Template

# Copy this file to .env and fill in your values
# DO NOT commit .env to version control

# ==============================================
# 🧠 NEUROCOLONY CONFIGURATION
# ==============================================

# Application Environment
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# ==============================================
# 🤖 AI CONFIGURATION (REQUIRED)
# ==============================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# ==============================================
# 🗄️ DATABASE CONFIGURATION
# ==============================================

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/neurocolony
MONGODB_DB_NAME=neurocolony

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# ==============================================
# 🔐 AUTHENTICATION & SECURITY
# ==============================================

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Session Configuration
SESSION_SECRET=your_session_secret_here

# ==============================================
# 💳 PAYMENT CONFIGURATION (OPTIONAL)
# ==============================================

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Subscription Plans
STRIPE_PRICE_ID_PRO=price_your_pro_plan_id
STRIPE_PRICE_ID_BUSINESS=price_your_business_plan_id

# ==============================================
# 📧 EMAIL CONFIGURATION (OPTIONAL)
# ==============================================

# SMTP Configuration for transactional emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME=NeuroColony
SUPPORT_EMAIL=<EMAIL>

# URLs
VITE_API_URL=http://localhost:5000/api

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>