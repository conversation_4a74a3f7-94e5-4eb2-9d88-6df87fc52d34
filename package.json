{"name": "neurocolony", "version": "1.0.0", "description": "NeuroColony - AI-powered email sequences that convert strangers into customers", "main": "index.js", "scripts": {"dev": "concurrently \"npm run frontend:dev\" \"npm run backend:dev\"", "frontend:dev": "cd frontend && npm run dev", "backend:dev": "cd backend && npm run dev", "build": "npm run frontend:build && npm run backend:build", "frontend:build": "cd frontend && npm run build", "backend:build": "cd backend && npm run build", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "deploy": "docker buildx build --builder cloud-kd8studios-bambam --push -t neurocolony:latest ."}, "keywords": ["ai", "email", "marketing", "saas", "automation", "neurocolony"], "author": "NeuroColony", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}