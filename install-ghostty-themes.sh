#!/bin/bash

# Ghostty Themes Installation Script
# Automatically installs all themes from the comprehensive guide

set -e

# Configuration
GHOSTTY_CONFIG_DIR="$HOME/.config/ghostty"
THEMES_DIR="$GHOSTTY_CONFIG_DIR/themes"
BACKUP_DIR="$GHOSTTY_CONFIG_DIR/backups"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🎨 Ghostty Themes Installation Script"
echo "====================================="

# Create directories
print_info "Creating directory structure..."
mkdir -p "$THEMES_DIR" "$BACKUP_DIR"
print_success "Directories created"

# Backup existing config
if [ -f "$GHOSTTY_CONFIG_DIR/config" ]; then
    backup_file="$BACKUP_DIR/config.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$GHOSTTY_CONFIG_DIR/config" "$backup_file"
    print_success "Existing config backed up to: $backup_file"
fi

# Install themes
print_info "Installing themes..."

# 1. Cyberpunk Matrix Theme
cat > "$THEMES_DIR/cyberpunk-matrix.conf" << 'EOF'
# Cyberpunk Matrix Theme
background = #000000
foreground = #00ff00
cursor-color = #00ff00
cursor-text = #000000
selection-background = #003300
selection-foreground = #00ff00
palette = 0=#000000
palette = 1=#ff0000
palette = 2=#00ff00
palette = 3=#ffff00
palette = 4=#0000ff
palette = 5=#ff00ff
palette = 6=#00ffff
palette = 7=#c0c0c0
palette = 8=#808080
palette = 9=#ff0000
palette = 10=#00ff41
palette = 11=#ffff00
palette = 12=#0066ff
palette = 13=#ff00ff
palette = 14=#00ffff
palette = 15=#ffffff
font-family = "JetBrains Mono"
font-size = 14
background-opacity = 0.95
cursor-style = block
cursor-style-blink = true
EOF

# 2. Military Tactical Theme
cat > "$THEMES_DIR/military-tactical.conf" << 'EOF'
# Military Tactical Theme
background = #0a0f0a
foreground = #ff8c00
cursor-color = #ffaa00
cursor-text = #0a0f0a
selection-background = #2d4a2d
selection-foreground = #ff8c00
palette = 0=#0a0f0a
palette = 1=#cc3300
palette = 2=#66cc00
palette = 3=#ff8c00
palette = 4=#3366cc
palette = 5=#cc6600
palette = 6=#00cccc
palette = 7=#cccccc
palette = 8=#666666
palette = 9=#ff4444
palette = 10=#88ff00
palette = 11=#ffaa00
palette = 12=#4488ff
palette = 13=#ff8800
palette = 14=#44ffff
palette = 15=#ffffff
font-family = "Cascadia Code"
font-size = 13
background-opacity = 0.98
cursor-style = block
cursor-style-blink = false
EOF

# 3. Synthwave Neon Theme
cat > "$THEMES_DIR/synthwave-neon.conf" << 'EOF'
# Synthwave Neon Theme
background = #1a0d1a
foreground = #ff00ff
cursor-color = #00ffff
cursor-text = #1a0d1a
selection-background = #4d1a4d
selection-foreground = #ff00ff
palette = 0=#1a0d1a
palette = 1=#ff0080
palette = 2=#00ff80
palette = 3=#ffff00
palette = 4=#0080ff
palette = 5=#ff00ff
palette = 6=#00ffff
palette = 7=#cccccc
palette = 8=#666666
palette = 9=#ff44aa
palette = 10=#44ff88
palette = 11=#ffff44
palette = 12=#44aaff
palette = 13=#ff44ff
palette = 14=#44ffff
palette = 15=#ffffff
font-family = "Iosevka"
font-size = 14
background-opacity = 0.92
background-blur = 15
cursor-style = bar
cursor-style-blink = true
EOF

# 4. Cyberpunk Anime Theme
cat > "$THEMES_DIR/cyberpunk-anime.conf" << 'EOF'
# Cyberpunk Anime Theme
background = #0d1117
foreground = #58a6ff
cursor-color = #ff7b72
cursor-text = #0d1117
selection-background = #264f78
selection-foreground = #58a6ff
palette = 0=#0d1117
palette = 1=#ff7b72
palette = 2=#7ee787
palette = 3=#f9e2af
palette = 4=#58a6ff
palette = 5=#bc8cff
palette = 6=#39c5cf
palette = 7=#b1bac4
palette = 8=#6e7681
palette = 9=#ffa198
palette = 10=#56d364
palette = 11=#e3b341
palette = 12=#79c0ff
palette = 13=#d2a8ff
palette = 14=#56d4dd
palette = 15=#f0f6fc
font-family = "Victor Mono"
font-size = 14
background-opacity = 0.94
cursor-style = block
cursor-style-blink = true
EOF

# 5. Minimal Dev Theme
cat > "$THEMES_DIR/minimal-dev.conf" << 'EOF'
# Minimal Dev Theme
background = #fafafa
foreground = #383a42
cursor-color = #526fff
cursor-text = #fafafa
selection-background = #e5e5e6
selection-foreground = #383a42
palette = 0=#fafafa
palette = 1=#e45649
palette = 2=#50a14f
palette = 3=#c18401
palette = 4=#4078f2
palette = 5=#a626a4
palette = 6=#0184bc
palette = 7=#383a42
palette = 8=#a0a1a7
palette = 9=#e45649
palette = 10=#50a14f
palette = 11=#c18401
palette = 12=#4078f2
palette = 13=#a626a4
palette = 14=#0184bc
palette = 15=#090a0b
font-family = "SF Mono"
font-size = 13
background-opacity = 1.0
cursor-style = bar
cursor-style-blink = false
EOF

# 6. Retro Amber Theme
cat > "$THEMES_DIR/retro-amber.conf" << 'EOF'
# Retro Amber Theme
background = #000000
foreground = #ffb000
cursor-color = #ffb000
cursor-text = #000000
selection-background = #332200
selection-foreground = #ffb000
palette = 0=#000000
palette = 1=#cc6600
palette = 2=#ffb000
palette = 3=#ffcc00
palette = 4=#996600
palette = 5=#cc9900
palette = 6=#ffcc66
palette = 7=#ccaa66
palette = 8=#664400
palette = 9=#ff8800
palette = 10=#ffcc00
palette = 11=#ffdd44
palette = 12=#cc9966
palette = 13=#ffbb33
palette = 14=#ffdd88
palette = 15=#ffffcc
font-family = "IBM Plex Mono"
font-size = 14
background-opacity = 1.0
cursor-style = block
cursor-style-blink = true
EOF

# 7. Retro IBM Theme
cat > "$THEMES_DIR/retro-ibm.conf" << 'EOF'
# Retro IBM Theme
background = #000000
foreground = #00aa00
cursor-color = #00aa00
cursor-text = #000000
selection-background = #002200
selection-foreground = #00aa00
palette = 0=#000000
palette = 1=#aa0000
palette = 2=#00aa00
palette = 3=#aa5500
palette = 4=#0000aa
palette = 5=#aa00aa
palette = 6=#00aaaa
palette = 7=#aaaaaa
palette = 8=#555555
palette = 9=#ff5555
palette = 10=#55ff55
palette = 11=#ffff55
palette = 12=#5555ff
palette = 13=#ff55ff
palette = 14=#55ffff
palette = 15=#ffffff
font-family = "Courier Prime"
font-size = 14
background-opacity = 1.0
cursor-style = block
cursor-style-blink = false
EOF

# 8. High Contrast Theme
cat > "$THEMES_DIR/high-contrast.conf" << 'EOF'
# High Contrast Theme
background = #000000
foreground = #ffffff
cursor-color = #ffff00
cursor-text = #000000
selection-background = #ffffff
selection-foreground = #000000
palette = 0=#000000
palette = 1=#ff0000
palette = 2=#00ff00
palette = 3=#ffff00
palette = 4=#0000ff
palette = 5=#ff00ff
palette = 6=#00ffff
palette = 7=#ffffff
palette = 8=#808080
palette = 9=#ff8080
palette = 10=#80ff80
palette = 11=#ffff80
palette = 12=#8080ff
palette = 13=#ff80ff
palette = 14=#80ffff
palette = 15=#ffffff
font-family = "Atkinson Hyperlegible"
font-size = 15
font-thicken = true
background-opacity = 1.0
cursor-style = block
cursor-style-blink = true
minimum-contrast = 7.0
EOF

# 9. Tokyo Night Theme
cat > "$THEMES_DIR/tokyo-night.conf" << 'EOF'
# Tokyo Night Theme
background = #1a1b26
foreground = #c0caf5
cursor-color = #c0caf5
cursor-text = #1a1b26
selection-background = #33467c
selection-foreground = #c0caf5
palette = 0=#15161e
palette = 1=#f7768e
palette = 2=#9ece6a
palette = 3=#e0af68
palette = 4=#7aa2f7
palette = 5=#bb9af7
palette = 6=#7dcfff
palette = 7=#a9b1d6
palette = 8=#414868
palette = 9=#f7768e
palette = 10=#9ece6a
palette = 11=#e0af68
palette = 12=#7aa2f7
palette = 13=#bb9af7
palette = 14=#7dcfff
palette = 15=#c0caf5
font-family = "JetBrains Mono"
font-size = 14
background-opacity = 0.96
cursor-style = block
cursor-style-blink = false
EOF

print_success "All themes installed successfully!"

# Create or update main config
if [ ! -f "$GHOSTTY_CONFIG_DIR/config" ]; then
    print_info "Creating base configuration file..."
    cat > "$GHOSTTY_CONFIG_DIR/config" << 'EOF'
# Ghostty Base Configuration
font-family = "JetBrains Mono"
font-size = 14
font-feature = +calt +liga +dlig
window-decoration = true
window-padding-x = 8
window-padding-y = 8
window-inherit-working-directory = true
window-inherit-font-size = true
scrollback-limit = 100000
cursor-style = block
cursor-style-blink = true
mouse-hide-while-typing = true
copy-on-select = false
shell-integration = detect
shell-integration-features = cursor,sudo,title
theme = cyberpunk-matrix
keybind = ctrl+shift+c=copy_to_clipboard
keybind = ctrl+shift+v=paste_from_clipboard
keybind = ctrl+shift+t=new_tab
keybind = ctrl+shift+w=close_surface
keybind = ctrl+shift+n=new_window
keybind = ctrl+shift+plus=increase_font_size:1
keybind = ctrl+shift+minus=decrease_font_size:1
keybind = ctrl+shift+zero=reset_font_size
keybind = ctrl+shift+r=reload_config
EOF
    print_success "Base configuration created with cyberpunk-matrix theme"
else
    print_info "Configuration file already exists, not overwriting"
fi

# Make theme manager executable
if [ -f "ghostty-theme-manager.sh" ]; then
    chmod +x ghostty-theme-manager.sh
    print_success "Theme manager script made executable"
fi

echo
print_success "Installation complete! 🎉"
echo
print_info "Installed themes:"
echo "  • cyberpunk-matrix    - Green-on-black hacker aesthetic"
echo "  • military-tactical   - Amber command center style"
echo "  • synthwave-neon     - Pink/cyan 80s aesthetic"
echo "  • cyberpunk-anime    - Blue/pink futuristic style"
echo "  • minimal-dev        - Clean professional theme"
echo "  • retro-amber        - Vintage CRT amber glow"
echo "  • retro-ibm          - Classic IBM terminal"
echo "  • high-contrast      - Maximum accessibility"
echo "  • tokyo-night        - Popular developer theme"
echo
print_info "To switch themes:"
echo "  1. Edit ~/.config/ghostty/config"
echo "  2. Change the 'theme = ' line to your desired theme"
echo "  3. Reload Ghostty (Ctrl+Shift+R)"
echo
print_info "Or use the theme manager script:"
echo "  ./ghostty-theme-manager.sh list"
echo "  ./ghostty-theme-manager.sh switch <theme-name>"
echo
print_warning "Don't forget to install the recommended fonts for the best experience!"
echo "  Ubuntu/Debian: sudo apt install fonts-jetbrains-mono fonts-firacode"
echo "  macOS: brew install --cask font-jetbrains-mono font-fira-code"
