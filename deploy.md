# 🚀 NeuroColony Deployment Guide

## 1. 🌐 Get Your Domain

### Recommended Domain Names (NeuroColony is taken):
- **flowsequence.ai** - Premium but brandable
- **intellisequence.com** - Professional
- **sequenceflow.ai** - Emphasizes workflow
- **aisequencer.com** - Action-oriented

### Where to Buy:
1. **Namecheap.com** - Best overall value ($10-15/year)
2. **Cloudflare.com/products/registrar/** - Cheapest ($8-12/year)
3. **domains.google.com** - Simplest interface

## 2. 🚀 Deploy with Railway (EASIEST - 1-Click)

1. Go to **railway.app**
2. Sign up with GitHub
3. Click "New Project" → "Deploy from GitHub repo"
4. Select this repository
5. Railway will auto-detect the railway.toml config
6. Your app will be live in 5 minutes!

**Cost**: Free for 2 weeks, then $5/month

## 3. 🌟 Deploy Frontend with Vercel (FREE)

1. Go to **vercel.com**
2. Sign up with GitHub  
3. Import this repository
4. Vercel will auto-detect it's a React app
5. Set these environment variables:
   - `VITE_API_URL`: Your backend URL

**Cost**: FREE forever for personal projects

## 4. 🔧 Deploy Backend Separately

### Option A: Railway Backend
1. Create new Railway project
2. Add MongoDB and Redis databases (1-click)
3. Deploy backend folder
4. Environment variables auto-configured

### Option B: DigitalOcean App Platform
1. Go to cloud.digitalocean.com
2. Create new App
3. Connect GitHub repository
4. Select backend folder
5. Add managed MongoDB database

## 5. 🔐 Environment Variables for Production

Copy these to your deployment platform:

```bash
# Required
MONGODB_URI=mongodb://your-production-db-url
JWT_SECRET=your-super-secret-jwt-key
FRONTEND_URL=https://your-domain.com

# Payment Processing
STRIPE_SECRET_KEY=sk_live_your-stripe-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-secret

# Email (Choose one)
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 6. 🎯 Complete Setup Checklist

- [ ] Buy domain name
- [ ] Deploy backend (Railway/DigitalOcean)
- [ ] Deploy frontend (Vercel)
- [ ] Point domain to frontend
- [ ] Set up SSL (automatic with most hosts)
- [ ] Configure email settings
- [ ] Test Stripe/PayPal webhooks with live URLs
- [ ] Update CORS settings with production domain

## 🆘 Need Help?

1. **Railway Issues**: Check railway.app/docs
2. **Vercel Issues**: Check vercel.com/docs
3. **Domain Issues**: Most providers have 24/7 support
4. **SSL Issues**: Usually automatic, but contact support if needed

## 💡 Pro Tips

- Start with Railway for full-stack deployment (easiest)
- Use Vercel for frontend if you want maximum speed
- Buy .com domain if available (more trustworthy)
- Set up monitoring after deployment (Railway includes this)