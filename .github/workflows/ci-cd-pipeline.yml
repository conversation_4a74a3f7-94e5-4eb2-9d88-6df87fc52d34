name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

jobs:
  # Code Quality & Security Scanning
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            frontend/package-lock.json
      
      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci
      
      - name: Run ESLint
        run: |
          cd backend && npm run lint
          cd ../frontend && npm run lint
      
      - name: Run Prettier check
        run: |
          cd backend && npm run format:check
          cd ../frontend && npm run format:check
      
      - name: SonarQ<PERSON> Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      
      - name: Trivy Security Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
      
      - name: Upload Trivy results to GitHub Security
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: OWASP Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'NeuroColony'
          path: '.'
          format: 'HTML'
          args: >
            --enableRetired
            --enableExperimental
      
      - name: Upload dependency check results
        uses: actions/upload-artifact@v4
        with:
          name: dependency-check-report
          path: reports/

  # Unit & Integration Tests
  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:7
        ports:
          - 27017:27017
        options: >-
          --health-cmd="mongosh --eval 'db.adminCommand({ ping: 1 })'"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
      
      - name: Install dependencies
        working-directory: ./backend
        run: npm ci
      
      - name: Run unit tests
        working-directory: ./backend
        run: npm run test:unit
        env:
          NODE_ENV: test
          MONGODB_URI: mongodb://localhost:27017/neurocolony-test
          REDIS_URL: redis://localhost:6379
      
      - name: Run integration tests
        working-directory: ./backend
        run: npm run test:integration
        env:
          NODE_ENV: test
          MONGODB_URI: mongodb://localhost:27017/neurocolony-test
          REDIS_URL: redis://localhost:6379
      
      - name: Generate coverage report
        working-directory: ./backend
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./backend/coverage
          flags: backend
          name: backend-coverage

  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
      
      - name: Run unit tests
        working-directory: ./frontend
        run: npm run test:unit
      
      - name: Run component tests
        working-directory: ./frontend
        run: npm run test:components
      
      - name: Generate coverage report
        working-directory: ./frontend
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./frontend/coverage
          flags: frontend
          name: frontend-coverage

  # E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Start services with Docker Compose
        run: |
          docker-compose -f docker-compose.test.yml up -d
          ./scripts/wait-for-services.sh
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:3000
      
      - name: Upload Playwright report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
      
      - name: Stop services
        if: always()
        run: docker-compose -f docker-compose.test.yml down

  # Build & Push Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [code-quality, test-backend, test-frontend]
    if: github.event_name == 'push'
    strategy:
      matrix:
        service:
          - name: auth-service
            context: ./backend
            dockerfile: ./backend/Dockerfile.auth
          - name: agent-service
            context: ./backend
            dockerfile: ./backend/Dockerfile.agent
          - name: workflow-service
            context: ./backend
            dockerfile: ./backend/Dockerfile.workflow
          - name: analytics-service
            context: ./backend
            dockerfile: ./backend/Dockerfile.analytics
          - name: frontend
            context: ./frontend
            dockerfile: ./frontend/Dockerfile
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.service.context }}
          file: ${{ matrix.service.dockerfile }}
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/neurocolony/${{ matrix.service.name }}:${{ github.sha }}
            ${{ env.ECR_REGISTRY }}/neurocolony/${{ matrix.service.name }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
      
      - name: Run performance tests
        run: |
          k6 run --out json=k6-results.json tests/performance/load-test.js
      
      - name: Upload performance results
        uses: actions/upload-artifact@v4
        with:
          name: k6-results
          path: k6-results.json

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-images, e2e-tests]
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.neurocolony.ai
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name neurocolony-staging --region ${{ env.AWS_REGION }}
      
      - name: Install ArgoCD CLI
        run: |
          curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/latest/download/argocd-linux-amd64
          chmod +x /usr/local/bin/argocd
      
      - name: Deploy with ArgoCD
        run: |
          argocd app set neurocolony-staging \
            --revision ${{ github.sha }} \
            --helm-set-string image.tag=${{ github.sha }} \
            --server ${{ secrets.ARGOCD_SERVER }} \
            --auth-token ${{ secrets.ARGOCD_AUTH_TOKEN }}
          
          argocd app sync neurocolony-staging \
            --server ${{ secrets.ARGOCD_SERVER }} \
            --auth-token ${{ secrets.ARGOCD_AUTH_TOKEN }} \
            --prune \
            --timeout 600
      
      - name: Wait for deployment
        run: |
          kubectl rollout status deployment -n neurocolony-staging --timeout=10m
      
      - name: Run smoke tests
        run: |
          npm run test:smoke -- --env=staging

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images, performance-tests]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://neurocolony.ai
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name neurocolony-production --region ${{ env.AWS_REGION }}
      
      - name: Create deployment record
        run: |
          echo "Creating deployment record..."
          curl -X POST ${{ secrets.DEPLOYMENT_TRACKER_URL }}/deployments \
            -H "Authorization: Bearer ${{ secrets.DEPLOYMENT_TRACKER_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "environment": "production",
              "version": "${{ github.sha }}",
              "deployer": "${{ github.actor }}",
              "status": "in_progress"
            }'
      
      - name: Blue-Green Deployment
        run: |
          # Deploy to green environment
          kubectl apply -f infrastructure/kubernetes/production/green-deployment.yaml
          kubectl set image deployment/neurocolony-green -n neurocolony-production \
            auth-service=${{ env.ECR_REGISTRY }}/neurocolony/auth-service:${{ github.sha }} \
            agent-service=${{ env.ECR_REGISTRY }}/neurocolony/agent-service:${{ github.sha }} \
            workflow-service=${{ env.ECR_REGISTRY }}/neurocolony/workflow-service:${{ github.sha }} \
            analytics-service=${{ env.ECR_REGISTRY }}/neurocolony/analytics-service:${{ github.sha }} \
            frontend=${{ env.ECR_REGISTRY }}/neurocolony/frontend:${{ github.sha }}
          
          # Wait for green deployment to be ready
          kubectl rollout status deployment/neurocolony-green -n neurocolony-production --timeout=10m
          
          # Run health checks on green environment
          ./scripts/health-check.sh green
          
          # Switch traffic to green
          kubectl patch service neurocolony-lb -n neurocolony-production \
            -p '{"spec":{"selector":{"version":"green"}}}'
          
          # Scale down blue deployment
          kubectl scale deployment neurocolony-blue -n neurocolony-production --replicas=0
      
      - name: Run production smoke tests
        run: |
          npm run test:smoke -- --env=production
      
      - name: Update deployment record
        if: always()
        run: |
          status="completed"
          if [ "${{ job.status }}" != "success" ]; then
            status="failed"
          fi
          
          curl -X PATCH ${{ secrets.DEPLOYMENT_TRACKER_URL }}/deployments/${{ github.sha }} \
            -H "Authorization: Bearer ${{ secrets.DEPLOYMENT_TRACKER_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d "{\"status\": \"${status}\"}"

  # Post-deployment monitoring
  post-deployment-validation:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Run synthetic monitoring
        run: |
          npm run test:synthetic -- --env=production
      
      - name: Check error rates
        run: |
          ./scripts/check-metrics.sh \
            --metric error_rate \
            --threshold 0.01 \
            --duration 15m
      
      - name: Check response times
        run: |
          ./scripts/check-metrics.sh \
            --metric response_time_p99 \
            --threshold 500 \
            --duration 15m
      
      - name: Create rollback if metrics degraded
        if: failure()
        run: |
          echo "Metrics degraded, initiating rollback..."
          kubectl patch service neurocolony-lb -n neurocolony-production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl scale deployment neurocolony-blue -n neurocolony-production --replicas=10