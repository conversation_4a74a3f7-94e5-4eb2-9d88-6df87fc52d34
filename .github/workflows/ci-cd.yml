name: 🧠 NeuroColony CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  DOCKER_BUILDKIT: 1

jobs:
  # ==============================================
  # 🧪 TESTING PIPELINE
  # ==============================================
  test:
    name: 🧪 Run Tests
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongosh
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: 📦 Install dependencies
      run: |
        npm ci
        cd backend && npm ci
        cd ../frontend && npm ci

    - name: 🔍 Lint code
      run: |
        npm run lint
        cd backend && npm run lint
        cd ../frontend && npm run lint

    - name: 🧪 Run unit tests
      run: npm run test:unit
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/neurocolony_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key-for-ci
        OPENAI_API_KEY: test-key

    - name: 🔗 Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/neurocolony_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key-for-ci
        OPENAI_API_KEY: test-key

    - name: 📊 Generate test coverage
      run: npm run test:coverage
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/neurocolony_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key-for-ci
        OPENAI_API_KEY: test-key

    - name: 📈 Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: neurocolony-coverage

  # ==============================================
  # 🔒 SECURITY SCANNING
  # ==============================================
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: 📦 Install dependencies
      run: npm ci

    - name: 🔍 Run security audit
      run: npm audit --audit-level high

    - name: 🔒 Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # ==============================================
  # 🏗️ BUILD PIPELINE
  # ==============================================
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: 📦 Install dependencies
      run: |
        npm ci
        cd backend && npm ci
        cd ../frontend && npm ci

    - name: 🏗️ Build frontend
      run: cd frontend && npm run build

    - name: 🏗️ Build backend
      run: cd backend && npm run build

    - name: 📤 Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: |
          frontend/dist/
          backend/dist/

  # ==============================================
  # 🐳 DOCKER BUILD
  # ==============================================
  docker:
    name: 🐳 Build Docker Images
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 🔑 Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: 🏷️ Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: neurocolony/app
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: 🏗️ Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # ==============================================
  # 🚀 DEPLOYMENT PIPELINE
  # ==============================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🚀 Deploy to Railway (Staging)
      uses: railway/cli@main
      with:
        command: up --service staging
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN_STAGING }}

    - name: 🧪 Run E2E tests against staging
      run: npm run test:e2e
      env:
        E2E_BASE_URL: ${{ secrets.STAGING_URL }}

  deploy-production:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🚀 Deploy to Railway (Production)
      uses: railway/cli@main
      with:
        command: up --service production
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN_PRODUCTION }}

    - name: 🏥 Health check
      run: |
        curl -f ${{ secrets.PRODUCTION_URL }}/api/health || exit 1

    - name: 📢 Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'NeuroColony successfully deployed to production! 🚀'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # ==============================================
  # 📊 PERFORMANCE MONITORING
  # ==============================================
  lighthouse:
    name: 📊 Lighthouse Performance
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: 📂 Checkout code
      uses: actions/checkout@v4

    - name: 🚀 Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          ${{ secrets.STAGING_URL }}
          ${{ secrets.STAGING_URL }}/login
          ${{ secrets.STAGING_URL }}/dashboard
        uploadArtifacts: true
        temporaryPublicStorage: true
