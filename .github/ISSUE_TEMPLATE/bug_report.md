---
name: 🐛 Bug Report
about: Create a report to help us improve NeuroColony
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📸 Screenshots

If applicable, add screenshots to help explain your problem.

## 🌍 Environment

**Desktop:**
- OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
- Version: [e.g. 1.0.0]

**Mobile:**
- Device: [e.g. iPhone 13, Samsung Galaxy S21]
- OS: [e.g. iOS 15.1, Android 12]
- Browser: [e.g. Safari, Chrome Mobile]

**Server (if applicable):**
- Node.js version: [e.g. 18.17.0]
- Database: [e.g. MongoDB 7.0]
- Deployment: [e.g. Railway, Docker, VPS]

## 📋 Additional Context

Add any other context about the problem here:
- Console errors (if any)
- Network requests that failed
- Related issues or PRs
- Workarounds you've tried

## 🔗 Related Information

- [ ] This bug affects the frontend
- [ ] This bug affects the backend/API  
- [ ] This bug affects the database
- [ ] This bug affects authentication
- [ ] This bug affects payments
- [ ] This is a security issue
- [ ] This is a performance issue

## ⚡ Priority

- [ ] Low - Nice to have
- [ ] Medium - Important for user experience
- [ ] High - Breaks core functionality
- [ ] Critical - Security issue or data loss
