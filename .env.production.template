# NeuroColony Production Environment Configuration
# Copy this to .env.production and fill in your values

# Node Environment
NODE_ENV=production
PORT=5000

# Domain Configuration
DOMAIN_NAME=neurocolony.dev
FRONTEND_URL=https://neurocolony.dev
BACKEND_URL=https://neurocolony.dev/api

# Database Configuration
MONGODB_URI=mongodb://neurocolony:${MONGO_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-super-secure-mongodb-password
MONGO_PASSWORD=your-secure-mongo-password

# PostgreSQL Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-postgres-password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_URL=redis://:your-secure-redis-password@redis:6379
REDIS_PASSWORD=your-secure-redis-password

# RabbitMQ Configuration
RABBITMQ_URL=amqp://neurocolony:your-secure-rabbitmq-password@rabbitmq:5672
RABBITMQ_USER=neurocolony
RABBITMQ_PASSWORD=your-secure-rabbitmq-password

# Security
JWT_SECRET=your-super-secure-jwt-secret-at-least-256-bits-long
INTEGRATION_ENCRYPTION_KEY=your-32-character-encryption-key
SESSION_SECRET=your-secure-session-secret

# AI API Keys
OPENAI_API_KEY=sk-YOUR_PRODUCTION_OPENAI_KEY
ANTHROPIC_API_KEY=sk-ant-YOUR_ANTHROPIC_API_KEY

# Stripe Configuration (LIVE KEYS)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_PRODUCTION_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY
STRIPE_STARTER_PRICE_ID=price_YOUR_STARTER_PRICE_ID
STRIPE_PROFESSIONAL_PRICE_ID=price_YOUR_PROFESSIONAL_PRICE_ID
STRIPE_BUSINESS_PRICE_ID=price_YOUR_BUSINESS_PRICE_ID

# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_WEBHOOK_KEY=your-sendgrid-webhook-key

# Email Marketing Integrations
MAILCHIMP_CLIENT_ID=your-mailchimp-client-id
MAILCHIMP_CLIENT_SECRET=your-mailchimp-client-secret
MAILCHIMP_WEBHOOK_KEY=your-mailchimp-webhook-key

CONVERTKIT_API_KEY=your-convertkit-api-key
CONVERTKIT_API_SECRET=your-convertkit-api-secret

ACTIVECAMPAIGN_API_URL=https://your-account.api-us1.com
ACTIVECAMPAIGN_API_KEY=your-activecampaign-api-key

KLAVIYO_API_KEY=pk_your-klaviyo-public-key
KLAVIYO_PRIVATE_KEY=sk_your-klaviyo-private-key

BREVO_API_KEY=xkeysib-your-brevo-api-key

# CRM Integrations
SALESFORCE_CLIENT_ID=your-salesforce-client-id
SALESFORCE_CLIENT_SECRET=your-salesforce-client-secret
SALESFORCE_REDIRECT_URI=https://api.neurocolony.dev/integrations/salesforce/callback

HUBSPOT_CLIENT_ID=your-hubspot-client-id
HUBSPOT_CLIENT_SECRET=your-hubspot-client-secret
HUBSPOT_REDIRECT_URI=https://api.neurocolony.dev/integrations/hubspot/callback

PIPEDRIVE_CLIENT_ID=your-pipedrive-client-id
PIPEDRIVE_CLIENT_SECRET=your-pipedrive-client-secret

# Analytics Integrations
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://api.neurocolony.dev/integrations/google/callback
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=https://api.neurocolony.dev/integrations/facebook/callback

MIXPANEL_TOKEN=your-mixpanel-token
AMPLITUDE_API_KEY=your-amplitude-api-key
SEGMENT_WRITE_KEY=your-segment-write-key

# E-commerce Integrations
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_WEBHOOK_KEY=your-shopify-webhook-key

# Communication Integrations
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret
SLACK_SIGNING_SECRET=your-slack-signing-secret

TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WEBHOOK_AUTH_TOKEN=your-twilio-webhook-auth-token

# Storage Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=your-secure-minio-password
S3_BUCKET_NAME=neurocolony-assets
S3_REGION=us-east-1

# Monitoring Configuration
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-secure-grafana-password

# ClickHouse Configuration
CLICKHOUSE_USER=analytics
CLICKHOUSE_PASSWORD=your-secure-clickhouse-password

# Kong API Gateway
KONG_DB_PASSWORD=your-secure-kong-password

# Temporal Workflow Engine
TEMPORAL_DB_PASSWORD=your-secure-temporal-password

# Feature Flags
ENABLE_AI_AGENTS=true
ENABLE_WORKFLOW_ENGINE=true
ENABLE_ANALYTICS=true
ENABLE_INTEGRATIONS=true
ENABLE_WEBHOOKS=true
DEMO_MODE=false

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ALLOWED_ORIGINS=https://neurocolony.dev,https://app.neurocolony.dev
COOKIE_DOMAIN=.neurocolony.dev

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id

# APM
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
NEW_RELIC_APP_NAME=NeuroColony Production