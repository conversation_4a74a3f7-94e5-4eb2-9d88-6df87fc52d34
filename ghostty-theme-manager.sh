#!/bin/bash

# Ghostty Theme Manager
# A utility script for managing Ghostty terminal themes

set -e

# Configuration
GHOSTTY_CONFIG_DIR="$HOME/.config/ghostty"
THEMES_DIR="$GHOSTTY_CONFIG_DIR/themes"
CONFIG_FILE="$GHOSTTY_CONFIG_DIR/config"
BACKUP_DIR="$GHOSTTY_CONFIG_DIR/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions
print_header() {
    echo -e "${PURPLE}╔══════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║        Ghostty Theme Manager         ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════╝${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Ghostty config directory exists
check_setup() {
    if [ ! -d "$GHOSTTY_CONFIG_DIR" ]; then
        print_error "Ghostty config directory not found: $GHOSTTY_CONFIG_DIR"
        print_info "Creating directory structure..."
        mkdir -p "$THEMES_DIR" "$BACKUP_DIR"
        print_success "Directory structure created"
    fi
    
    if [ ! -d "$THEMES_DIR" ]; then
        mkdir -p "$THEMES_DIR"
        print_success "Themes directory created"
    fi
    
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        print_success "Backup directory created"
    fi
}

# List available themes
list_themes() {
    echo -e "${CYAN}Available Themes:${NC}"
    echo "=================="
    
    if [ ! -d "$THEMES_DIR" ] || [ -z "$(ls -A "$THEMES_DIR" 2>/dev/null)" ]; then
        print_warning "No themes found in $THEMES_DIR"
        return
    fi
    
    local current_theme=""
    if [ -f "$CONFIG_FILE" ]; then
        current_theme=$(grep "^theme = " "$CONFIG_FILE" 2>/dev/null | sed 's/theme = //' | tr -d ' ')
    fi
    
    for theme_file in "$THEMES_DIR"/*.conf; do
        if [ -f "$theme_file" ]; then
            local theme_name=$(basename "$theme_file" .conf)
            if [ "$theme_name" = "$current_theme" ]; then
                echo -e "  ${GREEN}● $theme_name${NC} (current)"
            else
                echo -e "  ○ $theme_name"
            fi
        fi
    done
}

# Get current theme
get_current_theme() {
    if [ -f "$CONFIG_FILE" ]; then
        grep "^theme = " "$CONFIG_FILE" 2>/dev/null | sed 's/theme = //' | tr -d ' '
    else
        echo "none"
    fi
}

# Switch theme
switch_theme() {
    local theme_name="$1"
    
    if [ -z "$theme_name" ]; then
        print_error "Theme name required"
        echo "Usage: $0 switch <theme-name>"
        return 1
    fi
    
    local theme_file="$THEMES_DIR/$theme_name.conf"
    
    if [ ! -f "$theme_file" ]; then
        print_error "Theme '$theme_name' not found"
        print_info "Available themes:"
        list_themes
        return 1
    fi
    
    # Backup current config
    if [ -f "$CONFIG_FILE" ]; then
        local backup_file="$BACKUP_DIR/config.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$CONFIG_FILE" "$backup_file"
        print_info "Config backed up to: $backup_file"
    fi
    
    # Update theme in config file
    if [ -f "$CONFIG_FILE" ]; then
        if grep -q "^theme = " "$CONFIG_FILE"; then
            sed -i "s/^theme = .*/theme = $theme_name/" "$CONFIG_FILE"
        else
            echo "theme = $theme_name" >> "$CONFIG_FILE"
        fi
    else
        echo "theme = $theme_name" > "$CONFIG_FILE"
    fi
    
    print_success "Switched to theme: $theme_name"
    print_info "Reload Ghostty to see changes (Ctrl+Shift+R)"
}

# Preview theme colors
preview_theme() {
    local theme_name="$1"
    
    if [ -z "$theme_name" ]; then
        print_error "Theme name required"
        return 1
    fi
    
    local theme_file="$THEMES_DIR/$theme_name.conf"
    
    if [ ! -f "$theme_file" ]; then
        print_error "Theme '$theme_name' not found"
        return 1
    fi
    
    echo -e "${CYAN}Theme Preview: $theme_name${NC}"
    echo "=========================="
    
    # Extract colors from theme file
    local bg=$(grep "^background = " "$theme_file" 2>/dev/null | sed 's/background = //' | tr -d ' ')
    local fg=$(grep "^foreground = " "$theme_file" 2>/dev/null | sed 's/foreground = //' | tr -d ' ')
    local cursor=$(grep "^cursor-color = " "$theme_file" 2>/dev/null | sed 's/cursor-color = //' | tr -d ' ')
    
    echo "Background: ${bg:-'not set'}"
    echo "Foreground: ${fg:-'not set'}"
    echo "Cursor:     ${cursor:-'not set'}"
    echo
    
    # Show color palette if available
    echo "Color Palette:"
    for i in {0..15}; do
        local color=$(grep "^palette = $i=" "$theme_file" 2>/dev/null | sed "s/palette = $i=//" | tr -d ' ')
        if [ -n "$color" ]; then
            printf "  %2d: %s\n" "$i" "$color"
        fi
    done
}

# Install theme from URL or file
install_theme() {
    local source="$1"
    local theme_name="$2"
    
    if [ -z "$source" ]; then
        print_error "Source file or URL required"
        return 1
    fi
    
    if [ -z "$theme_name" ]; then
        theme_name=$(basename "$source" .conf)
    fi
    
    local dest_file="$THEMES_DIR/$theme_name.conf"
    
    if [ -f "$dest_file" ]; then
        print_warning "Theme '$theme_name' already exists"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Installation cancelled"
            return 0
        fi
    fi
    
    if [[ "$source" =~ ^https?:// ]]; then
        # Download from URL
        if command -v curl >/dev/null 2>&1; then
            curl -s "$source" > "$dest_file"
        elif command -v wget >/dev/null 2>&1; then
            wget -q "$source" -O "$dest_file"
        else
            print_error "curl or wget required to download themes"
            return 1
        fi
    else
        # Copy from local file
        if [ ! -f "$source" ]; then
            print_error "Source file not found: $source"
            return 1
        fi
        cp "$source" "$dest_file"
    fi
    
    print_success "Theme '$theme_name' installed successfully"
}

# Remove theme
remove_theme() {
    local theme_name="$1"
    
    if [ -z "$theme_name" ]; then
        print_error "Theme name required"
        return 1
    fi
    
    local theme_file="$THEMES_DIR/$theme_name.conf"
    
    if [ ! -f "$theme_file" ]; then
        print_error "Theme '$theme_name' not found"
        return 1
    fi
    
    local current_theme=$(get_current_theme)
    if [ "$theme_name" = "$current_theme" ]; then
        print_warning "Cannot remove currently active theme"
        return 1
    fi
    
    print_warning "This will permanently delete the theme '$theme_name'"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm "$theme_file"
        print_success "Theme '$theme_name' removed"
    else
        print_info "Removal cancelled"
    fi
}

# Show help
show_help() {
    echo "Ghostty Theme Manager"
    echo "Usage: $0 <command> [arguments]"
    echo
    echo "Commands:"
    echo "  list                    List available themes"
    echo "  current                 Show current theme"
    echo "  switch <theme>          Switch to specified theme"
    echo "  preview <theme>         Preview theme colors"
    echo "  install <file> [name]   Install theme from file or URL"
    echo "  remove <theme>          Remove theme"
    echo "  backup                  Backup current configuration"
    echo "  help                    Show this help message"
    echo
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 switch cyberpunk-matrix"
    echo "  $0 preview tokyo-night"
    echo "  $0 install my-theme.conf custom-theme"
}

# Backup configuration
backup_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        print_warning "No configuration file to backup"
        return 0
    fi
    
    local backup_file="$BACKUP_DIR/config.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$CONFIG_FILE" "$backup_file"
    print_success "Configuration backed up to: $backup_file"
}

# Main script logic
main() {
    print_header
    check_setup
    
    case "${1:-help}" in
        "list"|"ls")
            list_themes
            ;;
        "current")
            local current=$(get_current_theme)
            echo -e "${CYAN}Current theme:${NC} $current"
            ;;
        "switch"|"set")
            switch_theme "$2"
            ;;
        "preview"|"show")
            preview_theme "$2"
            ;;
        "install"|"add")
            install_theme "$2" "$3"
            ;;
        "remove"|"rm"|"delete")
            remove_theme "$2"
            ;;
        "backup")
            backup_config
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
