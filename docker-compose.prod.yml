# NeuroColony Production Docker Compose
# Usage: docker-compose -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: neurocolony-mongodb-prod
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USER:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: neurocolony
    volumes:
      - mongodb_data:/data/db
    networks:
      - neurocolony-network
    ports:
      - "27017:27017"

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: neurocolony-backend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/neurocolony?authSource=admin
      JWT_SECRET: ${JWT_SECRET}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      FRONTEND_URL: https://${DOMAIN_NAME}
      DEMO_MODE: false
    depends_on:
      - mongodb
    networks:
      - neurocolony-network
    ports:
      - "5000:5000"

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: https://${DOMAIN_NAME}/api
        VITE_STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
    container_name: neurocolony-frontend-prod
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - neurocolony-network
    ports:
      - "3000:3000"

volumes:
  mongodb_data:
    driver: local

networks:
  neurocolony-network:
    driver: bridge