import aiService from './backend/services/aiService.js'

async function testDemoMode() {
  try {
    console.log('🧪 Testing Demo AI Generation...')
    
    const businessInfo = {
      industry: 'E-commerce',
      productService: 'Email Marketing Automation Tool',
      targetAudience: 'Small Business Owners',
      pricePoint: '$29/month',
      mainBenefit: 'Increase sales by 200%',
      painPoint: 'Manual email marketing is time-consuming'
    }
    
    const settings = {
      sequenceLength: 3,
      tone: 'Professional',
      primaryGoal: 'Generate Sales',
      includeCTA: true,
      includePersonalization: true
    }
    
    console.log('📧 Generating demo 3-email sequence...')
    
    const result = await aiService.generateEmailSequence(businessInfo, settings)
    
    console.log('✅ Generation successful!')
    console.log('\n📊 AI Analysis:')
    console.log(`Overall Score: ${result.aiAnalysis.overallScore}`)
    console.log(`Mode: ${result.aiAnalysis.mode || 'demo'}`)
    console.log(`Predicted Conversion Rate: ${result.aiAnalysis.predictedConversionRate}%`)
    
    console.log('\n📧 Generated Emails:')
    result.emails.forEach((email, index) => {
      console.log(`\n--- Email ${index + 1} (Day ${email.dayDelay}) ---`)
      console.log(`Subject: ${email.subject}`)
      console.log(`Score: ${email.conversionScore}`)
      console.log(`Triggers: ${email.psychologyTriggers.join(', ')}`)
      console.log(`Body Preview: ${email.body.substring(0, 150)}...`)
    })
    
    console.log('\n🎉 Demo Test Complete!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testDemoMode()